<template>
  <div class="plugin-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">插件管理</h1>
      <p class="page-description">管理和配置您的AI插件扩展</p>
    </div>

    <!-- 当前使用的插件 -->
    <NCard v-if="currentPlugin" class="current-plugin-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:star-line" class="card-icon" />
          <span>当前启用的插件</span>
        </div>
      </template>
      
      <div class="current-plugin">
        <div class="plugin-info">
          <img
            v-if="currentPlugin.pluginImg"
            :src="currentPlugin.pluginImg"
            :alt="currentPlugin.pluginName"
            class="plugin-avatar"
          />
          <div v-else class="plugin-avatar-placeholder">
            <SvgIcon icon="ri:plug-line" class="text-2xl" />
          </div>
          
          <div class="plugin-details">
            <div class="plugin-name">{{ currentPlugin.pluginName }}</div>
            <div class="plugin-description">{{ currentPlugin.description || '暂无描述' }}</div>
          </div>
        </div>
        
        <NButton
          type="error"
          @click="disableCurrentPlugin"
        >
          <template #icon>
            <SvgIcon icon="ri:stop-line" />
          </template>
          停用插件
        </NButton>
      </div>
    </NCard>

    <!-- 插件使用说明 -->
    <NCard class="info-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:information-line" class="card-icon" />
          <span>插件使用说明</span>
        </div>
      </template>
      
      <div class="info-content">
        <div class="info-item">
          <SvgIcon icon="ri:checkbox-circle-line" class="info-icon success" />
          <span>插件可以扩展AI的能力，为您的对话添加特殊功能</span>
        </div>
        <div class="info-item">
          <SvgIcon icon="ri:alert-line" class="info-icon warning" />
          <span>同时只能启用一个插件，启用新插件会自动停用当前插件</span>
        </div>
        <div class="info-item">
          <SvgIcon icon="ri:coin-line" class="info-icon info" />
          <span>不同插件可能会消耗不同类型和数量的积分</span>
        </div>
      </div>
    </NCard>

    <!-- 可用插件列表 -->
    <NCard class="plugins-list-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:apps-line" class="card-icon" />
          <span>可用插件 ({{ pluginList.length }})</span>
        </div>
      </template>

      <div v-if="loading" class="loading-state">
        <NSpin size="large" />
        <div class="loading-text">加载插件列表...</div>
      </div>

      <div v-else-if="pluginList.length === 0" class="empty-state">
        <SvgIcon icon="ri:plug-line" class="empty-icon" />
        <div class="empty-title">暂无可用插件</div>
        <div class="empty-desc">插件功能正在开发中，敬请期待更多强大的AI功能扩展</div>
      </div>

      <div v-else class="plugins-grid">
        <div
          v-for="plugin in pluginList"
          :key="plugin.pluginId"
          class="plugin-card"
          :class="{ 'active': isPluginActive(plugin) }"
        >
          <div class="plugin-header">
            <img
              v-if="plugin.pluginImg"
              :src="plugin.pluginImg"
              :alt="plugin.pluginName"
              class="plugin-image"
            />
            <div v-else class="plugin-image-placeholder">
              <SvgIcon icon="ri:plug-line" class="text-xl" />
            </div>
            
            <div class="plugin-status">
              <NBadge
                v-if="isPluginActive(plugin)"
                type="success"
                dot
              >
                已启用
              </NBadge>
              <NBadge
                v-else
                type="default"
                dot
              >
                未启用
              </NBadge>
            </div>
          </div>

          <div class="plugin-content">
            <div class="plugin-title">{{ plugin.pluginName }}</div>
            <div class="plugin-desc">{{ plugin.description || '暂无描述' }}</div>
            
            <div class="plugin-meta">
              <div class="meta-item">
                <SvgIcon icon="ri:coin-line" class="meta-icon" />
                <span>{{ getDeductTypeText(plugin.deductType) }}</span>
              </div>
            </div>
          </div>

          <div class="plugin-actions">
            <NButton
              v-if="!isPluginActive(plugin)"
              type="primary"
              @click="enablePlugin(plugin)"
              block
            >
              <template #icon>
                <SvgIcon icon="ri:play-line" />
              </template>
              启用插件
            </NButton>
            
            <NButton
              v-else
              type="error"
              @click="disablePlugin(plugin)"
              block
            >
              <template #icon>
                <SvgIcon icon="ri:stop-line" />
              </template>
              停用插件
            </NButton>
          </div>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCard, NButton, NSpin, NBadge, useMessage } from 'naive-ui';
import { useChatStore } from '@/store';
import { SvgIcon } from '@/components/common';

const chatStore = useChatStore();
const message = useMessage();

// 状态管理
const loading = ref(false);

// 计算属性
const pluginList = computed(() => chatStore.pluginList || []);
const currentPlugin = computed(() => chatStore.currentPlugin);

// 方法
const isPluginActive = (plugin: any) => {
  return currentPlugin.value?.pluginId === plugin.pluginId;
};

const getDeductTypeText = (deductType: number) => {
  switch (deductType) {
    case 1:
      return '基础积分';
    case 2:
      return '高级积分';
    case 3:
      return '绘画积分';
    default:
      return '免费使用';
  }
};

const enablePlugin = (plugin: any) => {
  chatStore.setUsingPlugin(plugin);
  message.success(`已启用插件：${plugin.pluginName}`);
};

const disablePlugin = (plugin: any) => {
  chatStore.setUsingPlugin(null);
  message.success(`已停用插件：${plugin.pluginName}`);
};

const disableCurrentPlugin = () => {
  if (currentPlugin.value) {
    chatStore.setUsingPlugin(null);
    message.success(`已停用插件：${currentPlugin.value.pluginName}`);
  }
};

// 生命周期
onMounted(async () => {
  loading.value = true;
  try {
    await chatStore.queryPlugins();
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.plugin-manager {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.current-plugin-card {
  @apply shadow-sm border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20;
}

.info-card,
.plugins-list-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply flex items-center space-x-2 text-gray-900 dark:text-gray-100;
}

.card-icon {
  @apply text-lg text-primary-600;
}

.current-plugin {
  @apply flex items-center justify-between;
}

.plugin-info {
  @apply flex items-center space-x-4;
}

.plugin-avatar,
.plugin-image {
  @apply w-12 h-12 rounded-lg object-cover;
}

.plugin-avatar-placeholder,
.plugin-image-placeholder {
  @apply w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400;
}

.plugin-details {
  @apply flex-1;
}

.plugin-name,
.plugin-title {
  @apply font-medium text-gray-900 dark:text-gray-100 mb-1;
}

.plugin-description,
.plugin-desc {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.info-content {
  @apply space-y-3;
}

.info-item {
  @apply flex items-center space-x-3;
}

.info-icon {
  @apply text-lg flex-shrink-0;
}

.info-icon.success {
  @apply text-green-500;
}

.info-icon.warning {
  @apply text-yellow-500;
}

.info-icon.info {
  @apply text-blue-500;
}

.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-text {
  @apply mt-4 text-gray-500 dark:text-gray-400;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500;
}

.empty-icon {
  @apply text-4xl mb-3;
}

.empty-title {
  @apply text-lg font-medium mb-2;
}

.empty-desc {
  @apply text-sm text-center max-w-md;
}

.plugins-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.plugin-card {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg p-4 transition-all duration-200;
  @apply hover:shadow-md hover:border-primary-300 dark:hover:border-primary-600;
}

.plugin-card.active {
  @apply border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20;
}

.plugin-header {
  @apply flex items-center justify-between mb-3;
}

.plugin-status {
  @apply flex-shrink-0;
}

.plugin-content {
  @apply mb-4;
}

.plugin-meta {
  @apply mt-3 pt-3 border-t border-gray-100 dark:border-gray-700;
}

.meta-item {
  @apply flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400;
}

.meta-icon {
  @apply text-base;
}

.plugin-actions {
  @apply mt-4;
}
</style>
