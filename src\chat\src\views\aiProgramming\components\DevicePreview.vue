<template>
    <div class="relative p-2 device-preview-container" :class="[
        { 'fullscreen-container': isFullscreen },
        fullscreenContainerClass
    ]" id="preview-container" ref="previewContainerRef">
        <!-- 设备预览框 -->
        <transition name="device-change">
            <DeviceFrame ref="deviceFrameRef" :device-type="deviceType" :is-landscape="isLandscape"
                :is-fullscreen="isFullscreen" :html-content="code" :device-style="currentDeviceStyle"
                :device-class="previewContainerClass" />
        </transition>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
import DeviceFrame from './DeviceFrame.vue';

const props = defineProps({
    code: {
        type: String,
        required: true
    },
    deviceType: {
        type: String as () => 'mobile' | 'tablet' | 'desktop',
        required: true
    },
    isLandscape: {
        type: Boolean,
        default: false
    },
    isFullscreen: {
        type: Boolean,
        default: false
    },
    fullscreenContainerClass: {
        type: String,
        default: ''
    },
    previewContainerClass: {
        type: String,
        default: ''
    },
    currentDeviceStyle: {
        type: Object,
        default: () => ({})
    }
});

const emit = defineEmits([
    'change-device',
    'toggle-orientation',
    'toggle-fullscreen',
    'refresh',
    'container-resize'
]);

const deviceFrameRef = ref<InstanceType<typeof DeviceFrame> | null>(null);
const previewContainerRef = ref<HTMLElement | null>(null);
const containerSize = ref({ width: 0, height: 0 });
let resizeObserver: ResizeObserver | null = null;

// 刷新预览
const refreshPreview = () => {
    deviceFrameRef.value?.refreshPreview();
    emit('refresh');
};

// 设置CSS变量
const setDeviceScaleVariable = (scale: number) => {
    if (previewContainerRef.value) {
        previewContainerRef.value.style.setProperty('--device-scale', scale.toString());
    }
};

// 计算并设置设备缩放比例
const updateDeviceScale = () => {
    if (!previewContainerRef.value) return;

    // 获取容器尺寸
    const containerRect = previewContainerRef.value.getBoundingClientRect();
    containerSize.value = {
        width: containerRect.width,
        height: containerRect.height
    };

    // 通知父组件容器尺寸已更新
    emit('container-resize', containerSize.value);

    // 计算缩放比例并设置CSS变量
    if (props.isFullscreen) {
        // 在全屏模式下，将从父组件计算缩放比例
        return;
    }

    // 计算并设置缩放比例
    setDeviceScaleVariable(calculateDeviceScale());
};

// 计算设备缩放比例
const calculateDeviceScale = () => {
    if (props.deviceType === 'desktop') return 1;

    // 获取设备物理尺寸
    const deviceDimensions = getDeviceDimensions();
    if (!deviceDimensions) return 1;

    const { width, height } = containerSize.value;
    if (width === 0 || height === 0) return 1;

    // 计算容器可用空间的缩放比例
    const padding = 40; // 内边距
    let maxWidthScale = (width - padding) / deviceDimensions.width;
    let maxHeightScale = (height - padding) / deviceDimensions.height;

    // 平板竖屏模式特殊处理：高度按照容器高度的90%进行缩放
    if (props.deviceType === 'tablet' && !props.isLandscape) {
        // 计算目标高度为容器高度的90%
        const targetHeight = height * 0.9;
        // 根据目标高度计算缩放比例
        maxHeightScale = targetHeight / deviceDimensions.height;
    }

    // 取较小值，确保设备完全显示
    const scale = Math.min(maxWidthScale, maxHeightScale, 1);

    // 设置最小缩放值
    return Math.max(scale, 0.3);
};

// 获取当前设备尺寸
const getDeviceDimensions = () => {
    const devicePresets = {
        mobile: {
            width: 375,
            height: 667
        },
        tablet: {
            width: 768,
            height: 1024
        }
    };

    if (props.deviceType === 'desktop') {
        return null;
    }

    const preset = devicePresets[props.deviceType as 'mobile' | 'tablet'];

    // 横屏时交换宽高
    if (props.isLandscape) {
        return {
            width: preset.height,
            height: preset.width
        };
    }

    return {
        width: preset.width,
        height: preset.height
    };
};

// 监听容器尺寸变化
onMounted(() => {
    if (!previewContainerRef.value) return;

    updateDeviceScale();

    // 创建ResizeObserver
    resizeObserver = new ResizeObserver(() => {
        updateDeviceScale();
    });

    // 开始监听容器尺寸变化
    resizeObserver.observe(previewContainerRef.value);

    // 监听窗口大小变化
    window.addEventListener('resize', updateDeviceScale);
});

onUnmounted(() => {
    // 清理ResizeObserver
    if (resizeObserver) {
        resizeObserver.disconnect();
    }

    // 移除窗口大小变化监听器
    window.removeEventListener('resize', updateDeviceScale);
});

// 监听横竖屏变化，需要重新计算缩放比例
watch(() => props.isLandscape, () => {
    updateDeviceScale();

    if (props.isFullscreen) {
        // 获取预览容器
        const previewFrame = document.querySelector('.fullscreen-container .device-frame');
        if (previewFrame) {
            // 添加过渡类
            previewFrame.classList.add('rotating');

            // 延迟移除类以允许过渡完成
            setTimeout(() => {
                previewFrame.classList.remove('rotating');
                // 调整全屏模式下设备尺寸
                adjustFullscreenDeviceSize();
            }, 500);
        }
    }
});

// 监听设备类型变化，需要重新计算缩放比例
watch(() => props.deviceType, updateDeviceScale);

// 监听全屏状态变化，需要重新计算缩放比例
watch(() => props.isFullscreen, updateDeviceScale);

// 调整全屏模式下设备尺寸
const adjustFullscreenDeviceSize = () => {
    if (props.isFullscreen) {
        const container = document.querySelector('.fullscreen-container .device-frame');
        if (container) {
            // 强制重新计算样式
            container.classList.add('orientation-change');
            // 移除类以触发重新渲染
            setTimeout(() => {
                container.classList.remove('orientation-change');
                updateDeviceScale();
            }, 10);
        }
    }
};
</script>

<style lang="less" scoped>
/* 设备预览容器 */
.device-preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

/* 全屏预览模式 */
.fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 60;
    background: white;
    display: flex;
    flex-direction: column;

    .preview-frame {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px auto;
    }

    .dark & {
        background: #1f1f1f;
    }
}

/* 设备切换动画 */
.device-change-enter-active,
.device-change-leave-active {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.device-change-enter-from,
.device-change-leave-to {
    transform: scale(0.9);
    opacity: 0;
}

/* 屏幕方向变换动画 */
.rotate-enter-active,
.rotate-leave-active {
    transition: transform 0.5s ease;
}

.rotate-enter-from,
.rotate-leave-to {
    transform: rotate(90deg) scale(0.9);
    opacity: 0;
}

/* 为横竖屏切换添加特殊样式 */
.orientation-change {
    transition: all 0.3s ease-in-out;
}

/* 全屏模式下的特定设备和方向样式 */
.fullscreen-mobile.fullscreen-landscape :deep(.device-frame) {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.fullscreen-mobile.fullscreen-portrait :deep(.device-frame) {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.fullscreen-tablet.fullscreen-landscape :deep(.device-frame) {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.fullscreen-tablet.fullscreen-portrait :deep(.device-frame) {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
</style>