<template>
    <div class="flex flex-col h-full">
        <!-- 整体布局容器 -->
        <div class="w-full h-full flex flex-col editor-wrapper" ref="editorWrapperRef"
            :style="{ maxHeight: `${maxContainerHeight}px` }">
            <!-- 代码编辑器区域 -->
            <div class="w-full flex-grow overflow-auto border rounded-lg dark:border-gray-700/80 mb-2 editor-container">
                <CodeMirrorEditor :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)"
                    :dark="dark" :html-mode="true" class="w-full h-full" ref="editorRef" />
            </div>

            <!-- AI编程助手区域 -->
            <div
                class="border rounded-lg p-2 dark:border-gray-700/80 bg-white dark:bg-gray-800 shadow-sm ai-assistant-container">
                <!-- 标题区域 -->
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center gap-2">
                        <div class="w-6 h-6 flex-shrink-0 text-primary-500 animate-glow">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-full h-full" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                        </div>
                        <h3 class="text-base font-medium text-gray-800 dark:text-gray-200">DeepCreate助手</h3>
                    </div>

                    <!-- 当前选中模型信息 -->
                    <div class="flex items-center gap-2">
                        <n-tooltip trigger="hover" placement="top">
                            <template #trigger>
                                <div
                                    class="text-gray-400 cursor-help hover-effect hover:text-primary-500 transition-colors duration-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </template>
                            选择一个AI模型来处理您的编程需求
                        </n-tooltip>

                        <n-popover v-if="selectedModelInfo" trigger="hover" placement="top">
                            <template #trigger>
                                <div
                                    class="px-2 py-1 text-sm bg-primary-50 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400 rounded cursor-help flex items-center hover-effect hover:opacity-80 transition-opacity duration-200">
                                    <span v-if="selectedModelInfo.deduct">
                                        {{ selectedModelInfo.deductType === 1 ? '点数' : '次数' }}: {{
                                            selectedModelInfo.deduct
                                        }}
                                    </span>
                                    <span v-else>模型信息</span>
                                </div>
                            </template>
                            <div class="max-w-xs">
                                <div class="font-medium mb-1">{{ selectedModelInfo.label }}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-2"
                                    v-if="selectedModelInfo.modelDescription">
                                    {{ selectedModelInfo.modelDescription }}
                                </div>
                                <div class="text-sm" v-if="selectedModelInfo.deduct">
                                    计费方式: {{ selectedModelInfo.deductType === 1 ? '点数' : '次数' }}
                                </div>
                                <div class="text-sm" v-if="selectedModelInfo.deduct">
                                    费用: {{ selectedModelInfo.deduct }}
                                </div>
                            </div>
                        </n-popover>
                    </div>
                </div>

                <!-- 控制区域 -->
                <div class="flex items-center gap-1 mb-1 p-1 rounded-md control-area">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300 mr-1 whitespace-nowrap">模型:</span>
                    <n-select v-model:value="selectedModel" :options="modelOptions" size="small" placeholder="选择模型"
                        :loading="loadingModels" class="model-select w-24 sm:w-28 max-w-[112px]" :disabled="isLoading">
                        <template #render-option="{ option }">
                            <div class="flex items-center space-x-1.5">
                                <div class="w-4 h-4 flex-shrink-0">
                                    <img v-if="option.modelAvatar" :src="option.modelAvatar"
                                        class="w-full h-full object-cover rounded-full" />
                                    <div v-else
                                        class="w-4 h-4 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                        <span class="text-[8px]">AI</span>
                                    </div>
                                </div>
                                <div class="flex flex-col overflow-hidden">
                                    <span
                                        class="text-sm whitespace-nowrap overflow-hidden text-ellipsis max-w-[80px]">{{
                                            option.label }}</span>
                                    <span v-if="option.deduct"
                                        class="text-sm text-gray-500 dark:text-gray-400 whitespace-nowrap overflow-hidden text-ellipsis max-w-[80px]">
                                        {{ option.deductType === 1 ? '点数' : '次数' }}: {{ option.deduct }}
                                    </span>
                                </div>
                            </div>
                        </template>
                        <template #render-label="{ option }">
                            <div class="flex items-center overflow-hidden">
                                <div class="w-3.5 h-3.5 mr-1 flex-shrink-0">
                                    <img v-if="option.modelAvatar" :src="option.modelAvatar"
                                        class="w-full h-full object-cover rounded-full" />
                                    <div v-else
                                        class="w-full h-full bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                        <span class="text-[6px]">AI</span>
                                    </div>
                                </div>
                                <span class="whitespace-nowrap overflow-hidden text-ellipsis max-w-[70px] text-sm">{{
                                    option.label }}</span>
                            </div>
                        </template>
                    </n-select>

                    <!-- 提示模板选择器 -->
                    <n-popover trigger="click" placement="bottom" class="template-popover">
                        <template #trigger>
                            <n-button size="small"
                                class="prompt-template-btn px-2.5 text-xs hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 bg-gray-50 dark:bg-gray-800/70 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                                style="min-height: 26px; line-height: 1;">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1 text-primary-500"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <span class="hidden sm:inline-block">快捷提示</span>
                                    <span class="inline-block sm:hidden">提示</span>
                                    <div class="ml-1 transition-transform duration-200 transform group-hover:rotate-90">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </div>
                                </div>
                            </n-button>
                        </template>
                        <div class="w-68 p-3">
                            <div
                                class="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3 pb-2 border-b border-gray-200 dark:border-gray-700 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5 text-primary-500"
                                    fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                <span>职业化AI开发模板</span>
                            </div>

                            <div class="max-h-[360px] overflow-auto pr-2 template-list">
                                <div v-for="category in promptCategories" :key="category.id" class="mb-4">
                                    <div
                                        class="flex items-center mb-2 bg-gray-50 dark:bg-gray-800/50 rounded-md px-2 py-1.5">
                                        <div class="w-4 h-4 mr-1.5 text-primary-500">
                                            <component :is="category.icon" />
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-200">{{
                                            category.name }}</span>
                                    </div>

                                    <div class="grid gap-1.5 pl-2">
                                        <n-button v-for="template in category.templates" :key="template.id" size="tiny"
                                            text @click="applyPromptTemplate(template)"
                                            class="justify-start template-item group hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-colors duration-200">
                                            <div class="flex items-center py-1">
                                                <div
                                                    class="template-icon mr-1.5 text-xs text-gray-400 group-hover:text-primary-500 transition-colors">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                    </svg>
                                                </div>
                                                <span
                                                    class="text-xs text-gray-700 dark:text-gray-300 group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-200 whitespace-nowrap overflow-hidden text-ellipsis max-w-[200px]">
                                                    {{ template.name }}
                                                </span>
                                            </div>
                                        </n-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </n-popover>
                </div>

                <div class="flex gap-2 flex-col sm:flex-row">
                    <!-- 左侧输入区域 -->
                    <div class="flex-grow">
                        <n-input v-model:value="userPrompt" type="textarea" :autosize="{ minRows: 1, maxRows: 3 }"
                            placeholder="请输入您的编程需求，例如：在页面上添加一个按钮" :disabled="isLoading" class="prompt-input" />
                        <div class="ai-error" v-if="errorMessage">{{ errorMessage }}</div>
                    </div>

                    <!-- 右侧按钮区域 -->
                    <div class="flex flex-row sm:flex-col justify-between sm:justify-end gap-2 sm:gap-3">
                        <div class="flex items-center gap-2">
                            <n-button v-if="isStreaming" type="warning" size="small" @click="cancelStreaming"
                                class="action-btn cancel-btn">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    取消
                                </div>
                            </n-button>
                            <n-button type="default" size="small" @click="resetPrompt"
                                :disabled="isLoading || !userPrompt" class="action-btn reset-btn">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    清空
                                </div>
                            </n-button>
                            <n-button type="primary" size="small" @click="submitPrompt"
                                :loading="isLoading && !isStreaming" class="action-btn submit-btn">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    {{ isLoading ? '处理中' : '提交' }}
                                </div>
                            </n-button>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-2 sm:mt-2" v-if="isLoading">
                            <div class="flex items-center">
                                <div class="mr-2 relative">
                                    <n-progress type="circle" :percentage="streamingProgress" :stroke-width="8"
                                        :show-indicator="false" class="generation-progress" :color="progressColor" />
                                    <div class="progress-indicator">
                                        <span class="text-[9px]">{{ Math.round(streamingProgress) }}%</span>
                                    </div>
                                </div>
                                <span class="typing-effect">
                                    <template v-if="isStreaming">
                                        <span class="hidden sm:inline-block">AI正在智能生成代码中</span>
                                        <span class="inline-block sm:hidden">生成代码中</span>
                                        <span class="typing-dots">
                                            <span :class="{ 'active': updateCount % 4 === 1 }">.</span>
                                            <span :class="{ 'active': updateCount % 4 === 2 }">.</span>
                                            <span :class="{ 'active': updateCount % 4 === 3 }">.</span>
                                        </span>
                                    </template>
                                    <template v-else>
                                        <span class="hidden sm:inline-block">AI正在分析您的需求...</span>
                                        <span class="inline-block sm:hidden">分析需求中...</span>
                                    </template>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { fetchChatAPIProcess } from '@/api/index';
import { fetchQueryModelsListAPI } from '@/api/models';
import CodeMirrorEditor from '@/components/CodeMirrorEditor.vue';
import { NButton, NInput, NPopover, NProgress, NSelect, NTooltip, useMessage } from 'naive-ui';
import { computed, defineExpose, h, onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps({
    modelValue: {
        type: String,
        required: true
    },
    dark: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue', 'format']);

const editorRef = ref<InstanceType<typeof CodeMirrorEditor> | null>(null);
const message = useMessage();

// AI编程助手相关状态
const userPrompt = ref('');
const selectedModel = ref('');
const isLoading = ref(false);
const loadingModels = ref(false);
const modelOptions = ref<any[]>([]);
const errorMessage = ref('');
// 添加模型缓存
const modelMapsCache = ref({});
const modelTypeListCache = ref([]);

// 流式输入相关状态
const isStreaming = ref(false);          // 是否正在接收流式响应
const streamingText = ref('');           // 当前累积的流式响应文本
const updateTimer = ref<number | null>(null); // 更新定时器ID
const controller = ref<AbortController | null>(null); // 用于取消请求
const updateCount = ref(0);              // 更新计数器，用于动画效果
const streamingProgress = ref(0);        // 生成进度百分比
const progressColor = ref('#1890ff');    // 进度条颜色

// 提示模板系统 - 按职业分类
const promptCategories = ref([
    {
        id: 1,
        name: '教育工作者',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222'
        })]),
        templates: [
            { id: 101, name: '互动单词记忆游戏', template: '创建一个互动单词记忆游戏，玩家需要匹配单词和其定义。包含10个编程相关的单词，使用卡片翻转动画，计分系统和计时器。设计成适合教室环境使用的风格。' },
            { id: 102, name: '数学公式可视化工具', template: '开发一个数学公式可视化工具，允许用户输入简单的数学函数（如y=x²+2x+1），并在坐标系中显示其图形。添加缩放和平移功能，使用明亮的教育风格设计。' },
            { id: 103, name: '历史时间线生成器', template: '创建一个历史时间线生成器，用户可以添加历史事件及其日期，自动按时间顺序排列并以可视化时间线形式展示。包含5个预设示例事件，使用优雅的滚动动画。' },
            { id: 104, name: '学生随机分组工具', template: '设计一个班级学生随机分组工具，教师可以输入学生名单，选择每组人数，然后随机分配学生到不同小组。添加保存分组结果和重新分组功能。' }
        ]
    },
    {
        id: 2,
        name: '医疗保健专业人员',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
        })]),
        templates: [
            { id: 201, name: 'BMI计算与健康建议工具', template: '创建一个BMI（身体质量指数）计算器，用户输入身高和体重，工具计算BMI值并提供相应的健康建议。使用专业医疗风格设计，添加健康范围色彩指示器。' },
            { id: 202, name: '药物剂量计算器', template: '开发一个药物剂量计算器，医护人员输入患者体重、药物名称和浓度，计算出适当的剂量。包含常见药物的预设选项，采用清晰易读的医疗界面。' },
            { id: 203, name: '健康习惯追踪日历', template: '设计一个健康习惯追踪日历，用户可以设置每日健康目标（如喝水、锻炼、服药），记录完成情况，查看每周和每月统计。使用鼓励性的视觉反馈。' },
            { id: 204, name: '心理健康自测问卷', template: '创建一个简单的心理健康自测问卷工具，包含10个关于情绪和压力的问题，提供初步评估和自我照顾建议。注意使用温和的色彩和支持性语言。' }
        ]
    },
    {
        id: 3,
        name: '商业分析师',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
        })]),
        templates: [
            { id: 301, name: '销售数据可视化工具', template: '开发一个销售数据可视化工具，允许用户输入销售数据，自动生成柱状图、饼图和趋势线。包含示例数据集，支持时间段筛选，使用商业分析风格的配色方案。' },
            { id: 302, name: '投资回报率计算器', template: '创建一个投资回报率(ROI)计算器，用户输入初始投资、预期收入和时间跨度，计算并可视化展示ROI。添加不同风险情景的比较功能，使用专业财务界面设计。' },
            { id: 303, name: '项目时间线规划工具', template: '设计一个项目时间线规划工具，用户可以添加项目里程碑和任务，设置时间和依赖关系，生成甘特图视图。包含拖拽调整功能，使用现代商业应用风格。' },
            { id: 304, name: '市场细分分析器', template: '开发一个市场细分分析工具，允许输入不同客户群体的数据，生成细分比较和目标市场建议。使用交互式图表展示结果，采用专业数据分析风格。' }
        ]
    },
    {
        id: 4,
        name: '设计师',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01'
        })]),
        templates: [
            { id: 401, name: '调色板生成器', template: '创建一个调色板生成器，允许设计师选择主色调，自动生成和谐的配色方案。包含复制色值功能、保存方案选项和不同色彩理论（互补色、三色组等）的应用。' },
            { id: 402, name: '响应式布局测试器', template: '开发一个响应式布局测试工具，用户可以输入网站URL或HTML代码，在不同设备尺寸下预览效果。提供常见设备尺寸的预设和自定义宽度选项。' },
            { id: 403, name: '字体配对预览工具', template: '设计一个字体配对预览工具，展示常用的标题和正文字体组合效果。允许用户自定义文本内容，调整字号和行高，查看实时效果。' },
            { id: 404, name: '设计灵感收集板', template: '创建一个设计灵感收集板，用户可以添加色彩、形状、文字和图像参考，组织成可视化的创意面板。包含布局模板和简单的拖拽调整功能。' }
        ]
    },
    {
        id: 5,
        name: '开发人员',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4'
        })]),
        templates: [
            { id: 501, name: '代码片段管理器', template: '开发一个代码片段管理器，允许开发者存储、分类和搜索常用代码片段。包含语法高亮、标签系统和快速复制功能，采用IDE风格的深色主题。' },
            { id: 502, name: 'API请求测试工具', template: '创建一个简单的API请求测试工具，支持GET、POST等方法，允许设置请求头和请求体，显示响应结果和状态码。使用开发者友好的界面风格。' },
            { id: 503, name: '正则表达式测试器', template: '设计一个正则表达式测试工具，用户可以输入正则表达式和测试文本，实时高亮匹配项并展示分组捕获。包含常用正则模式示例。' },
            { id: 504, name: '代码复杂度分析工具', template: '创建一个代码复杂度分析工具，分析代码中的嵌套循环、条件语句数量，计算循环复杂度，提供优化建议。支持多种编程语言语法。' }
        ]
    },
    {
        id: 6,
        name: '娱乐与游戏',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z'
        }), h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
        })]),
        templates: [
            { id: 601, name: '记忆卡片游戏', template: '创建一个记忆卡片游戏，玩家需要找出配对的卡片。使用有趣的图标或表情作为卡片内容，添加计时器、计分系统和难度级别，设计成吸引人的游戏界面。' },
            { id: 602, name: '迷你益智闯关游戏', template: '开发一个迷你益智闯关游戏，玩家需要解决简单的逻辑谜题才能进入下一关。包含3-5个难度递增的关卡，使用引人入胜的游戏元素和奖励机制。' },
            { id: 603, name: '文字冒险游戏生成器', template: '设计一个简单的文字冒险游戏生成器，用户可以创建包含选择分支的互动故事。提供故事模板、角色设置选项和结局多样化的工具。' },
            { id: 604, name: '虚拟骰子和硬币工具', template: '创建一个虚拟骰子和硬币工具，支持不同面数的骰子、多个骰子同时投掷，以及硬币正反面随机。添加动画效果和声音，使用游戏化设计。' }
        ]
    },
    {
        id: 7,
        name: '生活与实用工具',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
        })]),
        templates: [
            { id: 701, name: '日程计划与提醒工具', template: '开发一个日程计划与提醒工具，用户可以添加每日任务、设置优先级和截止时间，接收视觉提醒。使用直观的拖放界面和色彩编码系统。' },
            { id: 702, name: '旅行行程规划助手', template: '创建一个旅行行程规划助手，允许用户按日期和时间添加活动、景点、交通和住宿信息，生成可打印的旅行计划。使用旅行主题设计。' },
            { id: 703, name: '家庭预算跟踪器', template: '设计一个家庭预算跟踪器，记录收入和支出，按类别分类并生成可视化报告。包含省钱目标设置和进度跟踪功能，使用清晰的财务界面。' },
            { id: 704, name: '购物清单与菜单规划工具', template: '创建一个购物清单与每周菜单规划工具，允许用户规划一周餐食，自动生成所需食材清单。包含预设食谱建议和分类购物功能。' }
        ]
    },
    {
        id: 8,
        name: '物联网与自动化',
        icon: () => h('svg', {
            xmlns: 'http://www.w3.org/2000/svg',
            viewBox: '0 0 24 24',
            width: '1em',
            height: '1em',
            fill: 'none',
            stroke: 'currentColor'
        }, [h('path', {
            'stroke-linecap': 'round',
            'stroke-linejoin': 'round',
            'stroke-width': '2',
            d: 'M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z'
        })]),
        templates: [
            { id: 801, name: '智能家居控制面板', template: '设计一个智能家居控制面板模拟器，展示如何控制灯光、温度、门锁和其他设备。包含日/夜模式切换、场景预设和简单的自动化规则设置。' },
            { id: 802, name: '环境监测仪表盘', template: '创建一个环境监测仪表盘，显示温度、湿度、空气质量等数据的图表和趋势。使用模拟数据展示，包含阈值警报设置功能。' },
            { id: 803, name: '能源使用追踪器', template: '开发一个家庭能源使用追踪工具，记录和分析不同设备的能耗，提供节能建议。使用直观的图表和比较功能，采用环保主题设计。' },
            { id: 804, name: '自动化规则生成器', template: '创建一个自动化规则生成器，允许用户创建基于条件的触发规则（如"当温度高于30°C时开启风扇"）。使用可视化流程设计，支持多条件逻辑。' }
        ]
    }
]);

// 容器大小设置
const editorWrapperRef = ref<HTMLElement | null>(null);
const maxContainerHeight = ref(window.innerHeight - 80); // 减少边距，增加可用空间

// 监听窗口大小变化，动态调整编辑器高度
const handleResize = () => {
    maxContainerHeight.value = window.innerHeight - 80; // 更新最大容器高度
};

/**
 * 重置提示输入
 */
const resetPrompt = () => {
    userPrompt.value = '';
    errorMessage.value = '';
};

/**
 * 获取可用模型列表
 */
const fetchModelsList = async () => {
    loadingModels.value = true;
    errorMessage.value = '';
    try {
        const res: any = await fetchQueryModelsListAPI();
        if (res.success && res.data) {
            const { modelMaps, modelTypeList } = res.data;
            modelMapsCache.value = modelMaps;
            modelTypeListCache.value = modelTypeList;

            // 处理模型列表，与chat组件保持一致
            const flatModelArray = Object.values(modelMaps).flat() as any[];
            // 这里可以根据需要筛选特定类型的模型，例如只显示keyType为1的模型
            const filteredModelArray = flatModelArray.filter(
                (model) => model.keyType === 1
            );

            modelOptions.value = filteredModelArray.map((model) => ({
                label: model.modelName,
                value: model.model,
                deductType: model.deductType,
                keyType: model.keyType,
                deduct: model.deduct,
                isFileUpload: model.isFileUpload,
                modelAvatar: model.modelAvatar,
                modelDescription: model.modelDescription
            }));

            // 默认选择第一个模型
            if (modelOptions.value.length > 0 && !selectedModel.value) {
                selectedModel.value = modelOptions.value[0].value;
            } else if (modelOptions.value.length === 0) {
                message.warning('没有找到可用的模型');
            }
        } else {
            errorMessage.value = '获取模型列表失败: ' + (res.message || '未知错误');
            message.error(errorMessage.value);
        }
    } catch (error) {
        errorMessage.value = '获取模型列表出错';
        console.error('获取模型列表出错:', error);
        message.error('获取模型列表出错');
    } finally {
        loadingModels.value = false;
    }
};

/**
 * 使用当前的流式文本更新编辑器和预览
 */
const updateEditorWithStreamingText = () => {
    if (!streamingText.value) return;

    try {
        updateCount.value++;

        // 模拟进度，基于字符数量的增长
        const targetLength = 1500; // 假设平均响应长度
        const currentLength = streamingText.value.length;
        const calculatedProgress = Math.min(99, (currentLength / targetLength) * 100);

        // 平滑进度条变化
        streamingProgress.value = streamingProgress.value + (calculatedProgress - streamingProgress.value) * 0.2;

        // 根据进度更新颜色
        if (streamingProgress.value < 30) {
            progressColor.value = '#1890ff'; // 蓝色
        } else if (streamingProgress.value < 70) {
            progressColor.value = '#52c41a'; // 绿色
        } else {
            progressColor.value = '#13c2c2'; // 青色
        }

        // 尝试提取代码块
        const codeMatch = streamingText.value.match(/```(?:html)?\s*([\s\S]*?)```/);
        let finalCode;

        if (codeMatch && codeMatch[1]) {
            // 找到了代码块
            finalCode = codeMatch[1].trim();
        } else if (streamingText.value.includes('<!DOCTYPE html>')) {
            // 找到完整的HTML文档
            // 如果有多个DOCTYPE声明，只保留最后一个完整的HTML文档
            const htmlDocs = streamingText.value.split('<!DOCTYPE html>');
            if (htmlDocs.length > 1) {
                // 取最后一个有效的HTML文档
                finalCode = '<!DOCTYPE html>' + htmlDocs[htmlDocs.length - 1];
            } else {
                finalCode = streamingText.value.trim();
            }
        } else {
            // 尝试其他常见格式
            const simpleHtmlMatch = streamingText.value.match(/<body[^>]*>([\s\S]*?)<\/body>/);
            if (simpleHtmlMatch) {
                // 如果只有body内容，则保留原有的html和head
                const originalHtml = props.modelValue;
                const bodyContent = simpleHtmlMatch[1].trim();

                const bodyStartMatch = originalHtml.match(/<body[^>]*>/);
                const bodyEndMatch = originalHtml.match(/<\/body>/);

                if (bodyStartMatch && bodyEndMatch && bodyStartMatch.index !== undefined) {
                    const beforeBody = originalHtml.substring(0, bodyStartMatch.index + bodyStartMatch[0].length);
                    const afterBody = originalHtml.substring(originalHtml.indexOf('</body>'));
                    finalCode = beforeBody + bodyContent + afterBody;
                } else {
                    finalCode = streamingText.value;
                }
            } else {
                // 如果无法识别，就使用整个响应
                finalCode = streamingText.value;
            }
        }

        if (finalCode && finalCode.trim()) {
            // 更新编辑器内容
            emit('update:modelValue', finalCode);

            // 滚动到编辑器底部以显示最新内容
            setTimeout(() => {
                if (editorRef.value) {
                    editorRef.value.scrollToBottom();
                }
            }, 50); // 短暂延迟以确保内容已更新
        }
    } catch (error) {
        console.error('更新编辑器内容出错:', error);
    }
};

/**
 * 取消当前的流式输入
 */
const cancelStreaming = () => {
    if (controller.value) {
        controller.value.abort();
        controller.value = null;
    }

    if (updateTimer.value !== null) {
        clearInterval(updateTimer.value);
        updateTimer.value = null;
    }

    isStreaming.value = false;
    streamingProgress.value = 0;
    message.info('已取消代码生成');
};

/**
 * 提交用户输入到AI
 */
const submitPrompt = async () => {
    if (!userPrompt.value.trim()) {
        message.warning('请输入您的编程需求');
        return;
    }

    if (!selectedModel.value) {
        message.warning('请选择一个模型');
        return;
    }

    const selectedModelInfo = modelOptions.value.find(model => model.value === selectedModel.value);
    if (!selectedModelInfo) {
        message.warning('模型信息获取失败');
        return;
    }

    // 重置状态
    isLoading.value = true;
    isStreaming.value = true;
    errorMessage.value = '';
    streamingText.value = '';
    updateCount.value = 0;
    streamingProgress.value = 0;

    // 设置更新定时器，每300ms更新一次编辑器和预览
    if (updateTimer.value !== null) {
        clearInterval(updateTimer.value);
    }
    updateTimer.value = window.setInterval(() => {
        updateEditorWithStreamingText();
    }, 300);

    controller.value = new AbortController();

    try {
        // 构建请求参数，更接近chat组件的实现
        const prompt = `我是一个专业的AI开发助手，使用单文件HTML创建为不同职业人士定制的实用工具或趣味游戏。当前需求：${userPrompt.value}\n\n请生成完整、美观且功能完善的HTML代码，包含所有必要的样式和脚本。代码应当具有专业设计感、良好的用户体验和响应式布局。请只返回完整的HTML代码，不要添加任何解释。`;

        // 设置超时
        const timeoutId = setTimeout(() => {
            if (controller.value) {
                controller.value.abort();
                message.error('请求超时，请稍后重试');
                isLoading.value = false;
                isStreaming.value = false;

                if (updateTimer.value !== null) {
                    clearInterval(updateTimer.value);
                    updateTimer.value = null;
                }
            }
        }, 60000); // 60秒超时

        const params = {
            model: selectedModelInfo.value,
            modelName: selectedModelInfo.label,
            modelType: selectedModelInfo.keyType,
            modelAvatar: selectedModelInfo.modelAvatar,
            prompt: prompt,
            signal: controller.value.signal,
            options: {
                groupId: 0,
                usingNetwork: true
            },
            // 添加流式响应的处理回调
            onDownloadProgress: (progressEvent: any) => {
                try {
                    // 兼容不同版本的axios事件结构
                    const xhr = progressEvent.event?.target || progressEvent.target;
                    if (!xhr?.responseText) return;

                    const rawText = xhr.responseText;
                    const lines = rawText.split('\n').filter(Boolean);

                    // 处理所有行
                    let hasNewContent = false;
                    let newStreamingText = '';  // 用于存储完整的新响应

                    lines.forEach((line: string) => {
                        try {
                            // 尝试将字符串解析为 JSON 对象
                            const jsonObj = JSON.parse(line);
                            // 检查 jsonObj 是否具有有效的 text 字段
                            if (jsonObj.text) {
                                // 累加 text 字段的内容到新文本
                                newStreamingText += jsonObj.text;
                                hasNewContent = true;
                                console.log('接收到AI响应片段:', jsonObj.text);
                            }
                        } catch (e) {
                            // 解析JSON失败，可能是不完整的数据
                            console.log('解析JSON失败，可能是不完整的数据:', line);
                        }
                    });

                    // 更新完整的流式文本（不是追加），避免重复内容
                    if (hasNewContent) {
                        streamingText.value = newStreamingText;
                    }
                } catch (error) {
                    console.error('处理流式响应出错:', error);
                }
            }
        };

        try {
            // 发送请求
            console.log('发送AI编程请求:', {
                model: params.model,
                modelName: params.modelName,
                prompt: '请求内容较长，不完整显示'
            });

            const result = await fetchChatAPIProcess(params);
            console.log('AI编程请求完成，返回结果:', result);
            clearTimeout(timeoutId);

            // 清除定时器并最后更新一次
            if (updateTimer.value !== null) {
                clearInterval(updateTimer.value);
                updateTimer.value = null;
            }
            updateEditorWithStreamingText(); // 最后一次更新确保显示完整内容

            if (streamingText.value) {
                message.success('AI已完成编码任务');
                userPrompt.value = ''; // 清空输入框
            } else {
                message.error('获取AI响应失败');
                errorMessage.value = '获取AI响应失败，请重试';
            }
        } catch (error: unknown) {
            clearTimeout(timeoutId);

            if (updateTimer.value !== null) {
                clearInterval(updateTimer.value);
                updateTimer.value = null;
            }

            if (error instanceof Error && error.name === 'AbortError') {
                message.error('请求已取消');
                errorMessage.value = '请求已取消，请重试';
            } else {
                throw error; // 重新抛出其他错误
            }
        }
    } catch (error: any) {
        console.error('提交请求出错:', error);

        // 尝试记录更详细的错误信息
        if (error.response) {
            console.error('错误响应数据:', error.response.data);
            console.error('错误响应状态:', error.response.status);
            console.error('错误响应头:', error.response.headers);
        } else if (error.request) {
            console.error('请求已发送但没有收到响应:', error.request);
        } else {
            console.error('请求设置时发生错误:', error.message);
        }

        // 处理频率限制错误
        if (error.response?.status === 429) {
            message.error('请求频率超限，请稍后再试');
            errorMessage.value = '请求频率超限，请稍后再试';
        } else if (error.response?.status === 402) {
            message.error('余额不足，请充值后再试');
            errorMessage.value = '余额不足，请充值后再试';
        } else {
            message.error('提交请求出错');
            errorMessage.value = '提交请求出错: ' + (error.message || '未知错误');
        }
    } finally {
        isLoading.value = false;
        isStreaming.value = false;

        if (updateTimer.value !== null) {
            clearInterval(updateTimer.value);
            updateTimer.value = null;
        }
        controller.value = null;
    }
};

/**
 * 应用选择的提示模板
 */
const applyPromptTemplate = (template: any) => {
    userPrompt.value = template.template;
};

// 在组件挂载时获取模型列表并设置窗口大小监听
onMounted(() => {
    fetchModelsList();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 初始调整
    handleResize();
});

// 组件卸载前清理资源和监听器
onBeforeUnmount(() => {
    // 清除定时器
    if (updateTimer.value !== null) {
        clearInterval(updateTimer.value);
        updateTimer.value = null;
    }

    // 终止可能正在进行的请求
    if (controller.value) {
        controller.value.abort();
        controller.value = null;
    }

    // 移除窗口大小监听
    window.removeEventListener('resize', handleResize);
});

/**
 * 调用编辑器组件格式化代码
 * @param formatter 格式化函数
 */
const formatCode = (formatter: (code: string) => string) => {
    if (editorRef.value) {
        editorRef.value.formatCode(formatter);
        emit('format');
    }
};

// 暴露方法给父组件使用
defineExpose({
    formatCode
});

// 当前选中的模型信息
const selectedModelInfo = computed(() => {
    if (!selectedModel.value) return null;
    return modelOptions.value.find(model => model.value === selectedModel.value);
});
</script>

<style lang="less" scoped>
.ai-error {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 5px;
}

/* 编辑器容器样式优化 */
.editor-container {
    transition: height 0.3s ease-in-out, max-height 0.3s ease-in-out, border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    backdrop-filter: none;
    flex: 1;
    min-height: 80%;

    &:focus-within {
        border-color: rgba(24, 144, 255, 0.4);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1), 0 2px 10px rgba(0, 0, 0, 0.06);
    }

    .dark & {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        &:focus-within {
            border-color: rgba(64, 158, 255, 0.5);
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.15), 0 2px 10px rgba(0, 0, 0, 0.2);
        }
    }
}

/* AI助手区域样式优化 */
.ai-assistant-container {
    transition: box-shadow 0.3s ease, height 0.3s ease-in-out, transform 0.3s ease;
    overflow-y: auto;
    min-height: 20%;
    flex: 0.3; /* 给AI助手分配更多空间 */

    .dark & {
        /* Use Tailwind's dark:bg-gray-800 */
    }
}

/* 编辑器包裹容器 */
.editor-wrapper {
    position: relative;
}

/* 流式输入的打字动画 */
.typing-dots {
    display: inline-block;

    span {
        opacity: 0.3;
        transition: opacity 0.2s ease;

        &.active {
            opacity: 1;
        }
    }
}

/* 打字效果动画 */
.typing-effect {
    display: inline-block;
    position: relative;
    overflow: hidden;
    animation: pulse 2s infinite alternate;
}

@keyframes pulse {
    0% {
        opacity: 0.7;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.7;
    }
}

/* 进度环样式 */
.generation-progress {
    width: 24px;
    height: 24px;
}

.progress-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--progress-color, #1890ff);
    font-weight: 500;
}

/* 显示编辑中的区域 */
.editing-highlight {
    background-color: rgba(79, 192, 141, 0.1);
    animation: highlightPulse 2s infinite;
}

@keyframes highlightPulse {
    0% {
        background-color: rgba(79, 192, 141, 0.05);
    }

    50% {
        background-color: rgba(79, 192, 141, 0.15);
    }

    100% {
        background-color: rgba(79, 192, 141, 0.05);
    }
}

/* 优化底部输入区域 */
.prompt-input {
    :deep(.n-input__textarea-el) {
        min-height: 32px;
        padding: 6px 10px;
        /* Adjusted padding */
        border-radius: 6px;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        resize: none;
        /* 移除默认边框，只在focus时显示 */
        border: none;
        background-color: #f7fafc;

        &:focus {
            border-color: rgba(24, 144, 255, 0.5);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.15);
            outline: none;
        }

        .dark & {
            background-color: rgba(45, 55, 72, 0.5);

            &:focus {
                border-color: rgba(64, 158, 255, 0.6);
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

/* 按钮样式优化 */
.action-btn {
    min-height: 32px;
    /* 增加高度 */
    padding: 0 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    /* 增大字体 */
    font-weight: 500;
    transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:not(:disabled) {
        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        &:active {
            transform: translateY(0);
        }
    }

    &:disabled {
        opacity: 0.6;
    }

    svg {
        transition: transform 0.2s ease;
    }

    &:hover svg {
        transform: scale(1.1);
    }

    .dark & {
        &:not(:disabled):hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
        }
    }
}

.reset-btn {
    background-color: #f7fafc;
    border-color: #e2e8f0;
    color: #4a5568;

    &:not(:disabled):hover {
        background-color: #edf2f7;
        border-color: #cbd5e0;
        color: #2d3748;
    }

    .dark & {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;

        &:not(:disabled):hover {
            background-color: #4a5568;
            border-color: #718096;
            color: #f7fafc;
        }
    }
}

.submit-btn {
    background-color: #4299e1;
    border-color: #3182ce;

    &:not(:disabled):hover {
        background-color: #3182ce;
        border-color: #2b6cb0;
    }

    .dark & {
        background-color: #3182ce;
        border-color: #2b6cb0;

        &:not(:disabled):hover {
            background-color: #2b6cb0;
            border-color: #2c5282;
        }
    }
}

.cancel-btn {
    background-color: #f56565;
    border-color: #e53e3e;

    &:not(:disabled):hover {
        background-color: #e53e3e;
        border-color: #c53030;
    }

    .dark & {
        background-color: #e53e3e;
        border-color: #c53030;

        &:not(:disabled):hover {
            background-color: #c53030;
            border-color: #9b2c2c;
        }
    }
}

/* 移动设备下的特殊调整 */
@media (max-width: 640px) {
    .editor-container {
        min-height: 150px;
        flex: 0.6; /* 移动设备下的编辑器比例 */
    }

    .ai-assistant-container {
        min-height: 200px;
        max-height: 350px;
        padding: 10px;
        flex: 0.4; /* 移动设备下的AI助手比例 */
    }

    .control-area {
        flex-wrap: wrap;
        gap: 8px;
    }
}

/* 提示模板按钮样式优化 */
.prompt-template-btn {
    transition: all 0.2s ease;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background-color: transparent;
    color: #4a5568;

    &:hover {
        border-color: rgba(24, 144, 255, 0.4);
        color: rgba(24, 144, 255, 0.8);
    }

    &:active {
        background-color: rgba(24, 144, 255, 0.05);
    }

    .dark & {
        border-color: #4a5568;
        color: #cbd5e0;

        &:hover {
            border-color: rgba(64, 158, 255, 0.5);
            color: rgba(64, 158, 255, 0.8);
        }

        &:active {
            background-color: rgba(64, 158, 255, 0.1);
        }
    }
}

.template-icon {
    /* Color handled by template-item hover */
}

.template-item {
    border-radius: 4px;
    padding: 5px 8px;
    /* Adjusted padding */
    transition: all 0.2s ease;
    cursor: pointer;

    /* Use group-hover for icon color change */
    &.group:hover {
        background-color: rgba(24, 144, 255, 0.08);

        .dark & {
            background-color: rgba(64, 158, 255, 0.15);
        }
    }
}

.template-list {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
    /* Tailwind gray-400 / gray-100 */

    &::-webkit-scrollbar {
        width: 6px;
        /* Slightly wider scrollbar */
    }

    &::-webkit-scrollbar-track {
        background: #f7fafc;
        /* Tailwind gray-100 */
        border-radius: 6px;

        .dark & {
            background: #2d3748;
            /* Tailwind gray-800 */
        }
    }

    &::-webkit-scrollbar-thumb {
        background-color: #cbd5e0;
        /* Tailwind gray-400 */
        border-radius: 6px;
        border: 1px solid #f7fafc;
        /* Tailwind gray-100 track */

        .dark & {
            background-color: #718096;
            /* Tailwind gray-500 */
            border-color: #2d3748;
            /* Tailwind gray-800 track */
        }

        &:hover {
            background-color: #a0aec0;

            /* Tailwind gray-500 */
            .dark & {
                background-color: #a0aec0;
                /* Tailwind gray-400 */
            }
        }
    }
}

/* 图标悬停效果 */
.hover-effect {
    transition: all 0.2s ease;
    /* Faster transition */
    border-radius: 4px;
    /* Add small rounding */
    padding: 2px;
    /* Add slight padding for hover bg */

    &:hover {
        transform: scale(1.05);
        /* Reduced scale */
        /* opacity: 0.9; */
        /* Use color change instead */
        background-color: rgba(0, 0, 0, 0.04);

        /* Subtle background on hover */
        .dark & {
            background-color: rgba(255, 255, 255, 0.08);
        }
    }
}

/* 图标呼吸光效 */
.animate-glow {
    animation: glow 2.5s infinite alternate;
    /* Slightly faster */
}

@keyframes glow {
    0% {
        filter: drop-shadow(0 0 0.5px rgba(24, 144, 255, 0.3));
    }

    100% {
        filter: drop-shadow(0 0 2px rgba(24, 144, 255, 0.5));
    }
}

/* 模型选择下拉框样式优化 */
.model-select {
    :deep(.n-base-selection) {
        background-color: transparent;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
        min-height: 28px;
        /* 略微增加高度适应更大的文字 */

        &:hover {
            border-color: rgba(24, 144, 255, 0.4);
        }

        &.n-base-selection--focused {
            border-color: rgba(24, 144, 255, 0.5);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.15);
        }

        .dark & {
            border-color: #4a5568;

            &:hover {
                border-color: rgba(64, 158, 255, 0.5);
            }

            &.n-base-selection--focused {
                border-color: rgba(64, 158, 255, 0.6);
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
        }
    }

    :deep(.n-base-selection-label) {
        padding: 0 6px;
        line-height: 26px;
        /* 调整行高适应更大字体 */
    }

    :deep(.n-base-selection-tags) {
        padding: 0 6px;
    }

    :deep(.n-select-option) {
        padding: 4px 8px;
        /* 增加内边距 */
        font-size: 0.875rem;
        /* 增大字体大小 */

        &.n-select-option--selected {
            background-color: rgba(24, 144, 255, 0.1);
            color: rgba(24, 144, 255, 1);

            .dark & {
                background-color: rgba(64, 158, 255, 0.2);
                color: rgba(64, 158, 255, 1);
            }
        }
    }
}
</style>