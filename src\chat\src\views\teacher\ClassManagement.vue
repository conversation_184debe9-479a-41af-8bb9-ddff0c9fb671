<template>
  <div class="class-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">班级管理</h1>
          <p class="page-description">全面管理班级学生信息，跟踪学习进度与成长轨迹</p>
        </div>
        
        <div class="header-actions">
          <NButton type="primary" @click="showAddStudentModal = true">
            <template #icon>
              <NIcon><PersonAddOutline /></NIcon>
            </template>
            添加学生
          </NButton>
          <NButton @click="exportClassData">
            <template #icon>
              <NIcon><DownloadOutline /></NIcon>
            </template>
            导出数据
          </NButton>
        </div>
      </div>
    </div>

    <!-- 班级概览统计 -->
    <div class="overview-section">
      <NGrid :cols="1" :sm-cols="2" :lg-cols="4" :x-gap="20" :y-gap="20">
        <NGridItem>
          <NCard class="overview-card">
            <div class="overview-content">
              <div class="overview-icon total-students">
                <NIcon size="32"><PeopleOutline /></NIcon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ classStats.totalStudents }}</div>
                <div class="overview-label">总学生数</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="overview-card">
            <div class="overview-content">
              <div class="overview-icon active-students">
                <NIcon size="32"><TrendingUpOutline /></NIcon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ classStats.activeStudents }}</div>
                <div class="overview-label">活跃学生</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="overview-card">
            <div class="overview-content">
              <div class="overview-icon avg-score">
                <NIcon size="32"><TrophyOutline /></NIcon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ classStats.avgScore }}分</div>
                <div class="overview-label">平均成绩</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="overview-card">
            <div class="overview-content">
              <div class="overview-icon total-works">
                <NIcon size="32"><BookOutline /></NIcon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ classStats.totalWorks }}</div>
                <div class="overview-label">作品总数</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 功能导航标签 -->
    <div class="function-tabs">
      <NTabs v-model:value="activeTab" type="line" size="large">
        <NTabPane name="students" tab="学生列表">
          <template #tab>
            <div class="tab-content">
              <NIcon><PeopleOutline /></NIcon>
              <span>学生列表</span>
            </div>
          </template>
        </NTabPane>
        <NTabPane name="groups" tab="分组管理">
          <template #tab>
            <div class="tab-content">
              <NIcon><LayersOutline /></NIcon>
              <span>分组管理</span>
            </div>
          </template>
        </NTabPane>
        <NTabPane name="analytics" tab="数据分析">
          <template #tab>
            <div class="tab-content">
              <NIcon><BarChartOutline /></NIcon>
              <span>数据分析</span>
            </div>
          </template>
        </NTabPane>
        <NTabPane name="communication" tab="家校沟通">
          <template #tab>
            <div class="tab-content">
              <NIcon><ChatbubbleEllipsesOutline /></NIcon>
              <span>家校沟通</span>
            </div>
          </template>
        </NTabPane>
      </NTabs>
    </div>

    <!-- 学生列表 -->
    <div v-show="activeTab === 'students'" class="students-section">
      <div class="students-toolbar">
        <div class="toolbar-left">
          <NInput 
            v-model:value="searchKeyword" 
            placeholder="搜索学生姓名..." 
            clearable
            style="width: 300px;"
          >
            <template #prefix>
              <NIcon><SearchOutline /></NIcon>
            </template>
          </NInput>
          
          <NSelect
            v-model:value="filterGroup"
            :options="groupOptions"
            placeholder="选择分组"
            clearable
            style="width: 150px;"
          />
          
          <NSelect
            v-model:value="sortBy"
            :options="sortOptions"
            placeholder="排序方式"
            style="width: 150px;"
          />
        </div>
        
        <div class="toolbar-right">
          <NButtonGroup>
            <NButton
              :type="viewMode === 'grid' ? 'primary' : 'default'"
              @click="viewMode = 'grid'"
            >
              <template #icon>
                <NIcon><GridOutline /></NIcon>
              </template>
            </NButton>
            <NButton
              :type="viewMode === 'list' ? 'primary' : 'default'"
              @click="viewMode = 'list'"
            >
              <template #icon>
                <NIcon><ListOutline /></NIcon>
              </template>
            </NButton>
          </NButtonGroup>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="students-grid">
        <NGrid :cols="1" :sm-cols="2" :md-cols="3" :lg-cols="4" :x-gap="20" :y-gap="20">
          <NGridItem v-for="student in filteredStudents" :key="student.id">
            <NCard class="student-card" hoverable @click="viewStudentProfile(student.id)">
              <div class="student-avatar">
                <NAvatar 
                  :src="student.avatar" 
                  :size="60"
                  round
                >
                  {{ student.name.charAt(0) }}
                </NAvatar>
                <div 
                  class="status-indicator"
                  :class="{ 'online': student.isOnline, 'offline': !student.isOnline }"
                ></div>
              </div>
              
              <div class="student-info">
                <h3 class="student-name">{{ student.name }}</h3>
                <p class="student-id">学号: {{ student.studentId }}</p>
                
                <div class="student-stats">
                  <div class="stat-item">
                    <span class="stat-label">作品:</span>
                    <span class="stat-value">{{ student.worksCount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">平均分:</span>
                    <span class="stat-value">{{ student.avgScore }}</span>
                  </div>
                </div>
                
                <div class="student-progress">
                  <div class="progress-label">学习进度</div>
                  <NProgress 
                    :percentage="student.progress" 
                    :color="getProgressColor(student.progress)"
                    :show-indicator="false"
                  />
                  <div class="progress-text">{{ student.progress }}%</div>
                </div>
                
                <div class="student-actions">
                  <NButton size="small" @click.stop="viewStudentProfile(student.id)">
                    查看详情
                  </NButton>
                  <NDropdown
                    :options="getStudentActions(student)"
                    @select="handleStudentAction($event, student)"
                    @click.stop
                  >
                    <NButton size="small" quaternary>
                      <template #icon>
                        <NIcon><EllipsisHorizontalOutline /></NIcon>
                      </template>
                    </NButton>
                  </NDropdown>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 列表视图 -->
      <div v-else class="students-list">
        <NDataTable
          :columns="tableColumns"
          :data="filteredStudents"
          :pagination="tablePagination"
          :row-key="(row) => row.id"
          @update:page="handlePageChange"
        />
      </div>
    </div>

    <!-- 分组管理 -->
    <div v-show="activeTab === 'groups'" class="groups-section">
      <div class="groups-header">
        <h2>分组管理</h2>
        <NButton type="primary" @click="showCreateGroupModal = true">
          <template #icon>
            <NIcon><AddCircleOutline /></NIcon>
          </template>
          创建分组
        </NButton>
      </div>
      
      <div class="groups-grid">
        <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="group in studentGroups" :key="group.id">
            <NCard class="group-card">
              <div class="group-header">
                <div class="group-info">
                  <h3 class="group-name">{{ group.name }}</h3>
                  <p class="group-description">{{ group.description }}</p>
                </div>
                <div class="group-actions">
                  <NButton size="small" @click="editGroup(group)">编辑</NButton>
                </div>
              </div>
              
              <div class="group-stats">
                <div class="stat-row">
                  <span>成员数量: {{ group.members.length }}人</span>
                  <span>平均分: {{ group.avgScore }}分</span>
                </div>
              </div>
              
              <div class="group-members">
                <div class="members-label">成员列表:</div>
                <div class="members-avatars">
                  <NAvatar
                    v-for="member in group.members.slice(0, 5)"
                    :key="member.id"
                    :size="32"
                    :src="member.avatar"
                    class="member-avatar"
                  >
                    {{ member.name.charAt(0) }}
                  </NAvatar>
                  <span v-if="group.members.length > 5" class="more-members">
                    +{{ group.members.length - 5 }}
                  </span>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>
    </div>

    <!-- 数据分析 -->
    <div v-show="activeTab === 'analytics'" class="analytics-section">
      <div class="analytics-cards">
        <NGrid :cols="1" :lg-cols="2" :x-gap="20" :y-gap="20">
          <NGridItem>
            <NCard title="学习进度分布">
              <div class="chart-placeholder">
                <div class="chart-content">
                  <div class="progress-bars">
                    <div v-for="range in progressRanges" :key="range.label" class="progress-range">
                      <div class="range-label">{{ range.label }}</div>
                      <div class="range-bar">
                        <div 
                          class="range-fill" 
                          :style="{ width: range.percentage + '%', backgroundColor: range.color }"
                        ></div>
                      </div>
                      <div class="range-count">{{ range.count }}人</div>
                    </div>
                  </div>
                </div>
              </div>
            </NCard>
          </NGridItem>
          
          <NGridItem>
            <NCard title="作品完成情况">
              <div class="chart-placeholder">
                <div class="chart-content">
                  <div class="completion-stats">
                    <div v-for="stat in completionStats" :key="stat.label" class="completion-item">
                      <div class="completion-circle" :style="{ backgroundColor: stat.color }">
                        {{ stat.count }}
                      </div>
                      <div class="completion-label">{{ stat.label }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>
    </div>

    <!-- 家校沟通 -->
    <div v-show="activeTab === 'communication'" class="communication-section">
      <div class="communication-header">
        <h2>家校沟通记录</h2>
        <NButton type="primary">
          <template #icon>
            <NIcon><MailOutline /></NIcon>
          </template>
          发送通知
        </NButton>
      </div>
      
      <div class="communication-list">
        <NList>
          <NListItem v-for="record in communicationRecords" :key="record.id">
            <div class="communication-item">
              <div class="comm-header">
                <div class="comm-student">{{ record.studentName }}</div>
                <div class="comm-time">{{ formatTime(record.time) }}</div>
              </div>
              <div class="comm-content">{{ record.content }}</div>
              <div class="comm-status">
                <NTag :type="record.status === 'read' ? 'success' : 'warning'">
                  {{ record.status === 'read' ? '已读' : '未读' }}
                </NTag>
              </div>
            </div>
          </NListItem>
        </NList>
      </div>
    </div>

    <!-- 添加学生弹窗 -->
    <NModal
      v-model:show="showAddStudentModal"
      preset="dialog"
      title="添加学生"
      positive-text="确认"
      negative-text="取消"
      @positive-click="addStudent"
    >
      <NForm ref="addStudentFormRef" :model="newStudent" :rules="studentRules">
        <NFormItem label="学生姓名" path="name">
          <NInput v-model:value="newStudent.name" placeholder="请输入学生姓名" />
        </NFormItem>
        <NFormItem label="学号" path="studentId">
          <NInput v-model:value="newStudent.studentId" placeholder="请输入学号" />
        </NFormItem>
        <NFormItem label="联系方式" path="contact">
          <NInput v-model:value="newStudent.contact" placeholder="请输入家长联系方式" />
        </NFormItem>
        <NFormItem label="所属分组" path="groupId">
          <NSelect
            v-model:value="newStudent.groupId"
            :options="groupOptions"
            placeholder="选择分组"
          />
        </NFormItem>
      </NForm>
    </NModal>

    <!-- 创建分组弹窗 -->
    <NModal
      v-model:show="showCreateGroupModal"
      preset="dialog"
      title="创建分组"
      positive-text="确认"
      negative-text="取消"
      @positive-click="createGroup"
    >
      <NForm ref="createGroupFormRef" :model="newGroup" :rules="groupRules">
        <NFormItem label="分组名称" path="name">
          <NInput v-model:value="newGroup.name" placeholder="请输入分组名称" />
        </NFormItem>
        <NFormItem label="分组描述" path="description">
          <NInput
            v-model:value="newGroup.description"
            type="textarea"
            placeholder="请输入分组描述"
          />
        </NFormItem>
      </NForm>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, h, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import {
  NCard,
  NGrid,
  NGridItem,
  NButton,
  NIcon,
  NTabs,
  NTabPane,
  NInput,
  NSelect,
  NButtonGroup,
  NAvatar,
  NProgress,
  NDropdown,
  NDataTable,
  NList,
  NListItem,
  NTag,
  NModal,
  NForm,
  NFormItem
} from 'naive-ui';
import {
  PersonAddOutline,
  DownloadOutline,
  PeopleOutline,
  TrendingUpOutline,
  TrophyOutline,
  BookOutline,
  LayersOutline,
  BarChartOutline,
  ChatbubbleEllipsesOutline,
  SearchOutline,
  GridOutline,
  ListOutline,
  EllipsisHorizontalOutline,
  AddCircleOutline,
  MailOutline
} from '@vicons/ionicons5';

const router = useRouter();
const message = useMessage();

// 响应式状态
const activeTab = ref('students');
const searchKeyword = ref('');
const filterGroup = ref(null);
const sortBy = ref('name');
const viewMode = ref('grid');
const showAddStudentModal = ref(false);
const showCreateGroupModal = ref(false);

// 表单引用
const addStudentFormRef = ref();
const createGroupFormRef = ref();

// 表单数据
const newStudent = ref({
  name: '',
  studentId: '',
  contact: '',
  groupId: null
});

const newGroup = ref({
  name: '',
  description: ''
});

// 班级统计数据
const classStats = ref({
  totalStudents: 32,
  activeStudents: 28,
  avgScore: 87.5,
  totalWorks: 156
});

// 学生数据
const students = ref([
  {
    id: 1,
    name: '张小明',
    studentId: 'S001',
    avatar: '',
    isOnline: true,
    worksCount: 12,
    avgScore: 92,
    progress: 85,
    groupId: 1,
    contact: '138****1234',
    lastActive: new Date(),
    joinDate: new Date('2024-09-01')
  },
  {
    id: 2,
    name: '李小红',
    studentId: 'S002',
    avatar: '',
    isOnline: false,
    worksCount: 8,
    avgScore: 88,
    progress: 78,
    groupId: 1,
    contact: '139****5678',
    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
    joinDate: new Date('2024-09-01')
  },
  {
    id: 3,
    name: '王小强',
    studentId: 'S003',
    avatar: '',
    isOnline: true,
    worksCount: 15,
    avgScore: 95,
    progress: 92,
    groupId: 2,
    contact: '137****9012',
    lastActive: new Date(),
    joinDate: new Date('2024-09-01')
  },
  {
    id: 4,
    name: '赵小美',
    studentId: 'S004',
    avatar: '',
    isOnline: false,
    worksCount: 6,
    avgScore: 82,
    progress: 65,
    groupId: 2,
    contact: '136****3456',
    lastActive: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    joinDate: new Date('2024-09-01')
  }
]);

// 分组数据
const studentGroups = ref([
  {
    id: 1,
    name: '创意小组',
    description: '专注于创意写作和绘本创作',
    members: students.value.filter(s => s.groupId === 1),
    avgScore: 90,
    createdAt: new Date('2024-09-15')
  },
  {
    id: 2,
    name: '探索小组',
    description: '科学探索和逻辑思维训练',
    members: students.value.filter(s => s.groupId === 2),
    avgScore: 88.5,
    createdAt: new Date('2024-09-20')
  }
]);

// 家校沟通记录
const communicationRecords = ref([
  {
    id: 1,
    studentName: '张小明',
    content: '孩子最近在AI创作方面表现很突出，建议家长多鼓励...',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'read'
  },
  {
    id: 2,
    studentName: '李小红',
    content: '学习进度需要加强，建议增加练习时间...',
    time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    status: 'unread'
  }
]);

// 选项配置
const groupOptions = computed(() => [
  { label: '全部分组', value: null },
  ...studentGroups.value.map(group => ({
    label: group.name,
    value: group.id
  }))
]);

const sortOptions = [
  { label: '按姓名', value: 'name' },
  { label: '按成绩', value: 'avgScore' },
  { label: '按进度', value: 'progress' },
  { label: '按活跃度', value: 'lastActive' }
];

// 进度分布数据
const progressRanges = computed(() => [
  { label: '优秀 (90-100%)', percentage: 25, count: 8, color: '#52c41a' },
  { label: '良好 (70-89%)', percentage: 50, count: 16, color: '#1890ff' },
  { label: '一般 (50-69%)', percentage: 20, count: 6, color: '#faad14' },
  { label: '需加强 (<50%)', percentage: 5, count: 2, color: '#ff4d4f' }
]);

// 完成情况统计
const completionStats = computed(() => [
  { label: '已完成', count: 124, color: '#52c41a' },
  { label: '进行中', count: 28, color: '#1890ff' },
  { label: '未开始', count: 4, color: '#d9d9d9' }
]);

// 过滤后的学生列表
const filteredStudents = computed(() => {
  let filtered = students.value;
  
  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(student => 
      student.name.toLowerCase().includes(keyword) ||
      student.studentId.toLowerCase().includes(keyword)
    );
  }
  
  // 分组过滤
  if (filterGroup.value) {
    filtered = filtered.filter(student => student.groupId === filterGroup.value);
  }
  
  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'avgScore':
        return b.avgScore - a.avgScore;
      case 'progress':
        return b.progress - a.progress;
      case 'lastActive':
        return new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
      default:
        return a.name.localeCompare(b.name);
    }
  });
  
  return filtered;
});

// 表格列配置
const tableColumns = [
  {
    title: '学生',
    key: 'student',
    render(row: any) {
      return h('div', { class: 'flex items-center gap-3' }, [
        h(NAvatar, { size: 36, src: row.avatar }, { default: () => row.name.charAt(0) }),
        h('div', [
          h('div', { class: 'font-medium' }, row.name),
          h('div', { class: 'text-sm text-gray-500' }, row.studentId)
        ])
      ]);
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row: any) {
      return h(NTag, { 
        type: row.isOnline ? 'success' : 'default',
        size: 'small'
      }, { default: () => row.isOnline ? '在线' : '离线' });
    }
  },
  {
    title: '作品数',
    key: 'worksCount'
  },
  {
    title: '平均分',
    key: 'avgScore'
  },
  {
    title: '学习进度',
    key: 'progress',
    render(row: any) {
      return h(NProgress, {
        percentage: row.progress,
        color: getProgressColor(row.progress),
        showIndicator: true
      });
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row: any) {
      return h(NButton, {
        size: 'small',
        onClick: () => viewStudentProfile(row.id)
      }, { default: () => '查看详情' });
    }
  }
];

// 表格分页
const tablePagination = {
  pageSize: 10
};

// 表单验证规则
const studentRules = {
  name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ]
};

const groupRules = {
  name: [
    { required: true, message: '请输入分组名称', trigger: 'blur' }
  ]
};

// 方法
const getProgressColor = (progress: number) => {
  if (progress >= 90) return '#52c41a';
  if (progress >= 70) return '#1890ff';
  if (progress >= 50) return '#faad14';
  return '#ff4d4f';
};

const getStudentActions = (student: any) => [
  {
    key: 'view',
    label: '查看详情'
  },
  {
    key: 'edit',
    label: '编辑信息'
  },
  {
    key: 'contact',
    label: '联系家长'
  },
  {
    key: 'remove',
    label: '移出班级'
  }
];

const viewStudentProfile = (studentId: number) => {
  router.push(`/teacher/class/student/${studentId}`);
};

const handleStudentAction = (key: string, student: any) => {
  switch (key) {
    case 'view':
      viewStudentProfile(student.id);
      break;
    case 'edit':
      message.info('编辑功能开发中...');
      break;
    case 'contact':
      message.info('联系家长功能开发中...');
      break;
    case 'remove':
      message.warning('移出班级功能开发中...');
      break;
  }
};

const handlePageChange = (page: number) => {
  console.log('分页变化:', page);
};

const addStudent = () => {
  addStudentFormRef.value?.validate((errors: any) => {
    if (!errors) {
      // 添加学生逻辑
      const student = {
        id: Date.now(),
        ...newStudent.value,
        avatar: '',
        isOnline: false,
        worksCount: 0,
        avgScore: 0,
        progress: 0,
        lastActive: new Date(),
        joinDate: new Date()
      };
      students.value.push(student);
      
      // 重置表单
      newStudent.value = {
        name: '',
        studentId: '',
        contact: '',
        groupId: null
      };
      showAddStudentModal.value = false;
      message.success('学生添加成功');
    }
  });
};

const createGroup = () => {
  createGroupFormRef.value?.validate((errors: any) => {
    if (!errors) {
      // 创建分组逻辑
      const group = {
        id: Date.now(),
        ...newGroup.value,
        members: [],
        avgScore: 0,
        createdAt: new Date()
      };
      studentGroups.value.push(group);
      
      // 重置表单
      newGroup.value = {
        name: '',
        description: ''
      };
      showCreateGroupModal.value = false;
      message.success('分组创建成功');
    }
  });
};

const editGroup = (group: any) => {
  message.info('编辑分组功能开发中...');
};

const exportClassData = () => {
  message.info('导出数据功能开发中...');
};

const formatTime = (time: Date) => {
  return time.toLocaleDateString('zh-CN') + ' ' + time.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

onMounted(() => {
  // 初始化数据
  console.log('班级管理页面已加载');
});
</script>

<style scoped lang="scss">
.class-management-page {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }
  }
  
  .title-section {
    flex: 1;
  }
  
  .page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }
  
  .page-description {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }
}

.overview-section {
  margin-bottom: 32px;
  
  .overview-card {
    height: 100%;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  .overview-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .overview-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    
    &.total-students { background: #3b82f6; }
    &.active-students { background: #10b981; }
    &.avg-score { background: #f59e0b; }
    &.total-works { background: #8b5cf6; }
  }
  
  .overview-info {
    flex: 1;
  }
  
  .overview-number {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
  }
  
  .overview-label {
    color: #6b7280;
    font-size: 14px;
  }
}

.function-tabs {
  background: white;
  border-radius: 12px;
  padding: 16px 24px 0;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .tab-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.students-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.students-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .toolbar-left {
    display: flex;
    gap: 12px;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
  }
}

.students-grid {
  .student-card {
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  .student-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 16px;
    
    .status-indicator {
      position: absolute;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid white;
      bottom: 2px;
      right: 2px;
      
      &.online { background: #52c41a; }
      &.offline { background: #d9d9d9; }
    }
  }
  
  .student-info {
    .student-name {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: #1f2937;
    }
    
    .student-id {
      color: #6b7280;
      font-size: 14px;
      margin: 0 0 16px 0;
    }
    
    .student-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        gap: 2px;
        
        .stat-label {
          font-size: 12px;
          color: #6b7280;
        }
        
        .stat-value {
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
    
    .student-progress {
      margin-bottom: 16px;
      
      .progress-label {
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 4px;
      }
      
      .progress-text {
        font-size: 12px;
        color: #6b7280;
        text-align: center;
        margin-top: 4px;
      }
    }
    
    .student-actions {
      display: flex;
      gap: 8px;
      justify-content: center;
    }
  }
}

.groups-section, .analytics-section, .communication-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.groups-header, .communication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h2 {
    margin: 0;
    color: #1f2937;
  }
}

.group-card {
  height: 100%;
  
  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    
    .group-info {
      flex: 1;
      
      .group-name {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
      
      .group-description {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
      }
    }
  }
  
  .group-stats {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .stat-row {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      color: #4b5563;
    }
  }
  
  .group-members {
    .members-label {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 8px;
    }
    
    .members-avatars {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .member-avatar {
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      .more-members {
        font-size: 12px;
        color: #6b7280;
      }
    }
  }
}

.chart-placeholder {
  .chart-content {
    padding: 20px 0;
  }
  
  .progress-bars {
    .progress-range {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      
      .range-label {
        width: 120px;
        font-size: 14px;
        color: #4b5563;
      }
      
      .range-bar {
        flex: 1;
        height: 20px;
        background: #f1f5f9;
        border-radius: 10px;
        overflow: hidden;
        
        .range-fill {
          height: 100%;
          border-radius: 10px;
          transition: width 0.3s ease;
        }
      }
      
      .range-count {
        width: 40px;
        text-align: right;
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
  
  .completion-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    
    .completion-item {
      text-align: center;
      
      .completion-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin: 0 auto 8px;
      }
      
      .completion-label {
        font-size: 14px;
        color: #4b5563;
      }
    }
  }
}

.communication-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  
  .comm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .comm-student {
      font-weight: 600;
      color: #1f2937;
    }
    
    .comm-time {
      font-size: 12px;
      color: #6b7280;
    }
  }
  
  .comm-content {
    color: #4b5563;
    margin-bottom: 8px;
    line-height: 1.5;
  }
  
  .comm-status {
    display: flex;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .class-management-page {
    padding: 16px;
  }
  
  .students-grid {
    :deep(.n-grid-item) {
      grid-column: span 1 !important;
    }
  }
}
</style> 