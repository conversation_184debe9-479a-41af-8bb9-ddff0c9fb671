const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:9520/api';
let TOKEN = ''; // 将在登录后设置

// 创建不带Token的axios实例，用于登录
const authApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Website-Domain': 'http://localhost:9002'
  },
  maxRedirects: 0, // 禁用重定向
  validateStatus: function (status) {
    return status >= 200 && status < 500; // 接受所有非500错误的状态码
  }
});

// 创建带Token的axios实例，用于其他API调用
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Website-Domain': 'http://localhost:9002'
  },
  maxRedirects: 0, // 禁用重定向
  validateStatus: function (status) {
    return status >= 200 && status < 500; // 接受所有非500错误的状态码
  }
});

// 添加请求拦截器，记录请求信息并添加Token
api.interceptors.request.use(function (config) {
  // 添加Token到请求头
  if (TOKEN) {
    config.headers['Authorization'] = `Bearer ${TOKEN}`;
  }

  console.log('发送请求:', config.method.toUpperCase(), config.url);
  console.log('请求头:', config.headers);
  if (config.data) {
    console.log('请求体:', config.data);
  }
  return config;
}, function (error) {
  console.error('请求错误:', error);
  return Promise.reject(error);
});

// 添加响应拦截器，记录响应信息
api.interceptors.response.use(function (response) {
  console.log('响应状态:', response.status);
  console.log('响应头:', response.headers);
  return response;
}, function (error) {
  console.error('响应错误:', error);
  if (error.response) {
    console.error('错误状态:', error.response.status);
    console.error('错误数据:', error.response.data);
  }
  return Promise.reject(error);
});

// 为authApi添加相同的拦截器
authApi.interceptors.request.use(function (config) {
  console.log('Auth 发送请求:', config.method.toUpperCase(), config.url);
  console.log('Auth 请求头:', config.headers);
  if (config.data) {
    console.log('Auth 请求体:', config.data);
  }
  return config;
}, function (error) {
  console.error('Auth 请求错误:', error);
  return Promise.reject(error);
});

authApi.interceptors.response.use(function (response) {
  console.log('Auth 响应状态:', response.status);
  console.log('Auth 响应头:', response.headers);
  return response;
}, function (error) {
  console.error('Auth 响应错误:', error);
  if (error.response) {
    console.error('Auth 错误状态:', error.response.status);
    console.error('Auth 错误数据:', error.response.data);
  }
  return Promise.reject(error);
});

// 测试创建角色
async function testCreateCharacter() {
  try {
    console.log('测试创建角色...');
    const response = await api.post('/storybook/character', {
      name: '测试角色',
      characterType: 'boy',
      appearance: '一个可爱的小男孩',
      personalityTraits: { traits: ['活泼', '开朗', '勇敢'] },
      imageUrl: 'https://example.com/image.jpg'
    });

    console.log('创建角色成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('创建角色失败:', error.response ? error.response.data : error.message);
    return null;
  }
}

// 测试获取用户角色库
async function testGetUserCharacters() {
  try {
    console.log('测试获取用户角色库...');
    const response = await api.get('/storybook/character/mine');

    console.log(`获取用户角色库成功，共${response.data.length}个角色`);
    console.log('角色列表:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取用户角色库失败:', error.response ? error.response.data : error.message);
    return [];
  }
}

// 测试更新角色
async function testUpdateCharacter(characterId) {
  try {
    console.log(`测试更新角色 ID:${characterId}...`);
    const response = await api.put(`/storybook/character/${characterId}`, {
      name: '更新后的角色名称',
      appearance: '更新后的外观描述',
      isFavorite: 1
    });

    console.log('更新角色成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('更新角色失败:', error.response ? error.response.data : error.message);
    return null;
  }
}

// 测试删除角色
async function testDeleteCharacter(characterId) {
  try {
    console.log(`测试删除角色 ID:${characterId}...`);
    const response = await api.delete(`/storybook/character/${characterId}`);

    console.log('删除角色成功:', response.data);
    return true;
  } catch (error) {
    console.error('删除角色失败:', error.response ? error.response.data : error.message);
    return false;
  }
}

// 测试获取角色模板
async function testGetCharacterTemplates() {
  try {
    console.log('测试获取角色模板...');
    const response = await api.get('/storybook/character/templates');

    console.log(`获取角色模板成功，共${response.data.length}个模板`);
    console.log('模板列表:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取角色模板失败:', error.response ? error.response.data : error.message);
    return [];
  }
}

// 登录并获取Token
async function login() {
  try {
    console.log('尝试登录...');
    const response = await authApi.post('/auth/login', {
      username: 'super',
      password: '123456'
    });

    if (response.data && response.data.success && response.data.data && response.data.data.token) {
      TOKEN = response.data.data.token;
      console.log('登录成功，获取到Token');
      return true;
    } else {
      console.error('登录失败:', response.data);
      return false;
    }
  } catch (error) {
    console.error('登录请求失败:', error.response ? error.response.data : error.message);
    return false;
  }
}

// 运行所有测试
async function runTests() {
  console.log('开始测试角色库API...');

  // 首先登录获取Token
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.error('登录失败，无法继续测试');
    return;
  }

  // 测试获取用户角色库
  const initialCharacters = await testGetUserCharacters();

  // 测试创建角色
  const createdCharacter = await testCreateCharacter();

  // 如果创建成功，测试更新和删除
  if (createdCharacter && createdCharacter.id) {
    // 测试更新角色
    await testUpdateCharacter(createdCharacter.id);

    // 再次获取用户角色库，确认更新成功
    await testGetUserCharacters();

    // 测试删除角色
    await testDeleteCharacter(createdCharacter.id);

    // 再次获取用户角色库，确认删除成功
    await testGetUserCharacters();
  }

  // 测试获取角色模板
  await testGetCharacterTemplates();

  console.log('角色库API测试完成');
}

// 执行测试
runTests();
