<template>
  <div>
    <n-card :bordered="false" class="mt-4 mb-4 proCard">
      <div class="flex items-center justify-between mb-4">
        <div class="text-lg font-bold">数据统计概览</div>
        <div class="flex items-center">
          <n-date-picker
            v-model:value="dateRange"
            type="daterange"
            clearable
            class="w-300px mr-2"
            :shortcuts="dateShortcuts"
          />
          <n-button type="primary" @click="loadStats">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新
          </n-button>
        </div>
      </div>

      <n-spin :show="loading">
        <div class="grid grid-cols-4 gap-4 mb-6">
          <n-card :bordered="false" class="stat-card bg-blue-50">
            <div class="text-3xl font-bold text-blue-600">{{ stats.totalStorybooks || 0 }}</div>
            <div class="text-gray-500">绘本总数</div>
          </n-card>
          <n-card :bordered="false" class="stat-card bg-green-50">
            <div class="text-3xl font-bold text-green-600">{{ stats.totalViews || 0 }}</div>
            <div class="text-gray-500">总浏览量</div>
          </n-card>
          <n-card :bordered="false" class="stat-card bg-purple-50">
            <div class="text-3xl font-bold text-purple-600">{{ stats.totalLikes || 0 }}</div>
            <div class="text-gray-500">总点赞量</div>
          </n-card>
          <n-card :bordered="false" class="stat-card bg-orange-50">
            <div class="text-3xl font-bold text-orange-600">{{ stats.totalUsers || 0 }}</div>
            <div class="text-gray-500">创作用户数</div>
          </n-card>
        </div>

        <!-- 趋势图 -->
        <div class="mb-6">
          <n-card :bordered="false" title="绘本创作趋势">
            <div ref="trendChartRef" style="width: 100%; height: 400px"></div>
          </n-card>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <!-- 绘本分类统计 -->
          <n-card :bordered="false" title="绘本分类统计">
            <div ref="categoryChartRef" style="width: 100%; height: 300px"></div>
          </n-card>

          <!-- 用户活跃度统计 -->
          <n-card :bordered="false" title="用户活跃度统计">
            <div ref="userActivityChartRef" style="width: 100%; height: 300px"></div>
          </n-card>
        </div>
      </n-spin>
    </n-card>

    <!-- 图像资源统计 -->
    <n-card :bordered="false" class="mt-4 mb-4 proCard">
      <template #header>
        <div class="text-lg font-bold">图像资源统计</div>
      </template>

      <n-spin :show="imageStatsLoading">
        <div class="grid grid-cols-3 gap-4 mb-6">
          <n-card :bordered="false" class="stat-card bg-teal-50">
            <div class="text-3xl font-bold text-teal-600">{{ imageStats.totalImages || 0 }}</div>
            <div class="text-gray-500">图像总数</div>
          </n-card>
          <n-card :bordered="false" class="stat-card bg-indigo-50">
            <div class="text-3xl font-bold text-indigo-600">{{ imageStats.approvedImages || 0 }}</div>
            <div class="text-gray-500">已审核通过</div>
          </n-card>
          <n-card :bordered="false" class="stat-card bg-red-50">
            <div class="text-3xl font-bold text-red-600">{{ imageStats.rejectedImages || 0 }}</div>
            <div class="text-gray-500">已拒绝</div>
          </n-card>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <!-- 图像类型分布 -->
          <n-card :bordered="false" title="图像类型分布">
            <div ref="imageTypeChartRef" style="width: 100%; height: 300px"></div>
          </n-card>

          <!-- 图像生成趋势 -->
          <n-card :bordered="false" title="图像生成趋势">
            <div ref="imageTrendChartRef" style="width: 100%; height: 300px"></div>
          </n-card>
        </div>
      </n-spin>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { useMessage } from 'naive-ui';
import { RefreshOutline } from '@vicons/ionicons5';
import { getStorybookStats, getImageStats } from '@/api/modules/storybook';
import * as echarts from 'echarts/core';
import { BarChart, LineChart, PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  PieChart,
  CanvasRenderer,
]);

const message = useMessage();

// 日期范围
const dateRange = ref<[number, number]>([
  new Date().getTime() - 30 * 24 * 60 * 60 * 1000,
  new Date().getTime(),
]);

// 日期快捷选项
const dateShortcuts = {
  '最近7天': [() => new Date().getTime() - 7 * 24 * 60 * 60 * 1000, () => new Date().getTime()],
  '最近30天': [() => new Date().getTime() - 30 * 24 * 60 * 60 * 1000, () => new Date().getTime()],
  '最近90天': [() => new Date().getTime() - 90 * 24 * 60 * 60 * 1000, () => new Date().getTime()],
};

// 统计数据
const stats = ref<any>({
  totalStorybooks: 0,
  totalViews: 0,
  totalLikes: 0,
  totalUsers: 0,
  dailyStats: [],
});
const loading = ref(false);

// 图像统计数据
const imageStats = ref<any>({
  totalImages: 0,
  approvedImages: 0,
  rejectedImages: 0,
  byType: [],
  byDate: [],
});
const imageStatsLoading = ref(false);

// 图表引用
const trendChartRef = ref<HTMLElement | null>(null);
const categoryChartRef = ref<HTMLElement | null>(null);
const userActivityChartRef = ref<HTMLElement | null>(null);
const imageTypeChartRef = ref<HTMLElement | null>(null);
const imageTrendChartRef = ref<HTMLElement | null>(null);

// 图表实例
let trendChart: echarts.ECharts | null = null;
let categoryChart: echarts.ECharts | null = null;
let userActivityChart: echarts.ECharts | null = null;
let imageTypeChart: echarts.ECharts | null = null;
let imageTrendChart: echarts.ECharts | null = null;

// 加载统计数据
const loadStats = async () => {
  if (!dateRange.value) {
    message.warning('请选择日期范围');
    return;
  }

  loading.value = true;
  try {
    const [startDate, endDate] = dateRange.value;
    const startDateStr = new Date(startDate).toISOString().split('T')[0];
    const endDateStr = new Date(endDate).toISOString().split('T')[0];

    const res = await getStorybookStats({
      startDate: startDateStr,
      endDate: endDateStr,
    });

    stats.value = res.data;
    renderCharts();
  } catch (error) {
    console.error('加载统计数据失败', error);
    message.error('加载统计数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载图像统计数据
const loadImageStats = async () => {
  if (!dateRange.value) {
    return;
  }

  imageStatsLoading.value = true;
  try {
    const [startDate, endDate] = dateRange.value;
    const startDateStr = new Date(startDate).toISOString().split('T')[0];
    const endDateStr = new Date(endDate).toISOString().split('T')[0];

    const res = await getImageStats({
      startDate: startDateStr,
      endDate: endDateStr,
    });

    imageStats.value = res.data;
    renderImageCharts();
  } catch (error) {
    console.error('加载图像统计数据失败', error);
    message.error('加载图像统计数据失败');
  } finally {
    imageStatsLoading.value = false;
  }
};

// 渲染图表
const renderCharts = () => {
  renderTrendChart();
  renderCategoryChart();
  renderUserActivityChart();
};

// 渲染图像图表
const renderImageCharts = () => {
  renderImageTypeChart();
  renderImageTrendChart();
};

// 渲染趋势图
const renderTrendChart = () => {
  if (!trendChartRef.value) return;

  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value);
  }

  const dates = stats.value.dailyStats.map((item: any) => item.date);
  const newStorybooks = stats.value.dailyStats.map((item: any) => item.newStorybookCount);
  const views = stats.value.dailyStats.map((item: any) => item.totalViewCount);

  const option = {
    title: {
      text: '绘本创作与浏览趋势',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['新增绘本', '浏览量'],
    },
    xAxis: {
      type: 'category',
      data: dates,
    },
    yAxis: [
      {
        type: 'value',
        name: '新增绘本',
      },
      {
        type: 'value',
        name: '浏览量',
      },
    ],
    series: [
      {
        name: '新增绘本',
        type: 'bar',
        data: newStorybooks,
      },
      {
        name: '浏览量',
        type: 'line',
        yAxisIndex: 1,
        data: views,
      },
    ],
  };

  trendChart.setOption(option);
};

// 渲染分类图
const renderCategoryChart = () => {
  if (!categoryChartRef.value) return;

  if (!categoryChart) {
    categoryChart = echarts.init(categoryChartRef.value);
  }

  // 模拟数据
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['童话故事', '科普知识', '历史故事', '冒险故事', '其他'],
    },
    series: [
      {
        name: '绘本分类',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 335, name: '童话故事' },
          { value: 310, name: '科普知识' },
          { value: 234, name: '历史故事' },
          { value: 135, name: '冒险故事' },
          { value: 148, name: '其他' },
        ],
      },
    ],
  };

  categoryChart.setOption(option);
};

// 渲染用户活跃度图
const renderUserActivityChart = () => {
  if (!userActivityChartRef.value) return;

  if (!userActivityChart) {
    userActivityChart = echarts.init(userActivityChartRef.value);
  }

  // 模拟数据
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '活跃用户',
        type: 'line',
        smooth: true,
        data: [120, 132, 101, 134, 90, 230, 210],
        areaStyle: {},
      },
    ],
  };

  userActivityChart.setOption(option);
};

// 渲染图像类型分布图
const renderImageTypeChart = () => {
  if (!imageTypeChartRef.value) return;

  if (!imageTypeChart) {
    imageTypeChart = echarts.init(imageTypeChartRef.value);
  }

  const typeNames = ['绘本页面', '角色形象', '场景背景', '其他'];
  const data = imageStats.value.byType.map((item: any) => ({
    value: item.count,
    name: typeNames[item.imageType] || '未知',
  }));

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: typeNames,
    },
    series: [
      {
        name: '图像类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: data.length > 0 ? data : [
          { value: 335, name: '绘本页面' },
          { value: 310, name: '角色形象' },
          { value: 234, name: '场景背景' },
          { value: 135, name: '其他' },
        ],
      },
    ],
  };

  imageTypeChart.setOption(option);
};

// 渲染图像生成趋势图
const renderImageTrendChart = () => {
  if (!imageTrendChartRef.value) return;

  if (!imageTrendChart) {
    imageTrendChart = echarts.init(imageTrendChartRef.value);
  }

  const dates = imageStats.value.byDate.map((item: any) => item.date);
  const counts = imageStats.value.byDate.map((item: any) => item.count);

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: dates.length > 0 ? dates : ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '图像生成数',
        type: 'line',
        smooth: true,
        data: counts.length > 0 ? counts : [120, 132, 101, 134, 90, 230, 210],
        areaStyle: {},
      },
    ],
  };

  imageTrendChart.setOption(option);
};

// 监听日期变化
watch(dateRange, () => {
  loadStats();
  loadImageStats();
});

// 窗口大小变化时重新调整图表大小
window.addEventListener('resize', () => {
  trendChart?.resize();
  categoryChart?.resize();
  userActivityChart?.resize();
  imageTypeChart?.resize();
  imageTrendChart?.resize();
});

onMounted(() => {
  loadStats();
  loadImageStats();
});
</script>

<style scoped>
.proCard {
  border-radius: 4px;
}

.stat-card {
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
