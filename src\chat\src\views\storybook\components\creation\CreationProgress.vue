<template>
  <div class="creation-progress">
    <div
      v-for="step in totalSteps"
      :key="step"
      class="progress-step"
      :class="{ active: currentStep >= step, completed: currentStep > step }"
    >
      <div class="step-icon">{{ getStepIcon(step) }}</div>
      <div class="step-label">{{ getStepLabel(step) }}</div>
      <div v-if="step < totalSteps" class="step-connector"></div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentStep: {
    type: Number,
    required: true
  },
  totalSteps: {
    type: Number,
    required: true
  }
});

const getStepIcon = (step) => {
  const icons = ['📝', '👤', '🏞️', '✏️', '📖'];
  return icons[step - 1];
};

const getStepLabel = (step) => {
  const labels = ['大纲', '角色', '场景', '创作', '完成'];
  return labels[step - 1];
};
</script>

<style scoped>
.creation-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.creation-progress::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  opacity: 0.5;
}

.dark .creation-progress {
  background-color: #252542;
  border-color: rgba(50, 50, 93, 0.25);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  z-index: 1;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid white;
}

.dark .step-icon {
  background-color: #334155;
  border-color: #252542;
}

.step-label {
  font-size: 0.7rem;
  color: #64748b;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
  max-width: 70px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .step-label {
  color: #94a3b8;
}

.step-connector {
  position: absolute;
  top: 16px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: #e2e8f0;
  z-index: 0;
}

.dark .step-connector {
  background-color: #334155;
}

.progress-step.active .step-icon {
  background-color: #3b82f6;
  color: white;
  transform: scale(1.1) translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.4);
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%, 100% { transform: scale(1.1) translateY(-2px); }
  50% { transform: scale(1.2) translateY(-4px); }
}

.dark .progress-step.active .step-icon {
  background-color: #60a5fa;
  box-shadow: 0 4px 10px rgba(96, 165, 250, 0.4);
}

.progress-step.active .step-label {
  color: #1e293b;
  font-weight: 700;
  transform: scale(1.03);
}

.dark .progress-step.active .step-label {
  color: #e2e8f0;
}

.progress-step.completed .step-icon {
  background-color: #10b981;
  color: white;
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

.dark .progress-step.completed .step-icon {
  background-color: #34d399;
  box-shadow: 0 2px 6px rgba(52, 211, 153, 0.3);
}

.progress-step.completed .step-connector {
  background-color: #10b981;
  height: 3px;
}

.dark .progress-step.completed .step-connector {
  background-color: #34d399;
}
</style>
