import { computed, ref, watchEffect, type Ref } from 'vue';

/**
 * 全屏模式控制相关功能
 * @param deviceType 当前设备类型引用
 * @param isLandscape 是否横屏引用
 * @returns 全屏相关状态和方法
 */
export function useFullscreen(
    deviceType: Ref<'mobile' | 'tablet' | 'desktop'>,
    isLandscape: Ref<boolean>
) {
    // 是否全屏
    const isFullscreen = ref(false);

    // 全屏预览
    const toggleFullscreen = () => {
        isFullscreen.value = !isFullscreen.value;

        if (isFullscreen.value) {
            try {
                const previewContainer = document.getElementById('preview-container');
                if (previewContainer && previewContainer.requestFullscreen) {
                    previewContainer.requestFullscreen();
                }
            } catch (error) {
                console.error('全屏模式失败:', error);
            }
        } else {
            if (document.fullscreenElement && document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    };

    // 调整全屏模式下设备尺寸
    const adjustFullscreenDeviceSize = () => {
        if (isFullscreen.value) {
            const container = document.querySelector('.fullscreen-container .preview-frame');
            if (container) {
                // 强制重新计算样式
                container.classList.add('orientation-change');
                // 移除类以触发重新渲染
                setTimeout(() => {
                    container.classList.remove('orientation-change');
                }, 10);
            }
        }
    };

    // 监听全屏状态变化
    watchEffect(() => {
        const handleFullscreenChange = () => {
            isFullscreen.value = !!document.fullscreenElement;
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    });

    // 全屏模式下的特殊容器类
    const fullscreenContainerClass = computed(() => {
        if (!isFullscreen.value) return '';

        let classes = [];

        // 设备类型
        if (deviceType.value === 'mobile') {
            classes.push('fullscreen-mobile');
        } else if (deviceType.value === 'tablet') {
            classes.push('fullscreen-tablet');
        } else {
            classes.push('fullscreen-desktop');
        }

        // 屏幕方向
        if (isLandscape.value && deviceType.value !== 'desktop') {
            classes.push('fullscreen-landscape');
        } else {
            classes.push('fullscreen-portrait');
        }

        return classes.join(' ');
    });

    return {
        isFullscreen,
        toggleFullscreen,
        adjustFullscreenDeviceSize,
        fullscreenContainerClass
    };
} 