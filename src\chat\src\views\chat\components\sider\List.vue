<script setup lang="ts">
import { fetchCollectAppAPI, fetchCleanInvalidCollectionsAPI } from '@/api/appStore';
import type { ResData } from '@/api/types';
import { SvgIcon } from '@/components/common';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import {
  useAppCatStore,
  useAppStore,
  useAuthStore,
  useChatStore,
  useGlobalStoreWithOut,
} from '@/store';
import { ApplicationTwo, Down, Up } from '@icon-park/vue-next';
import { NScrollbar, useMessage } from 'naive-ui';
import { computed, inject, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ListItem from './ListItem.vue';

const { isMobile } = useBasicLayout();
const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const chatStore = useChatStore();
const authStore = useAuthStore();
const ms = useMessage();

const customKeyId = ref(100);
const appCatStore = useAppCatStore();
const showMoreSticky = ref(false);
const showMoreMineApps = ref(false);
const showMorePlugins = ref(false);

// 控制列表高度
const scrollbarRef = ref(null);
const maxListHeight = ref('calc(100vh - 180px)');

// 动态计算侧边栏内容区域的最大高度
function updateMaxHeight() {
  // 获取视口高度
  const viewportHeight = window.innerHeight;

  // 考虑页面内边距和其他元素的高度
  const pagePadding = 16; // 页面的内边距 (p-2 = 8px * 2)
  const headerHeight = 48; // 侧边栏顶部高度
  const footerHeight = 64; // 侧边栏底部高度
  const extraMargin = 8;  // 额外边距，确保与输入框对齐

  // 计算可用高度
  const availableHeight = viewportHeight - pagePadding - headerHeight - footerHeight - extraMargin;
  maxListHeight.value = `${availableHeight}px`;

  // 确保高度不小于最小值
  if (availableHeight < 300) {
    maxListHeight.value = '300px';
  }
}

// 监听窗口大小变化
onMounted(() => {
  updateMaxHeight();
  window.addEventListener('resize', updateMaxHeight);

  // 监听自定义事件，更新侧边栏高度
  window.addEventListener('update-sidebar-height', updateMaxHeight);

  // 初始化后稍微延迟再次调整高度，确保对齐
  setTimeout(updateMaxHeight, 100);

  // 查询应用列表
  appCatStore.queryMineApps();
  // 查询插件列表
  chatStore.queryPlugins();
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', updateMaxHeight);
  window.removeEventListener('update-sidebar-height', updateMaxHeight);
});

// const catId = computed(() => appCatStore.catId);

const dataSources = computed(() => chatStore.groupList);
const groupKeyWord = computed(() => chatStore.groupKeyWord);
watch(dataSources, () => (customKeyId.value = customKeyId.value + 1));
watch(groupKeyWord, () => (customKeyId.value = customKeyId.value + 1));
const isStreamIn = computed(() => {
  return chatStore.isStreamIn !== undefined ? chatStore.isStreamIn : false;
});
const isLogin = computed(() => authStore.isLogin);

const createNewChatGroup = inject(
  'createNewChatGroup',
  async (appId?: number) => {
    // 默认逻辑或简单的提示信息
  }
) as (appId?: number) => Promise<void>;

const mineApps = computed(() => {
  return appCatStore.mineApps;
});

// utc格式转换
function formatUtcTime(utcTime: Date | string | number) {
  // 如果是数字，直接使用
  if (typeof utcTime === 'number') {
    return utcTime;
  }

  try {
    const date = new Date(utcTime);
    if (isNaN(date.getTime())) {
      console.error('无效的日期格式:', utcTime);
      return 0; // 返回一个很早的时间戳，确保它会被排在最后
    }
    const shanghaiTime = date.getTime() + 8 * 60 * 60 * 1000;
    return shanghaiTime;
  } catch (error) {
    console.error('日期转换错误:', error);
    return 0;
  }
}

const today = new Date().setHours(0, 0, 0, 0);

const stickyList = computed(() => {
  // 先筛选出收藏的对话
  const filteredList = dataSources.value.filter((item) =>
    groupKeyWord.value
      ? item.title.includes(groupKeyWord.value) && item.isSticky && !item.params
      : item.isSticky && !item.params
  );

  // 按更新时间降序排序，确保最新的对话显示在最前面
  return filteredList.sort((a, b) => {
    // 使用ID进行比较，这样新建的对话（ID较大）会显示在前面
    // 先按照更新时间排序
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA; // 先按时间降序排列
    }

    // 如果时间相同，则按ID降序排列（新建的对话会有更大的ID）
    return b.uuid - a.uuid;
  });
});

const todayList = computed(() => {
  // 先筛选出今日对话
  const filteredList = dataSources.value.filter((item: any) => {
    if (groupKeyWord.value)
      return (
        item.title.includes(groupKeyWord.value) &&
        !item.isSticky &&
        formatUtcTime(item.updatedAt) >= today &&
        !item.params
      );
    else
      return (
        !item.isSticky && formatUtcTime(item.updatedAt) >= today && !item.params
      );
  });

  // 按更新时间降序排序，确保最新的对话显示在最前面
  return filteredList.sort((a, b) => {
    // 使用ID进行比较，这样新建的对话（ID较大）会显示在前面
    // 先按照更新时间排序
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA; // 先按时间降序排列
    }

    // 如果时间相同，则按ID降序排列（新建的对话会有更大的ID）
    return b.uuid - a.uuid;
  });
});

const otherList = computed(() => {
  // 先筛选出历史对话
  const filteredList = dataSources.value.filter((item: any) => {
    if (groupKeyWord.value)
      return (
        item.title.includes(groupKeyWord.value) &&
        !item.isSticky &&
        formatUtcTime(item.updatedAt) < today &&
        !item.params
      );
    else
      return (
        !item.isSticky && formatUtcTime(item.updatedAt) < today && !item.params
      );
  });

  // 按更新时间降序排序，确保最新的对话显示在最前面
  return filteredList.sort((a, b) => {
    // 使用ID进行比较，这样新建的对话（ID较大）会显示在前面
    // 先按照更新时间排序
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA; // 先按时间降序排列
    }

    // 如果时间相同，则按ID降序排列（新建的对话会有更大的ID）
    return b.uuid - a.uuid;
  });
});

/* 选中切换对话 */
async function handleSelect(group: Chat.History) {
  if (isStreamIn.value) {
    ms.info('AI回复中，请稍后再试');
    return;
  }
  const { uuid } = group;
  if (isActive(uuid)) return;

  await chatStore.setActiveGroup(uuid);
  if (route.name !== 'Chat') router.replace('/chat');

  if (isMobile.value) appStore.setSiderCollapsed(true);
}

async function addNewChatGroupFromApp(appId: number) {
  appCatStore.queryMineApps();
  createNewChatGroup(appId);
  // router.replace({ path: '/chat', query: { appId: appId } });
}

/* 删除对话组 */
async function handleDelete(params: Chat.History) {
  event?.stopPropagation();
  await chatStore.deleteGroup(params);
  await chatStore.queryMyGroup();
  if (isMobile.value) appStore.setSiderCollapsed(true);
}

const useGlobalStore = useGlobalStoreWithOut();

/* 判断是不是当前选中 */
function isActive(uuid: number) {
  return chatStore.active === uuid;
}

// 移除应用中心相关函数

async function handleCollect(appId?) {
  // app.loading = true;
  try {
    const res: ResData = await fetchCollectAppAPI({ appId: appId });
    // ms.success(res.data);
    await appCatStore.queryMineApps();
    // app.loading = false;
  } catch (error) {
    // app.loading = false;
  }
}

// 清理无效的收藏应用（已被删除的应用）
async function cleanInvalidCollections() {
  try {
    const res: ResData = await fetchCleanInvalidCollectionsAPI();
    ms.success(res.data.message);
    // 刷新收藏列表
    await appCatStore.queryMineApps();
  } catch (error) {
    ms.error('清理失败，请稍后再试');
    console.error('清理无效收藏失败:', error);
  }
}

// 检查是否有无效的收藏应用
const hasInvalidApps = computed(() => {
  return mineApps.value.some((app: any) => app.isInvalid);
});



// 切换插件选择
function togglePluginSelection(plugin: any) {
  if (chatStore.currentPlugin?.parameters === plugin.parameters) {
    // 清除当前选中的插件
    chatStore.setUsingPlugin(null);
  } else {
    // 设置当前插件为选中的插件
    chatStore.setUsingPlugin(plugin);
  }
}

// 监听登录状态的变化
watch(
  () => authStore.isLogin,
  (newValue, oldValue) => {
    if (newValue === true) {
      // 如果登录了，则查询我的应用
      appCatStore.queryMineApps();
      chatStore.queryMyGroup();
      // 查询插件列表
      chatStore.queryPlugins();
    }
  },
  { immediate: true } // 立即执行，以处理组件加载时的逻辑
);
</script>

<template>
  <NScrollbar ref="scrollbarRef" class="px-3 overflow-hidden scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent" :style="{ maxHeight: maxListHeight }">
    <div class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white dark:to-gray-800 opacity-30 pointer-events-none h-full w-full z-10"></div>
    <div class="flex flex-col gap-1 text-sm w-full pt-2">
      <template v-if="!dataSources.length">
        <div
          class="flex flex-col items-center mt-4 text-center text-neutral-300"
        >
          <SvgIcon icon="ri:inbox-line" class="mb-2 text-3xl" />
          <span>{{ $t('common.noData') }}</span>
        </div>
      </template>
      <template v-else>
        <!-- 自由聊天选项 -->
        <div
          class="relative flex items-center gap-2 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-gray-50 group dark:hover:bg-gray-700 font-medium text-sm text-gray-700 dark:text-gray-300 border-l-2 border-l-transparent transition-all duration-300 mb-2 hover-float"
          @click="createNewChatGroup()"
        >
          <div class="w-7 h-7 rounded-full bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center shadow-sm border border-blue-100 dark:border-blue-800 transition-all duration-300 group-hover:shadow-md">
            <SvgIcon icon="ri:chat-1-line" class="text-blue-500 dark:text-blue-400" />
          </div>
          <span>自由聊天</span>
        </div>

        <!-- 创作工具分类 -->
        <p class="mt-3 mb-2 text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3">
          创作工具
        </p>

        <!-- 插件列表 -->
        <div
          v-for="plugin in showMorePlugins ? chatStore.pluginList : chatStore.pluginList?.slice(0, 3)"
          :key="plugin.parameters"
          class="relative flex items-center gap-2 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-gray-50 group dark:hover:bg-gray-700 font-medium text-sm text-gray-700 dark:text-gray-300 border-l-2 border-l-transparent transition-all duration-300 hover-float"
          @click="togglePluginSelection(plugin)"
        >
          <div class="w-7 h-7 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center overflow-hidden shadow-sm border border-gray-200 dark:border-gray-500">
            <span
              v-if="plugin.pluginImg && plugin.pluginImg.startsWith('emoji:')"
              class="text-xl"
            >
              {{ plugin.pluginImg.replace('emoji:', '') }}
            </span>
            <img
              v-else-if="plugin.pluginImg"
              :src="plugin.pluginImg"
              alt="Plugin icon"
              class="w-full h-full object-cover"
            />
            <span v-else class="text-sm font-medium">
              {{ plugin.pluginName.charAt(0) }}
            </span>
          </div>
          <span>{{ plugin.pluginName }}</span>
        </div>

        <!-- 更多插件按钮 -->
        <button
          class="relative flex items-center gap-1 px-3 py-1 rounded-md cursor-pointer text-gray-500 dark:text-gray-400 text-xs font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
          v-if="chatStore.pluginList?.length > 3"
          @click="showMorePlugins = !showMorePlugins"
        >
          {{ showMorePlugins ? t('chat.collapse') : t('chat.more') }}
          <Down v-if="!showMorePlugins" theme="outline" size="20" />
          <Up v-else theme="outline" size="20" />
        </button>

        <!-- 无插件时的提示 -->
        <div v-if="!chatStore.pluginList?.length" class="flex flex-col items-center justify-center py-4 text-gray-400 dark:text-gray-500">
          <SvgIcon icon="ri:plug-line" class="text-2xl mb-2" />
          <span class="text-sm">暂无可用插件</span>
        </div>

        <!-- 收藏应用 -->
        <div class="mt-4 mb-2 px-3 flex justify-between items-center">
          <p class="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            收藏应用
          </p>
          <!-- 清理按钮，只在有无效应用时显示 -->
          <button
            v-if="hasInvalidApps"
            @click.stop="cleanInvalidCollections"
            class="text-xs text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 transition-colors"
            title="清理已删除的应用"
          >
            <SvgIcon icon="ri:delete-bin-line" class="text-sm" />
            清理
          </button>
        </div>

        <!-- 收藏的应用 -->
        <div
          v-for="app in (showMoreMineApps ? mineApps : mineApps.slice(0, 3))"
          :key="app.appId"
          @click="app.isInvalid ? cleanInvalidCollections() : addNewChatGroupFromApp(app.appId)"
          class="relative flex items-center gap-2 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-gray-50 group dark:hover:bg-gray-700 font-medium text-sm border-l-2 border-l-transparent transition-all duration-300 hover-float"
          :class="app.isInvalid ? 'text-gray-400 dark:text-gray-500 line-through' : 'text-gray-700 dark:text-gray-300'"
          :title="app.isInvalid ? '此应用已被删除，点击清理' : ''"
        >
          <div
            class="w-7 h-7 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center overflow-hidden shadow-sm border border-gray-200 dark:border-gray-500"
            :class="{'opacity-50': app.isInvalid}"
          >
            <span
              v-if="app.coverImg && app.coverImg.startsWith('emoji:')"
              class="text-xl"
            >
              {{ app.coverImg.replace('emoji:', '') }}
            </span>
            <img
              v-else-if="app.coverImg && app.coverImg !== '未知'"
              :src="app.coverImg"
              alt="app cover"
              class="w-full h-full object-cover"
            />
            <span v-else class="text-sm font-medium">
              {{ app.appName.charAt(0) }}
            </span>
          </div>
          <span>{{ app.appName }}</span>
          <!-- 无效应用标记 -->
          <span v-if="app.isInvalid" class="ml-auto text-xs text-red-400 dark:text-red-500">
            已删除
          </span>
        </div>

        <!-- 移除更多应用按钮，应用中心已移动到教学工具页面 -->

        <!-- 更多应用按钮 -->
        <button
          class="relative flex items-center gap-1 px-3 py-1 rounded-md cursor-pointer text-gray-500 dark:text-gray-400 text-xs font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
          v-if="mineApps.length > 3"
          @click="showMoreMineApps = !showMoreMineApps"
        >
          {{ showMoreMineApps ? t('chat.collapse') : t('chat.more') }}
          <Down v-if="!showMoreMineApps" theme="outline" size="20" />
          <Up v-else theme="outline" size="20" />
        </button>

        <!-- 无收藏应用时的提示 -->
        <div v-if="mineApps.length === 0" class="flex flex-col items-center justify-center py-4 text-gray-400 dark:text-gray-500">
          <SvgIcon icon="ri:star-line" class="text-2xl mb-2" />
          <span class="text-sm">暂无收藏应用</span>
        </div>

        <!-- 收藏夹 -->
        <ListItem
          v-if="stickyList.length"
          :key="`stickyList-${showMoreSticky}-${stickyList}`"
          :title="t('chat.favorites')"
          :data-sources="showMoreSticky ? stickyList : stickyList.slice(0, 5)"
          @select="handleSelect"
          @delete="handleDelete"
        />

        <button
          class="relative flex items-center gap-3 px-3 break-all rounded-md cursor-pointer text-gray-900 dark:text-gray-400 text-xs font-bold transition-all duration-300 hover-float"
          v-if="stickyList.length > 5"
          @click="showMoreSticky = !showMoreSticky"
        >
          {{ showMoreSticky ? t('chat.collapse') : t('chat.more') }}
          <Down v-if="!showMoreSticky" theme="outline" size="20" />
          <Up v-else theme="outline" size="20" />
        </button>

        <!-- 对话历史 -->
        <p class="mt-4 mb-2 text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3">
          对话历史
        </p>

        <!-- 合并今日和历史对话 -->
        <div
          v-for="item in [...todayList, ...otherList]"
          :key="item.uuid"
          @click="handleSelect(item)"
          class="relative flex items-center gap-2 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-gray-50 group dark:hover:bg-gray-700 font-medium text-sm text-gray-700 dark:text-gray-300 border-l-2 transition-all duration-300 hover-float"
          :class="isActive(item.uuid) ? 'border-l-primary-500 bg-gray-50 dark:bg-gray-700' : 'border-l-transparent'"
        >
          <div class="w-7 h-7 rounded-full bg-gradient-to-br from-gray-100 to-gray-50 dark:from-gray-600 dark:to-gray-700 flex items-center justify-center shadow-sm border border-gray-200 dark:border-gray-500 transition-all duration-300 group-hover:shadow-md">
            <SvgIcon icon="ri:chat-history-line" class="text-gray-500 dark:text-gray-400" />
          </div>
          <span class="truncate max-w-[180px]">{{ item.title }}</span>
        </div>
      </template>
    </div>
  </NScrollbar>
</template>
