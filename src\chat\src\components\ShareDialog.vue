<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
    @click.self="closeDialog"
  >
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md shadow-xl">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">分享链接</h3>
        <button
          @click="closeDialog"
          class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="mb-4">
        <div class="flex items-center mb-2">
          <input
            type="text"
            readonly
            :value="shareUrl"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
          <button
            @click="copyShareUrl"
            class="ml-2 p-2 bg-primary-600 hover:bg-primary-500 text-white rounded-md flex items-center justify-center"
            title="复制链接"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
            </svg>
          </button>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400">扫描二维码或复制链接分享</p>
      </div>
      
      <div class="flex justify-center mb-4">
        <div class="bg-white p-2 rounded-lg shadow-md">
          <img
            :src="qrCodeUrl"
            :alt="'分享链接二维码'"
            :title="'扫描访问分享内容'"
            class="w-48 h-48"
          />
        </div>
      </div>
      
      <div class="flex justify-end">
        <button
          @click="closeDialog"
          class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md mr-2"
        >
          关闭
        </button>
        <button
          @click="copyShareUrl"
          class="px-4 py-2 bg-primary-600 hover:bg-primary-500 text-white rounded-md"
        >
          复制链接
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';

interface Props {
  shareUrl: string;
  onClose: () => void;
}

const props = defineProps<Props>();
const emit = defineEmits(['close']);

// 生成二维码URL
const qrCodeUrl = computed(() => {
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(props.shareUrl)}`;
});

// 复制分享链接
const copyShareUrl = async () => {
  try {
    await navigator.clipboard.writeText(props.shareUrl);
    window.$message?.success('分享链接已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    window.$message?.error('复制失败，请手动复制');
  }
};

// 关闭对话框
const closeDialog = () => {
  emit('close');
  props.onClose();
};
</script>

<style scoped>
/* 添加过渡动画 */
.fixed {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>
