import { Injectable, Logger } from '@nestjs/common';
import { GlobalConfigService } from '../../modules/globalConfig/globalConfig.service';
import { UploadService } from '../../modules/upload/upload.service';
import { ChatLogService } from '../../modules/chatLog/chatLog.service';
import { OpenAIChatService } from './openaiChat.service';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * OpenAI图像服务
 * 统一处理OpenAI的图像生成功能，包括DALL-E-3和gpt-image-1模型
 */
@Injectable()
export class OpenAIImageService {
  constructor(
    private readonly globalConfigService: GlobalConfigService,
    private readonly uploadService: UploadService,
    private readonly chatLogService: ChatLogService,
    private readonly openAIChatService: OpenAIChatService,
  ) {
    this.logger = new Logger(OpenAIImageService.name);
  }

  private readonly logger: Logger;

  /**
   * 生成图像
   * @param inputs 输入参数
   * @param buildMessageFromParentMessageId 构建消息历史的函数（用于连续对话模式）
   */
  async generateImage(inputs, buildMessageFromParentMessageId?) {
    Logger.log(`开始提交 OpenAI 图像生成任务，模型: ${inputs.model}`, 'OpenAIImageService');
    const {
      apiKey,
      model,
      proxyUrl,
      prompt,
      extraParam,
      timeout,
      onSuccess,
      onFailure,
      groupId,
      assistantLogId,
      modelName,
    } = inputs;

    // 获取配置
    const { isDalleChat, isImageGenChat, isConvertToBase64 } = await this.globalConfigService.getConfigs([
      'isDalleChat',
      'isImageGenChat',
      'isConvertToBase64',
    ]);

    // 确定使用哪个配置项来控制连续对话
    const useContinuousChat = model === 'dall-e-3' ? isDalleChat === '1' : isImageGenChat === '1';
    
    // 处理提示词
    let imagePrompt;
    if (useContinuousChat && buildMessageFromParentMessageId) {
      try {
        Logger.log('已开启连续对话模式，使用上下文优化提示词', 'OpenAIImageService');
        const { messagesHistory } = await buildMessageFromParentMessageId(
          `参考上文，结合我的需求，给出详细的图像描述。我的需求是：${prompt}`,
          {
            groupId,
            systemMessage:
              '你是一个图像提示词生成工具，请根据用户的要求，结合上下文，用一段详细的文字描述用户需要的图像。不要包含任何礼貌性的寒暄，只需要场景的描述，可以适当联想细节以丰富图像效果。',
            maxModelTokens: 8000,
            maxRounds: 5,
            fileInfo: '',
          },
          this.chatLogService
        );
        imagePrompt = await this.openAIChatService.chatFree(
          prompt,
          undefined,
          messagesHistory
        );
      } catch (error) {
        Logger.error('调用chatFree优化提示词失败：', error);
        imagePrompt = prompt;
      }
    } else {
      imagePrompt = prompt;
    }

    // 处理参数
    const size = extraParam?.size || '1024x1024';
    const quality = extraParam?.quality || 'standard';
    const style = extraParam?.style || 'vivid';
    const n = model === 'gpt-image-1' ? (extraParam?.n || 1) : 1; // gpt-image-1支持多图生成
    const responseFormat = isConvertToBase64 === '1' ? 'b64_json' : 'url';

    let result = { 
      answer: '', 
      fileInfo: '', 
      status: 2, 
      drawId: '', 
      customId: '',
      progress: '0%'
    };

    // 更新任务状态为处理中
    if (assistantLogId) {
      await this.chatLogService.updateChatLog(assistantLogId, {
        answer: '图像生成中...',
        progress: '10%',
        status: 2,
      });
    }

    try {
      // 构建API请求
      const options: AxiosRequestConfig = {
        method: 'POST',
        url: `${proxyUrl}/v1/images/generations`,
        timeout: timeout,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
        },
        data: {
          model: model,
          prompt: imagePrompt,
          n: n,
          size: size,
          quality: quality,
          style: style,
          response_format: responseFormat,
        },
      };

      // 记录请求日志
      Logger.log(
        `正在准备发送请求到 ${options.url}，payload: ${JSON.stringify(
          options.data
        )}, headers: ${JSON.stringify(options.headers)}`,
        'OpenAIImageService'
      );

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: '正在生成图像...',
          progress: '30%',
          status: 2,
        });
      }

      // 发送请求
      const response: AxiosResponse = await axios(options);
      
      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: '图像生成完成，正在处理结果...',
          progress: '70%',
          status: 2,
        });
      }

      // 处理返回的图像数据
      const images = response.data.data;
      const imageUrls = [];
      
      // 处理每张生成的图像
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        let imageUrl;
        
        // 根据响应格式获取图像URL或Base64数据
        if (responseFormat === 'b64_json') {
          // 如果是Base64格式，需要保存为文件
          const base64Data = image.b64_json;
          const buffer = Buffer.from(base64Data, 'base64');
          
          // 使用上传服务保存图像
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const currentDate = `${year}${month}/${day}`;
          
          imageUrl = await this.uploadService.uploadBuffer({
            buffer,
            filename: `openai_${model}_${Date.now()}_${i}.png`,
            dir: `images/openai/${currentDate}`,
          });
        } else {
          // 如果是URL格式，需要下载并保存
          try {
            Logger.log(`开始上传图片 ${i+1}/${images.length}`, 'OpenAIImageService');
            
            // 使用 Date 对象获取当前日期并格式化为 YYYYMM/DD
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const currentDate = `${year}${month}/${day}`;
            
            imageUrl = await this.uploadService.uploadFileFromUrl({
              url: image.url,
              dir: `images/openai/${currentDate}`,
            });
            
            Logger.log(`图片 ${i+1} 上传成功，URL: ${imageUrl}`, 'OpenAIImageService');
          } catch (error) {
            Logger.error(`上传图片 ${i+1} 过程中出现错误: ${error}`, 'OpenAIImageService');
            imageUrl = image.url; // 如果上传失败，使用原始URL
          }
        }
        
        imageUrls.push(imageUrl);
      }

      // 生成回复文本
      let replyText;
      try {
        // 如果是DALL-E-3，使用revised_prompt生成回复
        if (model === 'dall-e-3' && response.data.data[0].revised_prompt) {
          replyText = await this.openAIChatService.chatFree(
            `根据提示词{${response.data.data[0].revised_prompt}}, 模拟AI绘画机器人的语气，用中文回复，告诉用户已经画好了`
          );
        } else {
          replyText = await this.openAIChatService.chatFree(
            `根据提示词"${imagePrompt}"，我已经生成了${images.length}张图像。请用中文回复，告诉用户图像已经生成完成，并简要描述图像内容。`
          );
        }
      } catch (error) {
        replyText = `已根据您的提示"${prompt}"生成${images.length}张图像`;
        Logger.error('生成回复文本失败: ', error);
      }

      // 设置结果
      result.answer = replyText;
      result.fileInfo = imageUrls[0]; // 主图像URL
      result.status = 3; // 成功状态
      result.customId = imageUrls.length > 1 ? JSON.stringify(imageUrls) : ''; // 存储所有图像URL
      result.progress = '100%';

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          fileInfo: result.fileInfo,
          answer: result.answer,
          progress: result.progress,
          status: result.status,
          customId: result.customId,
        });
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      // 处理错误
      result.status = 5; // 失败状态
      result.progress = '0%';
      
      // 获取错误信息
      const status = error?.response?.status || 500;
      const message = error?.response?.data?.error?.message || '未知错误';
      
      Logger.error(`图像生成失败: ${status} - ${message}`, error, 'OpenAIImageService');
      
      // 根据错误类型设置不同的错误消息
      if (status === 429) {
        result.answer = '当前请求已过载，请稍后再试！';
      } else if (status === 400) {
        if (message.includes('Billing hard limit has been reached')) {
          result.answer = '当前模型key已被封禁、已冻结当前调用Key、尝试重新对话试试吧！';
        } else {
          result.answer = `请求参数错误: ${message}`;
        }
      } else if (status === 401) {
        result.answer = 'API密钥无效或已过期';
      } else if (status === 500) {
        result.answer = '生成图像失败，请检查你的提示词是否有非法描述！';
      } else {
        result.answer = `图像生成失败: ${message}`;
      }
      
      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: result.answer,
          status: result.status,
        });
      }
      
      // 调用失败回调
      if (onFailure) {
        onFailure(result);
      }
      
      return result;
    }
  }

  /**
   * DALL-E-3绘图（向后兼容方法）
   * @param inputs 输入参数
   * @param buildMessageFromParentMessageId 构建消息历史的函数
   */
  async dalleDraw(inputs, buildMessageFromParentMessageId?) {
    // 确保model是dall-e-3
    const modifiedInputs = {
      ...inputs,
      model: 'dall-e-3',
    };
    
    return this.generateImage(modifiedInputs, buildMessageFromParentMessageId);
  }
}
