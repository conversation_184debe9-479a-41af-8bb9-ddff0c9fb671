import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsObject, IsArray } from 'class-validator';

export class CreateCharacterDto {
  @ApiProperty({ description: '所属绘本ID', required: false })
  @IsOptional()
  @IsNumber()
  storybookId?: number;

  @ApiProperty({ description: '角色名称' })
  @IsNotEmpty({ message: '角色名称不能为空' })
  @IsString()
  name: string;

  @ApiProperty({ description: '角色类型', required: false })
  @IsOptional()
  @IsString()
  characterType?: string;

  @ApiProperty({ description: '外观描述', required: false })
  @IsOptional()
  @IsString()
  appearance?: string;

  @ApiProperty({ description: '性格特点', required: false })
  @IsOptional()
  @IsObject()
  personalityTraits?: object;

  @ApiProperty({ description: '外观特点', required: false })
  @IsOptional()
  @IsObject()
  appearanceTraits?: object;

  @ApiProperty({ description: '角色图片URL', required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({ description: '是否为模板(0:否,1:是)', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  isTemplate?: number;

  @ApiProperty({ description: '角色标签', required: false })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({ description: '是否收藏(0:否,1:是)', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  isFavorite?: number;

  @ApiProperty({ description: '角色在故事中的角色', required: false })
  @IsOptional()
  @IsString()
  role?: string;
}
