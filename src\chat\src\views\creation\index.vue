<template>
  <div class="creation-page">
    <!-- 创作页面头部 -->
    <CreationHeader
      :creation-type="creationType"
      :title="workTitle"
      @save="showSaveDialog"
      @new="createNew"
    />

    <div class="creation-content">
      <!-- 创作页面侧边栏 -->
      <CreationSidebar
        v-model:creation-type="creationType"
        :history-list="historyList"
        @select-history="loadHistory"
      />

      <!-- 创作工作区 -->
      <div class="creation-main">
        <!-- 创作工具栏 -->
        <CreationToolbar
          :creation-type="creationType"
          @toggle-settings="toggleSettings"
        />

        <!-- 创作工作区 -->
        <CreationWorkspace
          :creation-type="creationType"
          :content="content"
          :loading="loading"
          :streaming="streaming"
          @update:content="updateContent"
          @send="handleSend"
          @stop="handleStop"
        />
      </div>

      <!-- 创作设置面板 -->
      <CreationSettings
        v-if="showSettings"
        :creation-type="creationType"
        :settings="settings"
        @update:settings="updateSettings"
        @close="toggleSettings"
      />
    </div>

    <!-- 作品管理功能已移除 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useAuthStore, useChatStore } from '@/store';
import { fetchUpdateGroupAPI } from '@/api/group';
// 作品管理功能已移除
import CreationHeader from './components/CreationHeader.vue';
import CreationSidebar from './components/CreationSidebar.vue';
import CreationToolbar from './components/CreationToolbar.vue';
import CreationWorkspace from './components/CreationWorkspace.vue';
import CreationSettings from './components/CreationSettings.vue';

// 路由和消息
const route = useRoute();
const router = useRouter();
const message = useMessage();

// Store
const authStore = useAuthStore();
const chatStore = useChatStore();

// 状态
const creationType = ref('chat');
const workTitle = ref('未命名作品');
const content = ref('');
const loading = ref(false);
const streaming = ref(false);
const showSettings = ref(false);
// 作品管理功能已移除
const settings = ref({
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 2000,
  showPrompt: false,
});

// 历史记录
const historyList = computed(() => {
  return chatStore.groupList.map(group => ({
    id: group.uuid,
    title: group.title,
    date: new Date(group.updatedAt).toLocaleString(),
    type: getTypeFromConfig(group.config)
  }));
});

// 从配置中获取创作类型
function getTypeFromConfig(configStr: string) {
  try {
    const config = JSON.parse(configStr || '{}');
    return config.creationType || 'chat';
  } catch (e) {
    return 'chat';
  }
}

// 作品管理功能已移除

// 方法
function toggleSettings() {
  showSettings.value = !showSettings.value;
}

function updateContent(newContent: string) {
  content.value = newContent;
}

function updateSettings(newSettings: any) {
  settings.value = { ...settings.value, ...newSettings };
}

// 作品管理功能已移除

async function createNew() {
  // 创建新的对话
  await chatStore.addNewChatGroup();
  content.value = '';
  workTitle.value = '未命名作品';
}

async function loadHistory(id: number) {
  // 加载历史对话
  await chatStore.setActiveGroup(id);

  // 获取对话内容
  const chatList = chatStore.chatList;
  if (chatList && chatList.length > 0) {
    // 只获取AI回复的内容
    const aiResponses = chatList.filter(chat => !chat.inversion);
    if (aiResponses.length > 0) {
      content.value = aiResponses[aiResponses.length - 1].text || '';
    }
  }

  // 获取对话标题
  // 从 groupList 中查找对应的组
  const groupInfo = chatStore.groupList.find(group => group.uuid === id);
  if (groupInfo) {
    workTitle.value = groupInfo.title || '未命名作品';

    // 获取创作类型
    try {
      const config = JSON.parse(groupInfo.config || '{}');
      if (config.creationType) {
        creationType.value = config.creationType;
      }
    } catch (e) {
      console.error('解析配置失败:', e);
    }
  }
}

async function handleSend(prompt: string) {
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }

  if (!prompt.trim()) {
    message.warning('请输入内容');
    return;
  }

  loading.value = true;
  streaming.value = true;

  try {
    // 保存当前创作类型到配置
    const config = {
      creationType: creationType.value,
      modelInfo: {
        model: settings.value.model,
        temperature: settings.value.temperature,
        maxTokens: settings.value.maxTokens
      }
    };

    // 更新当前对话组配置
    if (chatStore.active) {
      // 使用 fetchUpdateGroupAPI 直接更新配置
      await fetchUpdateGroupAPI({
        groupId: chatStore.active,
        config: JSON.stringify(config)
      });
      // 更新后重新查询对话组
      await chatStore.queryMyGroup();
    }

    // 发送消息
    // 直接使用 chatStore 的方法
    await chatStore.addGroupChat({
      text: prompt,
      inversion: true,
      model: settings.value.model
    });

    // 添加一个AI回复
    await chatStore.addGroupChat({
      text: '',
      inversion: false,
      loading: true,
      model: settings.value.model
    });

    // 模拟生成内容
    setTimeout(() => {
      // 更新最后一条消息
      if (chatStore.chatList.length > 0) {
        const lastIndex = chatStore.chatList.length - 1;
        chatStore.updateGroupChat(lastIndex, {
          text: content.value,
          loading: false
        });
      }
    }, 1000);

    // 获取最新回复
    const chatList = chatStore.chatList;
    if (chatList && chatList.length > 0) {
      const lastMessage = chatList[chatList.length - 1];
      if (!lastMessage.inversion) {
        content.value = lastMessage.text || '';
      }
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    message.error('发送失败，请重试');
  } finally {
    loading.value = false;
    streaming.value = false;
  }
}

function handleStop() {
  // 简单地停止流式生成
  streaming.value = false;

  // 如果有正在加载的消息，将其标记为已完成
  if (chatStore.chatList.length > 0) {
    const lastIndex = chatStore.chatList.length - 1;
    const lastMessage = chatStore.chatList[lastIndex];
    if (!lastMessage.inversion && lastMessage.loading) {
      chatStore.updateGroupChatSome(lastIndex, {
        loading: false
      });
    }
  }
}

// 生命周期钩子
onMounted(async () => {
  // 查询对话组列表
  await chatStore.queryMyGroup();

  // 从URL获取创作类型
  const preset = route.query.preset as string;
  if (preset) {
    creationType.value = preset;
  }

  // 如果没有活动对话，创建一个新的
  if (!chatStore.active) {
    await createNew();
  } else {
    // 加载当前活动对话
    await loadHistory(chatStore.active);
  }
});

// 监听创作类型变化
watch(creationType, async (newType) => {
  // 更新路由
  router.push({
    path: '/creation',
    query: { preset: newType }
  });

  // 保存当前创作类型到配置
  if (chatStore.active) {
    try {
      const groupInfo = chatStore.groupList.find(group => group.uuid === chatStore.active);
      if (groupInfo) {
        const config = JSON.parse(groupInfo.config || '{}');
        config.creationType = newType;
        // 使用 fetchUpdateGroupAPI 直接更新配置
        await fetchUpdateGroupAPI({
          groupId: chatStore.active,
          config: JSON.stringify(config)
        });
        // 更新后重新查询对话组
        await chatStore.queryMyGroup();
      }
    } catch (e) {
      console.error('更新配置失败:', e);
    }
  }
});
</script>

<style scoped>
.creation-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: var(--color-gray-50);
}

.dark .creation-page {
  background-color: var(--color-gray-900);
}

.creation-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.creation-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 1rem;
}

@media (max-width: 768px) {
  .creation-content {
    flex-direction: column;
  }

  .creation-main {
    padding: 0.5rem;
  }
}
</style>
