<template>
  <div class="create-storybook">
    <!-- 儿童友好的欢迎区域 -->
    <div class="welcome-section">
      <div class="character-animation">
        <div class="character">🧚</div>
        <div class="speech-bubble">让我们一起创作一个神奇的故事吧！</div>
      </div>

      <h1 class="rainbow-title">创造新故事</h1>
      <p class="subtitle">发挥你的想象力，创造一个与众不同的故事！</p>
    </div>

    <!-- 装饰元素 -->
    <div class="decorative-items">
      <div class="dec-item dec-star">✨</div>
      <div class="dec-item dec-pencil">✏️</div>
      <div class="dec-item dec-book">📚</div>
      <div class="dec-item dec-magic">🔮</div>
    </div>

    <div class="create-card">
      <div class="create-form-container">
        <NForm ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="80">
          <NFormItem label="故事标题" path="title">
            <NInput v-model:value="formData.title" placeholder="给你的故事起个好听的名字吧..." class="kid-input" />
          </NFormItem>

          <NFormItem label="故事简介" path="description">
            <NInput
              v-model:value="formData.description"
              type="textarea"
              placeholder="写一些关于这个故事的介绍..."
              :autosize="{ minRows: 3, maxRows: 5 }"
              class="kid-input"
            />
          </NFormItem>

          <NFormItem label="封面图片">
            <div class="upload-container">
              <div v-if="formData.coverImg" class="cover-preview">
                <img :src="formData.coverImg" alt="封面预览" class="preview-image" />
                <button class="remove-button" @click="removeCover">❌</button>
              </div>

              <div v-else class="upload-box" @click="triggerUpload">
                <input
                  type="file"
                  ref="fileInput"
                  style="display: none"
                  accept="image/*"
                  @change="handleFileChange"
                />
                <div class="upload-content">
                  <div class="upload-icon">🖼️</div>
                  <div class="upload-text">点击上传封面图片</div>
                </div>
              </div>
            </div>
          </NFormItem>

          <div class="form-actions">
            <button class="create-button" @click="handleSubmit">
              <span class="button-icon">✨</span>
              <span class="button-text">创造我的故事</span>
            </button>

            <button class="cancel-button" @click="goBack">
              <span class="button-icon">↩️</span>
              <span class="button-text">返回</span>
            </button>
          </div>
        </NForm>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { NForm, NFormItem, NInput } from 'naive-ui';
import { useWorksStore } from '@/store/modules/works';

const router = useRouter();
const worksStore = useWorksStore();

// 文件上传引用
const fileInput = ref(null);

// 表单数据
const formRef = ref(null);
const formData = ref({
  title: '',
  description: '',
  coverImg: ''
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入故事标题', trigger: 'blur' },
    { max: 50, message: '标题不能超过50个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '介绍不能超过200个字符', trigger: 'blur' }
  ]
};

// 触发文件上传
const triggerUpload = () => {
  fileInput.value.click();
};

// 处理文件变更
const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.coverImg = e.target.result;
  };
  reader.readAsDataURL(file);
};

// 移除封面
const removeCover = () => {
  formData.value.coverImg = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    try {
      window.$message?.info('正在创建你的故事书...');

      // 创建绘本
      const storybook = await worksStore.createStorybook({
        title: formData.value.title,
        description: formData.value.description,
        coverImg: formData.value.coverImg,
        status: 0, // 草稿状态
        isPublic: 0, // 默认私有
        pageCount: 0,
        wordCount: 0
      });

      window.$message?.success('故事创建成功！');
      router.push(`/storybook/edit/${storybook.id}`);
    } catch (error) {
      console.error('创建故事失败:', error);
      window.$message?.error('创建故事失败');
    }
  });
};

// 返回
const goBack = () => {
  router.push('/storybook');
};
</script>

<style scoped>
.create-storybook {
  padding: 20px;
  position: relative;
  min-height: 100%;
  overflow: hidden;
}

.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.character-animation {
  position: relative;
  height: 100px;
  margin-bottom: 1rem;
}

.character {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 3rem;
  animation: float 3s ease-in-out infinite;
}

.speech-bubble {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-20%);
  background: #fff;
  border-radius: 20px;
  padding: 10px 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-size: 1rem;
  animation: bounce 2s ease-in-out infinite;
  z-index: 2;
}

.speech-bubble:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 20px;
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: #fff transparent;
}

.rainbow-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px;
  background: linear-gradient(90deg, #ff9a9e 0%, #fad0c4 25%, #fad0c4 50%, #a1c4fd 75%, #c2e9fb 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: rainbow 5s linear infinite;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.dark .subtitle {
  color: #aaa;
}

.decorative-items {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.dec-item {
  position: absolute;
  font-size: 2rem;
  opacity: 0.5;
  animation: floatSlow 7s ease-in-out infinite;
}

.dec-star {
  top: 15%;
  left: 15%;
  animation-delay: -1s;
}

.dec-pencil {
  top: 70%;
  left: 10%;
  animation-delay: -3s;
}

.dec-book {
  top: 30%;
  right: 10%;
  animation-delay: -5s;
}

.dec-magic {
  bottom: 15%;
  right: 15%;
  animation-delay: -7s;
}

.create-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 30px;
  max-width: 700px;
  margin: 0 auto;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dark .create-card {
  background: rgba(40, 44, 52, 0.9);
}

.create-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.kid-input {
  border-radius: 15px !important;
  font-size: 1rem !important;
  padding: 10px 15px !important;
}

.upload-container {
  margin-top: 10px;
}

.upload-box {
  border: 2px dashed #ddd;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.dark .upload-box {
  border-color: #444;
}

.upload-box:hover {
  border-color: #a1c4fd;
  background: rgba(161, 196, 253, 0.1);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upload-icon {
  font-size: 2.5rem;
}

.upload-text {
  font-size: 1rem;
  color: #666;
}

.dark .upload-text {
  color: #aaa;
}

.cover-preview {
  position: relative;
  max-width: 300px;
  margin: 0 auto;
}

.preview-image {
  width: 100%;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.remove-button {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #fff;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.remove-button:hover {
  transform: scale(1.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  justify-content: center;
}

.create-button, .cancel-button {
  border: none;
  border-radius: 50px;
  padding: 12px 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
}

.create-button {
  background: linear-gradient(90deg, #a1c4fd, #c2e9fb);
  color: #333;
}

.create-button:hover {
  background: linear-gradient(90deg, #a1c4fd, #a1c4fd);
  box-shadow: 0 5px 15px rgba(161, 196, 253, 0.4);
  transform: translateY(-2px);
}

.cancel-button {
  background: #f5f5f5;
  color: #666;
}

.dark .cancel-button {
  background: #333;
  color: #eee;
}

.cancel-button:hover {
  background: #eee;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .cancel-button:hover {
  background: #444;
}

.button-icon {
  font-size: 1.2rem;
}

@keyframes float {
  0% { transform: translateY(0) translateX(-50%); }
  50% { transform: translateY(-15px) translateX(-50%); }
  100% { transform: translateY(0) translateX(-50%); }
}

@keyframes floatSlow {
  0% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(0) rotate(0); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0) translateX(-20%); }
  50% { transform: translateY(-10px) translateX(-20%); }
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .rainbow-title {
    font-size: 2rem;
  }

  .create-card {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .upload-box {
    padding: 20px;
  }
}
</style>
