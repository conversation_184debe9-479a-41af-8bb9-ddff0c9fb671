<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore, useChatStore } from '@/store';
import { 
  NGrid, 
  NGridItem, 
  NCard, 
  NButton, 
  NIcon, 
  NStatistic, 
  NSpace,
  NTag,
  NProgress,
  NBadge,
  NEmpty,
  NAvatar,
  NCarousel,
  NImage
} from 'naive-ui';
import {
  CreateOutline,
  BookOutline,
  ColorPaletteOutline,
  StarOutline,
  TrendingUpOutline,
  PeopleOutline,
  HeartOutline,
  PlayCircleOutline,
  BrushOutline,
  PencilOutline
} from '@vicons/ionicons5';

const router = useRouter();
const authStore = useAuthStore();

// 用户信息
const userInfo = computed(() => authStore.userInfo);

// 模拟学生数据 - 实际应用中应该从API获取
const studentData = ref({
  stats: {
    totalWorks: 12,
    completedStories: 8,
    drafts: 4,
    likes: 56,
    views: 234
  },
  recentWorks: [
    {
      id: 1,
      title: '小兔子的冒险',
      cover: 'https://picsum.photos/300/200?random=1',
      status: 'published',
      likes: 15,
      createTime: new Date(Date.now() - 1000 * 60 * 60 * 24)
    },
    {
      id: 2,
      title: '彩虹城堡',
      cover: 'https://picsum.photos/300/200?random=2',
      status: 'draft',
      likes: 0,
      createTime: new Date(Date.now() - 1000 * 60 * 60 * 48)
    },
    {
      id: 3,
      title: '魔法森林的秘密',
      cover: 'https://picsum.photos/300/200?random=3',
      status: 'published',
      likes: 23,
      createTime: new Date(Date.now() - 1000 * 60 * 60 * 72)
    }
  ],
  achievements: [
    { name: '初来乍到', icon: '🌟', description: '完成第一个作品', unlocked: true },
    { name: '人气作家', icon: '❤️', description: '获得50个赞', unlocked: true },
    { name: '创作达人', icon: '🎨', description: '创作10个作品', unlocked: true },
    { name: '想象大师', icon: '✨', description: '创作20个作品', unlocked: false }
  ],
  classmates: [
    { name: '小明', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=xiaoming', works: 8 },
    { name: '小红', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=xiaohong', works: 12 },
    { name: '小刚', avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=xiaogang', works: 6 }
  ]
});

// 创作工具配置
const creationTools = [
  {
    title: '🎨 AI绘本创作',
    description: '用AI帮你画出心中的故事',
    color: 'primary',
    size: 'large',
    action: () => router.push('/storybook')
  },
  {
    title: '✏️ 故事续写',
    description: '续写有趣的故事情节',
    color: 'success',
    size: 'medium',
    action: () => router.push('/storybook?mode=continue')
  },
  {
    title: '🎭 角色创造',
    description: '设计你的专属角色',
    color: 'warning',
    size: 'medium',
    action: () => router.push('/storybook?mode=character')
  },
  {
    title: '🌈 场景绘制',
    description: '描绘美丽的故事场景',
    color: 'info',
    size: 'medium',
    action: () => router.push('/storybook?mode=scene')
  }
];

// 获取问候语
const getGreeting = computed(() => {
  const hour = new Date().getHours();
  const name = userInfo.value?.username || '小创作家';
  
  if (hour < 6) return `🌙 ${name}，这么晚还在创作呀？`;
  if (hour < 12) return `🌅 早上好，${name}！`;
  if (hour < 18) return `☀️ 下午好，${name}！`;
  return `🌆 晚上好，${name}！`;
});

// 计算创作进度
const creationProgress = computed(() => {
  const completed = studentData.value.stats.completedStories;
  const total = studentData.value.stats.totalWorks;
  return total > 0 ? Math.round((completed / total) * 100) : 0;
});

// 处理作品点击
const handleWorkClick = (workId: number) => {
  router.push(`/storybook/view/${workId}`);
};

// 查看更多作品
const viewAllWorks = () => {
  router.push('/storybook/my-works');
};

// 查看同学作品
const viewClassmateWorks = () => {
  router.push('/showcase');
};

onMounted(() => {
  console.log('学生仪表板已加载');
});
</script>

<template>
  <div class="student-dashboard p-4 md:p-6 bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <!-- 欢迎区域 -->
    <div class="welcome-section mb-6">
      <div class="bg-gradient-to-r from-green-400 to-green-500 text-white p-6 rounded-2xl shadow-lg relative overflow-hidden">
        <!-- 装饰元素 -->
        <div class="absolute top-2 right-4 text-6xl opacity-20">🎨</div>
        <div class="absolute bottom-2 left-4 text-4xl opacity-20">✨</div>
        
        <div class="relative z-10">
          <h1 class="text-2xl md:text-3xl font-bold mb-2">
            {{ getGreeting }}
          </h1>
          <p class="text-green-100 text-lg">
            💡 今天想创作什么有趣的故事呢？让我们一起发挥想象力吧！
          </p>
        </div>
      </div>
    </div>

    <!-- 创作统计 -->
    <div class="stats-section mb-6">
      <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
        <span class="text-2xl mr-2">📊</span>
        我的创作数据
      </h2>
      
      <NGrid :cols="4" :x-gap="12" responsive="screen" class="stats-grid">
        <NGridItem :span="1">
          <NCard class="stat-card">
            <div class="text-center">
              <div class="text-3xl mb-2">📚</div>
              <NStatistic
                label="总作品"
                :value="studentData.stats.totalWorks"
                class="stat-number"
              />
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem :span="1">
          <NCard class="stat-card">
            <div class="text-center">
              <div class="text-3xl mb-2">✅</div>
              <NStatistic
                label="已完成"
                :value="studentData.stats.completedStories"
                class="stat-number"
              />
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem :span="1">
          <NCard class="stat-card">
            <div class="text-center">
              <div class="text-3xl mb-2">❤️</div>
              <NStatistic
                label="获得点赞"
                :value="studentData.stats.likes"
                class="stat-number"
              />
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem :span="1">
          <NCard class="stat-card">
            <div class="text-center">
              <div class="text-3xl mb-2">👀</div>
              <NStatistic
                label="作品浏览"
                :value="studentData.stats.views"
                class="stat-number"
              />
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 主要内容区域 -->
    <NGrid :cols="12" :x-gap="20" :y-gap="20" responsive="screen">
      <!-- 创作工具区 -->
      <NGridItem :span="12" :lg-span="8">
        <NCard class="creation-tools-card">
          <template #header>
            <div class="flex items-center">
              <span class="text-2xl mr-2">🛠️</span>
              <span class="font-bold text-xl">创作工具箱</span>
            </div>
          </template>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 主要创作工具 -->
            <div class="md:col-span-2">
              <NButton 
                :type="creationTools[0].color"
                :size="creationTools[0].size"
                class="creation-main-btn w-full h-20 text-lg font-bold"
                @click="creationTools[0].action"
              >
                <template #icon>
                  <div class="text-3xl mr-3">🎨</div>
                </template>
                <div class="text-left">
                  <div>{{ creationTools[0].title }}</div>
                  <div class="text-sm opacity-80">{{ creationTools[0].description }}</div>
                </div>
              </NButton>
            </div>
            
            <!-- 其他创作工具 -->
            <div 
              v-for="(tool, index) in creationTools.slice(1)" 
              :key="index"
              class="creation-tool-item"
            >
              <NButton 
                :type="tool.color"
                :size="tool.size"
                class="w-full h-16 creation-tool-btn"
                @click="tool.action"
              >
                <template #icon>
                  <span class="text-xl">{{ tool.title.split(' ')[0] }}</span>
                </template>
                <div class="text-left ml-2">
                  <div class="font-medium">{{ tool.title.split(' ').slice(1).join(' ') }}</div>
                  <div class="text-xs opacity-80">{{ tool.description }}</div>
                </div>
              </NButton>
            </div>
          </div>
        </NCard>
      </NGridItem>

      <!-- 创作进度 -->
      <NGridItem :span="12" :lg-span="4">
        <NCard class="progress-card">
          <template #header>
            <div class="flex items-center">
              <span class="text-2xl mr-2">🎯</span>
              <span class="font-bold">创作进度</span>
            </div>
          </template>
          
          <div class="text-center">
            <div class="mb-4">
              <NProgress 
                type="circle" 
                :percentage="creationProgress"
                :stroke-width="12"
                :size="120"
                status="success"
              >
                <div class="text-center">
                  <div class="text-2xl font-bold text-green-600">{{ creationProgress }}%</div>
                  <div class="text-sm text-gray-500">完成度</div>
                </div>
              </NProgress>
            </div>
            
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span>已完成作品：</span>
                <span class="font-medium">{{ studentData.stats.completedStories }}</span>
              </div>
              <div class="flex justify-between">
                <span>草稿箱：</span>
                <span class="font-medium">{{ studentData.stats.drafts }}</span>
              </div>
            </div>
          </div>
        </NCard>
      </NGridItem>

      <!-- 我的最新作品 -->
      <NGridItem :span="12" :lg-span="8">
        <NCard class="recent-works-card">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="text-2xl mr-2">📖</span>
                <span class="font-bold">我的最新作品</span>
              </div>
              <NButton text type="primary" @click="viewAllWorks">
                查看全部 →
              </NButton>
            </div>
          </template>
          
          <div v-if="studentData.recentWorks.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div 
              v-for="work in studentData.recentWorks" 
              :key="work.id"
              class="work-item cursor-pointer group"
              @click="handleWorkClick(work.id)"
            >
              <div class="relative overflow-hidden rounded-lg border-2 border-gray-200 group-hover:border-green-400 transition-all">
                <NImage
                  :src="work.cover"
                  :alt="work.title"
                  class="w-full h-32 object-cover"
                  preview-disabled
                />
                <div class="absolute top-2 right-2">
                  <NTag 
                    :type="work.status === 'published' ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ work.status === 'published' ? '已发布' : '草稿' }}
                  </NTag>
                </div>
                <div class="p-3 bg-white dark:bg-gray-800">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-1 truncate">
                    {{ work.title }}
                  </h4>
                  <div class="flex items-center justify-between text-sm text-gray-500">
                    <span>{{ work.likes }} ❤️</span>
                    <span>{{ new Date(work.createTime).toLocaleDateString() }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <NEmpty v-else description="还没有作品，快去创作第一个故事吧！" size="small">
            <template #icon>
              <div class="text-6xl">📝</div>
            </template>
            <template #extra>
              <NButton type="primary" @click="router.push('/storybook')">
                开始创作
              </NButton>
            </template>
          </NEmpty>
        </NCard>
      </NGridItem>

      <!-- 成就展示 -->
      <NGridItem :span="12" :lg-span="4">
        <NCard class="achievements-card">
          <template #header>
            <div class="flex items-center">
              <span class="text-2xl mr-2">🏆</span>
              <span class="font-bold">我的成就</span>
            </div>
          </template>
          
          <div class="space-y-3">
            <div 
              v-for="achievement in studentData.achievements" 
              :key="achievement.name"
              class="achievement-item p-3 rounded-lg border"
              :class="achievement.unlocked ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200 opacity-50'"
            >
              <div class="flex items-center">
                <div class="text-2xl mr-3">{{ achievement.icon }}</div>
                <div class="flex-1">
                  <div class="font-medium text-sm">{{ achievement.name }}</div>
                  <div class="text-xs text-gray-500">{{ achievement.description }}</div>
                </div>
                <div v-if="achievement.unlocked" class="text-green-600">
                  ✅
                </div>
              </div>
            </div>
          </div>
        </NCard>
      </NGridItem>

      <!-- 同学作品推荐 -->
      <NGridItem :span="12">
        <NCard class="classmates-card">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="text-2xl mr-2">👫</span>
                <span class="font-bold">同学们的精彩作品</span>
              </div>
              <NButton text type="primary" @click="viewClassmateWorks">
                查看更多 →
              </NButton>
            </div>
          </template>
          
          <div class="flex items-center justify-center space-x-8">
            <div 
              v-for="classmate in studentData.classmates" 
              :key="classmate.name"
              class="classmate-item text-center group cursor-pointer"
              @click="viewClassmateWorks"
            >
              <div class="relative mb-2">
                <NAvatar
                  :src="classmate.avatar"
                  :size="60"
                  class="mx-auto group-hover:scale-110 transition-transform"
                />
                <div class="absolute -bottom-1 -right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded-full">
                  {{ classmate.works }}
                </div>
              </div>
              <div class="font-medium text-sm">{{ classmate.name }}</div>
              <div class="text-xs text-gray-500">{{ classmate.works }} 个作品</div>
            </div>
          </div>
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>

<style scoped lang="scss">
.student-dashboard {
  .stats-grid {
    @media (max-width: 768px) {
      :deep(.n-grid-item) {
        span: 2;
      }
    }
    
    @media (max-width: 640px) {
      :deep(.n-grid-item) {
        span: 4;
      }
    }
  }

  .stat-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    border: 2px solid transparent;
    
    &:hover {
      transform: translateY(-2px);
      border-color: #22c55e;
      box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15);
    }
    
    .stat-number {
      :deep(.n-statistic-value) {
        font-size: 1.5rem;
        font-weight: 700;
        color: #16a34a;
      }
      
      :deep(.n-statistic-label) {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }

  .creation-main-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }

  .creation-tool-btn {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }

  .work-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }

  .achievement-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateX(4px);
    }
  }

  .classmate-item {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }

  :deep(.n-card) {
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    
    .n-card-header {
      .n-card-header__main {
        font-weight: 600;
        color: #374151;
        @apply dark:text-gray-200;
      }
    }
  }

  :deep(.n-progress) {
    .n-progress-circle-fill {
      stroke: #22c55e;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .student-dashboard {
    padding: 1rem;
    
    .welcome-section {
      .bg-gradient-to-r {
        padding: 1.5rem;
        
        h1 {
          font-size: 1.5rem;
        }
        
        p {
          font-size: 1rem;
        }
      }
    }
    
    .creation-main-btn {
      height: 4rem;
      
      .text-lg {
        font-size: 1rem;
      }
    }
  }
}

// 动画效果
@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.3); }
  50% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.6); }
}

.student-dashboard {
  .creation-main-btn:hover {
    animation: pulse-glow 2s infinite;
  }
  
  .stat-card:hover {
    animation: bounce-gentle 0.6s ease-in-out;
  }
}
</style>