import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'storybook_prompt' })
export class StorybookPromptEntity extends BaseEntity {
  @Column({ comment: '提示词标题' })
  title: string;

  @Column({ comment: '提示词内容', type: 'text' })
  content: string;

  @Column({ comment: '提示词类型', nullable: true })
  type: string;

  @Column({ comment: '状态(0:禁用,1:启用)', default: 1 })
  status: number;

  @Column({ comment: '排序', default: 100 })
  order: number;
}
