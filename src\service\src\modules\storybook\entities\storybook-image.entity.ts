import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity, ManyToOne, JoinColumn } from 'typeorm';
import { StorybookEntity } from './storybook.entity';
import { StorybookPageEntity } from './storybook-page.entity';
import { StorybookCharacterEntity } from './storybook-character.entity';

@Entity({ name: 'storybook_image' })
export class StorybookImageEntity extends BaseEntity {
  @Column({ comment: '图片URL', type: 'text' })
  imageUrl: string;

  @Column({ comment: '图片描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '生成提示词', type: 'text', nullable: true })
  prompt: string;

  @Column({ comment: '使用的模型', nullable: true })
  model: string;

  @Column({ comment: '图片类型(1:角色图,2:页面图,3:封面图)', default: 2 })
  imageType: number;

  @Column({ comment: '图片质量评级(1-5星)', default: 3, nullable: true })
  qualityRating: number;

  @Column({ comment: '审核状态(0:未审核,1:已通过,2:已拒绝)', default: 0 })
  auditStatus: number;

  @Column({ comment: '审核备注', nullable: true })
  auditRemark: string;

  @Column({ comment: '所属绘本ID', nullable: true })
  storybookId: number;

  @Column({ comment: '所属页面ID', nullable: true })
  pageId: number;

  @Column({ comment: '所属角色ID', nullable: true })
  characterId: number;

  @Column({ comment: '创建用户ID' })
  userId: number;

  @Column({ comment: '图片宽度', nullable: true })
  width: number;

  @Column({ comment: '图片高度', nullable: true })
  height: number;

  @Column({ comment: '图片大小(KB)', nullable: true })
  size: number;

  @Column({ comment: '图片格式', nullable: true })
  format: string;

  @Column({ comment: '生成参数', type: 'json', nullable: true })
  generationParams: object;

  @ManyToOne(() => StorybookEntity, storybook => storybook.id, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'storybookId' })
  storybook: StorybookEntity;

  @ManyToOne(() => StorybookPageEntity, page => page.id, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'pageId' })
  page: StorybookPageEntity;

  @ManyToOne(() => StorybookCharacterEntity, character => character.id, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'characterId' })
  character: StorybookCharacterEntity;
}
