<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NInput, NButton, NGrid, NGridItem, NEmpty, NSpin, NModal, NTabs, NTabPane } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import BookReader from '@/components/BookReader/index.vue';

const router = useRouter();

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 参考资料状态
const searchQuery = ref('');
const isLoading = ref(false);
const activeCategory = ref('all');
const showDetailModal = ref(false);
const selectedReference = ref(null);



// 示例参考绘本
const referenceBooks = reactive([
  {
    id: 1,
    title: '小王子',
    author: '安托万·德·圣-埃克苏佩里',
    coverImg: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20storybook%20cover%20illustration%20for%20children',
    description: '一部关于友谊、爱、责任和人生意义的经典童话。',
    category: 'classic',
    pages: [
      { id: 1, type: 'hard', image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20cover%20illustration', text: '小王子' },
      { id: 2, image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20page%201%20illustration', text: '当我六岁的时候，在一本描述原始森林的名叫《真实的故事》的书中，看到了一副精彩的插画，画的是一条蟒蛇正在吞食一只野兽。' },
      { id: 3, image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20page%202%20illustration', text: '于是我度过了我生命中孤独的岁月，没有一个能真正谈得来的人，直到六年前在沙漠上发生了那次故障。' },
      { id: 4, type: 'hard', image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20back%20cover%20illustration', text: '小王子的故事告诉我们，真正的爱需要责任和理解。' }
    ]
  },
  {
    id: 2,
    title: '爱丽丝梦游仙境',
    author: '刘易斯·卡罗尔',
    coverImg: 'https://image.pollinations.ai/prompt/Alice%20in%20Wonderland%20storybook%20cover%20illustration%20for%20children',
    description: '一个小女孩追逐一只兔子掉进兔子洞后，进入了一个奇幻世界的故事。',
    category: 'fantasy',
    pages: [
      { id: 1, type: 'hard', image: 'https://image.pollinations.ai/prompt/Alice%20in%20Wonderland%20cover%20illustration', text: '爱丽丝梦游仙境' },
      { id: 2, image: 'https://image.pollinations.ai/prompt/Alice%20in%20Wonderland%20page%201%20illustration', text: '爱丽丝开始感到厌烦，她坐在河岸上，百无聊赖。突然，一只粉红眼睛的白兔从她身边跑过。' },
      { id: 3, image: 'https://image.pollinations.ai/prompt/Alice%20in%20Wonderland%20page%202%20illustration', text: '爱丽丝好奇地跟着兔子跳进了兔子洞，发现自己在一个奇怪的房间里，周围有许多不同大小的门。' },
      { id: 4, type: 'hard', image: 'https://image.pollinations.ai/prompt/Alice%20in%20Wonderland%20back%20cover%20illustration', text: '爱丽丝最终醒来，发现这一切都是一场梦。' }
    ]
  },
  {
    id: 3,
    title: '野兽国',
    author: '莫里斯·桑达克',
    coverImg: 'https://image.pollinations.ai/prompt/Where%20the%20Wild%20Things%20Are%20storybook%20cover%20illustration%20for%20children',
    description: '一个淘气的小男孩被送到床上不吃晚饭，然后想象自己航行到野兽居住的岛屿。',
    category: 'adventure',
    pages: [
      { id: 1, type: 'hard', image: 'https://image.pollinations.ai/prompt/Where%20the%20Wild%20Things%20Are%20cover%20illustration', text: '野兽国' },
      { id: 2, image: 'https://image.pollinations.ai/prompt/Where%20the%20Wild%20Things%20Are%20page%201%20illustration', text: '那天晚上，麦克斯穿上他的狼装，在家里到处捣乱。' },
      { id: 3, image: 'https://image.pollinations.ai/prompt/Where%20the%20Wild%20Things%20Are%20page%202%20illustration', text: '他的房间开始变化成一片森林，一片海洋出现了，麦克斯乘船来到了野兽居住的地方。' },
      { id: 4, type: 'hard', image: 'https://image.pollinations.ai/prompt/Where%20the%20Wild%20Things%20Are%20back%20cover%20illustration', text: '麦克斯回到了家，发现晚餐还热着，等着他。' }
    ]
  },
  {
    id: 4,
    title: '饥饿的毛毛虫',
    author: '艾瑞克·卡尔',
    coverImg: 'https://image.pollinations.ai/prompt/The%20Very%20Hungry%20Caterpillar%20storybook%20cover%20illustration%20for%20children',
    description: '一只非常饥饿的毛毛虫吃了很多食物，最后变成了美丽的蝴蝶。',
    category: 'educational',
    pages: [
      { id: 1, type: 'hard', image: 'https://image.pollinations.ai/prompt/The%20Very%20Hungry%20Caterpillar%20cover%20illustration', text: '饥饿的毛毛虫' },
      { id: 2, image: 'https://image.pollinations.ai/prompt/The%20Very%20Hungry%20Caterpillar%20page%201%20illustration', text: '在一个阳光明媚的早晨，一只小小的毛毛虫从蛋里孵化出来。' },
      { id: 3, image: 'https://image.pollinations.ai/prompt/The%20Very%20Hungry%20Caterpillar%20page%202%20illustration', text: '它开始寻找食物，因为它非常饿。它吃了很多很多的食物，但它还是很饿。' },
      { id: 4, type: 'hard', image: 'https://image.pollinations.ai/prompt/The%20Very%20Hungry%20Caterpillar%20butterfly%20illustration', text: '最后，它不再是一只小毛毛虫了，它变成了一只美丽的蝴蝶！' }
    ]
  },
  {
    id: 5,
    title: '月亮森林的秘密',
    author: '林小月',
    coverImg: 'https://image.pollinations.ai/prompt/Magical%20forest%20with%20glowing%20moon%20and%20animals%20children%20book%20cover%20illustration',
    description: '一个关于友谊、勇气和保护自然的温馨故事，讲述了小女孩琳琳和会说话的动物们一起保护月亮森林的冒险。',
    category: 'fantasy',
    pages: [
      {
        id: 1,
        type: 'hard',
        image: 'https://image.pollinations.ai/prompt/Magical%20forest%20with%20glowing%20moon%20and%20animals%20children%20book%20cover%20illustration',
        text: '月亮森林的秘密'
      },
      {
        id: 2,
        image: 'https://image.pollinations.ai/prompt/Little%20girl%20with%20lantern%20entering%20magical%20forest%20at%20night%20children%20book%20illustration',
        text: '在一个宁静的小村庄边缘，有一片神秘的森林。村里的人们都叫它"月亮森林"，因为每当满月之夜，森林里会闪烁着奇异的蓝光。小女孩琳琳总是好奇那片森林里藏着什么秘密。'
      },
      {
        id: 3,
        image: 'https://image.pollinations.ai/prompt/Little%20girl%20meeting%20a%20talking%20fox%20in%20magical%20forest%20children%20book%20illustration',
        text: '一天晚上，琳琳看见一道蓝光从森林中射出。她鼓起勇气，拿着小灯笼走进了月亮森林。"你好？有人吗？"琳琳小声呼唤着。突然，一只红狐狸从灌木丛中跳了出来。"嘘，小声点，"狐狸说道，"你会吵醒其他动物的。"'
      },
      {
        id: 4,
        image: 'https://image.pollinations.ai/prompt/Magical%20forest%20with%20talking%20animals%20gathering%20around%20a%20glowing%20tree%20children%20book%20illustration',
        text: '"你会说话！"琳琳惊讶地说。"在月亮森林里，所有动物都会说话，"狐狸解释道，"我叫火尾，我是森林的守护者之一。"火尾带着琳琳来到一棵巨大的橡树前，那里聚集着许多动物：机智的猫头鹰智慧、温柔的鹿斑点、活泼的兔子跳跳。'
      },
      {
        id: 5,
        image: 'https://image.pollinations.ai/prompt/Magical%20glowing%20tree%20with%20blue%20light%20in%20forest%20children%20book%20illustration',
        text: '"这是月亮树，"火尾指着那棵发出蓝光的巨树说，"它是森林的心脏，保护着我们所有人。月亮树的光芒让我们拥有了说话的能力，也让森林充满了魔法。"琳琳惊叹地看着月亮树，它的叶子像是由星光编织而成。'
      },
      {
        id: 6,
        image: 'https://image.pollinations.ai/prompt/Lumberjacks%20with%20machines%20at%20forest%20edge%20children%20book%20illustration',
        text: '"但是现在月亮树有危险了，"猫头鹰智慧忧心忡忡地说，"人类要砍伐森林，建造一座工厂。如果月亮树被砍倒，整个森林的魔法都会消失，我们也将失去说话的能力。"琳琳看到远处森林边缘已经有了伐木机器。'
      },
      {
        id: 7,
        image: 'https://image.pollinations.ai/prompt/Little%20girl%20and%20animals%20planning%20together%20in%20forest%20children%20book%20illustration',
        text: '"我们必须阻止他们！"琳琳坚定地说。动物们围在一起，开始制定计划。"我们需要让人类了解月亮森林的重要性，"琳琳说，"但是怎么做呢？"大家七嘴八舌地出主意，最后决定在满月之夜展示森林的魔法给村民们看。'
      },
      {
        id: 8,
        image: 'https://image.pollinations.ai/prompt/Animals%20preparing%20forest%20decorations%20with%20flowers%20and%20lights%20children%20book%20illustration',
        text: '接下来的几天，琳琳和动物们忙着准备。兔子跳跳收集了会发光的蘑菇，鹿斑点用角上挂满了小铃铛，猫头鹰智慧教会了森林里的鸟儿唱一首美妙的歌。火尾则负责在村庄边缘留下神秘的蓝色脚印，引起村民的好奇心。'
      },
      {
        id: 9,
        image: 'https://image.pollinations.ai/prompt/Little%20girl%20inviting%20villagers%20to%20follow%20blue%20footprints%20children%20book%20illustration',
        text: '满月之夜终于到来了。琳琳回到村子里，神秘地告诉大家："今晚，跟随蓝色的脚印，你们会看到一生中最神奇的景象。"村民们半信半疑，但好奇心驱使他们跟着琳琳来到了森林边缘。'
      },
      {
        id: 10,
        image: 'https://image.pollinations.ai/prompt/Magical%20forest%20show%20with%20animals%20performing%20and%20glowing%20tree%20for%20villagers%20children%20book%20illustration',
        text: '当他们踏入森林的那一刻，一场奇妙的表演开始了。鸟儿们唱起了动听的歌谣，萤火虫在空中画出美丽的图案，动物们站成一排，向人类鞠躬致意。最令人惊讶的是，月亮树散发出的蓝光比以往任何时候都要明亮，照亮了整个森林。'
      },
      {
        id: 11,
        image: 'https://image.pollinations.ai/prompt/Talking%20fox%20addressing%20surprised%20villagers%20in%20magical%20forest%20children%20book%20illustration',
        text: '然后，火尾走上前来，对着惊讶的村民们说话了："我们是月亮森林的居民，这里是我们的家园。月亮树给予我们生命和魔法，也保护着你们的村庄。如果森林被砍伐，不仅我们会失去家园，你们也将失去清澈的水源和新鲜的空气。"'
      },
      {
        id: 12,
        image: 'https://image.pollinations.ai/prompt/Village%20mayor%20shaking%20hands%20with%20fox%20while%20villagers%20and%20animals%20celebrate%20children%20book%20illustration',
        text: '村长走上前来，震惊地看着会说话的动物们。经过长时间的思考，他终于明白了森林的重要性。"我们不会允许任何人砍伐月亮森林，"村长庄严地宣布，"从今天起，这片森林将受到我们村庄的保护。"村民们热烈鼓掌，动物们欢呼雀跃。'
      },
      {
        id: 12,
        image: 'https://image.pollinations.ai/prompt/Little%20girl%20and%20fox%20sitting%20under%20glowing%20tree%20at%20night%20with%20forest%20animals%20around%20children%20book%20illustration',
        text: '从那以后，琳琳经常来月亮森林拜访她的动物朋友们。村民们也学会了尊重自然，保护环境。月亮树的光芒越来越明亮，照亮了森林，也照亮了人类与自然和谐相处的未来。\n\n每当满月之夜，如果你仔细聆听，或许能听到月亮森林里动物们欢快的笑声和琳琳讲述的冒险故事。'
      },
      {
        id: 13,
        type: 'hard',
        image: 'https://image.pollinations.ai/prompt/Magical%20forest%20with%20full%20moon%20and%20animals%20silhouettes%20children%20book%20back%20cover%20illustration',
        text: '故事的结束，也是新的开始。'
      }
    ]
  },
  {
    id: 6,
    title: '海底城堡历险记',
    author: '王蓝波',
    coverImg: 'https://image.pollinations.ai/prompt/Underwater%20castle%20with%20mermaids%20and%20sea%20creatures%20children%20book%20cover%20illustration',
    description: '小男孩小波意外获得了与海洋生物交流的能力，并帮助美人鱼公主找回失落的魔法珍珠，拯救海底王国的奇幻冒险故事。',
    category: 'adventure',
    pages: [
      {
        id: 1,
        type: 'hard',
        image: 'https://image.pollinations.ai/prompt/Underwater%20castle%20with%20mermaids%20and%20sea%20creatures%20children%20book%20cover%20illustration',
        text: '海底城堡历险记'
      },
      {
        id: 2,
        image: 'https://image.pollinations.ai/prompt/Little%20boy%20finding%20a%20glowing%20shell%20on%20beach%20children%20book%20illustration',
        text: '小波和爸爸妈妈来到海边度假。一天清晨，当所有人还在睡觉时，小波独自在沙滩上散步。突然，他发现了一个闪闪发光的贝壳，蓝色的光芒非常奇特。小波好奇地捡起了贝壳。'
      },
      {
        id: 3,
        image: 'https://image.pollinations.ai/prompt/Little%20boy%20hearing%20voices%20from%20ocean%20at%20sunset%20children%20book%20illustration',
        text: '当晚，小波把贝壳放在床头。半夜，他被一阵奇怪的声音吵醒。"救救我们...救救海底城堡..."小波惊讶地发现，声音是从贝壳里传出来的！他把贝壳贴在耳边，听到了更多求救声。'
      },
      {
        id: 4,
        image: 'https://image.pollinations.ai/prompt/Little%20boy%20diving%20into%20ocean%20following%20glowing%20shell%20children%20book%20illustration',
        text: '第二天，小波再次来到海边。他把贝壳放入水中，贝壳立刻发出强烈的蓝光。小波感到一股神秘的力量拉着他进入水中。奇怪的是，他发现自己可以在水下呼吸！贝壳的魔力保护着他。'
      },
      {
        id: 5,
        image: 'https://image.pollinations.ai/prompt/Underwater%20scene%20with%20boy%20meeting%20mermaid%20princess%20children%20book%20illustration',
        text: '小波跟随贝壳的指引，游到了深海。突然，一位美丽的美人鱼出现在他面前。"你好，人类小朋友，我是珊瑚公主。感谢你回应我们的求救。"小波惊讶地发现自己能听懂美人鱼的语言，这一定是贝壳的魔力。'
      },
      {
        id: 6,
        image: 'https://image.pollinations.ai/prompt/Magnificent%20underwater%20castle%20with%20merfolk%20and%20sea%20creatures%20children%20book%20illustration',
        text: '珊瑚公主带着小波来到了海底城堡。这是一座由珊瑚、贝壳和发光海藻建成的宏伟宫殿。各种各样的鱼儿和海洋生物在城堡周围游动。但小波注意到，城堡的光芒正在逐渐减弱。'
      },
      {
        id: 7,
        image: 'https://image.pollinations.ai/prompt/Mermaid%20king%20on%20throne%20explaining%20to%20boy%20about%20missing%20pearl%20children%20book%20illustration',
        text: '"我们的城堡由魔法珍珠的力量维持，"海王解释道，"但珍珠被邪恶的深海巨章鱼墨黑偷走了。如果找不回珍珠，城堡将会崩塌，我们所有人都将无家可归。"小波决定帮助美人鱼们找回魔法珍珠。'
      },
      {
        id: 8,
        image: 'https://image.pollinations.ai/prompt/Boy%20and%20mermaid%20princess%20with%20dolphin%20and%20turtle%20friends%20underwater%20journey%20children%20book%20illustration',
        text: '珊瑚公主、海豚波波和乌龟老伯陪伴小波踏上了寻找珍珠的旅程。他们必须穿过危险的海沟、神秘的海藻森林和闪光的珊瑚礁。一路上，小波用他的智慧和勇气帮助伙伴们克服了许多困难。'
      },
      {
        id: 9,
        image: 'https://image.pollinations.ai/prompt/Dark%20underwater%20cave%20with%20giant%20octopus%20guarding%20glowing%20pearl%20children%20book%20illustration',
        text: '最终，他们来到了深海洞穴，巨章鱼墨黑就住在这里。洞穴入口处，墨黑的触手像哨兵一样守卫着。"珍珠就在洞穴最深处，"珊瑚公主小声说，"但墨黑太强大了，我们无法正面对抗他。"'
      },
      {
        id: 10,
        image: 'https://image.pollinations.ai/prompt/Boy%20playing%20shell%20music%20to%20sleeping%20octopus%20underwater%20children%20book%20illustration',
        text: '小波想到了一个主意。他用贝壳发出美妙的音乐，这音乐有着神奇的魔力，能让听者平静下来。墨黑被音乐吸引，渐渐放松了警惕，最后竟然睡着了。小波和伙伴们悄悄潜入洞穴深处。'
      },
      {
        id: 11,
        image: 'https://image.pollinations.ai/prompt/Boy%20and%20mermaid%20finding%20glowing%20pearl%20in%20underwater%20cave%20children%20book%20illustration',
        text: '在洞穴最深处，他们找到了魔法珍珠！它散发着耀眼的蓝光，美丽非凡。就在他们准备离开时，墨黑醒了过来，触手挡住了出口。"等等！"墨黑喊道，"我拿走珍珠是因为我很孤独，想要吸引别人来陪伴我..."'
      },
      {
        id: 12,
        image: 'https://image.pollinations.ai/prompt/Octopus%20joining%20underwater%20celebration%20with%20merfolk%20and%20sea%20creatures%20children%20book%20illustration',
        text: '小波和珊瑚公主理解了墨黑的感受。"你不需要偷东西来获得朋友，"小波温柔地说，"如果你友善待人，自然会有朋友。你愿意和我们一起回海底城堡吗？"墨黑惊讶地点点头，决定归还珍珠并与大家和平相处。'
      },
      {
        id: 12,
        image: 'https://image.pollinations.ai/prompt/Underwater%20castle%20glowing%20brightly%20with%20boy%20saying%20goodbye%20to%20mermaid%20friends%20children%20book%20illustration',
        text: '他们回到海底城堡，将魔法珍珠放回王座上。城堡立刻恢复了光彩，比以前更加璀璨。海王感谢小波的帮助，赐予他随时来访海底王国的能力。\n\n小波依依不舍地告别了新朋友们，游回了海面。从此以后，每当他来到海边，戴上那个特殊的贝壳，就能再次拜访海底城堡，与珊瑚公主和所有海洋朋友们一起探险。'
      },
      {
        id: 13,
        type: 'hard',
        image: 'https://image.pollinations.ai/prompt/Underwater%20castle%20with%20glowing%20pearl%20and%20sea%20creatures%20children%20book%20back%20cover%20illustration',
        text: '海底的冒险永远不会结束。'
      }
    ]
  }
]);

// 分类选项
const categories = [
  { label: '全部绘本', value: 'all' },
  { label: '经典童话', value: 'classic' },
  { label: '奇幻冒险', value: 'fantasy' },
  { label: '探险故事', value: 'adventure' },
  { label: '教育启蒙', value: 'educational' }
];

// 过滤后的参考绘本
const filteredBooks = computed(() => {
  let result = referenceBooks;

  // 按分类筛选
  if (activeCategory.value !== 'all') {
    result = result.filter(book => book.category === activeCategory.value);
  }

  // 按搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(book =>
      book.title.toLowerCase().includes(query) ||
      book.author.toLowerCase().includes(query) ||
      book.description.toLowerCase().includes(query)
    );
  }

  return result;
});

// 搜索参考绘本
const searchReferences = () => {
  isLoading.value = true;
  // 模拟搜索延迟
  setTimeout(() => {
    isLoading.value = false;
  }, 500);
};

// 查看参考绘本详情
const viewReferenceDetail = (book) => {
  selectedReference.value = book;
  showDetailModal.value = true;
};

// 添加到我的参考资料
const addToMyReferences = (book) => {
  // 检查是否已经添加
  const exists = props.projectData.references.some(ref => ref.id === book.id);
  if (!exists) {
    props.projectData.references.push(book);
    window.$message?.success('已添加到参考资料');
  } else {
    window.$message?.info('该绘本已在参考资料中');
  }
};

// 从我的参考资料中移除
const removeFromReferences = (bookId) => {
  const index = props.projectData.references.findIndex(ref => ref.id === bookId);
  if (index !== -1) {
    props.projectData.references.splice(index, 1);
    window.$message?.success('已从参考资料中移除');
  }
};

// 绘本阅读器状态
const showReader = ref(false);
const selectedBook = ref(null);
const currentPage = ref(0); // 当前页码

// 打开绘本阅读器
const openBookReader = (book) => {
  // 如果是从详情弹窗打开的，先关闭详情弹窗
  if (showDetailModal.value) {
    showDetailModal.value = false;
  }

  // 将书籍数据存储在 localStorage 中
  localStorage.setItem('current-book-data', JSON.stringify(book));

  // 跳转到独立的绘本阅读器页面
  router.push({
    path: `/storybook/reader/${book.id}`,
    query: { source: 'reference' }
  });
};

// 关闭绘本阅读器
const closeBookReader = () => {
  showReader.value = false;
};

// 页面翻转事件处理
const handlePageFlip = (pageNumber) => {
  currentPage.value = pageNumber;
  console.log('当前页面:', pageNumber + 1);
};

// 使用横版模式

// 收集灵感
const collectInspiration = (text) => {
  if (!props.projectData.inspirations) {
    props.projectData.inspirations = [];
  }

  props.projectData.inspirations.push({
    id: Date.now(),
    content: text,
    timestamp: new Date().toISOString()
  });
  window.$message?.success('灵感已收集');
};

// 获取分类标签
const getCategoryLabel = (categoryValue) => {
  const category = categories.find(cat => cat.value === categoryValue);
  return category ? category.label : '未分类';
};

onMounted(() => {
  // 初始化逻辑
  // 确保projectData中有references数组
  if (!props.projectData.references) {
    props.projectData.references = [];
  }
});
</script>

<template>
  <div class="reference-library">
    <!-- 标题和搜索区域 -->
    <div class="library-header">
      <div class="header-content">
        <div class="header-title-section">
          <div class="header-icon">📚</div>
          <div class="header-titles">
            <h1 class="header-title">绘本图书馆</h1>
            <p class="header-subtitle">探索精彩绘本，获取创作灵感</p>
          </div>
        </div>
        <div class="search-container">
          <NInput
            v-model:value="searchQuery"
            placeholder="搜索绘本标题、作者..."
            clearable
            @keyup.enter="searchReferences"
            class="search-input"
          >
            <template #prefix>
              <SvgIcon name="ri:search-line" size="18" />
            </template>
          </NInput>
        </div>
      </div>

      <div class="category-tabs">
        <NTabs v-model:value="activeCategory" type="line" size="medium">
          <NTabPane
            v-for="category in categories"
            :key="category.value"
            :name="category.value"
            :tab="category.label"
          />
        </NTabs>
      </div>
    </div>

    <div class="library-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <NSpin size="large" />
        <p class="loading-text">正在加载绘本...</p>
      </div>

      <!-- 空状态 -->
      <NEmpty
        v-else-if="filteredBooks.length === 0"
        description="没有找到匹配的绘本"
        class="empty-state"
      >
        <template #icon>
          <div class="empty-icon-container">
            <span class="empty-icon">🔍</span>
          </div>
        </template>
        <template #extra>
          <NButton @click="activeCategory = 'all'; searchQuery = ''" class="reset-button">
            <span class="button-icon">↻</span>
            重置筛选
          </NButton>
        </template>
      </NEmpty>

      <!-- 绘本列表 -->
      <div v-else class="books-grid">
        <NGrid :cols="isMobile ? 1 : 3" :x-gap="24" :y-gap="24">
          <NGridItem v-for="book in filteredBooks" :key="book.id">
            <div class="book-card" @click="viewReferenceDetail(book)">
              <div class="book-cover">
                <img :src="book.coverImg" :alt="book.title" />
                <div class="book-category">{{ getCategoryLabel(book.category) }}</div>
                <div class="book-overlay">
                  <div class="book-quick-actions">
                    <button class="quick-action-button view-button" @click.stop="viewReferenceDetail(book)" title="查看详情">
                      <span class="action-icon">👁️</span>
                    </button>
                    <button class="quick-action-button read-quick-button" @click.stop="openBookReader(book)" title="阅读绘本">
                      <span class="action-icon">📖</span>
                    </button>
                    <button class="quick-action-button add-quick-button" @click.stop="addToMyReferences(book)" title="添加到参考">
                      <span class="action-icon">📌</span>
                    </button>
                  </div>
                </div>
              </div>
              <div class="book-info">
                <div class="book-meta">
                  <div class="book-pages-count">{{ book.pages.length }}页</div>
                  <div class="book-category-label">{{ getCategoryLabel(book.category) }}</div>
                </div>
                <h3 class="book-title">{{ book.title }}</h3>
                <p class="book-author">
                  <span class="author-icon">✍️</span>
                  {{ book.author }}
                </p>
                <p class="book-description">{{ book.description }}</p>
              </div>
              <!-- 底部按钮已移除 -->
            </div>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 我的参考资料 -->
      <div v-if="projectData.references && projectData.references.length > 0" class="my-references">
        <div class="section-header">
          <div class="section-title-container">
            <span class="section-icon">📑</span>
            <h3 class="section-title">我的参考资料</h3>
          </div>
          <div class="section-count">{{ projectData.references.length }}本绘本</div>
        </div>

        <div class="references-list">
          <NGrid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16">
            <NGridItem v-for="ref in projectData.references" :key="ref.id">
              <div class="reference-item">
                <div class="reference-cover" @click="viewReferenceDetail(ref)">
                  <img :src="ref.coverImg" :alt="ref.title" />
                  <div class="reference-overlay">
                    <div class="reference-actions">
                      <NButton
                        size="small"
                        class="ref-action-button read-button"
                        @click.stop="openBookReader(ref)"
                      >
                        <span class="emoji-icon">📖</span>
                        阅读
                      </NButton>
                    </div>
                  </div>
                </div>
                <div class="reference-info">
                  <div class="reference-title" @click="viewReferenceDetail(ref)">{{ ref.title }}</div>
                  <div class="reference-meta">{{ getCategoryLabel(ref.category) }}</div>
                </div>
                <NButton
                  size="small"
                  class="remove-button"
                  @click="removeFromReferences(ref.id)"
                >
                  <span class="emoji-icon small-emoji">🗑️</span>
                  移除
                </NButton>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>
    </div>

    <!-- 绘本详情弹窗 -->
    <NModal
      v-model:show="showDetailModal"
      preset="card"
      style="width: 90%; max-width: 900px;"
      :title="null"
      :bordered="false"
      size="huge"
      class="book-detail-modal"
    >
      <template v-if="selectedReference">
        <div class="book-detail">

          <div class="book-detail-header">
            <div class="detail-cover">
              <img :src="selectedReference.coverImg" :alt="selectedReference.title" />
              <div class="detail-category-badge">
                {{ getCategoryLabel(selectedReference.category) }}
              </div>
            </div>
            <div class="detail-info">
              <div class="detail-meta">
                <span class="detail-category-tag">{{ getCategoryLabel(selectedReference.category) }}</span>
              </div>
              <h2 class="detail-title">{{ selectedReference.title }}</h2>
              <p class="detail-author">
                <span class="author-label">作者:</span>
                <span class="author-name">{{ selectedReference.author }}</span>
              </p>
              <div class="detail-divider"></div>
              <p class="detail-description">{{ selectedReference.description }}</p>
              <div class="detail-actions">
                <NButton
                  class="detail-action-button add-reference-button"
                  @click="addToMyReferences(selectedReference)"
                >
                  <span class="emoji-icon">📌</span>
                  添加到参考
                </NButton>
                <NButton
                  @click="openBookReader(selectedReference)"
                  class="detail-action-button read-book-button"
                >
                  <span class="emoji-icon">📖</span>
                  阅读绘本
                </NButton>
                <NButton
                  @click="collectInspiration(selectedReference.description)"
                  class="detail-action-button collect-button"
                >
                  <span class="emoji-icon">💡</span>
                  收集灵感
                </NButton>
              </div>
            </div>
          </div>

          <div class="book-pages">
            <div class="pages-header">
              <h3 class="pages-title">绘本页面预览</h3>
              <p class="pages-count">共 {{ selectedReference.pages.length }} 页</p>
            </div>
            <div class="pages-grid">
              <div
                v-for="page in selectedReference.pages"
                :key="page.id"
                class="page-item"
                :class="{ 'hard-page': page.type === 'hard' }"
              >
                <div class="page-number">{{ page.id }}</div>
                <div class="page-image">
                  <img :src="page.image" :alt="`Page ${page.id}`" />
                </div>
                <div class="page-text">
                  <p>{{ page.text }}</p>
                  <NButton
                    size="tiny"
                    class="collect-text-button"
                    @click="collectInspiration(page.text)"
                  >
                    <span class="small-emoji">💡</span>
                    收集文本
                  </NButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </NModal>

    <!-- 绘本阅读器已移至独立页面 -->


  </div>
</template>

<style scoped>
/* 基础布局 */
.reference-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
  background-color: #f8fafc;
}

.dark .reference-library {
  background-color: #0f172a;
}

.library-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 2rem;
}

/* 标题和搜索区域 */
.library-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .library-header {
  border-bottom-color: #334155;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  color: white;
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
}

.header-titles {
  display: flex;
  flex-direction: column;
}

.header-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #8b5cf6, #6366f1);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

.dark .header-subtitle {
  color: #94a3b8;
}

.search-container {
  width: 300px;
}

.search-input {
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-input:hover,
.search-input:focus {
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.category-tabs {
  margin-top: 1rem;
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  gap: 1rem;
}

.loading-text {
  color: #64748b;
  font-size: 1rem;
}

.dark .loading-text {
  color: #94a3b8;
}

.empty-state {
  padding: 3rem 0;
}

.empty-icon-container {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.dark .empty-icon-container {
  background: linear-gradient(135deg, #0c4a6e, #075985);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.empty-icon {
  font-size: 2.5rem;
}

.reset-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.button-icon {
  font-size: 1.1rem;
}

/* 绘本卡片样式 */
.books-grid {
  margin-bottom: 2.5rem;
}

.book-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  border: 1px solid #e2e8f0;
  position: relative;
}

.dark .book-card {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
}

.book-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 30px rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
}

.dark .book-card:hover {
  box-shadow: 0 20px 30px rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.6);
}

.book-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 1.25rem;
  z-index: 1;
}

.book-card:hover::before {
  opacity: 0.1;
}

.dark .book-card::before {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), transparent);
}

.dark .book-card:hover::before {
  opacity: 0.2;
}

.book-cover {
  height: 240px;
  position: relative;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.book-card:hover .book-cover img {
  transform: scale(1.1);
}

.book-category {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.dark .book-category {
  background-color: rgba(30, 41, 59, 0.9);
  color: #a5b4fc;
}

.book-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 1.5rem;
  z-index: 2;
}

.book-card:hover .book-overlay {
  opacity: 1;
}

.book-quick-actions {
  display: flex;
  gap: 1rem;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.4s ease 0.1s;
}

.book-card:hover .book-quick-actions {
  transform: translateY(0);
  opacity: 1;
}

.quick-action-button {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.quick-action-button:hover {
  transform: translateY(-5px);
}

.view-button {
  color: #6366f1;
}

.view-button:hover {
  background-color: #6366f1;
  color: white;
}

.read-quick-button {
  color: #0ea5e9;
}

.read-quick-button:hover {
  background-color: #0ea5e9;
  color: white;
}

.add-quick-button {
  color: #f59e0b;
}

.add-quick-button:hover {
  background-color: #f59e0b;
  color: white;
}

.action-icon {
  font-size: 1.5rem;
}

.book-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  border-top: 1px solid #f1f5f9;
  position: relative;
}

.dark .book-info {
  border-top-color: #334155;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.book-pages-count {
  font-size: 0.75rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
}

.dark .book-pages-count {
  color: #94a3b8;
  background-color: #334155;
}

.book-category-label {
  font-size: 0.75rem;
  color: #6366f1;
  font-weight: 600;
}

.dark .book-category-label {
  color: #a5b4fc;
}

.book-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .book-title {
  color: #e2e8f0;
}

.book-author {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.75rem;
}

.dark .book-author {
  color: #94a3b8;
}

.author-icon {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.book-description {
  font-size: 0.9rem;
  color: #475569;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
  margin-bottom: 0;
  padding-bottom: 1rem;
}

/* 底部按钮样式已移除 */
.emoji-icon {
  font-size: 1.1rem;
  line-height: 1;
  margin-right: 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.small-emoji {
  font-size: 0.9rem;
  margin-right: 0.25rem;
}

/* 我的参考资料部分 */
.my-references {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.dark .my-references {
  border-top-color: #334155;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-icon {
  font-size: 1.5rem;
  background-color: #f0f9ff;
  color: #0ea5e9;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
}

.dark .section-icon {
  background-color: #0c4a6e;
  color: #38bdf8;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.dark .section-title {
  color: #e2e8f0;
}

.section-count {
  font-size: 0.875rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
}

.dark .section-count {
  color: #94a3b8;
  background-color: #334155;
}

.references-list {
  margin-top: 1.5rem;
}

.reference-item {
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: white;
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dark .reference-item {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.reference-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(14, 165, 233, 0.15);
  border-color: rgba(14, 165, 233, 0.3);
}

.dark .reference-item:hover {
  box-shadow: 0 8px 16px rgba(14, 165, 233, 0.25);
  border-color: rgba(14, 165, 233, 0.5);
}

.reference-cover {
  height: 150px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
}

.reference-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.reference-item:hover .reference-cover img {
  transform: scale(1.08);
}

.reference-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  padding: 1rem;
}

.reference-item:hover .reference-overlay {
  opacity: 1;
}

.reference-actions {
  width: 100%;
}

.ref-action-button {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  color: #0ea5e9;
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.ref-action-button:hover {
  background-color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.reference-info {
  padding: 0.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.reference-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  cursor: pointer;
  line-height: 1.4;
}

.dark .reference-title {
  color: #e2e8f0;
}

.reference-meta {
  font-size: 0.75rem;
  color: #64748b;
}

.dark .reference-meta {
  color: #94a3b8;
}

.remove-button {
  margin: 0 0.75rem 0.75rem;
  background-color: #fee2e2;
  color: #ef4444;
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.dark .remove-button {
  background-color: rgba(239, 68, 68, 0.2);
}

.remove-button:hover {
  background-color: #fecaca;
}

.dark .remove-button:hover {
  background-color: rgba(239, 68, 68, 0.3);
}

/* 绘本详情弹窗样式 */
.book-detail-modal :deep(.n-card) {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  background-color: #f8fafc;
}

.dark .book-detail-modal :deep(.n-card) {
  background-color: #0f172a;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 移除了关闭按钮样式 */

.book-detail {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  padding: 0.5rem;
}

.book-detail-header {
  display: flex;
  gap: 2rem;
}

.detail-cover {
  width: 240px;
  height: 320px;
  flex-shrink: 0;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.detail-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-category-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .detail-category-badge {
  background-color: rgba(30, 41, 59, 0.9);
  color: #a5b4fc;
}

.detail-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-meta {
  margin-bottom: 0.75rem;
}

.detail-category-tag {
  display: inline-block;
  background-color: #f1f5f9;
  color: #6366f1;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
}

.dark .detail-category-tag {
  background-color: #334155;
  color: #a5b4fc;
}

.detail-title {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  color: #1e293b;
  line-height: 1.2;
}

.dark .detail-title {
  color: #e2e8f0;
}

.detail-author {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark .detail-author {
  color: #94a3b8;
}

.author-label {
  font-weight: 600;
}

.author-name {
  color: #334155;
}

.dark .author-name {
  color: #cbd5e1;
}

.detail-divider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 0 0 1.5rem;
}

.dark .detail-divider {
  background-color: #334155;
}

.detail-description {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #475569;
  margin-bottom: 2rem;
  flex: 1;
}

.dark .detail-description {
  color: #cbd5e1;
}

.detail-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.detail-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-reference-button {
  background-color: #6366f1;
  color: white;
  border: none;
}

.add-reference-button:hover {
  background-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

.read-book-button {
  background-color: #f8fafc;
  color: #0ea5e9;
  border: 1px solid #e2e8f0;
}

.dark .read-book-button {
  background-color: #1e293b;
  color: #38bdf8;
  border-color: #334155;
}

.read-book-button:hover {
  background-color: #f1f5f9;
  border-color: #0ea5e9;
  transform: translateY(-2px);
}

.dark .read-book-button:hover {
  background-color: #334155;
  border-color: #38bdf8;
}

.collect-button {
  background-color: #f8fafc;
  color: #f59e0b;
  border: 1px solid #e2e8f0;
}

.dark .collect-button {
  background-color: #1e293b;
  color: #fbbf24;
  border-color: #334155;
}

.collect-button:hover {
  background-color: #f1f5f9;
  border-color: #f59e0b;
  transform: translateY(-2px);
}

.dark .collect-button:hover {
  background-color: #334155;
  border-color: #fbbf24;
}

.book-pages {
  margin-top: 1rem;
}

.pages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.pages-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.dark .pages-title {
  color: #e2e8f0;
}

.pages-count {
  font-size: 0.875rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
}

.dark .pages-count {
  color: #94a3b8;
  background-color: #334155;
}

.pages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.page-item {
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  background-color: white;
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
  position: relative;
}

.dark .page-item {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.page-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.hard-page {
  border-color: #cbd5e1;
  background-color: #f8fafc;
}

.dark .hard-page {
  border-color: #475569;
  background-color: #0f172a;
}

.page-number {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 600;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .page-number {
  background-color: rgba(30, 41, 59, 0.9);
  color: #94a3b8;
}

.page-image {
  height: 200px;
  overflow: hidden;
}

.page-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.page-item:hover .page-image img {
  transform: scale(1.05);
}

.page-text {
  padding: 1rem;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #334155;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.dark .page-text {
  color: #e2e8f0;
}

.collect-text-button {
  align-self: flex-end;
  background-color: #fef3c7;
  color: #d97706;
  border: none;
  transition: all 0.3s ease;
}

.dark .collect-text-button {
  background-color: rgba(217, 119, 6, 0.2);
}

.collect-text-button:hover {
  background-color: #fde68a;
  transform: translateY(-2px);
}

.dark .collect-text-button:hover {
  background-color: rgba(217, 119, 6, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .search-container {
    width: 100%;
  }

  .book-detail-header {
    flex-direction: column;
  }

  .detail-cover {
    width: 100%;
    height: 250px;
    margin-bottom: 1.5rem;
  }

  .pages-grid {
    grid-template-columns: 1fr;
  }
}

/* 阅读容器样式 */
.reading-container {
  position: relative;
}
</style>
