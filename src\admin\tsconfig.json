{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "baseUrl": "./", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["src/*"], "#/*": ["src/types/*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-pages/client", "vite-plugin-vue-meta-layouts/client", "element-plus/global"], "allowImportingTsExtensions": true, "allowJs": false, "strict": true, "noEmit": true, "sourceMap": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "references": [{"path": "./tsconfig.node.json"}], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}