<template>
  <div class="step-page-container">
    <!-- 页面标题和描述 -->
    <div class="step-header">
      <h2 class="step-title">创造角色</h2>
      <p class="step-description">为你的故事创建生动有趣的角色</p>
      <div class="auto-save-hint">
        <span class="hint-icon">💾</span>
        <span class="hint-text">角色信息会自动保存</span>
      </div>
    </div>

    <div class="character-builder-container">
      <CharacterBuilder
        :character="getActiveCharacter()"
        :characters="charactersList"
        :activeCharacterIndex="activeCharacterIndex"
        @update:character="updateCharacter"
        @add-character="addNewCharacter"
        @select-character="selectCharacter"
        @delete-character="deleteCharacter"
      />
    </div>

    <!-- 删除确认弹窗 -->
    <NModal
      v-model:show="showDeleteModal"
      preset="dialog"
      title="确认删除"
      positive-text="删除"
      negative-text="取消"
      @positive-click="confirmDeleteCharacter"
    >
      <div class="delete-modal-content">
        <div class="delete-warning-icon">
          <span class="warning-icon">⚠️</span>
        </div>
        <p class="delete-warning-text">确定要删除角色"{{ characterToDeleteName }}"吗？此操作不可恢复。</p>
      </div>
    </NModal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { NModal } from 'naive-ui';
import CharacterBuilder from './CharacterBuilder.vue';
import { saveStorybookProject } from '@/utils/storybookStorage';

const props = defineProps({
  projectData: Object
});

const activeCharacterIndex = ref(0);
const charactersList = ref([]); // 存储所有角色的数组

// 确保角色数据结构存在
onMounted(() => {
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }

  // 初始化角色数组
  if (!props.projectData.outline.characters) {
    props.projectData.outline.characters = [];
  } else if (!Array.isArray(props.projectData.outline.characters)) {
    // 如果characters不是数组，尝试将其转换为数组
    try {
      // 如果是对象，尝试提取值
      if (typeof props.projectData.outline.characters === 'object') {
        const charactersArray = Object.values(props.projectData.outline.characters);
        if (charactersArray.length > 0) {
          props.projectData.outline.characters = charactersArray;
          console.log('成功将角色对象转换为数组:', charactersArray);
        } else {
          props.projectData.outline.characters = [];
          console.log('角色对象为空，初始化为空数组');
        }
      } else {
        props.projectData.outline.characters = [];
        console.log('角色数据不是对象也不是数组，初始化为空数组');
      }
    } catch (error) {
      console.error('转换角色数据时出错:', error);
      props.projectData.outline.characters = [];
    }
  }

  // 确保每个角色的图像字段都正确设置
  props.projectData.outline.characters = props.projectData.outline.characters.map(character => {
    // 创建一个深拷贝，避免引用问题
    const characterCopy = JSON.parse(JSON.stringify(character));

    // 确保所有必要的图像字段都正确设置
    // 首先确定最终要使用的图像URL
    const imageUrl = characterCopy.imageUrl || characterCopy.image || characterCopy._image || '';

    // 然后统一设置所有图像相关字段
    if (imageUrl) {
      characterCopy.imageUrl = imageUrl;
      characterCopy._image = imageUrl;
      characterCopy.image = imageUrl; // 兼容旧版本
    }

    // 确保其他必要字段存在
    characterCopy.personalityTraits = characterCopy.personalityTraits || [];
    characterCopy.appearanceTraits = characterCopy.appearanceTraits || [];
    characterCopy.tags = characterCopy.tags || [];
    characterCopy.createdAt = characterCopy.createdAt || new Date().toISOString();
    characterCopy.updatedAt = characterCopy.updatedAt || new Date().toISOString();

    return characterCopy;
  });

  // 将角色数据加载到本地列表
  charactersList.value = props.projectData.outline.characters;
  console.log('[AI绘本创作-角色创建] 加载角色数据:', charactersList.value);

  // 保存标准化后的数据
  saveStorybookProject(props.projectData);
  console.log('[AI绘本创作-角色创建] 已保存标准化后的角色数据');

  // 如果没有角色，创建一个默认角色
  if (charactersList.value.length === 0) {
    addNewCharacter();
  }
});



// 获取当前选中的角色
const getActiveCharacter = () => {
  if (charactersList.value.length === 0) {
    return null;
  }
  return charactersList.value[activeCharacterIndex.value];
};

// 更新角色信息
const updateCharacter = (updatedCharacter) => {
  if (charactersList.value.length === 0) {
    return;
  }

  // 创建一个深拷贝，避免引用问题
  const characterCopy = JSON.parse(JSON.stringify(updatedCharacter));

  // 确保所有必要的图像字段都正确设置
  // 首先确定最终要使用的图像URL
  const imageUrl = characterCopy.imageUrl || characterCopy.image || characterCopy._image || '';

  // 然后统一设置所有图像相关字段
  if (imageUrl) {
    characterCopy.imageUrl = imageUrl;
    characterCopy._image = imageUrl;
    characterCopy.image = imageUrl; // 兼容旧版本
  }

  // 更新角色列表中的角色
  charactersList.value[activeCharacterIndex.value] = characterCopy;

  // 更新项目数据
  props.projectData.outline.characters = charactersList.value;

  // 自动保存到localStorage
  saveStorybookProject(props.projectData);
  console.log('[AI绘本创作-角色创建] 角色信息已更新并保存:', characterCopy.name);
  console.log('[AI绘本创作-角色创建] 更新后的角色数据:', JSON.stringify(characterCopy));
};

// 添加新角色
const addNewCharacter = () => {
  const newCharacter = {
    id: Date.now() + Math.floor(Math.random() * 1000), // 生成唯一ID
    name: '',                 // 角色名称
    characterType: '',        // 角色类型

    // 性格特点
    personalityTraits: [],    // 性格特点数组

    // 外表特点
    appearance: '',           // 外表描述
    appearanceTraits: [],     // 外表特点数组

    // 视觉元素 - 确保所有图像字段都存在
    imageUrl: '',             // 新版字段
    _image: '',               // 内部字段
    image: '',                // 兼容旧版本

    // 其他属性
    tags: [],                 // 标签
    isFavorite: 0,            // 是否收藏
    isTemplate: 0,            // 是否模板

    // 时间戳
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // 添加到角色列表
  charactersList.value.push(newCharacter);

  // 更新项目数据
  props.projectData.outline.characters = charactersList.value;

  // 自动保存到localStorage
  saveStorybookProject(props.projectData);
  console.log('[AI绘本创作-角色创建] 已添加新角色并保存');

  // 切换到新角色
  activeCharacterIndex.value = charactersList.value.length - 1;

  window.$message?.success('已添加新角色');
};

// 选择角色
const selectCharacter = (index) => {
  if (index >= 0 && index < charactersList.value.length) {
    activeCharacterIndex.value = index;
  }
};

// 删除角色相关状态
const showDeleteModal = ref(false);
const characterToDeleteIndex = ref(-1);
const characterToDeleteName = ref('');

// 显示删除确认弹窗
const showDeleteConfirmation = (index) => {
  if (charactersList.value.length <= 1) {
    window.$message?.warning('至少需要保留一个角色');
    return;
  }

  characterToDeleteIndex.value = index;
  characterToDeleteName.value = charactersList.value[index]?.name || '未命名角色';
  showDeleteModal.value = true;
};

// 执行删除角色操作
const confirmDeleteCharacter = () => {
  const index = characterToDeleteIndex.value;
  if (index < 0 || index >= charactersList.value.length) return;

  // 从列表中删除
  charactersList.value.splice(index, 1);

  // 更新项目数据
  props.projectData.outline.characters = charactersList.value;

  // 自动保存到localStorage
  saveStorybookProject(props.projectData);
  console.log('[AI绘本创作-角色创建] 角色已删除并保存');

  // 如果删除的是当前选中的角色，调整选中索引
  if (activeCharacterIndex.value >= charactersList.value.length) {
    activeCharacterIndex.value = charactersList.value.length - 1;
  }

  window.$message?.success('角色已删除');
  showDeleteModal.value = false;
};

// 删除角色（触发确认弹窗）
const deleteCharacter = (index) => {
  showDeleteConfirmation(index);
};

// 获取角色表情
const getCharacterEmoji = (character) => {
  if (!character) return '👤';

  // 根据角色类型返回不同的表情
  const typeEmojis = {
    boy: '👦',
    girl: '👧',
    animal: '🐶',
    magical: '🧚',
    robot: '🤖',
    alien: '👽',
    oldman: '👴',
    oldwoman: '👵',
    teen: '🧑',
    baby: '👶',
    superhero: '🦸',
    princess: '👸',
    prince: '🤴',
    elf: '🧝',
    wizard: '🧙'
  };

  return typeEmojis[character.characterType] || '👤';
};


</script>

<style scoped>
/* 使用全局统一样式，这里只添加特定于此组件的样式 */
.character-builder-container {
  margin-top: 1.5rem;
  flex: 1;
  min-height: 0;
}

/* 自动保存提示样式 */
.auto-save-hint {
  display: inline-flex;
  align-items: center;
  background-color: #f0f9ff;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  margin-left: 1rem;
  font-size: 0.8rem;
  color: #3b82f6;
  border: 1px solid #bfdbfe;
}

.dark .auto-save-hint {
  background-color: #1e3a8a;
  color: #93c5fd;
  border-color: #1e40af;
}

.hint-icon {
  margin-right: 0.25rem;
  font-size: 0.9rem;
}





/* 角色选择器样式 */
.character-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .character-selector {
  background-color: #1e293b;
  border-color: #334155;
}

.character-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: white;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.dark .character-tab {
  background-color: #334155;
  border-color: #475569;
}

.character-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .character-tab:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.character-tab.active {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border-color: #3b82f6;
}

.dark .character-tab.active {
  background: linear-gradient(135deg, #1e40af, #2563eb);
  border-color: #60a5fa;
}

.character-tab-icon {
  font-size: 1.25rem;
}

.character-tab-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .character-tab-name {
  color: #e2e8f0;
}

.delete-character-btn {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #fee2e2;
  color: #ef4444;
  border: none;
  font-size: 10px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  position: absolute;
  top: -5px;
  right: -5px;
}

.dark .delete-character-btn {
  background-color: #991b1b;
  color: #fca5a5;
}

.character-tab:hover .delete-character-btn {
  opacity: 1;
}

.character-builder-container {
  flex: 1;
  min-height: 0;
}

@media (max-width: 768px) {
  .character-selector {
    padding: 0.5rem;
    overflow-x: auto;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .character-tab {
    flex: 0 0 auto;
  }
}

/* 删除确认弹窗样式 */
.delete-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  text-align: center;
}

.delete-warning-icon {
  margin-bottom: 1rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fee2e2;
  border-radius: 50%;
}

.dark .delete-warning-icon {
  background-color: #7f1d1d;
}

.warning-icon {
  font-size: 1.5rem;
}

.delete-warning-text {
  font-size: 1rem;
  color: #1e293b;
  line-height: 1.5;
}

.dark .delete-warning-text {
  color: #e2e8f0;
}
</style>
