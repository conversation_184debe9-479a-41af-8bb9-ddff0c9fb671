import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class BatchImportAppsDto {
  @ApiProperty({ description: 'JSON数据内容', example: '[{"app":{...}, "forms":[{...}]}]' })
  @IsNotEmpty({ message: 'JSON数据不能为空' })
  @IsString({ message: 'JSON数据必须是字符串' })
  jsonContent: string;

  @ApiProperty({ description: '是否仅验证JSON数据，不执行导入', example: false })
  @IsBoolean({ message: '验证标志必须是布尔值' })
  validateOnly: boolean;
}
