import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber } from 'class-validator';

/**
 * 创建文件夹DTO
 */
export class CreateFolderDto {
  @ApiProperty({ description: '文件夹名称' })
  @IsNotEmpty({ message: '文件夹名称不能为空' })
  @IsString()
  name: string;

  @ApiProperty({ description: '文件夹描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '封面图片URL', required: false })
  @IsOptional()
  @IsString()
  coverImg?: string;

  @ApiProperty({ description: '文件夹颜色', required: false })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({ description: '文件夹图标', required: false })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({ description: '排序', required: false })
  @IsOptional()
  @IsNumber()
  order?: number;
}

/**
 * 更新文件夹DTO
 */
export class UpdateFolderDto {
  @ApiProperty({ description: '文件夹名称', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: '文件夹描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '封面图片URL', required: false })
  @IsOptional()
  @IsString()
  coverImg?: string;

  @ApiProperty({ description: '文件夹颜色', required: false })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({ description: '文件夹图标', required: false })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({ description: '排序', required: false })
  @IsOptional()
  @IsNumber()
  order?: number;
}

/**
 * 文件夹查询DTO
 */
export class QueryFolderDto {
  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({ description: '关键词搜索', required: false })
  @IsOptional()
  @IsString()
  keyword?: string;
}

/**
 * 移动作品到文件夹DTO
 */
export class MoveToFolderDto {
  @ApiProperty({ description: '绘本ID数组' })
  @IsNotEmpty({ message: '绘本ID数组不能为空' })
  storybookIds: number[];

  @ApiProperty({ description: '目标文件夹ID', required: false })
  @IsOptional()
  @IsNumber()
  folderId?: number;
}
