<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
  NCard, 
  NTabs, 
  NTabPane, 
  NList, 
  NListItem, 
  NButton, 
  NEmpty,
  NPagination,
  NSpace,
  NBadge,
  NPopconfirm,
  NTag,
  useMessage
} from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useAuthStore } from '@/store';

const authStore = useAuthStore();
const ms = useMessage();

// 标签页
const activeTab = ref('system');

// 分页
const currentPage = ref(1);
const pageSize = ref(10);

// 通知数据（模拟数据，实际应从API获取）
const systemNotifications = ref([
  {
    id: 1,
    title: '系统升级通知',
    content: '系统将于2023年6月1日进行升级维护，届时服务将暂停使用约2小时。',
    isRead: true,
    createdAt: '2023-05-28T10:00:00Z',
    type: 'info'
  },
  {
    id: 2,
    title: '新功能上线',
    content: 'AI绘本创作功能已全新上线，欢迎体验！',
    isRead: false,
    createdAt: '2023-05-25T14:30:00Z',
    type: 'success'
  },
  {
    id: 3,
    title: '账号安全提醒',
    content: '我们检测到您的账号在异地登录，如非本人操作，请及时修改密码。',
    isRead: false,
    createdAt: '2023-05-20T09:15:00Z',
    type: 'warning'
  }
]);

const classNotifications = ref([
  {
    id: 1,
    title: '班级作业通知',
    content: '请于本周五前完成AI绘本创作作业，并提交到班级作品集。',
    isRead: false,
    createdAt: '2023-05-27T08:00:00Z',
    sender: '王老师',
    className: '三年二班'
  },
  {
    id: 2,
    title: '班级活动通知',
    content: '下周三下午将举行AI创作比赛，请有兴趣的同学提前报名。',
    isRead: true,
    createdAt: '2023-05-24T16:45:00Z',
    sender: '李老师',
    className: '三年二班'
  }
]);

// 未读通知数量
const unreadSystemCount = computed(() => 
  systemNotifications.value.filter(item => !item.isRead).length
);

const unreadClassCount = computed(() => 
  classNotifications.value.filter(item => !item.isRead).length
);

// 根据当前标签页获取对应的通知列表
const currentNotifications = computed(() => {
  return activeTab.value === 'system' ? systemNotifications.value : classNotifications.value;
});

// 分页后的通知列表
const paginatedNotifications = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return currentNotifications.value.slice(startIndex, endIndex);
});

// 总页数
const totalPages = computed(() => 
  Math.ceil(currentNotifications.value.length / pageSize.value)
);

// 切换标签页
function handleTabChange(tab) {
  activeTab.value = tab;
  currentPage.value = 1; // 重置分页
}

// 标记通知为已读
function markAsRead(id) {
  const notifications = activeTab.value === 'system' ? systemNotifications : classNotifications;
  const notification = notifications.value.find(item => item.id === id);
  
  if (notification) {
    notification.isRead = true;
    ms.success('已标记为已读');
  }
}

// 标记所有通知为已读
function markAllAsRead() {
  const notifications = activeTab.value === 'system' ? systemNotifications : classNotifications;
  
  notifications.value.forEach(item => {
    item.isRead = true;
  });
  
  ms.success('已全部标记为已读');
}

// 删除通知
function deleteNotification(id) {
  const notifications = activeTab.value === 'system' ? systemNotifications : classNotifications;
  const index = notifications.value.findIndex(item => item.id === id);
  
  if (index !== -1) {
    notifications.value.splice(index, 1);
    ms.success('删除成功');
  }
}

// 清空所有通知
function clearAllNotifications() {
  const notifications = activeTab.value === 'system' ? systemNotifications : classNotifications;
  notifications.value = [];
  ms.success('已清空所有通知');
}

// 格式化日期
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 获取通知类型标签
function getTypeTag(type) {
  switch (type) {
    case 'info':
      return { type: 'info', text: '信息' };
    case 'success':
      return { type: 'success', text: '成功' };
    case 'warning':
      return { type: 'warning', text: '警告' };
    case 'error':
      return { type: 'error', text: '错误' };
    default:
      return { type: 'default', text: '通知' };
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 实际应用中，这里应该从API获取通知数据
});
</script>

<template>
  <div class="notifications-container animate__animated animate__fadeIn">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
      <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200">通知与消息</h2>
      
      <div class="flex items-center gap-3">
        <NPopconfirm
          v-if="currentNotifications.length > 0"
          @positive-click="clearAllNotifications"
          negative-text="取消"
          positive-text="确定"
        >
          <template #trigger>
            <NButton class="hover-float transition-all duration-300">
              <template #icon>
                <SvgIcon icon="ri:delete-bin-line" />
              </template>
              清空
            </NButton>
          </template>
          确定要清空所有{{ activeTab === 'system' ? '系统' : '班级' }}通知吗？
        </NPopconfirm>
        
        <NButton
          v-if="activeTab === 'system' ? unreadSystemCount > 0 : unreadClassCount > 0"
          @click="markAllAsRead"
          class="hover-float transition-all duration-300"
        >
          <template #icon>
            <SvgIcon icon="ri:check-double-line" />
          </template>
          全部已读
        </NButton>
      </div>
    </div>
    
    <NCard>
      <NTabs v-model:value="activeTab" type="line" animated @update:value="handleTabChange">
        <NTabPane name="system" tab="系统通知">
          <template #tab>
            <div class="flex items-center">
              <NBadge :value="unreadSystemCount" :show="unreadSystemCount > 0" :max="99">
                <div class="flex items-center">
                  <SvgIcon icon="ri:notification-3-line" class="mr-1" />
                  <span>系统通知</span>
                </div>
              </NBadge>
            </div>
          </template>
        </NTabPane>
        
        <NTabPane name="class" tab="班级通知">
          <template #tab>
            <div class="flex items-center">
              <NBadge :value="unreadClassCount" :show="unreadClassCount > 0" :max="99">
                <div class="flex items-center">
                  <SvgIcon icon="ri:group-line" class="mr-1" />
                  <span>班级通知</span>
                </div>
              </NBadge>
            </div>
          </template>
        </NTabPane>
      </NTabs>
      
      <!-- 通知列表 -->
      <div v-if="paginatedNotifications.length > 0" class="notification-list mt-4">
        <NList hoverable clickable>
          <NListItem
            v-for="notification in paginatedNotifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.isRead }"
          >
            <div class="notification-content">
              <div class="notification-header">
                <div class="notification-title-wrapper">
                  <NTag
                    v-if="activeTab === 'system'"
                    :type="getTypeTag(notification.type).type"
                    size="small"
                    class="mr-2"
                  >
                    {{ getTypeTag(notification.type).text }}
                  </NTag>
                  
                  <div class="notification-title">{{ notification.title }}</div>
                </div>
                
                <div class="notification-time">{{ formatDate(notification.createdAt) }}</div>
              </div>
              
              <div class="notification-body">
                <div class="notification-message">{{ notification.content }}</div>
                
                <div v-if="activeTab === 'class'" class="notification-meta">
                  <div class="notification-sender">
                    <span class="sender-label">发送人：</span>
                    <span class="sender-name">{{ notification.sender }}</span>
                  </div>
                  
                  <div class="notification-class">
                    <span class="class-label">班级：</span>
                    <span class="class-name">{{ notification.className }}</span>
                  </div>
                </div>
              </div>
              
              <div class="notification-actions">
                <NButton
                  v-if="!notification.isRead"
                  size="small"
                  @click.stop="markAsRead(notification.id)"
                  class="action-btn"
                >
                  标记已读
                </NButton>
                
                <NPopconfirm
                  @positive-click="deleteNotification(notification.id)"
                  negative-text="取消"
                  positive-text="确定"
                >
                  <template #trigger>
                    <NButton size="small" type="error" class="action-btn">
                      删除
                    </NButton>
                  </template>
                  确定要删除这条通知吗？
                </NPopconfirm>
              </div>
            </div>
          </NListItem>
        </NList>
        
        <!-- 分页 -->
        <div class="pagination-container mt-6">
          <NPagination
            v-model:page="currentPage"
            :page-count="totalPages"
            :page-size="pageSize"
            :page-sizes="[10, 20, 30]"
            show-size-picker
            @update:page-size="size => { pageSize = size; currentPage = 1; }"
          />
        </div>
      </div>
      
      <!-- 空状态 -->
      <NEmpty v-else description="暂无通知" class="mt-8" />
    </NCard>
  </div>
</template>

<style scoped>
.notifications-container {
  max-width: 900px;
  margin: 0 auto;
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.notification-item {
  position: relative;
  transition: all 0.3s ease;
}

.notification-item.unread {
  background-color: rgba(24, 144, 255, 0.05);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #2080f0;
  border-radius: 2px;
}

.notification-content {
  width: 100%;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title-wrapper {
  display: flex;
  align-items: center;
}

.notification-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.dark .notification-title {
  color: #e0e0e0;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

.dark .notification-time {
  color: #777;
}

.notification-body {
  margin-bottom: 12px;
}

.notification-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.dark .notification-message {
  color: #aaa;
}

.notification-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;
  font-size: 13px;
}

.notification-sender, .notification-class {
  display: flex;
  align-items: center;
}

.sender-label, .class-label {
  color: #999;
  margin-right: 4px;
}

.dark .sender-label, .dark .class-label {
  color: #777;
}

.sender-name, .class-name {
  color: #666;
  font-weight: 500;
}

.dark .sender-name, .dark .class-name {
  color: #aaa;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.action-btn {
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.pagination-container {
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .notification-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .notification-actions {
    margin-top: 8px;
  }
}
</style>
