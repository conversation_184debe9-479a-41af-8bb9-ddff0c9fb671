import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { StorybookService } from './storybook.service';
import { StorybookWorksService } from './storybook-works.service';
import { StorybookExportService } from './storybook-export.service';
import { StorybookController } from './storybook.controller';
import { StorybookAdminController } from './storybook-admin.controller';
import { StorybookWorksController } from './storybook-works.controller';
import { BadWordsModule } from '../badWords/badWords.module';
import { UploadModule } from '../upload/upload.module';
import {
  StorybookEntity,
  StorybookPageEntity,
  StorybookCharacterEntity,
  StorybookTemplateEntity,
  StorybookStatisticEntity,
  StorybookPromptEntity,
  StorybookConfigEntity,
  StorybookImageEntity,
  StorybookFolderEntity
} from './entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      StorybookEntity,
      StorybookPageEntity,
      StorybookCharacterEntity,
      StorybookTemplateEntity,
      StorybookStatisticEntity,
      StorybookPromptEntity,
      StorybookConfigEntity,
      StorybookImageEntity,
      StorybookFolderEntity
    ]),
    ConfigModule,
    BadWordsModule,
    UploadModule,
  ],
  controllers: [StorybookController, StorybookAdminController, StorybookWorksController],
  providers: [StorybookService, StorybookWorksService, StorybookExportService],
  exports: [StorybookService, StorybookWorksService, StorybookExportService],
})
export class StorybookModule {}
