import api from '../index';

export default {
  queryModels: (params: any) => api.get('models/query', { params }),
  setModels: (data: any) => api.post('models/setModel', data),
  delModels: (data: any) => api.post('models/delModel', data),

  /**
   * 获取模型列表
   * @param params 查询参数，可以包含modelType来筛选模型类型
   * @returns 模型列表
   */
  getModelList: (params: { modelType?: number, status?: number } = {}) => api.get('models/list', { params }),
};
