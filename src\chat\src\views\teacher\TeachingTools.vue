<template>
  <div class="teaching-tools-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">教学工具集</h1>
          <p class="page-description">智能化教学辅助工具，提升教学效率与质量</p>
        </div>

        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ totalTools }}</div>
            <div class="stat-label">可用工具</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ recentUsage }}</div>
            <div class="stat-label">本周使用</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具分类导航 -->
    <div class="tools-nav">
      <NTabs
        v-model:value="activeCategory"
        type="line"
        size="large"
        @update:value="handleCategoryChange"
      >
        <NTabPane
          v-for="category in toolCategories"
          :key="category.key"
          :name="category.key"
          :tab="category.label"
        >
          <template #tab>
            <div class="category-tab">
              <NIcon :component="category.icon" size="18" />
              <span>{{ category.label }}</span>
              <NBadge
                :value="category.count"
                type="info"
                :show="category.count > 0"
                class="category-badge"
              />
            </div>
          </template>
        </NTabPane>
      </NTabs>
    </div>

    <!-- 工具内容区域 -->
    <div class="tools-content">
      <!-- 应用中心 -->
      <div v-if="activeCategory === 'apps'" class="apps-section">
        <AppList />
      </div>

      <!-- 课程规划工具 -->
      <div v-if="activeCategory === 'planning'" class="tools-grid">
        <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="tool in planningTools" :key="tool.id">
            <NCard
              class="tool-card"
              hoverable
              @click="openTool(tool)"
              :class="{ 'tool-premium': tool.isPremium }"
            >
              <div class="tool-icon">
                <div class="icon-wrapper" :style="{ backgroundColor: tool.color + '20' }">
                  <NIcon :component="tool.icon" :size="32" :style="{ color: tool.color }" />
                </div>
                <NBadge v-if="tool.isNew" value="NEW" type="success" class="new-badge" />
                <NBadge v-if="tool.isPremium" value="PRO" type="warning" class="pro-badge" />
              </div>

              <div class="tool-info">
                <h3 class="tool-title">{{ tool.title }}</h3>
                <p class="tool-description">{{ tool.description }}</p>

                <div class="tool-features">
                  <NTag
                    v-for="feature in tool.features"
                    :key="feature"
                    size="small"
                    type="info"
                    class="feature-tag"
                  >
                    {{ feature }}
                  </NTag>
                </div>

                <div class="tool-actions">
                  <NButton
                    type="primary"
                    size="small"
                    @click.stop="openTool(tool)"
                    :disabled="tool.isPremium && !hasPremium"
                  >
                    {{ tool.isPremium && !hasPremium ? '升级解锁' : '立即使用' }}
                  </NButton>

                  <NButton
                    quaternary
                    size="small"
                    @click.stop="toggleFavorite(tool)"
                    :type="tool.isFavorite ? 'warning' : 'default'"
                  >
                    <template #icon>
                      <NIcon :component="tool.isFavorite ? StarOutline : StarOutline" />
                    </template>
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 学生管理工具 -->
      <div v-else-if="activeCategory === 'management'" class="tools-grid">
        <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="tool in managementTools" :key="tool.id">
            <NCard class="tool-card" hoverable @click="openTool(tool)">
              <div class="tool-icon">
                <div class="icon-wrapper" :style="{ backgroundColor: tool.color + '20' }">
                  <NIcon :component="tool.icon" :size="32" :style="{ color: tool.color }" />
                </div>
              </div>

              <div class="tool-info">
                <h3 class="tool-title">{{ tool.title }}</h3>
                <p class="tool-description">{{ tool.description }}</p>

                <div class="tool-stats" v-if="tool.stats">
                  <div class="stat-row">
                    <span class="stat-label">{{ tool.stats.label }}：</span>
                    <span class="stat-value">{{ tool.stats.value }}</span>
                  </div>
                </div>

                <div class="tool-actions">
                  <NButton type="primary" size="small" @click.stop="openTool(tool)">
                    打开管理
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 教学辅助工具 -->
      <div v-else-if="activeCategory === 'assistance'" class="tools-grid">
        <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="tool in assistanceTools" :key="tool.id">
            <NCard class="tool-card" hoverable @click="openTool(tool)">
              <div class="tool-icon">
                <div class="icon-wrapper" :style="{ backgroundColor: tool.color + '20' }">
                  <NIcon :component="tool.icon" :size="32" :style="{ color: tool.color }" />
                </div>
              </div>

              <div class="tool-info">
                <h3 class="tool-title">{{ tool.title }}</h3>
                <p class="tool-description">{{ tool.description }}</p>

                <div class="tool-actions">
                  <NButton type="primary" size="small" @click.stop="openTool(tool)">
                    {{ tool.actionText || '开始使用' }}
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 数据分析工具 -->
      <div v-else-if="activeCategory === 'analytics'" class="tools-grid">
        <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="tool in analyticsTools" :key="tool.id">
            <NCard class="tool-card" hoverable @click="openTool(tool)">
              <div class="tool-icon">
                <div class="icon-wrapper" :style="{ backgroundColor: tool.color + '20' }">
                  <NIcon :component="tool.icon" :size="32" :style="{ color: tool.color }" />
                </div>
              </div>

              <div class="tool-info">
                <h3 class="tool-title">{{ tool.title }}</h3>
                <p class="tool-description">{{ tool.description }}</p>

                <div class="tool-preview" v-if="tool.preview">
                  <img :src="tool.preview" :alt="tool.title" class="preview-image" />
                </div>

                <div class="tool-actions">
                  <NButton type="primary" size="small" @click.stop="openTool(tool)">
                    查看分析
                  </NButton>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>
    </div>

    <!-- 工具使用历史 -->
    <div class="recent-tools-section">
      <h2 class="section-title">最近使用</h2>
      <div class="recent-tools">
        <div
          v-for="tool in recentTools"
          :key="tool.id"
          class="recent-tool-item"
          @click="openTool(tool)"
        >
          <div class="recent-icon">
            <NIcon :component="tool.icon" :size="20" />
          </div>
          <div class="recent-info">
            <div class="recent-title">{{ tool.title }}</div>
            <div class="recent-time">{{ formatTime(tool.lastUsed) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具详情弹窗 -->
    <NModal
      v-model:show="showToolModal"
      preset="card"
      :title="selectedTool?.title"
      style="width: 90%; max-width: 800px;"
      :bordered="false"
      size="huge"
      @mask-click="closeToolModal"
    >
      <div v-if="selectedTool" class="tool-modal-content">
        <div class="modal-header">
          <div class="modal-icon">
            <NIcon :component="selectedTool.icon" :size="40" />
          </div>
          <div class="modal-info">
            <h3>{{ selectedTool.title }}</h3>
            <p>{{ selectedTool.description }}</p>
          </div>
        </div>

        <div class="modal-body">
          <div v-if="selectedTool.type === 'link'">
            <p>此工具将在新窗口中打开，是否继续？</p>
            <div class="modal-actions">
              <NButton type="primary" @click="confirmOpenTool">
                在新窗口打开
              </NButton>
              <NButton @click="closeToolModal">
                取消
              </NButton>
            </div>
          </div>

          <div v-else-if="selectedTool.type === 'coming-soon'">
            <NEmpty description="此功能正在开发中，敬请期待！">
              <template #extra>
                <NButton @click="closeToolModal">
                  我知道了
                </NButton>
              </template>
            </NEmpty>
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, provide } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import {
  NCard,
  NGrid,
  NGridItem,
  NTabs,
  NTabPane,
  NButton,
  NIcon,
  NBadge,
  NTag,
  NModal,
  NEmpty
} from 'naive-ui';
import AppList from '@/views/chat/components/CodePreview/AppList.vue';
import {
  DocumentTextOutline,
  PeopleOutline,
  SchoolOutline,
  BarChartOutline,
  BookOutline,
  ChatbubbleEllipsesOutline,
  CreateOutline,
  CalendarOutline,
  CheckmarkCircleOutline,
  TrendingUpOutline,
  LayersOutline,
  StarOutline,
  ClipboardOutline,
  BulbOutline,
  GameControllerOutline,
  CameraOutline,
  DocumentOutline
} from '@vicons/ionicons5';

const router = useRouter();
const message = useMessage();

// 状态管理
const activeCategory = ref('apps');
const showToolModal = ref(false);
const selectedTool = ref(null);
const hasPremium = ref(false); // 模拟用户权限

// 应用数量管理
const appCount = ref(0);

// 提供应用数量更新函数给 AppList 组件
provide('updateAppCount', (count: number) => {
  appCount.value = count;
  // 更新应用中心分类的计数
  const appsCategory = toolCategories.value.find(cat => cat.key === 'apps');
  if (appsCategory) {
    appsCategory.count = count;
  }
});

// 工具分类
const toolCategories = ref([
  {
    key: 'apps',
    label: '应用中心',
    icon: LayersOutline,
    count: 0
  },
  {
    key: 'planning',
    label: '课程规划',
    icon: DocumentTextOutline,
    count: 6
  },
  {
    key: 'management',
    label: '学生管理',
    icon: PeopleOutline,
    count: 4
  },
  {
    key: 'assistance',
    label: '教学辅助',
    icon: SchoolOutline,
    count: 8
  },
  {
    key: 'analytics',
    label: '数据分析',
    icon: BarChartOutline,
    count: 5
  }
]);

// 课程规划工具
const planningTools = ref([
  {
    id: 1,
    title: 'AI课程设计器',
    description: '基于教学目标，智能生成完整的课程教学计划',
    icon: BookOutline,
    color: '#2080f0',
    features: ['自动生成', '个性化', '多学科'],
    isNew: true,
    isPremium: false,
    isFavorite: false,
    type: 'feature',
    path: '/teacher/chat?template=course-design'
  },
  {
    id: 2,
    title: '教案生成助手',
    description: '输入课题和要求，自动生成详细教案模板',
    icon: DocumentTextOutline,
    color: '#18a058',
    features: ['模板丰富', '格式标准', '可编辑'],
    isNew: false,
    isPremium: false,
    isFavorite: true,
    type: 'feature',
    path: '/teacher/chat?template=lesson-plan'
  },
  {
    id: 3,
    title: '作业布置器',
    description: '根据教学进度和学生能力，智能生成个性化作业',
    icon: ClipboardOutline,
    color: '#f0a020',
    features: ['分层设计', '能力匹配', '自动评估'],
    isNew: false,
    isPremium: true,
    isFavorite: false,
    type: 'coming-soon'
  },
  {
    id: 4,
    title: '教学日历',
    description: '智能排课和教学进度管理工具',
    icon: CalendarOutline,
    color: '#d03050',
    features: ['进度跟踪', '提醒功能', '同步共享'],
    isNew: false,
    isPremium: false,
    isFavorite: false,
    type: 'coming-soon'
  },
  {
    id: 5,
    title: '知识点图谱',
    description: '构建学科知识点关联图，优化教学顺序',
    icon: LayersOutline,
    color: '#722ed1',
    features: ['可视化', '关联分析', '路径优化'],
    isNew: false,
    isPremium: true,
    isFavorite: false,
    type: 'coming-soon'
  },
  {
    id: 6,
    title: '教学资源库',
    description: 'AI生成的教学素材，包括图片、视频、互动内容',
    icon: LayersOutline,
    color: '#eb2f96',
    features: ['素材丰富', 'AI生成', '版权安全'],
    isNew: true,
    isPremium: false,
    isFavorite: false,
    type: 'link',
    url: '/showcase'
  }
]);

// 学生管理工具
const managementTools = ref([
  {
    id: 11,
    title: '班级管理',
    description: '全面管理班级学生信息，跟踪学习进度',
    icon: PeopleOutline,
    color: '#2080f0',
    stats: { label: '当前学生', value: '32人' },
    type: 'feature',
    path: '/teacher/class'
  },
  {
    id: 12,
    title: '学习档案',
    description: '每个学生的详细学习记录和成长轨迹',
    icon: BookOutline,
    color: '#18a058',
    stats: { label: '活跃档案', value: '28份' },
    type: 'coming-soon'
  },
  {
    id: 13,
    title: '分组管理',
    description: '智能分组建议和协作项目管理',
    icon: PeopleOutline,
    color: '#f0a020',
    stats: { label: '活动小组', value: '8个' },
    type: 'coming-soon'
  },
  {
    id: 14,
    title: '家长沟通',
    description: '学习报告自动生成和家校沟通记录',
    icon: ChatbubbleEllipsesOutline,
    color: '#d03050',
    stats: { label: '本周沟通', value: '15次' },
    type: 'coming-soon'
  }
]);

// 教学辅助工具
const assistanceTools = ref([
  {
    id: 21,
    title: 'AI教学对话',
    description: '专业的教学场景对话助手',
    icon: ChatbubbleEllipsesOutline,
    color: '#2080f0',
    actionText: '开始对话',
    type: 'feature',
    path: '/teacher/chat'
  },
  {
    id: 22,
    title: '互动教学游戏',
    description: '将知识点游戏化，提升学习兴趣',
    icon: GameControllerOutline,
    color: '#18a058',
    type: 'coming-soon'
  },
  {
    id: 23,
    title: '虚拟实验室',
    description: '安全的科学实验模拟环境',
    icon: BulbOutline,
    color: '#f0a020',
    type: 'coming-soon'
  },
  {
    id: 24,
    title: 'AI编程助手',
    description: '编程教学的智能助手工具',
    icon: CreateOutline,
    color: '#722ed1',
    type: 'link',
    url: '/aiProgramming'
  },
  {
    id: 25,
    title: '课堂拍照识别',
    description: '拍照识别学生作业和课堂板书',
    icon: CameraOutline,
    color: '#eb2f96',
    type: 'coming-soon'
  },
  {
    id: 26,
    title: '语音转文字',
    description: '课堂录音自动转换为文字记录',
    icon: ChatbubbleEllipsesOutline,
    color: '#52c41a',
    type: 'coming-soon'
  },
  {
    id: 27,
    title: '学生作品管理',
    description: '查看、评阅和管理学生创作的作品',
    icon: BookOutline,
    color: '#1890ff',
    type: 'feature',
    path: '/teacher/works'
  },
  {
    id: 28,
    title: 'PPT智能生成',
    description: '根据教学内容自动生成精美课件',
    icon: DocumentOutline,
    color: '#fa8c16',
    type: 'coming-soon'
  }
]);

// 数据分析工具
const analyticsTools = ref([
  {
    id: 31,
    title: '学习进度分析',
    description: '学生个人和班级整体学习进度可视化',
    icon: TrendingUpOutline,
    color: '#2080f0',
    preview: '/images/preview-progress.png',
    type: 'coming-soon'
  },
  {
    id: 32,
    title: '知识点掌握热力图',
    description: '全班知识点掌握情况一目了然',
    icon: BarChartOutline,
    color: '#18a058',
    type: 'coming-soon'
  },
  {
    id: 33,
    title: '学习轨迹图谱',
    description: '学生学习路径和行为模式分析',
    icon: BarChartOutline,
    color: '#f0a020',
    type: 'coming-soon'
  },
  {
    id: 34,
    title: '教学效果评估',
    description: 'AI分析教学效果，提供改进建议',
    icon: CheckmarkCircleOutline,
    color: '#d03050',
    type: 'coming-soon'
  },
  {
    id: 35,
    title: '预警系统',
    description: '学习困难学生早期识别和干预建议',
    icon: TrendingUpOutline,
    color: '#722ed1',
    type: 'coming-soon'
  }
]);

// 最近使用的工具
const recentTools = ref([
  { ...planningTools.value[0], lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000) },
  { ...assistanceTools.value[0], lastUsed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) },
  { ...planningTools.value[1], lastUsed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) }
]);

// 计算属性
const totalTools = computed(() => {
  return planningTools.value.length +
         managementTools.value.length +
         assistanceTools.value.length +
         analyticsTools.value.length;
});

const recentUsage = computed(() => {
  return recentTools.value.length * 5; // 模拟使用次数
});

// 方法
const handleCategoryChange = (category: string) => {
  activeCategory.value = category;
};

const openTool = (tool: any) => {
  if (tool.type === 'feature' && tool.path) {
    router.push(tool.path);
  } else if (tool.type === 'link' && tool.url) {
    router.push(tool.url);
  } else {
    selectedTool.value = tool;
    showToolModal.value = true;
  }
};

const confirmOpenTool = () => {
  if (selectedTool.value?.url) {
    window.open(selectedTool.value.url, '_blank');
  }
  closeToolModal();
};

const closeToolModal = () => {
  showToolModal.value = false;
  selectedTool.value = null;
};

const toggleFavorite = (tool: any) => {
  tool.isFavorite = !tool.isFavorite;
  message.success(tool.isFavorite ? '已添加到收藏' : '已取消收藏');
};

const formatTime = (time: Date) => {
  const now = new Date();
  const diff = now.getTime() - time.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return `${days}天前`;
  }
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped lang="scss">
.teaching-tools-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
      text-align: center;
    }
  }

  .title-section {
    flex: 1;
  }

  .page-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }

  .page-description {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }

  .header-stats {
    display: flex;
    gap: 32px;

    @media (max-width: 768px) {
      gap: 20px;
    }
  }

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

.tools-nav {
  background: white;
  border-radius: 12px;
  padding: 16px 24px 0;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .category-tab {
    display: flex;
    align-items: center;
    gap: 8px;

    .category-badge {
      margin-left: 4px;
    }
  }
}

.tools-content {
  margin-bottom: 32px;
}

.tools-grid {
  .tool-card {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.tool-premium {
      border: 2px solid #faad14;
    }
  }

  .tool-icon {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 16px;

    .icon-wrapper {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .new-badge,
    .pro-badge {
      position: absolute;
      top: -4px;
      right: -8px;
    }
  }

  .tool-info {
    text-align: center;
  }

  .tool-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
  }

  .tool-description {
    font-size: 14px;
    color: #666;
    margin: 0 0 16px 0;
    line-height: 1.5;
  }

  .tool-features {
    margin: 16px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;

    .feature-tag {
      font-size: 12px;
    }
  }

  .tool-stats {
    margin: 12px 0;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;

    .stat-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;

      .stat-label {
        color: #666;
      }

      .stat-value {
        font-weight: 600;
        color: #333;
      }
    }
  }

  .tool-preview {
    margin: 12px 0;

    .preview-image {
      width: 100%;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
      background: #f0f2f5;
    }
  }

  .tool-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
    margin-top: 16px;
  }
}

.recent-tools-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .section-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: #333;
  }

  .recent-tools {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding: 8px 0;

    .recent-tool-item {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: #f8f9fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 200px;

      &:hover {
        background: #e9ecef;
      }

      .recent-icon {
        width: 32px;
        height: 32px;
        background: #2080f0;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .recent-info {
        flex: 1;

        .recent-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .recent-time {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

.tool-modal-content {
  .modal-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;

    .modal-icon {
      width: 64px;
      height: 64px;
      background: #f0f2f5;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-info {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
      }
    }
  }

  .modal-body {
    .modal-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      margin-top: 24px;
    }
  }
}

// 应用中心样式
.apps-section {
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

@media (max-width: 768px) {
  .teaching-tools-page {
    padding: 16px;
  }

  .tools-grid {
    :deep(.n-grid-item) {
      grid-column: span 1 !important;
    }
  }

  .recent-tools {
    flex-direction: column !important;

    .recent-tool-item {
      min-width: auto !important;
    }
  }
}
</style>