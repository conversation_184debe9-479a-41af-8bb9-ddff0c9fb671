{"type": "module", "version": "4.1.0", "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.22", "@imengyu/vue3-context-menu": "^1.4.1", "@vueuse/core": "^10.10.0", "@vueuse/integrations": "^10.10.0", "autoprefixer": "^10.4.19", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.5.0", "element-plus": "^2.7.4", "eruda": "^3.0.1", "floating-vue": "5.2.2", "hotkeys-js": "^3.13.7", "less": "^4.2.0", "lodash-es": "^4.17.21", "marked": "^13.0.0", "md-editor-v3": "^4.16.7", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "overlayscrollbars": "^2.8.3", "overlayscrollbars-vue": "^0.5.9", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "resize-observer-polyfill": "^1.5.1", "vconsole": "^3.15.1", "vue": "^3.4.27", "vue-m-message": "^4.0.2", "vue-router": "^4.3.2"}, "devDependencies": {"@iconify/json": "^2.2.217", "@iconify/vue": "^4.1.2", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/path-browserify": "^1.0.2", "@unocss/core": "^0.61.0", "@unocss/preset-mini": "^0.61.0", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "archiver": "^7.0.1", "boxen": "^7.1.1", "bumpp": "^9.4.1", "eslint": "^9.4.0", "esno": "^4.7.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "inquirer": "^9.2.23", "npm-run-all2": "^6.2.0", "picocolors": "^1.0.1", "plop": "^4.0.1", "sass": "^1.77.4", "stylelint": "^16.6.1", "svgo": "^3.3.2", "typescript": "^5.4.5", "unocss": "^0.60.4", "unplugin-auto-import": "^0.17.6", "unplugin-turbo-console": "^1.8.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.12", "vite-plugin-banner": "^0.7.1", "vite-plugin-checker": "^0.6.4", "vite-plugin-compression2": "^1.1.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-pages": "^0.32.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.2.1", "vite-plugin-vue-meta-layouts": "^0.4.3", "vue-tsc": "^2.0.19"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}