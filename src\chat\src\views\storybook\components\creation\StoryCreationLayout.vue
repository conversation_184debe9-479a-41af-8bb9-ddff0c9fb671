<template>
  <div class="story-creation-layout">
    <!-- 左侧栏 -->
    <div class="sidebar" :class="{ 'open': isSidebarOpen }">
      <div class="sidebar-header">
        <div class="logo-container">
          <div class="logo-icon-wrapper">
            <span class="logo-icon">📚</span>
          </div>
          <div class="logo-text-wrapper">
            <span class="logo-text">AI绘本创作</span>
            <span class="logo-subtitle">创造精彩故事的魔法世界</span>
          </div>
        </div>
      </div>

      <div class="sidebar-content">
        <div class="sidebar-section">
          <div class="section-title">绘本资源</div>
          <div class="tool-list">
            <div
              v-for="tool in tools"
              :key="tool.id"
              class="tool-item"
              :class="{ 'active': props.activeTab === tool.id }"
              @click="switchTool(tool.id)"
            >
              <div class="tool-icon">{{ tool.emoji }}</div>
              <div class="tool-name">{{ tool.label }}</div>
            </div>
          </div>
        </div>

        <div class="sidebar-section" v-if="props.currentStep > 0">
          <div class="section-title">创作步骤</div>
          <div class="step-list">
            <div
              v-for="step in steps"
              :key="step.id"
              class="step-item"
              :class="{ 'active': props.currentStep === step.id }"
              @click="setCurrentStep(step.id)"
            >
              <div class="step-icon">{{ step.icon }}</div>
              <div class="step-info">
                <div class="step-name">{{ step.name }}</div>
                <div class="step-desc">{{ step.description }}</div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <div class="sidebar-footer">
        <div class="footer-buttons">
          <button class="footer-button" @click="goToUserCenter">
            <span class="button-icon">👤</span>
            <span class="button-text">个人中心</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <div class="content-wrapper">
        <slot></slot>
      </div>
    </div>

    <!-- 移动端侧边栏切换按钮 -->
    <div v-if="isMobile" class="mobile-sidebar-toggle" @click="toggleSidebar">
      <span v-if="!isSidebarOpen">📚</span>
      <span v-else>✖️</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useGlobalStoreWithOut } from '@/store';
import { useBasicLayout } from '@/hooks/useBasicLayout';

const router = useRouter();
const { isMobile } = useBasicLayout();

// 侧边栏状态
const isSidebarOpen = ref(!isMobile.value);

const props = defineProps({
  currentStep: {
    type: Number,
    default: 1
  },
  activeTab: {
    type: String,
    default: 'outline'
  }
});

const emit = defineEmits(['update:current-step', 'update:active-tab']);

// 绘本资源工具
const tools = [
  { id: 'reference', label: '故事书架', emoji: '📚' },
  { id: 'my-works', label: '我的作品', emoji: '📝' },
  { id: 'free-creation', label: '自由创作', emoji: '✏️' }
];

// 切换工具
const switchTool = (toolId) => {
  console.log('切换工具:', toolId);

  // 如果是自由创作，跳转到聊天页面（保留路由跳转，因为这是完全不同的功能模块）
  if (toolId === 'free-creation') {
    console.log('跳转到聊天页面');
    router.push('/chat');
    return;
  }

  // 对于所有绘本创作相关的功能，统一使用标签页切换，避免不必要的路由跳转
  console.log(`切换到${toolId}标签`);
  emit('update:active-tab', toolId);

  // 在移动端上，点击后关闭侧边栏
  if (isMobile.value) {
    isSidebarOpen.value = false;
  }
};

// 创作步骤
const steps = [
  {
    id: 1,
    name: '想一个故事',
    icon: '💭',
    description: '你的故事是关于什么的？'
  },
  {
    id: 2,
    name: '创造角色',
    icon: '👦',
    description: '故事里有哪些人物？'
  },
  {
    id: 3,
    name: '画出故事',
    icon: '🎨',
    description: '把你的故事画出来吧！'
  },
  {
    id: 4,
    name: '完成绘本',
    icon: '🎉',
    description: '看看你的精彩故事！'
  }
];

const setCurrentStep = (step) => {
  // 确保在切换步骤时也切换到绘本创作模式
  emit('update:active-tab', 'outline');
  emit('update:current-step', step);

  // 在移动端上，点击后关闭侧边栏
  if (isMobile.value) {
    isSidebarOpen.value = false;
  }
};

// 前往用户中心
const goToUserCenter = () => {
  // 使用弹窗而不是页面跳转
  const useGlobalStore = useGlobalStoreWithOut();
  useGlobalStore.updateUserCenterDialog(true);

  // 在移动端上，点击后关闭侧边栏
  if (isMobile.value) {
    isSidebarOpen.value = false;
  }
};

// 切换侧边栏显示状态
const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};


</script>

<style scoped>
.story-creation-layout {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  height: 100%;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
}

.dark .sidebar {
  background-color: #1e293b;
  border-right: 1px solid #334155;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.dark .sidebar-header {
  border-bottom: 1px solid #334155;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon-wrapper {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
  transform: rotate(-5deg);
  transition: all 0.3s ease;
}

.logo-icon-wrapper:hover {
  transform: rotate(0deg) scale(1.05);
}

.logo-icon {
  font-size: 1.75rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text-wrapper {
  display: flex;
  flex-direction: column;
}

.logo-text {
  font-size: 1.35rem;
  font-weight: 800;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
  position: relative;
}

.logo-subtitle {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.dark .logo-text {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .logo-subtitle {
  color: #94a3b8;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  background-color: #f8fafc;
  background-image:
    radial-gradient(circle at 25% 10%, rgba(59, 130, 246, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 75% 75%, rgba(249, 115, 22, 0.05) 0%, transparent 20%);
}

.dark .sidebar-content {
  background-color: #1e293b;
  background-image:
    radial-gradient(circle at 25% 10%, rgba(59, 130, 246, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 75% 75%, rgba(249, 115, 22, 0.1) 0%, transparent 20%);
}

.sidebar-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  padding: 1rem;
  border-radius: 1rem;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02),
              0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(226, 232, 240, 0.7);
  transition: all 0.3s ease;
}

.dark .sidebar-section {
  background-color: rgba(15, 23, 42, 0.7);
  border-color: rgba(51, 65, 85, 0.7);
}

.sidebar-section:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.03),
              0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

.section-title {
  font-size: 0.875rem;
  font-weight: 700;
  color: #3b82f6;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  padding-left: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 1rem;
  background: linear-gradient(to bottom, #3b82f6, #60a5fa);
  border-radius: 2px;
}

.dark .section-title {
  color: #60a5fa;
}

.dark .section-title::before {
  background: linear-gradient(to bottom, #60a5fa, #93c5fd);
}

.step-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.step-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(96, 165, 250, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.step-item:hover {
  background-color: rgba(241, 245, 249, 0.8);
  border-color: rgba(226, 232, 240, 0.8);
  transform: translateY(-2px);
}

.step-item:hover::before {
  opacity: 1;
}

.dark .step-item:hover {
  background-color: rgba(51, 65, 85, 0.5);
  border-color: rgba(51, 65, 85, 0.8);
}

.step-item.active {
  background-color: rgba(224, 242, 254, 0.8);
  border-color: rgba(186, 230, 253, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.dark .step-item.active {
  background-color: rgba(15, 23, 42, 0.8);
  border-color: rgba(30, 41, 59, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  font-size: 1.25rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.dark .step-icon {
  background-color: #334155;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.step-item:hover .step-icon {
  transform: scale(1.05) rotate(-5deg);
}

.step-item.active .step-icon {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.dark .step-item.active .step-icon {
  background: linear-gradient(135deg, #60a5fa, #93c5fd);
  color: #1e293b;
  box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
}

.step-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  transition: all 0.3s ease;
}

.step-item:hover .step-info {
  transform: translateX(4px);
}

.step-name {
  font-size: 0.9rem;
  font-weight: 700;
  color: #1e293b;
  transition: color 0.3s ease;
}

.dark .step-name {
  color: #e2e8f0;
}

.step-item.active .step-name {
  color: #3b82f6;
}

.dark .step-item.active .step-name {
  color: #60a5fa;
}

.step-desc {
  font-size: 0.75rem;
  color: #64748b;
  transition: color 0.3s ease;
}

.dark .step-desc {
  color: #94a3b8;
}

.step-item.active .step-desc {
  color: #3b82f6;
  opacity: 0.8;
}

.dark .step-item.active .step-desc {
  color: #60a5fa;
  opacity: 0.8;
}

.tool-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.tool-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(96, 165, 250, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.tool-item:hover {
  background-color: rgba(241, 245, 249, 0.8);
  border-color: rgba(226, 232, 240, 0.8);
  transform: translateY(-2px);
}

.tool-item:hover::before {
  opacity: 1;
}

.dark .tool-item:hover {
  background-color: rgba(51, 65, 85, 0.5);
  border-color: rgba(51, 65, 85, 0.8);
}

.tool-item.active {
  background-color: rgba(224, 242, 254, 0.8);
  border-color: rgba(186, 230, 253, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.dark .tool-item.active {
  background-color: rgba(15, 23, 42, 0.8);
  border-color: rgba(30, 41, 59, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.tool-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  font-size: 1.25rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.dark .tool-icon {
  background-color: #334155;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.tool-item:hover .tool-icon {
  transform: scale(1.05) rotate(-5deg);
}

.tool-item.active .tool-icon {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.dark .tool-item.active .tool-icon {
  background: linear-gradient(135deg, #60a5fa, #93c5fd);
  color: #1e293b;
  box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
}

.tool-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.tool-item:hover .tool-name {
  transform: translateX(4px);
}

.dark .tool-name {
  color: #e2e8f0;
}

.tool-item.active .tool-name {
  color: #3b82f6;
}

.dark .tool-item.active .tool-name {
  color: #60a5fa;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(to top, rgba(241, 245, 249, 0.8), transparent);
}

.dark .sidebar-footer {
  border-top: 1px solid #334155;
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
}

.footer-buttons {
  display: flex;
  gap: 0.75rem;
  width: 100%;
}

.footer-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.85rem;
  border-radius: 0.75rem;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #3b82f6;
  font-size: 0.95rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05),
              0 1px 3px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.footer-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0));
  transition: all 0.6s ease;
}

.dark .footer-button {
  background: linear-gradient(135deg, #334155, #1e293b);
  color: #60a5fa;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2),
              0 1px 3px rgba(0, 0, 0, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.footer-button:hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15),
              0 2px 6px rgba(59, 130, 246, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.footer-button:hover::before {
  left: 100%;
}

.dark .footer-button:hover {
  background: linear-gradient(135deg, #475569, #334155);
  color: #93c5fd;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2),
              0 2px 6px rgba(59, 130, 246, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.footer-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1),
              inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .footer-button:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3),
              inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.button-icon {
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.footer-button:hover .button-icon {
  transform: translateY(-2px);
}



.main-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  padding: 1rem;
  overflow: hidden;
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  /* 添加移动端侧边栏切换按钮 */
  .mobile-sidebar-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
    z-index: 99;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .mobile-sidebar-toggle:hover {
    transform: scale(1.1);
  }

  .dark .mobile-sidebar-toggle {
    background: linear-gradient(135deg, #60a5fa, #93c5fd);
    color: #1e293b;
  }
}
</style>
