<template>
  <div class="creation-workspace-wrapper">
    <CreationWorkspace
      :project-data="projectData"
      :current-step="currentStep"
      @update:current-step="updateCurrentStep"
      @step-change="handleStepChange"
    />

    <!-- 传递当前步骤给AI魔笔 -->
    <AIMagicPen
      v-if="showAIMagicPen"
      :project-data="projectData"
      :current-step="currentStep"
    />
  </div>
</template>

<script setup>
import { ref, provide, inject, watch } from 'vue';
import CreationWorkspace from './creation/CreationWorkspace.vue';
import AIMagicPen from './AIMagicPen.vue';

const props = defineProps({
  projectData: Object,
  currentStep: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:current-step']);

// 控制AI魔笔显示
const showAIMagicPen = ref(true);

// 从父组件注入onConversation函数
const onConversation = inject('onConversation');
const logStoryBookEvent = inject('logStoryBookEvent');

// 更新当前步骤
const updateCurrentStep = (step) => {
  emit('update:current-step', step);
  logStoryBookEvent('INFO', `步骤已更新为: ${step}`);
};

// 处理步骤变化
const handleStepChange = (step) => {
  updateCurrentStep(step);
};

// 提供onConversation函数给子组件
provide('onConversation', onConversation);

// 监听当前步骤变化
watch(() => props.currentStep, (newStep) => {
  logStoryBookEvent('DEBUG', `CreationWorkspaceWrapper接收到新步骤: ${newStep}`);
});
</script>

<style scoped>
.creation-workspace-wrapper {
  height: 100%;
  width: 100%;
}
</style>
