{"prettier.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "cSpell.words": ["ant<PERSON>", "axios", "Baichuan", "bumpp", "Chatbox", "chatglm", "chatgpt", "chenz<PERSON>yu", "chevereto", "cogvideox", "commitlint", "cref", "dall", "dalle", "<PERSON><PERSON><PERSON>", "deepseek", "docker<PERSON>b", "EMAILCODE", "esno", "GPTAPI", "gpts", "highlightjs", "hljs", "hun<PERSON>", "iconify", "ISDEV", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "langchain", "linkify", "logprobs", "longcontext", "luma", "mapi", "Markmap", "mdhljs", "micromessenger", "mila", "Mindmap", "MODELSMAPLIST", "MODELTYPELIST", "modelvalue", "newconfig", "niji", "Nmessage", "nodata", "OPENAI", "pinia", "Popconfirm", "PPTCREATE", "projectaddress", "qwen", "rushstack", "sdxl", "<PERSON><PERSON>", "sref", "suno", "tailwindcss", "traptitech", "tsup", "Typecheck", "typeorm", "unplugin", "usercenter", "vastxie", "VITE", "vueuse", "<PERSON>"], "vue.codeActions.enabled": false, "volar.experimental.tsconfigPaths": {"./chat": ["./chat/tsconfig.json"], "./admin": ["./admin/tsconfig.json"], "./service": ["./service/tsconfig.json"]}}