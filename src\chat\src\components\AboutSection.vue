<template>
  <div id="about" class="about-section">
    <div class="about-background">
      <div class="about-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>
    </div>

    <div class="section-container">
      <div class="section-header">
        <div class="section-badge">关于我们</div>
        <h2 class="section-title">释放<span class="text-gradient">创作力量</span></h2>
        <p class="section-description">
          DeepCreate 是一个专注于AI创作的平台，我们致力于为用户提供最先进的AI创作工具，
          帮助用户轻松创作高质量的内容。无论是编程、写作、绘本还是音乐，我们都为您提供了
          强大的AI助手，让创作变得更加简单和有趣。
        </p>
      </div>

      <div class="about-features">
        <div class="about-feature">
          <div class="feature-icon">
            <SvgIcon name="ri:shield-check-line" size="32" />
          </div>
          <h3 class="feature-title">安全可靠</h3>
          <p class="feature-description">我们使用最先进的安全技术保护您的数据和创作内容</p>
        </div>
        <div class="about-feature">
          <div class="feature-icon">
            <SvgIcon name="ri:group-line" size="32" />
          </div>
          <h3 class="feature-title">社区支持</h3>
          <p class="feature-description">加入我们的创作者社区，分享作品并获取反馈</p>
        </div>
        <div class="about-feature">
          <div class="feature-icon">
            <SvgIcon name="ri:rocket-line" size="32" />
          </div>
          <h3 class="feature-title">持续创新</h3>
          <p class="feature-description">我们不断更新和改进我们的AI模型和创作工具</p>
        </div>
      </div>

      <div class="about-cta">
        <n-button type="primary" size="large" class="cta-button" @click="navigateToCreate">
          <div class="button-content">
            <span>开始创作之旅</span>
            <SvgIcon name="ri:arrow-right-line" size="18" />
          </div>
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@/components/common';
import { NButton } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/store';

const router = useRouter();
const authStore = useAuthStore();

function navigateToCreate() {
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }
  router.push('/chat');
}
</script>

<style scoped>
.about-section {
  padding: 6rem 0;
  background-color: var(--color-gray-50);
  position: relative;
  overflow: hidden;
}

.dark .about-section {
  background-color: var(--color-gray-900);
}

.about-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.about-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.3;
}

.dark .shape {
  opacity: 0.15;
}

.shape-1 {
  top: -10%;
  left: -10%;
  width: 50%;
  height: 50%;
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
}

.shape-2 {
  bottom: -10%;
  right: -10%;
  width: 50%;
  height: 50%;
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
}

.section-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.dark .section-badge {
  background-color: rgba(79, 70, 229, 0.2);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--color-gray-900);
}

.dark .section-title {
  color: white;
}

.text-gradient {
  background: linear-gradient(to right, #4f46e5, #06b6d4);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.section-description {
  max-width: 800px;
  margin: 0 auto 3rem;
  text-align: center;
  color: var(--color-gray-600);
  line-height: 1.6;
  font-size: 1.125rem;
}

.dark .section-description {
  color: var(--color-gray-400);
}

.about-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-bottom: 4rem;
}

.about-feature {
  background-color: white;
  border-radius: 1.5rem;
  padding: 2.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.02);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .about-feature {
  background-color: var(--color-gray-800);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.about-feature:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 30px -10px rgba(79, 70, 229, 0.1), 0 10px 15px -5px rgba(79, 70, 229, 0.05);
}

.dark .about-feature:hover {
  box-shadow: 0 20px 30px -10px rgba(79, 70, 229, 0.2), 0 10px 15px -5px rgba(79, 70, 229, 0.1);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
  color: #4f46e5;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.dark .feature-icon {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.2) 0%, rgba(6, 182, 212, 0.2) 100%);
  color: #818cf8;
}

.about-feature:hover .feature-icon {
  background: linear-gradient(135deg, #4f46e5 0%, #06b6d4 100%);
  color: white;
  transform: scale(1.05);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--color-gray-900);
}

.dark .feature-title {
  color: white;
}

.feature-description {
  color: var(--color-gray-600);
  font-size: 0.875rem;
  line-height: 1.6;
}

.dark .feature-description {
  color: var(--color-gray-400);
}

.about-cta {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.cta-button {
  padding: 0 2rem;
  height: 3.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(135deg, #4f46e5, #06b6d4);
  border: none;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(79, 70, 229, 0.2), 0 4px 6px -2px rgba(79, 70, 229, 0.1);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 20px -3px rgba(79, 70, 229, 0.3), 0 6px 8px -2px rgba(79, 70, 229, 0.15);
}

.button-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .about-features {
    grid-template-columns: 1fr;
  }
}
</style>
