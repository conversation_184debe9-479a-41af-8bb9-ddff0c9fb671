<script setup lang="ts">
import { fetchUpdatePasswordAPI } from '@/api';
import type { ResData } from '@/api/types';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { useAuthStore } from '@/store';
import type { FormInst, FormItemInst, FormItemRule, FormRules } from 'naive-ui';
import {
  NButton,
  NCard,
  NForm,
  NFormItem,
  NGrid,
  NGridItem,
  NInput,
  useMessage,
} from 'naive-ui';
import { ref } from 'vue';

interface ModelType {
  password: string;
  reenteredPassword: string;
}

const modelRef = ref<ModelType>({
  password: '',
  reenteredPassword: '',
});
const model = modelRef;

const formRef = ref<FormInst | null>(null);
const rPasswordFormItemRef = ref<FormItemInst | null>(null);

function validatePasswordStartWith(rule: FormItemRule, value: string): boolean {
  return (
    !value ||
    (!!modelRef.value.password &&
      value.startsWith(modelRef.value.password.slice(0, value.length)))
  );
}

function validatePasswordSame(rule: FormItemRule, value: string): boolean {
  return value === modelRef.value.password;
}

function handlePasswordInput() {
  if (modelRef.value.reenteredPassword) {
    rPasswordFormItemRef.value?.validate({ trigger: 'password-input' });
  }
}

const rules: FormRules = {
  password: [
    {
      required: true,
      message: '请输入密码',
    },
  ],
  reenteredPassword: [
    {
      required: true,
      message: '请再次输入密码',
      trigger: ['input', 'blur'],
    },
    {
      validator: validatePasswordStartWith,
      message: '两次输入的密码不一致',
      trigger: 'input',
    },
    {
      validator: validatePasswordSame,
      message: '两次输入的密码不一致',
      trigger: ['blur', 'password-input'],
    },
  ],
};

const { isMobile } = useBasicLayout();
const authStore = useAuthStore();
const ms = useMessage();

async function updatePassword(options: { password: string }) {
  const res: ResData = await fetchUpdatePasswordAPI(options);
  ms.success('密码更新成功');
  resetForm();
  authStore.updatePasswordSuccess();
}

function resetForm() {
  modelRef.value = {
    password: '',
    reenteredPassword: '',
  };
}

function handleValidate(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const { password } = modelRef.value;
      updatePassword({ password });
    }
  });
}
</script>

<template>
  <div class="password-container">
    <NCard>
      <template #header>
        <div class="text-base font-medium">修改密码</div>
      </template>
      <div class="password-content">
        <NGrid :x-gap="24" :y-gap="24" :cols="isMobile ? 1 : 3">
          <!-- 密码表单 -->
          <NGridItem :span="isMobile ? 1 : 2" class="password-form-container">
            <NForm ref="formRef" :model="model" :rules="rules" class="password-form">
              <NFormItem path="password" label="新密码">
                <NInput
                  v-model:value="model.password"
                  type="password"
                  @input="handlePasswordInput"
                  @keydown.enter.prevent
                  class="hover-input"
                />
              </NFormItem>

              <NFormItem
                ref="rPasswordFormItemRef"
                first
                path="reenteredPassword"
                label="确认密码"
              >
                <NInput
                  v-model:value="model.reenteredPassword"
                  :disabled="!model.password"
                  type="password"
                  tabindex="0"
                  @keyup.enter="handleValidate"
                  class="hover-input"
                />
              </NFormItem>

              <div class="form-actions">
                <span class="form-note">密码修改后需要重新登录</span>
                <NButton
                  :disabled="!model.password"
                  type="primary"
                  class="hover-float transition-all duration-300"
                  @click="handleValidate"
                >
                  更新密码
                </NButton>
              </div>
            </NForm>
          </NGridItem>

          <!-- 密码要求说明 -->
          <NGridItem :span="isMobile ? 1 : 1" class="password-requirements-container">
            <div class="password-requirements">
              <div class="requirements-title">密码要求</div>
              <p class="requirements-description">
                请按照以下要求设置新密码
              </p>
              <ul class="requirements-list">
                <li>最少 6 个字符</li>
                <li>最多 20 个字符</li>
                <li>包含数字</li>
                <li>包含字母</li>
              </ul>
            </div>
          </NGridItem>
        </NGrid>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.password-container {
  padding: 8px 0;
}

.password-content {
  padding: 8px 0;
}

.password-form-container {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.password-form-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.form-note {
  color: #95aac9;
  font-size: 14px;
}

.password-requirements-container {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.password-requirements-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.password-requirements {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.requirements-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.requirements-description {
  color: #95aac9;
  font-size: 14px;
}

.requirements-list {
  list-style-type: disc;
  padding-left: 20px;
  color: #95aac9;
}

.requirements-list li {
  margin-bottom: 6px;
}

.hover-input:hover {
  border-color: var(--primary-color);
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

:deep(.dark) .password-form-container,
:deep(.dark) .password-requirements-container {
  border-color: rgba(255, 255, 255, 0.17);
}

:deep(.dark) .requirements-title {
  color: #e0e0e0;
}
</style>
