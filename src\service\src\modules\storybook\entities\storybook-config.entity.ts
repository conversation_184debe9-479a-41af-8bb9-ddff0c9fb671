import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'storybook_config' })
export class StorybookConfigEntity extends BaseEntity {
  @Column({ comment: '配置键', unique: true })
  configKey: string;

  @Column({ comment: '配置值', type: 'text' })
  configVal: string;

  @Column({ comment: '配置描述', nullable: true })
  description: string;

  @Column({ comment: '是否公开(0:私有,1:公开)', default: 0 })
  public: number;
}
