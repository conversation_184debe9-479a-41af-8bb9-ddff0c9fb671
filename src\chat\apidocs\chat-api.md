# AI聊天接口文档

## 接口规范

### 请求格式

所有API请求应遵循以下格式：

- 请求方法：POST/GET (根据具体接口而定)
- 请求头：
  - `Content-Type: application/json`
  - `Authorization: Bearer {token}` (需要认证的接口)
- 请求体：JSON格式

### 响应格式

所有API响应遵循以下格式：

```json
{
  "code": 200,
  "success": true,
  "data": {}, // 具体数据，根据接口而定
  "message": "请求成功"
}
```

**注意**：流式响应接口（如聊天流式响应）不遵循此格式，会直接返回数据流。

## 聊天相关接口

### 1. 聊天对话（流式响应）

**接口地址**：`/chatgpt/chat-process`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "prompt": "你好，请介绍一下自己",
  "options": {
    "parentMessageId": "message_id",
    "model": "gpt-3.5-turbo",
    "temperature": 0.7,
    "top_p": 1,
    "groupId": 123
  },
  "systemMessage": "You are ChatGPT, a large language model trained by OpenAI. Follow the user's instructions carefully. Respond using markdown.",
  "model": "gpt-3.5-turbo"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| prompt | string | 是 | 用户输入的问题 |
| options | object | 否 | 对话选项 |
| options.parentMessageId | string | 否 | 上一条消息ID，用于连续对话 |
| options.model | string | 否 | 使用的模型，如不指定则使用默认模型 |
| options.temperature | number | 否 | 温度参数，控制随机性，默认0.7 |
| options.top_p | number | 否 | 核采样参数 |
| options.groupId | number | 否 | 对话组ID |
| systemMessage | string | 否 | 系统预设信息 |
| model | string | 否 | 使用的模型，优先级高于options中的model |

**响应结果**：

流式响应，每个响应块为一个JSON对象，格式如下：

```json
{"text":"你","model":"gpt-3.5-turbo","modelName":"GPT-3.5","chatId":12345,"answer":"你","errMsg":"","modelAvatar":"https://example.com/avatar.png"}
```

```json
{"text":"你好","model":"gpt-3.5-turbo","modelName":"GPT-3.5","chatId":12345,"answer":"你好","errMsg":"","modelAvatar":"https://example.com/avatar.png"}
```

最后一个响应块可能包含额外信息：

```json
{"text":"你好，我是ChatGPT，一个由OpenAI训练的大型语言模型。我可以回答问题、提供信息、进行对话等。有什么我可以帮助你的吗？","model":"gpt-3.5-turbo","modelName":"GPT-3.5","chatId":12345,"answer":"你好，我是ChatGPT，一个由OpenAI训练的大型语言模型。我可以回答问题、提供信息、进行对话等。有什么我可以帮助你的吗？","errMsg":"","modelAvatar":"https://example.com/avatar.png","status":2,"userBalance":{"model3Count":95,"model4Count":50,"drawMjCount":20}}
```

### 2. 聊天对话（同步响应）

**接口地址**：`/chatgpt/chat-sync`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：与流式响应接口相同

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "你好，我是ChatGPT，一个由OpenAI训练的大型语言模型。我可以回答问题、提供信息、进行对话等。有什么我可以帮助你的吗？",
  "message": "请求成功"
}
```

### 3. 直接使用消息数组进行对话生成

**接口地址**：`/chatgpt/direct-completion`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "messages": [
    { "role": "system", "content": "你是一个专业的SQL生成器" },
    { "role": "user", "content": "请生成一个创建用户表的SQL" }
  ],
  "model": "gpt-3.5-turbo",
  "temperature": 0.3,
  "stream": false
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| messages | array | 是 | 消息数组 |
| messages[].role | string | 是 | 消息角色，可选值：system, user, assistant |
| messages[].content | string | 是 | 消息内容 |
| model | string | 否 | 使用的模型，默认为gpt-3.5-turbo |
| temperature | number | 否 | 温度参数，控制随机性，默认0.3 |
| stream | boolean | 否 | 是否使用流式响应，默认false |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "CREATE TABLE users (\n  id INT AUTO_INCREMENT PRIMARY KEY,\n  username VARCHAR(50) NOT NULL UNIQUE,\n  password VARCHAR(255) NOT NULL,\n  email VARCHAR(100) NOT NULL UNIQUE,\n  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n);",
  "message": "请求成功"
}
```

### 4. 思维导图提示

**接口地址**：`/chatgpt/chat-mind`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：与流式响应接口相同，但会自动添加specialModel=MindMap

**响应结果**：流式响应，格式与聊天流式响应相同

### 5. 语音播报

**接口地址**：`/chatgpt/tts-process`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "prompt": "这是一段需要转换为语音的文本"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| prompt | string | 是 | 需要转换为语音的文本 |

**响应结果**：二进制音频数据流

## 对话组管理接口

### 1. 创建对话组

**接口地址**：`/group/create`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "title": "新对话"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| title | string | 是 | 对话组标题 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": {
    "id": 123,
    "title": "新对话",
    "userId": 100,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  "message": "请求成功"
}
```

### 2. 查询对话组

**接口地址**：`/group/query`

**请求方式**：GET

**请求头**：

```
Authorization: Bearer {token}
```

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": [
    {
      "id": 123,
      "title": "对话1",
      "userId": 100,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": 124,
      "title": "对话2",
      "userId": 100,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "message": "请求成功"
}
```
