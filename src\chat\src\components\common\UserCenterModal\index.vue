<template>
  <NModal
    v-model:show="show"
    :mask-closable="false"
    :auto-focus="false"
    class="user-center-modal"
    style="width: 90vw; max-width: 1000px;"
  >
    <div class="user-center-container">
      <div class="modal-header">
        <div class="logo-container">
          <div class="logo-icon-wrapper">
            <span class="logo-icon">👤</span>
          </div>
          <div class="logo-text-wrapper">
            <span class="logo-text">个人中心</span>
            <span class="logo-subtitle">管理您的账户和作品</span>
          </div>
        </div>
        <button class="close-button" @click="closeModal">
          <SvgIcon icon="ri:close-line" class="text-gray-600 dark:text-gray-300" />
        </button>
      </div>

      <div class="modal-content">
        <div class="sidebar">
          <!-- 用户信息区域 -->
          <div class="user-info">
            <NAvatar
              :size="80"
              :src="avatar"
              :fallback-src="defaultAvatar"
              class="user-avatar"
            />
            <div class="username">{{ username }}</div>
          </div>

          <!-- 菜单区域 -->
          <div class="menu-list">
            <div
              v-for="item in menuItems"
              :key="item.key"
              class="menu-item"
              :class="{ 'active': activeTab === item.key }"
              @click="switchTab(item.key)"
            >
              <SvgIcon :icon="item.icon" class="menu-icon" />
              <span class="menu-label">{{ item.label }}</span>
            </div>
          </div>

          <!-- 底部区域 -->
          <div class="sidebar-footer">
            <NButton
              block
              type="error"
              @click="logOut"
              class="logout-button"
            >
              <template #icon>
                <SvgIcon icon="ri:logout-box-line" />
              </template>
              退出登录
            </NButton>
          </div>
        </div>

        <div class="content-area">
          <!-- 根据当前激活的标签页显示对应的组件 -->
          <PersonalInfo v-if="activeTab === 'personal-info'" />
          <PointsManagement v-else-if="activeTab === 'points'" />
          <Notifications v-else-if="activeTab === 'notifications'" />
        </div>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { NModal, NAvatar, NButton, useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import { SvgIcon } from '@/components/common';
import { useAuthStore } from '@/store';
import defaultAvatar from '@/assets/avatar.png';
import PersonalInfo from '@/views/userCenter/components/PersonalInfo.vue';
import PointsManagement from '@/views/userCenter/components/PointsManagement.vue';
import Notifications from '@/views/userCenter/components/Notifications.vue';

interface Props {
  visible: boolean;
}

interface Emit {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const router = useRouter();
const authStore = useAuthStore();
const ms = useMessage();

// 使用计算属性来处理双向绑定
const show = computed({
  get() {
    return props.visible;
  },
  set(visible: boolean) {
    emit('update:visible', visible);
  },
});

// 当前激活的标签页
const activeTab = ref('personal-info');

// 用户信息
const avatar = computed(() => authStore.userInfo.avatar ?? defaultAvatar);
const username = computed(() => authStore.userInfo.username ?? '未登录');
const userBalance = computed(() => authStore.userBalance);

// 侧边栏菜单项
const menuItems = [
  {
    key: 'personal-info',
    label: '个人信息',
    icon: 'ri:user-3-line'
  },
  {
    key: 'points',
    label: '积分/订阅',
    icon: 'ri:coin-line'
  },
  {
    key: 'notifications',
    label: '通知与消息',
    icon: 'ri:notification-3-line'
  }
];

// 切换标签页
function switchTab(tab: string) {
  activeTab.value = tab;
}

// 关闭弹窗
function closeModal() {
  show.value = false;
}

// 退出登录
function logOut() {
  authStore.logOut();
  closeModal();
  router.replace('/');
}
</script>

<style scoped>
.user-center-modal :deep(.n-modal) {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-center-modal :deep(.n-modal-body) {
  padding: 0;
}

.user-center-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
  max-height: 700px;
  background-color: #f8fafc;
  border-radius: 1rem;
  overflow: hidden;
}

.dark .user-center-container {
  background-color: #1e293b;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
}

.dark .modal-header {
  border-bottom: 1px solid #334155;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-icon-wrapper {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
  transform: rotate(-5deg);
  transition: all 0.3s ease;
}

.logo-icon {
  font-size: 1.75rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text-wrapper {
  display: flex;
  flex-direction: column;
}

.logo-text {
  font-size: 1.35rem;
  font-weight: 800;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.logo-subtitle {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.dark .logo-text {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .logo-subtitle {
  color: #94a3b8;
}

.close-button {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  background-color: rgba(241, 245, 249, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background-color: rgba(226, 232, 240, 0.9);
  transform: translateY(-2px);
}

.dark .close-button {
  background-color: rgba(30, 41, 59, 0.8);
  border-color: rgba(51, 65, 85, 0.8);
}

.dark .close-button:hover {
  background-color: rgba(51, 65, 85, 0.9);
}

.modal-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.dark .sidebar {
  border-right: 1px solid #334155;
  background-color: #1e293b;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .user-info {
  border-bottom: 1px solid #334155;
}

.user-avatar {
  border: 2px solid white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .user-avatar {
  border-color: #334155;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.username {
  margin-top: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .username {
  color: #e2e8f0;
}

.menu-list {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: rgba(241, 245, 249, 0.8);
  transform: translateY(-2px);
}

.menu-item.active {
  background-color: rgba(224, 242, 254, 0.8);
  border: 1px solid rgba(186, 230, 253, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.dark .menu-item:hover {
  background-color: rgba(51, 65, 85, 0.5);
}

.dark .menu-item.active {
  background-color: rgba(15, 23, 42, 0.8);
  border-color: rgba(30, 41, 59, 0.8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.menu-icon {
  font-size: 1.25rem;
  color: #3b82f6;
}

.dark .menu-icon {
  color: #60a5fa;
}

.menu-label {
  font-size: 0.95rem;
  font-weight: 500;
  color: #1e293b;
}

.dark .menu-label {
  color: #e2e8f0;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
}

.dark .sidebar-footer {
  border-top: 1px solid #334155;
}

.logout-button {
  transition: all 0.3s ease;
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* 自定义滚动条 */
.content-area::-webkit-scrollbar,
.menu-list::-webkit-scrollbar {
  width: 6px;
}

.content-area::-webkit-scrollbar-thumb,
.menu-list::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.content-area::-webkit-scrollbar-thumb:hover,
.menu-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

.content-area::-webkit-scrollbar-track,
.menu-list::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>
