<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NButton, NSpin } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import BookReaderComponent from '@/components/BookReader/index.vue';
import AIMagicPen from './components/AIMagicPen.vue';

// 响应式布局
const { isMobile } = useBasicLayout();
const router = useRouter();
const route = useRoute();

// 状态
const isLoading = ref(true);
const bookData = ref({
  id: 0,
  title: '',
  pages: []
});
const isLandscape = ref(true); // 默认使用横版模式

// 创建项目数据对象，用于保存AI魔笔数据
const projectData = ref({
  id: Date.now(),
  title: '阅读笔记',
  description: '',
  notes: '',
  // AI魔笔相关数据
  aiMagicPen: {
    inspirations: [], // 保存的灵感
    outlines: [],     // 构思的大纲
    questions: []     // 提问历史
  }
});

// 从 localStorage 中获取书籍数据
onMounted(async () => {
  try {
    // 尝试从 localStorage 中获取书籍数据
    const bookDataStr = localStorage.getItem('current-book-data');

    if (!bookDataStr) {
      window.$message?.error('未找到书籍数据');
      goBack();
      return;
    }

    // 解析书籍数据
    const parsedBookData = JSON.parse(bookDataStr);

    if (!parsedBookData || !parsedBookData.pages || !Array.isArray(parsedBookData.pages)) {
      window.$message?.error('书籍数据格式不正确');
      goBack();
      return;
    }

    // 设置书籍数据
    bookData.value = parsedBookData;

    // 设置项目数据的标题
    projectData.value.title = `阅读笔记: ${parsedBookData.title || '未命名绘本'}`;

    // 尝试从localStorage加载之前保存的AI魔笔数据
    try {
      const savedProjectData = localStorage.getItem(`reading-notes-${parsedBookData.id}`);
      if (savedProjectData) {
        const parsedProjectData = JSON.parse(savedProjectData);
        if (parsedProjectData && parsedProjectData.aiMagicPen) {
          // 合并AI魔笔数据
          projectData.value.aiMagicPen = parsedProjectData.aiMagicPen;
          console.log('Loaded saved AI magic pen data');
        }
      }
    } catch (e) {
      console.warn('Failed to load saved AI magic pen data', e);
    }

    isLoading.value = false;

  } catch (error) {
    console.error('加载书籍数据失败:', error);
    window.$message?.error('加载书籍数据失败');
    goBack();
  }
});

// 处理页面翻转
const handlePageFlip = (pageNumber: number) => {
  console.log('当前页面:', pageNumber + 1);
};

// 切换横竖版模式
const toggleOrientation = () => {
  isLandscape.value = !isLandscape.value;
};

// 返回上一页
const goBack = () => {
  // 清除 localStorage 中的数据
  localStorage.removeItem('current-book-data');
  router.back();
};

// 监听projectData变化，自动保存到localStorage
watch(
  () => projectData.value.aiMagicPen,
  () => {
    if (bookData.value && bookData.value.id) {
      try {
        localStorage.setItem(`reading-notes-${bookData.value.id}`, JSON.stringify(projectData.value));
        console.log('Saved AI magic pen data');
      } catch (e) {
        console.warn('Failed to save AI magic pen data', e);
      }
    }
  },
  { deep: true }
);

// 组件卸载时清除数据
onUnmounted(() => {
  localStorage.removeItem('current-book-data');

  // 保存最终的AI魔笔数据
  if (bookData.value && bookData.value.id) {
    try {
      localStorage.setItem(`reading-notes-${bookData.value.id}`, JSON.stringify(projectData.value));
      console.log('Saved final AI magic pen data');
    } catch (e) {
      console.warn('Failed to save final AI magic pen data', e);
    }
  }
});
</script>

<template>
  <div class="book-reader-page">
    <div class="reader-header">
      <NButton @click="goBack" class="back-button">
        <SvgIcon name="ri:arrow-left-line" size="18" />
        返回
      </NButton>

      <div class="reader-controls">
        <NButton size="small" @click="toggleOrientation" class="control-button">
          <SvgIcon :name="isLandscape ? 'ri:smartphone-line' : 'ri:tablet-line'" size="16" />
          {{ isLandscape ? '切换竖版' : '切换横版' }}
        </NButton>
      </div>
    </div>

    <div class="reader-container">
      <NSpin v-if="isLoading" size="large" />

      <div v-else class="book-reader-wrapper">
        <BookReaderComponent
          :pages="bookData.pages"
          :width="isMobile ? 320 : (isLandscape ? 800 : 600)"
          :height="isMobile ? 420 : (isLandscape ? 600 : 800)"
          :show-cover="true"
          :landscape="isLandscape"
          @page-flip="handlePageFlip"
          @close="goBack"
        />
      </div>
    </div>

    <!-- AI魔笔 -->
    <AIMagicPen :project-data="projectData" />
  </div>
</template>

<style scoped>
.book-reader-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  padding: 1rem;
}

.dark .book-reader-page {
  background-color: #0f172a;
}

.reader-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .reader-header {
  border-bottom-color: #334155;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reader-controls {
  display: flex;
  gap: 0.5rem;
}

.reader-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.book-reader-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .book-reader-page {
    padding: 0.5rem;
  }

  .reader-header {
    margin-bottom: 0.5rem;
  }
}
</style>
