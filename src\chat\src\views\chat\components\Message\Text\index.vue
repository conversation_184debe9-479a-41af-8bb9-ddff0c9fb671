<script lang="ts" setup>
import { fetchTtsAPIProces, fetchCreateHtmlShare } from '@/api';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { useAuthStore } from '@/store';
import {
  ArrowRight,
  Close,
  Copy,
  Delete,
  Down,
  Edit,
  LoadingOne,
  PauseOne,
  Refresh,
  Rotation,
  Send,
  Up,
  VoiceMessage,
} from '@icon-park/vue-next';
import mdKatex from '@traptitech/markdown-it-katex';
import hljs from 'highlight.js';
import MarkdownIt from 'markdown-it';
import mila from 'markdown-it-link-attributes';
import { NImage } from 'naive-ui';
import { computed, inject, nextTick, onMounted, ref, watch } from 'vue';
import HtmlModal from './htmlModal.vue';
import CodeEditorBlock from './CodeEditorBlock.vue';

interface Props {
  chatId?: number;
  index: number;
  inversion?: boolean;
  text: string;
  modelType?: number;
  status?: number;
  loading?: boolean;
  asRawText?: boolean;
  fileInfo?: string;
  ttsUrl?: string;
  model?: string;
  promptReference?: string;
  isLast?: boolean;
}

interface Emit {
  (ev: 'regenerate'): void;
  (ev: 'delete'): void;
  (ev: 'copy'): void;
}

interface TtsResponse {
  ttsUrl: string;
}

const authStore = useAuthStore();
const { isMobile } = useBasicLayout();
const onConversation = inject<any>('onConversation');
const handleRegenerate = inject<any>('handleRegenerate');
const updateCurrentCode = inject<(code: string) => void>('updateCurrentCode', () => {});
// 正确地注入函数，并提供默认值
// 注意：这里的默认值必须是函数，而不是数字或其他类型
const clearConversation = inject<() => void>('clearConversation', () => console.log('clearConversation not provided'));
const setActiveAppId = inject<(id: number) => void>('setActiveAppId', () => console.log('setActiveAppId not provided'));
const activeAppId = inject<any>('activeAppId', 0); // 注入当前活动的应用ID

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const modalVisible = ref(false);
const editableText = ref(props.text);
const showThinking = ref(true);
const textRef = ref<HTMLElement>();
const mainContentRef = ref<HTMLElement>();
const codeEditorRef = ref<InstanceType<typeof CodeEditorBlock> | null>(null);
const localTtsUrl = ref(props.ttsUrl);
const htmlContent = ref<string>('');
const isHtml = ref(false);
const playbackState = ref('paused');
const editableContent = ref(props.text);
const isEditable = ref(false);
const textarea = ref<HTMLTextAreaElement | null>(null);
const useCodeEditor = ref(false); // 是否使用代码编辑器
const isSharing = ref(false);
const enableTracking = ref(false);

let currentAudio: HTMLAudioElement | null = null;

const isHideTts = computed(
  () => Number(authStore.globalConfig?.isHideTts) === 1
);

const buttonGroupClass = computed(() => {
  return playbackState.value !== 'paused' || isEditable.value
    ? 'opacity-100'
    : 'opacity-0 group-hover:opacity-100';
});

const handlePlay = async () => {
  if (playbackState.value === 'loading' || playbackState.value === 'playing')
    return;
  if (localTtsUrl.value) {
    playAudio(localTtsUrl.value);
    return;
  }

  playbackState.value = 'loading';
  try {
    if (!props.chatId) return;

    const res = (await fetchTtsAPIProces({
      chatId: props.chatId,
      prompt: props.text,
    })) as TtsResponse;

    const ttsUrl = res.ttsUrl;
    if (ttsUrl) {
      localTtsUrl.value = ttsUrl;
      playAudio(ttsUrl);
    } else {
      throw new Error('TTS URL is undefined');
    }
  } catch (error) {
    playbackState.value = 'paused';
  }
};

function playAudio(audioSrc: string | undefined) {
  if (currentAudio) {
    currentAudio.pause();
  }
  currentAudio = new Audio(audioSrc);
  currentAudio
    .play()
    .then(() => {
      playbackState.value = 'playing';
    })
    .catch((error) => {
      playbackState.value = 'paused';
    });

  currentAudio.onended = () => {
    playbackState.value = 'paused';
    currentAudio = null;
  };
}

function pauseAudio() {
  if (currentAudio) {
    currentAudio.pause();
    playbackState.value = 'paused';
  }
}

function playOrPause() {
  if (playbackState.value === 'playing') {
    pauseAudio();
  } else {
    handlePlay();
  }
}

const mdi = new MarkdownIt({
  linkify: true,
  html: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? '';
      if (language === 'html') {
        htmlContent.value = code;
        isHtml.value = true;
        useCodeEditor.value = true; // 启用代码编辑器
        // 更新代码预览内容
        checkForHtmlContent(code, language);
        // 对于HTML代码，返回一个特殊标记，用于定位代码编辑器
        return '<div id="code-editor-placeholder" class="code-editor-position-marker"></div>';
      }
      return highlightBlock(
        hljs.highlight(code, { language: lang }).value,
        lang
      );
    }

    return highlightBlock(hljs.highlightAuto(code).value, '');
  },
});

// 检查代码块中是否包含HTML内容，如果是，则更新代码预览
const checkForHtmlContent = (code: string, language: string) => {
  if (language === 'html') {
    // 更新代码预览内容
    updateCurrentCode(code);
  }
};

// 处理代码更新
const handleCodeUpdate = (newCode: string) => {
  try {
    console.log('代码更新:', newCode.substring(0, 100) + '...');
    htmlContent.value = newCode;
    // 使用nextTick确保状态更新后再通知父组件
    nextTick(() => {
      updateCurrentCode(newCode);
    });
  } catch (error) {
    console.error('处理代码更新时出错:', error);
  }
};

mdi.renderer.rules.image = function (tokens, idx, options, env, self) {
  const token = tokens[idx];
  const src = token.attrGet('src');
  const title = token.attrGet('title');
  const alt = token.content;

  if (isMobile.value) {
    return `<img src="${src}" alt="${alt}" title="${
      title || alt
    }" class="rounded-md"
      style=" max-width:100% "
      onclick="(function() {
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = 0;
        modal.style.left = 0;
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.background = 'rgba(0, 0, 0, 1)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = 1000;
        modal.style.overflow = 'hidden';

        modal.onclick = (event) => {
            document.body.removeChild(modal);
        };

        const img = document.createElement('img');
        img.src = '${src}';
        img.style.maxWidth = '100%';
        img.style.maxHeight = '95%';
        img.style.transform = 'scale(1) translate(0px, 0px)';
        img.style.transition = 'transform 0.2s';

        let scale = 1;
        let posX = 0, posY = 0;
        let isDragging = false;
        let startX, startY;

        let initialDistance = null;
        modal.addEventListener('touchmove', (event) => {
          if (event.touches.length === 2) {
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            const currentDistance = Math.sqrt(
              (touch2.clientX - touch1.clientX) ** 2 +
              (touch2.clientY - touch1.clientY) ** 2
            );

            if (initialDistance === null) {
              initialDistance = currentDistance;
            } else {
              const scaleChange = currentDistance / initialDistance;
              scale = Math.max(0.5, Math.min(scale * scaleChange, 3));
              img.style.transform = \`scale(\${scale}) translate(\${posX}px, \${posY}px)\`;
              initialDistance = currentDistance;
            }
          }
        });

        modal.addEventListener('touchend', () => {
          initialDistance = null;
        });

        modal.appendChild(img);
        document.body.appendChild(modal);
      })()"
  />`;
  } else {
    return `<img src="${src}" alt="${alt}" title="${
      title || alt
    }" class="rounded-md"
      style="max-width:100% ;max-height: 30vh;"
      onclick="(function() {
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = 0;
        modal.style.left = 0;
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.background = 'rgba(0, 0, 0, 0.5)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = 1000;
        modal.style.overflow = 'hidden';

        modal.onclick = (event) => {
          if (event.target === modal) {
            document.body.removeChild(modal);
          }
        };

        const img = document.createElement('img');
        img.src = '${src}';
        img.style.maxWidth = '95%';
        img.style.maxHeight = '95%';
        img.style.borderRadius = '8px';
        img.style.transform = 'scale(1) translate(0px, 0px)';
        img.style.transition = 'transform 0.2s';

        let scale = 1;
        let posX = 0, posY = 0;
        let isDragging = false;
        let startX, startY;

        modal.addEventListener('wheel', (event) => {
          event.preventDefault();
          const zoomFactor = 0.1;
          if (event.deltaY < 0) {
            scale = Math.min(scale + zoomFactor, 3);
          } else {
            scale = Math.max(scale - zoomFactor, 0.5);
          }
          img.style.transform = \`scale(\${scale}) translate(\${posX}px, \${posY}px)\`;
        });

        let initialDistance = null;
        modal.addEventListener('touchmove', (event) => {
          if (event.touches.length === 2) {
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            const currentDistance = Math.sqrt(
              (touch2.clientX - touch1.clientX) ** 2 +
              (touch2.clientY - touch1.clientY) ** 2
            );

            if (initialDistance === null) {
              initialDistance = currentDistance;
            } else {
              const scaleChange = currentDistance / initialDistance;
              scale = Math.max(0.5, Math.min(scale * scaleChange, 3));
              img.style.transform = \`scale(\${scale}) translate(\${posX}px, \${posY}px)\`;
              initialDistance = currentDistance;
            }
          }
        });

        modal.addEventListener('touchend', () => {
          initialDistance = null;
        });

        const close = document.createElement('span');
        close.innerText = '×';
        close.style.position = 'absolute';
        close.style.top = '24px';
        close.style.right = '24px';
        close.style.color = 'white';
        close.style.fontSize = '1.5rem';
        close.style.cursor = 'pointer';
        close.onclick = () => document.body.removeChild(modal);

        modal.appendChild(img);
        modal.appendChild(close);
        document.body.appendChild(modal);
      })()"
  />`;
  }
};

const fileInfo = computed(() => props.fileInfo);
const fileInfoArray = computed(() =>
  fileInfo?.value?.split(',').map((file) => file.trim())
);

const isVideoUrl = computed(() => {
  if (!fileInfo.value) return false;
  return /\.(mp4|avi|mov|wmv|flv)$/i.test(fileInfo.value);
});

const isImageUrl = computed(() => {
  if (!fileInfo.value) return false;
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(fileInfo.value);
});

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } });
mdi.use(mdKatex, {
  blockClass: 'katexmath-block p-0 flex h-full items-center justify-start',
  inlineClass: 'katexmath-inline',
  errorColor: ' #cc0000',
});

const mainContent = computed(() => {
  let value = props.text || '';

  if (value.startsWith('<think>')) {
    const endThinkTagIndex = value.indexOf('</think>');
    if (endThinkTagIndex !== -1) {
      value =
        value.slice(0, value.indexOf('<think>')) +
        value.slice(endThinkTagIndex + 8);
    } else {
      value = '';
    }
  }

  let modifiedValue = value
    .replace(/\\\(\s*/g, '$')
    .replace(/\s*\\\)/g, '$')
    .replace(/\\\[\s*/g, '$$')
    .replace(/\s*\\\]/g, '$$')
    .replace(
      /\[\[(\d+)\]\((https?:\/\/[^\)]+)\)\]/g,
      '<button class="bg-gray-500 text-white rounded-full w-4 h-4 mx-1 flex justify-center items-center text-sm hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 inline-flex" onclick="window.open(\'$2\', \'_blank\')">$1</button>'
    );

  if (!props.asRawText) {
    // 渲染Markdown内容
    let renderedContent = mdi.render(modifiedValue);

    // 不再移除占位符，而是保留它以便在正确的位置插入代码编辑器
    // 移除原来的代码块样式，使占位符更干净
    if (useCodeEditor.value && isHtml.value) {
      renderedContent = renderedContent.replace(/<pre[\s\S]*?<div id="code-editor-placeholder"[\s\S]*?<\/div>[\s\S]*?<\/pre>/g,
        '<div id="code-editor-placeholder" class="code-editor-position-marker"></div>');
    }

    return renderedContent;
  }

  return modifiedValue;
});

const thinkingContent = computed<string>(() => {
  let content = '';

  if (props.text?.includes('<think>') && !props.text.includes('</think>')) {
    content = props.text.replace(/<think>/g, '').trim();
  } else {
    const match = props.text?.match(/<think>([\s\S]*?)<\/think>/);
    if (match) {
      content = match[1];
    }
  }

  const modifiedContent = content
    .replace(/\\\(\s*/g, '$')
    .replace(/\s*\\\)/g, '$')
    .replace(/\\\[\s*/g, '$$')
    .replace(/\s*\\\]/g, '$$');

  if (!props.asRawText) {
    return mdi.render(modifiedContent);
  }

  return modifiedContent;
});

function highlightBlock(str: string, lang?: string) {
  return `<pre
    style=" max-width:100%; background-color: #212121; "
    class="code-block-wrapper"
  ><div class="code-block-header "><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t(
    'chat.copyCode'
  )}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

async function handleEdit() {
  editableText.value = props.text;
  modalVisible.value = true;
}

function closeModal() {
  modalVisible.value = false;
}

async function handleEditMessage() {
  if (isEditable.value) {
    const tempEditableContent = editableContent.value;
    await onConversation({
      msg: tempEditableContent,
      chatId: props.chatId,
    });

    isEditable.value = false;
  } else {
    editableContent.value = props.text;
    isEditable.value = true;
    await nextTick();
    adjustTextareaHeight();
  }
}

async function handleMessage(item: string) {
  await onConversation({
    msg: item,
  });
}

function handleCopy() {
  emit('copy');
}

function handleDelete() {
  emit('delete');
}

const cancelEdit = () => {
  isEditable.value = false;
  editableContent.value = props.text;
};

const adjustTextareaHeight = () => {
  if (textarea.value) {
    textarea.value.style.height = 'auto';
    textarea.value.style.height = `${textarea.value.scrollHeight}px`;
  }
};

// 处理“重新填写表单”按钮的点击事件
const handleRefillForm = () => {
  // 清空当前对话内容，显示表单
  if (activeAppId) {
    // 使用已经注入的函数
    clearConversation();

    // 重新加载应用表单
    setActiveAppId(activeAppId);
  }
};

// 直接分享 HTML 内容
const handleDirectShare = async () => {
  if (!htmlContent.value) {
    window.$message?.error('HTML内容不能为空');
    return;
  }

  try {
    isSharing.value = true;
    const response = await fetchCreateHtmlShare({
      htmlContent: htmlContent.value
    });

    console.log('分享响应数据:', response);
    // 处理不同的响应数据结构
    const shareCodeValue = response.data?.data?.data?.shareCode || response.data?.data?.shareCode || response.data?.shareCode;

    if (shareCodeValue) {
      // 判断是否使用哈希模式
      const isHashMode = window.location.href.includes('#/');
      const shareUrl = isHashMode
        ? `${window.location.origin}/#/share/${shareCodeValue}`
        : `${window.location.origin}/share/${shareCodeValue}`;
      await navigator.clipboard.writeText(shareUrl);
      window.$message?.success('分享链接已复制到剪贴板');


    } else {
      window.$message?.error('创建分享失败');
    }
  } catch (err) {
    console.error('分享失败:', err);
    window.$message?.error('分享失败');
  } finally {
    isSharing.value = false;
  }
};

defineExpose({ textRef });

// 将代码编辑器插入到正确的位置
const insertCodeEditorAtPlaceholder = () => {
  if (!mainContentRef.value || !codeEditorRef.value || !useCodeEditor.value || !isHtml.value) return;

  // 查找占位符元素
  const placeholder = mainContentRef.value.querySelector('#code-editor-placeholder');
  if (!placeholder) {
    console.log('未找到代码编辑器占位符，可能是DOM结构变化');
    return;
  }

  // 获取代码编辑器元素
  const codeEditorElement = codeEditorRef.value.$el;
  if (!codeEditorElement) {
    console.log('未找到代码编辑器元素');
    return;
  }

  try {
    // 将代码编辑器元素从隐藏容器中移除并插入到占位符位置
    codeEditorElement.classList.remove('hidden');
    placeholder.parentNode?.replaceChild(codeEditorElement, placeholder);

    // 使用Vue的nextTick确保DOM更新后再滚动
    nextTick(() => {
      // 使用原生方法滚动到底部
      if (codeEditorElement) {
        const editorContent = codeEditorElement.querySelector('.cm-content');
        if (editorContent) {
          editorContent.scrollTop = editorContent.scrollHeight;
        }
      }
    });
  } catch (error) {
    console.error('插入代码编辑器时出错:', error);
  }
};

// 监听主内容变化
watch(() => mainContent.value, () => {
  // 使用nextTick确保DOM已更新后再插入编辑器
  nextTick(() => {
    // 给DOM一些时间完全渲染
    setTimeout(() => {
      insertCodeEditorAtPlaceholder();
    }, 50);
  });
});

onMounted(() => {
  // 在组件挂载后尝试插入代码编辑器
  nextTick(() => {
    insertCodeEditorAtPlaceholder();
  });

  const uniqueId = document.querySelector('.preview-button')?.id;
  const previewButton = uniqueId ? document.getElementById(uniqueId) : null;
  if (previewButton) {
    previewButton.addEventListener('click', handleEdit);
  }
});
</script>

<template>
  <div class="flex flex-col group w-full">
    <div class="text-wrap rounded-lg min-w-12 flex w-full flex-col">
      <div
        v-if="thinkingContent && !inversion"
        @click="showThinking = !showThinking"
        class="text-gray-600 flex items-center mb-2 text-base hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer transition-all duration-300 px-3 py-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <span class="font-medium">{{ mainContent || !loading ? '已深度思考' : '深度思考中' }}</span>
        <LoadingOne
          v-if="!mainContent && loading"
          class="rotate-icon flex mx-1"
        />
        <Down v-if="!showThinking" size="20" class="mr-1 flex transition-transform duration-300" />
        <Up v-else size="20" class="mr-1 flex transition-transform duration-300" />
      </div>
      <div
        v-else-if="loading && thinkingContent && !inversion"
        class="text-gray-600 flex items-center mb-2 text-base hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300 cursor-pointer transition-all duration-300 px-3 py-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <span class="font-medium">深度思考</span>
        <LoadingOne class="rotate-icon flex mx-1" />
      </div>

      <div ref="textRef" class="flex w-full px-3">
        <div
          v-if="!inversion"
          class="flex flex-col items-start transition-all duration-300"
          style="max-width: 100%"
        >
          <div class="w-full">
            <span
              v-if="loading && !text"
              class="inline-block w-3.5 h-3.5 ml-0.5 align-middle rounded-full animate-breathe dark:bg-gray-100 bg-gray-950"
            ></span>
            <div
              v-if="thinkingContent && showThinking"
              :class="[
                'markdown-body text-gray-600 dark:text-gray-400 pl-5 mt-2 mb-4 border-l-2 border-gray-300 dark:border-gray-600 overflow-hidden transition-all duration-500 ease-in-out rounded-lg bg-gray-50 dark:bg-gray-800/50 p-4 ds-shadow-sm',
                {
                  'markdown-body-generate': loading && !mainContent,
                },
              ]"
              v-html="thinkingContent"
            ></div>
            <div
              :class="[
                'markdown-body text-gray-950 dark:text-gray-100 transition-all duration-300 rounded-lg',
                { 'markdown-body-generate': loading || !mainContent },
              ]"
              v-html="mainContent"
              ref="mainContentRef"
            ></div>

            <!-- 代码编辑器组件 - 将在挂载后通过JS插入到正确位置 -->
            <div v-if="useCodeEditor && isHtml && !inversion" class="hidden">
              <CodeEditorBlock
                :code="htmlContent"
                @update:code="handleCodeUpdate"
                ref="codeEditorRef"
              />
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex justify-end w-full"
          :class="[isMobile ? 'pl-20' : 'pl-28 ']"
          style="max-width: 100%"
        >
          <div
            v-if="isEditable"
            class="p-4 rounded-2xl w-full bg-primary-50 dark:bg-gray-750 break-words shadow-sm border border-primary-100 dark:border-gray-700 transition-all duration-300"
            style="max-width: 100%"
          >
            <textarea
              v-model="editableContent"
              class="min-w-full text-base resize-none overflow-hidden bg-transparent whitespace-pre-wrap text-gray-950 dark:text-gray-100 focus:outline-none"
              @input="adjustTextareaHeight"
              @keydown.enter="handleEditMessage"
              ref="textarea"
            ></textarea>
          </div>
          <div
            v-else
            class="p-4 rounded-2xl text-base bg-gradient-to-r from-primary-50 to-white dark:from-gray-750 dark:to-gray-800 break-words whitespace-pre-wrap text-gray-950 dark:text-gray-100 ds-shadow-md border border-primary-100 dark:border-gray-700 transition-all duration-300 hover-float"
            v-text="text"
            style="max-width: 100%"
          />
        </div>
      </div>

      <div
        v-if="fileInfo && !isImageUrl && !isVideoUrl"
        class="my-1 flex w-full"
        :class="[{ 'justify-end': inversion }]"
      >
        <div
          class="flex p-3 items-center justify-center ring-1 ring-inset ring-gray-200 dark:ring-gray-700 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700"
        >
          <span>{{ t('chat.fileAnalysis') }} </span>
        </div>
      </div>

      <div
        v-if="fileInfoArray && isImageUrl"
        class="my-2 w-full flex"
        :class="[isMobile ? 'pl-20' : 'pl-28', { 'justify-end': inversion }]"
        :style="{
          maxHeight: isMobile ? '' : '30vh',
          maxWidth: isMobile ? '100%' : '100%',
        }"
      >
        <NImage
          v-for="(file, index) in fileInfoArray"
          :key="index"
          :src="file"
          :preview-src="file"
          alt="图片"
          class="rounded-md flex ml-2"
          :class="[{ 'justify-end': inversion }]"
          :style="{
            maxHeight: '100%',
            height: 'auto',
            objectFit: 'contain',
          }"
        />
      </div>
    </div>

    <!-- 代码编辑器区域已移到主内容区域内部 -->

    <!-- 旧的预览按钮，当不使用代码编辑器时显示 -->
    <div v-if="isHtml && !inversion && !useCodeEditor" class="flex-none mt-3 mb-2">
      <div class="flex justify-start">
        <button
          @click="handleEdit"
          class="px-4 py-2 ds-shadow-md bg-white hover:bg-gray-50 text-gray-900 rounded-lg mr-4 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700 transition-all duration-300 hover-float flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
          </svg>
          预览代码
        </button>
        <button
          @click="handleDirectShare"
          :disabled="isSharing"
          class="px-4 py-2 ds-shadow-md bg-primary-600 hover:bg-primary-500 text-white rounded-lg mr-4 dark:bg-primary-700 dark:hover:bg-primary-600 border border-primary-600 dark:border-primary-700 transition-all duration-300 hover-float flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
          </svg>
          {{ isSharing ? '分享中...' : '分享代码' }}
        </button>

      </div>
    </div>

    <div
      v-if="promptReference && !inversion && isLast"
      class="flex-none transition-opacity duration-500"
    >
      <button
        v-for="(item, index) in promptReference
          ? promptReference
              .match(/{(.*?)}/g)
              ?.map((str: string | any[]) => str.slice(1, -1))
              .slice(0, 3)
          : []"
        :key="index"
        @click="handleMessage(item as string)"
        class="flex flex-row items-center my-3 px-3 py-2 ds-shadow-sm bg-opacity hover:bg-gray-50 text-left text-gray-900 rounded-full overflow-hidden dark:bg-gray-750 dark:text-gray-400 transition-all duration-300 hover-float"
      >
        {{ item }}
        <ArrowRight class="ml-1" />
      </button>
    </div>

    <div
      :class="[
        'flex transition-all duration-300 text-gray-700',
        buttonGroupClass,
        { 'justify-end': inversion },
      ]"
    >
      <div class="mt-2 flex">
        <button
          v-if="!isEditable"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleCopy"
        >
          <Copy class="flex mr-1" />
          <span class="flex text-sm">{{ t('chat.copy') }}</span>
        </button>

        <button
          v-if="!isEditable"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleDelete"
        >
          <Delete class="mr-1" />
          <span class="flex text-sm">{{ t('chat.delete') }} </span>
        </button>
        <button
          v-if="isEditable"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="cancelEdit"
        >
          <Close class="mr-1" />
          <span class="flex text-sm">取消</span>
        </button>
        <button
          v-if="isEditable"
          class="flex ml-0 items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-3 px-2 py-1 rounded-md hover:bg-primary-50 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleEditMessage"
        >
          <Send class="mr-1" />
          <span class="flex text-sm">提交</span>
        </button>
        <button
          v-if="inversion && !isEditable && modelType === 1"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleEditMessage"
        >
          <Edit class="mr-1" />
          <span class="flex text-sm">编辑</span>
        </button>
        <button
          v-if="!inversion && modelType === 1"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleRegenerate(index, chatId)"
        >
          <Refresh class="mr-1" />
          <span class="flex text-sm">重新生成</span>
        </button>
        <button
          v-if="!inversion && !isHideTts"
          class="flex ml-0 items-center text-gray-500 hover:text-gray-700 dark:text-gray-500 dark:hover:text-gray-300 mr-3 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="playOrPause"
        >
          <VoiceMessage v-if="playbackState === 'paused'" class="flex mr-1" />
          <Rotation
            v-if="playbackState === 'loading'"
            class="rotate-icon flex mr-1"
          />
          <PauseOne v-else-if="playbackState === 'playing'" class="flex mr-1" />
          <span class="flex text-sm">
            {{
              playbackState === 'playing'
                ? t('chat.pause')
                : playbackState === 'loading'
                ? t('chat.loading')
                : t('chat.readAloud')
            }}
          </span>
        </button>
        <!-- 重新填写表单按钮 -->
        <button
          v-if="!inversion && activeAppId"
          class="flex ml-0 items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-3 px-2 py-1 rounded-md hover:bg-primary-50 dark:hover:bg-gray-800 transition-all duration-300 hover-float"
          text
          @click="handleRefillForm"
        >
          <Refresh class="mr-1" />
          <span class="flex text-sm">重新填写表单</span>
        </button>
      </div>
    </div>
    <HtmlModal v-if="modalVisible" :text="htmlContent" :close="closeModal" />
  </div>
</template>

<style lang="less">
@import url(../style.less);

@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.rotate-icon {
  animation: rotateAnimation 3s linear infinite;
  transform-origin: center;
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    /* 原始尺寸 */
    opacity: 1;
    /* 完全不透明 */
  }

  50% {
    transform: scale(0.5);
    /* 缩小到50%的尺寸 */
    opacity: 0.5;
    /* 半透明 */
  }
}

.breathing-dot {
  display: inline-block;
  width: 10px;
  /* 圆点的基础宽度 */
  height: 10px;
  /* 圆点的基础高度 */
  border-radius: 50%;
  /* 使其成为圆形 */
  background-color: black;
  /* 圆点的颜色 */
  animation: breathe 2s infinite;
  /* 应用动画，无限循环 */
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
    /* 开始和结束时完全不透明 */
  }

  50% {
    transform: scale(0.75);
    /* 中间缩小到75% */
    opacity: 0.75;
    /* 中间透明度降低，增加平滑感 */
  }
}

.animate-breathe {
  animation: breathe 2s infinite ease-in-out;
}
</style>
