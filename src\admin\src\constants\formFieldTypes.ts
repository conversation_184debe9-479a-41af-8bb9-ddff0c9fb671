/**
 * 表单字段类型库
 * 定义了所有支持的表单字段类型及其默认配置
 */

export interface FormFieldType {
  type: string;
  label: string;
  defaultName: string;
  template: {
    type: string;
    name: string;
    label: string;
    required: boolean;
    placeholder?: string;
    [key: string]: any;
  };
}

const formFieldTypes: FormFieldType[] = [
  {
    type: 'input',
    label: '文本输入框',
    defaultName: 'textInput',
    template: {
      type: 'input',
      name: 'textInput',
      label: '文本输入框',
      required: false,
      placeholder: '请输入'
    }
  },
  {
    type: 'textarea',
    label: '多行文本框',
    defaultName: 'textArea',
    template: {
      type: 'textarea',
      name: 'textArea',
      label: '多行文本框',
      required: false,
      placeholder: '请输入',
      rows: 4
    }
  },
  {
    type: 'select',
    label: '下拉选择框',
    defaultName: 'selectOption',
    template: {
      type: 'select',
      name: 'selectOption',
      label: '下拉选择框',
      required: false,
      placeholder: '请选择',
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' },
        { label: '选项3', value: 'option3' }
      ]
    }
  },
  {
    type: 'radio',
    label: '单选按钮组',
    defaultName: 'radioOption',
    template: {
      type: 'radio',
      name: 'radioOption',
      label: '单选按钮组',
      required: false,
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' },
        { label: '选项3', value: 'option3' }
      ]
    }
  },
  {
    type: 'checkbox',
    label: '复选框组',
    defaultName: 'checkboxOptions',
    template: {
      type: 'checkbox',
      name: 'checkboxOptions',
      label: '复选框组',
      required: false,
      options: [
        { label: '选项1', value: 'option1' },
        { label: '选项2', value: 'option2' },
        { label: '选项3', value: 'option3' }
      ]
    }
  },
  {
    type: 'number',
    label: '数字输入框',
    defaultName: 'numberInput',
    template: {
      type: 'number',
      name: 'numberInput',
      label: '数字输入框',
      required: false,
      min: 0,
      max: 100,
      step: 1,
      placeholder: '请输入数字'
    }
  },
  {
    type: 'date',
    label: '日期选择器',
    defaultName: 'dateSelect',
    template: {
      type: 'date',
      name: 'dateSelect',
      label: '日期选择器',
      required: false,
      format: 'YYYY-MM-DD',
      placeholder: '请选择日期'
    }
  },
  {
    type: 'time',
    label: '时间选择器',
    defaultName: 'timeSelect',
    template: {
      type: 'time',
      name: 'timeSelect',
      label: '时间选择器',
      required: false,
      format: 'HH:mm:ss',
      placeholder: '请选择时间'
    }
  },
  {
    type: 'datetime',
    label: '日期时间选择器',
    defaultName: 'datetimeSelect',
    template: {
      type: 'datetime',
      name: 'datetimeSelect',
      label: '日期时间选择器',
      required: false,
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: '请选择日期时间'
    }
  },
  {
    type: 'slider',
    label: '滑块',
    defaultName: 'sliderValue',
    template: {
      type: 'slider',
      name: 'sliderValue',
      label: '滑块',
      required: false,
      min: 0,
      max: 100,
      step: 1
    }
  },
  {
    type: 'switch',
    label: '开关',
    defaultName: 'switchValue',
    template: {
      type: 'switch',
      name: 'switchValue',
      label: '开关',
      required: false,
      activeText: '是',
      inactiveText: '否'
    }
  },
  {
    type: 'rate',
    label: '评分',
    defaultName: 'rateValue',
    template: {
      type: 'rate',
      name: 'rateValue',
      label: '评分',
      required: false,
      max: 5
    }
  },
  {
    type: 'color',
    label: '颜色选择器',
    defaultName: 'colorValue',
    template: {
      type: 'color',
      name: 'colorValue',
      label: '颜色选择器',
      required: false
    }
  }
];

// 字段类型标签映射
export const fieldTypeLabels: Record<string, string> = {
  'input': '文本输入框',
  'textarea': '多行文本框',
  'number': '数字输入框',
  'select': '下拉选择框',
  'radio': '单选按钮组',
  'checkbox': '复选框组',
  'switch': '开关',
  'slider': '滑块',
  'date': '日期选择器',
  'time': '时间选择器',
  'datetime': '日期时间选择器',
  'rate': '评分',
  'color': '颜色选择器'
};

// 字段类型标签样式映射
export const fieldTypeTagTypes: Record<string, string> = {
  'input': 'primary',
  'textarea': 'info',
  'number': 'warning',
  'select': 'success',
  'radio': 'success',
  'checkbox': 'success',
  'switch': 'warning',
  'slider': 'warning',
  'date': 'info',
  'time': 'info',
  'datetime': 'info',
  'rate': 'danger',
  'color': 'danger'
};

// 日期时间类型的默认格式
export const defaultDateTimeFormats: Record<string, string> = {
  'date': 'YYYY-MM-DD',
  'time': 'HH:mm:ss',
  'datetime': 'YYYY-MM-DD HH:mm:ss'
};

export default formFieldTypes;
