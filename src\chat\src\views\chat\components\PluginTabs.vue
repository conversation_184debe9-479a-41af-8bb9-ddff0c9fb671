<script setup lang="ts">
import { SvgIcon } from '@/components/common';
import { useChatStore } from '@/store';
import { Switch } from '@headlessui/vue';
import { NScrollbar, NIcon, NButton } from 'naive-ui';
import { computed, onMounted, ref, watch } from 'vue';
import { SettingsOutline } from '@vicons/ionicons5';

const chatStore = useChatStore();
const customKeyId = ref(100);
const dataSources = computed(() => chatStore.groupList);
const groupKeyWord = computed(() => chatStore.groupKeyWord);

// 默认插件列表
const defaultPlugins: any[] = [];

// 计算属性插件列表
const pluginList = computed(() =>
  chatStore.pluginList?.length ? chatStore.pluginList : defaultPlugins
);

watch(dataSources, () => (customKeyId.value = customKeyId.value + 1));
watch(groupKeyWord, () => (customKeyId.value = customKeyId.value + 1));
const usingPlugin = computed(() => chatStore.currentPlugin);

function toggleSelection(plugin: any) {
  if (usingPlugin.value?.parameters === plugin.parameters) {
    // 清除当前插件，因为它已经被选中
    chatStore.setUsingPlugin(null);
  } else {
    // 设置当前插件为选中的插件
    chatStore.setUsingPlugin(plugin);
  }
}

onMounted(() => {
  chatStore.queryPlugins();
  chatStore.queryMyGroup();
});
</script>

<template>
  <div class="plugin-tabs h-full flex flex-col">
    <!-- 标签页头部 -->
    <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">插件管理</h3>
      <NButton
        secondary
        circle
        size="small"
        class="flex-shrink-0"
        title="插件设置"
      >
        <template #icon>
          <NIcon :component="SettingsOutline" />
        </template>
      </NButton>
    </div>

    <!-- 插件内容 -->
    <div class="flex-1 overflow-hidden">
      <NScrollbar class="h-full">
        <div class="p-4">
          <!-- 插件描述 -->
          <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="text-sm text-blue-800 dark:text-blue-200">
              <div class="font-medium mb-1">🔌 插件功能</div>
              <div class="text-xs text-blue-600 dark:text-blue-300">
                插件可以扩展AI的能力，为您的对话添加特殊功能。启用插件后，AI将获得相应的能力。
              </div>
            </div>
          </div>

          <!-- 插件列表 -->
          <div v-if="pluginList.length > 0" class="space-y-3">
            <div
              v-for="plugin in pluginList"
              :key="plugin.parameters"
              class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 flex-1">
                  <!-- 插件图标 -->
                  <div class="w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-600 flex items-center justify-center overflow-hidden shadow-sm border border-gray-200 dark:border-gray-500 flex-shrink-0">
                    <span
                      v-if="plugin.pluginImg && plugin.pluginImg.startsWith('emoji:')"
                      class="text-2xl"
                    >
                      {{ plugin.pluginImg.replace('emoji:', '') }}
                    </span>
                    <img
                      v-else-if="plugin.pluginImg"
                      :src="plugin.pluginImg"
                      alt="Plugin icon"
                      class="w-full h-full object-cover"
                    />
                    <span v-else class="text-lg font-medium text-gray-500 dark:text-gray-400">
                      {{ plugin.pluginName.charAt(0) }}
                    </span>
                  </div>

                  <!-- 插件信息 -->
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {{ plugin.pluginName }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                      {{ plugin.description }}
                    </div>
                  </div>
                </div>

                <!-- 开关控件 -->
                <div class="flex-shrink-0 ml-4">
                  <Switch
                    :class="[
                      'group relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                    ]"
                    @click.prevent="toggleSelection(plugin)"
                  >
                    <span class="sr-only">启用插件</span>
                    <span
                      aria-hidden="true"
                      :class="[
                        usingPlugin?.parameters === plugin.parameters
                          ? 'bg-blue-600'
                          : 'bg-gray-200 dark:bg-gray-700',
                        'pointer-events-none absolute mx-auto h-4 w-9 rounded-full transition-colors duration-200 ease-in-out',
                      ]"
                    ></span>
                    <span
                      aria-hidden="true"
                      :class="[
                        usingPlugin?.parameters === plugin.parameters
                          ? 'translate-x-5'
                          : 'translate-x-0',
                        'pointer-events-none absolute left-0 inline-block h-5 w-5 transform rounded-full border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-300 shadow ring-0 transition-transform duration-200 ease-in-out',
                      ]"
                    ></span>
                  </Switch>
                </div>
              </div>

              <!-- 启用状态提示 -->
              <div v-if="usingPlugin?.parameters === plugin.parameters" 
                   class="mt-3 px-3 py-2 bg-blue-50 dark:bg-blue-900/30 rounded-md border border-blue-200 dark:border-blue-800">
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span class="text-sm text-blue-700 dark:text-blue-300">插件已启用</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500">
            <SvgIcon icon="ri:plug-line" class="text-4xl mb-3" />
            <div class="text-lg font-medium mb-2">暂无可用插件</div>
            <div class="text-sm text-center max-w-xs">
              插件功能正在开发中，敬请期待更多强大的AI功能扩展。
            </div>
          </div>

          <!-- 插件使用提示 -->
          <div v-if="pluginList.length > 0" class="mt-6 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="text-xs text-gray-600 dark:text-gray-400">
              <div class="font-medium mb-1">💡 使用提示</div>
              <ul class="space-y-1 text-xs">
                <li>• 同时只能启用一个插件</li>
                <li>• 启用插件后，AI将在对话中应用相应功能</li>
                <li>• 不同插件可能会消耗不同的积分</li>
              </ul>
            </div>
          </div>
        </div>
      </NScrollbar>
    </div>
  </div>
</template>

<style scoped lang="scss">
.plugin-tabs {
  background: white;
  
  @media (prefers-color-scheme: dark) {
    background: #1f2937;
  }
}

// 修复Switch组件的样式
:deep(.headlessui-switch-group) {
  display: flex;
  align-items: center;
}
</style> 