import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToM<PERSON> } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';
import { StorybookEntity } from './storybook.entity';

/**
 * 绘本文件夹实体
 * 用于组织用户的绘本作品
 */
@Entity({ name: 'storybook_folder' })
export class StorybookFolderEntity extends BaseEntity {
  @Column({ comment: '文件夹名称' })
  name: string;

  @Column({ comment: '文件夹描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '封面图片URL', type: 'text', nullable: true })
  coverImg: string;

  @Column({ comment: '创建用户ID' })
  userId: number;

  @Column({ comment: '排序', default: 0 })
  order: number;

  @Column({ comment: '文件夹颜色', nullable: true })
  color: string;

  @Column({ comment: '文件夹图标', nullable: true })
  icon: string;

  @Column({ comment: '是否为系统文件夹', default: 0 })
  isSystem: number;

  // 关联的绘本作品
  @OneToMany(() => StorybookEntity, storybook => storybook.folder)
  storybooks: StorybookEntity[];
}
