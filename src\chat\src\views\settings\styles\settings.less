// 设置页面样式增强
.settings-page {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  &.dark {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);

    &::before {
      background:
        radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
    }
  }
}

// 页面头部样式增强
.settings-header {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;

  .dark & {
    background: rgba(17, 24, 39, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

// 标签页容器样式增强
.settings-tabs {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;

  .dark & {
    background: rgba(17, 24, 39, 0.9);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  // 标签页导航区域
  .n-tabs-nav {
    background: rgba(248, 250, 252, 0.8) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    padding: 12px 0;

    .dark & {
      background: rgba(30, 41, 59, 0.8) !important;
      border-bottom: 1px solid rgba(71, 85, 105, 0.5);
    }
  }

  // 标签页按钮样式
  .n-tabs-tab {
    border-radius: 16px;
    margin: 0 6px;
    padding: 12px 0;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 悬浮效果背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 16px;
    }

    // 悬浮状态
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 30px rgba(99, 102, 241, 0.15);

      &::before {
        opacity: 1;
      }

      .tab-icon {
        transform: scale(1.15);
        color: #6366f1;
      }

      .tab-label {
        color: #6366f1;
        font-weight: 600;
      }
    }

    // 活跃状态
    &.n-tabs-tab--active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow:
        0 12px 30px rgba(99, 102, 241, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);

      &::before {
        opacity: 0;
      }

      .tab-icon,
      .tab-label {
        color: white;
      }

      .tab-icon {
        transform: scale(1.1);
      }
    }
  }

  // 隐藏默认指示器
  .n-tabs-tab-pad {
    display: none;
  }
}

// 卡片样式增强
.setting-card,
.current-plugin-card,
.info-card,
.plugins-list-card,
.current-mode-card,
.tips-card,
.search-card,
.history-list-card,
.quick-actions-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 28px;
  margin-bottom: 20px;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // 卡片光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.12),
      0 0 0 1px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.3);

    &::before {
      left: 100%;
    }
  }

  .dark & {
    background: rgba(17, 24, 39, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.05);

    &::before {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }

    &:hover {
      border-color: rgba(99, 102, 241, 0.4);
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(99, 102, 241, 0.3);
    }
  }
}

// 悬浮动画
.hover-float {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 渐变按钮增强
.gradient-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
  }
}

// 插件卡片特殊样式增强
.plugin-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &.active {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    border-color: #4ade80;
    box-shadow: 0 12px 30px rgba(74, 222, 128, 0.3);

    .dark & {
      background: linear-gradient(135deg, #134e4a 0%, #7c2d12 100%) !important;
      box-shadow: 0 12px 30px rgba(20, 78, 74, 0.4);
    }
  }
}

// 模式卡片样式增强
.mode-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);

    &::before {
      left: 100%;
    }
  }

  &.teacher-mode {
    background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%) !important;
    border-color: rgba(59, 130, 246, 0.3);

    .dark & {
      background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%) !important;
      border-color: rgba(79, 70, 229, 0.4);
    }
  }

  &.student-mode {
    background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%) !important;
    border-color: rgba(34, 197, 94, 0.3);

    .dark & {
      background: linear-gradient(135deg, #0f172a 0%, #14532d 100%) !important;
      border-color: rgba(34, 197, 94, 0.4);
    }
  }
}

// 聊天项目样式增强
.chat-item {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
  }

  &:hover {
    transform: translateX(8px) translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: rgba(99, 102, 241, 0.3);

    &::before {
      left: 100%;
    }
  }

  &.sticky {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.2);

    .dark & {
      background: linear-gradient(135deg, #451a03 0%, #92400e 100%) !important;
      border-color: rgba(245, 158, 11, 0.3);
      box-shadow: 0 8px 20px rgba(69, 26, 3, 0.3);
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.2), transparent);
    }
  }
}

// 响应式设计增强
@media (max-width: 768px) {
  .settings-page {
    &::before {
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 40%);
    }
  }

  .settings-content {
    margin: 0 12px 12px 12px;

    .settings-tabs {
      border-radius: 16px;
    }

    .n-tab-pane {
      padding: 20px;
    }
  }

  .settings-tabs {
    .n-tabs-nav {
      padding: 8px 0;
    }

    .n-tabs-nav-scroll-content {
      padding: 0 12px;
    }

    .n-tabs-tab {
      min-width: 64px;
      justify-content: center;
      margin: 0 2px;
      padding: 10px 0;

      .tab-label {
        display: none;
      }

      .tab-icon {
        font-size: 20px;
      }
    }
  }

  // 卡片在移动端的优化
  .setting-card,
  .current-plugin-card,
  .info-card,
  .plugins-list-card,
  .current-mode-card,
  .tips-card,
  .search-card,
  .history-list-card,
  .quick-actions-card {
    padding: 20px;
    margin-bottom: 16px;
    border-radius: 16px;

    &:hover {
      transform: translateY(-3px) scale(1.01);
    }
  }

  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .plugins-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 1024px) {
  .plugins-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// 标签页横向滚动优化
.settings-tabs {
  .n-tabs-nav-scroll-wrapper {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .n-tabs-nav-scroll-content {
    display: flex;
    min-width: max-content;
  }
}

// 滚动条样式增强
.settings-page ::-webkit-scrollbar {
  width: 8px;
}

.settings-page ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin: 4px;
}

.settings-page ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.6) 0%, rgba(139, 92, 246, 0.6) 100%);
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.8) 0%, rgba(139, 92, 246, 0.8) 100%);
    background-clip: content-box;
  }
}

.dark .settings-page ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark .settings-page ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4) 0%, rgba(139, 92, 246, 0.4) 100%);
  background-clip: content-box;

  &:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.6) 0%, rgba(139, 92, 246, 0.6) 100%);
    background-clip: content-box;
  }
}

// 加载动画增强
.loading-state {
  .n-spin {
    color: #667eea;
  }

  .loading-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 空状态样式增强
.empty-state {
  .empty-icon {
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.2));
  }

  .empty-text {
    animation: fadeInUp 0.6s ease-out;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-12px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 徽章样式增强
.menu-badge {
  animation: pulse 2s infinite;
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
  box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

// 页面进入动画
.settings-page {
  animation: slideInFromRight 0.6s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 标签页切换动画
.n-tab-pane {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
