<template>
  <div class="character-library-modal" v-if="visible">
    <div class="modal-overlay" @click="closeLibrary"></div>
    <div class="character-library">
      <div class="library-header">
        <h3 class="library-title">角色库</h3>
        <div class="library-actions">
          <button class="library-action-btn sync-btn" @click="syncCharacters" title="同步角色库">
            <span class="action-icon">🔄</span>
          </button>
          <button class="library-action-btn" @click="closeLibrary">
            <span class="action-icon">×</span>
          </button>
        </div>
      </div>

    <div class="library-toolbar">
      <div class="search-box">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="搜索角色..."
          class="search-input"
        />
      </div>
      <div class="filter-options">
        <select v-model="filterType" class="filter-select">
          <option value="">所有类型</option>
          <option v-for="(name, type) in typeMap" :key="type" :value="type">{{ name }}</option>
        </select>
        <div class="view-options">
          <button
            class="view-option-btn"
            :class="{ active: viewMode === 'grid' }"
            @click="viewMode = 'grid'"
            title="网格视图"
          >
            <span class="view-icon">▦</span>
          </button>
          <button
            class="view-option-btn"
            :class="{ active: viewMode === 'list' }"
            @click="viewMode = 'list'"
            title="列表视图"
          >
            <span class="view-icon">☰</span>
          </button>
        </div>
      </div>
    </div>

    <div class="library-content">
      <div v-if="isLoading" class="loading-indicator">
        <div class="loading-spinner"></div>
        <p>正在加载角色库...</p>
      </div>

      <div v-else-if="filteredCharacters.length === 0" class="empty-library">
        <div class="empty-icon">📚</div>
        <p class="empty-text">{{ searchQuery ? '没有找到匹配的角色' : '角色库还是空的' }}</p>
        <p class="empty-hint">{{ searchQuery ? '尝试其他搜索词或清除筛选条件' : '创建角色后会自动保存到这里' }}</p>
      </div>

      <div v-else>
        <!-- 角色列表 -->
        <div :class="viewMode === 'grid' ? 'character-grid' : 'character-list'">
          <div
            v-for="character in paginatedCharacters"
            :key="character.id"
            :class="viewMode === 'grid' ? 'character-card' : 'character-list-item'"
            @click="selectCharacter(character)"
          >
            <div class="character-image">
              <img v-if="character.imageUrl || character._image" :src="character.imageUrl || character._image" :alt="character.name" />
              <div v-else class="image-placeholder">
                <span class="placeholder-icon">{{ getCharacterEmoji(character) }}</span>
              </div>
              <div v-if="character.isFavorite" class="favorite-badge" title="收藏的角色">★</div>
            </div>
            <div class="character-info">
              <h4 class="character-name">{{ character.name }}</h4>
              <div class="character-type">{{ getCharacterTypeName(character.characterType) }}</div>
              <div v-if="character.tags && character.tags.length > 0" class="character-tags">
                <span v-for="tag in character.tags.slice(0, 3)" :key="tag" class="character-tag">{{ tag }}</span>
                <span v-if="character.tags.length > 3" class="character-tag more-tag">+{{ character.tags.length - 3 }}</span>
              </div>
            </div>
            <div class="character-actions">
              <button class="character-action-btn" @click.stop="toggleFavorite(character.id)" :title="character.isFavorite ? '取消收藏' : '收藏角色'">
                <span class="action-icon">{{ character.isFavorite ? '★' : '☆' }}</span>
              </button>
              <button class="character-action-btn" @click.stop="duplicateCharacter(character.id)" title="复制角色">
                <span class="action-icon">📋</span>
              </button>
              <button class="character-action-btn" @click.stop="showTagEditor(character)" title="编辑标签">
                <span class="action-icon">🏷️</span>
              </button>
              <button class="character-action-btn" @click.stop="deleteCharacter(character.id)" title="删除角色">
                <span class="action-icon">🗑️</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="currentPage = 1"
            title="第一页"
          >
            «
          </button>
          <button
            class="pagination-btn"
            :disabled="currentPage === 1"
            @click="currentPage--"
            title="上一页"
          >
            ‹
          </button>

          <div class="pagination-info">
            {{ currentPage }} / {{ totalPages }}
          </div>

          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
            title="下一页"
          >
            ›
          </button>
          <button
            class="pagination-btn"
            :disabled="currentPage === totalPages"
            @click="currentPage = totalPages"
            title="最后一页"
          >
            »
          </button>
        </div>

        <!-- 角色计数 -->
        <div class="character-count">
          共 {{ filteredCharacters.length }} 个角色
          <span v-if="characters && characters.length !== filteredCharacters.length">
            (筛选自 {{ characters.length }} 个)
          </span>
        </div>
      </div>
    </div>

    <div class="library-footer">
      <div class="import-export-actions">
        <button class="import-btn" @click="importCharacters">导入角色</button>
        <button class="export-btn" @click="exportCharacters">导出角色</button>
      </div>
    </div>

    <!-- 标签编辑弹窗 -->
    <div v-if="showTagModal" class="tag-modal">
      <div class="tag-modal-content">
        <h4 class="tag-modal-title">编辑标签</h4>
        <div class="tag-input-container">
          <input
            type="text"
            v-model="newTag"
            placeholder="输入标签..."
            class="tag-input"
            @keyup.enter="addTag"
          />
          <button class="add-tag-btn" @click="addTag">添加</button>
        </div>
        <div class="current-tags">
          <div v-if="!selectedCharacter.tags || selectedCharacter.tags.length === 0" class="no-tags">
            暂无标签
          </div>
          <div v-else class="tag-list">
            <div v-for="tag in selectedCharacter.tags" :key="tag" class="tag-item">
              <span class="tag-name">{{ tag }}</span>
              <button class="remove-tag-btn" @click="removeTag(tag)">×</button>
            </div>
          </div>
        </div>
        <div class="tag-modal-actions">
          <button class="cancel-btn" @click="closeTagEditor">关闭</button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入框，用于导入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none"
      accept=".json"
      @change="handleFileImport"
    />
    </div>

    <!-- 删除确认弹窗 -->
    <NModal
      v-model:show="showDeleteModal"
      preset="dialog"
      title="确认删除"
      positive-text="删除"
      negative-text="取消"
      @positive-click="confirmDeleteCharacter"
    >
      <div class="delete-modal-content">
        <div class="delete-warning-icon">
          <span class="warning-icon">⚠️</span>
        </div>
        <p class="delete-warning-text">确定要删除角色"{{ characterToDelete.name }}"吗？此操作不可恢复。</p>
      </div>
    </NModal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { NModal } from 'naive-ui';
import {
  getCharacterLibrary,
  removeCharacterFromLibrary,
  exportCharacterLibrary,
  importCharacterLibrary,
  synchronizeCharacterLibrary,
  toggleCharacterFavorite as toggleFav,
  duplicateCharacter as duplicateChar,
  addTagToCharacter,
  removeTagFromCharacter
} from '@/utils/characterLibrary';
import { useAuthStore } from '@/store';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'select-character']);

// 状态变量
const searchQuery = ref('');
const filterType = ref('');
const viewMode = ref('grid');
const isLoading = ref(false);
const showTagModal = ref(false);
const newTag = ref('');
const selectedCharacter = reactive({
  id: 0,
  name: '',
  tags: []
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(12); // 每页显示的角色数量

// 角色列表
const characters = ref([]);

// 初始化角色列表
onMounted(() => {
  characters.value = getCharacterLibrary();
});

// 过滤后的角色列表
const filteredCharacters = computed(() => {
  let result = characters.value;

  // 按类型筛选
  if (filterType.value) {
    result = result.filter(c => c.characterType === filterType.value);
  }

  // 按搜索词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(c =>
      c.name.toLowerCase().includes(query) ||
      (c.tags && c.tags.some(tag => tag.toLowerCase().includes(query))) ||
      (c.appearance && c.appearance.toLowerCase().includes(query)) ||
      (c.characterType && getCharacterTypeName(c.characterType).toLowerCase().includes(query))
    );
  }

  // 收藏的角色排在前面
  return result.sort((a, b) => {
    // 处理isFavorite可能是数字或布尔值的情况
    const aFav = a.isFavorite === 1 || a.isFavorite === true;
    const bFav = b.isFavorite === 1 || b.isFavorite === true;

    if (aFav && !bFav) return -1;
    if (!aFav && bFav) return 1;

    // 如果收藏状态相同，按更新时间排序（新的在前）
    const aTime = new Date(a.updatedAt || a.createdAt || 0).getTime();
    const bTime = new Date(b.updatedAt || b.createdAt || 0).getTime();
    return bTime - aTime;
  });
});

// 总页数
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(filteredCharacters.value.length / pageSize.value));
});

// 当前页的角色
const paginatedCharacters = computed(() => {
  // 当筛选条件变化时，重置到第一页
  if (currentPage.value > totalPages.value) {
    currentPage.value = 1;
  }

  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return filteredCharacters.value.slice(startIndex, endIndex);
});

// 文件输入引用
const fileInput = ref(null);

// 角色类型映射
const typeMap = {
  boy: '男孩',
  girl: '女孩',
  animal: '动物',
  magical: '魔法生物',
  robot: '机器人',
  alien: '外星人',
  oldman: '老爷爷',
  oldwoman: '老奶奶',
  teen: '青少年',
  baby: '婴儿',
  superhero: '超级英雄',
  princess: '公主',
  prince: '王子',
  elf: '精灵',
  wizard: '巫师',
  protagonist: '主角',
  friend: '朋友',
  helper: '助手',
  opponent: '对手'
};

// 组件挂载时同步角色库
onMounted(async () => {
  try {
    const authStore = useAuthStore();
    if (authStore.token) {
      await syncCharacters();
    }
  } catch (error) {
    console.error('角色库同步失败:', error);
    // 即使同步失败，也不影响用户体验
  }
});

// 关闭角色库
const closeLibrary = () => {
  emit('close');
};

// 选择角色
const selectCharacter = (character) => {
  emit('select-character', character);
  closeLibrary();
};

// 删除角色相关状态
const showDeleteModal = ref(false);
const characterToDelete = ref({
  id: null,
  name: ''
});

// 显示删除确认弹窗
const showDeleteConfirmation = (characterId) => {
  // 获取角色信息，用于显示更具体的消息
  const character = characters.value.find(c => c.id === characterId);
  if (character) {
    characterToDelete.value = {
      id: character.id,
      name: character.name
    };
    showDeleteModal.value = true;
  }
};

// 执行删除角色操作
const confirmDeleteCharacter = async () => {
  const { id: characterId, name: characterName } = characterToDelete.value;

  if (!characterId) return;

  isLoading.value = true;
  showDeleteModal.value = false;

  try {
    // 显示删除中的状态提示
    const loadingMessage = window.$message?.loading(`正在删除角色"${characterName}"...`, {
      duration: 0 // 不自动关闭
    });

    const success = await removeCharacterFromLibrary(characterId);

    // 关闭加载提示
    loadingMessage?.destroy();

    if (success) {
      window.$message?.success(`角色"${characterName}"已成功删除`);

      // 更新本地角色列表，无需重新加载整个页面
      characters.value = getCharacterLibrary();
    } else {
      window.$message?.error(`删除角色"${characterName}"失败，请稍后重试`);
    }
  } catch (error) {
    console.error(`删除角色"${characterName}"失败:`, error);

    // 提供更具体的错误信息
    let errorMessage = `删除角色"${characterName}"失败`;

    if (error.message) {
      errorMessage += `: ${error.message}`;
    } else if (error.response?.status) {
      errorMessage += `: 服务器返回错误 (${error.response.status})`;
    }

    window.$message?.error(errorMessage);
  } finally {
    isLoading.value = false;
  }
};

// 删除角色（触发确认弹窗）
const deleteCharacter = (characterId) => {
  showDeleteConfirmation(characterId);
};

// 导出角色
const exportCharacters = () => {
  try {
    const jsonString = exportCharacterLibrary();

    // 创建Blob对象
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `角色库_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);

    window.$message?.success('角色库导出成功');
  } catch (error) {
    console.error('导出角色库失败:', error);
    window.$message?.error('导出角色库失败');
  }
};

// 导入角色
const importCharacters = () => {
  fileInput.value.click();
};

// 处理文件导入
const handleFileImport = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 检查文件类型
  if (file.type && file.type !== 'application/json' && !file.name.endsWith('.json')) {
    window.$message?.error('请选择JSON格式的角色库文件');
    event.target.value = '';
    return;
  }

  // 检查文件大小（限制为10MB）
  if (file.size > 10 * 1024 * 1024) {
    window.$message?.error('文件过大，请选择小于10MB的文件');
    event.target.value = '';
    return;
  }

  // 显示加载提示
  const loadingMessage = window.$message?.loading('正在导入角色库...', {
    duration: 0
  });

  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const jsonString = e.target.result;

      // 尝试解析JSON
      let parsedData;
      try {
        parsedData = JSON.parse(jsonString);
      } catch (parseError) {
        loadingMessage?.destroy();
        window.$message?.error('导入失败：无效的JSON格式');
        console.error('JSON解析失败:', parseError);
        return;
      }

      // 验证是否为数组
      if (!Array.isArray(parsedData)) {
        loadingMessage?.destroy();
        window.$message?.error('导入失败：文件格式不正确，应为角色数组');
        return;
      }

      // 验证数组内容
      if (parsedData.length === 0) {
        loadingMessage?.destroy();
        window.$message?.warning('导入的角色库为空');
        return;
      }

      // 验证每个角色是否有必要的字段
      const invalidItems = parsedData.filter(item =>
        !item || typeof item !== 'object' || !item.name || typeof item.name !== 'string'
      );

      if (invalidItems.length > 0) {
        loadingMessage?.destroy();
        window.$message?.error(`导入失败：包含${invalidItems.length}个无效角色数据`);
        return;
      }

      // 导入角色库
      const success = importCharacterLibrary(jsonString);
      loadingMessage?.destroy();

      if (success) {
        window.$message?.success(`角色库导入成功，共${parsedData.length}个角色`);
        // 刷新角色列表显示
        setTimeout(() => {
          // 强制刷新视图
          characters.value = getCharacterLibrary();
        }, 100);
      } else {
        window.$message?.error('导入失败：处理角色数据时出错');
      }
    } catch (error) {
      loadingMessage?.destroy();
      console.error('读取文件失败:', error);
      window.$message?.error(`读取文件失败: ${error.message || '未知错误'}`);
    } finally {
      // 重置文件输入
      event.target.value = '';
    }
  };

  reader.onerror = () => {
    loadingMessage?.destroy();
    window.$message?.error('读取文件时发生错误');
    event.target.value = '';
  };

  reader.readAsText(file);
};

// 同步角色库
const syncCharacters = async () => {
  isLoading.value = true;

  try {
    // 显示同步中的状态提示
    const loadingMessage = window.$message?.loading('正在同步角色库...', {
      duration: 0 // 不自动关闭
    });

    const result = await synchronizeCharacterLibrary();

    // 关闭加载提示
    loadingMessage?.destroy();

    if (result && result.length >= 0) {
      // 更新本地角色列表
      characters.value = getCharacterLibrary();
      window.$message?.success(`角色库同步成功，共${result.length}个角色`);
    } else {
      // 即使没有返回数据，也刷新本地列表
      characters.value = getCharacterLibrary();
      window.$message?.warning('角色库同步完成，但未返回数据');
    }
  } catch (error) {
    console.error('同步角色库失败:', error);

    // 提供更具体的错误信息
    let errorMessage = '同步角色库失败';

    if (error.message) {
      // 根据错误类型提供更具体的提示
      if (error.message.includes('网络')) {
        errorMessage = '同步失败：网络连接问题，请检查网络后重试';
      } else if (error.message.includes('登录') || error.message.includes('认证')) {
        errorMessage = '同步失败：登录状态已过期，请重新登录';
      } else if (error.message.includes('超时')) {
        errorMessage = '同步失败：请求超时，请稍后重试';
      } else {
        errorMessage = `同步失败：${error.message}`;
      }
    } else if (error.response) {
      // 根据HTTP状态码提供更具体的提示
      const status = error.response.status;
      if (status === 401) {
        errorMessage = '同步失败：登录状态已过期，请重新登录';
      } else if (status === 403) {
        errorMessage = '同步失败：没有权限执行此操作';
      } else if (status === 404) {
        errorMessage = '同步失败：服务不可用，请联系管理员';
      } else if (status >= 500) {
        errorMessage = '同步失败：服务器错误，请稍后重试';
      } else {
        errorMessage = `同步失败：服务器返回错误 (${status})`;
      }
    }

    window.$message?.error(errorMessage);
  } finally {
    isLoading.value = false;
  }
};

// 切换收藏状态
const toggleFavorite = (characterId) => {
  if (toggleFav(characterId)) {
    // 成功切换收藏状态
  }
};

// 复制角色
const duplicateCharacter = (characterId) => {
  const newCharacter = duplicateChar(characterId);
  if (newCharacter) {
    window.$message?.success('角色复制成功');
  } else {
    window.$message?.error('角色复制失败');
  }
};

// 显示标签编辑器
const showTagEditor = (character) => {
  selectedCharacter.id = character.id;
  selectedCharacter.name = character.name;
  selectedCharacter.tags = [...(character.tags || [])];
  showTagModal.value = true;
  newTag.value = '';
};

// 关闭标签编辑器
const closeTagEditor = () => {
  showTagModal.value = false;
};

// 添加标签
const addTag = () => {
  if (!newTag.value.trim()) return;

  if (addTagToCharacter(selectedCharacter.id, newTag.value.trim())) {
    // 更新本地状态
    if (!selectedCharacter.tags) {
      selectedCharacter.tags = [];
    }
    selectedCharacter.tags.push(newTag.value.trim());
    newTag.value = '';
  }
};

// 移除标签
const removeTag = (tag) => {
  if (removeTagFromCharacter(selectedCharacter.id, tag)) {
    // 更新本地状态
    selectedCharacter.tags = selectedCharacter.tags.filter(t => t !== tag);
  }
};

// 获取角色表情
const getCharacterEmoji = (character) => {
  const typeEmojis = {
    boy: '👦',
    girl: '👧',
    animal: '🐶',
    magical: '🧚',
    robot: '🤖',
    alien: '👽',
    oldman: '👴',
    oldwoman: '👵',
    teen: '🧑',
    baby: '👶',
    superhero: '🦸',
    princess: '👸',
    prince: '🤴',
    elf: '🧝',
    wizard: '🧙',
    protagonist: '👑',
    friend: '💛',
    helper: '🤝',
    opponent: '⚡'
  };

  // 如果有角色类型，使用角色类型的表情
  if (character.characterType && typeEmojis[character.characterType]) {
    return typeEmojis[character.characterType];
  }

  // 否则使用角色在故事中的角色类型的表情
  if (character.role && typeEmojis[character.role]) {
    return typeEmojis[character.role];
  }

  // 默认返回通用角色表情
  return '👤';
};

// 获取角色类型名称
const getCharacterTypeName = (typeValue) => {
  return typeMap[typeValue] || '未知类型';
};
</script>

<style scoped>
.character-library-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.character-library {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 900px;
  height: 80vh;
  max-height: 700px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 1001;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dark .character-library {
  background-color: #1e293b;
  color: #e2e8f0;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .library-header {
  border-color: #334155;
}

.library-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.library-actions {
  display: flex;
  gap: 0.5rem;
}

.library-action-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.library-action-btn:hover {
  background-color: #f1f5f9;
}

.dark .library-action-btn:hover {
  background-color: #334155;
}

.sync-btn {
  color: #3b82f6;
}

.dark .sync-btn {
  color: #60a5fa;
}

.action-icon {
  font-size: 1.5rem;
}

/* 工具栏样式 */
.library-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .library-toolbar {
  border-color: #334155;
}

.search-box {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
  transition: border-color 0.2s;
}

.dark .search-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.filter-options {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
  transition: border-color 0.2s;
}

.dark .filter-select {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
}

.view-options {
  display: flex;
  gap: 0.25rem;
}

.view-option-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .view-option-btn {
  background-color: #1e293b;
  border-color: #334155;
  color: #e2e8f0;
}

.view-option-btn.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.dark .view-option-btn.active {
  background-color: #2563eb;
  border-color: #2563eb;
}

.view-icon {
  font-size: 1rem;
}

/* 内容区域样式 */
.library-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 0.25rem solid #e2e8f0;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.dark .loading-spinner {
  border-color: #334155;
  border-top-color: #3b82f6;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-library {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.empty-hint {
  font-size: 0.875rem;
  color: #64748b;
}

.dark .empty-hint {
  color: #94a3b8;
}

/* 网格视图 */
.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem;
}

.character-card {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.dark .character-card {
  background-color: #334155;
}

.character-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 列表视图 */
.character-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.character-list-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.dark .character-list-item {
  background-color: #334155;
}

.character-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.character-list-item .character-image {
  width: 4rem;
  height: 4rem;
  border-radius: 0.375rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.character-list-item .character-info {
  flex: 1;
  padding: 0;
}

.character-list-item .character-actions {
  position: static;
  opacity: 0;
  margin-left: 0.5rem;
}

.character-list-item:hover .character-actions {
  opacity: 1;
}

/* 角色图像 */
.character-image {
  position: relative;
  width: 100%;
  height: 180px;
  background-color: #f8fafc;
  overflow: hidden;
}

.dark .character-image {
  background-color: #1e293b;
}

.character-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f1f5f9;
}

.dark .image-placeholder {
  background-color: #334155;
}

.placeholder-icon {
  font-size: 3rem;
}

.favorite-badge {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(234, 179, 8, 0.9);
  color: white;
  border-radius: 50%;
  font-size: 0.875rem;
}

/* 角色信息 */
.character-info {
  padding: 0.75rem;
}

.character-name {
  margin: 0 0 0.25rem;
  font-size: 1rem;
  font-weight: 600;
}

.character-type {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.dark .character-type {
  color: #94a3b8;
}

.character-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.character-tag {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
  background-color: #f1f5f9;
  color: #334155;
  border-radius: 1rem;
}

.dark .character-tag {
  background-color: #334155;
  color: #e2e8f0;
}

/* 角色操作按钮 */
.character-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.character-card:hover .character-actions {
  opacity: 1;
}

.character-action-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .character-action-btn {
  background-color: rgba(30, 41, 59, 0.9);
}

.character-action-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

.dark .character-action-btn:hover {
  background-color: rgba(30, 41, 59, 1);
}

/* 底部区域 */
.library-footer {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
}

.dark .library-footer {
  border-color: #334155;
}

.import-export-actions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.import-btn,
.export-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.import-btn {
  background-color: #f1f5f9;
  color: #334155;
}

.dark .import-btn {
  background-color: #334155;
  color: #e2e8f0;
}

.import-btn:hover {
  background-color: #e2e8f0;
}

.dark .import-btn:hover {
  background-color: #475569;
}

.export-btn {
  background-color: #eff6ff;
  color: #1e40af;
}

.dark .export-btn {
  background-color: #1e40af;
  color: #eff6ff;
}

.export-btn:hover {
  background-color: #dbeafe;
}

.dark .export-btn:hover {
  background-color: #2563eb;
}

/* 标签编辑弹窗 */
.tag-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1010; /* 确保在角色库弹窗之上 */
}

.tag-modal-content {
  width: 90%;
  max-width: 400px;
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .tag-modal-content {
  background-color: #1e293b;
  color: #e2e8f0;
}

.tag-modal-title {
  margin: 0 0 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.tag-input-container {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.dark .tag-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.tag-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.add-tag-btn {
  padding: 0.5rem 0.75rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-tag-btn:hover {
  background-color: #2563eb;
}

.current-tags {
  margin-bottom: 1.5rem;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

.no-tags {
  color: #64748b;
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem 0;
}

.dark .no-tags {
  color: #94a3b8;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: #f1f5f9;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.dark .tag-item {
  background-color: #334155;
}

.tag-name {
  color: #334155;
}

.dark .tag-name {
  color: #e2e8f0;
}

.remove-tag-btn {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e2e8f0;
  color: #64748b;
  border: none;
  border-radius: 50%;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .remove-tag-btn {
  background-color: #475569;
  color: #94a3b8;
}

.remove-tag-btn:hover {
  background-color: #ef4444;
  color: white;
}

.tag-modal-actions {
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  padding: 0.5rem 1rem;
  background-color: #f1f5f9;
  color: #334155;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .cancel-btn {
  background-color: #334155;
  color: #e2e8f0;
}

.cancel-btn:hover {
  background-color: #e2e8f0;
}

.dark .cancel-btn:hover {
  background-color: #475569;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  padding: 0.5rem;
  gap: 0.5rem;
}

.pagination-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .pagination-btn {
  background-color: #1e293b;
  border-color: #334155;
  color: #e2e8f0;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e2e8f0;
}

.dark .pagination-btn:hover:not(:disabled) {
  background-color: #334155;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  padding: 0 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.dark .pagination-info {
  color: #94a3b8;
}

/* 角色计数 */
.character-count {
  text-align: center;
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.dark .character-count {
  color: #94a3b8;
}

/* 更多标签指示器 */
.more-tag {
  background-color: #e2e8f0;
  color: #64748b;
}

.dark .more-tag {
  background-color: #334155;
  color: #94a3b8;
}

/* 删除确认弹窗样式 */
.delete-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  text-align: center;
}

.delete-warning-icon {
  margin-bottom: 1rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fee2e2;
  border-radius: 50%;
}

.dark .delete-warning-icon {
  background-color: #7f1d1d;
}

.warning-icon {
  font-size: 1.5rem;
}

.delete-warning-text {
  font-size: 1rem;
  color: #1e293b;
  line-height: 1.5;
}

.dark .delete-warning-text {
  color: #e2e8f0;
}
</style>
