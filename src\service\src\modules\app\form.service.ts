import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Not, Repository } from 'typeorm';
import { FormEntity } from './form.entity';
import { AppEntity } from './app.entity';
import { CreateFormDto, DeleteFormDto, QueryFormDto, UpdateFormDto } from './dto/form.dto';
import { FormValidator } from '../../utils/form-validator';

@Injectable()
export class FormService {
  private readonly logger = new Logger(FormService.name);

  constructor(
    @InjectRepository(FormEntity)
    private readonly formEntity: Repository<FormEntity>,
    @InjectRepository(AppEntity)
    private readonly appEntity: Repository<AppEntity>
  ) {}

  /**
   * 创建表单
   * @param body 创建表单DTO
   * @returns 创建的表单
   */
  async createForm(body: CreateFormDto) {
    try {
      const { name, appId, fields } = body;

      // 验证表单字段JSON格式
      if (!FormValidator.validateFormFieldsJson(fields)) {
        this.logger.error('表单字段JSON格式错误或缺少必要属性');
        throw new HttpException('表单字段JSON格式错误或缺少必要属性', HttpStatus.BAD_REQUEST);
      }

      // 验证表单字段
      try {
        const fieldsArray = JSON.parse(fields);
        if (!FormValidator.validateFields(fieldsArray)) {
          this.logger.error('表单字段验证失败，存在无效字段');
          throw new HttpException('表单字段验证失败，请检查字段配置', HttpStatus.BAD_REQUEST);
        }
      } catch (error) {
        if (error instanceof HttpException) {
          throw error;
        }
        this.logger.error(`表单字段验证异常: ${error.message}`);
        throw new HttpException('表单字段验证失败', HttpStatus.BAD_REQUEST);
      }

      // 检查应用是否存在
      const app = await this.appEntity.findOne({ where: { id: appId } });
      if (!app) {
        this.logger.warn(`创建表单失败: 关联的应用ID ${appId} 不存在`);
        throw new HttpException('关联的应用不存在！', HttpStatus.BAD_REQUEST);
      }

      // 检查表单名称是否已存在于同一应用下
      const existingForm = await this.formEntity.findOne({ where: { name, appId } });
      if (existingForm) {
        this.logger.warn(`创建表单失败: 应用ID ${appId} 下已存在同名表单 "${name}"`);
        throw new HttpException('该应用下已存在同名表单！', HttpStatus.BAD_REQUEST);
      }

      // 创建表单
      const result = await this.formEntity.save(body);
      this.logger.log(`成功创建表单: ID=${result.id}, 名称="${name}", 应用ID=${appId}`);
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`创建表单时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('创建表单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新表单
   * @param body 更新表单DTO
   * @returns 更新结果
   */
  async updateForm(body: UpdateFormDto) {
    try {
      const { id, name, appId, fields } = body;

      // 验证表单字段JSON格式
      if (!FormValidator.validateFormFieldsJson(fields)) {
        this.logger.error('表单字段JSON格式错误或缺少必要属性');
        throw new HttpException('表单字段JSON格式错误或缺少必要属性', HttpStatus.BAD_REQUEST);
      }

      // 验证表单字段
      try {
        const fieldsArray = JSON.parse(fields);
        if (!FormValidator.validateFields(fieldsArray)) {
          this.logger.error('表单字段验证失败，存在无效字段');
          throw new HttpException('表单字段验证失败，请检查字段配置', HttpStatus.BAD_REQUEST);
        }
      } catch (error) {
        if (error instanceof HttpException) {
          throw error;
        }
        this.logger.error(`表单字段验证异常: ${error.message}`);
        throw new HttpException('表单字段验证失败', HttpStatus.BAD_REQUEST);
      }

      // 检查表单是否存在
      const form = await this.formEntity.findOne({ where: { id } });
      if (!form) {
        this.logger.warn(`更新表单失败: 表单ID ${id} 不存在`);
        throw new HttpException('表单不存在！', HttpStatus.BAD_REQUEST);
      }

      // 检查应用是否存在
      const app = await this.appEntity.findOne({ where: { id: appId } });
      if (!app) {
        this.logger.warn(`更新表单失败: 关联的应用ID ${appId} 不存在`);
        throw new HttpException('关联的应用不存在！', HttpStatus.BAD_REQUEST);
      }

      // 检查表单名称是否已存在于同一应用下(排除自身)
      const existingForm = await this.formEntity.findOne({
        where: {
          name,
          appId,
          id: Not(id)
        }
      });
      if (existingForm) {
        this.logger.warn(`更新表单失败: 应用ID ${appId} 下已存在同名表单 "${name}"`);
        throw new HttpException('该应用下已存在同名表单！', HttpStatus.BAD_REQUEST);
      }

      // 更新表单
      const res = await this.formEntity.update({ id }, body);
      if (res.affected > 0) {
        this.logger.log(`成功更新表单: ID=${id}, 名称="${name}", 应用ID=${appId}`);
        return '修改表单成功';
      }

      this.logger.warn(`更新表单失败: 表单ID ${id} 更新操作未影响任何记录`);
      throw new HttpException('修改表单失败！', HttpStatus.BAD_REQUEST);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`更新表单时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('更新表单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 删除表单
   * @param body 删除表单DTO
   * @returns 删除结果
   */
  async deleteForm(body: DeleteFormDto) {
    try {
      const { id } = body;

      // 检查表单是否存在
      const form = await this.formEntity.findOne({ where: { id } });
      if (!form) {
        this.logger.warn(`删除表单失败: 表单ID ${id} 不存在`);
        throw new HttpException('表单不存在！', HttpStatus.BAD_REQUEST);
      }

      // 删除表单
      const res = await this.formEntity.delete(id);
      if (res.affected > 0) {
        this.logger.log(`成功删除表单: ID=${id}, 名称="${form.name}", 应用ID=${form.appId}`);
        return '删除表单成功';
      }

      this.logger.warn(`删除表单失败: 表单ID ${id} 删除操作未影响任何记录`);
      throw new HttpException('删除表单失败！', HttpStatus.BAD_REQUEST);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`删除表单时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('删除表单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 查询表单列表
   * @param query 查询表单DTO
   * @returns 表单列表和总数
   */
  async queryForms(query: QueryFormDto) {
    try {
      const { page = 1, size = 10, name, status, appId } = query;
      const where: any = {};

      // 构建查询条件
      if (name) {
        where.name = Like(`%${name}%`);
      }
      if (status !== undefined) {
        where.status = status;
      }
      if (appId) {
        where.appId = appId;
      }

      // 查询表单列表
      const [rows, count] = await this.formEntity.findAndCount({
        where,
        order: { order: 'DESC' },
        skip: (page - 1) * size,
        take: size,
      });

      // 查询关联的应用信息
      if (rows.length > 0) {
        const appIds = rows.map(form => form.appId);
        const apps = await this.appEntity.find({ where: { id: In(appIds) } });

        rows.forEach((form: any) => {
          const app = apps.find(app => app.id === form.appId);
          form.appName = app ? app.name : '未知';
        });
      }

      this.logger.log(`查询表单列表成功: 共 ${count} 条记录, 当前页 ${page}, 每页 ${size} 条`);
      return { rows, count };
    } catch (error) {
      this.logger.error(`查询表单列表时发生错误: ${error.message}`, error.stack);
      throw new HttpException('查询表单列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 根据ID获取表单详情
   * @param id 表单ID
   * @returns 表单详情
   */
  async getFormById(id: number) {
    try {
      if (!id) {
        this.logger.warn('获取表单详情失败: 缺少表单ID');
        throw new HttpException('缺失必要参数！', HttpStatus.BAD_REQUEST);
      }

      // 查询表单详情
      const form = await this.formEntity.findOne({ where: { id } });
      if (!form) {
        this.logger.warn(`获取表单详情失败: 表单ID ${id} 不存在`);
        throw new HttpException('表单不存在！', HttpStatus.BAD_REQUEST);
      }

      // 查询关联的应用信息
      const app = await this.appEntity.findOne({ where: { id: form.appId } });
      const result = {
        ...form,
        appName: app ? app.name : '未知'
      };

      this.logger.log(`获取表单详情成功: ID=${id}, 名称="${form.name}"`);
      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`获取表单详情时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('获取表单详情失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取应用关联的所有表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  async getFormsByAppId(appId: number) {
    try {
      if (!appId) {
        this.logger.warn('获取应用关联表单失败: 缺少应用ID');
        throw new HttpException('缺失必要参数！', HttpStatus.BAD_REQUEST);
      }

      // 检查应用是否存在
      const app = await this.appEntity.findOne({ where: { id: appId } });
      if (!app) {
        this.logger.warn(`获取应用关联表单失败: 应用ID ${appId} 不存在`);
        throw new HttpException('应用不存在！', HttpStatus.BAD_REQUEST);
      }

      // 查询应用关联的表单
      const forms = await this.formEntity.find({
        where: { appId, status: 1 },
        order: { order: 'DESC' }
      });

      this.logger.log(`获取应用关联表单成功: 应用ID=${appId}, 表单数量=${forms.length}`);
      return forms;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`获取应用关联表单时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('获取应用关联表单失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }


}
