<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const props = defineProps<{
  pages: Array<{
    id: number | string;
    image?: string;
    text?: string;
    type?: 'soft' | 'hard';
    layout?: 'vertical' | 'horizontal' | 'horizontal-reverse' | 'full-image' | 'collage';
    textStyle?: {
      position?: 'top' | 'bottom' | 'left' | 'right' | 'overlay';
      alignment?: 'left' | 'center' | 'right';
      fontSize?: 'small' | 'medium' | 'large';
      color?: string;
      backgroundColor?: string;
      opacity?: number;
    };
  }>;
  width?: number;
  height?: number;
  showCover?: boolean;
  fullscreen?: boolean; // 是否使用全屏模式
  landscape?: boolean; // 是否使用横板模式
}>();

const emit = defineEmits(['page-flip', 'close']);

// DOM references
const bookElement = ref<HTMLElement | null>(null);
const pageFlip = ref<any>(null);

// State
const currentPage = ref(0);
const totalPages = ref(0);
const isLoading = ref(true);
const isLandscape = ref(props.landscape || true); // 默认使用横版模式
const { isMobile } = useBasicLayout();

// 根据横竖版模式计算默认尺寸
const getDefaultDimensions = () => {
  if (isLandscape.value) {
    // 横板模式：宽大于高，使用标准绘本比例 4:3
    return {
      width: props.width || 800,
      height: props.height || 600 // 4:3 比例
    };
  } else {
    // 竖版模式：高大于宽，使用标准绘本比例 3:4
    return {
      width: props.width || 600,
      height: props.height || 800 // 3:4 比例
    };
  }
};

// 获取默认尺寸
const defaultDimensions = getDefaultDimensions();

// Configuration
const config = {
  width: defaultDimensions.width,
  height: defaultDimensions.height,
  size: 'fixed' as 'fixed' | 'stretch',
  minWidth: isLandscape.value ? 600 : 320,
  maxWidth: isLandscape.value ? 1200 : 1000,
  minHeight: isLandscape.value ? 350 : 420,
  maxHeight: isLandscape.value ? 800 : 1350,
  maxShadowOpacity: 0.5,
  showCover: props.showCover !== undefined ? props.showCover : true,
  mobileScrollSupport: true,
  flippingTime: 1000,
  usePortrait: !isLandscape.value, // 根据模式设置方向
  autoSize: true,
  drawShadow: true,
};

// Load PageFlip library
const loadPageFlipLibrary = () => {
  console.log('开始加载PageFlip库...');
  return new Promise<void>((resolve, reject) => {
    if (window.PageFlip) {
      console.log('PageFlip库已经加载过，直接使用');
      resolve();
      return;
    }

    // 只从本地文件加载
    const localSource = '/libs/page-flip.browser.min.js';

    const script = document.createElement('script');
    script.src = localSource;
    script.async = true;

    script.onload = () => {
      console.log(`PageFlip库加载成功: ${localSource}`);
      resolve();
    };

    script.onerror = (error) => {
      console.error(`PageFlip库加载失败: ${localSource}`, error);
      reject(new Error('PageFlip库加载失败，请检查本地文件是否存在'));
    };

    document.head.appendChild(script);
    console.log(`正在加载PageFlip库: ${localSource}`);
  });
};

// Initialize PageFlip
const initPageFlip = async () => {
  console.log('开始初始化PageFlip...');
  if (!bookElement.value) {
    console.error('bookElement不存在，无法初始化PageFlip');
    isLoading.value = false;
    return;
  }

  try {
    // 确保bookElement可见
    if (bookElement.value) {
      bookElement.value.style.display = 'block';

      // 如果是全屏模式，调整尺寸
      if (props.fullscreen) {
        // 计算合适的尺寸，保持宽高比
        const aspectRatio = config.height / config.width;
        let newWidth, newHeight;

        // 横板模式下的全屏尺寸计算
        if (isLandscape.value) {
          // 横板模式下，宽度应该大于高度
          if (window.innerWidth / window.innerHeight > aspectRatio) {
            // 屏幕比例比书本更宽，以高度为基准
            newHeight = window.innerHeight * 0.85; // 留出一些边距
            newWidth = newHeight / aspectRatio;
          } else {
            // 屏幕比例比书本更窄，以宽度为基准
            newWidth = window.innerWidth * 0.9; // 横板模式下使用更大的宽度
            newHeight = newWidth * aspectRatio;
          }
        } else {
          // 竖版模式下的尺寸计算
          if (window.innerWidth / window.innerHeight > aspectRatio) {
            // 屏幕更宽，以高度为基准
            newHeight = window.innerHeight * 0.8; // 留出一些边距
            newWidth = newHeight / aspectRatio;
          } else {
            // 屏幕更高，以宽度为基准
            newWidth = window.innerWidth * 0.8; // 留出一些边距
            newHeight = newWidth * aspectRatio;
          }
        }

        bookElement.value.style.width = `${newWidth}px`;
        bookElement.value.style.height = `${newHeight}px`;
        console.log('全屏模式：bookElement已设置为可见，调整后尺寸:', newWidth, 'x', newHeight);
      } else {
        bookElement.value.style.width = `${config.width}px`;
        bookElement.value.style.height = `${config.height}px`;
        console.log('bookElement已设置为可见，尺寸:', config.width, 'x', config.height);
      }
    }

    console.log('开始加载PageFlip库...');
    try {
      await loadPageFlipLibrary();
      console.log('PageFlip库加载完成，准备创建实例');
    } catch (error) {
      console.error('加载PageFlip库失败:', error);
      isLoading.value = false;
      window.$message?.error('加载绘本阅读器组件失败，请刷新页面重试');
      return;
    }

    // Create PageFlip instance
    const PageFlip = window.PageFlip || window.St?.PageFlip;
    if (!PageFlip) {
      console.error('PageFlip库未加载成功，window.PageFlip和window.St.PageFlip均不存在');
      isLoading.value = false;
      window.$message?.error('初始化绘本阅读器失败，请刷新页面重试');
      return;
    }

    console.log('找到PageFlip构造函数，配置:', config);
    console.log('bookElement:', bookElement.value);

    try {
      pageFlip.value = new PageFlip(bookElement.value, config);
      console.log('PageFlip实例创建成功');
    } catch (instanceError) {
      console.error('创建PageFlip实例失败:', instanceError);
      isLoading.value = false;
      window.$message?.error('初始化绘本阅读器失败，请刷新页面重试');
      return;
    }

    // Create HTML pages
    console.log('开始创建HTML页面元素，页面数量:', props.pages.length);
    const htmlPages = props.pages.map((page, index) => {
      const pageElement = document.createElement('div');
      pageElement.className = 'page';

      if (page.type === 'hard' ||
          (props.showCover && (index === 0 || index === props.pages.length - 1))) {
        pageElement.setAttribute('data-density', 'hard');
        console.log(`第${index + 1}页设置为硬页面`);
      }

      const pageContent = document.createElement('div');
      pageContent.className = isLandscape.value ? 'page-content landscape-content' : 'page-content';

      // 获取页面布局类型，默认使用全局设置
      const pageLayout = page.layout || (isLandscape.value ? 'horizontal' : 'vertical');

      // 获取文本样式
      const textStyle = page.textStyle || {
        position: pageLayout === 'horizontal' ? 'right' : 'bottom',
        alignment: 'left',
        fontSize: 'medium',
        color: '#000000',
        backgroundColor: 'transparent',
        opacity: 1
      };

      // 设置页面内容容器的类名
      pageContent.className = `page-content layout-${pageLayout}`;

      // 创建页面布局容器
      const layoutContainer = document.createElement('div');
      layoutContainer.className = `layout-container layout-${pageLayout}`;

      // 创建图片容器
      if (page.image) {
        const imageContainer = document.createElement('div');
        imageContainer.className = `image-container image-${pageLayout}`;

        const imageElement = document.createElement('div');
        imageElement.className = 'page-image';
        imageElement.style.backgroundImage = `url(${page.image})`;

        imageContainer.appendChild(imageElement);
        layoutContainer.appendChild(imageContainer);
      }

      // 创建文本容器
      if (page.text) {
        const textContainer = document.createElement('div');
        textContainer.className = `text-container text-${pageLayout} text-position-${textStyle.position || 'bottom'}`;

        // 设置文本容器样式
        if (textStyle.backgroundColor && textStyle.backgroundColor !== 'transparent') {
          // 将十六进制颜色转换为rgba
          const hexColor = textStyle.backgroundColor.replace('#', '');
          const r = parseInt(hexColor.substring(0, 2), 16);
          const g = parseInt(hexColor.substring(2, 4), 16);
          const b = parseInt(hexColor.substring(4, 6), 16);
          const opacity = textStyle.opacity !== undefined ? textStyle.opacity : 1;

          textContainer.style.backgroundColor = `rgba(${r}, ${g}, ${b}, ${opacity})`;
        }

        const textElement = document.createElement('div');
        textElement.className = `page-text text-align-${textStyle.alignment || 'left'} text-size-${textStyle.fontSize || 'medium'}`;
        textElement.textContent = page.text;

        // 设置文本样式
        if (textStyle.color) {
          textElement.style.color = textStyle.color;
        }

        textContainer.appendChild(textElement);
        layoutContainer.appendChild(textContainer);
      }

      pageContent.appendChild(layoutContainer);

      // Add page number
      const pageNumber = document.createElement('div');
      pageNumber.className = 'page-number';
      pageNumber.textContent = (index + 1).toString();
      pageContent.appendChild(pageNumber);

      pageElement.appendChild(pageContent);
      return pageElement;
    });

    console.log('HTML页面元素创建完成，准备加载到PageFlip实例');

    try {
      // Load pages
      pageFlip.value.loadFromHTML(htmlPages);
      console.log('页面成功加载到PageFlip实例');

      // Add event listeners
      pageFlip.value.on('flip', (e) => {
        console.log('页面翻转事件触发，当前页面:', e.data);
        currentPage.value = e.data;
        emit('page-flip', e.data);
      });

      totalPages.value = props.pages.length;
      console.log('PageFlip初始化完成，总页数:', totalPages.value);

      // 确保bookElement在正确的位置显示
      if (bookElement.value) {
        // 将bookElement移动到book-container中
        const bookContainer = document.querySelector('.book-container');
        if (bookContainer) {
          bookContainer.appendChild(bookElement.value);
        }

        // 设置样式
        bookElement.value.style.position = 'relative';
        bookElement.value.style.zIndex = '10';
      }

      isLoading.value = false;
    } catch (loadError) {
      console.error('加载页面到PageFlip实例失败:', loadError);
      isLoading.value = false;
      window.$message?.error('加载绘本页面失败，请检查页面数据后重试');
    }
  } catch (error) {
    console.error('初始化PageFlip过程中发生错误:', error);
    isLoading.value = false;
    window.$message?.error('初始化绘本阅读器时发生错误，请刷新页面重试');
  }
};

// Navigation methods
const flipNext = () => {
  if (pageFlip.value) {
    pageFlip.value.flipNext();
  }
};

const flipPrev = () => {
  if (pageFlip.value) {
    pageFlip.value.flipPrev();
  }
};

// 使用横版模式作为默认模式

const closeReader = () => {
  // 如果是全屏模式，添加淡出动画
  if (props.fullscreen && bookElement.value) {
    const readerElement = document.querySelector('.book-reader');
    if (readerElement) {
      readerElement.classList.add('fade-out');
      setTimeout(() => {
        emit('close');
      }, 300); // 300ms后关闭，与CSS动画时间匹配
    } else {
      emit('close');
    }
  } else {
    emit('close');
  }
};

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (!pageFlip.value) return;

  switch (event.key) {
    case 'ArrowRight':
    case ' ': // 空格键
      flipNext();
      event.preventDefault();
      break;
    case 'ArrowLeft':
      flipPrev();
      event.preventDefault();
      break;
    case 'Escape':
      closeReader();
      event.preventDefault();
      break;
  }
};

// Lifecycle hooks
onMounted(() => {
  console.log('BookReader组件已挂载，开始初始化PageFlip');

  // 检查页面数据
  if (!props.pages || props.pages.length === 0) {
    console.error('没有提供页面数据或页面数据为空');
    isLoading.value = false;
    return;
  } else {
    console.log('页面数据检查通过，共', props.pages.length, '页');
  }

  // 如果是全屏模式，添加键盘事件监听器
  if (props.fullscreen) {
    window.addEventListener('keydown', handleKeyDown);
  }

  // 增加延迟时间，确保DOM已完全渲染
  setTimeout(() => {
    console.log('延迟结束，开始初始化PageFlip');
    console.log('bookElement存在状态:', !!bookElement.value);

    if (bookElement.value) {
      console.log('bookElement尺寸:', bookElement.value.offsetWidth, 'x', bookElement.value.offsetHeight);

      // 确保bookElement有正确的尺寸
      if (bookElement.value.offsetWidth === 0 || bookElement.value.offsetHeight === 0) {
        console.log('bookElement尺寸为0，设置初始尺寸');
        bookElement.value.style.width = `${config.width}px`;
        bookElement.value.style.height = `${config.height}px`;
      }
    } else {
      console.error('bookElement不存在，无法初始化PageFlip');
      isLoading.value = false;
      return;
    }

    // 检查全局环境
    console.log('window对象存在状态:', typeof window !== 'undefined');
    console.log('document对象存在状态:', typeof document !== 'undefined');

    initPageFlip();
  }, 1500); // 增加到1500毫秒，给DOM更多时间渲染
});

onBeforeUnmount(() => {
  console.log('BookReader组件即将卸载，清理PageFlip实例');

  // 移除键盘事件监听器
  if (props.fullscreen) {
    window.removeEventListener('keydown', handleKeyDown);
  }

  if (pageFlip.value) {
    try {
      pageFlip.value.destroy();
      console.log('PageFlip实例已销毁');
    } catch (error) {
      console.error('销毁PageFlip实例时发生错误:', error);
    }
  }
});

// Watch for changes in pages
watch(() => props.pages, () => {
  if (pageFlip.value) {
    pageFlip.value.destroy();
    initPageFlip();
  }
}, { deep: true });

// Declare PageFlip on window
declare global {
  interface Window {
    PageFlip?: any;
    St?: {
      PageFlip: any;
    };
  }
}
</script>

<template>
  <div class="book-reader" :class="{ 'fullscreen-mode': props.fullscreen }">
    <div class="book-reader-container">
      <!-- 始终渲染book元素，但可能隐藏它 -->
      <div ref="bookElement" class="book" style="display: none;"></div>

      <div v-if="isLoading" class="book-loading">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <div v-else class="book-content">
        <!-- 当加载完成后，bookElement会自动显示 -->
        <div class="book-container"></div>
      </div>

      <div class="book-pagination">
        <span>{{ currentPage + 1 }} / {{ totalPages }}</span>
      </div>

      <!-- 关闭按钮 -->
      <button class="close-button" @click="closeReader" title="关闭">
        <span class="close-emoji">❌</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.book-reader {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

/* 淡出动画 */
.fade-out {
  opacity: 0;
}

.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9990;
  border-radius: 0;
  background-color: rgba(0, 0, 0, 0.9); /* 深色背景 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.fullscreen-mode .book-reader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.fullscreen-mode .book-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fullscreen-mode .book {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.dark .book-reader {
  background-color: #1a1a1a;
}

.fullscreen-mode .book-pagination {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  font-size: 16px;
  z-index: 10000;
}

/* 关闭按钮样式 */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10000;
  transition: transform 0.3s;
}

.close-emoji {
  font-size: 20px;
  line-height: 1;
}

.close-button:hover {
  transform: scale(1.1);
}

.dark .close-button {
  background: rgba(255, 255, 255, 0.2);
}

.book-reader-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.book-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 16px;
}

.dark .loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top-color: #3b82f6;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.book-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.book {
  height: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: white;
  min-width: 320px;
  min-height: 420px;
  margin: 0 auto;
}

.book-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.dark .book {
  background-color: #2a2a2a;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.book-navigation {
  display: flex;
  align-items: center;
  margin: 0 20px;
}

.nav-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.fullscreen-mode .nav-button {
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  width: 50px;
  height: 50px;
}

.nav-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .nav-button {
  color: #ccc;
}

.dark .nav-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.book-pagination {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

.dark .book-pagination {
  color: #ccc;
}

/* Page styles */
:deep(.page) {
  background-color: white;
  color: #333;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.dark .page) {
  background-color: #2a2a2a;
  color: #eee;
}

:deep(.page-content) {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
  position: relative;
}

/* 页面内容样式 */
:deep(.page-content) {
  padding: 20px;
}

/* 布局容器 */
:deep(.layout-container) {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
}

/* 不同布局样式 */
:deep(.layout-vertical) {
  flex-direction: column;
}

:deep(.layout-horizontal) {
  flex-direction: row;
}

:deep(.layout-horizontal-reverse) {
  flex-direction: row-reverse;
}

:deep(.layout-full-image) {
  position: relative;
}

:deep(.layout-collage) {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4px;
}

/* 图片容器样式 */
:deep(.image-container) {
  overflow: hidden;
  position: relative;
}

:deep(.image-vertical) {
  width: 100%;
  height: 75%; /* 标准绘本比例，图片占3/4 */
}

:deep(.image-horizontal) {
  width: 75%; /* 标准绘本比例，图片占3/4 */
  height: 100%;
}

:deep(.image-horizontal-reverse) {
  width: 75%; /* 标准绘本比例，图片占3/4 */
  height: 100%;
}

:deep(.image-full-image) {
  width: 100%;
  height: 100%;
}

:deep(.image-collage) {
  width: 50%;
  height: 50%;
}

/* 文本容器样式 */
:deep(.text-container) {
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.text-vertical) {
  width: 100%;
  height: 25%; /* 标准绘本比例，文字占1/4 */
}

:deep(.text-horizontal) {
  width: 25%; /* 标准绘本比例，文字占1/4 */
  height: 100%;
}

:deep(.text-horizontal-reverse) {
  width: 25%; /* 标准绘本比例，文字占1/4 */
  height: 100%;
}

:deep(.text-full-image) {
  position: absolute;
  width: 100%;
}

:deep(.text-position-bottom) {
  bottom: 0;
  height: 30%;
}

:deep(.text-position-top) {
  top: 0;
  height: 30%;
}

:deep(.text-position-left) {
  left: 0;
  width: 30%;
  height: 100%;
}

:deep(.text-position-right) {
  right: 0;
  width: 30%;
  height: 100%;
}

:deep(.text-position-overlay) {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.text-collage) {
  width: 50%;
  height: 50%;
}

/* 图片样式 */
:deep(.page-image) {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 4px;
}

/* 文本样式 */
:deep(.page-text) {
  padding: 15px;
  line-height: 1.5;
}

:deep(.text-align-left) {
  text-align: left;
}

:deep(.text-align-center) {
  text-align: center;
}

:deep(.text-align-right) {
  text-align: right;
}

:deep(.text-size-small) {
  font-size: 14px;
}

:deep(.text-size-medium) {
  font-size: 16px;
}

:deep(.text-size-large) {
  font-size: 20px;
}

:deep(.page-number) {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 12px;
  color: #999;
}

:deep(.dark .page-number) {
  color: #666;
}

/* 横板模式下的书本样式 */
.book-reader.landscape-mode .book {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

/* 横板模式下的全屏样式 */
.fullscreen-mode.landscape-mode .book-content {
  padding: 0 40px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .book-reader-container {
    padding: 10px;
  }

  .book-navigation {
    margin: 0 10px;
  }

  :deep(.page-content) {
    padding: 10px;
  }

  :deep(.page-text) {
    font-size: 14px;
  }

  :deep(.landscape-text) {
    font-size: 16px;
  }

  :deep(.landscape-content-wrapper) {
    flex-direction: column;
  }

  :deep(.landscape-image) {
    flex: 0 0 200px;
    margin-bottom: 15px;
    min-height: 150px;
  }
}
</style>
