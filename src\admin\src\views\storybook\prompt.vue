<template>
  <div>
    <el-card class="mt-4 mb-4 proCard">
      <div class="flex items-center justify-between mb-4">
        <div>
          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增提示词
          </el-button>
          <el-button class="ml-2" @click="handleRefresh">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
        </div>
        <div class="flex items-center">
          <el-input
            v-model="searchParams.keyword"
            placeholder="请输入提示词"
            clearable
            class="w-200px mr-2"
            @keyup.enter="handleSearch"
          />
          <el-select
            v-model="searchParams.type"
            placeholder="类型"
            clearable
            class="w-150px mr-2"
            @change="handleSearch"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button type="primary" @click="handleSearch">
            <el-icon>
              <Search />
            </el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <el-table
        ref="table"
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        :row-key="(row) => row.id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" width="200" />
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="content" label="内容" width="300" show-overflow-tooltip />
        <el-table-column prop="order" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'danger'" size="small">
              {{ scope.row.status ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="flex space-x-2">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                <el-icon>
                  <Edit />
                </el-icon>
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                <el-icon>
                  <Delete />
                </el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.itemCount"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 提示词表单对话框 -->
    <el-dialog
      v-model="showFormModal"
      :title="isEdit ? '编辑提示词' : '新增提示词'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            placeholder="请输入提示词内容"
            :rows="5"
          />
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="formData.order" :min="0" :max="9999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="formData.status"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="showFormModal = false">取消</el-button>
          <el-button
            type="primary"
            class="ml-2"
            :loading="submitting"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { Plus, Refresh, Search, Delete, Edit } from '@element-plus/icons-vue';
import ApiStorybook from '@/api/modules/storybook';

// 类型选项
const typeOptions = [
  { label: '角色设定', value: '角色设定' },
  { label: '情节发展', value: '情节发展' },
  { label: '场景描述', value: '场景描述' },
  { label: '对话生成', value: '对话生成' },
  { label: '其他', value: '其他' },
];

// 搜索参数
const searchParams = reactive({
  keyword: '',
  type: null,
  page: 1,
  limit: 10,
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageSizes: [10, 20, 30, 40],
});

// 表单相关
const formRef = ref<FormInstance>();
const showFormModal = ref(false);
const isEdit = ref(false);
const submitting = ref(false);
const formData = reactive({
  id: 0,
  title: '',
  type: '',
  content: '',
  order: 100,
  status: true,
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入提示词内容', trigger: 'blur' }
  ],
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const res = await ApiStorybook.getPromptList(searchParams);
    tableData.value = res.data.items;
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error('加载提示词列表失败', error);
    ElMessage.error('加载提示词列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  searchParams.page = page;
  loadData();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  searchParams.limit = size;
  searchParams.page = 1;
  pagination.page = 1;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  searchParams.page = 1;
  pagination.page = 1;
  loadData();
};

// 处理刷新
const handleRefresh = () => {
  loadData();
};

// 处理新增
const handleAdd = () => {
  isEdit.value = false;
  formData.id = 0;
  formData.title = '';
  formData.type = '';
  formData.content = '';
  formData.order = 100;
  formData.status = true;
  showFormModal.value = true;
};

// 处理编辑
const handleEdit = (row: any) => {
  isEdit.value = true;
  formData.id = row.id;
  formData.title = row.title;
  formData.type = row.type;
  formData.content = row.content;
  formData.order = row.order;
  formData.status = row.status === 1;
  showFormModal.value = true;
};

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除提示词 "${row.title}" 吗？此操作不可撤销！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await ApiStorybook.deletePrompt(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败', error);
      ElMessage.error('删除失败');
    }
  }
};

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (!valid) {
      return;
    }

    submitting.value = true;
    try {
      const data = {
        ...formData,
        status: formData.status ? 1 : 0,
      };

      if (isEdit.value) {
        await ApiStorybook.updatePrompt(formData.id, data);
        ElMessage.success('更新成功');
      } else {
        await ApiStorybook.createPrompt(data);
        ElMessage.success('创建成功');
      }

      showFormModal.value = false;
      loadData();
    } catch (error) {
      console.error('提交失败', error);
      ElMessage.error('提交失败');
    } finally {
      submitting.value = false;
    }
  });
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.proCard {
  border-radius: 4px;
}
</style>
