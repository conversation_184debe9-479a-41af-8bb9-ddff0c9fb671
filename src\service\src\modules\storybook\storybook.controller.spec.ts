import { Test, TestingModule } from '@nestjs/testing';
import { StorybookController } from './storybook.controller';
import { StorybookService } from './storybook.service';
import { CreateStorybookDto, UpdateStorybookDto, CreatePageDto, CreateCharacterDto } from './dto';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';
import { JwtAuthGuard } from 'src/common/auth/jwtAuth.guard';
import { NotFoundException, BadRequestException } from '@nestjs/common';

// 添加Jest类型声明
import 'jest';

// 创建模拟StorybookService
const mockStorybookService = () => ({
  createStorybook: jest.fn(),
  getUserStorybooks: jest.fn(),
  getPublicStorybooks: jest.fn(),
  getStorybookDetail: jest.fn(),
  updateStorybook: jest.fn(),
  deleteStorybook: jest.fn(),
  createPage: jest.fn(),
  updatePage: jest.fn(),
  deletePage: jest.fn(),
  getStorybookPages: jest.fn(),
  createCharacter: jest.fn(),
  updateCharacter: jest.fn(),
  deleteCharacter: jest.fn(),
  getStorybookCharacters: jest.fn(),
  getCharacterTemplates: jest.fn(),
  getConfig: jest.fn(),
  createImage: jest.fn(),
  getImages: jest.fn(),
  getImageDetail: jest.fn(),
  updateImage: jest.fn(),
  deleteImage: jest.fn(),
  getImageGenerationConfig: jest.fn(),
  filterSensitiveContent: jest.fn(),
  checkContent: jest.fn(),
});

// 创建模拟请求对象
const mockRequest = () => ({
  user: {
    id: 1,
    username: 'testuser',
  },
});

describe('StorybookController', () => {
  let controller: StorybookController;
  let service: StorybookService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StorybookController],
      providers: [
        {
          provide: StorybookService,
          useFactory: mockStorybookService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<StorybookController>(StorybookController);
    service = module.get<StorybookService>(StorybookService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  // 测试创建绘本 - ST-001
  describe('createStorybook', () => {
    it('应该成功创建绘本', async () => {
      const dto: CreateStorybookDto = {
        title: '测试绘本',
        description: '测试描述',
      };
      const mockResult = { id: 1, ...dto, userId: 1 };

      jest.spyOn(service, 'createStorybook').mockResolvedValue(mockResult as any);

      const result = await controller.createStorybook(dto, mockRequest() as any);

      expect(result).toEqual(mockResult);
      expect(service.createStorybook).toHaveBeenCalledWith(1, dto);
    });
  });

  // 测试获取用户绘本列表 - ST-002
  describe('getUserStorybooks', () => {
    it('应该返回用户绘本列表', async () => {
      const mockResult = {
        items: [{ id: 1, title: '测试绘本' }],
        total: 1,
        page: 1,
        limit: 10,
      };

      jest.spyOn(service, 'getUserStorybooks').mockResolvedValue(mockResult as any);

      const result = await controller.getUserStorybooks(
        mockRequest() as any,
        1,
        10,
        1,
        '测试'
      );

      expect(result).toEqual(mockResult);
      expect(service.getUserStorybooks).toHaveBeenCalledWith(1, {
        page: 1,
        limit: 10,
        status: 1,
        keyword: '测试',
      });
    });
  });

  // 测试获取公开绘本列表
  describe('getPublicStorybooks', () => {
    it('应该返回公开绘本列表', async () => {
      const mockResult = {
        items: [{ id: 1, title: '公开绘本', isPublic: 1 }],
        total: 1,
        page: 1,
        limit: 10,
      };

      jest.spyOn(service, 'getPublicStorybooks').mockResolvedValue(mockResult as any);

      const result = await controller.getPublicStorybooks(1, 10, '公开');

      expect(result).toEqual(mockResult);
      expect(service.getPublicStorybooks).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        keyword: '公开',
      });
    });
  });

  // 测试获取绘本详情 - ST-003
  describe('getStorybookDetail', () => {
    it('应该返回绘本详情', async () => {
      const mockStorybook = {
        id: 1,
        title: '测试绘本',
        description: '测试描述',
        pages: [],
        characters: [],
      };

      jest.spyOn(service, 'getStorybookDetail').mockResolvedValue(mockStorybook as any);

      const result = await controller.getStorybookDetail(1, mockRequest() as any);

      expect(result).toEqual(mockStorybook);
      expect(service.getStorybookDetail).toHaveBeenCalledWith(1, 1);
    });

    it('当绘本不存在时应该抛出异常', async () => {
      jest.spyOn(service, 'getStorybookDetail').mockRejectedValue(new NotFoundException('绘本不存在'));

      await expect(controller.getStorybookDetail(999, mockRequest() as any)).rejects.toThrow(NotFoundException);
    });
  });

  // 测试更新绘本 - ST-004
  describe('updateStorybook', () => {
    it('应该成功更新绘本', async () => {
      const updateDto: UpdateStorybookDto = {
        title: '更新后的标题',
        description: '更新后的描述',
      };

      const mockResult = {
        id: 1,
        ...updateDto,
        userId: 1,
      };

      jest.spyOn(service, 'updateStorybook').mockResolvedValue(mockResult as any);

      const result = await controller.updateStorybook(1, updateDto, mockRequest() as any);

      expect(result).toEqual(mockResult);
      expect(service.updateStorybook).toHaveBeenCalledWith(1, 1, updateDto);
    });
  });

  // 测试删除绘本 - ST-005
  describe('deleteStorybook', () => {
    it('应该成功删除绘本', async () => {
      jest.spyOn(service, 'deleteStorybook').mockResolvedValue(undefined);

      const result = await controller.deleteStorybook(1, mockRequest() as any);

      expect(result).toEqual({ success: true, message: '删除成功' });
      expect(service.deleteStorybook).toHaveBeenCalledWith(1, 1);
    });
  });

  // 测试创建绘本页面 - ST-008
  describe('createPage', () => {
    it('应该成功创建绘本页面', async () => {
      const createPageDto: CreatePageDto = {
        storybookId: 1,
        pageNumber: 1,
        text: '页面内容',
        imageUrl: 'https://example.com/image.jpg',
      };

      const mockResult = {
        id: 1,
        ...createPageDto,
      };

      jest.spyOn(service, 'createPage').mockResolvedValue(mockResult as any);

      // 根据控制器实际实现调用方法
      const result = await controller.createPage(createPageDto);

      expect(result).toEqual(mockResult);
      expect(service.createPage).toHaveBeenCalledWith(createPageDto);
    });
  });

  // 测试创建绘本角色 - ST-011
  describe('createCharacter', () => {
    it('应该成功创建绘本角色', async () => {
      const createCharacterDto: CreateCharacterDto = {
        storybookId: 1,
        name: '角色名称',
        characterType: '主角',
        appearance: '外观描述',
        personalityTraits: { trait1: '性格特点1', trait2: '性格特点2' },
      };

      const mockResult = {
        id: 1,
        ...createCharacterDto,
      };

      jest.spyOn(service, 'createCharacter').mockResolvedValue(mockResult as any);

      // 根据控制器实际实现调用方法
      const result = await controller.createCharacter(createCharacterDto);

      expect(result).toEqual(mockResult);
      expect(service.createCharacter).toHaveBeenCalledWith(createCharacterDto);
    });
  });

  // 测试获取配置 - ST-036
  describe('getConfig', () => {
    it('应该返回公开配置', async () => {
      const mockConfig = { configKey: 'test_key', configVal: 'test_value', public: 1 };

      jest.spyOn(service, 'getConfig').mockResolvedValue(mockConfig as any);

      const result = await controller.getConfig('test_key');

      expect(result).toEqual(mockConfig);
      expect(service.getConfig).toHaveBeenCalledWith('test_key');
    });

    it('当配置不存在或不公开时应该返回空配置', async () => {
      jest.spyOn(service, 'getConfig').mockResolvedValue(null);

      const result = await controller.getConfig('private_key');

      expect(result).toEqual({ configKey: 'private_key', configVal: '', public: 0 });
    });
  });

  // 测试创建图像 - ST-022
  describe('createImage', () => {
    it('应该成功创建图像记录', async () => {
      const createImageDto: CreateImageDto = {
        imageUrl: 'https://example.com/image.jpg',
        description: '图像描述',
        imageType: 1,
      };

      const mockResult = {
        id: 1,
        ...createImageDto,
        userId: 1,
      };

      jest.spyOn(service, 'createImage').mockResolvedValue(mockResult as any);

      const result = await controller.createImage(createImageDto, mockRequest() as any);

      expect(result).toEqual(mockResult);
      expect(service.createImage).toHaveBeenCalledWith(1, createImageDto);
    });
  });

  // 测试内容安全检查 - ST-042
  describe('checkContent', () => {
    it('应该检查内容是否包含敏感词', async () => {
      const mockResult = { safe: true, triggeredWords: [] };

      jest.spyOn(service, 'checkContent').mockResolvedValue(mockResult);

      const result = await controller.checkContent('安全内容', mockRequest() as any);

      expect(result).toEqual(mockResult);
      expect(service.checkContent).toHaveBeenCalledWith('安全内容', 1);
    });

    it('当内容包含敏感词时应该返回不安全结果', async () => {
      const mockResult = { safe: false, triggeredWords: ['敏感'] };

      jest.spyOn(service, 'checkContent').mockResolvedValue(mockResult);

      const result = await controller.checkContent('不安全内容包含敏感词', mockRequest() as any);

      expect(result).toEqual(mockResult);
      expect(service.checkContent).toHaveBeenCalledWith('不安全内容包含敏感词', 1);
    });
  });
});
