/**
 * 操作类型枚举
 */
export enum OperationType {
  CLICK = 'click',
  INPUT = 'input',
  SCROLL = 'scroll',
}

/**
 * 点击操作数据接口
 */
export interface ClickOperationData {
  x: number;
  y: number;
  target: string;
  id?: string;
  className?: string;
}

/**
 * 输入操作数据接口
 */
export interface InputOperationData {
  value: string;
  target: string;
  id?: string;
  name?: string;
}

/**
 * 滚动操作数据接口
 */
export interface ScrollOperationData {
  scrollX: number;
  scrollY: number;
}

/**
 * 操作数据接口
 */
export type OperationData = ClickOperationData | InputOperationData | ScrollOperationData;

/**
 * 操作记录接口
 */
export interface Operation {
  type: OperationType;
  data: OperationData;
  timestamp: string;
}

/**
 * 会话操作记录接口
 */
export interface SessionOperations {
  sessionId: string;
  operations: Operation[];
  createdAt: string;
  updatedAt: string;
}
