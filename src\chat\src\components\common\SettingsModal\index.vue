<script setup lang="ts">
import { computed, ref, markRaw, watch, onMounted } from 'vue';
import { NModal, NTabs, NTabPane, useMessage } from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useAuthStore, useGlobalStoreWithOut, useAppStore, useChatStore } from '@/store';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { fetchGetGlobalNoticeAPI } from '@/api/global';

// 导入各个设置页面组件
import Profile from '@/components/common/UserCenter/Profile.vue';
import Wallet from '@/components/common/UserCenter/Wallet.vue';
import Password from '@/components/common/UserCenter/Password.vue';
import HtmlManager from '@/components/common/UserCenter/HtmlManager.vue';
import NoticeContent from '@/layout/components/Settings/NoticeDialog.vue';
import SignInContent from '@/layout/components/Settings/SignInDialog.vue';

interface Props {
  visible: boolean;
}

interface Emit {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const authStore = useAuthStore();
const useGlobalStore = useGlobalStoreWithOut();
const appStore = useAppStore();
const chatStore = useChatStore();
const { isMobile } = useBasicLayout();
const ms = useMessage();

const activeTab = ref('notice');
const darkMode = computed(() => appStore.theme === 'dark');
const signInStatus = computed(() => Number(authStore.globalConfig?.signInStatus) === 1);

// 使用计算属性来处理双向绑定
const show = computed({
  get() {
    return props.visible;
  },
  set(visible: boolean) {
    emit('update:visible', visible);
  },
});

// 标签页配置
const tabs = [
  {
    key: 'notice',
    label: '网站公告',
    icon: 'ri:notification-line',
    component: markRaw(NoticeContent),
  },
  {
    key: 'profile',
    label: '基本信息',
    icon: 'ri:user-3-line',
    component: markRaw(Profile),
  },
  {
    key: 'wallet',
    label: '我的钱包',
    icon: 'ri:wallet-3-line',
    component: markRaw(Wallet),
  },
  {
    key: 'password',
    label: '密码管理',
    icon: 'ri:lock-password-line',
    component: markRaw(Password),
  },
  {
    key: 'html',
    label: 'HTML管理',
    icon: 'ri:html5-line',
    component: markRaw(HtmlManager),
  }
];

// 打开商城
function openPointsMall() {
  useGlobalStore.updateGoodsDialog(true);
  show.value = false;
}

// 如果签到功能开启，添加签到标签
if (signInStatus.value) {
  tabs.splice(1, 0, {
    key: 'signin',
    label: '签到奖励',
    icon: 'ri:calendar-check-line',
    component: markRaw(SignInContent),
  });
}

// 加载公告内容
async function loadNotice() {
  try {
    const res = await fetchGetGlobalNoticeAPI();
    if (res.success && res.data) {
      // 更新全局配置中的公告信息
      authStore.globalConfig.noticeInfo = res.data.noticeInfo || '';
      authStore.globalConfig.noticeTitle = res.data.noticeTitle || '';
    }
  } catch (error) {
    console.error('Failed to load notice:', error);
  }
}

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 弹窗显示时加载公告
    await loadNotice();
  }
});

// 组件挂载时加载公告
onMounted(async () => {
  if (props.visible) {
    await loadNotice();
  }
});

// 关闭弹窗
function handleClose() {
  show.value = false;
}

// 清除所有聊天记录
function handleClearChats() {
  // 使用原有的清除聊天记录功能
  window.$dialog?.warning({
    title: '清空对话',
    content: '确认清空所有非置顶的对话？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      await chatStore.delAllGroup();
      await chatStore.addNewChatGroup();
      ms.success('已清空所有非置顶对话');
    },
  });
}

// 退出登录
function handleLogout() {
  authStore.logOut();
  show.value = false;
}
</script>

<template>
  <NModal
    v-model:show="show"
    title="设置中心"
    preset="card"
    :style="{ width: isMobile ? '95%' : '800px', maxWidth: '95vw' }"
    :auto-focus="false"
    class="settings-modal"
  >
    <div class="settings-container">
      <NTabs
        v-model:value="activeTab"
        type="line"
        animated
        class="settings-tabs"
        :class="{ 'mobile-tabs': isMobile }"
      >
        <NTabPane
          v-for="tab in tabs"
          :key="tab.key"
          :name="tab.key"
          :tab="tab.label"
        >
          <template #tab>
            <div class="flex items-center">
              <SvgIcon class="text-lg mr-1" :icon="tab.icon" />
              <span>{{ tab.label }}</span>
            </div>
          </template>

          <!-- 动态组件 -->
          <component :is="tab.component" />
        </NTabPane>
      </NTabs>

      <!-- 底部操作按钮 -->
      <div class="settings-footer">
        <div class="flex justify-between mt-4 border-t pt-4 border-gray-100 dark:border-gray-700">
          <div class="flex gap-2">
            <button
              @click="handleClearChats"
              class="px-4 py-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 hover-float"
            >
              <div class="flex items-center">
                <SvgIcon icon="ri:delete-bin-line" class="mr-1" />
                <span>清空对话</span>
              </div>
            </button>

            <button
              @click="openPointsMall"
              class="px-4 py-2 rounded-md bg-primary-500 text-white hover:bg-primary-600 transition-all duration-300 hover-float"
            >
              <div class="flex items-center">
                <SvgIcon icon="ri:shopping-cart-2-line" class="mr-1" />
                <span>积分商城</span>
              </div>
            </button>
          </div>

          <button
            @click="handleLogout"
            class="px-4 py-2 rounded-md bg-red-500 text-white hover:bg-red-600 transition-all duration-300 hover-float"
          >
            <div class="flex items-center">
              <SvgIcon icon="ri:logout-box-line" class="mr-1" />
              <span>退出登录</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.settings-container {
  min-height: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.settings-tabs {
  height: 100%;
}

.mobile-tabs :deep(.n-tabs-nav) {
  padding: 0 8px;
}

.settings-modal :deep(.n-card-header) {
  padding-bottom: 8px;
  border-bottom: 1px solid var(--n-border-color);
}

.settings-modal :deep(.n-card-header__main) {
  font-size: 18px;
  font-weight: 600;
}

.settings-footer {
  margin-top: 16px;
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条 */
.settings-container::-webkit-scrollbar {
  width: 6px;
}

.settings-container::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.settings-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

.settings-container::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>
