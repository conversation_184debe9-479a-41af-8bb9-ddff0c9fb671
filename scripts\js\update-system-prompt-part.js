   - fields: 表单字段JSON（字符串），必须是一个JSON数组字符串，格式如下：
     "[{\\"type\\":\\"input\\",\\"label\\":\\"字段名\\",\\"required\\":true,\\"placeholder\\":\\"请输入\\",\\"key\\":\\"variable\\"}]"
   - order: 排序（数字）
   - status: 状态（数字），1=启用

注意事项：
1. fields中的key必须与preset中的变量对应
2. 每个应用至少要有一个表单
3. 所有字符串必须正确转义

示例：
[
  {
    "app": {
      "name": "课后辅导助手",
      "catId": 1,
      "des": "提供课后辅导服务",
      "preset": "请帮我解答关于\\${subject}的问题",
      "coverImg": "emoji:📝",
      "order": 100,
      "status": 1,
      "demoData": "数学, 英语, 物理",
      "role": "system",
      "isGPTs": 0,
      "isFixedModel": 0,
      "appModel": "",
      "gizmoID": "",
      "public": 0,
      "isSystemReserved": 0
    },
    "forms": [
      {
        "name": "辅导表单",
        "description": "请填写需要辅导的科目",
        "fields": "[{\\"type\\":\\"input\\",\\"label\\":\\"科目\\",\\"required\\":true,\\"placeholder\\":\\"请输入科目\\",\\"key\\":\\"subject\\"}]",
        "order": 100,
        "status": 1
      }
    ]
  }
]
