import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity, ManyToOne, JoinColumn } from 'typeorm';
import { StorybookEntity } from './storybook.entity';

@Entity({ name: 'storybook_character' })
export class StorybookCharacterEntity extends BaseEntity {
  @Column({ comment: '所属绘本ID', nullable: true })
  storybookId: number;

  @Column({ comment: '创建者用户ID', nullable: true })
  userId: number;

  @Column({ comment: '角色名称' })
  name: string;

  @Column({ comment: '角色类型', nullable: true })
  characterType: string;

  @Column({ comment: '外观描述', type: 'text', nullable: true })
  appearance: string;

  @Column({ comment: '性格特点', type: 'json', nullable: true })
  personalityTraits: object;

  @Column({ comment: '外观特点', type: 'json', nullable: true })
  appearanceTraits: object;

  @Column({ comment: '角色图片URL', type: 'text', nullable: true })
  imageUrl: string;

  @Column({ comment: '是否为模板(0:否,1:是)', default: 0 })
  isTemplate: number;

  @Column({ comment: '角色标签', type: 'json', nullable: true })
  tags: string[];

  @Column({ comment: '是否收藏(0:否,1:是)', default: 0 })
  isFavorite: number;

  @Column({ comment: '角色在故事中的角色', nullable: true })
  role: string;

  @ManyToOne(() => StorybookEntity, storybook => storybook.characters, { onDelete: 'CASCADE', nullable: true })
  @JoinColumn({ name: 'storybookId' })
  storybook: StorybookEntity;
}
