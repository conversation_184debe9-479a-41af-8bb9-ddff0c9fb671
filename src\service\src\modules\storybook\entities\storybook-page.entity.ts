import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity, ManyToOne, JoinColumn } from 'typeorm';
import { StorybookEntity } from './storybook.entity';

@Entity({ name: 'storybook_page' })
export class StorybookPageEntity extends BaseEntity {
  @Column({ comment: '所属绘本ID' })
  storybookId: number;

  @Column({ comment: '页码' })
  pageNumber: number;

  @Column({ comment: '页面文本内容', type: 'text', nullable: true })
  text: string;

  @Column({ comment: '页面图片URL', type: 'text', nullable: true })
  imageUrl: string;

  @Column({ comment: '布局类型', default: 'vertical' })
  layout: string;

  @Column({ comment: '文本样式配置', type: 'json', nullable: true })
  textStyle: object;

  @Column({ comment: '场景元素', type: 'json', nullable: true })
  sceneElements: object;

  @Column({ comment: '画面描述', type: 'text', nullable: true })
  imageDescription: string;

  @ManyToOne(() => StorybookEntity, storybook => storybook.pages, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'storybookId' })
  storybook: StorybookEntity;
}
