// 角色库存储服务
import { ss } from './storage';
import {
  getLocalCharacters,
  saveLocalCharacters,
  saveCharacterToServer,
  deleteCharacterFromServer,
  syncCharacterLibrary
} from './characterService';

// 角色类型定义
export interface Character {
  id: number;
  name: string;
  characterType: string;
  personalityTraits: string[] | { traits: string[] } | object;
  appearance: string;
  appearanceTraits: string[] | object;
  imageUrl: string; // 统一使用imageUrl字段，与后端保持一致
  createdAt: string;
  updatedAt: string;
  // 可选字段
  role?: string;
  tags?: string[];
  isFavorite?: number; // 使用数字(0|1)与后端保持一致
  storybookId?: number; // 添加所属绘本ID
  userId?: number; // 添加创建者用户ID
  isTemplate?: number; // 添加是否为模板标记

  // 兼容旧版本的字段，内部使用
  _image?: string; // 旧版本使用的image字段
  traits?: string[]; // 旧版本使用的traits字段
}

/**
 * 获取角色库
 */
export function getCharacterLibrary(): Character[] {
  return getLocalCharacters();
}

/**
 * 保存角色库
 */
export function saveCharacterLibrary(characters: Character[]): void {
  saveLocalCharacters(characters);
}

/**
 * 添加角色到角色库
 */
export async function addCharacterToLibrary(character: Character): Promise<Character> {
  const library = getCharacterLibrary();

  // 确保角色有ID和时间戳
  const newCharacter = {
    ...character,
    id: character.id || Date.now() + Math.floor(Math.random() * 1000),
    createdAt: character.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // 检查是否已存在相同ID的角色
  const existingIndex = library.findIndex(c => c.id === newCharacter.id);

  if (existingIndex >= 0) {
    // 更新现有角色
    library[existingIndex] = newCharacter;
  } else {
    // 添加新角色
    library.push(newCharacter);
  }

  // 保存更新后的角色库
  saveCharacterLibrary(library);

  // 尝试保存到服务器
  const savedCharacter = await saveCharacterToServer(newCharacter);
  if (savedCharacter) {
    // 如果服务器保存成功，更新本地角色ID
    const updatedLibrary = getCharacterLibrary();
    const idx = updatedLibrary.findIndex(c => c.id === newCharacter.id);
    if (idx >= 0) {
      updatedLibrary[idx] = savedCharacter;
      saveCharacterLibrary(updatedLibrary);
      return savedCharacter;
    }
  }

  return newCharacter;
}

/**
 * 从角色库中删除角色
 * @param characterId 要删除的角色ID
 * @returns 删除是否成功
 */
export async function removeCharacterFromLibrary(characterId: number): Promise<boolean> {
  // 获取当前角色库
  const library = getCharacterLibrary();
  const initialLength = library.length;

  // 查找要删除的角色
  const character = library.find(c => c.id === characterId);

  // 如果角色不存在，返回false
  if (!character) {
    console.warn(`[角色库] 要删除的角色ID:${characterId}不存在于本地角色库中`);
    return false;
  }

  // 过滤掉要删除的角色
  const filteredLibrary = library.filter(c => c.id !== characterId);

  // 如果过滤后长度变化，说明找到了要删除的角色
  if (filteredLibrary.length !== initialLength) {
    // 尝试从服务器删除
    let serverDeleteSuccess = true; // 默认为成功
    let serverError = null;

    try {
      // 如果是服务器角色（ID小于1000000），先尝试从服务器删除
      if (characterId < 1000000) {
        console.log(`[角色库] 尝试从服务器删除角色ID:${characterId}, 名称:"${character.name}"`);
        serverDeleteSuccess = await deleteCharacterFromServer(characterId);

        if (serverDeleteSuccess) {
          console.log(`[角色库] 成功从服务器删除角色ID:${characterId}`);
        } else {
          console.warn(`[角色库] 从服务器删除角色ID:${characterId}失败，但仍会删除本地角色`);
        }
      } else {
        console.log(`[角色库] 角色ID:${characterId}是本地角色，无需从服务器删除`);
      }
    } catch (error) {
      serverError = error;
      console.error('[角色库] 从服务器删除角色时出错:', error);
      // 即使服务器删除失败，也继续删除本地角色
    }

    // 无论服务器删除是否成功，都删除本地角色
    console.log(`[角色库] 从本地角色库中删除角色ID:${characterId}, 名称:"${character.name}"`);
    saveCharacterLibrary(filteredLibrary);

    // 如果服务器删除失败且有明确的错误（非404），可以考虑抛出异常
    // 但为了保持兼容性，我们仍然返回true表示本地删除成功
    if (!serverDeleteSuccess && serverError && characterId < 1000000) {
      console.warn('[角色库] 服务器删除失败，但本地删除成功');
    }

    return true;
  }

  console.warn(`[角色库] 未能删除角色ID:${characterId}，可能是过滤操作失败`);
  return false;
}

/**
 * 获取角色库中的角色
 */
export function getCharacterFromLibrary(characterId: number): Character | null {
  const library = getCharacterLibrary();
  return library.find(c => c.id === characterId) || null;
}

/**
 * 导出角色库为JSON字符串
 */
export function exportCharacterLibrary(): string {
  const library = getCharacterLibrary();
  return JSON.stringify(library);
}

/**
 * 从JSON字符串导入角色库
 */
export function importCharacterLibrary(jsonString: string): boolean {
  try {
    const characters = JSON.parse(jsonString);

    if (!Array.isArray(characters)) {
      return false;
    }

    // 验证每个角色对象
    const validCharacters = characters.filter(c =>
      c && typeof c === 'object' &&
      c.name && typeof c.name === 'string'
    );

    saveCharacterLibrary(validCharacters);
    return true;
  } catch (error) {
    console.error('导入角色库失败:', error);
    return false;
  }
}

/**
 * 同步角色库（合并本地和服务器数据）
 */
export async function synchronizeCharacterLibrary(): Promise<Character[]> {
  return await syncCharacterLibrary();
}

/**
 * 设置角色为收藏
 */
export function toggleCharacterFavorite(characterId: number): boolean {
  const library = getCharacterLibrary();
  const character = library.find(c => c.id === characterId);

  if (character) {
    character.isFavorite = !character.isFavorite;
    saveCharacterLibrary(library);
    return true;
  }

  return false;
}

/**
 * 添加标签到角色
 */
export function addTagToCharacter(characterId: number, tag: string): boolean {
  const library = getCharacterLibrary();
  const character = library.find(c => c.id === characterId);

  if (character) {
    if (!character.tags) {
      character.tags = [];
    }

    if (!character.tags.includes(tag)) {
      character.tags.push(tag);
      saveCharacterLibrary(library);
    }

    return true;
  }

  return false;
}

/**
 * 从角色中移除标签
 */
export function removeTagFromCharacter(characterId: number, tag: string): boolean {
  const library = getCharacterLibrary();
  const character = library.find(c => c.id === characterId);

  if (character && character.tags) {
    character.tags = character.tags.filter(t => t !== tag);
    saveCharacterLibrary(library);
    return true;
  }

  return false;
}

/**
 * 复制角色
 */
export function duplicateCharacter(characterId: number): Character | null {
  const library = getCharacterLibrary();
  const character = library.find(c => c.id === characterId);

  if (character) {
    const newCharacter = {
      ...character,
      id: Date.now() + Math.floor(Math.random() * 1000),
      name: `${character.name} (复制)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    library.push(newCharacter);
    saveCharacterLibrary(library);
    return newCharacter;
  }

  return null;
}
