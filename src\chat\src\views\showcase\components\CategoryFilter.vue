<script setup lang="ts">
import { ref, watch, h } from 'vue';
import { NInput, NRadioGroup, NRadio, NSpace, NIcon } from 'naive-ui';
import { MagnifyingGlassIcon } from '@heroicons/vue/24/outline';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const { isMobile } = useBasicLayout();

const props = defineProps<{
  categories: { label: string; value: string }[];
  activeCategory: string;
}>();

const emit = defineEmits(['category-change', 'search']);

const searchValue = ref('');
const selectedCategory = ref(props.activeCategory);

// 监听分类变化
watch(() => props.activeCategory, (newValue) => {
  selectedCategory.value = newValue;
});

// 处理分类变化
const handleCategoryChange = (value: string) => {
  selectedCategory.value = value;
  emit('category-change', value);
};

// 处理搜索
const handleSearch = () => {
  emit('search', searchValue.value);
};

// 清除搜索
const clearSearch = () => {
  searchValue.value = '';
  emit('search', '');
};
</script>

<template>
  <div class="category-filter">
    <div class="filter-container" :class="{ 'mobile': isMobile }">
      <div class="categories">
        <NRadioGroup v-model:value="selectedCategory" @update:value="handleCategoryChange">
          <NSpace :size="isMobile ? 'small' : 'large'" :wrap="isMobile">
            <NRadio
              v-for="category in categories"
              :key="category.value"
              :value="category.value"
              :label="category.label"
              class="category-radio"
            />
          </NSpace>
        </NRadioGroup>
      </div>

      <div class="search-container">
        <NInput
          v-model:value="searchValue"
          placeholder="搜索作品..."
          clearable
          @keyup.enter="handleSearch"
          @clear="clearSearch"
        >
          <template #prefix>
            <SvgIcon name="ri:search-line" size="16" />
          </template>
        </NInput>
      </div>
    </div>
  </div>
</template>

<style scoped>
.category-filter {
  margin-bottom: 2rem;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  background-color: var(--color-background);
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: var(--shadow-sm);
}

.filter-container.mobile {
  flex-direction: column;
  align-items: stretch;
}

.categories {
  flex: 1;
}

.search-container {
  width: 250px;
}

.filter-container.mobile .search-container {
  width: 100%;
}

.category-radio {
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.category-radio:hover {
  background-color: var(--color-surface-hover);
}

/* 暗色模式适配 */
.dark .filter-container {
  background-color: var(--color-surface);
}
</style>
