<template>
    <div class="flex items-center justify-center transition-all duration-500 mx-auto device-frame" :class="[
        deviceType === 'mobile' ? 'mobile-device' : '',
        deviceType === 'tablet' ? 'tablet-device' : '',
        deviceType === 'desktop' ? 'desktop-frame' : '',
        isLandscape && deviceType !== 'desktop' ? 'landscape' : 'portrait',
        deviceClass
    ]" :style="deviceStyle">
        <!-- 设备元素 - 手机 -->
        <div v-if="deviceType === 'mobile'" class="device-hardware">
            <!-- 前置摄像头 -->
            <div class="camera-notch"></div>
            <!-- 音量按钮 -->
            <div class="volume-buttons">
                <div class="volume-up"></div>
                <div class="volume-down"></div>
            </div>
            <!-- 电源按钮 -->
            <div class="power-button"></div>
        </div>

        <!-- 设备元素 - 平板 -->
        <div v-if="deviceType === 'tablet'" class="device-hardware">
            <!-- 前置摄像头 -->
            <div class="camera"></div>
            <!-- 主页按钮 -->
            <div class="home-button"></div>
        </div>

        <iframe ref="previewIframeRef" :srcDoc="htmlContent"
            class="w-full h-full bg-white transition-all duration-500"></iframe>
    </div>
</template>

<script lang="ts" setup>
import { defineExpose, ref } from 'vue';

const props = defineProps({
    deviceType: {
        type: String as () => 'mobile' | 'tablet' | 'desktop',
        required: true
    },
    isLandscape: {
        type: Boolean,
        default: false
    },
    isFullscreen: {
        type: Boolean,
        default: false
    },
    htmlContent: {
        type: String,
        required: true
    },
    deviceStyle: {
        type: Object,
        default: () => ({})
    },
    deviceClass: {
        type: String,
        default: ''
    }
});

const previewIframeRef = ref<HTMLIFrameElement | null>(null);

// 刷新预览
const refreshPreview = () => {
    if (previewIframeRef.value) {
        const iframe = previewIframeRef.value;
        const content = iframe.srcdoc;
        iframe.srcdoc = "";
        setTimeout(() => {
            iframe.srcdoc = content;
        }, 10);
    }
};

defineExpose({
    refreshPreview,
    previewIframe: previewIframeRef
});
</script>

<style lang="less" scoped>
.device-frame {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform-origin: center center;
    position: relative;
    will-change: transform;
}

/* 自动缩放的设备 */
.auto-scaled-device {
    transition: transform 0.3s ease-out;
}

/* 设备硬件组件容器 */
.device-hardware {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

/* 移动设备样式 */
.mobile-device {
    border-radius: 36px;
    border: 8px solid #333;
    background-color: #333;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.25),
        inset 0 0 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: center center;
    animation: floatDevice 6s ease-in-out infinite;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 26px;
        background-color: #222;
        border-radius: 0 0 18px 18px;
        z-index: 2;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    /* 摄像头切口 */
    .camera-notch {
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 14px;
        background-color: #111;
        border-radius: 16px;
        z-index: 3;
        box-shadow: inset 0 0 3px 1px rgba(0, 0, 0, 0.3);
    }

    /* 音量按钮 */
    .volume-buttons {
        position: absolute;
        top: 100px;
        left: -8px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        z-index: 3;

        .volume-up,
        .volume-down {
            width: 4px;
            height: 24px;
            background-color: #222;
            border-radius: 2px 0 0 2px;
            box-shadow: inset 1px 0 3px rgba(0, 0, 0, 0.2);
        }
    }

    /* 电源按钮 */
    .power-button {
        position: absolute;
        top: 120px;
        right: -8px;
        width: 4px;
        height: 32px;
        background-color: #222;
        border-radius: 0 2px 2px 0;
        z-index: 3;
        box-shadow: inset -1px 0 3px rgba(0, 0, 0, 0.2);
    }

    &.landscape {
        border-radius: 36px;
        transform-origin: center center;

        &:before {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 26px;
            height: 120px;
            border-radius: 0 18px 18px 0;
        }

        /* 横屏模式下重新定位摄像头 */
        .camera-notch {
            top: 50%;
            left: 8px;
            transform: translateY(-50%);
            width: 14px;
            height: 80px;
            border-radius: 16px;
        }

        /* 横屏模式下重新定位按钮 */
        .volume-buttons {
            top: -8px;
            left: 100px;
            flex-direction: row;

            .volume-up,
            .volume-down {
                width: 24px;
                height: 4px;
                border-radius: 2px 2px 0 0;
            }
        }

        .power-button {
            top: auto;
            bottom: -8px;
            right: 120px;
            width: 32px;
            height: 4px;
            border-radius: 0 0 2px 2px;
        }
    }

    .dark & {
        border-color: #222;
        background-color: #222;

        &:before {
            background-color: #181818;
        }

        .camera-notch,
        .volume-buttons .volume-up,
        .volume-buttons .volume-down,
        .power-button {
            background-color: #151515;
        }
    }

    /* 全屏模式下的特殊样式 */
    .fullscreen-container & {
        transform-origin: center center;
        margin: 0 auto;
    }

    /* 悬浮动画 */
    @keyframes floatDevice {

        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-5px);
        }
    }

    /* 滚动条样式优化 */
    iframe {
        border-radius: 28px;
        background-color: white;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
    }
}

/* 全屏模式下的横竖屏修正 */
.fullscreen-container .preview-frame.landscape {
    transform-origin: center;
}

/* 平板设备样式 */
.tablet-device {
    border-radius: 20px;
    border: 12px solid #333;
    background-color: #333;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.15),
        0 12px 24px rgba(0, 0, 0, 0.2),
        inset 0 0 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transform-origin: center center;
    animation: floatDevice 8s ease-in-out infinite;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 8px;
        background-color: #222;
        border-radius: 0 0 10px 10px;
        z-index: 2;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    /* 前置摄像头 */
    .camera {
        position: absolute;
        top: 16px;
        left: 50%;
        transform: translateX(-50%);
        width: 10px;
        height: 10px;
        background-color: #111;
        border-radius: 50%;
        z-index: 3;
        box-shadow: inset 0 0 2px 1px rgba(0, 0, 0, 0.4);
    }

    /* 主页按钮 */
    .home-button {
        position: absolute;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        z-index: 3;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }

    &.landscape {
        border-radius: 20px;
        transform-origin: center center;

        &:before {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 8px;
            height: 60px;
            border-radius: 0 10px 10px 0;
        }

        /* 横屏模式下重新定位摄像头 */
        .camera {
            top: 50%;
            left: 16px;
            transform: translateY(-50%);
        }

        /* 横屏模式下重新定位主页按钮 */
        .home-button {
            bottom: 50%;
            left: auto;
            right: 16px;
            transform: translateY(50%);
        }
    }

    .dark & {
        border-color: #222;
        background-color: #222;

        &:before {
            background-color: #181818;
        }

        .camera {
            background-color: #151515;
        }

        .home-button {
            border-color: rgba(255, 255, 255, 0.15);
        }
    }

    /* 滚动条样式优化 */
    iframe {
        border-radius: 10px;
        background-color: white;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
    }

    /* 悬浮动画 */
    @keyframes floatDevice {

        0%,
        100% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-6px);
        }
    }
}

/* 桌面设备样式 */
.desktop-frame {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    background-color: white;
    overflow: hidden;

    /* 添加顶部控制条 */
    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 28px;
        background-color: #f1f1f1;
        border-bottom: 1px solid #ddd;
        border-radius: 8px 8px 0 0;
        z-index: 2;
    }

    /* 添加窗口控制按钮 */
    &:after {
        content: '';
        position: absolute;
        top: 9px;
        left: 10px;
        width: 46px;
        height: 10px;
        z-index: 3;
        background-image: radial-gradient(circle, #ff5f57 5px, #ff5f57 5px, transparent 5px),
            radial-gradient(circle, #ffbd2e 5px, #ffbd2e 5px, transparent 5px),
            radial-gradient(circle, #28c940 5px, #28c940 5px, transparent 5px);
        background-position: 0 0, 16px 0, 32px 0;
        background-repeat: no-repeat;
    }

    /* 调整iframe位置适应窗口标题栏 */
    iframe {
        position: absolute;
        top: 28px;
        left: 0;
        width: 100%;
        height: calc(100% - 28px);
        border: none;
        background-color: white;
        z-index: 1;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
    }

    .dark & {
        border-color: #444;
        background-color: #222;

        &:before {
            background-color: #2a2a2a;
            border-color: #444;
        }

        iframe {
            background-color: #222;
        }
    }
}

/* 旋转时的过渡效果 */
.portrait,
.landscape {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.landscape {
    transform: rotate(0deg); // 重置可能的旋转
}

/* 旋转动画 */
.rotating {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 移动设备在全屏模式下的旋转处理 */
.fullscreen-container .mobile-device {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    &.landscape {
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
}

/* 平板设备在全屏模式下的旋转处理 */
.fullscreen-container .tablet-device {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    &.landscape {
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
}
</style>