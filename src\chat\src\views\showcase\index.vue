<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAuthStore } from '@/store';
import { NButton, NBackTop, NSpin } from 'naive-ui';
// 导入儿童友好组件
import KidFriendlyHeroSection from './components/KidFriendlyHeroSection.vue';
import KidFriendlyCreationTools from './components/KidFriendlyCreationTools.vue';

// 作品管理相关组件已移除

const { isMobile } = useBasicLayout();
const router = useRouter();
const authStore = useAuthStore();

// 作品管理相关代码已移除

// 跳转到创作页面
const navigateToCreate = () => {
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }
  // 直接跳转到绘本创作页面，更适合小学生
  router.push('/storybook');
};

// 滚动到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

// 作品管理相关代码已移除
</script>

<template>
  <div class="showcase-container">
    <!-- 儿童友好英雄区域 -->
    <KidFriendlyHeroSection @create="navigateToCreate" />

    <!-- 儿童友好创作工具区域 -->
    <KidFriendlyCreationTools />

    <!-- 作品展示区域已移除 -->
    <section class="works-section" id="works-section">
      <div class="section-container">
        <div class="section-header">
          <div class="section-title-wrapper">
            <span class="section-emoji">🎨</span>
            <h2 class="section-title">作品展示功能开发中</h2>
          </div>
          <p class="section-description">作品管理功能已移除，新功能正在开发中...</p>
        </div>

        <!-- 返回顶部按钮 -->
        <div class="back-to-top-container">
          <NButton size="large" class="back-to-top-button" @click="scrollToTop">
            <span class="btn-icon">🚀</span>
            返回顶部
          </NButton>
        </div>
      </div>
    </section>

    <!-- 返回顶部组件 -->
    <NBackTop :right="30" :bottom="100">
      <div class="back-top-btn">
        <span class="back-top-icon">🚀</span>
      </div>
    </NBackTop>
  </div>
</template>

<style scoped>
.showcase-container {
  min-height: 100vh;
  background-color: #FAFAFA;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.works-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #FAFAFA;
  position: relative;
  overflow: hidden;
}

.works-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  z-index: 1;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  position: relative;
  z-index: 2;
}

.section-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.section-emoji {
  font-size: 2.5rem;
  margin-right: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #5D4037;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.section-description {
  font-size: 1.25rem;
  color: #795548;
  max-width: 700px;
  margin: 0 auto;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.works-container {
  min-height: 400px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-text {
  margin-top: 1rem;
  font-size: 1.1rem;
  color: #795548;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.back-to-top-container {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
  padding-bottom: 2rem;
}

.back-to-top-button {
  background: white;
  color: #FF9A9E;
  border: 2px solid #FF9A9E;
  font-size: 1.25rem;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.back-to-top-button:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  background: #FFF9C4;
}

.back-top-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF9A9E, #FFAFBD);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 15px rgba(255, 154, 158, 0.4);
  transition: all 0.3s ease;
}

.back-top-btn:hover {
  transform: scale(1.1);
}

.back-top-icon {
  font-size: 2rem;
  color: white;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1.1rem;
  }

  .back-to-top-button {
    font-size: 1rem;
    padding: 0.6rem 1.5rem;
  }

  .back-top-btn {
    width: 50px;
    height: 50px;
  }

  .back-top-icon {
    font-size: 1.5rem;
  }
}
</style>
