/**
 * 表单服务类
 * 封装表单相关的操作
 */
import { extractVariablesFromPreset, createFieldsFromPreset } from '@/utils/formUtils';
import ApiForm from '@/api/modules/form';
import { ElMessage } from 'element-plus';

/**
 * 表单数据接口
 */
export interface FormData {
  id?: number;
  name: string;
  description: string;
  appId: number;
  fields: string;
  status: number;
  order: number;
}

/**
 * 表单字段接口
 */
export interface FormField {
  type: string;
  name: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }> | Array<string>;
  min?: number;
  max?: number;
  step?: number;
  format?: string;
  activeText?: string;
  inactiveText?: string;
  [key: string]: any;
}

/**
 * 表单服务类
 */
export class FormService {
  /**
   * 从预设文本中提取变量并创建表单字段
   * @param preset 预设文本
   * @param existingFields 已存在的字段
   * @param formName 表单名称
   * @returns 处理结果
   */
  static extractVariablesAndCreateFields(
    preset: string,
    existingFields: any[] = [],
    formName: string = ''
  ): {
    fields: any[],
    formName: string,
    hasForm: boolean,
    addedCount: number
  } {
    if (!preset) {
      ElMessage.warning('预设信息为空，无法提取变量');
      return {
        fields: existingFields,
        formName,
        hasForm: false,
        addedCount: 0
      };
    }

    // 自动启用表单
    const hasForm = true;

    // 如果表单名称为空，自动设置一个默认名称
    if (!formName) {
      formName = '应用参数表单';
    }

    try {
      // 使用工具函数从预设中提取变量并创建字段
      const result = createFieldsFromPreset(preset, existingFields);

      if (result.addedCount > 0) {
        ElMessage.success(`成功从预设中提取并添加了${result.addedCount}个变量字段`);
      } else {
        ElMessage.info('所有变量已经存在于表单字段中');
      }

      return {
        fields: result.fields,
        formName,
        hasForm,
        addedCount: result.addedCount
      };
    } catch (error) {
      console.error('提取变量失败', error);
      ElMessage.error('提取变量失败，请检查预设格式');
      return {
        fields: existingFields,
        formName,
        hasForm: false,
        addedCount: 0
      };
    }
  }

  /**
   * 验证表单字段JSON格式
   * @param fieldsJson 表单字段JSON字符串
   * @returns 是否有效
   */
  static validateFormFieldsJson(fieldsJson: string): boolean {
    try {
      const fields = JSON.parse(fieldsJson);
      if (!Array.isArray(fields)) {
        return false;
      }

      // 验证每个字段是否有必要的属性
      for (const field of fields) {
        if (!field.type || !field.name || !field.label) {
          return false;
        }
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 格式化表单字段JSON
   * @param fieldsJson 表单字段JSON字符串
   * @returns 格式化后的JSON字符串
   */
  static formatFormFieldsJson(fieldsJson: string): string {
    try {
      const fields = JSON.parse(fieldsJson);
      return JSON.stringify(fields, null, 2);
    } catch (error) {
      return fieldsJson;
    }
  }

  /**
   * 获取表单列表
   * @param params 查询参数
   * @returns 表单列表
   */
  static async getFormList(params: any = {}): Promise<any> {
    try {
      const res = await ApiForm.queryForms(params);
      return res.data || { rows: [], count: 0 };
    } catch (error) {
      console.error('获取表单列表失败', error);
      ElMessage.error('获取表单列表失败');
      return { rows: [], count: 0 };
    }
  }

  /**
   * 获取表单详情
   * @param id 表单ID
   * @returns 表单详情
   */
  static async getFormById(id: number): Promise<FormData | null> {
    try {
      const res = await ApiForm.getFormById(id);
      return res.data || null;
    } catch (error) {
      console.error('获取表单详情失败', error);
      ElMessage.error('获取表单详情失败');
      return null;
    }
  }

  /**
   * 获取应用关联的表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  static async getFormsByAppId(appId: number): Promise<FormData[]> {
    try {
      // 尝试使用新API
      try {
        const res = await ApiForm.getFormsByAppId(appId);
        return res.data || [];
      } catch (error) {
        // 如果新API失败，回退到旧API
        console.warn('新表单API调用失败，尝试旧API', error);
        const res = await ApiForm.getFormsByAppIdLegacy(appId);
        return res.data || [];
      }
    } catch (error) {
      console.error('获取应用关联的表单失败', error);
      return [];
    }
  }

  /**
   * 创建表单
   * @param formData 表单数据
   * @returns 创建结果
   */
  static async createForm(formData: FormData): Promise<any> {
    try {
      // 验证表单字段JSON格式
      if (!this.validateFormFieldsJson(formData.fields)) {
        throw new Error('表单字段JSON格式错误');
      }

      // 创建表单
      const res = await ApiForm.createForm(formData);
      ElMessage.success('创建表单成功');
      return res.data;
    } catch (error) {
      console.error('创建表单失败', error);
      if (error instanceof Error) {
        ElMessage.error(`创建表单失败: ${error.message}`);
      } else {
        ElMessage.error('创建表单失败');
      }
      throw error;
    }
  }

  /**
   * 更新表单
   * @param formData 表单数据
   * @returns 更新结果
   */
  static async updateForm(formData: FormData): Promise<any> {
    try {
      if (!formData.id) {
        throw new Error('表单ID不能为空');
      }

      // 验证表单字段JSON格式
      if (!this.validateFormFieldsJson(formData.fields)) {
        throw new Error('表单字段JSON格式错误');
      }

      // 更新表单
      const res = await ApiForm.updateForm(formData);
      ElMessage.success('更新表单成功');
      return res.data;
    } catch (error) {
      console.error('更新表单失败', error);
      if (error instanceof Error) {
        ElMessage.error(`更新表单失败: ${error.message}`);
      } else {
        ElMessage.error('更新表单失败');
      }
      throw error;
    }
  }

  /**
   * 删除表单
   * @param id 表单ID
   * @returns 删除结果
   */
  static async deleteForm(id: number): Promise<any> {
    try {
      const res = await ApiForm.deleteForm({ id });
      ElMessage.success('删除表单成功');
      return res.data;
    } catch (error) {
      console.error('删除表单失败', error);
      ElMessage.error('删除表单失败');
      throw error;
    }
  }

  /**
   * 创建或更新表单
   * @param formData 表单数据
   * @returns 操作结果
   */
  static async createOrUpdateForm(formData: FormData): Promise<any> {
    try {
      // 查询是否已有表单
      const forms = await this.getFormsByAppId(formData.appId);

      if (forms && forms.length > 0) {
        // 更新表单
        const formId = forms[0].id;
        return await this.updateForm({ ...formData, id: formId });
      } else {
        // 创建表单
        return await this.createForm(formData);
      }
    } catch (error) {
      console.error('保存表单失败', error);
      throw error;
    }
  }
}

export default FormService;
