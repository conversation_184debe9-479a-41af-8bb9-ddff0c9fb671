<script setup lang="ts">
import { ref, computed } from 'vue';
import { NInput, NButton, NCard, NPopconfirm, NEmpty } from 'naive-ui';

const props = defineProps<{
  projectData: any;
}>();

// 灵感输入
const inspirationInput = ref('');

// 添加灵感
const addInspiration = () => {
  if (!inspirationInput.value.trim()) {
    window.$message?.warning('请输入灵感内容');
    return;
  }

  // 确保项目数据中有aiMagicPen.inspirations数组
  if (!props.projectData.aiMagicPen) {
    props.projectData.aiMagicPen = { inspirations: [], outlines: [], questions: [] };
  }
  if (!props.projectData.aiMagicPen.inspirations) {
    props.projectData.aiMagicPen.inspirations = [];
  }

  // 添加新灵感
  const newInspiration = {
    id: Date.now(),
    content: inspirationInput.value,
    timestamp: new Date().toISOString(),
    source: 'magic-pen' // 标记来源为AI魔笔
  };

  props.projectData.aiMagicPen.inspirations.push(newInspiration);

  // 为了向后兼容，也添加到旧的位置
  if (!props.projectData.inspirations) {
    props.projectData.inspirations = [];
  }
  props.projectData.inspirations.push(newInspiration);

  // 清空输入
  inspirationInput.value = '';

  // 可以添加本地存储备份
  try {
    localStorage.setItem('project-data', JSON.stringify(props.projectData));
  } catch (e) {
    console.warn('Failed to save project data to localStorage', e);
  }

  window.$message?.success('灵感已添加');
};

// 删除灵感
const deleteInspiration = (id) => {
  let deleted = false;

  // 从新数据结构中删除
  if (props.projectData.aiMagicPen && props.projectData.aiMagicPen.inspirations) {
    const newIndex = props.projectData.aiMagicPen.inspirations.findIndex(item => item.id === id);
    if (newIndex !== -1) {
      props.projectData.aiMagicPen.inspirations.splice(newIndex, 1);
      deleted = true;
    }
  }

  // 从旧数据结构中删除（向后兼容）
  if (props.projectData.inspirations) {
    const oldIndex = props.projectData.inspirations.findIndex(item => item.id === id);
    if (oldIndex !== -1) {
      props.projectData.inspirations.splice(oldIndex, 1);
      deleted = true;
    }
  }

  if (deleted) {
    // 更新本地存储
    try {
      localStorage.setItem('project-data', JSON.stringify(props.projectData));
    } catch (e) {
      console.warn('Failed to save project data to localStorage', e);
    }

    window.$message?.success('灵感已删除');
  }
};

// 计算灵感列表
const inspirations = computed(() => {
  // 优先使用新数据结构
  if (props.projectData.aiMagicPen && props.projectData.aiMagicPen.inspirations) {
    return props.projectData.aiMagicPen.inspirations;
  }
  // 向后兼容，使用旧数据结构
  return props.projectData.inspirations || [];
});

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<template>
  <div class="inspiration-container">
    <div class="inspiration-header">
      <h3 class="inspiration-title">灵感收集</h3>
      <p class="inspiration-subtitle">记录你的创意灵感，随时查看和使用</p>
    </div>

    <div class="inspiration-input">
      <div class="input-label">
        <span class="input-icon">✨</span>
        <span>写下你的灵感</span>
      </div>
      <NInput
        v-model:value="inspirationInput"
        type="textarea"
        placeholder="在这里记录你的创意灵感、故事点子或角色构思..."
        :autosize="{ minRows: 3, maxRows: 5 }"
        class="inspiration-textarea"
      />
      <div class="input-actions">
        <div class="input-tips">灵感随时可以编辑和整理</div>
        <NButton
          type="primary"
          @click="addInspiration"
          :disabled="!inspirationInput.trim()"
          class="add-button"
        >
          <span class="button-icon">💡</span>
          <span>保存灵感</span>
        </NButton>
      </div>
    </div>

    <div class="inspiration-list-container">
      <div class="list-header">
        <span class="list-title">我的灵感库</span>
        <span class="list-count" v-if="inspirations.length > 0">{{ inspirations.length }}条灵感</span>
      </div>

      <div class="inspiration-list">
        <NEmpty v-if="inspirations.length === 0" description="暂无灵感，开始添加吧！" class="empty-state">
          <template #icon>
            <div class="empty-icon-container">
              <span class="empty-icon">💭</span>
            </div>
          </template>
          <template #extra>
            <p class="empty-tip">记录下你的创意火花，让它们在这里闪耀！</p>
          </template>
        </NEmpty>

        <div
          v-for="inspiration in inspirations"
          :key="inspiration.id"
          class="inspiration-card"
        >
          <div class="card-content">{{ inspiration.content }}</div>
          <div class="card-footer">
            <span class="card-time">
              <span class="time-icon">🕒</span>
              {{ formatDate(inspiration.timestamp) }}
            </span>
            <NPopconfirm
              trigger="click"
              positive-text="确定"
              negative-text="取消"
              @positive-click="deleteInspiration(inspiration.id)"
            >
              <template #trigger>
                <NButton size="small" class="delete-button">
                  <span class="delete-icon">🗑️</span>
                  <span>删除</span>
                </NButton>
              </template>
              <div class="confirm-delete">确定要删除这条灵感吗？</div>
            </NPopconfirm>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.inspiration-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 0.5rem;
}

/* 标题样式 */
.inspiration-header {
  margin-bottom: 1.5rem;
}

.inspiration-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  background: linear-gradient(90deg, #8b5cf6, #6366f1);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.inspiration-subtitle {
  font-size: 0.875rem;
  color: #64748b;
}

.dark .inspiration-subtitle {
  color: #94a3b8;
}

/* 输入区域样式 */
.inspiration-input {
  margin-bottom: 2rem;
  background-color: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.dark .inspiration-input {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #334155;
  font-size: 1rem;
}

.dark .input-label {
  color: #e2e8f0;
}

.input-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.inspiration-textarea {
  margin-bottom: 0.75rem;
}

.inspiration-input :deep(.n-input) {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.dark .inspiration-input :deep(.n-input) {
  background-color: #0f172a;
}

.inspiration-input :deep(.n-input:hover),
.inspiration-input :deep(.n-input--focus) {
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
}

.dark .inspiration-input :deep(.n-input:hover),
.dark .inspiration-input :deep(.n-input--focus) {
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
  border-color: rgba(139, 92, 246, 0.5);
}

.inspiration-input :deep(.n-input__textarea-el) {
  font-size: 0.95rem;
  padding: 0.75rem;
  line-height: 1.5;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-tips {
  font-size: 0.75rem;
  color: #64748b;
}

.dark .input-tips {
  color: #94a3b8;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

.button-icon {
  font-size: 1.1rem;
}

/* 灵感列表样式 */
.inspiration-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.list-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
  display: flex;
  align-items: center;
}

.list-title::before {
  content: '📚';
  margin-right: 0.5rem;
}

.dark .list-title {
  color: #e2e8f0;
}

.list-count {
  font-size: 0.8rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.dark .list-count {
  color: #94a3b8;
  background-color: #334155;
}

.inspiration-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.5rem;
}

.inspiration-card {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.dark .inspiration-card {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.inspiration-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
}

.dark .inspiration-card:hover {
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.25);
  border-color: rgba(139, 92, 246, 0.5);
}

.card-content {
  white-space: pre-line;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 0.95rem;
  color: #334155;
}

.dark .card-content {
  color: #e2e8f0;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.dark .card-footer {
  border-top-color: #334155;
}

.card-time {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #64748b;
}

.dark .card-time {
  color: #94a3b8;
}

.time-icon {
  margin-right: 0.25rem;
  font-size: 0.9rem;
}

.delete-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border: none;
}

.delete-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.delete-icon {
  font-size: 0.9rem;
}

.confirm-delete {
  padding: 0.5rem;
  font-size: 0.9rem;
}

/* 空状态样式 */
.empty-state {
  padding: 2rem 0;
}

.empty-icon-container {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.dark .empty-icon-container {
  background: linear-gradient(135deg, #0c4a6e, #075985);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.empty-icon {
  font-size: 2.5rem;
}

.empty-tip {
  font-size: 0.9rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.dark .empty-tip {
  color: #94a3b8;
}
</style>
