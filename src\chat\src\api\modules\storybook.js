import { get } from '@/utils/request';

/**
 * 绘本相关API模块
 */
export default {
  /**
   * 获取绘本配置
   * @returns 配置列表
   */
  getStorybookConfig: () => get({
    url: 'storybook/config',
    params: {} // 添加空参数对象，避免验证错误
  }),

  /**
   * 获取图像生成配置
   * @returns 图像生成配置
   */
  getImageGenerationConfig: () => get({
    url: 'storybook/image-config/1', // 使用新的带ID参数的路由
    data: {}
  }),

  /**
   * 获取小狐狸助手配置
   * @returns 小狐狸助手AI配置
   */
  getFoxAssistantConfig: () => get({
    url: 'storybook/fox-assistant-config',
    params: {}
  }),
};
