<template>
  <div class="canvas-mask-container" ref="containerRef">
    <div class="canvas-wrapper"
         :style="{ transform: `scale(${scale}) translate(${panX}px, ${panY}px)` }"
         @mousedown="startPan"
         @wheel="handleZoom">
      <canvas ref="backgroundCanvas" :width="width" :height="height"></canvas>
      <canvas ref="canvas" :width="width" :height="height"
              @mousedown.stop="startDrawing"
              @mousemove.stop="draw"
              @mouseup.stop="stopDrawing"
              @mouseleave.stop="stopDrawing"></canvas>
    </div>

    <!-- 缩放控制 -->
    <div class="zoom-controls">
      <button class="zoom-btn" @click="zoomIn" title="放大">
        <span>➕</span>
      </button>
      <div class="zoom-level">{{ Math.round(scale * 100) }}%</div>
      <button class="zoom-btn" @click="zoomOut" title="缩小">
        <span>➖</span>
      </button>
      <button class="zoom-btn" @click="resetZoom" title="重置">
        <span>🔄</span>
      </button>
    </div>

    <!-- 提示信息 -->
    <div class="canvas-hint" v-if="showHint">
      <div class="hint-content">
        <div class="hint-title">小提示</div>
        <div class="hint-steps">
          <div class="hint-step">
            <div class="hint-step-number">1</div>
            <div class="hint-step-text">用<strong>画笔</strong>在图像上画出你想修改的区域</div>
          </div>
          <div class="hint-step">
            <div class="hint-step-number">2</div>
            <div class="hint-step-text">用<strong>缩放按钮</strong>或鼠标滚轮放大/缩小图像</div>
          </div>
          <div class="hint-step">
            <div class="hint-step-number">3</div>
            <div class="hint-step-text">按住<strong>鼠标右键</strong>可以拖动图像查看不同部分</div>
          </div>
        </div>
        <button class="hint-close" @click="closeHint">我知道了！</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  /* 图片地址 如果不是同源跨域 传入base64 */
  src: String,
  /* 图片 高度 宽度 不传就是用图片宽高、如果是缩略图 使用尺寸导出到原始尺寸 */
  width: Number,
  height: Number,
  /* 允许的画布最大宽度 限制区域 */
  max: {
    type: Number,
    default: 500
  },
  /*导出蒙版的底色背景色 */
  exportMaskBackgroundColor: {
    type: String,
    default: 'black'
  },
  /* 导出蒙版的绘制颜色 */
  exportMaskColor: {
    type: String,
    default: 'white'
  },
  penColor: {
    type: String,
    default: 'white'
  },
  penWidth: {
    type: Number,
    default: 20
  },
  updateFileInfo: Function
});

// TODO  如果动态变更了线宽颜色等 在导出的时候没有记录每一步的线宽 而是使用了最后的

const canvas = ref<any>(null);
const backgroundCanvas = ref<any>(null);
const containerRef = ref<any>(null);
const paths = ref<any>([]);
let isDrawing = false;
let currentPath: any = [];
let baseImage: any = new Image()
const isEraserEnabled = ref(false);

const computedWidth = ref(0);
const computedHeight = ref(0);
const scaleRatio = ref(0);

// 缩放和平移相关变量
const scale = ref(1);
const panX = ref(0);
const panY = ref(0);
const isPanning = ref(false);
const lastPanPoint = ref({ x: 0, y: 0 });
const showHint = ref(true);

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.1);
  }
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.1);
  }
};

const resetZoom = () => {
  scale.value = 1;
  panX.value = 0;
  panY.value = 0;
};

// 处理鼠标滚轮缩放
const handleZoom = (e) => {
  e.preventDefault();
  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.5, Math.min(3, scale.value + delta));

  // 计算鼠标位置相对于容器的坐标
  const rect = containerRef.value.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;

  // 计算缩放前鼠标在画布上的位置
  const oldX = (mouseX - panX.value) / scale.value;
  const oldY = (mouseY - panY.value) / scale.value;

  // 更新缩放比例
  scale.value = newScale;

  // 计算缩放后鼠标在画布上的新位置，并调整平移量保持鼠标位置不变
  const newX = mouseX - oldX * newScale;
  const newY = mouseY - oldY * newScale;

  panX.value = newX;
  panY.value = newY;
};

// 平移控制
const startPan = (e) => {
  // 只有右键点击才启动平移
  if (e.button === 2 || e.ctrlKey) {
    e.preventDefault();
    isPanning.value = true;
    lastPanPoint.value = { x: e.clientX, y: e.clientY };

    // 添加鼠标移动和释放事件监听
    document.addEventListener('mousemove', pan);
    document.addEventListener('mouseup', stopPan);
  }
};

const pan = (e) => {
  if (isPanning.value) {
    const deltaX = e.clientX - lastPanPoint.value.x;
    const deltaY = e.clientY - lastPanPoint.value.y;

    panX.value += deltaX;
    panY.value += deltaY;

    lastPanPoint.value = { x: e.clientX, y: e.clientY };
  }
};

const stopPan = () => {
  isPanning.value = false;
  document.removeEventListener('mousemove', pan);
  document.removeEventListener('mouseup', stopPan);
};

// 关闭提示
const closeHint = () => {
  showHint.value = false;
  try {
    localStorage.setItem('canvasMaskHintShown', 'true');
  } catch (e) {
    console.error('无法保存提示状态到localStorage', e);
  }
};

onMounted(() => {
  const ctx: any = canvas.value.getContext('2d');
  const backgroundCtx = backgroundCanvas.value?.getContext('2d');

  // 加载图像
  baseImage.src = props.src;
  baseImage.onload = () => {
    // 计算适合容器的缩放比例
    const containerWidth = containerRef.value.clientWidth;
    const containerHeight = containerRef.value.clientHeight;

    // 确保图像完全适应容器，同时保持纵横比
    const ratio = Math.min(
      containerWidth / baseImage.width,
      containerHeight / baseImage.height,
      props.max / Math.max(baseImage.width, baseImage.height)
    );

    scaleRatio.value = ratio;

    // 计算画布尺寸
    computedWidth.value = props.width || (ratio < 1 ? baseImage.width * ratio : baseImage.width);
    computedHeight.value = props.height || (ratio < 1 ? baseImage.height * ratio : baseImage.height);

    // 更新文件信息
    props.updateFileInfo?.({
      width: baseImage.width,
      height: baseImage.height,
      scaleRatio: ratio.toFixed(3)
    });

    // 设置画布尺寸
    canvas.value.width = computedWidth.value;
    backgroundCanvas.value.width = computedWidth.value;
    canvas.value.height = computedHeight.value;
    backgroundCanvas.value.height = computedHeight.value;

    // 绘制背景图像
    backgroundCtx.drawImage(baseImage, 0, 0, computedWidth.value, computedHeight.value);

    // 自动调整初始缩放以适应容器
    const initialScale = Math.min(
      containerWidth / computedWidth.value,
      containerHeight / computedHeight.value
    );

    // 只有当图像大于容器时才缩小
    if (initialScale < 1) {
      scale.value = initialScale * 0.9; // 稍微缩小一点，留出边距
    }

    // 居中图像
    panX.value = (containerWidth - computedWidth.value * scale.value) / 2;
    panY.value = (containerHeight - computedHeight.value * scale.value) / 2;
  };

  // 检查是否已经显示过提示
  try {
    const hintShown = localStorage.getItem('canvasMaskHintShown');
    if (hintShown === 'true') {
      showHint.value = false;
    }
  } catch (e) {
    console.error('无法从localStorage读取提示状态', e);
  }

  // 阻止右键菜单，以便支持右键拖动
  containerRef.value.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    return false;
  });
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', pan);
  document.removeEventListener('mouseup', stopPan);
});

/* 开始绘制 */
const emit = defineEmits(['draw-start']);

const startDrawing = (e: any) => {
  // 如果正在平移，不启动绘制
  if (isPanning.value) return;

  isDrawing = true;
  const ctx = canvas.value.getContext('2d');

  // 获取画布的边界矩形
  const rect = canvas.value.getBoundingClientRect();

  // 计算相对于画布的精确坐标（考虑边界偏移）
  const x = ((e.clientX - rect.left) / scale.value);
  const y = ((e.clientY - rect.top) / scale.value);

  ctx.beginPath();
  ctx.moveTo(x, y);
  currentPath = [{ type: isEraserEnabled.value ? 'erase' : 'draw', x, y }];

  // 触发绘制开始事件
  emit('draw-start');
};

/* 绘制过程 */
const draw = (e: any) => {
  if (!isDrawing) return;

  // 获取画布的边界矩形
  const rect = canvas.value.getBoundingClientRect();

  // 计算相对于画布的精确坐标（考虑边界偏移）
  const x = ((e.clientX - rect.left) / scale.value);
  const y = ((e.clientY - rect.top) / scale.value);

  const ctx = canvas.value.getContext('2d');
  ctx.lineTo(x, y);

  if (isEraserEnabled.value) {
    // 橡皮擦模式：清除画布上的内容
    ctx.globalCompositeOperation = 'destination-out';
    ctx.lineWidth = props.penWidth * 2 / scale.value; // 橡皮擦宽度根据缩放调整
  } else {
    // 正常绘制模式
    ctx.globalCompositeOperation = 'source-over';
    ctx.strokeStyle = props.penColor;
    ctx.lineWidth = props.penWidth / scale.value; // 线宽根据缩放调整
  }
  ctx.stroke();
  currentPath.push({ type: isEraserEnabled.value ? 'erase' : 'draw', x, y });
};

/* 完成单词绘制 */
const stopDrawing = () => {
  if (!isDrawing) return;

  isDrawing = false;
  paths.value.push([...currentPath, { type: 'end' }]);
  currentPath = [];
};

/* 获取Base图片 */
const exportImage = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    const exportCanvas = document.createElement('canvas');
    const image: any = baseImage;
    exportCanvas.width = image.width;
    exportCanvas.height = image.height;
    const exportCtx = exportCanvas.getContext('2d');
    if (exportCtx) {
      // 创建透明背景的蒙版 - OpenAI API要求透明区域(alpha=0)表示要编辑的区域
      // 首先清空画布，使其完全透明
      exportCtx.clearRect(0, 0, exportCanvas.width, exportCanvas.height);

      // 填充黑色背景（完全不透明）- 黑色表示不需要编辑的区域
      exportCtx.fillStyle = 'black';
      exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);

      // 设置绘制模式为"destination-out"，这样绘制的区域会变成透明
      // 透明区域(alpha=0)表示需要编辑的区域
      exportCtx.globalCompositeOperation = 'destination-out';

      const xRatio = image.width / computedWidth.value;
      const yRatio = image.height / computedHeight.value;

      // 绘制所有路径
      paths.value.forEach((pathArr: any[]) => {
        pathArr.forEach((path, index) => {
          // 绘制路径（使其透明）
          if (path.type === 'draw') {
            if (index === 0 || pathArr[index - 1].type !== path.type) {
              exportCtx.beginPath();
            }
            exportCtx.lineTo(path.x * xRatio, path.y * yRatio);
            exportCtx.lineWidth = props.penWidth * xRatio;
          }
          // 橡皮擦在这里实际上是恢复不透明
          if (path.type === 'erase') {
            if (index === 0 || pathArr[index - 1].type !== path.type) {
              // 切换回正常绘制模式
              exportCtx.stroke(); // 结束之前的路径
              exportCtx.globalCompositeOperation = 'source-over';
              exportCtx.beginPath();
            }
            exportCtx.lineTo(path.x * xRatio, path.y * yRatio);
            exportCtx.strokeStyle = 'black';
            exportCtx.lineWidth = props.penWidth * xRatio * 2;
          }
          // 每当一个路径类型结束时，结束当前的路径
          if (index < pathArr.length - 1 && pathArr[index + 1].type !== path.type) {
            exportCtx.stroke();
            // 如果下一个是绘制，切换回destination-out模式
            if (pathArr[index + 1].type === 'draw') {
              exportCtx.globalCompositeOperation = 'destination-out';
            }
          }
        });
        // 确保路径被结束
        if (pathArr.length > 0) {
          exportCtx.stroke();
        }
      });

      // 导出为PNG（保留透明度）
      const base64Image = exportCanvas.toDataURL('image/png');
      console.log('[CanvasMask] 已生成蒙版图像，包含透明区域作为编辑区域');
      resolve(base64Image);
    } else {
      reject(new Error('无法获取canvas的2D渲染上下文'));
    }
  });
}

/* 清空画布并重置 */
function clear() {
  paths.value = [];
	const ctx = canvas.value.getContext('2d');
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
};

/* 获取绘制后的蒙版图片 */
async function getBase(){
	return await exportImage()
}

/* 返回上一步 */
function undo(){
  if (paths.value.length > 0) {
    paths.value.pop();
    redrawCanvas();
  }
};

/* 重新绘制 */
function redrawCanvas() {
  const ctx = canvas.value.getContext('2d');
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
  ctx.drawImage(baseImage, 0, 0, computedWidth.value, computedHeight.value);

  paths.value.forEach((pathArr: any[]) => {
    pathArr.forEach((path, index) => {
      if (index === 0 || pathArr[index - 1].type !== path.type) {
        ctx.beginPath();
      }
      if (path.type === 'erase') {
        ctx.globalCompositeOperation = "destination-out";
        ctx.strokeStyle = 'rgba(0,0,0,0)';
      } else {
        ctx.globalCompositeOperation = "source-over";
        ctx.strokeStyle = 'white';
      }
      ctx.lineWidth = path.type === 'erase' ? props.penWidth * 2 : props.penWidth;
      ctx.lineTo(path.x, path.y);
      ctx.stroke();
      if (index === pathArr.length - 1 || pathArr[index + 1].type !== path.type) {
        ctx.closePath();
      }
    });
  });
  ctx.globalCompositeOperation = "source-over";
}



/* 切换橡皮擦模式 */
const toggleEraser = () => {
  isEraserEnabled.value = !isEraserEnabled.value;
};

defineExpose({
	getBase,
	undo,
	clear,
	toggleEraser
})


</script>

<style scoped>
.canvas-mask-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
  user-select: none;
}

.canvas-wrapper {
  position: relative;
  transform-origin: 0 0;
  will-change: transform;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #ddd;
  pointer-events: none;
}

canvas:last-child {
  pointer-events: auto;
}

.zoom-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.dark .zoom-controls {
  background-color: rgba(30, 41, 59, 0.8);
}

.zoom-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 2px;
  font-size: 14px;
  transition: all 0.2s;
}

.zoom-btn:hover {
  background-color: #f0f0f0;
}

.dark .zoom-btn {
  border-color: #475569;
  color: #e2e8f0;
}

.dark .zoom-btn:hover {
  background-color: #334155;
}

.zoom-level {
  margin: 0 8px;
  font-size: 14px;
  min-width: 45px;
  text-align: center;
}

.canvas-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  pointer-events: none;
}

.hint-content {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  max-width: 350px;
  pointer-events: auto;
  border: 2px solid #e2e8f0;
}

.dark .hint-content {
  background-color: rgba(30, 41, 59, 0.95);
  color: #e2e8f0;
  border-color: #475569;
}

.hint-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #3b82f6;
  text-align: center;
}

.dark .hint-title {
  color: #60a5fa;
}

.hint-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.hint-step {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hint-step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
}

.dark .hint-step-number {
  background-color: #60a5fa;
  color: #1e293b;
}

.hint-step-text {
  font-size: 14px;
  line-height: 1.4;
}

.hint-close {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: block;
  margin: 0 auto;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.hint-close:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(59, 130, 246, 0.4);
}

.dark .hint-close {
  background-color: #60a5fa;
  color: #1e293b;
  box-shadow: 0 4px 6px rgba(96, 165, 250, 0.4);
}

.dark .hint-close:hover {
  background-color: #93c5fd;
  box-shadow: 0 6px 8px rgba(96, 165, 250, 0.5);
}
</style>
