import type { Router } from 'vue-router';
import { useAuthStore } from '@/store';

// 角色路由映射
const ROLE_ROUTES = {
  teacher: {
    default: '/chat',
    layout: () => import('@/layout/TeacherLayout.vue')
  },
  student: {
    default: '/storybook', 
    layout: () => import('@/layout/StudentLayout.vue')
  }
};

// 检测用户角色的函数
export function detectUserRole(route: any): 'teacher' | 'student' {
  const authStore = useAuthStore();
  const userInfo = authStore.userInfo;
  
  // 1. 优先从用户信息中获取角色
  if (userInfo?.role && (userInfo.role === 'teacher' || userInfo.role === 'student')) {
    return userInfo.role;
  }
  
  // 2. 根据当前路径推断角色
  if (route.path.includes('/chat') || route.path.includes('/teacher')) {
    return 'teacher';
  } else if (route.path.includes('/storybook') || route.path.includes('/student')) {
    return 'student';
  }
  
  // 3. 默认返回学生角色（更适合小学生使用场景）
  return 'student';
}

// 获取角色对应的布局组件
export function getRoleLayout(role: 'teacher' | 'student') {
  return ROLE_ROUTES[role].layout;
}

// 获取角色的默认路由
export function getRoleDefaultRoute(role: 'teacher' | 'student'): string {
  return ROLE_ROUTES[role].default;
}

// 检查路由是否需要特定角色
export function isRoleSpecificRoute(path: string): 'teacher' | 'student' | null {
  if (path.startsWith('/chat') || path.includes('/teacher')) {
    return 'teacher';
  } else if (path.startsWith('/storybook') || path.includes('/student')) {
    return 'student';
  }
  return null;
}

// 设置角色守卫
export function setupRoleGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    // 跳过特殊路由
    if (to.path === '/404' || to.path === '/500' || to.path.startsWith('/share/')) {
      return next();
    }
    
    // 检测用户当前角色
    const currentRole = detectUserRole(to);
    
    // 检查目标路由是否需要特定角色
    const requiredRole = isRoleSpecificRoute(to.path);
    
    // 如果路由需要特定角色，但用户角色不匹配，则重定向到合适的默认页面
    if (requiredRole && requiredRole !== currentRole) {
      const defaultRoute = getRoleDefaultRoute(currentRole);
      console.log(`角色不匹配，从 ${to.path} 重定向到 ${defaultRoute}`);
      return next(defaultRoute);
    }
    
    // 为根路径提供智能重定向
    if (to.path === '/') {
      const defaultRoute = getRoleDefaultRoute(currentRole);
      console.log(`根路径重定向，角色: ${currentRole}，目标: ${defaultRoute}`);
      return next(defaultRoute);
    }
    
    next();
  });
}

// 角色切换函数
export function switchUserRole(router: Router, targetRole: 'teacher' | 'student') {
  const defaultRoute = getRoleDefaultRoute(targetRole);
  router.push(defaultRoute);
}

// 导出类型定义
export type UserRole = 'teacher' | 'student';
export type RoleRouteConfig = typeof ROLE_ROUTES; 