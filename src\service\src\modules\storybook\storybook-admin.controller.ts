import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards, ParseIntPipe, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { AdminAuthGuard } from 'src/common/auth/adminAuth.guard';
import { SuperAuthGuard } from 'src/common/auth/superAuth.guard';
import { StorybookService } from './storybook.service';
import { Request } from 'express';
import { StorybookConfigEntity, StorybookPromptEntity, StorybookTemplateEntity, StorybookImageEntity } from './entities';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';

@ApiTags('Admin Storybook')
@Controller('admin/storybook')
export class StorybookAdminController {
  constructor(private readonly storybookService: StorybookService) {}

  // ==================== 绘本作品管理 ====================
  // 作品管理功能已移除

  // ==================== 配置管理 ====================

  @Get('config')
  @ApiOperation({ summary: '获取所有配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async getAllConfigs() {
    return this.storybookService.getAllConfigs();
  }

  @Put('config/:key')
  @ApiOperation({ summary: '更新单个配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'key', description: '配置键' })
  async updateConfig(
    @Param('key') key: string,
    @Body() data: { configVal: string; description?: string; public?: number },
  ) {
    return this.storybookService.setConfig(key, data.configVal, data.description);
  }

  @Put('config')
  @ApiOperation({ summary: '批量更新配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async updateBatchConfig(@Body() data: Record<string, any>) {
    const results = [];

    // 记录处理开始
    console.log('开始批量更新配置:', Object.keys(data));

    try {
      // 遍历所有配置键值对并更新
      for (const [key, value] of Object.entries(data)) {
        try {
          // 将值转换为字符串
          const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
          console.log(`正在更新配置: ${key} = ${stringValue}`);

          // 设置配置项，并添加描述
          let description;
          if (key.startsWith('foxAssistant')) {
            description = `小狐狸助手专用配置: ${key}`;
          }

          const result = await this.storybookService.setConfig(key, stringValue, description);
          console.log(`配置更新成功: ${key}`);
          results.push(result);
        } catch (error) {
          console.error(`更新配置 ${key} 失败:`, error);
          // 继续处理其他配置，不中断
        }
      }

      console.log('批量更新配置完成，共更新', results.length, '项');

      // 获取所有最新配置并返回
      const allConfigs = await this.storybookService.getAllConfigs();

      return {
        success: true,
        message: `成功更新 ${results.length} 项配置`,
        updatedKeys: Object.keys(data),
        configs: allConfigs
      };
    } catch (error) {
      console.error('批量更新配置失败:', error);
      throw new Error('批量更新配置失败: ' + error.message);
    }
  }

  // ==================== 提示词管理 ====================

  @Get('prompt')
  @ApiOperation({ summary: '获取所有提示词' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async getAllPrompts() {
    return this.storybookService.getAllPrompts();
  }

  @Post('prompt')
  @ApiOperation({ summary: '创建提示词' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async createPrompt(@Body() data: Partial<StorybookPromptEntity>) {
    return this.storybookService.createPrompt(data);
  }

  @Put('prompt/:id')
  @ApiOperation({ summary: '更新提示词' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '提示词ID' })
  async updatePrompt(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: Partial<StorybookPromptEntity>,
  ) {
    return this.storybookService.updatePrompt(id, data);
  }

  @Delete('prompt/:id')
  @ApiOperation({ summary: '删除提示词' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '提示词ID' })
  async deletePrompt(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.deletePrompt(id);
  }

  // ==================== 模板管理 ====================

  @Get('template')
  @ApiOperation({ summary: '获取所有模板' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async getAllTemplates() {
    return this.storybookService.getAllTemplates();
  }

  @Post('template')
  @ApiOperation({ summary: '创建模板' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async createTemplate(@Body() data: Partial<StorybookTemplateEntity>) {
    return this.storybookService.createTemplate(data);
  }

  @Put('template/:id')
  @ApiOperation({ summary: '更新模板' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '模板ID' })
  async updateTemplate(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: Partial<StorybookTemplateEntity>,
  ) {
    return this.storybookService.updateTemplate(id, data);
  }

  @Delete('template/:id')
  @ApiOperation({ summary: '删除模板' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '模板ID' })
  async deleteTemplate(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.deleteTemplate(id);
  }

  // ==================== 统计数据 ====================

  @Get('statistics')
  @ApiOperation({ summary: '获取统计数据' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  async getStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.storybookService.getStatistics(startDate, endDate);
  }

  // ==================== AI绘图资源管理 ====================

  @Get('image')
  @ApiOperation({ summary: '获取图像列表' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'userId', required: false, description: '用户ID' })
  @ApiQuery({ name: 'imageType', required: false, description: '图像类型' })
  @ApiQuery({ name: 'auditStatus', required: false, description: '审核状态' })
  @ApiQuery({ name: 'storybookId', required: false, description: '绘本ID' })
  @ApiQuery({ name: 'keyword', required: false, description: '关键词' })
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  async getImages(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('userId') userId?: number,
    @Query('imageType') imageType?: number,
    @Query('auditStatus') auditStatus?: number,
    @Query('storybookId') storybookId?: number,
    @Query('keyword') keyword?: string,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    return this.storybookService.getImages({
      page,
      limit,
      userId,
      imageType,
      auditStatus,
      storybookId,
      keyword,
      startDate,
      endDate
    });
  }

  @Get('image/:id')
  @ApiOperation({ summary: '获取图像详情' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async getImageDetail(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.getImageDetail(id);
  }

  @Put('image/:id/audit')
  @ApiOperation({ summary: '审核图像' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async auditImage(
    @Param('id', ParseIntPipe) id: number,
    @Body('auditStatus') auditStatus: number,
    @Body('auditRemark') auditRemark?: string,
  ) {
    return this.storybookService.auditImage(id, auditStatus, auditRemark);
  }

  @Put('image/:id/quality')
  @ApiOperation({ summary: '设置图像质量评级' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async setImageQuality(
    @Param('id', ParseIntPipe) id: number,
    @Body('qualityRating') qualityRating: number,
  ) {
    return this.storybookService.setImageQuality(id, qualityRating);
  }

  @Delete('image/:id')
  @ApiOperation({ summary: '删除图像' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async deleteImage(@Param('id', ParseIntPipe) id: number) {
    await this.storybookService.deleteImage(id);
    return { success: true, message: '删除成功' };
  }

  @Get('image-config')
  @ApiOperation({ summary: '获取图像生成配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async getImageGenerationConfig() {
    return this.storybookService.getImageGenerationConfig();
  }

  @Put('image-config')
  @ApiOperation({ summary: '更新图像生成配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async updateImageGenerationConfig(@Body() config: {
    model?: string;
    size?: string;
    quality?: string;
    style?: string;
    fallbackService?: string;
    enabled?: boolean;
  }) {
    return this.storybookService.updateImageGenerationConfig(config);
  }

  @Get('image-stats')
  @ApiOperation({ summary: '获取图像资源使用统计' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  async getImageUsageStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.storybookService.getImageUsageStats(startDate, endDate);
  }

  // ==================== 内容安全管理 ====================

  @Get('content-safety/config')
  @ApiOperation({ summary: '获取内容安全配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async getContentSafetyConfig() {
    return this.storybookService.getContentSafetyConfig();
  }

  @Put('content-safety/config')
  @ApiOperation({ summary: '更新内容安全配置' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  async updateContentSafetyConfig(@Body() config: { autoAudit?: boolean; sensitiveFilter?: boolean }) {
    return this.storybookService.updateContentSafetyConfig(config);
  }

  @Post(':id/audit-content')
  @ApiOperation({ summary: '审核绘本内容' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '绘本ID' })
  async auditStorybookContent(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ) {
    const userId = req.user['id'];
    return this.storybookService.auditStorybookContent(id, userId);
  }

  @Post(':id/auto-audit')
  @ApiOperation({ summary: '自动审核绘本' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '绘本ID' })
  async autoAuditStorybook(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ) {
    const userId = req.user['id'];
    return this.storybookService.autoAuditStorybook(id, userId);
  }

  @Get('violation-stats')
  @ApiOperation({ summary: '获取违规内容统计' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'startDate', required: false, description: '开始日期' })
  @ApiQuery({ name: 'endDate', required: false, description: '结束日期' })
  async getViolationStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.storybookService.getViolationStats(startDate, endDate);
  }
}
