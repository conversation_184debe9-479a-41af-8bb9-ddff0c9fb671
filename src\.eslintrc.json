{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["standard-with-typescript", "plugin:vue/vue3-essential", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["vue", "unused-imports", "prettier", "@typescript-eslint"], "rules": {"unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@typescript-eslint/no-unused-expressions": "off", "prettier/prettier": ["error", {"semi": true, "singleQuote": true, "tabWidth": 2, "useTabs": false, "trailingComma": "es5", "bracketSpacing": true}]}, "overrides": [{"files": ["chat/**/*", "admin/**/*", "service/**/*"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./chat/tsconfig.json", "./admin/tsconfig.json", "./service/tsconfig.json"]}, "rules": {"@typescript-eslint/no-unused-expressions": "off"}}]}