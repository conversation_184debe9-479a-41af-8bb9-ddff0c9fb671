import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsArray } from 'class-validator';

export class UpdateStorybookDto {
  @ApiProperty({ description: '绘本标题', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: '绘本描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '封面图片URL', required: false })
  @IsOptional()
  @IsString()
  coverImg?: string;

  @ApiProperty({ description: '是否公开(0:私有,1:公开)', required: false })
  @IsOptional()
  @IsNumber()
  isPublic?: number;

  @ApiProperty({ description: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', required: false })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty({ description: '绘本内容JSON', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ description: '来源', required: false })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  tags?: string[];
}
