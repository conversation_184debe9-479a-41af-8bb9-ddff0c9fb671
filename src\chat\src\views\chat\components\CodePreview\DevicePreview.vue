<template>
    <div class="relative px-3 py-2 device-preview-container h-full" :class="[
        { 'fullscreen-container': isFullscreen },
        fullscreenContainerClass
    ]" id="preview-container" ref="previewContainerRef" role="region" aria-label="设备预览器">
        <!-- 设备控制栏 -->
        <div class="device-controls flex flex-wrap items-center justify-between mb-4 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700" role="toolbar" aria-label="设备预览控制面板">
            <!-- 设备类型选择 -->
            <div class="flex items-center space-x-2 md:space-x-3" role="radiogroup" aria-label="设备类型选择">
                <button @click="changeDeviceType('mobile')"
                    class="p-2 md:p-2.5 rounded-md transition-colors duration-200 touch-manipulation"
                    :class="deviceType === 'mobile' ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'"
                    title="手机视图"
                    aria-label="切换到手机视图"
                    :aria-pressed="deviceType === 'mobile'"
                    role="radio">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
                        <line x1="12" y1="18" x2="12" y2="18" />
                    </svg>
                    <span class="sr-only">手机视图</span>
                </button>
                <button @click="changeDeviceType('tablet')"
                    class="p-2 md:p-2.5 rounded-md transition-colors duration-200 touch-manipulation"
                    :class="deviceType === 'tablet' ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'"
                    title="平板视图"
                    aria-label="切换到平板视图"
                    :aria-pressed="deviceType === 'tablet'"
                    role="radio">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <rect x="4" y="2" width="16" height="20" rx="2" ry="2" />
                        <line x1="12" y1="18" x2="12" y2="18" />
                    </svg>
                    <span class="sr-only">平板视图</span>
                </button>
                <button @click="changeDeviceType('desktop')"
                    class="p-2 md:p-2.5 rounded-md transition-colors duration-200 touch-manipulation"
                    :class="deviceType === 'desktop' ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'"
                    title="桌面视图"
                    aria-label="切换到桌面视图"
                    :aria-pressed="deviceType === 'desktop'"
                    role="radio">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                        <line x1="8" y1="21" x2="16" y2="21" />
                        <line x1="12" y1="17" x2="12" y2="21" />
                    </svg>
                    <span class="sr-only">桌面视图</span>
                </button>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center space-x-2 md:space-x-3 mt-0 md:mt-0">
                <button @click="toggleOrientation"
                    class="p-2 md:p-2.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 touch-manipulation"
                    :class="{ 'opacity-50 pointer-events-none': deviceType === 'desktop', 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400': isLandscape && deviceType !== 'desktop' }"
                    :disabled="deviceType === 'desktop'"
                    title="切换横竖屏"
                    aria-label="切换横竖屏方向"
                    :aria-pressed="isLandscape">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M21 12H3M12 3v18" />
                    </svg>
                    <span class="sr-only">切换横竖屏</span>
                </button>
                <button @click="refreshPreview"
                    class="p-2 md:p-2.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 touch-manipulation"
                    title="刷新预览"
                    aria-label="刷新预览内容">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M21 2v6h-6M3 12a9 9 0 0 1 15-6.7L21 8M3 22v-6h6M21 12a9 9 0 0 1-15 6.7L3 16" />
                    </svg>
                    <span class="sr-only">刷新预览</span>
                </button>
                <!-- 局部编辑按钮 -->
                <button @click="toggleEditMode"
                    class="p-2 md:p-2.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 touch-manipulation"
                    :class="{ 'bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400': isEditMode }"
                    title="局部编辑"
                    aria-label="切换局部编辑模式"
                    :aria-pressed="isEditMode">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                    <span class="sr-only">局部编辑</span>
                </button>
                <button @click="toggleFullscreen"
                    class="p-2 md:p-2.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 touch-manipulation"
                    :class="{ 'bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400': isFullscreen }"
                    title="全屏预览"
                    aria-label="切换全屏预览模式"
                    :aria-pressed="isFullscreen">
                    <svg v-if="!isFullscreen" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                        <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" />
                    </svg>
                    <span class="sr-only">全屏预览</span>
                </button>
            </div>
        </div>

        <!-- 设备预览框 -->
        <div class="device-frame-container">
            <transition name="device-change">
                <DeviceFrame ref="deviceFrameRef" :device-type="deviceType" :is-landscape="isLandscape"
                    :is-fullscreen="isFullscreen" :html-content="code" :device-style="currentDeviceStyle"
                    :device-class="previewContainerClass"
                    aria-label="代码预览内容"
                    :aria-live="'polite'"
                    @update:html-content="handleHtmlContentUpdate"
                    @element-edited="(data) => $emit('element-edited', data)" />
            </transition>
        </div>

        <!-- 加载状态指示器 -->
        <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 z-10"
            role="status" aria-live="polite">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex items-center space-x-3">
                <svg class="animate-spin h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>加载预览中...</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
import DeviceFrame from './DeviceFrame.vue';

const props = defineProps({
    code: {
        type: String,
        required: true
    }
});

const emit = defineEmits([
    'change-device',
    'toggle-orientation',
    'toggle-fullscreen',
    'refresh',
    'container-resize',
    'update:code'
]);

// 设备预览状态
const deviceType = ref<'mobile' | 'tablet' | 'desktop'>('desktop');
const isLandscape = ref(false);
const isFullscreen = ref(false);
const previewContainerClass = ref('');
const fullscreenContainerClass = ref('');
const currentDeviceStyle = ref({});
const isLoading = ref(false); // 加载状态
const isEditMode = ref(false); // 编辑模式状态

// 引用
const deviceFrameRef = ref<InstanceType<typeof DeviceFrame> | null>(null);
const previewContainerRef = ref<HTMLElement | null>(null);
const containerSize = ref({ width: 0, height: 0 });
let resizeObserver: ResizeObserver | null = null;

// 刷新预览
const refreshPreview = () => {
    // 静默刷新，不显示加载状态
    deviceFrameRef.value?.refreshPreview();
    emit('refresh');
};

// 设置CSS变量
const setDeviceScaleVariable = (scale: number) => {
    if (previewContainerRef.value) {
        previewContainerRef.value.style.setProperty('--device-scale', scale.toString());
    }
};

// 更新设备缩放比例
const updateDeviceScale = () => {
    if (!previewContainerRef.value) return;

    const containerWidth = previewContainerRef.value.clientWidth;
    const containerHeight = previewContainerRef.value.clientHeight;

    // 获取设备尺寸配置
    const deviceSizes = {
        mobile: {
            portrait: { width: 375, height: 667 },
            landscape: { width: 667, height: 375 }
        },
        tablet: {
            // 使用 7:5 的宽高比，根据用户要求
            portrait: { width: 500, height: 700 },
            landscape: { width: 700, height: 500 }
        }
    };

    // 根据设备类型和方向计算合适的缩放比例
    let scale = 1;

    if (deviceType.value === 'mobile' || deviceType.value === 'tablet') {
        // 获取当前设备类型和方向的尺寸
        const orientation = isLandscape.value ? 'landscape' : 'portrait';
        const deviceSize = deviceSizes[deviceType.value][orientation];

        // 计算容器可用空间
        // 为不同设备类型调整内边距
        const padding = deviceType.value === 'mobile' ? 40 : 30; // 减小平板的内边距
        const paddingVertical = deviceType.value === 'mobile' ? 80 : 50; // 减小平板的垂直内边距

        // 计算缩放比例
        const widthScale = (containerWidth - padding) / deviceSize.width;
        const heightScale = (containerHeight - paddingVertical) / deviceSize.height;

        // 限制缩放比例范围
        // 为平板设置更大的最小缩放比例
        const minScale = deviceType.value === 'mobile' ? 0.3 : 0.4;
        // 为平板设置更大的最大缩放比例
        const maxScale = deviceType.value === 'mobile' ? 1.2 : 1.2;

        // 选择最合适的缩放比例
        scale = Math.min(widthScale, heightScale);

        // 平板模式下的特殊处理，增大缩放比例
        if (deviceType.value === 'tablet') {
            // 增加一个缩放因子，使平板显示更大
            scale = scale * 1.15;
        }

        // 限制缩放比例范围
        scale = Math.max(minScale, Math.min(scale, maxScale));

        // 平板端特殊处理
        if (deviceType.value === 'tablet') {
            if (!isLandscape.value) {
                // 竖屏模式下，使用更小的缩放比例
                // 这样在切换到横屏时变化更明显
                scale = scale * 0.85;

                // 如果容器高度不足，进一步调整
                if (containerHeight < 800) {
                    scale = Math.min(scale, 0.55);
                }
            } else {
                // 横屏模式下，使用更大的缩放比例
                // 这样在切换到竖屏时变化更明显
                scale = scale * 1.25;

                // 确保充分利用空间
                if (containerWidth < 1100) {
                    scale = Math.min(scale, 0.85);
                } else {
                    // 在大屏幕上显示更大
                    scale = Math.min(scale, 0.95);
                }
            }
        }
    } else {
        // 桌面视图，保持原始大小
        scale = 1;
    }

    // 应用缩放比例
    setDeviceScaleVariable(scale);

    // 更新容器尺寸信息
    containerSize.value = {
        width: containerWidth,
        height: containerHeight
    };

    emit('container-resize', containerSize.value);
};

// 调整全屏模式下的设备尺寸
const adjustFullscreenDeviceSize = () => {
    if (isFullscreen.value && previewContainerRef.value) {
        const containerWidth = window.innerWidth;
        const containerHeight = window.innerHeight;

        // 获取设备尺寸配置
        const deviceSizes = {
            mobile: {
                portrait: { width: 375, height: 667 },
                landscape: { width: 667, height: 375 }
            },
            tablet: {
                // 使用 7:5 的宽高比，根据用户要求
                portrait: { width: 500, height: 700 },
                landscape: { width: 700, height: 500 }
            }
        };

        let scale = 1;

        if (deviceType.value === 'mobile' || deviceType.value === 'tablet') {
            // 获取当前设备类型和方向的尺寸
            const orientation = isLandscape.value ? 'landscape' : 'portrait';
            const deviceSize = deviceSizes[deviceType.value][orientation];

            // 全屏模式下的内边距调整
            // 为平板设置更小的内边距，使其显示更大
            const padding = deviceType.value === 'mobile' ? 100 : 80; // 水平方向的内边距
            const paddingVertical = deviceType.value === 'mobile' ? 140 : 100; // 垂直方向的内边距

            // 计算缩放比例
            const widthScale = (containerWidth - padding) / deviceSize.width;
            const heightScale = (containerHeight - paddingVertical) / deviceSize.height;

            // 选择最合适的缩放比例
            scale = Math.min(widthScale, heightScale);

            // 平板模式下的特殊处理，增大缩放比例
            if (deviceType.value === 'tablet') {
                if (isLandscape.value) {
                    // 横屏模式下显示更大
                    scale = scale * 1.3;
                } else {
                    // 竖屏模式下显示稍小一点
                    scale = scale * 1.1;
                }
            }

            // 全屏模式下允许更大的缩放比例
            const maxScale = deviceType.value === 'mobile' ? 2.0 : 2.0; // 增大平板的最大缩放比例
            scale = Math.min(scale, maxScale);

            // 确保缩放比例不会太小
            const minFullscreenScale = deviceType.value === 'mobile' ? 0.5 : 0.7; // 增大平板的最小缩放比例
            scale = Math.max(minFullscreenScale, scale);

            // 对于小屏幕设备的特殊处理
            if (containerWidth < 500 || containerHeight < 600) {
                // 在小屏幕上进一步减小缩放比例
                scale = Math.min(scale, deviceType.value === 'mobile' ? 0.8 : 0.5);
            }
        } else {
            // 桌面视图在全屏模式下保持原始大小
            scale = 1;
        }

        // 应用缩放比例
        setDeviceScaleVariable(scale);
    }
};

// 切换设备类型
const changeDeviceType = (type: 'mobile' | 'tablet' | 'desktop') => {
    deviceType.value = type;
    emit('change-device', type);

    // 桌面模式下强制设置为横向
    if (type === 'desktop') {
        isLandscape.value = true;
    }

    // 更新缩放比例
    setTimeout(() => {
        updateDeviceScale();
    }, 10);
};

// 切换横竖屏
const toggleOrientation = () => {
    // 桌面模式下不允许切换
    if (deviceType.value === 'desktop') return;

    isLandscape.value = !isLandscape.value;
    emit('toggle-orientation', isLandscape.value);

    // 更新缩放比例
    setTimeout(() => {
        updateDeviceScale();
    }, 10);
};

// 切换全屏模式
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
    emit('toggle-fullscreen', isFullscreen.value);

    // 全屏模式下需要重新计算缩放比例
    setTimeout(() => {
        if (isFullscreen.value) {
            adjustFullscreenDeviceSize();
        } else {
            updateDeviceScale();
        }
    }, 100);
};

// 监听窗口大小变化
const handleResize = () => {
    if (isFullscreen.value) {
        adjustFullscreenDeviceSize();
    } else {
        updateDeviceScale();
    }
};

// 监听ESC键退出全屏
const handleEscKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isFullscreen.value) {
        toggleFullscreen();
    }
};

// 监听代码变化，刷新预览
watch(() => props.code, (newCode) => {
    // 静默刷新，不显示提示
    if (!newCode || !newCode.trim()) return;

    try {
        // 使用Vue的nextTick确保状态已更新
        nextTick(() => {
            // 静默调用刷新，不显示加载状态
            deviceFrameRef.value?.refreshPreview();
            emit('refresh');

            // 对于大型代码块，可能需要再次刷新
            const codeSize = newCode.length;

            // 根据代码大小动态调整延迟
            const delay = codeSize > 10000 ? 500 : 300;

            // 添加一个延迟刷新，确保内容完全加载
            setTimeout(() => {
                deviceFrameRef.value?.refreshPreview();
            }, delay);
        });
    } catch (error) {
        console.error('刷新预览时出错:', error);
    }
}, { immediate: true });

// 监听横竖屏变化，需要重新计算缩放比例
watch(() => isLandscape.value, () => {
    // 获取预览容器
    const previewFrame = document.querySelector('.device-frame');
    if (previewFrame) {
        // 在旋转前先重置缩放比例，使旋转效果更明显
        // 对于平板设备，使用更小的初始缩放比例，以便旋转后的变化更明显
        if (deviceType.value === 'tablet') {
            const tempScale = isLandscape.value ? 0.5 : 0.4;
            setDeviceScaleVariable(tempScale);
        }

        // 添加旋转动画类
        previewFrame.classList.add('device-rotating');

        // 添加设备类型特定的旋转类
        const deviceClass = deviceType.value === 'tablet' ? 'tablet-rotating' : 'device-rotating';
        previewFrame.classList.add(deviceClass);

        // 添加旋转方向类，根据当前状态决定旋转方向
        if (isLandscape.value) {
            previewFrame.classList.add('rotate-to-landscape');
            previewFrame.classList.remove('rotate-to-portrait');
        } else {
            previewFrame.classList.add('rotate-to-portrait');
            previewFrame.classList.remove('rotate-to-landscape');
        }

        // 延迟移除旋转动画类，以允许过渡完成
        setTimeout(() => {
            previewFrame.classList.remove('device-rotating');
            previewFrame.classList.remove('tablet-rotating');

            // 更新缩放比例
            updateDeviceScale();

            // 如果是全屏模式，调整全屏尺寸
            if (isFullscreen.value) {
                adjustFullscreenDeviceSize();
            }

            // 强制刷新预览，确保内容正确显示
            setTimeout(() => {
                refreshPreview();
            }, 100);
        }, 500); // 增加过渡时间以使动画更流畅
    } else {
        // 如果没有找到预览容器，仍然更新缩放比例
        updateDeviceScale();
    }
});

// 监听全屏状态变化
watch(() => isFullscreen.value, () => {
    // 获取预览容器
    const previewFrame = document.querySelector('.device-frame');

    if (isFullscreen.value) {
        // 进入全屏模式
        if (previewFrame) {
            // 添加全屏过渡动画类
            previewFrame.classList.add('fullscreen-transition');

            // 设置全屏容器类
            fullscreenContainerClass.value = `fullscreen-${deviceType.value} fullscreen-${isLandscape.value ? 'landscape' : 'portrait'}`;

            // 延迟调整全屏尺寸，等待过渡完成
            setTimeout(() => {
                adjustFullscreenDeviceSize();

                // 移除过渡类
                previewFrame.classList.remove('fullscreen-transition');

                // 添加全屏事件监听
                window.addEventListener('resize', handleResize);

                // 添加ESC键退出全屏的监听
                window.addEventListener('keydown', handleEscKeydown);
            }, 300);
        } else {
            // 如果没有找到预览容器，仍然调整全屏尺寸
            adjustFullscreenDeviceSize();
            window.addEventListener('resize', handleResize);
            window.addEventListener('keydown', handleEscKeydown);
        }
    } else {
        // 退出全屏模式
        if (previewFrame) {
            // 添加退出全屏过渡动画类
            previewFrame.classList.add('exit-fullscreen-transition');

            // 清除全屏容器类
            fullscreenContainerClass.value = '';

            // 延迟更新缩放比例，等待过渡完成
            setTimeout(() => {
                updateDeviceScale();

                // 移除过渡类
                previewFrame.classList.remove('exit-fullscreen-transition');

                // 移除全屏事件监听
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('keydown', handleEscKeydown);
            }, 300);
        } else {
            // 如果没有找到预览容器，仍然更新缩放比例
            updateDeviceScale();
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('keydown', handleEscKeydown);
        }
    }
});

// 手势相关变量
let touchStartX = 0;
let touchStartY = 0;
let touchStartTime = 0;
let pinchStartDistance = 0;
let initialScale = 1;

// 处理触摸开始
const handleTouchStart = (event: TouchEvent) => {
    if (event.touches.length === 1) {
        // 单指触摸
        touchStartX = event.touches[0].clientX;
        touchStartY = event.touches[0].clientY;
        touchStartTime = Date.now();
    } else if (event.touches.length === 2) {
        // 双指触摸 (缩放手势)
        const dx = event.touches[0].clientX - event.touches[1].clientX;
        const dy = event.touches[0].clientY - event.touches[1].clientY;
        pinchStartDistance = Math.sqrt(dx * dx + dy * dy);
        initialScale = parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--device-scale') || '1');
    }
};

// 处理触摸移动
const handleTouchMove = (event: TouchEvent) => {
    if (event.touches.length === 2 && deviceType.value !== 'desktop') {
        // 双指缩放手势
        event.preventDefault(); // 阻止默认行为

        const dx = event.touches[0].clientX - event.touches[1].clientX;
        const dy = event.touches[0].clientY - event.touches[1].clientY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 计算新的缩放比例
        const scale = initialScale * (distance / pinchStartDistance);

        // 限制缩放范围
        const minScale = 0.5;
        const maxScale = deviceType.value === 'mobile' ? 2.0 : 1.5;
        const clampedScale = Math.max(minScale, Math.min(scale, maxScale));

        // 应用新的缩放比例
        setDeviceScaleVariable(clampedScale);
    }
};

// 处理触摸结束
const handleTouchEnd = (event: TouchEvent) => {
    if (event.touches.length === 0 && Date.now() - touchStartTime < 300) {
        // 快速点击
        const touchEndX = event.changedTouches[0].clientX;
        const touchEndY = event.changedTouches[0].clientY;

        // 计算移动距离
        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;

        // 如果移动距离很小，认为是点击而非滑动
        if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
            // 双击切换全屏
            if (Date.now() - lastTapTime < 300) {
                toggleFullscreen();
                lastTapTime = 0; // 重置以避免连续触发
            } else {
                lastTapTime = Date.now();
            }
        }
    }
};

// 记录最后一次点击时间，用于检测双击
let lastTapTime = 0;

// 组件挂载时初始化
onMounted(() => {
    // 初始化ResizeObserver监听容器大小变化
    if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
            if (!isFullscreen.value) {
                updateDeviceScale();
            }
        });

        if (previewContainerRef.value) {
            resizeObserver.observe(previewContainerRef.value);

            // 添加触摸事件监听
            const container = previewContainerRef.value;
            container.addEventListener('touchstart', handleTouchStart, { passive: false });
            container.addEventListener('touchmove', handleTouchMove, { passive: false });
            container.addEventListener('touchend', handleTouchEnd);
        }
    }

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 初始更新缩放比例
    setTimeout(() => {
        updateDeviceScale();
    }, 100);
});

// 组件卸载时清理
onUnmounted(() => {
    // 清理ResizeObserver
    if (resizeObserver) {
        resizeObserver.disconnect();
        resizeObserver = null;
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);

    // 移除触摸事件监听
    if (previewContainerRef.value) {
        const container = previewContainerRef.value;
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('touchend', handleTouchEnd);
    }
});

// 切换编辑模式
const toggleEditMode = () => {
    isEditMode.value = !isEditMode.value;

    // 调用DeviceFrame组件的编辑模式切换方法
    if (deviceFrameRef.value) {
        deviceFrameRef.value.toggleEditMode();
    }
};

// 监听HTML内容变化
const handleHtmlContentUpdate = (newHtml: string) => {
    // 将更新后的HTML内容发送给父组件
    emit('update:code', newHtml);
};

// 监听编辑模式状态变化
watch(() => deviceFrameRef.value?.isEditMode, (newValue) => {
    if (newValue !== undefined) {
        isEditMode.value = newValue;
    }
});

// 暴露方法给父组件
defineExpose({
    refreshPreview,
    changeDeviceType,
    toggleOrientation,
    toggleFullscreen,
    toggleEditMode
});
</script>

<style scoped>
.device-preview-container {
    --device-scale: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 设备框架容器，确保设备居中 */
.device-frame-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
}

.fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    transition: background-color 0.3s ease;
}

/* 全屏模式下的设备框架容器 */
.fullscreen-container .device-frame-container {
    flex: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 全屏模式下的设备类型特定样式 */
.fullscreen-mobile {
    --fullscreen-padding: 40px;
}

.fullscreen-tablet {
    --fullscreen-padding: 60px;
}

/* 全屏模式下的方向特定样式 */
.fullscreen-landscape {
    --fullscreen-orientation: landscape;
}

.fullscreen-portrait {
    --fullscreen-orientation: portrait;
}

/* 全屏过渡动画 */
.fullscreen-transition {
    animation: enterFullscreen 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.exit-fullscreen-transition {
    animation: exitFullscreen 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes enterFullscreen {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
    }
    100% {
        transform: scale(var(--device-scale, 1));
        opacity: 1;
    }
}

@keyframes exitFullscreen {
    0% {
        transform: scale(var(--device-scale, 1));
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0.8;
    }
}

.device-controls {
    z-index: 10;
    /* 移动设备上的优化 */
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 设备切换动画 */
.device-change-enter-active,
.device-change-leave-active {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.device-change-enter-from,
.device-change-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

/* 横竖屏旋转动画 */
.device-rotating {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 平板设备的旋转动画特殊处理 */
.tablet-rotating {
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 向横屏旋转的动画 */
.rotate-to-landscape {
    animation: rotateToLandscape 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* 向竖屏旋转的动画 */
.rotate-to-portrait {
    animation: rotateToPortrait 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* 平板设备的旋转动画 */
.tablet-rotating.rotate-to-landscape {
    animation: tabletRotateToLandscape 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.tablet-rotating.rotate-to-portrait {
    animation: tabletRotateToPortrait 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes rotateToLandscape {
    0% {
        transform: scale(var(--device-scale, 1)) rotate(0deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(var(--device-scale, 1)) rotate(90deg);
        opacity: 1;
    }
}

@keyframes rotateToPortrait {
    0% {
        transform: scale(var(--device-scale, 1)) rotate(90deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(var(--device-scale, 1)) rotate(0deg);
        opacity: 1;
    }
}

/* 平板特殊的旋转动画，增加缩放效果 */
@keyframes tabletRotateToLandscape {
    0% {
        transform: scale(0.4) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(0.45) rotate(45deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.5) rotate(90deg);
        opacity: 1;
    }
}

@keyframes tabletRotateToPortrait {
    0% {
        transform: scale(0.5) rotate(90deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(0.45) rotate(45deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.4) rotate(0deg);
        opacity: 1;
    }
}

/* 移动设备上的触摸优化 */
@media (max-width: 640px) {
    .device-controls {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .touch-manipulation {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
}
</style>
