<template>
  <NCard
    class="work-card"
    :class="{ 'list-mode': viewMode === 'list' }"
    hoverable
    @click="handleCardClick"
  >
    <div class="card-content">
      <!-- 封面图 -->
      <div class="cover-container">
        <img
          v-if="work.coverImg"
          :src="work.coverImg"
          :alt="work.title"
          class="cover-image"
        />
        <div v-else class="cover-placeholder">
          <span class="book-emoji">📚</span>
        </div>

        <!-- 状态标签 - 使用更友好的图标和文字 -->
        <div class="status-badge" :class="statusClass">
          <span class="status-emoji">{{ statusEmoji }}</span>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>

      <!-- 作品信息 -->
      <div class="work-info">
        <h3 class="work-title" :title="work.title">{{ work.title || '未命名绘本' }}</h3>

        <p v-if="viewMode === 'list'" class="work-description">
          {{ work.description || '暂无描述' }}
        </p>

        <div class="work-meta">
          <div class="meta-item">
            <span class="meta-emoji">📅</span>
            <span>{{ formatDate(work.createdAt) }}</span>
          </div>

          <div class="meta-item">
            <span class="meta-emoji">📄</span>
            <span>{{ work.pageCount || 0 }}页</span>
          </div>

          <div v-if="isRecentlyEdited" class="recent-badge">
            <span class="meta-emoji">⏰</span>
            <span>最近编辑</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要操作按钮 - 更加突出和友好 -->
    <div class="main-actions" @click.stop>
      <NButton class="edit-button" @click="handleEdit" title="编辑故事">
        <span class="button-emoji">✏️</span>
        <span class="button-text">编辑</span>
      </NButton>

      <NButton class="view-button" @click="handleView" title="阅读故事">
        <span class="button-emoji">👀</span>
        <span class="button-text">阅读</span>
      </NButton>
    </div>

    <!-- 次要操作按钮 - 更简洁的下拉菜单 -->
    <div class="more-actions" @click.stop>
      <NDropdown
        trigger="click"
        :options="actionOptions"
        @select="handleAction"
      >
        <NButton class="more-button" title="更多操作">
          <span class="button-emoji">⋯</span>
        </NButton>
      </NDropdown>
    </div>
  </NCard>
</template>

<script setup lang="ts">
import { computed, h } from 'vue';
import { NCard, NButton, NButtonGroup, NDropdown } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { Work } from '@/store/modules/works';

const props = defineProps({
  work: {
    type: Object as () => Work,
    required: true
  },
  viewMode: {
    type: String,
    default: 'grid'
  }
});

const emit = defineEmits(['edit', 'view', 'delete', 'export', 'share', 'move']);

// 状态文本 - 更友好的表述
const statusText = computed(() => {
  switch (props.work.status) {
    case 0: return '创作中';
    case 1: return '写好啦';
    case 2: return '已分享';
    default: return '新故事';
  }
});

// 状态表情符号 - 使用emoji增加趣味性
const statusEmoji = computed(() => {
  switch (props.work.status) {
    case 0: return '✍️';
    case 1: return '🎉';
    case 2: return '🌟';
    default: return '📝';
  }
});

// 状态样式类
const statusClass = computed(() => {
  switch (props.work.status) {
    case 0: return 'status-draft';
    case 1: return 'status-completed';
    case 2: return 'status-published';
    default: return '';
  }
});

// 是否最近编辑（7天内）
const isRecentlyEdited = computed(() => {
  if (!props.work.lastEditedAt) return false;

  const lastEditTime = new Date(props.work.lastEditedAt).getTime();
  const now = new Date().getTime();
  const sevenDays = 7 * 24 * 60 * 60 * 1000;
  return (now - lastEditTime) < sevenDays;
});

// 下拉菜单选项 - 使用emoji和更友好的文字
const actionOptions = [
  {
    label: '分享给朋友',
    key: 'share',
    icon: () => h('span', { class: 'menu-emoji' }, '🔗')
  },
  {
    label: '保存到收藏夹',
    key: 'move',
    icon: () => h('span', { class: 'menu-emoji' }, '📁')
  },
  {
    label: '下载PDF',
    key: 'export-pdf',
    icon: () => h('span', { class: 'menu-emoji' }, '📄')
  },
  {
    label: '保存图片',
    key: 'export-images',
    icon: () => h('span', { class: 'menu-emoji' }, '🖼️')
  },
  {
    type: 'divider',
    key: 'd1'
  },
  {
    label: '放入回收站',
    key: 'delete',
    icon: () => h('span', { class: 'menu-emoji' }, '🗑️')
  }
];

// 格式化日期 - 更友好的日期显示
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

  // 对于小学生更友好的日期表示
  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    // 超过一周才显示具体日期
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    });
  }
};

// 点击卡片
const handleCardClick = () => {
  emit('view', props.work);
};

// 编辑作品
const handleEdit = () => {
  emit('edit', props.work);
};

// 查看作品
const handleView = () => {
  emit('view', props.work);
};

// 处理下拉菜单操作
const handleAction = (key) => {
  switch (key) {
    case 'delete':
      emit('delete', props.work);
      break;
    case 'export-pdf':
      emit('export', { work: props.work, format: 'pdf' });
      break;
    case 'export-images':
      emit('export', { work: props.work, format: 'images' });
      break;
    case 'share':
      emit('share', props.work);
      break;
    case 'move':
      emit('move', props.work);
      break;
  }
};
</script>

<style scoped>
/* 儿童友好的卡片样式 */
.work-card {
  height: 100%;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* 弹性动画效果 */
  position: relative;
  overflow: hidden;
  border-radius: 1.2rem !important; /* 更圆润的边角 */
  background: linear-gradient(135deg, #ffffff, #f0f9ff) !important; /* 更明亮的背景 */
  border: 2px solid rgba(186, 230, 253, 0.8) !important; /* 更明显的边框 */
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.1),
              0 2px 4px rgba(59, 130, 246, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

.dark .work-card {
  background: linear-gradient(135deg, #1e293b, #0f172a) !important;
  border: 2px solid rgba(96, 165, 250, 0.3) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15),
              0 2px 4px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

/* 卡片悬停效果 - 更加生动 */
.work-card:hover {
  transform: translateY(-8px) scale(1.02); /* 更明显的上浮效果 */
  box-shadow: 0 15px 30px rgba(59, 130, 246, 0.15),
              0 8px 15px rgba(59, 130, 246, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(59, 130, 246, 0.6) !important; /* 更鲜艳的边框 */
}

.dark .work-card:hover {
  box-shadow: 0 15px 30px rgba(59, 130, 246, 0.2),
              0 8px 15px rgba(59, 130, 246, 0.15),
              inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(96, 165, 250, 0.6) !important;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0.5rem; /* 增加内边距 */
}

.work-card.list-mode .card-content {
  flex-direction: row;
  gap: 1.2rem;
}

/* 封面容器样式 - 更加突出 */
.cover-container {
  position: relative;
  height: 160px;
  overflow: hidden;
  border-radius: 1rem; /* 更圆润的边角 */
  margin-bottom: 1rem;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  border: 3px solid white; /* 相框效果 */
}

.work-card:hover .cover-container {
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.15);
  transform: scale(1.02); /* 轻微放大效果 */
}

.work-card.list-mode .cover-container {
  width: 120px;
  height: 120px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.work-card:hover .cover-image {
  transform: scale(1.08); /* 更明显的缩放效果 */
}

/* 封面占位符 - 使用emoji */
.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0f2fe, #bae6fd); /* 更鲜艳的颜色 */
  transition: all 0.4s ease;
}

.dark .cover-placeholder {
  background: linear-gradient(135deg, #334155, #1e293b);
}

.book-emoji {
  font-size: 3rem; /* 更大的emoji */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.4s ease;
}

.work-card:hover .book-emoji {
  transform: scale(1.2) rotate(5deg); /* 添加旋转效果 */
}

/* 各元素使用类似的悬停动画效果 */

/* 状态标签 - 更加友好和醒目 */
.status-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.work-card:hover .status-badge {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.status-emoji {
  font-size: 1rem;
}

.status-draft {
  background: linear-gradient(135deg, #f97316, #fb923c); /* 橙色 */
}

.status-completed {
  background: linear-gradient(135deg, #22c55e, #4ade80); /* 绿色 */
}

.status-published {
  background: linear-gradient(135deg, #3b82f6, #60a5fa); /* 蓝色 */
}

/* 作品信息样式 */
.work-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0.5rem 0;
}

.work-title {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 添加文字阴影 */
}

.dark .work-title {
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.work-card:hover .work-title {
  color: #3b82f6;
  transform: translateY(-2px); /* 轻微上移效果 */
}

.dark .work-card:hover .work-title {
  color: #60a5fa;
}

.work-description {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #64748b;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
}

.dark .work-description {
  color: #94a3b8;
}

/* 元数据样式 - 使用emoji */
.work-meta {
  margin-top: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.8rem;
  color: #64748b;
  padding-top: 0.5rem;
  border-top: 1px dashed rgba(186, 230, 253, 0.8);
}

.dark .work-meta {
  color: #94a3b8;
  border-top: 1px dashed rgba(51, 65, 85, 0.8);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: all 0.3s ease;
}

.meta-emoji {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.work-card:hover .meta-emoji {
  transform: scale(1.2) rotate(5deg);
}

.work-card:hover .meta-item {
  color: #3b82f6;
}

/* 最近编辑标签 - 更加醒目 */
.recent-badge {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  background: linear-gradient(135deg, #f43f5e, #fb7185);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 0.8rem;
  font-weight: 600;
  font-size: 0.75rem;
  box-shadow: 0 3px 6px rgba(244, 63, 94, 0.2);
  animation: pulse 2s infinite; /* 添加脉冲动画 */
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 主要操作按钮 - 更加突出和友好 */
.main-actions {
  position: absolute;
  bottom: 0.75rem;
  left: 0.75rem;
  right: 0.75rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 10;
}

.work-card:hover .main-actions {
  opacity: 1;
  transform: translateY(0);
}

.edit-button, .view-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.8rem !important;
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.edit-button {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.view-button {
  background: linear-gradient(135deg, #f97316, #fb923c) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(249, 115, 22, 0.3) !important;
}

.edit-button:hover, .view-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
}

.button-emoji {
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.edit-button:hover .button-emoji, .view-button:hover .button-emoji {
  transform: scale(1.2) rotate(10deg);
}

/* 次要操作按钮 - 更简洁的下拉菜单 */
.more-actions {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  z-index: 10;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.work-card:hover .more-actions {
  opacity: 1;
  transform: scale(1);
}

.more-button {
  width: 2.2rem !important;
  height: 2.2rem !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(4px) !important;
  border: 2px solid rgba(226, 232, 240, 0.8) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}

.dark .more-button {
  background: rgba(30, 41, 59, 0.9) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
}

.more-button:hover {
  transform: rotate(90deg) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.menu-emoji {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

/* 列表模式特殊样式 */
.work-card.list-mode .main-actions {
  right: 1rem;
  left: auto;
  bottom: auto;
  top: 50%;
  transform: translateY(-50%) translateX(10px);
  flex-direction: column;
  width: auto;
}

.work-card.list-mode:hover .main-actions {
  transform: translateY(-50%) translateX(0);
}

.work-card.list-mode .more-actions {
  top: 0.75rem;
  right: 0.75rem;
}
</style>
