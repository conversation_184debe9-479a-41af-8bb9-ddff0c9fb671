<template>
  <div class="code-editor-block" role="region" aria-label="代码编辑器">
    <!-- 代码编辑器区域 -->
    <div class="w-full border rounded-lg dark:border-gray-700/80 mb-2 editor-container"
      :class="{ 'small-content': isSmallContent }">
      <div class="editor-header px-3 py-2 bg-gray-50 dark:bg-gray-800 border-b dark:border-gray-700 flex justify-between items-center">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="editor-title">
          HTML代码编辑器
        </span>
        <span v-if="code" class="text-xs text-gray-500 dark:text-gray-400">
          {{ codeLineCount }}行代码
        </span>
      </div>
      <CodeMirrorEditor
        :model-value="code"
        @update:model-value="handleCodeChange"
        :dark="isDarkMode"
        :html-mode="true"
        class="w-full"
        ref="editorRef"
        aria-labelledby="editor-title"
      />
    </div>

    <!-- 工具栏 -->
    <div class="flex justify-between items-center mb-2" role="toolbar" aria-label="代码编辑器工具栏">
      <div class="flex space-x-2">
        <button
          @click="formatCode"
          class="px-3 py-1.5 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 hover-float ds-shadow-sm flex items-center"
          title="格式化代码"
          aria-label="格式化代码"
          :disabled="!code"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          <span>格式化</span>
        </button>
      </div>
      <div class="flex space-x-2">
        <button
          @click="previewCode"
          class="px-3 py-1.5 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 hover-float ds-shadow-sm flex items-center"
          title="预览代码"
          aria-label="在右侧栏预览 HTML 代码"
          :disabled="!code"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
          </svg>
          <span>预览</span>
        </button>

        <button
          @click="shareCode"
          class="px-3 py-1.5 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 hover-float ds-shadow-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          title="分享代码"
          aria-label="分享 HTML 代码"
          :disabled="!code || isSharing"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
          </svg>
          <span>{{ isSharing ? '分享中...' : '分享' }}</span>
        </button>

        <button
          @click="copyCode"
          class="px-3 py-1.5 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 hover-float ds-shadow-sm flex items-center"
          title="复制代码"
          aria-label="复制代码到剪贴板"
          :disabled="!code"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
          </svg>
          <span>复制</span>
        </button>
      </div>
    </div>
    <!-- 使用通用反馈提示组件 -->
    <FeedbackToast
      v-model:visible="operationFeedback.show"
      :message="operationFeedback.message"
      :type="operationFeedback.type"
      :duration="3000"
    />

    <!-- 分享对话框 -->
    <ShareDialog
      v-if="showShareDialog"
      :share-url="shareUrl"
      :on-close="() => showShareDialog = false"
      @close="showShareDialog = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch, reactive, nextTick, inject, onMounted, onUnmounted } from 'vue';
import { useMessage } from 'naive-ui';
import { useAppStore } from '@/store';
import { fetchCreateHtmlShare } from '@/api';
import CodeMirrorEditor from '@/components/CodeMirrorEditor.vue';
import FeedbackToast from '@/components/FeedbackToast.vue';
import ShareDialog from '@/components/ShareDialog.vue';

// 注入更新代码预览的函数
const updateCurrentCode = inject<(code: string) => void>('updateCurrentCode', () => {});

const props = defineProps<{
  code: string;
}>();

// 判断代码内容是否较短，用于动态调整编辑器高度
const isSmallContent = computed(() => {
  // 计算代码行数
  const lineCount = props.code.split('\n').length;
  // 如果行数少于20行，认为是小内容
  return lineCount < 20;
});

// 计算代码行数，用于显示
 const codeLineCount = computed(() => {
  return props.code.split('\n').length;
});

// 操作反馈状态
const operationFeedback = reactive({
  show: false,
  message: '',
  type: 'success' as 'success' | 'error'
});

// 显示操作反馈
const showFeedback = (message: string, type: 'success' | 'error' = 'success') => {
  // 设置新的反馈
  operationFeedback.message = message;
  operationFeedback.type = type;
  operationFeedback.show = true;
};

const emit = defineEmits<{
  (e: 'update:code', code: string): void;
}>();

const message = useMessage();
const appStore = useAppStore();
const editorRef = ref<InstanceType<typeof CodeMirrorEditor> | null>(null);
const isSharing = ref(false);
const showShareDialog = ref(false);
const shareUrl = ref('');



// 检测暗黑模式
const isDarkMode = computed(() => {
  return appStore.theme === 'dark';
});

// 处理代码变化
const handleCodeChange = (newCode: string) => {
  emit('update:code', newCode);
};

// 复制代码到剪贴板
const copyCode = () => {
  if (!props.code) return;

  navigator.clipboard.writeText(props.code)
    .then(() => {
      showFeedback('代码已复制到剪贴板', 'success');
      message.success('代码已复制到剪贴板');
    })
    .catch(err => {
      showFeedback('复制失败，请手动复制', 'error');
      message.error('复制失败，请手动复制');
      console.error('复制失败:', err);
    });
};

// 分享 HTML 代码
const shareCode = async () => {
  if (!props.code) {
    message.error('HTML内容不能为空');
    return;
  }

  try {
    isSharing.value = true;
    const response = await fetchCreateHtmlShare({
      htmlContent: props.code
    });

    console.log('分享响应数据:', response);
    // 处理不同的响应数据结构
    const shareCodeValue = response.data?.data?.data?.shareCode || response.data?.data?.shareCode || response.data?.shareCode;

    if (shareCodeValue) {
      // 判断是否使用哈希模式
      const isHashMode = window.location.href.includes('#/');
      shareUrl.value = isHashMode
        ? `${window.location.origin}/#/share/${shareCodeValue}`
        : `${window.location.origin}/share/${shareCodeValue}`;

      // 显示分享对话框，而不是直接复制到剪贴板
      showShareDialog.value = true;
      showFeedback('分享链接已生成', 'success');
    } else {
      showFeedback('创建分享失败', 'error');
      message.error('创建分享失败');
    }
  } catch (err) {
    console.error('分享失败:', err);
    showFeedback('分享失败', 'error');
    message.error('分享失败');
  } finally {
    isSharing.value = false;
  }
};



// 预览代码
const previewCode = () => {
  if (!props.code) return;

  try {
    // 调用注入的更新代码预览函数
    updateCurrentCode(props.code);

    // 显示成功反馈
    showFeedback('代码已加载到右侧预览区', 'success');
    message.success('代码已加载到右侧预览区');

    // 如果在移动设备上，可能需要显示预览区域
    const isMobile = window.innerWidth < 768;
    if (isMobile) {
      // 创建一个自定义事件，通知父组件显示预览区域
      const event = new CustomEvent('show-preview');
      window.dispatchEvent(event);
    }
  } catch (err) {
    showFeedback('加载预览失败', 'error');
    console.error('预览失败:', err);
    message.error('加载预览失败');
  }
};

// 格式化代码
const formatCode = () => {
  if (!editorRef.value || !props.code) return;

  try {
    editorRef.value.formatCode(prettifyHTML);
    showFeedback('代码格式化成功', 'success');
    message.success('代码格式化成功');
  } catch (err) {
    showFeedback('代码格式化失败', 'error');
    console.error('格式化失败:', err);
    message.error('代码格式化失败');
  }
};

// 美化HTML代码
const prettifyHTML = (html: string): string => {
  let formatted = '';
  let indent = '';
  const tab = '  '; // 两个空格作为缩进

  html.split(/>\s*</).forEach(element => {
    if (element.match(/^\/\w/)) {
      // 如果是闭合标签，减少缩进
      indent = indent.substring(tab.length);
    }

    formatted += indent + '<' + element + '>\n';

    if (element.match(/^<?\w[^>]*[^\/]$/) &&
      !element.startsWith('input') &&
      !element.startsWith('img') &&
      !element.startsWith('br')) {
      // 如果是开放标签且不是自闭合标签，增加缩进
      indent += tab;
    }
  });

  return formatted.substring(1, formatted.length - 2);
};

// 监听HTML编辑器更新事件
const handleHtmlEditorUpdate = (event: CustomEvent) => {
  if (!event.detail || !event.detail.code) return;

  const newCode = event.detail.code;
  console.log('收到HTML编辑器更新事件，代码长度:', newCode.length);

  // 更新编辑器内容
  emit('update:code', newCode);

  // 如果编辑器实例存在，直接设置值
  if (editorRef.value) {
    editorRef.value.setValue(newCode);
    showFeedback('HTML内容已同步更新', 'success');
  }
};

// 在组件挂载时添加事件监听
onMounted(() => {
  window.addEventListener('update-html-editor', handleHtmlEditorUpdate as EventListener);
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('update-html-editor', handleHtmlEditorUpdate as EventListener);
});

// 监听代码变化，使用更可靠的滚动机制
watch(() => props.code, (newCode, oldCode) => {
  if (!editorRef.value) return;

  // 判断是否是流式输出（新代码是旧代码的扩展）
  const isStreaming = oldCode && newCode && newCode.startsWith(oldCode);

  // 使用Vue的nextTick确保内容已更新
  nextTick(() => {
    try {
      // 滚动到底部
      editorRef.value?.scrollToBottom();

      // 如果是流式输出，添加一个定时器来持续滚动
      if (isStreaming) {
        // 创建一个定时器，在流式输出期间每100ms滚动一次
        const scrollInterval = setInterval(() => {
          if (editorRef.value) {
            editorRef.value.scrollToBottom();
          } else {
            clearInterval(scrollInterval); // 如果组件不存在了，清除定时器
          }
        }, 100);

        // 2秒后自动清除定时器
        setTimeout(() => {
          clearInterval(scrollInterval);
        }, 2000);
      }
    } catch (error) {
      console.error('滚动代码编辑器时出错:', error);
    }
  });
});
</script>

<style scoped>
.code-editor-block {
  margin: 1rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.editor-container {
  height: min(500px, 60vh); /* 使用相对高度和固定高度的最小值，确保不会超出屏幕 */
  overflow: hidden; /* 修改为hidden，避免出现多个滚动条 */
  transition: height 0.3s ease;
}

@media (max-width: 768px) {
  .editor-container {
    height: min(350px, 50vh); /* 移动设备上使用更小的高度 */
  }
}

/* 当代码较短时自适应高度 */
.editor-container.small-content {
  height: auto;
  min-height: 200px;
  max-height: min(500px, 60vh);
  overflow: hidden; /* 确保小内容模式下也不会出现多个滚动条 */
}
</style>
