Write-Host "正在运行AI绘本创作后台管理系统测试..." -ForegroundColor Green

Write-Host "1. 运行服务层单元测试" -ForegroundColor Cyan
Set-Location -Path src/service
npm test -- src/modules/storybook/storybook.service.spec.ts

Write-Host "2. 运行控制器单元测试" -ForegroundColor Cyan
npm test -- src/modules/storybook/storybook.controller.spec.ts

Write-Host "3. 运行管理员控制器单元测试" -ForegroundColor Cyan
npm test -- src/modules/storybook/storybook-admin.controller.spec.ts

Write-Host "4. 运行内容安全测试" -ForegroundColor Cyan
npm test -- src/modules/storybook/content-safety.spec.ts

Write-Host "5. 运行图像管理测试" -ForegroundColor Cyan
npm test -- src/modules/storybook/image-management.spec.ts

Write-Host "6. 运行端到端测试" -ForegroundColor Cyan
npm run test:e2e -- test/storybook.e2e-spec.ts

Write-Host "所有测试已完成！" -ForegroundColor Green
