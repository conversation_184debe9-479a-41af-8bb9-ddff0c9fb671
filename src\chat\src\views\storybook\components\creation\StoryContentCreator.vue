<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NCard, NTabs, NTabPane, NCollapse, NCollapseItem, NInput, NButton, NSpace, NDivider } from 'naive-ui';

const props = defineProps({
  projectData: {
    type: Object,
    required: true
  }
});

// 故事内容数据
const storyContent = reactive({
  pages: []
});

// 当前编辑的页面索引
const currentPageIndex = ref(0);

// 组件挂载时初始化数据
onMounted(() => {
  initializeStoryContent();
});

// 初始化故事内容
const initializeStoryContent = () => {
  // 如果项目中已有页面数据，使用它
  if (props.projectData.pages && props.projectData.pages.length > 0) {
    storyContent.pages = props.projectData.pages;
  } else {
    // 否则，根据场景创建页面
    createPagesFromScenes();
  }
};

// 根据场景创建页面
const createPagesFromScenes = () => {
  // 清空现有页面
  storyContent.pages = [];
  
  // 如果有场景数据，为每个场景创建一个页面
  if (props.projectData.outline && props.projectData.outline.scenes && props.projectData.outline.scenes.length > 0) {
    props.projectData.outline.scenes.forEach((scene, index) => {
      storyContent.pages.push({
        id: Date.now() + index,
        pageNumber: index + 1,
        image: scene.image || '',
        text: scene.description || '',
        scene: scene
      });
    });
  } else {
    // 如果没有场景数据，创建一个空白页面
    storyContent.pages.push({
      id: Date.now(),
      pageNumber: 1,
      image: '',
      text: '',
      scene: null
    });
  }
  
  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 添加新页面
const addNewPage = () => {
  const newPage = {
    id: Date.now(),
    pageNumber: storyContent.pages.length + 1,
    image: '',
    text: '',
    scene: null
  };
  
  storyContent.pages.push(newPage);
  currentPageIndex.value = storyContent.pages.length - 1;
  
  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 删除当前页面
const deleteCurrentPage = () => {
  if (storyContent.pages.length <= 1) {
    window.$message?.warning('至少需要保留一个页面');
    return;
  }
  
  storyContent.pages.splice(currentPageIndex.value, 1);
  
  // 更新页码
  storyContent.pages.forEach((page, index) => {
    page.pageNumber = index + 1;
  });
  
  // 调整当前页面索引
  if (currentPageIndex.value >= storyContent.pages.length) {
    currentPageIndex.value = storyContent.pages.length - 1;
  }
  
  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新页面内容
const updatePageContent = (text) => {
  if (storyContent.pages[currentPageIndex.value]) {
    storyContent.pages[currentPageIndex.value].text = text;
    
    // 更新项目数据
    props.projectData.pages = storyContent.pages;
  }
};

// 生成页面图像
const generatePageImage = () => {
  const currentPage = storyContent.pages[currentPageIndex.value];
  if (!currentPage) return;
  
  // 构建提示词
  let prompt = currentPage.text;
  
  // 如果有关联的场景，添加场景信息
  if (currentPage.scene) {
    prompt += `, ${currentPage.scene.setting || ''}, ${currentPage.scene.description || ''}`;
  }
  
  // 添加风格描述
  prompt += ', 儿童绘本风格, 简单可爱, 明亮色彩';
  
  // 使用Pollinations API生成图像
  currentPage.image = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}`;
  
  // 更新项目数据
  props.projectData.pages = storyContent.pages;
  
  window.$message?.success('图像生成成功！');
};

// 保存故事内容
const saveStoryContent = () => {
  // 更新项目数据
  props.projectData.pages = storyContent.pages;
  
  window.$message?.success('故事内容已保存');
};
</script>

<template>
  <div class="story-content-creator">
    <h2 class="page-title">故事创作</h2>
    <p class="page-description">为你的绘本创作故事内容</p>
    
    <div class="content-layout">
      <!-- 左侧：页面导航 -->
      <div class="page-navigation">
        <h3 class="section-title">页面</h3>
        
        <div class="page-list">
          <div
            v-for="(page, index) in storyContent.pages"
            :key="page.id"
            class="page-item"
            :class="{ active: index === currentPageIndex }"
            @click="currentPageIndex = index"
          >
            <div class="page-preview">
              <img v-if="page.image" :src="page.image" :alt="`页面 ${page.pageNumber}`" class="page-image" />
              <div v-else class="page-placeholder">
                <span class="placeholder-icon">🖼️</span>
              </div>
            </div>
            <div class="page-number">{{ page.pageNumber }}</div>
          </div>
          
          <div class="page-item add-page" @click="addNewPage">
            <div class="add-icon">+</div>
            <div class="add-label">添加页面</div>
          </div>
        </div>
      </div>
      
      <!-- 右侧：页面编辑 -->
      <div class="page-editor" v-if="storyContent.pages.length > 0">
        <div class="editor-header">
          <h3 class="editor-title">编辑页面 {{ currentPageIndex + 1 }}</h3>
          
          <div class="editor-actions">
            <NButton size="small" @click="deleteCurrentPage" class="delete-button">
              删除页面
            </NButton>
          </div>
        </div>
        
        <div class="editor-content">
          <!-- 页面预览 -->
          <div class="page-preview-large">
            <img 
              v-if="storyContent.pages[currentPageIndex]?.image" 
              :src="storyContent.pages[currentPageIndex].image" 
              :alt="`页面 ${storyContent.pages[currentPageIndex].pageNumber}`" 
              class="preview-image" 
            />
            <div v-else class="preview-placeholder">
              <span class="placeholder-icon">🖼️</span>
              <p class="placeholder-text">点击"生成图像"创建插图</p>
            </div>
            
            <NButton class="generate-image-btn" @click="generatePageImage">
              生成图像
            </NButton>
          </div>
          
          <!-- 页面文本 -->
          <div class="page-text-editor">
            <h4 class="text-editor-title">页面文本</h4>
            <NInput
              v-model:value="storyContent.pages[currentPageIndex].text"
              type="textarea"
              placeholder="在这里输入页面文本..."
              :autosize="{ minRows: 4, maxRows: 8 }"
              @update:value="updatePageContent"
            />
            
            <div class="text-editor-tips">
              <p>提示：描述这个页面上发生的事情，角色的对话和动作等。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="form-actions">
      <NButton type="primary" @click="saveStoryContent">
        保存故事内容
      </NButton>
    </div>
  </div>
</template>

<style scoped>
.story-content-creator {
  padding: 1rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.page-description {
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 1.5rem 0;
}

.content-layout {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.page-navigation {
  width: 200px;
  flex-shrink: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.page-item {
  cursor: pointer;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.page-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.page-item.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.page-preview {
  height: 100px;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.page-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f1f5f9;
}

.placeholder-icon {
  font-size: 2rem;
  color: #94a3b8;
}

.page-number {
  padding: 0.5rem;
  text-align: center;
  background-color: #f1f5f9;
  font-weight: 600;
  color: #64748b;
}

.page-item.active .page-number {
  background-color: #3b82f6;
  color: white;
}

.add-page {
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border: 2px dashed #cbd5e1;
}

.add-icon {
  font-size: 2rem;
  color: #64748b;
  line-height: 1;
}

.add-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.page-editor {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  background-color: white;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.editor-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.delete-button {
  color: #ef4444;
  border-color: #ef4444;
}

.editor-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page-preview-large {
  height: 300px;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  overflow: hidden;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f1f5f9;
}

.placeholder-text {
  font-size: 0.9rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.generate-image-btn {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-text-editor {
  margin-top: 1rem;
}

.text-editor-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #333;
}

.text-editor-tips {
  margin-top: 0.75rem;
  font-size: 0.8rem;
  color: #64748b;
}

.form-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}
</style>
