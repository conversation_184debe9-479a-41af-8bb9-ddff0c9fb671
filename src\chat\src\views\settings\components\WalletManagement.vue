<template>
  <div class="wallet-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">我的钱包</h1>
      <p class="page-description">查看余额、充值记录和积分管理</p>
    </div>

    <!-- 复用现有的 Wallet 组件 -->
    <div class="wallet-wrapper">
      <Wallet />
    </div>

    <!-- 快捷操作 -->
    <NCard class="quick-actions-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:flashlight-line" class="card-icon" />
          <span>快捷操作</span>
        </div>
      </template>
      
      <div class="actions-grid">
        <button
          @click="openPointsMall"
          class="action-btn primary"
        >
          <SvgIcon icon="ri:shopping-cart-2-line" class="action-icon" />
          <div class="action-content">
            <div class="action-title">积分商城</div>
            <div class="action-desc">购买积分套餐</div>
          </div>
        </button>

        <button
          @click="viewRechargeHistory"
          class="action-btn secondary"
        >
          <SvgIcon icon="ri:history-line" class="action-icon" />
          <div class="action-content">
            <div class="action-title">充值记录</div>
            <div class="action-desc">查看历史记录</div>
          </div>
        </button>

        <button
          @click="viewUsageStats"
          class="action-btn secondary"
        >
          <SvgIcon icon="ri:bar-chart-line" class="action-icon" />
          <div class="action-content">
            <div class="action-title">使用统计</div>
            <div class="action-desc">查看消费统计</div>
          </div>
        </button>

        <button
          @click="contactSupport"
          class="action-btn secondary"
        >
          <SvgIcon icon="ri:customer-service-2-line" class="action-icon" />
          <div class="action-content">
            <div class="action-title">客服支持</div>
            <div class="action-desc">获取帮助</div>
          </div>
        </button>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { NCard, useMessage } from 'naive-ui';
import { useGlobalStoreWithOut } from '@/store';
import { SvgIcon } from '@/components/common';
import Wallet from '@/components/common/UserCenter/Wallet.vue';

const useGlobalStore = useGlobalStoreWithOut();
const message = useMessage();

// 打开积分商城
const openPointsMall = () => {
  useGlobalStore.updateGoodsDialog(true);
};

// 查看充值记录
const viewRechargeHistory = () => {
  message.info('充值记录功能已在钱包组件中提供');
};

// 查看使用统计
const viewUsageStats = () => {
  message.info('使用统计功能开发中');
};

// 联系客服
const contactSupport = () => {
  message.info('客服支持功能开发中');
};
</script>

<style scoped>
.wallet-management {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.wallet-wrapper {
  @apply w-full;
}

.quick-actions-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply flex items-center space-x-2 text-gray-900 dark:text-gray-100;
}

.card-icon {
  @apply text-lg text-primary-600;
}

.actions-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.action-btn {
  @apply flex items-center space-x-4 p-4 rounded-lg border transition-all duration-200;
  @apply hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.action-btn.primary {
  @apply bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800;
  @apply hover:bg-primary-100 dark:hover:bg-primary-900/30;
}

.action-btn.secondary {
  @apply bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
}

.action-icon {
  @apply text-2xl flex-shrink-0;
}

.action-btn.primary .action-icon {
  @apply text-primary-600;
}

.action-btn.secondary .action-icon {
  @apply text-gray-600 dark:text-gray-400;
}

.action-content {
  @apply flex-1 text-left;
}

.action-title {
  @apply font-medium text-gray-900 dark:text-gray-100 mb-1;
}

.action-desc {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 覆盖内部组件样式 */
.wallet-wrapper :deep(.wallet-container) {
  @apply space-y-6;
}
</style>
