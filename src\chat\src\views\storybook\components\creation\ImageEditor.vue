<template>
  <div class="image-editor">
    <!-- 编辑模态框 -->
    <div class="editor-modal" v-if="visible">
      <div class="editor-container">
        <!-- 编辑器头部 -->
        <div class="editor-header">
          <div class="editor-title-container">
            <h3 class="editor-title">
              <span class="magic-wand">✨</span> 角色魔法修改
              <span class="magic-wand">✨</span>
            </h3>
            <div class="editor-subtitle">
              <span class="step-number">1</span> 在图像上画出你想修改的区域
              <span class="step-number">2</span> 选择或输入你想要的变化
            </div>
          </div>
          <button class="close-button" @click="closeEditor">×</button>
        </div>

        <!-- 编辑器内容 - 使用两列布局 -->
        <div class="editor-main">
          <!-- 左侧：画布和工具栏 -->
          <div class="editor-left-panel">
            <!-- 画布容器 -->
            <div class="canvas-container">
              <div class="canvas-hint" v-if="showCanvasHint">
                <div class="hint-content">
                  <div class="hint-icon">👆</div>
                  <div class="hint-text">在想要修改的地方画一画吧！</div>
                  <div class="hint-tip">提示：只有你画出的区域会被修改，其他部分保持不变</div>
                </div>
              </div>
              <CanvasMask
                ref="canvasMaskRef"
                :src="imageUrl"
                :width="editorWidth"
                :height="editorHeight"
                :max="800"
                :penColor="penColor"
                :penWidth="penWidth"
                exportMaskBackgroundColor="black"
                exportMaskColor="white"
                :updateFileInfo="updateFileInfo"
                @draw-start="onDrawStart"
              />
            </div>

            <!-- 工具栏 -->
            <div class="editor-toolbar">
              <div class="tool-group">
                <button
                  class="tool-button tool-button-large"
                  :class="{ active: !isEraserMode }"
                  @click="toggleEraser(false)"
                  title="画笔"
                >
                  <span class="tool-icon">🖌️</span>
                  <span class="tool-label">画笔</span>
                </button>
                <button
                  class="tool-button tool-button-large"
                  :class="{ active: isEraserMode }"
                  @click="toggleEraser(true)"
                  title="橡皮擦"
                >
                  <span class="tool-icon">🧽</span>
                  <span class="tool-label">橡皮擦</span>
                </button>
                <button class="tool-button tool-button-large" @click="undoAction" title="撤销">
                  <span class="tool-icon">↩️</span>
                  <span class="tool-label">撤销</span>
                </button>
                <button class="tool-button tool-button-large" @click="clearCanvas" title="清空">
                  <span class="tool-icon">🗑️</span>
                  <span class="tool-label">清空</span>
                </button>
              </div>

              <div class="pen-size-control">
                <div class="pen-size-label">笔触大小:</div>
                <div class="pen-size-options">
                  <button
                    class="size-option"
                    :class="{ active: penWidth === 10 }"
                    @click="penWidth = 10"
                  >小</button>
                  <button
                    class="size-option"
                    :class="{ active: penWidth === 20 }"
                    @click="penWidth = 20"
                  >中</button>
                  <button
                    class="size-option"
                    :class="{ active: penWidth === 40 }"
                    @click="penWidth = 40"
                  >大</button>
                </div>
                <div class="pen-size-preview" :style="{ width: `${penWidth}px`, height: `${penWidth}px` }"></div>
              </div>
            </div>
          </div>

          <!-- 右侧：提示词和操作按钮 -->
          <div class="editor-right-panel">
            <!-- 常用修改选项 -->
            <div class="common-edits">
              <div class="section-title">常用魔法修改</div>
              <div class="edit-options">
                <button class="edit-option" @click="applyExample('添加眼镜')">
                  <span class="option-icon">👓</span>
                  <span class="option-text">添加眼镜</span>
                </button>
                <button class="edit-option" @click="applyExample('添加帽子')">
                  <span class="option-icon">🧢</span>
                  <span class="option-text">添加帽子</span>
                </button>
                <button class="edit-option" @click="applyExample('改变发型')">
                  <span class="option-icon">💇</span>
                  <span class="option-text">改变发型</span>
                </button>
                <button class="edit-option" @click="applyExample('改变衣服颜色')">
                  <span class="option-icon">👕</span>
                  <span class="option-text">改变衣服</span>
                </button>
                <button class="edit-option" @click="applyExample('添加微笑')">
                  <span class="option-icon">😊</span>
                  <span class="option-text">添加微笑</span>
                </button>
                <button class="edit-option" @click="applyExample('添加背景')">
                  <span class="option-icon">🏞️</span>
                  <span class="option-text">添加背景</span>
                </button>
              </div>
            </div>

            <!-- 提示词输入 -->
            <div class="prompt-container">
              <div class="prompt-label">
                <span class="prompt-icon">✏️</span>
                告诉AI你想要什么变化:
              </div>
              <textarea
                v-model="editPrompt"
                class="prompt-input"
                placeholder="例如: 添加一顶红色帽子，改变衣服颜色为蓝色..."
                :disabled="isProcessing"
                rows="3"
              ></textarea>
              <div class="prompt-examples">
                <div class="example-title">提示词示例:</div>
                <div class="example-tags">
                  <span class="example-tag" @click="applyExample('添加红色围巾')">添加红色围巾</span>
                  <span class="example-tag" @click="applyExample('把头发变成金色')">把头发变成金色</span>
                  <span class="example-tag" @click="applyExample('添加一只小狗')">添加一只小狗</span>
                  <span class="example-tag" @click="applyExample('改变背景为森林')">改变背景为森林</span>
                </div>
              </div>
            </div>

            <!-- 进度条 -->
            <div v-if="isProcessing" class="progress-container">
              <div class="progress-status">
                <div class="status-icon">
                  <div class="magic-spinner"></div>
                </div>
                <div class="status-text">{{ editStatus }}</div>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: `${editProgress}%` }"></div>
              </div>
              <div class="progress-percentage">{{ editProgress }}%</div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <button class="cancel-button" @click="closeEditor">
                <span class="button-icon">❌</span>
                <span class="button-text">取消</span>
              </button>
              <button
                class="apply-button"
                @click="applyEdit"
                :disabled="isProcessing"
              >
                <span class="button-icon">{{ isProcessing ? '⏳' : '✨' }}</span>
                <span class="button-text">{{ isProcessing ? '魔法修改中...' : '开始魔法修改' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import CanvasMask from '@/components/common/CanvasMask/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  },
  onSuccess: {
    type: Function,
    default: () => {}
  },
  onCancel: {
    type: Function,
    default: () => {}
  },
  onConversation: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['update:visible', 'edit-complete']);

// 编辑器状态
const canvasMaskRef = ref(null);
const isEraserMode = ref(false);
const penColor = ref('white');
const penWidth = ref(20);
const editPrompt = ref('');
const isProcessing = ref(false);
const editorWidth = ref(800);
const editorHeight = ref(600);
const imageInfo = ref({
  width: 0,
  height: 0,
  scaleRatio: 1
});
const showCanvasHint = ref(true); // 显示画布提示

// 更新图像信息
const updateFileInfo = (info) => {
  imageInfo.value = info;
  console.log('[图像编辑器] 图像信息已更新:', info);
};

// 切换橡皮擦模式
const toggleEraser = (mode) => {
  isEraserMode.value = mode;
  if (canvasMaskRef.value) {
    canvasMaskRef.value.toggleEraser();
  }
};

// 撤销操作
const undoAction = () => {
  if (canvasMaskRef.value) {
    canvasMaskRef.value.undo();
  }
};

// 清空画布
const clearCanvas = () => {
  if (canvasMaskRef.value) {
    canvasMaskRef.value.clear();
  }
};

// 关闭编辑器
const closeEditor = () => {
  // 重置状态
  if (isProcessing.value) {
    isProcessing.value = false;
    editProgress.value = 0;
    editStatus.value = '';
  }

  // 通知父组件
  emit('update:visible', false);
  props.onCancel();
};

// 验证图像URL
const validateImageUrl = async (url) => {
  try {
    // 检查URL格式
    if (!url) {
      throw new Error('图像URL不能为空');
    }

    // 如果是base64格式，检查大小
    if (url.startsWith('data:')) {
      const base64Data = url.split(',')[1];
      const sizeInBytes = Math.ceil((base64Data.length * 3) / 4);
      const sizeInMB = sizeInBytes / (1024 * 1024);

      if (sizeInMB > 25) {
        throw new Error('图像大小超过25MB限制，请使用较小的图像');
      }

      // 检查格式
      const mimeType = url.split(';')[0].split(':')[1];
      if (!['image/png', 'image/jpeg', 'image/webp'].includes(mimeType)) {
        throw new Error('图像格式必须是PNG、JPEG或WEBP');
      }
    } else {
      // 如果是URL，尝试获取图像信息
      console.log('[图像编辑器] 验证图像URL:', url.substring(0, 50) + '...');
    }

    return true;
  } catch (error) {
    console.error('[图像编辑器] 图像验证失败:', error);
    window.$message?.error('图像验证失败: ' + error.message);
    return false;
  }
};

// 更新编辑进度
const editProgress = ref(0);
const editStatus = ref('');

// 应用编辑
const applyEdit = async () => {
  if (!canvasMaskRef.value) return;

  // 检查是否有绘制内容
  const hasDrawing = await checkHasDrawing();
  if (!hasDrawing) {
    window.$message?.warning('请先在图像上画出你想修改的区域');
    return;
  }

  // 检查是否有提示词
  if (!editPrompt.value.trim()) {
    window.$message?.warning('请输入你想要的修改描述');
    return;
  }

  try {
    // 重置状态
    isProcessing.value = true;
    editProgress.value = 0;
    editStatus.value = '准备中';

    // 验证图像
    console.log('[图像编辑器] 开始验证图像');
    const isImageValid = await validateImageUrl(props.imageUrl);
    if (!isImageValid) {
      throw new Error('图像验证失败，请确保图像格式正确且大小不超过25MB');
    }

    editProgress.value = 15;
    editStatus.value = '生成蒙版';

    // 获取mask图像
    const maskBase64 = await canvasMaskRef.value.getBase();
    console.log('[图像编辑器] 已生成蒙版图像');

    // 验证蒙版
    if (!maskBase64 || !maskBase64.startsWith('data:image/png;base64,')) {
      throw new Error('蒙版生成失败，请重试');
    }

    editProgress.value = 25;
    editStatus.value = '处理提示词';

    // 构建提示词 - 增强提示词以获得更好的结果
    let prompt = editPrompt.value;

    // 确保提示词包含保持原始风格的指示
    if (!prompt.includes('保持原始风格') && !prompt.includes('与原图风格一致')) {
      prompt += '，保持与原图一致的风格和质量';
    }

    // 添加明确的指示，只修改标记区域，保留其他部分不变
    if (!prompt.includes('只修改标记区域') && !prompt.includes('只在标记的区域')) {
      prompt += '，只在我标记的区域进行修改，保持其他区域不变';
    }

    editProgress.value = 35;
    editStatus.value = '发送编辑请求';

    // 调用图像编辑API
    console.log('[图像编辑器] 发送图像编辑请求，提示词:', prompt);

    // 创建一个可以取消的进度更新定时器
    let progressInterval;
    let timeoutTimer;

    // 使用onConversation函数发送请求
    try {
      console.log('[图像编辑器] 准备发送图像编辑请求，参数:', {
        model: 'gpt-image-1',
        promptLength: prompt.length,
        imageUrlLength: props.imageUrl ? props.imageUrl.substring(0, 30) + '...' : 'undefined',
        maskBase64Length: maskBase64 ? maskBase64.substring(0, 30) + '...' : 'undefined',
        extraParams: {
          size: '1024x1024',
          quality: 'hd',
          style: 'natural'
        }
      });

      const response = await props.onConversation({
        msg: prompt,
        model: 'gpt-image-1',
        modelName: 'GPT Image',
        modelType: 2,
        extraParam: {
          size: '1024x1024',
          quality: 'hd',
          style: 'natural',
          // 添加图像编辑所需的参数
          image: props.imageUrl,
          mask: maskBase64
        },
        onSuccess: (response) => {
          console.log('[图像编辑器] 图像编辑响应:', JSON.stringify(response));

          // 清除定时器
          if (progressInterval) clearInterval(progressInterval);
          if (timeoutTimer) clearTimeout(timeoutTimer);

          // 检查是否是进度更新
          if (response && response.status === 2) {
            console.log('[图像编辑器] 收到进度更新:', response.progress);
            // 这是一个进度更新，不是最终结果
            if (response.progress) {
              const progressValue = parseInt(response.progress);
              if (!isNaN(progressValue)) {
                editProgress.value = Math.min(95, progressValue);
              }
            }
            editStatus.value = response.text || 'AI魔法修改中';
            return; // 继续等待最终结果
          }

          // 检查是否有最终结果
          let imageUrl = null;

          // 尝试从不同格式的响应中提取图像URL
          if (response && response.fileInfo) {
            // 旧格式
            imageUrl = response.fileInfo;
          } else if (response && response.data && response.data.length > 0) {
            // OpenAI API格式
            if (response.data[0].b64_json) {
              // 如果是base64格式，转换为数据URL
              imageUrl = 'data:image/png;base64,' + response.data[0].b64_json;
            } else if (response.data[0].url) {
              imageUrl = response.data[0].url;
            }
          } else if (response && response.url) {
            // 简化格式
            imageUrl = response.url;
          } else if (typeof response === 'string' && (response.startsWith('http') || response.startsWith('data:'))) {
            // 直接返回URL字符串
            imageUrl = response;
          }

          if (imageUrl) {
            console.log('[图像编辑器] 成功提取图像URL:', imageUrl.substring(0, 50) + '...');
            editProgress.value = 100;
            editStatus.value = '编辑完成';

            // 编辑成功，通知父组件
            // 注意：这里直接使用了API返回的图像，可能导致原图丢失
            // 对于gpt-image-1模型，API会返回整个修改后的图像，而不是只返回修改的部分
            // 因此我们需要确保返回的是完整的图像
            emit('edit-complete', imageUrl);
            props.onSuccess(imageUrl);

            // 显示成功消息
            window.$message?.success('角色图像修改成功！');

            // 延迟关闭编辑器，让用户看到100%的进度
            setTimeout(() => {
              // 重置所有状态
              isProcessing.value = false;
              editProgress.value = 0;
              editStatus.value = '';
              editPrompt.value = '';
              showCanvasHint.value = true;
              clearCanvas();

              // 关闭编辑器
              closeEditor();
            }, 1000);
          } else {
            console.error('[图像编辑器] 无法从响应中提取图像URL:', JSON.stringify(response));
            window.$message?.error('图像编辑失败，请重试');
            editStatus.value = '编辑失败';
            isProcessing.value = false;

            // 尝试使用备用方案
            tryFallbackImageGeneration(prompt);
          }
        },
        onFailure: (error) => {
          console.error('[图像编辑器] 图像编辑失败:', error);
          console.error('[图像编辑器] 错误详情:', JSON.stringify(error));

          // 清除定时器
          if (progressInterval) clearInterval(progressInterval);
          if (timeoutTimer) clearTimeout(timeoutTimer);

          editProgress.value = 0;
          editStatus.value = '编辑失败';

          // 提供更详细的错误信息
          let errorMessage = '图像编辑失败';
          if (error.answer) {
            errorMessage += ': ' + error.answer;
          } else if (error.message) {
            errorMessage += ': ' + error.message;
          } else if (typeof error === 'string') {
            errorMessage += ': ' + error;
          } else {
            errorMessage += '，请检查图像格式和大小，或稍后重试';
          }

          window.$message?.error(errorMessage);
          isProcessing.value = false;

          // 尝试使用备用方案
          tryFallbackImageGeneration(prompt);
        }
      });

      // 如果直接返回了结果
      if (response) {
        console.log('[图像编辑器] onConversation直接返回了结果:', response);
      }
    } catch (apiError) {
      console.error('[图像编辑器] API调用过程中出错:', apiError);
      console.error('[图像编辑器] API错误详情:', JSON.stringify(apiError));

      // 清除定时器
      if (progressInterval) clearInterval(progressInterval);
      if (timeoutTimer) clearTimeout(timeoutTimer);

      // 尝试使用备用方案
      tryFallbackImageGeneration(prompt);
    }

    // 更新进度 - 请求已发送
    editProgress.value = 50;
    editStatus.value = 'AI魔法修改中';

    // 模拟进度更新 - 避免用户等待时间过长没有反馈
    // 使用更有趣的状态消息
    const statusMessages = [
      'AI魔法修改中',
      '正在绘制新内容',
      '调整图像细节',
      '应用你的创意',
      '魔法即将完成',
      '最后一点点调整'
    ];

    let statusIndex = 0;
    progressInterval = setInterval(() => {
      if (editProgress.value < 90) {
        editProgress.value += 3;

        // 每隔一段时间更新状态消息
        if (editProgress.value > 50 && editProgress.value % 10 === 0) {
          statusIndex = (statusIndex + 1) % statusMessages.length;
          editStatus.value = statusMessages[statusIndex];
        }
      } else {
        clearInterval(progressInterval);
      }
    }, 1500);

    // 3分钟后自动清除进度更新
    timeoutTimer = setTimeout(() => {
      if (progressInterval) clearInterval(progressInterval);
      if (editProgress.value < 100) {
        editProgress.value = 0;
        editStatus.value = '请求超时';
        isProcessing.value = false;
        window.$message?.error('图像编辑请求超时，请稍后重试');
      }
    }, 180000); // 3分钟超时

  } catch (error) {
    console.error('[图像编辑器] 图像编辑过程中出错:', error);
    editProgress.value = 0;
    editStatus.value = '编辑失败';

    // 提供更详细的错误信息
    let errorMessage = '图像编辑失败';
    if (error.message) {
      errorMessage += ': ' + error.message;
    }

    window.$message?.error(errorMessage);
    isProcessing.value = false;
  }
};

// 检查是否有绘制内容
const checkHasDrawing = async () => {
  if (!canvasMaskRef.value) return false;

  try {
    const maskBase64 = await canvasMaskRef.value.getBase();
    if (!maskBase64) return false;

    // 创建一个临时图像来检查蒙版是否有内容
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // 检查是否有透明像素（表示有绘制内容）
        for (let i = 3; i < data.length; i += 4) {
          if (data[i] < 255) {
            resolve(true);
            return;
          }
        }

        resolve(false);
      };
      img.onerror = () => resolve(false);
      img.src = maskBase64;
    });
  } catch (e) {
    console.error('[图像编辑器] 检查绘制内容失败:', e);
    return false;
  }
};

// 监听图像URL变化
watch(() => props.imageUrl, (newUrl) => {
  if (newUrl && canvasMaskRef.value) {
    // 重置画布
    clearCanvas();
  }
});

// 应用示例提示词
const applyExample = (example) => {
  if (!editPrompt.value) {
    editPrompt.value = example;
  } else if (!editPrompt.value.endsWith('，') && !editPrompt.value.endsWith('、') && !editPrompt.value.endsWith(' ')) {
    editPrompt.value += '，' + example;
  } else {
    editPrompt.value += example;
  }
};

// 处理绘制开始事件
const onDrawStart = () => {
  // 隐藏画布提示
  showCanvasHint.value = false;
};

// 备用图像生成方法
const tryFallbackImageGeneration = (prompt) => {
  console.log('[图像编辑器] 尝试使用备用图像生成方法');

  // 更新状态
  editStatus.value = '尝试备用方案...';
  editProgress.value = 60;

  try {
    // 使用pollinations.ai作为备用
    const encodedPrompt = encodeURIComponent(prompt);
    const fallbackImageUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}`;

    console.log('[图像编辑器] 使用备用图像URL:', fallbackImageUrl);

    // 创建一个Image对象来验证图像是否可用
    const img = new Image();
    img.onload = () => {
      console.log('[图像编辑器] 备用图像加载成功');

      // 构造一个fileInfo对象
      const fileInfo = {
        url: fallbackImageUrl,
        width: img.width,
        height: img.height,
        type: 'image/jpeg',
        size: 0, // 无法获取实际大小
        name: 'fallback-image.jpg'
      };

      // 更新状态
      editProgress.value = 100;
      editStatus.value = '编辑完成(备用)';

      // 通知父组件
      emit('edit-complete', fileInfo);
      props.onSuccess(fileInfo);

      // 显示成功消息
      window.$message?.success('角色图像修改成功(备用方案)！');

      // 延迟关闭编辑器
      setTimeout(() => {
        // 重置所有状态
        isProcessing.value = false;
        editProgress.value = 0;
        editStatus.value = '';
        editPrompt.value = '';
        showCanvasHint.value = true;
        clearCanvas();

        // 关闭编辑器
        closeEditor();
      }, 1000);
    };

    img.onerror = () => {
      console.error('[图像编辑器] 备用图像加载失败');
      editStatus.value = '编辑失败';
      isProcessing.value = false;
      window.$message?.error('图像编辑失败，备用方案也无法使用');
    };

    // 开始加载图像
    img.src = fallbackImageUrl;
  } catch (fallbackError) {
    console.error('[图像编辑器] 备用方案失败:', fallbackError);
    editStatus.value = '编辑失败';
    isProcessing.value = false;
    window.$message?.error('图像编辑失败，所有方案均无法使用');
  }
};

// 组件挂载后的处理
onMounted(() => {
  console.log('[图像编辑器] 组件已挂载');
});
</script>

<style scoped>
@import './ImageEditorStyles.css';
@import './ImageEditorStyles2.css';
@import './ImageEditorStyles3.css';
</style>
