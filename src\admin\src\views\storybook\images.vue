<template>
  <div>
    <page-header title="绘本图片管理" />
    <page-main>
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="mb-4">
      <el-form-item label="关键词">
        <el-input v-model="searchForm.keyword" placeholder="描述/提示词" clearable />
      </el-form-item>
      <el-form-item label="图片类型">
        <el-select v-model="searchForm.imageType" placeholder="全部" clearable>
          <el-option label="角色图" :value="1" />
          <el-option label="页面图" :value="2" />
          <el-option label="封面图" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="searchForm.auditStatus" placeholder="全部" clearable>
          <el-option label="未审核" :value="0" />
          <el-option label="已通过" :value="1" />
          <el-option label="已拒绝" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="绘本ID">
        <el-input v-model="searchForm.storybookId" placeholder="绘本ID" clearable />
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input v-model="searchForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="图片" width="150">
        <template #default="{ row }">
          <el-image
            :src="row.imageUrl"
            fit="cover"
            style="width: 120px; height: 120px; border-radius: 4px"
            :preview-src-list="[row.imageUrl]"
            preview-teleported
          />
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
      <el-table-column prop="prompt" label="提示词" min-width="150" show-overflow-tooltip />
      <el-table-column prop="model" label="模型" width="120" />
      <el-table-column label="图片类型" width="100">
        <template #default="{ row }">
          <el-tag :type="getImageTypeType(row.imageType)">
            {{ getImageTypeText(row.imageType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getAuditStatusType(row.auditStatus)">
            {{ getAuditStatusText(row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="storybookId" label="绘本ID" width="90" />
      <el-table-column prop="userId" label="用户ID" width="90" />
      <el-table-column prop="createdAt" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="viewDetail(row)">详情</el-button>
          <el-button
            v-if="row.auditStatus === 0"
            size="small"
            type="success"
            @click="handleAudit(row, 1)"
          >通过</el-button>
          <el-button
            v-if="row.auditStatus === 0"
            size="small"
            type="danger"
            @click="handleAudit(row, 2)"
          >拒绝</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 图片详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="图片详情" width="800px">
      <div v-if="currentImage" class="flex">
        <div class="mr-6">
          <el-image
            :src="currentImage.imageUrl"
            fit="cover"
            style="width: 300px; height: 300px; border-radius: 8px"
            :preview-src-list="[currentImage.imageUrl]"
            preview-teleported
          />
        </div>
        <div class="flex-1">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="ID">{{ currentImage.id }}</el-descriptions-item>
            <el-descriptions-item label="图片类型">
              {{ getImageTypeText(currentImage.imageType) }}
            </el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getAuditStatusType(currentImage.auditStatus)">
                {{ getAuditStatusText(currentImage.auditStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="审核备注" v-if="currentImage.auditRemark">
              {{ currentImage.auditRemark }}
            </el-descriptions-item>
            <el-descriptions-item label="绘本ID">{{ currentImage.storybookId }}</el-descriptions-item>
            <el-descriptions-item label="页面ID" v-if="currentImage.pageId">{{ currentImage.pageId }}</el-descriptions-item>
            <el-descriptions-item label="角色ID" v-if="currentImage.characterId">{{ currentImage.characterId }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ currentImage.userId }}</el-descriptions-item>
            <el-descriptions-item label="模型">{{ currentImage.model }}</el-descriptions-item>
            <el-descriptions-item label="图片尺寸" v-if="currentImage.width && currentImage.height">
              {{ currentImage.width }} x {{ currentImage.height }}
            </el-descriptions-item>
            <el-descriptions-item label="图片大小" v-if="currentImage.size">
              {{ (currentImage.size / 1024).toFixed(2) }} KB
            </el-descriptions-item>
            <el-descriptions-item label="图片格式" v-if="currentImage.format">
              {{ currentImage.format }}
            </el-descriptions-item>
            <el-descriptions-item label="描述">
              {{ currentImage.description || '无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="提示词">
              {{ currentImage.prompt || '无提示词' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentImage.createdAt }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 审核拒绝对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="拒绝原因" width="500px">
      <el-form :model="rejectForm">
        <el-form-item label="拒绝原因" :label-width="'80px'">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReject">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </page-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import ApiStorybook from '@/api/modules/storybook';

const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const dateRange = ref([]);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  imageType: '',
  auditStatus: '',
  storybookId: '',
  userId: '',
  startDate: '',
  endDate: '',
});

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 详情对话框
const detailDialogVisible = ref(false);
const currentImage = ref(null);

// 拒绝对话框
const rejectDialogVisible = ref(false);
const rejectForm = reactive({
  id: 0,
  reason: '',
});

// 获取图片类型文本
const getImageTypeText = (type) => {
  const typeMap = {
    1: '角色图',
    2: '页面图',
    3: '封面图',
  };
  return typeMap[type] || '未知';
};

// 获取图片类型样式
const getImageTypeType = (type) => {
  const typeMap = {
    1: 'success',
    2: 'primary',
    3: 'warning',
  };
  return typeMap[type] || 'info';
};

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const statusMap = {
    0: '未审核',
    1: '已通过',
    2: '已拒绝',
  };
  return statusMap[status] || '未知';
};

// 获取审核状态样式
const getAuditStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'danger',
  };
  return typeMap[status] || 'info';
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      searchForm.startDate = dateRange.value[0];
      searchForm.endDate = dateRange.value[1];
    } else {
      searchForm.startDate = '';
      searchForm.endDate = '';
    }

    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm,
    };

    const res = await ApiStorybook.getStorybookImages(params);
    tableData.value = res.data.items;
    total.value = res.data.total;
  } catch (error) {
    console.error('加载图片列表失败', error);
    ElMessage.error('加载图片列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  dateRange.value = [];
  currentPage.value = 1;
  loadData();
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 查看详情
const viewDetail = (row) => {
  currentImage.value = row;
  detailDialogVisible.value = true;
};

// 处理审核
const handleAudit = async (row, auditStatus) => {
  if (auditStatus === 2) {
    // 拒绝需要填写原因
    rejectForm.id = row.id;
    rejectForm.reason = '';
    rejectDialogVisible.value = true;
    return;
  }

  try {
    await ApiStorybook.updateImageAuditStatus(row.id, { auditStatus });
    ElMessage.success('审核操作成功');
    loadData();
  } catch (error) {
    console.error('审核操作失败', error);
    ElMessage.error('审核操作失败');
  }
};

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.reason) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }

  try {
    await ApiStorybook.updateImageAuditStatus(rejectForm.id, {
      auditStatus: 2,
      auditRemark: rejectForm.reason
    });
    ElMessage.success('拒绝操作成功');
    rejectDialogVisible.value = false;
    loadData();
  } catch (error) {
    console.error('拒绝操作失败', error);
    ElMessage.error('拒绝操作失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确定要删除此图片吗？此操作不可恢复！',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await ApiStorybook.deleteImage(row.id);
        ElMessage.success('删除成功');
        loadData();
      } catch (error) {
        console.error('删除失败', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}
</style>
