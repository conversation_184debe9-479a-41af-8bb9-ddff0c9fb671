<script setup lang='ts'>
import { computed } from 'vue'
import { HoverButton, UserAvatar, SvgIcon } from '@/components/common'
import { useAuthStore } from '@/store'
const authStore = useAuthStore()
const isLogin = computed(() => !!authStore.token)
</script>

<template>
  <footer class="flex items-center justify-between min-w-0 p-4 overflow-hidden border-t dark:border-neutral-800">
    <div class="flex-1 flex-shrink-0 overflow-hidden">
      <UserAvatar />
    </div>
    <HoverButton v-if="isLogin" tooltip="退出账户登录" @click="authStore.logOut()">
      <SvgIcon name="ri:logout-box-line" size="18" color="#0e7a0d" />
    </HoverButton>
  </footer>
</template>
