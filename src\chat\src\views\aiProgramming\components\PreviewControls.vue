<template>
    <div class="control-bar flex items-center space-x-3 px-3 py-2">
        <!-- 设备选择按钮组，改为药丸式设计 -->
        <div class="device-selector-group bg-gray-100 dark:bg-gray-700 rounded-full p-1 flex items-center">
            <button @click="changeDeviceType('mobile')" :class="[
                'device-button flex items-center justify-center px-3 py-1.5 text-sm rounded-full transition-all duration-200',
                deviceType === 'mobile' ? 'active-device' : 'inactive-device'
            ]">
                <svg class="h-4 w-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <rect x="7" y="4" width="10" height="16" rx="2" ry="2" stroke-width="2" />
                    <line x1="12" y1="18" x2="12" y2="18.01" stroke-width="2" stroke-linecap="round" />
                </svg>
                手机
            </button>
            <button @click="changeDeviceType('tablet')" :class="[
                'device-button flex items-center justify-center px-3 py-1.5 text-sm rounded-full transition-all duration-200',
                deviceType === 'tablet' ? 'active-device' : 'inactive-device'
            ]">
                <svg class="h-4 w-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <rect x="4" y="4" width="16" height="16" rx="2" ry="2" stroke-width="2" />
                    <line x1="12" y1="17" x2="12" y2="17.01" stroke-width="2" stroke-linecap="round" />
                </svg>
                平板
            </button>
            <button @click="changeDeviceType('desktop')" :class="[
                'device-button flex items-center justify-center px-3 py-1.5 text-sm rounded-full transition-all duration-200',
                deviceType === 'desktop' ? 'active-device' : 'inactive-device'
            ]">
                <svg class="h-4 w-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <rect x="2" y="4" width="20" height="14" rx="2" ry="2" stroke-width="2" />
                    <line x1="8" y1="20" x2="16" y2="20" stroke-width="2" stroke-linecap="round" />
                    <line x1="12" y1="18" x2="12" y2="20" stroke-width="2" />
                </svg>
                电脑
            </button>
        </div>

        <!-- 方向控制按钮 -->
        <button v-if="deviceType !== 'desktop'" @click="toggleOrientation"
            class="control-button rounded-md flex items-center justify-center p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                stroke="currentColor">
                <rect x="4" y="5" width="16" height="14" rx="2" ry="2" stroke-width="2" />
                <path d="M12 2v2m0 18v2" stroke-width="2" stroke-linecap="round" />
            </svg>
        </button>

        <div class="flex-grow"></div>

        <!-- 全屏和刷新按钮 - 改为圆形设计 -->
        <div class="flex items-center space-x-2">
            <button @click="toggleFullscreen"
                class="control-button rounded-full flex items-center justify-center w-9 h-9 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                <svg v-if="isFullscreen" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor">
                    <path
                        d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <svg v-else class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>

            <button @click="refresh"
                class="control-button rounded-full flex items-center justify-center w-9 h-9 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <path d="M23 4v6h-6M1 20v-6h6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { Computer, Ipad, Phone } from '@icon-park/vue-next';

const props = defineProps({
    deviceType: {
        type: String as () => 'mobile' | 'tablet' | 'desktop',
        required: true
    },
    isLandscape: {
        type: Boolean,
        default: false
    },
    isFullscreen: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['change-device', 'toggle-orientation', 'toggle-fullscreen', 'refresh']);

// 设备预设配置
const devicePresets = {
    mobile: {
        name: '手机',
        icon: Phone,
        width: '375px',
        height: '667px',
        portraitClass: 'w-[375px] h-[667px]',
        landscapeClass: 'w-[667px] h-[375px]',
    },
    tablet: {
        name: '平板',
        icon: Ipad,
        width: '768px',
        height: '1024px',
        portraitClass: 'w-[768px] h-[1024px]',
        landscapeClass: 'w-[1024px] h-[768px]',
    },
    desktop: {
        name: '电脑',
        icon: Computer,
        width: '100%',
        height: '100%',
        portraitClass: 'w-full h-full desktop-frame',
        landscapeClass: 'w-full h-full desktop-frame',
    }
};

// 切换设备类型并发出事件
const changeDeviceType = (type: string) => {
    emit('change-device', type);
};

// 切换屏幕方向并发出事件
const toggleOrientation = () => {
    emit('toggle-orientation');
};

// 切换全屏并发出事件
const toggleFullscreen = () => {
    emit('toggle-fullscreen');
};

// 刷新预览并发出事件
const refresh = () => {
    emit('refresh');
};
</script>

<style lang="less" scoped>
.control-bar {
    border-bottom: 1px solid var(--border-color);
}

/* 设备选择按钮组样式 */
.device-selector-group {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 设备按钮基础样式 */
.device-button {
    font-weight: 500;
    min-width: 80px;

    &:not(:last-child) {
        margin-right: 1px;
    }
}

.active-device {
    background-color: white;
    color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .dark & {
        background-color: #1e293b;
        color: #60a5fa;
    }

    svg {
        stroke: currentColor;
    }
}

.inactive-device {
    color: #64748b;

    .dark & {
        color: #94a3b8;
    }

    &:hover {
        color: #334155;
        background-color: rgba(255, 255, 255, 0.5);

        .dark & {
            color: #cbd5e1;
            background-color: rgba(255, 255, 255, 0.05);
        }
    }
}

/* 控制按钮样式 */
.control-button {
    color: #64748b;

    .dark & {
        color: #94a3b8;
    }

    &:hover {
        color: #334155;

        .dark & {
            color: #cbd5e1;
        }
    }

    &:active {
        transform: scale(0.95);
    }

    /* 添加圆形按钮的阴影和悬停效果 */
    &.rounded-full {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        &:active {
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transform: translateY(0) scale(0.98);
        }
    }
}
</style>