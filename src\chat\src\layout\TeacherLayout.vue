<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAppStore } from '@/store';
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NDrawer,
  NDrawerContent,
  NButton,
  NIcon
} from 'naive-ui';
import { MenuOutline } from '@vicons/ionicons5';
import RoleBasedNav from './components/RoleBasedNav.vue';

const { isMobile } = useBasicLayout();
const appStore = useAppStore();

// 侧边栏状态管理
const siderCollapsed = computed({
  get: () => appStore.siderCollapsed,
  set: (value: boolean) => appStore.setSiderCollapsed(value)
});

// 移动端侧边栏显示控制
const mobileDrawerVisible = ref(false);

// 侧边栏宽度配置
const siderWidth = computed(() => {
  return isMobile.value ? '100vw' : 260;
});

// 响应式类名计算
const getLayoutClass = computed(() => {
  if (isMobile.value) {
    return ['h-full', 'overflow-hidden'];
  }
  return ['h-full', 'teacher-layout'];
});

const getContentClass = computed(() => {
  return ['bg-gray-50', 'dark:bg-gray-800'];
});

// 切换侧边栏
const toggleSider = () => {
  if (isMobile.value) {
    mobileDrawerVisible.value = !mobileDrawerVisible.value;
  } else {
    siderCollapsed.value = !siderCollapsed.value;
  }
};

// 关闭移动端侧边栏
const closeMobileDrawer = () => {
  mobileDrawerVisible.value = false;
};

// 监听窗口大小变化
const handleResize = () => {
  if (!isMobile.value && mobileDrawerVisible.value) {
    mobileDrawerVisible.value = false;
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="teacher-layout-container h-full">
    <NLayout :class="getLayoutClass" has-sider position="absolute">

      <!-- 桌面端侧边栏 -->
      <NLayoutSider
        v-if="!isMobile"
        :collapsed="siderCollapsed"
        :collapsed-width="0"
        :width="siderWidth"
        :native-scrollbar="false"
        bordered
        class="teacher-sider"
        collapse-mode="width"
      >
        <RoleBasedNav />
      </NLayoutSider>

      <!-- 主内容区域 -->
      <NLayout>
        <!-- 主内容区 -->
        <NLayoutContent :class="getContentClass" content-style="height: 100%;">
          <div class="h-full relative">
            <!-- 打开侧边栏按钮 -->
            <NButton
              v-if="!isMobile && siderCollapsed"
              quaternary
              circle
              @click="toggleSider"
              class="open-sider-btn fixed top-4 left-4 z-50 shadow-lg"
              title="打开侧边栏"
            >
              <template #icon>
                <NIcon :component="MenuOutline" />
              </template>
            </NButton>

            <RouterView v-slot="{ Component, route }">
              <component :is="Component" :key="route.fullPath" />
            </RouterView>
          </div>
        </NLayoutContent>
      </NLayout>
    </NLayout>

    <!-- 移动端侧边栏抽屉 -->
    <NDrawer
      v-model:show="mobileDrawerVisible"
      :width="280"
      placement="left"
      class="teacher-mobile-drawer"
    >
      <NDrawerContent closable title="教师工作台" @close="closeMobileDrawer">
        <RoleBasedNav />
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped lang="scss">
.teacher-layout-container {
  background-color: #f9fafb;

  .teacher-sider {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    :deep(.n-layout-sider-scroll-container) {
      display: flex;
      flex-direction: column;
    }
  }
}

// 深色模式基础
@media (prefers-color-scheme: dark) {
  .teacher-layout-container {
    background-color: #111827;
  }
}

// 老师模式的主题样式
.teacher-layout {
  --primary-color: #2080f0;
  --primary-hover: #1a73e8;
  --primary-pressed: #1557b0;

  :deep(.n-button--primary-type) {
    background-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-hover);
    }

    &:active {
      background-color: var(--primary-pressed);
    }
  }
}

// 打开侧边栏按钮样式
.open-sider-btn {
  background-color: white;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }
}

@media (prefers-color-scheme: dark) {
  .open-sider-btn {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }
}

// 移动端优化
@media (max-width: 768px) {
  .teacher-layout-container {
    // 移动端特定样式
  }
}

// 深色模式优化
@media (prefers-color-scheme: dark) {
  .teacher-layout-container {
    .teacher-sider {
      border-color: #374151;
    }
  }
}

// 滚动条样式
.teacher-layout-container {
  :deep(.n-scrollbar-content) {
    height: 100%;
  }

  :deep(.n-scrollbar) {
    height: 100%;
  }
}

// 抽屉样式
.teacher-mobile-drawer {
  :deep(.n-drawer-body) {
    padding: 0;
  }

  :deep(.n-drawer-header) {
    background-color: #eff6ff;
    border-bottom: 1px solid rgb(229, 231, 235);

    .n-drawer-header__main {
      color: #1d4ed8;
      font-weight: 500;
    }
  }
}

// 深色模式抽屉
@media (prefers-color-scheme: dark) {
  .teacher-mobile-drawer {
    :deep(.n-drawer-header) {
      background-color: rgba(59, 130, 246, 0.2);

      .n-drawer-header__main {
        color: #93c5fd;
      }
    }
  }
}
</style>