<template>
  <div class="creation-sidebar" :class="{ 'collapsed': isCollapsed }">
    <div class="sidebar-header">
      <div class="toggle-button" @click="toggleSidebar">
        <SvgIcon :name="isCollapsed ? 'ri:menu-unfold-line' : 'ri:menu-fold-line'" size="20" />
      </div>
      <h2 v-if="!isCollapsed" class="sidebar-title">创作类型</h2>
    </div>
    
    <div class="creation-types">
      <div 
        v-for="type in creationTypes" 
        :key="type.id"
        class="type-item"
        :class="{ 'active': creationType === type.id }"
        @click="selectType(type.id)"
      >
        <div class="type-icon">
          <SvgIcon :name="type.icon" size="20" />
        </div>
        <div v-if="!isCollapsed" class="type-info">
          <div class="type-name">{{ type.name }}</div>
          <div class="type-description">{{ type.description }}</div>
        </div>
      </div>
    </div>
    
    <div class="sidebar-divider"></div>
    
    <div class="sidebar-header">
      <h2 v-if="!isCollapsed" class="sidebar-title">历史记录</h2>
      <div class="header-actions" v-if="!isCollapsed">
        <n-button text @click="clearHistory">
          <template #icon>
            <SvgIcon name="ri:delete-bin-line" size="16" />
          </template>
          清空
        </n-button>
      </div>
    </div>
    
    <div class="history-list">
      <div v-if="historyList.length === 0" class="empty-history">
        <SvgIcon name="ri:inbox-line" size="24" />
        <span v-if="!isCollapsed">暂无历史记录</span>
      </div>
      
      <div 
        v-for="item in historyList" 
        :key="item.id"
        class="history-item"
        :class="{ 'active': activeHistory === item.id }"
        @click="selectHistory(item.id)"
      >
        <div class="history-icon">
          <SvgIcon :name="getTypeIcon(item.type)" size="16" />
        </div>
        <div v-if="!isCollapsed" class="history-info">
          <div class="history-title">{{ item.title }}</div>
          <div class="history-date">{{ item.date }}</div>
        </div>
        <div v-if="!isCollapsed" class="history-actions">
          <n-dropdown 
            :options="historyOptions" 
            @select="(key) => handleHistoryAction(key, item.id)"
            placement="bottom-end"
            trigger="click"
          >
            <div class="action-button" @click.stop>
              <SvgIcon name="ri:more-2-fill" size="16" />
            </div>
          </n-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { NButton, NDropdown, useMessage, useDialog } from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useChatStore } from '@/store';

const props = defineProps({
  creationType: {
    type: String,
    default: 'chat'
  },
  historyList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:creation-type', 'select-history']);

const message = useMessage();
const dialog = useDialog();
const chatStore = useChatStore();

const isCollapsed = ref(false);
const activeHistory = ref(0);

// 创作类型列表
const creationTypes = [
  {
    id: 'chat',
    name: '通用对话',
    description: '与AI进行自由对话',
    icon: 'ri:chat-3-line'
  },
  {
    id: 'programming',
    name: 'AI编程',
    description: '创建网页、应用和交互式内容',
    icon: 'ri:code-box-line'
  },
  {
    id: 'writing',
    name: 'AI写作',
    description: '创作文章、故事和各类文本内容',
    icon: 'ri:file-text-line'
  },
  {
    id: 'picturebook',
    name: 'AI绘本',
    description: '创作图文结合的绘本和故事书',
    icon: 'ri:book-open-line'
  },
  {
    id: 'ppt',
    name: 'AI PPT',
    description: '快速生成专业的演示幻灯片',
    icon: 'ri:slideshow-line'
  },
  {
    id: 'music',
    name: 'AI音乐',
    description: '创作音乐和音频内容',
    icon: 'ri:music-line'
  },
  {
    id: 'freestyle',
    name: '自由创作',
    description: '不受限制的创意探索',
    icon: 'ri:magic-line'
  }
];

// 历史操作选项
const historyOptions = [
  {
    label: '重命名',
    key: 'rename',
    icon: () => h(SvgIcon, { name: 'ri:edit-line', size: '16' })
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => h(SvgIcon, { name: 'ri:delete-bin-line', size: '16' })
  }
];

// 方法
function toggleSidebar() {
  isCollapsed.value = !isCollapsed.value;
}

function selectType(type: string) {
  emit('update:creation-type', type);
}

function selectHistory(id: number) {
  activeHistory.value = id;
  emit('select-history', id);
}

function getTypeIcon(type: string) {
  const iconMap = {
    'chat': 'ri:chat-3-line',
    'programming': 'ri:code-box-line',
    'writing': 'ri:file-text-line',
    'picturebook': 'ri:book-open-line',
    'ppt': 'ri:slideshow-line',
    'music': 'ri:music-line',
    'freestyle': 'ri:magic-line'
  };
  
  return iconMap[type] || 'ri:chat-3-line';
}

function handleHistoryAction(key: string, id: number) {
  if (key === 'rename') {
    renameHistory(id);
  } else if (key === 'delete') {
    deleteHistory(id);
  }
}

function renameHistory(id: number) {
  const group = chatStore.getGroupById(id);
  if (!group) return;
  
  dialog.warning({
    title: '重命名',
    content: () => {
      const input = h('input', {
        class: 'n-input',
        style: {
          width: '100%',
          padding: '8px 12px',
          borderRadius: '4px',
          border: '1px solid #d9d9d9',
          marginTop: '12px'
        },
        value: group.name,
        onInput: (e) => {
          input.value = e.target.value;
        }
      });
      
      return h('div', [
        h('p', '请输入新的名称:'),
        input
      ]);
    },
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      const newName = document.querySelector('.n-input')?.value;
      if (newName && newName.trim()) {
        chatStore.updateGroupName(id, newName);
        message.success('重命名成功');
      }
    }
  });
}

function deleteHistory(id: number) {
  dialog.warning({
    title: '删除确认',
    content: '确定要删除这条历史记录吗？此操作不可撤销。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      chatStore.deleteGroup(id);
      message.success('删除成功');
      
      // 如果删除的是当前活动的历史记录，则创建一个新的
      if (activeHistory.value === id) {
        chatStore.addNewGroup();
        if (chatStore.active) {
          activeHistory.value = chatStore.active;
          emit('select-history', chatStore.active);
        }
      }
    }
  });
}

function clearHistory() {
  dialog.warning({
    title: '清空确认',
    content: '确定要清空所有历史记录吗？此操作不可撤销。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      chatStore.clearGroups();
      message.success('清空成功');
      
      // 创建一个新的对话
      chatStore.addNewGroup();
      if (chatStore.active) {
        activeHistory.value = chatStore.active;
        emit('select-history', chatStore.active);
      }
    }
  });
}
</script>

<style scoped>
.creation-sidebar {
  width: 280px;
  height: 100%;
  background-color: white;
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
}

.dark .creation-sidebar {
  background-color: var(--color-gray-800);
  border-right: 1px solid var(--color-gray-700);
}

.creation-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  gap: 0.5rem;
}

.toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.toggle-button:hover {
  background-color: var(--color-gray-100);
}

.dark .toggle-button:hover {
  background-color: var(--color-gray-700);
}

.sidebar-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
}

.creation-types {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 0.5rem 1rem;
  overflow-y: auto;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.type-item:hover {
  background-color: var(--color-gray-100);
}

.dark .type-item:hover {
  background-color: var(--color-gray-700);
}

.type-item.active {
  background-color: var(--color-primary-50);
  color: var(--color-primary-600);
}

.dark .type-item.active {
  background-color: rgba(79, 70, 229, 0.2);
  color: var(--color-primary-400);
}

.type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.type-info {
  flex: 1;
  min-width: 0;
}

.type-name {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.type-description {
  font-size: 0.75rem;
  color: var(--color-gray-500);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .type-description {
  color: var(--color-gray-400);
}

.sidebar-divider {
  height: 1px;
  background-color: var(--color-gray-200);
  margin: 0.5rem 1rem;
}

.dark .sidebar-divider {
  background-color: var(--color-gray-700);
}

.history-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 0.5rem 1rem;
  overflow-y: auto;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 0;
  color: var(--color-gray-400);
  font-size: 0.875rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.history-item:hover {
  background-color: var(--color-gray-100);
}

.dark .history-item:hover {
  background-color: var(--color-gray-700);
}

.history-item.active {
  background-color: var(--color-primary-50);
  color: var(--color-primary-600);
}

.dark .history-item.active {
  background-color: rgba(79, 70, 229, 0.2);
  color: var(--color-primary-400);
}

.history-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.history-info {
  flex: 1;
  min-width: 0;
}

.history-title {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-date {
  font-size: 0.75rem;
  color: var(--color-gray-500);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .history-date {
  color: var(--color-gray-400);
}

.history-actions {
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.history-item:hover .history-actions {
  opacity: 1;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.action-button:hover {
  background-color: var(--color-gray-200);
}

.dark .action-button:hover {
  background-color: var(--color-gray-600);
}

@media (max-width: 768px) {
  .creation-sidebar {
    position: absolute;
    z-index: 100;
    height: calc(100% - 60px);
    top: 60px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  }
  
  .dark .creation-sidebar {
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.3);
  }
  
  .creation-sidebar.collapsed {
    transform: translateX(-100%);
  }
}
</style>
