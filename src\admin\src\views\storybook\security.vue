<template>
  <div>
    <n-card :bordered="false" class="mt-4 mb-4 proCard">
      <template #header>
        <div class="text-lg font-bold">内容安全配置</div>
      </template>

      <n-spin :show="configLoading">
        <n-form
          v-if="securityConfig"
          ref="formRef"
          :model="securityConfig"
          label-placement="left"
          label-width="200px"
          require-mark-placement="right-hanging"
        >
          <n-form-item label="自动审核" path="autoAudit">
            <n-switch v-model:value="securityConfig.autoAudit">
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
            <div class="text-gray-400 text-sm mt-1">
              开启后，新创建的绘本将自动进行内容安全审核
            </div>
          </n-form-item>
          <n-form-item label="敏感内容过滤" path="sensitiveFilter">
            <n-switch v-model:value="securityConfig.sensitiveFilter">
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </n-switch>
            <div class="text-gray-400 text-sm mt-1">
              开启后，用户输入的内容将自动过滤敏感词
            </div>
          </n-form-item>
          <n-form-item>
            <n-button type="primary" @click="saveSecurityConfig" :loading="configSubmitting">
              保存配置
            </n-button>
          </n-form-item>
        </n-form>
      </n-spin>
    </n-card>

    <n-card :bordered="false" class="mt-4 mb-4 proCard">
      <template #header>
        <div class="text-lg font-bold">内容审核记录</div>
      </template>

      <div class="flex items-center justify-between mb-4">
        <div>
          <n-button type="primary" @click="handleRefresh">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新
          </n-button>
        </div>
        <div class="flex items-center">
          <n-input
            v-model:value="searchParams.keyword"
            placeholder="请输入绘本标题"
            clearable
            class="w-200px mr-2"
            @keydown.enter="handleSearch"
          />
          <n-select
            v-model:value="searchParams.auditResult"
            placeholder="审核结果"
            clearable
            :options="auditResultOptions"
            class="w-150px mr-2"
            @update:value="handleSearch"
          />
          <n-button type="primary" @click="handleSearch">
            <template #icon>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
            搜索
          </n-button>
        </div>
      </div>

      <n-data-table
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
      />
    </n-card>

    <!-- 审核详情对话框 -->
    <n-modal v-model:show="showDetailModal" preset="card" style="width: 700px">
      <template #header>
        <div class="text-lg font-bold">审核详情</div>
      </template>
      <div v-if="currentAudit">
        <div class="mb-4">
          <n-tag :type="currentAudit.safe ? 'success' : 'error'" size="large">
            {{ currentAudit.safe ? '内容安全' : '存在敏感内容' }}
          </n-tag>
        </div>

        <div v-if="!currentAudit.safe && currentAudit.issues.length > 0">
          <h3 class="text-lg font-bold mb-2">问题详情</h3>
          <n-list bordered>
            <n-list-item v-for="(issue, index) in currentAudit.issues" :key="index">
              <div class="flex flex-col">
                <div class="mb-2">
                  <n-tag type="error">{{ getIssueTypeText(issue.type) }}</n-tag>
                </div>
                <div class="mb-2">
                  <span class="font-bold">内容：</span>
                  {{ issue.content }}
                </div>
                <div>
                  <span class="font-bold">触发敏感词：</span>
                  <n-tag v-for="word in issue.triggeredWords" :key="word" class="mr-2 mt-1" type="error">
                    {{ word }}
                  </n-tag>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { RefreshOutline, SearchOutline, EyeOutline } from '@vicons/ionicons5';
import {
  getSecurityConfig,
  updateSecurityConfig,
  getStorybookList,
  auditStorybookContent,
} from '@/api/modules/storybook';

const router = useRouter();
const message = useMessage();

// 审核结果选项
const auditResultOptions = [
  { label: '安全', value: 'safe' },
  { label: '不安全', value: 'unsafe' },
];

// 搜索参数
const searchParams = reactive({
  keyword: '',
  auditResult: null,
  page: 1,
  limit: 10,
});

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageSizes: [10, 20, 30, 40],
  showSizePicker: true,
  onChange: (page: number) => {
    pagination.page = page;
    searchParams.page = page;
    loadData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchParams.page = 1;
    searchParams.limit = pageSize;
    loadData();
  },
});

// 安全配置
const securityConfig = ref<any>({
  autoAudit: false,
  sensitiveFilter: false,
});
const configLoading = ref(false);
const configSubmitting = ref(false);

// 审核详情
const showDetailModal = ref(false);
const currentAudit = ref<any>(null);

// 获取问题类型文本
const getIssueTypeText = (type: string) => {
  const typeMap = {
    title: '标题',
    description: '描述',
    page: '页面内容',
    image: '图像描述',
  };
  return typeMap[type] || type;
};

// 表格列定义
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
  },
  {
    title: '绘本标题',
    key: 'title',
    width: 200,
  },
  {
    title: '创建者',
    key: 'userName',
    width: 120,
  },
  {
    title: '审核结果',
    key: 'auditResult',
    width: 100,
    render(row: any) {
      return h(
        'n-tag',
        {
          type: row.auditResult === 'safe' ? 'success' : 'error',
          size: 'small',
        },
        { default: () => (row.auditResult === 'safe' ? '安全' : '不安全') }
      );
    },
  },
  {
    title: '问题数量',
    key: 'issueCount',
    width: 100,
  },
  {
    title: '审核时间',
    key: 'auditTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row: any) {
      return h('div', { class: 'flex space-x-2' }, [
        h(
          'n-button',
          {
            size: 'small',
            onClick: () => handleViewDetail(row),
          },
          { default: () => '查看详情', icon: () => h(EyeOutline) }
        ),
        h(
          'n-button',
          {
            size: 'small',
            type: 'primary',
            onClick: () => handleReaudit(row),
          },
          { default: () => '重新审核' }
        ),
      ]);
    },
  },
];

// 加载安全配置
const loadSecurityConfig = async () => {
  configLoading.value = true;
  try {
    const res = await getSecurityConfig();
    securityConfig.value = res.data;
  } catch (error) {
    console.error('加载安全配置失败', error);
    message.error('加载安全配置失败');
  } finally {
    configLoading.value = false;
  }
};

// 保存安全配置
const saveSecurityConfig = async () => {
  configSubmitting.value = true;
  try {
    await updateSecurityConfig(securityConfig.value);
    message.success('保存配置成功');
  } catch (error) {
    console.error('保存配置失败', error);
    message.error('保存配置失败');
  } finally {
    configSubmitting.value = false;
  }
};

// 加载审核记录
const loadData = async () => {
  loading.value = true;
  try {
    // 这里使用模拟数据，实际应该调用专门的审核记录API
    const res = await getStorybookList({
      ...searchParams,
      auditStatus: searchParams.auditResult === 'unsafe' ? 3 : null,
    });
    
    // 处理数据，添加审核相关字段
    tableData.value = res.data.items.map((item: any) => ({
      ...item,
      auditResult: item.status === 3 ? 'unsafe' : 'safe',
      issueCount: item.status === 3 ? Math.floor(Math.random() * 5) + 1 : 0,
      auditTime: item.updatedAt,
    }));
    
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error('加载审核记录失败', error);
    message.error('加载审核记录失败');
  } finally {
    loading.value = false;
  }
};

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  searchParams.page = page;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  searchParams.page = 1;
  pagination.page = 1;
  loadData();
};

// 处理刷新
const handleRefresh = () => {
  loadData();
};

// 查看审核详情
const handleViewDetail = async (row: any) => {
  try {
    const res = await auditStorybookContent(row.id);
    currentAudit.value = res.data;
    showDetailModal.value = true;
  } catch (error) {
    console.error('获取审核详情失败', error);
    message.error('获取审核详情失败');
  }
};

// 重新审核
const handleReaudit = async (row: any) => {
  try {
    message.loading('正在审核中...');
    const res = await auditStorybookContent(row.id);
    message.success('审核完成');
    
    // 更新当前行数据
    const index = tableData.value.findIndex((item) => item.id === row.id);
    if (index !== -1) {
      tableData.value[index].auditResult = res.data.safe ? 'safe' : 'unsafe';
      tableData.value[index].issueCount = res.data.safe ? 0 : res.data.issues.length;
      tableData.value[index].auditTime = new Date().toLocaleString();
    }
    
    // 显示审核详情
    currentAudit.value = res.data;
    showDetailModal.value = true;
  } catch (error) {
    console.error('审核失败', error);
    message.error('审核失败');
  }
};

onMounted(() => {
  loadSecurityConfig();
  loadData();
});
</script>

<style scoped>
.proCard {
  border-radius: 4px;
}
</style>
