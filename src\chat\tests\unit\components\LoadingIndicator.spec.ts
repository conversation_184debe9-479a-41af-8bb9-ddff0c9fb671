import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import LoadingIndicator from '@/components/LoadingIndicator.vue';

describe('LoadingIndicator.vue', () => {
  it('renders correctly when visible is true', () => {
    const wrapper = mount(LoadingIndicator, {
      props: {
        visible: true,
        message: '加载中...'
      }
    });

    // 检查组件是否渲染
    expect(wrapper.text()).toContain('加载中...');
    
    // 检查角色属性是否正确
    const indicator = wrapper.find('[role="status"]');
    expect(indicator.exists()).toBe(true);
  });

  it('does not render when visible is false', () => {
    const wrapper = mount(LoadingIndicator, {
      props: {
        visible: false,
        message: '加载中...'
      }
    });

    // 检查组件是否不可见
    const indicator = wrapper.find('[role="status"]');
    expect(indicator.exists()).toBe(false);
  });

  it('applies custom overlay class', () => {
    const wrapper = mount(LoadingIndicator, {
      props: {
        visible: true,
        message: '加载中...',
        overlayClass: 'custom-overlay'
      }
    });

    // 检查自定义类是否应用
    const indicator = wrapper.find('[role="status"]');
    expect(indicator.classes()).toContain('custom-overlay');
  });

  it('uses default message when not provided', () => {
    const wrapper = mount(LoadingIndicator, {
      props: {
        visible: true
      }
    });

    // 检查是否使用默认消息
    expect(wrapper.text()).toContain('加载中...');
  });
});
