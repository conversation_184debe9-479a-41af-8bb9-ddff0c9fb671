import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { StorybookPageEntity } from './storybook-page.entity';
import { StorybookCharacterEntity } from './storybook-character.entity';
import { StorybookFolderEntity } from './storybook-folder.entity';

@Entity({ name: 'storybook' })
export class StorybookEntity extends BaseEntity {
  @Column({ comment: '绘本标题' })
  title: string;

  @Column({ comment: '绘本描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '封面图片URL', type: 'text', nullable: true })
  coverImg: string;

  @Column({ comment: '创建用户ID' })
  userId: number;

  @Column({ comment: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', default: 0 })
  status: number;

  @Column({ comment: '是否公开(0:私有,1:公开)', default: 0 })
  isPublic: number;

  @Column({ comment: '是否推荐(0:否,1:是)', default: 0 })
  isRecommended: number;

  @Column({ comment: '浏览次数', default: 0 })
  viewCount: number;

  @Column({ comment: '点赞次数', default: 0 })
  likeCount: number;

  @Column({ comment: '绘本内容JSON', type: 'text', nullable: true })
  content: string;

  @Column({ comment: '来源', nullable: true, default: 'storybook' })
  source: string;

  @Column({ comment: '标签', type: 'simple-array', nullable: true })
  tags: string[];

  @OneToMany(() => StorybookPageEntity, page => page.storybook)
  pages: StorybookPageEntity[];

  @OneToMany(() => StorybookCharacterEntity, character => character.storybook)
  characters: StorybookCharacterEntity[];

  @ManyToOne(() => StorybookFolderEntity, folder => folder.storybooks, { nullable: true })
  @JoinColumn({ name: 'folderId' })
  folder: StorybookFolderEntity;

  @Column({ comment: '所属文件夹ID', nullable: true })
  folderId: number;

  @Column({ comment: '页数', default: 0 })
  pageCount: number;

  @Column({ comment: '字数', default: 0 })
  wordCount: number;

  @Column({ comment: '最后编辑时间', nullable: true })
  lastEditedAt: Date;

  @Column({ comment: '是否在回收站', default: 0 })
  isDeleted: number;

  @Column({ comment: '删除时间', nullable: true })
  deletedAt: Date;
}
