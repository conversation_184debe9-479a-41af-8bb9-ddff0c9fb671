/* 过渡动画样式 */

/* 淡入淡出过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滑动过渡 */
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-right-enter-from,
.slide-left-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

.slide-right-leave-to,
.slide-left-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

/* 滑动上下过渡 */
.slide-up-enter-active,
.slide-up-leave-active,
.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-up-enter-from,
.slide-down-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to,
.slide-down-enter-from {
  transform: translateY(-20px);
  opacity: 0;
}

/* 缩放过渡 */
.scale-enter-active,
.scale-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.9);
  opacity: 0;
}

/* 弹性过渡 */
.bounce-enter-active {
  animation: bounce-in 0.5s;
}
.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}
@keyframes bounce-in {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
  }
}

/* 旋转过渡 */
.rotate-enter-active,
.rotate-leave-active {
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.rotate-enter-from {
  transform: rotate(-180deg) scale(0.7);
  opacity: 0;
}

.rotate-leave-to {
  transform: rotate(180deg) scale(0.7);
  opacity: 0;
}

/* 折叠过渡 */
.collapse-enter-active,
.collapse-leave-active {
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 1000px;
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
  opacity: 0;
}

/* 抽屉过渡 */
.drawer-right-enter-active,
.drawer-right-leave-active,
.drawer-left-enter-active,
.drawer-left-leave-active {
  transition: transform 0.3s ease;
}

.drawer-right-enter-from,
.drawer-right-leave-to {
  transform: translateX(100%);
}

.drawer-left-enter-from,
.drawer-left-leave-to {
  transform: translateX(-100%);
}

/* 页面过渡 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 列表项过渡 */
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 列表移动过渡 */
.list-move {
  transition: transform 0.5s ease;
}

/* 卡片翻转过渡 */
.flip-enter-active {
  animation: flip-in 0.5s;
}
.flip-leave-active {
  animation: flip-in 0.5s reverse;
}
@keyframes flip-in {
  0% {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateY(-10deg);
  }
  70% {
    transform: perspective(400px) rotateY(10deg);
  }
  100% {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

/* 模态框过渡 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}
.modal-enter-from {
  opacity: 0;
  transform: scale(1.1);
}
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 背景遮罩过渡 */
.overlay-enter-active,
.overlay-leave-active {
  transition: opacity 0.3s ease;
}
.overlay-enter-from,
.overlay-leave-to {
  opacity: 0;
}

/* 消息气泡过渡 */
.message-enter-active {
  animation: message-in 0.4s ease-out forwards;
}
.message-leave-active {
  animation: message-out 0.3s ease-in forwards;
}
@keyframes message-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes message-out {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* 打字机效果过渡 */
.typing-enter-active {
  transition: all 0.3s;
}
.typing-leave-active {
  transition: all 0.3s;
  position: absolute;
}
.typing-enter-from {
  opacity: 0;
  transform: translateY(20px);
}
.typing-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 路由过渡 */
.router-enter-active,
.router-leave-active {
  transition: opacity 0.5s ease;
}
.router-enter-from,
.router-leave-to {
  opacity: 0;
}

/* 侧边栏过渡 */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.sidebar-enter-from,
.sidebar-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

/* 工具栏过渡 */
.toolbar-enter-active,
.toolbar-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.toolbar-enter-from,
.toolbar-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 底部栏过渡 */
.footer-enter-active,
.footer-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.footer-enter-from,
.footer-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* 标签页过渡 */
.tab-enter-active,
.tab-leave-active {
  transition: all 0.3s;
}
.tab-enter-from,
.tab-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* 菜单过渡 */
.menu-enter-active,
.menu-leave-active {
  transition: all 0.3s ease;
  transform-origin: top left;
}
.menu-enter-from,
.menu-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 提示框过渡 */
.tooltip-enter-active,
.tooltip-leave-active {
  transition: all 0.3s ease;
}
.tooltip-enter-from,
.tooltip-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 抽屉过渡 */
.drawer-enter-active,
.drawer-leave-active {
  transition: all 0.3s ease;
}
.drawer-enter-from,
.drawer-leave-to {
  transform: translateX(100%);
}

/* 下拉菜单过渡 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s ease;
  transform-origin: top;
}
.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0);
}

/* 通知过渡 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}
.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}
.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 加载过渡 */
.loading-enter-active,
.loading-leave-active {
  transition: opacity 0.5s ease;
}
.loading-enter-from,
.loading-leave-to {
  opacity: 0;
}

/* 卡片堆叠过渡 */
.stack-enter-active,
.stack-leave-active {
  transition: all 0.3s ease;
  position: absolute;
  width: 100%;
}
.stack-enter-from {
  opacity: 0;
  transform: translateY(30px);
}
.stack-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

/* 3D翻转过渡 */
.flip-3d-enter-active,
.flip-3d-leave-active {
  transition: all 0.6s;
  transform-style: preserve-3d;
}
.flip-3d-enter-from {
  transform: rotateY(180deg);
}
.flip-3d-leave-to {
  transform: rotateY(-180deg);
}

/* 滑动抽屉过渡 */
.slide-drawer-enter-active,
.slide-drawer-leave-active {
  transition: transform 0.3s ease-out;
}
.slide-drawer-enter-from,
.slide-drawer-leave-to {
  transform: translateX(-100%);
}

/* 缩放淡入淡出过渡 */
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition: transform 0.3s, opacity 0.3s;
}
.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.97);
}
.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.03);
}

/* 滑动覆盖过渡 */
.slide-cover-enter-active,
.slide-cover-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.slide-cover-enter-from {
  transform: translateX(100%);
}
.slide-cover-leave-to {
  transform: translateX(-100%);
}

/* 滑动揭示过渡 */
.slide-reveal-enter-active,
.slide-reveal-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.slide-reveal-enter-from {
  transform: translateX(100%);
}
.slide-reveal-leave-to {
  transform: translateX(-30%);
  opacity: 0;
}

/* 折叠卡片过渡 */
.fold-card-enter-active,
.fold-card-leave-active {
  transition: all 0.3s;
  transform-origin: center top;
}
.fold-card-enter-from,
.fold-card-leave-to {
  transform: scaleY(0);
  opacity: 0;
}

/* 弹出菜单过渡 */
.pop-menu-enter-active,
.pop-menu-leave-active {
  transition: all 0.2s;
}
.pop-menu-enter-from,
.pop-menu-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 滑动面板过渡 */
.slide-panel-enter-active,
.slide-panel-leave-active {
  transition: all 0.3s;
}
.slide-panel-enter-from {
  transform: translateY(100%);
}
.slide-panel-leave-to {
  transform: translateY(100%);
}

/* 展开折叠过渡 */
.expand-collapse-enter-active,
.expand-collapse-leave-active {
  transition: all 0.3s;
  max-height: 300px;
  overflow: hidden;
}
.expand-collapse-enter-from,
.expand-collapse-leave-to {
  max-height: 0;
  opacity: 0;
}

/* 滑动切换过渡 */
.slide-switch-enter-active,
.slide-switch-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.slide-switch-enter-from {
  opacity: 0;
  transform: translateX(30px);
}
.slide-switch-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 卡片堆叠过渡 */
.card-stack-enter-active,
.card-stack-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.card-stack-enter-from {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}
.card-stack-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.95);
}

/* 3D旋转过渡 */
.rotate-3d-enter-active,
.rotate-3d-leave-active {
  transition: all 0.5s;
  transform-style: preserve-3d;
}
.rotate-3d-enter-from {
  opacity: 0;
  transform: rotateX(-90deg);
}
.rotate-3d-leave-to {
  opacity: 0;
  transform: rotateX(90deg);
}

/* 滑动抽屉过渡 */
.drawer-slide-enter-active,
.drawer-slide-leave-active {
  transition: transform 0.3s ease-out;
}
.drawer-slide-enter-from,
.drawer-slide-leave-to {
  transform: translateX(100%);
}

/* 缩放淡入淡出过渡 */
.scale-fade-enter-active,
.scale-fade-leave-active {
  transition: transform 0.3s, opacity 0.3s;
}
.scale-fade-enter-from {
  opacity: 0;
  transform: scale(0.9);
}
.scale-fade-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 滑动覆盖过渡 */
.cover-slide-enter-active,
.cover-slide-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.cover-slide-enter-from {
  transform: translateY(100%);
}
.cover-slide-leave-to {
  transform: translateY(-100%);
}

/* 滑动揭示过渡 */
.reveal-slide-enter-active,
.reveal-slide-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.reveal-slide-enter-from {
  transform: translateY(100%);
}
.reveal-slide-leave-to {
  transform: translateY(-30%);
  opacity: 0;
}

/* 折叠卡片过渡 */
.card-fold-enter-active,
.card-fold-leave-active {
  transition: all 0.3s;
  transform-origin: center top;
}
.card-fold-enter-from,
.card-fold-leave-to {
  transform: scaleY(0);
  opacity: 0;
}

/* 弹出菜单过渡 */
.menu-pop-enter-active,
.menu-pop-leave-active {
  transition: all 0.2s;
}
.menu-pop-enter-from,
.menu-pop-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 滑动面板过渡 */
.panel-slide-enter-active,
.panel-slide-leave-active {
  transition: all 0.3s;
}
.panel-slide-enter-from {
  transform: translateX(100%);
}
.panel-slide-leave-to {
  transform: translateX(100%);
}

/* 展开折叠过渡 */
.collapse-expand-enter-active,
.collapse-expand-leave-active {
  transition: all 0.3s;
  max-height: 300px;
  overflow: hidden;
}
.collapse-expand-enter-from,
.collapse-expand-leave-to {
  max-height: 0;
  opacity: 0;
}

/* 滑动切换过渡 */
.switch-slide-enter-active,
.switch-slide-leave-active {
  transition: all 0.3s;
  position: absolute;
  width: 100%;
}
.switch-slide-enter-from {
  opacity: 0;
  transform: translateY(30px);
}
.switch-slide-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}
