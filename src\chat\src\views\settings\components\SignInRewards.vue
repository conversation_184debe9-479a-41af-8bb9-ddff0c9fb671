<template>
  <div class="signin-rewards">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">签到奖励</h1>
      <p class="page-description">每日签到获取积分奖励</p>
    </div>

    <!-- 复用现有的签到组件 -->
    <div class="signin-wrapper">
      <SignInDialog />
    </div>
  </div>
</template>

<script setup lang="ts">
import SignInDialog from '@/layout/components/Settings/SignInDialog.vue';
</script>

<style scoped>
.signin-rewards {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.signin-wrapper {
  @apply w-full;
}

/* 覆盖内部组件样式以适应新的布局 */
.signin-wrapper :deep(.signin-container) {
  @apply bg-transparent shadow-none border-0 p-0;
}
</style>
