import api from '../index';

/**
 * 绘本管理API模块
 */
export default {
  /**
   * 获取所有绘本列表
   * @param params 查询参数
   * @returns 绘本列表
   */
  getAllStorybooks: (params: any) => api.get('admin/storybook', { params }),

  /**
   * 获取绘本详情
   * @param id 绘本ID
   * @returns 绘本详情
   */
  getStorybookDetail: (id: number) => api.get(`admin/storybook/${id}`),

  /**
   * 更新绘本状态
   * @param id 绘本ID
   * @param data 状态数据
   * @returns 更新结果
   */
  updateStorybookStatus: (id: number, data: { status: number }) =>
    api.put(`admin/storybook/${id}/status`, data),

  /**
   * 设置绘本推荐状态
   * @param id 绘本ID
   * @param data 推荐状态数据
   * @returns 更新结果
   */
  setStorybookRecommend: (id: number, data: { isRecommended: number }) =>
    api.put(`admin/storybook/${id}/recommend`, data),

  /**
   * 删除绘本
   * @param id 绘本ID
   * @returns 删除结果
   */
  deleteStorybook: (id: number) => api.delete(`admin/storybook/${id}`),

  /**
   * 获取绘本图片列表
   * @param params 查询参数
   * @returns 图片列表
   */
  getStorybookImages: (params: any) => api.get('admin/storybook/image', { params }),

  /**
   * 获取图片详情
   * @param id 图片ID
   * @returns 图片详情
   */
  getImageDetail: (id: number) => api.get(`admin/storybook/image/${id}`),

  /**
   * 更新图片审核状态
   * @param id 图片ID
   * @param data 审核状态数据
   * @returns 更新结果
   */
  updateImageAuditStatus: (id: number, data: { auditStatus: number, auditRemark?: string }) =>
    api.put(`admin/storybook/image/${id}/audit`, data),

  /**
   * 删除图片
   * @param id 图片ID
   * @returns 删除结果
   */
  deleteImage: (id: number) => api.delete(`admin/storybook/image/${id}`),

  /**
   * 获取绘本配置
   * @returns 配置列表
   */
  getStorybookConfig: () => api.get('admin/storybook/config'),

  /**
   * 更新绘本配置
   * @param data 配置数据
   * @returns 更新结果
   */
  updateStorybookConfig: (data: any) => api.put('admin/storybook/config', data),

  /**
   * 获取图像生成配置
   * @returns 图像生成配置
   */
  getImageGenerationConfig: () => api.get('admin/storybook/image-config'),

  /**
   * 更新图像生成配置
   * @param data 图像生成配置数据
   * @returns 更新结果
   */
  updateImageGenerationConfig: (data: {
    model?: string;
    size?: string;
    quality?: string;
    style?: string;
    fallbackService?: string;
    enabled?: boolean;
  }) => api.put('admin/storybook/image-config', data),

  /**
   * 获取绘本统计数据
   * @param params 查询参数
   * @returns 统计数据
   */
  getStorybookStatistics: (params: any) => api.get('admin/storybook/statistics', { params }),

  /**
   * 获取模板列表
   * @param params 查询参数
   * @returns 模板列表
   */
  getTemplateList: (params: any) => api.get('admin/storybook/template', { params }),

  /**
   * 获取模板详情
   * @param id 模板ID
   * @returns 模板详情
   */
  getTemplateDetail: (id: number) => api.get(`admin/storybook/template/${id}`),

  /**
   * 创建模板
   * @param data 模板数据
   * @returns 创建结果
   */
  createTemplate: (data: any) => api.post('admin/storybook/template', data),

  /**
   * 更新模板
   * @param id 模板ID
   * @param data 模板数据
   * @returns 更新结果
   */
  updateTemplate: (id: number, data: any) => api.put(`admin/storybook/template/${id}`, data),

  /**
   * 删除模板
   * @param id 模板ID
   * @returns 删除结果
   */
  deleteTemplate: (id: number) => api.delete(`admin/storybook/template/${id}`),

  /**
   * 获取提示词列表
   * @param params 查询参数
   * @returns 提示词列表
   */
  getPromptList: (params: any) => api.get('admin/storybook/prompt', { params }),

  /**
   * 获取提示词详情
   * @param id 提示词ID
   * @returns 提示词详情
   */
  getPromptDetail: (id: number) => api.get(`admin/storybook/prompt/${id}`),

  /**
   * 创建提示词
   * @param data 提示词数据
   * @returns 创建结果
   */
  createPrompt: (data: any) => api.post('admin/storybook/prompt', data),

  /**
   * 更新提示词
   * @param id 提示词ID
   * @param data 提示词数据
   * @returns 更新结果
   */
  updatePrompt: (id: number, data: any) => api.put(`admin/storybook/prompt/${id}`, data),

  /**
   * 删除提示词
   * @param id 提示词ID
   * @returns 删除结果
   */
  deletePrompt: (id: number) => api.delete(`admin/storybook/prompt/${id}`),
};
