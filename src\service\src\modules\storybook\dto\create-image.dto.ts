import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsObject } from 'class-validator';

export class CreateImageDto {
  @ApiProperty({ description: '图片URL' })
  @IsNotEmpty({ message: '图片URL不能为空' })
  @IsString()
  imageUrl: string;

  @ApiProperty({ description: '图片描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '生成提示词', required: false })
  @IsOptional()
  @IsString()
  prompt?: string;

  @ApiProperty({ description: '使用的模型', required: false })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({ description: '图片类型(1:角色图,2:页面图,3:封面图)', required: false, default: 2 })
  @IsOptional()
  @IsNumber()
  imageType?: number;

  @ApiProperty({ description: '所属绘本ID', required: false })
  @IsOptional()
  @IsNumber()
  storybookId?: number;

  @ApiProperty({ description: '所属页面ID', required: false })
  @IsOptional()
  @IsNumber()
  pageId?: number;

  @ApiProperty({ description: '所属角色ID', required: false })
  @IsOptional()
  @IsNumber()
  characterId?: number;

  @ApiProperty({ description: '图片宽度', required: false })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({ description: '图片高度', required: false })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({ description: '图片大小(KB)', required: false })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({ description: '图片格式', required: false })
  @IsOptional()
  @IsString()
  format?: string;

  @ApiProperty({ description: '生成参数', required: false })
  @IsOptional()
  @IsObject()
  generationParams?: object;
}
