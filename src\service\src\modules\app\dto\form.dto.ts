import { ApiProperty } from '@nestjs/swagger';
import { IsDefined, IsIn, IsNumber, IsOptional } from 'class-validator';

export class CreateFormDto {
  @ApiProperty({ example: '用户需求表单', description: '表单名称', required: true })
  @IsDefined({ message: '表单名称是必传参数' })
  name: string;

  @ApiProperty({ example: '用于收集用户需求的表单', description: '表单描述', required: false })
  @IsOptional()
  description: string;

  @ApiProperty({ example: 1, description: '关联的应用ID', required: true })
  @IsDefined({ message: '应用ID是必传参数' })
  appId: number;

  @ApiProperty({ 
    example: '[{"type":"input","label":"项目名称","required":true,"placeholder":"请输入项目名称"},{"type":"textarea","label":"需求描述","required":true,"placeholder":"请详细描述您的需求"}]', 
    description: '表单字段定义', 
    required: true 
  })
  @IsDefined({ message: '表单字段定义是必传参数' })
  fields: string;

  @ApiProperty({ example: 100, description: '表单排序、数字越大越靠前', required: false })
  @IsOptional()
  order: number;

  @ApiProperty({ example: 1, description: '表单状态 0：禁用 1：启用', required: true })
  @IsNumber({}, { message: '表单状态必须是Number' })
  @IsIn([0, 1], { message: '表单状态错误' })
  status: number;
}

export class UpdateFormDto extends CreateFormDto {
  @ApiProperty({ example: 1, description: '表单ID', required: true })
  @IsDefined({ message: '表单ID是必传参数' })
  id: number;
}

export class QueryFormDto {
  @ApiProperty({ example: 1, description: '页码', required: false })
  @IsOptional()
  page?: number;

  @ApiProperty({ example: 10, description: '每页数量', required: false })
  @IsOptional()
  size?: number;

  @ApiProperty({ example: '表单名称', description: '表单名称', required: false })
  @IsOptional()
  name?: string;

  @ApiProperty({ example: 1, description: '表单状态', required: false })
  @IsOptional()
  status?: number;

  @ApiProperty({ example: 1, description: '应用ID', required: false })
  @IsOptional()
  appId?: number;
}

export class DeleteFormDto {
  @ApiProperty({ example: 1, description: '表单ID', required: true })
  @IsDefined({ message: '表单ID是必传参数' })
  id: number;
}
