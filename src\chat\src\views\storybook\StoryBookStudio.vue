<script setup lang="ts">
import { ref, reactive, onMounted, provide, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NLayout, NLayoutSider, NLayoutContent, NTabs, NTabPane, NButton, NIcon, NTooltip, NDrawer, NDrawerContent } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import CreationWorkspaceWrapper from './components/CreationWorkspaceWrapper.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore, useChatStore } from '@/store';
import {
  saveStorybookProject,
  getStorybookProject,
  clearStorybookProject,
  saveCurrentStep,
  getCurrentStep,
  saveActiveTab,
  getActiveTab,
  createNewStorybookProject
} from '@/utils/storybookStorage';

// 创建一个日志记录函数，用于记录绘本创作过程中的关键事件
const logStoryBookEvent = (eventType, message, data = {}) => {
  const timestamp = new Date().toISOString();
  const logPrefix = '[AI绘本创作]';
  const logMessage = `${logPrefix} [${eventType}] ${message}`;

  // 根据事件类型选择不同的日志级别
  switch (eventType) {
    case 'ERROR':
      console.error(logMessage, data);
      break;
    case 'WARNING':
      console.warn(logMessage, data);
      break;
    case 'INFO':
      console.info(logMessage, data);
      break;
    default:
      console.log(logMessage, data);
  }

  // 在这里可以添加更多的日志处理逻辑，如发送到服务器等
  return { timestamp, eventType, message, data };
};

const authStore = useAuthStore();
const chatStore = useChatStore();

// 创建一个本地的onConversation函数实现
const onConversation = async ({
  msg,
  model,
  modelName,
  modelType,
  extraParam,
  onSuccess
}) => {
  try {
    // 记录开始生成图像的事件
    logStoryBookEvent('INFO', '开始图像生成请求', {
      msg: msg.length > 100 ? msg.substring(0, 100) + '...' : msg,
      model,
      modelName,
      modelType,
      extraParam
    });

    // 使用fetchChatAPIProcess发送请求
    logStoryBookEvent('DEBUG', '发送API请求参数', {
      model: model || 'gpt-image-1',
      modelName: modelName || 'GPT Image',
      modelType: modelType || 2,
      promptLength: msg.length,
      extraParam: extraParam || { size: '1024x1024' }
    });

    const response = await fetchChatAPIProcess({
      model: model || 'gpt-image-1',
      modelName: modelName || 'GPT Image',
      modelType: modelType || 2, // 图像生成模式
      prompt: msg,
      extraParam: extraParam || {
        size: '1024x1024'
      },
      options: {
        groupId: chatStore.active || 0
      }
    });

    // 记录初始响应
    logStoryBookEvent('DEBUG', '图像生成初始响应', {
      status: response?.status,
      hasFileInfo: !!response?.fileInfo,
      error: response?.error
    });

    // 如果响应中已经包含了文件信息，则直接调用成功回调
    if (response && response.fileInfo) {
      logStoryBookEvent('INFO', '图像已直接生成成功', {
        fileUrl: response.fileInfo.substring(0, 50) + '...'
      });
      if (typeof onSuccess === 'function') {
        onSuccess(response);
      }
      return response;
    }

    // 使用聊天界面的轮询机制，通过queryActiveChatLogList查询最新状态
    logStoryBookEvent('INFO', '开始使用聊天界面轮询机制检查图像生成状态');

    // 创建一个轮询函数来检查图像生成状态
    const pollImageStatus = () => {
      // 返回一个初始响应，让UI显示处理中状态
      const initialResponse = {
        status: 2,
        model: model || 'gpt-image-1',
        modelName: modelName || 'GPT Image',
        text: '图像生成中...',
        progress: '10%'
      };

      // 立即调用成功回调，显示处理中状态
      if (typeof onSuccess === 'function') {
        onSuccess(initialResponse);
      }

      let maxRetries = 20; // 最多尝试20次
      let retryCount = 0;
      const checkInterval = 5000; // 5秒检查一次，与聊天界面保持一致

      // 创建一个定时器来定期检查图像生成状态
      const intervalId = setInterval(async () => {
        try {
          retryCount++;
          logStoryBookEvent('DEBUG', `定时轮询检查图像生成状态，第${retryCount}次尝试`);

          // 查询最新状态 - 使用与聊天界面相同的方式：查询数据库中的记录
          await chatStore.queryActiveChatLogList();

          // 获取最新的聊天记录
          const chatList = chatStore.chatList;

          // 查找最新的图像生成记录（非用户输入的记录）
          const latestImageRecord = chatList
            .filter(chat => !chat.inversion && chat.modelType === 2 &&
                   (chat.model === 'gpt-image-1' || chat.model === model))
            .pop();

          logStoryBookEvent('DEBUG', '查询到的最新图像记录', {
            found: !!latestImageRecord,
            status: latestImageRecord?.status,
            hasFileInfo: !!latestImageRecord?.fileInfo
          });

          // 如果找到记录并且有文件信息，说明图像已生成完成
          if (latestImageRecord && latestImageRecord.fileInfo) {
            clearInterval(intervalId);
            logStoryBookEvent('INFO', '图像生成成功', {
              fileUrl: latestImageRecord.fileInfo.substring(0, 50) + '...'
            });
            if (typeof onSuccess === 'function') {
              onSuccess({
                status: 3, // 成功状态
                fileInfo: latestImageRecord.fileInfo,
                text: latestImageRecord.text,
                model: latestImageRecord.model,
                modelName: latestImageRecord.modelName
              });
            }
            return;
          }

          // 如果找到记录且状态是失败，则退出
          if (latestImageRecord && latestImageRecord.status === 5) {
            clearInterval(intervalId);
            logStoryBookEvent('ERROR', '图像生成失败', {
              status: latestImageRecord.status
            });
            if (typeof onSuccess === 'function') {
              onSuccess({
                status: 5,
                error: '图像生成失败',
                fileInfo: `https://image.pollinations.ai/prompt/${encodeURIComponent(msg)}`
              });
            }
            return;
          }

          // 如果达到最大重试次数，则视为超时
          if (retryCount >= maxRetries) {
            clearInterval(intervalId);
            logStoryBookEvent('ERROR', '图像生成超时', { maxRetries });

            // 使用备用方案
            if (typeof onSuccess === 'function') {
              onSuccess({
                status: 5,
                error: '图像生成超时',
                fileInfo: `https://image.pollinations.ai/prompt/${encodeURIComponent(msg)}`
              });
            }
            return;
          }
        } catch (error) {
          logStoryBookEvent('ERROR', '检查图像状态失败', {
            error: error.message || error
          });
          // 出错时不停止轮询，继续尝试
        }
      }, checkInterval);

      // 设置一个超时时间，确保定时器不会无限运行
      setTimeout(() => {
        if (intervalId) {
          clearInterval(intervalId);
          logStoryBookEvent('WARNING', '轮询超时自动停止', {
            totalTime: maxRetries * checkInterval / 1000 + '秒'
          });
        }
      }, maxRetries * checkInterval + 1000);

      // 返回初始响应
      return initialResponse;
    };

    // 开始轮询并返回结果
    return pollImageStatus();

  } catch (error) {
    logStoryBookEvent('ERROR', '图像生成请求失败', {
      error: error.message || error
    });
    if (typeof onSuccess === 'function') {
      onSuccess({
        status: 5,
        error: error.message,
        fileInfo: `https://image.pollinations.ai/prompt/${encodeURIComponent(msg)}`
      });
    }
    return {
      status: 5,
      error: error.message,
      fileInfo: `https://image.pollinations.ai/prompt/${encodeURIComponent(msg)}`
    };
  }
};

// 提供onConversation函数给子组件
provide('onConversation', onConversation);

// 提供logStoryBookEvent函数给子组件
provide('logStoryBookEvent', logStoryBookEvent);

// 记录应用启动事件
logStoryBookEvent('INFO', '绘本创作应用已启动');


// 响应式布局
const { isMobile } = useBasicLayout();
const router = useRouter();

// 工作区状态
const route = useRoute();
// 从查询参数中获取活动标签页，如果没有则使用本地存储的值
const activeTab = ref(route.query.tab?.toString() || getActiveTab());
const showSidebar = ref(!isMobile.value);
const sidebarWidth = ref(300);
const showMobileDrawer = ref(false);
const currentStep = ref(getCurrentStep());

// 项目数据 - 从本地存储加载或创建新项目
const storyProject = reactive(getStorybookProject() || createNewStorybookProject());

// 自动保存定时器
const autoSaveTimer = ref(null);

// 启动自动保存定时器
const startAutoSaveTimer = () => {
  // 清除现有定时器
  if (autoSaveTimer.value) {
    clearTimeout(autoSaveTimer.value);
    logStoryBookEvent('DEBUG', '已清除现有自动保存定时器', { timerId: autoSaveTimer.value });
  }

  // 设置新定时器，每5分钟保存一次到本地
  const intervalTime = 5 * 60 * 1000; // 5分钟
  autoSaveTimer.value = setInterval(() => {
    logStoryBookEvent('INFO', '定时器触发自动保存到本地', {
      interval: `${intervalTime/1000}秒`,
      timerId: autoSaveTimer.value,
      currentTime: new Date().toISOString()
    });
    // 保存到本地存储
    saveStorybookProject(storyProject);
  }, intervalTime);

  logStoryBookEvent('INFO', '已启动自动保存定时器', {
    interval: `${intervalTime/1000}秒`,
    timerId: autoSaveTimer.value,
    startTime: new Date().toISOString()
  });
};

// 项目数据变更计数器
let projectChangeCount = 0;

// 监听项目数据变化，自动保存到本地存储
watch(storyProject, (newVal, oldVal) => {
  projectChangeCount++;
  const changeId = projectChangeCount;

  // 保存到本地存储
  const localSaveStart = Date.now();
  saveStorybookProject(storyProject);
  const localSaveEnd = Date.now();

  logStoryBookEvent('INFO', `项目数据已自动保存到本地存储 [变更#${changeId}]`, {
    localStorageSaveTime: `${localSaveEnd - localSaveStart}ms`,
    hasTitle: !!storyProject.title,
    hasCharacters: !!storyProject.characters?.length,
    characterCount: storyProject.characters?.length || 0,
    hasPages: !!storyProject.pages?.length,
    pageCount: storyProject.pages?.length || 0,
    currentStep: currentStep.value
  });
}, { deep: true });

// 监听当前步骤变化，自动保存
watch(currentStep, (newStep, oldStep) => {
  const stepChangeId = Date.now().toString(36);

  // 保存步骤到本地存储
  const localSaveStart = Date.now();
  saveCurrentStep(newStep);
  const localSaveEnd = Date.now();

  logStoryBookEvent('INFO', `当前步骤已更新 [${stepChangeId}]`, {
    from: oldStep,
    to: newStep,
    localStorageSaveTime: `${localSaveEnd - localSaveStart}ms`
  });
});

// 监听活动标签变化，自动保存
watch(activeTab, (newTab, oldTab) => {
  const tabChangeId = Date.now().toString(36);

  // 保存标签到本地存储
  const localSaveStart = Date.now();
  saveActiveTab(newTab);
  const localSaveEnd = Date.now();

  logStoryBookEvent('INFO', `当前活动标签已更新 [${tabChangeId}]`, {
    from: oldTab,
    to: newTab,
    localStorageSaveTime: `${localSaveEnd - localSaveStart}ms`
  });

  // 检查是否从"我的作品"切换到"绘本创作"，并且有创建新故事的标记
  if (oldTab === 'my-works' && newTab === 'outline') {
    const isCreatingNew = localStorage.getItem('storybook-creating-new') === 'true';
    if (isCreatingNew) {
      logStoryBookEvent('INFO', '从我的作品切换到绘本创作，准备创建新故事');

      // 创建一个新的故事项目
      Object.assign(storyProject, createNewStorybookProject());

      // 重置当前步骤
      currentStep.value = 1;

      // 清除标记
      localStorage.removeItem('storybook-creating-new');

      logStoryBookEvent('INFO', '已创建新故事项目数据');
    }
  }
});

// 监听路由查询参数变化，更新活动标签页
watch(() => route.query.tab, (newTab) => {
  if (newTab && typeof newTab === 'string') {
    activeTab.value = newTab;
    logStoryBookEvent('INFO', `从路由查询参数更新活动标签页`, {
      tab: newTab
    });
  }
});

// 工具栏配置 - 现在只使用绘本创作组件，其他功能已集成到其中
const tools = [
  { id: 'outline', label: '绘本创作', emoji: '✏️', component: CreationWorkspaceWrapper }
];

// 切换工具
const switchTool = (toolId: string) => {
  activeTab.value = toolId;
  if (isMobile.value) {
    showMobileDrawer.value = false;
  }
};

// 返回聊天页
const goBack = () => {
  router.push('/chat');
};

// 前往用户中心
const goToUserCenter = () => {
  router.push('/user-center');
};

// 保存项目 - 仅保存到本地
const saveProject = async () => {
  const saveId = Date.now().toString(36);
  logStoryBookEvent('INFO', `开始手动保存项目 [${saveId}]`, {
    title: storyProject.title,
    currentStep: currentStep.value,
    hasCharacters: !!storyProject.characters?.length,
    characterCount: storyProject.characters?.length || 0,
    hasPages: !!storyProject.pages?.length,
    pageCount: storyProject.pages?.length || 0
  });

  storyProject.updatedAt = new Date().toISOString();

  // 保存到本地存储
  const localSaveStart = Date.now();
  saveStorybookProject(storyProject);
  const localSaveEnd = Date.now();

  logStoryBookEvent('INFO', `项目已手动保存到本地 [${saveId}]`, {
    localStorageSaveTime: `${localSaveEnd - localSaveStart}ms`,
    updatedAt: storyProject.updatedAt
  });

  window.$message?.success('项目已保存到本地');

  // 记录完成事件
  const endTime = Date.now();
  logStoryBookEvent('INFO', `手动保存项目完成 [${saveId}]`, {
    totalTime: `${endTime - localSaveStart}ms`,
    currentTime: new Date().toISOString()
  });
};

// 创建新项目 - 仅保存到本地
const createNewProject = () => {
  const createId = Date.now().toString(36);
  logStoryBookEvent('INFO', `尝试创建新项目 [${createId}]`, {
    currentTitle: storyProject.title,
    currentStep: currentStep.value,
    hasCharacters: !!storyProject.characters?.length,
    characterCount: storyProject.characters?.length || 0,
    hasPages: !!storyProject.pages?.length,
    pageCount: storyProject.pages?.length || 0
  });

  if (window.confirm('创建新项目将清除当前未保存的内容，确定要继续吗？')) {
    logStoryBookEvent('INFO', `用户确认创建新项目 [${createId}]`);

    // 创建新项目前先保存当前项目的快照
    try {
      const projectSnapshot = JSON.stringify(storyProject);
      localStorage.setItem('storybook-last-project-backup', projectSnapshot);
      logStoryBookEvent('INFO', `已创建当前项目快照 [${createId}]`, {
        snapshotSize: `${(projectSnapshot.length / 1024).toFixed(2)} KB`
      });
    } catch (error) {
      logStoryBookEvent('WARNING', `创建项目快照失败 [${createId}]`, { error: error.message || error });
    }

    // 创建新项目
    const startTime = Date.now();
    Object.assign(storyProject, createNewStorybookProject());
    const endTime = Date.now();

    // 重置当前步骤
    currentStep.value = 1;

    logStoryBookEvent('INFO', `已创建新项目 [${createId}]`, {
      creationTime: `${endTime - startTime}ms`,
      newTitle: storyProject.title,
      newStep: currentStep.value
    });

    // 保存到本地存储
    saveStorybookProject(storyProject);

    window.$message?.success('已创建新项目');
  } else {
    logStoryBookEvent('INFO', `用户取消创建新项目 [${createId}]`);
  }
};

// 移动端抽屉控制
const toggleMobileDrawer = () => {
  showMobileDrawer.value = !showMobileDrawer.value;
};

onMounted(async () => {
  // 初始化逻辑
  logStoryBookEvent('INFO', '初始化绘本创作应用', {
    hasExistingProject: !!getStorybookProject(),
    currentStep: currentStep.value,
    activeTab: activeTab.value
  });

  // 确保有一个活动的聊天组，用于轮询图像生成状态
  if (!chatStore.active) {
    try {
      // 创建一个新的聊天组用于绘本创作
      await chatStore.addNewChatGroup();
      logStoryBookEvent('INFO', '已创建新的聊天组用于绘本创作', {
        groupId: chatStore.active
      });
    } catch (error) {
      logStoryBookEvent('ERROR', '创建聊天组失败', {
        error: error.message || error
      });
    }
  }

  // 提供当前步骤和项目数据给子组件
  provide('currentStep', currentStep);
  provide('projectData', storyProject);

  // 启动自动保存定时器
  startAutoSaveTimer();

  // 监听页面刷新或关闭事件，确保数据被保存
  window.addEventListener('beforeunload', (event) => {
    // 保存到本地存储
    saveStorybookProject(storyProject);
    saveCurrentStep(currentStep.value);
    saveActiveTab(activeTab.value);

    // 为了确保数据保存，可以提示用户等待
    // 注意：现代浏览器可能会忽略这个提示，但仍然可以尝试
    event.preventDefault();
    event.returnValue = '您的绘本项目正在保存，确定要离开吗？';
    return event.returnValue;
  });
});
</script>

<template>
  <div class="storybook-studio">
    <!-- 主工作区 -->
    <div class="studio-workspace">
      <!-- 工作区内容 -->
      <div class="workspace-content">
        <component
          :is="tools.find(t => t.id === activeTab)?.component"
          :project-data="storyProject"
          :current-step="currentStep"
          :key="activeTab"
          @update:current-step="(step) => currentStep = step"
        />
      </div>
    </div>

    <!-- 移动端工具抽屉 -->
    <NDrawer v-if="isMobile" v-model:show="showMobileDrawer" placement="left" :width="280">
      <NDrawerContent title="创作工具" closable>
        <div class="mobile-tools">
          <div
            class="mobile-tool-item active"
          >
            <span class="emoji-icon">✏️</span>
            <span>绘本创作</span>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.storybook-studio {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
  overflow: hidden;
}

.dark .storybook-studio {
  background-color: #1a1a1a;
}

.studio-workspace {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.workspace-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.mobile-tools {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-tool-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-tool-item:hover {
  background-color: #f1f5f9;
}

.mobile-tool-item.active {
  background-color: #eff6ff;
  color: #3b82f6;
}

.dark .mobile-tool-item:hover {
  background-color: #333;
}

.dark .mobile-tool-item.active {
  background-color: #1e293b;
  color: #60a5fa;
}

/* 拟物风格样式 */

.emoji-icon {
  font-size: 20px;
  line-height: 1;
  margin-right: 6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.mobile-tool-item .emoji-icon {
  margin-right: 12px;
  font-size: 24px;
}



/* 响应式调整 */
@media (max-width: 768px) {
  /* 移除标题栏相关样式 */
}
</style>
