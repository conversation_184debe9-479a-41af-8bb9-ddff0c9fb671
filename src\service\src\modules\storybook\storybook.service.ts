import { Injectable, Logger, NotFoundException, BadRequestException, OnModuleInit, HttpException, HttpStatus, Inject, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between, MoreThanOrEqual, LessThan<PERSON>r<PERSON>qual, IsNull, In } from 'typeorm';
import { BadWordsService } from '../badWords/badWords.service';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import {
  StorybookEntity,
  StorybookPageEntity,
  StorybookCharacterEntity,
  StorybookTemplateEntity,
  StorybookStatisticEntity,
  StorybookPromptEntity,
  StorybookConfigEntity,
  StorybookImageEntity,
  StorybookFolderEntity
} from './entities';
import {
  CreateStorybookDto,
  UpdateStorybookDto,
  CreatePageDto,
  CreateCharacterDto,
  CreateImageDto,
  UpdateImageDto,
  CreateFolderDto,
  UpdateFolderDto,
  QueryFolderDto,
  MoveToFolderDto,
  QueryStorybookDto,
  BatchOperationDto
} from './dto';

@Injectable({ scope: Scope.REQUEST })
export class StorybookService implements OnModuleInit {
  private readonly logger = new Logger(StorybookService.name);

  constructor(
    @InjectRepository(StorybookEntity)
    private readonly storybookRepository: Repository<StorybookEntity>,

    @InjectRepository(StorybookPageEntity)
    private readonly pageRepository: Repository<StorybookPageEntity>,

    @InjectRepository(StorybookCharacterEntity)
    private readonly characterRepository: Repository<StorybookCharacterEntity>,

    @InjectRepository(StorybookTemplateEntity)
    private readonly templateRepository: Repository<StorybookTemplateEntity>,

    @InjectRepository(StorybookStatisticEntity)
    private readonly statisticRepository: Repository<StorybookStatisticEntity>,

    @InjectRepository(StorybookPromptEntity)
    private readonly promptRepository: Repository<StorybookPromptEntity>,

    @InjectRepository(StorybookConfigEntity)
    private readonly configRepository: Repository<StorybookConfigEntity>,

    @InjectRepository(StorybookImageEntity)
    private readonly imageRepository: Repository<StorybookImageEntity>,

    @InjectRepository(StorybookFolderEntity)
    private readonly folderRepository: Repository<StorybookFolderEntity>,

    private readonly badWordsService: BadWordsService,

    @Inject(REQUEST)
    private readonly request: Request,
  ) {}

  // 获取配置
  async getConfig(key: string): Promise<StorybookConfigEntity> {
    return this.configRepository.findOne({ where: { configKey: key } });
  }

  // 获取所有配置
  async getAllConfigs(): Promise<StorybookConfigEntity[]> {
    return this.configRepository.find();
  }

  // 设置配置
  async setConfig(key: string, value: string, description?: string): Promise<StorybookConfigEntity> {
    const existingConfig = await this.configRepository.findOne({ where: { configKey: key } });

    if (existingConfig) {
      existingConfig.configVal = value;
      if (description) {
        existingConfig.description = description;
      }
      return this.configRepository.save(existingConfig);
    } else {
      const newConfig = this.configRepository.create({
        configKey: key,
        configVal: value,
        description: description || `Configuration for ${key}`,
      });
      return this.configRepository.save(newConfig);
    }
  }

  // 初始化默认配置
  async onModuleInit() {
    await this.initDefaultConfigs();
    this.logger.log('Storybook module initialized');
  }

  async initDefaultConfigs(): Promise<void> {
    const defaultConfigs = [
      { key: 'storybook_enabled', value: '1', description: '是否启用绘本创作功能(0:禁用,1:启用)' },
      { key: 'storybook_public_review', value: '1', description: '公开绘本是否需要审核(0:不需要,1:需要)' },
      { key: 'storybook_default_model', value: 'gpt-4o', description: '绘本创作默认使用的AI模型' },
      { key: 'storybook_image_model', value: 'gpt-image-1', description: '绘本创作默认使用的图像生成模型' },
      { key: 'storybook_image_size', value: '1024x1024', description: '绘本创作默认使用的图像尺寸' },
      { key: 'storybook_image_quality', value: 'hd', description: '绘本创作默认使用的图像质量' },
      { key: 'storybook_image_style', value: 'natural', description: '绘本创作默认使用的图像风格' },
      { key: 'storybook_image_fallback_service', value: 'https://image.pollinations.ai/prompt/%s', description: '备用图像生成服务' },
      { key: 'storybook_image_generation_enabled', value: '1', description: '是否启用图像生成功能(0:禁用,1:启用)' },
      { key: 'storybook_max_pages', value: '20', description: '单本绘本最大页数' },
      { key: 'storybook_daily_limit', value: '5', description: '用户每日可创建绘本数量限制' },
      { key: 'storybook_auto_audit', value: '1', description: '是否启用绘本自动审核(0:禁用,1:启用)' },
      { key: 'storybook_sensitive_filter', value: '1', description: '是否启用敏感词过滤(0:禁用,1:启用)' },
    ];

    for (const config of defaultConfigs) {
      const existing = await this.getConfig(config.key);
      if (!existing) {
        await this.setConfig(config.key, config.value, config.description);
        this.logger.log(`Created default config: ${config.key}`);
      }
    }
  }

  // ==================== 绘本作品管理 ====================
  // 作品管理功能已移除

  /* 以下代码已移除
  // 创建绘本
  async createStorybook(userId: number, createStorybookDto: CreateStorybookDto): Promise<StorybookEntity> {
    this.logger.log(`开始创建绘本: userId=${userId}, title="${createStorybookDto.title}", status=${createStorybookDto.status}, source=${createStorybookDto.source}`);

    // 记录请求来源信息
    const requestInfo = {
      ip: this.request.ip,
      userAgent: this.request.headers['user-agent'],
      contentLength: createStorybookDto.content ? createStorybookDto.content.length : 0
    };
    this.logger.log(`请求信息: ${JSON.stringify(requestInfo)}`);

    // 如果是草稿状态，跳过日限制检查
    if (createStorybookDto.status !== 0) {
      this.logger.log(`非草稿状态(${createStorybookDto.status})，执行日限制检查`);

      // 检查用户当日创建的绘本数量
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const dailyLimit = await this.getConfig('storybook_daily_limit');
      const limit = dailyLimit ? parseInt(dailyLimit.configVal) : 5;
      this.logger.log(`日限制配置: ${limit}本/天`);

      const count = await this.storybookRepository.count({
        where: {
          userId,
          status: 1, // 只计算已发布的作品
          createdAt: Between(today, tomorrow)
        }
      });
      this.logger.log(`用户今日已创建绘本数: ${count}/${limit}`);

      if (count >= limit) {
        this.logger.warn(`用户(${userId})超出日限制，拒绝创建`);
        throw new BadRequestException(`您今日已创建${limit}本绘本，请明天再试`);
      }
    } else {
      this.logger.log(`草稿状态(${createStorybookDto.status})，跳过日限制检查`);
    }

    // 如果是草稿状态，跳过敏感词检查
    if (createStorybookDto.status !== 0) {
      this.logger.log(`非草稿状态(${createStorybookDto.status})，执行敏感词检查`);

      // 检查内容安全
      if (createStorybookDto.title) {
        this.logger.log(`检查标题敏感词: "${createStorybookDto.title}"`);
        const titleCheck = await this.checkContent(createStorybookDto.title, userId);
        if (!titleCheck.safe) {
          this.logger.warn(`标题包含敏感内容: "${createStorybookDto.title}"`);
          throw new HttpException('标题包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
        }
      }

      if (createStorybookDto.description) {
        this.logger.log(`检查描述敏感词: "${createStorybookDto.description.substring(0, 30)}..."`);
        const descriptionCheck = await this.checkContent(createStorybookDto.description, userId);
        if (!descriptionCheck.safe) {
          this.logger.warn(`描述包含敏感内容: "${createStorybookDto.description.substring(0, 30)}..."`);
          throw new HttpException('描述包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
        }
      }
    } else {
      this.logger.log(`草稿状态(${createStorybookDto.status})，跳过敏感词检查`);
    }

    // 设置默认值
    if (!createStorybookDto.source) {
      createStorybookDto.source = 'storybook';
      this.logger.log(`设置默认来源: source=${createStorybookDto.source}`);
    }

    if (createStorybookDto.status === undefined) {
      createStorybookDto.status = 0; // 默认为草稿状态
      this.logger.log(`设置默认状态: status=${createStorybookDto.status}`);
    }

    // 创建新绘本
    const storybook = this.storybookRepository.create({
      ...createStorybookDto,
      userId
    });
    this.logger.log(`准备保存绘本: userId=${userId}, title="${storybook.title}", status=${storybook.status}`);

    try {
      const startTime = Date.now();
      const savedStorybook = await this.storybookRepository.save(storybook);
      const endTime = Date.now();

      this.logger.log(`创建绘本成功: ID=${savedStorybook.id}, 标题="${savedStorybook.title}", 状态=${savedStorybook.status}, 耗时=${endTime - startTime}ms`);

      // 记录更多详细信息用于调试
      const debugInfo = {
        id: savedStorybook.id,
        userId: savedStorybook.userId,
        title: savedStorybook.title,
        status: savedStorybook.status,
        isPublic: savedStorybook.isPublic,
        source: savedStorybook.source,
        hasContent: !!savedStorybook.content,
        contentLength: savedStorybook.content ? savedStorybook.content.length : 0,
        createdAt: savedStorybook.createdAt,
        processingTime: endTime - startTime
      };
      this.logger.log(`绘本创建详情: ${JSON.stringify(debugInfo)}`);

      return savedStorybook;
    } catch (error) {
      this.logger.error(`创建绘本失败: ${error.message}`, error.stack);

      // 记录更详细的错误信息
      const errorDetails = {
        userId,
        title: createStorybookDto.title,
        status: createStorybookDto.status,
        errorName: error.name,
        errorMessage: error.message,
        sqlMessage: error.sqlMessage,
        sqlState: error.sqlState,
        sqlErrorCode: error.errno
      };
      this.logger.error(`创建绘本错误详情: ${JSON.stringify(errorDetails)}`);

      throw new HttpException('创建绘本失败，请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 获取绘本列表（用户端）
  async getUserStorybooks(userId: number, options: {
    page?: number;
    limit?: number;
    status?: number;
    keyword?: string;
  } = {}): Promise<{ items: StorybookEntity[]; total: number }> {
    const { page = 1, limit = 10, status, keyword } = options;

    const queryBuilder = this.storybookRepository.createQueryBuilder('storybook')
      .where('storybook.userId = :userId', { userId });

    if (status !== undefined) {
      queryBuilder.andWhere('storybook.status = :status', { status });
    }

    if (keyword) {
      queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy('storybook.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { items, total };
  }

  // 获取公开绘本列表
  async getPublicStorybooks(options: {
    page?: number;
    limit?: number;
    keyword?: string;
  } = {}): Promise<{ items: StorybookEntity[]; total: number }> {
    const { page = 1, limit = 10, keyword } = options;

    const queryBuilder = this.storybookRepository.createQueryBuilder('storybook')
      .where('storybook.isPublic = :isPublic', { isPublic: 1 })
      .andWhere('storybook.status = :status', { status: 1 }); // 已发布状态

    if (keyword) {
      queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy('storybook.isRecommended', 'DESC')
      .addOrderBy('storybook.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { items, total };
  }

  // 获取绘本详情
  async getStorybookDetail(id: number, userId?: number): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id },
      relations: ['pages', 'characters']
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查访问权限
    if (storybook.isPublic !== 1 && storybook.userId !== userId) {
      throw new NotFoundException('绘本不存在或无权访问');
    }

    // 更新浏览次数
    storybook.viewCount += 1;
    await this.storybookRepository.save(storybook);

    return storybook;
  }

  // 更新绘本
  async updateStorybook(id: number, userId: number, updateStorybookDto: UpdateStorybookDto): Promise<StorybookEntity> {
    this.logger.log(`开始更新绘本: id=${id}, userId=${userId}, title="${updateStorybookDto.title}", status=${updateStorybookDto.status}`);

    // 记录请求来源信息
    const requestInfo = {
      ip: this.request.ip,
      userAgent: this.request.headers['user-agent'],
      contentLength: updateStorybookDto.content ? updateStorybookDto.content.length : 0
    };
    this.logger.log(`更新请求信息: ${JSON.stringify(requestInfo)}`);

    const startTime = Date.now();
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      this.logger.warn(`更新绘本失败: 绘本不存在 id=${id}`);
      throw new NotFoundException('绘本不存在');
    }

    // 记录当前绘本状态
    this.logger.log(`当前绘本状态: id=${storybook.id}, title="${storybook.title}", status=${storybook.status}, userId=${storybook.userId}, 请求用户ID=${userId}`);

    // 检查权限
    if (storybook.userId !== userId) {
      this.logger.warn(`更新绘本失败: 无权修改 id=${id}, 绘本所有者=${storybook.userId}, 请求用户=${userId}`);
      throw new BadRequestException('无权修改此绘本');
    }

    // 如果不是草稿状态，检查内容安全
    if (storybook.status !== 0 && updateStorybookDto.status !== 0) {
      this.logger.log(`非草稿状态(当前=${storybook.status}, 更新=${updateStorybookDto.status})，执行敏感词检查`);

      if (updateStorybookDto.title) {
        this.logger.log(`检查标题敏感词: "${updateStorybookDto.title}"`);
        const titleCheck = await this.checkContent(updateStorybookDto.title, userId);
        if (!titleCheck.safe) {
          this.logger.warn(`标题包含敏感内容: "${updateStorybookDto.title}"`);
          throw new HttpException('标题包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
        }
      }

      if (updateStorybookDto.description) {
        this.logger.log(`检查描述敏感词: "${updateStorybookDto.description.substring(0, 30)}..."`);
        const descriptionCheck = await this.checkContent(updateStorybookDto.description, userId);
        if (!descriptionCheck.safe) {
          this.logger.warn(`描述包含敏感内容: "${updateStorybookDto.description.substring(0, 30)}..."`);
          throw new HttpException('描述包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
        }
      }
    } else {
      this.logger.log(`草稿状态(当前=${storybook.status}, 更新=${updateStorybookDto.status})，跳过敏感词检查`);
    }

    // 如果要将绘本设为公开，检查是否需要审核
    if (updateStorybookDto.isPublic === 1 && storybook.isPublic !== 1) {
      this.logger.log(`绘本将设为公开: id=${id}, 当前公开状态=${storybook.isPublic}, 更新公开状态=${updateStorybookDto.isPublic}`);

      const needReview = await this.getConfig('storybook_public_review');
      if (needReview && needReview.configVal === '1') {
        this.logger.log(`需要审核: 配置值=${needReview.configVal}`);

        // 需要审核，设置状态为审核中
        updateStorybookDto.status = 2; // 审核中
        this.logger.log(`设置状态为审核中: status=${updateStorybookDto.status}`);

        // 检查是否启用自动审核
        const autoAuditConfig = await this.getConfig('storybook_auto_audit');
        const autoAudit = autoAuditConfig ? autoAuditConfig.configVal === '1' : false;
        this.logger.log(`自动审核配置: ${autoAudit ? '启用' : '禁用'}`);

        if (autoAudit) {
          this.logger.log(`执行自动审核流程: id=${id}`);

          // 先保存更新，然后进行自动审核
          Object.assign(storybook, updateStorybookDto);
          const updatedStorybook = await this.storybookRepository.save(storybook);
          this.logger.log(`更新绘本成功(自动审核前): ID=${updatedStorybook.id}, 标题="${updatedStorybook.title}", 状态=${updatedStorybook.status}`);

          // 异步执行自动审核，不阻塞当前请求
          this.autoAuditStorybook(updatedStorybook.id, userId).catch(error => {
            this.logger.error(`自动审核失败: ${error.message}`, error.stack);
          });

          const endTime = Date.now();
          this.logger.log(`更新绘本完成(自动审核): ID=${updatedStorybook.id}, 耗时=${endTime - startTime}ms`);

          return updatedStorybook;
        }
      } else {
        this.logger.log(`无需审核: 配置值=${needReview ? needReview.configVal : '未设置'}`);
      }
    }

    // 更新绘本
    try {
      // 记录更新前后的差异
      const changes = {
        title: { from: storybook.title, to: updateStorybookDto.title },
        status: { from: storybook.status, to: updateStorybookDto.status },
        isPublic: { from: storybook.isPublic, to: updateStorybookDto.isPublic },
        hasContentChange: !!updateStorybookDto.content,
        contentLengthChange: updateStorybookDto.content ?
          (storybook.content ? updateStorybookDto.content.length - storybook.content.length : updateStorybookDto.content.length) : 0
      };
      this.logger.log(`更新内容差异: ${JSON.stringify(changes)}`);

      Object.assign(storybook, updateStorybookDto);

      const saveStartTime = Date.now();
      const updatedStorybook = await this.storybookRepository.save(storybook);
      const saveEndTime = Date.now();

      this.logger.log(`更新绘本成功: ID=${updatedStorybook.id}, 标题="${updatedStorybook.title}", 状态=${updatedStorybook.status}, 保存耗时=${saveEndTime - saveStartTime}ms, 总耗时=${saveEndTime - startTime}ms`);

      // 记录更多详细信息用于调试
      const debugInfo = {
        id: updatedStorybook.id,
        userId: updatedStorybook.userId,
        title: updatedStorybook.title,
        status: updatedStorybook.status,
        isPublic: updatedStorybook.isPublic,
        source: updatedStorybook.source,
        hasContent: !!updatedStorybook.content,
        contentLength: updatedStorybook.content ? updatedStorybook.content.length : 0,
        updatedAt: updatedStorybook.updatedAt,
        processingTime: saveEndTime - startTime
      };
      this.logger.log(`绘本更新详情: ${JSON.stringify(debugInfo)}`);

      return updatedStorybook;
    } catch (error) {
      this.logger.error(`更新绘本失败: ${error.message}`, error.stack);

      // 记录更详细的错误信息
      const errorDetails = {
        id,
        userId,
        title: updateStorybookDto.title,
        status: updateStorybookDto.status,
        errorName: error.name,
        errorMessage: error.message,
        sqlMessage: error.sqlMessage,
        sqlState: error.sqlState,
        sqlErrorCode: error.errno
      };
      this.logger.error(`更新绘本错误详情: ${JSON.stringify(errorDetails)}`);

      throw new HttpException('更新绘本失败，请稍后重试', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // 删除绘本
  async deleteStorybook(id: number, userId: number): Promise<void> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查权限
    if (storybook.userId !== userId) {
      throw new BadRequestException('无权删除此绘本');
    }

    await this.storybookRepository.remove(storybook);
  }
  */

  // ==================== 管理员功能 ====================

  // 获取所有绘本（管理端）
  async getAllStorybooks(options: {
    page?: number;
    limit?: number;
    status?: number;
    userId?: number;
    keyword?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}): Promise<{ items: StorybookEntity[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      status,
      userId,
      keyword,
      startDate,
      endDate
    } = options;

    const queryBuilder = this.storybookRepository.createQueryBuilder('storybook');

    if (status !== undefined) {
      queryBuilder.andWhere('storybook.status = :status', { status });
    }

    if (userId) {
      queryBuilder.andWhere('storybook.userId = :userId', { userId });
    }

    if (keyword) {
      queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    if (startDate) {
      queryBuilder.andWhere('storybook.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('storybook.createdAt <= :endDate', { endDate });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy('storybook.updatedAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { items, total };
  }

  // 审核绘本
  async reviewStorybook(id: number, status: number): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 只能审核状态为"审核中"的绘本
    if (storybook.status !== 2) {
      throw new BadRequestException('只能审核状态为"审核中"的绘本');
    }

    // 更新状态
    storybook.status = status;
    return this.storybookRepository.save(storybook);
  }

  // 设置推荐状态
  async setRecommendStatus(id: number, isRecommended: number): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 只能推荐已发布且公开的绘本
    if (storybook.status !== 1 || storybook.isPublic !== 1) {
      throw new BadRequestException('只能推荐已发布且公开的绘本');
    }

    storybook.isRecommended = isRecommended;
    return this.storybookRepository.save(storybook);
  }

  // ==================== 绘本页面管理 ====================

  // 创建页面
  async createPage(createPageDto: CreatePageDto): Promise<StorybookPageEntity> {
    // 检查绘本是否存在
    const storybook = await this.storybookRepository.findOne({
      where: { id: createPageDto.storybookId }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查页数是否超过限制
    const maxPagesConfig = await this.getConfig('storybook_max_pages');
    const maxPages = maxPagesConfig ? parseInt(maxPagesConfig.configVal) : 20;

    const pageCount = await this.pageRepository.count({
      where: { storybookId: createPageDto.storybookId }
    });

    if (pageCount >= maxPages) {
      throw new BadRequestException(`页数已达上限(${maxPages}页)`);
    }

    // 检查内容安全
    if (createPageDto.text) {
      const textCheck = await this.checkContent(createPageDto.text, storybook.userId);
      if (!textCheck.safe) {
        throw new HttpException('页面文本包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
      }
    }

    if (createPageDto.imageDescription) {
      const descriptionCheck = await this.checkContent(createPageDto.imageDescription, storybook.userId);
      if (!descriptionCheck.safe) {
        throw new HttpException('图片描述包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
      }
    }

    // 创建新页面
    const page = this.pageRepository.create(createPageDto);
    return this.pageRepository.save(page);
  }

  // 更新页面
  async updatePage(id: number, updateData: Partial<StorybookPageEntity>): Promise<StorybookPageEntity> {
    const page = await this.pageRepository.findOne({
      where: { id },
      relations: ['storybook']
    });

    if (!page) {
      throw new NotFoundException('页面不存在');
    }

    // 检查内容安全
    if (updateData.text) {
      const textCheck = await this.checkContent(updateData.text, page.storybook.userId);
      if (!textCheck.safe) {
        throw new HttpException('页面文本包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
      }
    }

    if (updateData.imageDescription) {
      const descriptionCheck = await this.checkContent(updateData.imageDescription, page.storybook.userId);
      if (!descriptionCheck.safe) {
        throw new HttpException('图片描述包含敏感内容，请修改后重试', HttpStatus.BAD_REQUEST);
      }
    }

    // 更新页面
    Object.assign(page, updateData);
    return this.pageRepository.save(page);
  }

  // 删除页面
  async deletePage(id: number): Promise<void> {
    const page = await this.pageRepository.findOne({
      where: { id }
    });

    if (!page) {
      throw new NotFoundException('页面不存在');
    }

    await this.pageRepository.remove(page);
  }

  // 获取绘本的所有页面
  async getStorybookPages(storybookId: number): Promise<StorybookPageEntity[]> {
    return this.pageRepository.find({
      where: { storybookId },
      order: { pageNumber: 'ASC' }
    });
  }

  // ==================== 绘本角色管理 ====================

  // 创建角色
  async createCharacter(createCharacterDto: CreateCharacterDto): Promise<StorybookCharacterEntity> {
    // 如果有绘本ID，检查绘本是否存在
    if (createCharacterDto.storybookId) {
      const storybook = await this.storybookRepository.findOne({
        where: { id: createCharacterDto.storybookId }
      });

      if (!storybook) {
        throw new NotFoundException('绘本不存在');
      }
    }

    // 从请求上下文中获取用户ID
    const userId = this.request?.user?.id;

    // 如果有用户ID，设置到角色中
    if (userId) {
      createCharacterDto['userId'] = userId;
    }

    // 确保标签是数组
    if (createCharacterDto.tags && !Array.isArray(createCharacterDto.tags)) {
      try {
        createCharacterDto.tags = JSON.parse(createCharacterDto.tags as any);
      } catch (e) {
        createCharacterDto.tags = [];
      }
    }

    // 创建新角色
    const character = this.characterRepository.create(createCharacterDto);
    return this.characterRepository.save(character);
  }

  // 更新角色
  async updateCharacter(id: number, updateData: Partial<StorybookCharacterEntity>): Promise<StorybookCharacterEntity> {
    const character = await this.characterRepository.findOne({
      where: { id }
    });

    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    // 更新角色
    Object.assign(character, updateData);
    return this.characterRepository.save(character);
  }

  // 删除角色
  async deleteCharacter(id: number): Promise<void> {
    const character = await this.characterRepository.findOne({
      where: { id }
    });

    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    await this.characterRepository.remove(character);
  }

  // 获取绘本的所有角色
  async getStorybookCharacters(storybookId: number): Promise<StorybookCharacterEntity[]> {
    return this.characterRepository.find({
      where: { storybookId }
    });
  }

  // 获取角色模板
  async getCharacterTemplates(): Promise<StorybookCharacterEntity[]> {
    return this.characterRepository.find({
      where: { isTemplate: 1, storybookId: IsNull() }
    });
  }

  // 获取用户的角色库
  async getUserCharacters(userId: number): Promise<StorybookCharacterEntity[]> {
    return this.characterRepository.find({
      where: [
        { userId, storybookId: IsNull() }, // 独立角色（不属于任何绘本）
        { userId, isTemplate: 0 } // 用户创建的非模板角色
      ],
      order: { updatedAt: 'DESC' }
    });
  }

  // ==================== 管理员功能 - 配置管理 ====================

  // ==================== 管理员功能 - 提示词管理 ====================

  // 获取所有提示词
  async getAllPrompts(): Promise<StorybookPromptEntity[]> {
    return this.promptRepository.find({
      order: { order: 'DESC' },
    });
  }

  // 创建提示词
  async createPrompt(data: Partial<StorybookPromptEntity>): Promise<StorybookPromptEntity> {
    const prompt = this.promptRepository.create(data);
    return this.promptRepository.save(prompt);
  }

  // 更新提示词
  async updatePrompt(id: number, data: Partial<StorybookPromptEntity>): Promise<StorybookPromptEntity | { success: boolean; message: string }> {
    const prompt = await this.promptRepository.findOne({
      where: { id },
    });
    if (!prompt) {
      return { success: false, message: '提示词不存在' };
    }
    Object.assign(prompt, data);
    return this.promptRepository.save(prompt);
  }

  // 删除提示词
  async deletePrompt(id: number): Promise<{ success: boolean; message: string }> {
    const prompt = await this.promptRepository.findOne({
      where: { id },
    });
    if (!prompt) {
      return { success: false, message: '提示词不存在' };
    }
    await this.promptRepository.remove(prompt);
    return { success: true, message: '删除成功' };
  }

  // ==================== 管理员功能 - 模板管理 ====================

  // 获取所有模板
  async getAllTemplates(): Promise<StorybookTemplateEntity[]> {
    return this.templateRepository.find({
      order: { order: 'DESC' },
    });
  }

  // 创建模板
  async createTemplate(data: Partial<StorybookTemplateEntity>): Promise<StorybookTemplateEntity> {
    const template = this.templateRepository.create(data);
    return this.templateRepository.save(template);
  }

  // 更新模板
  async updateTemplate(id: number, data: Partial<StorybookTemplateEntity>): Promise<StorybookTemplateEntity | { success: boolean; message: string }> {
    const template = await this.templateRepository.findOne({
      where: { id },
    });
    if (!template) {
      return { success: false, message: '模板不存在' };
    }
    Object.assign(template, data);
    return this.templateRepository.save(template);
  }

  // 删除模板
  async deleteTemplate(id: number): Promise<{ success: boolean; message: string }> {
    const template = await this.templateRepository.findOne({
      where: { id },
    });
    if (!template) {
      return { success: false, message: '模板不存在' };
    }
    await this.templateRepository.remove(template);
    return { success: true, message: '删除成功' };
  }

  // ==================== 管理员功能 - 统计数据 ====================

  // 获取统计数据
  async getStatistics(startDate?: string, endDate?: string): Promise<any> {
    // 解析日期
    const start = startDate ? new Date(startDate) : new Date();
    start.setDate(start.getDate() - 30); // 默认查询30天
    const end = endDate ? new Date(endDate) : new Date();

    // 获取统计数据
    const totalStorybooks = await this.storybookRepository.count();
    const publicStorybooks = await this.storybookRepository.count({
      where: { isPublic: 1, status: 1 },
    });
    const pendingReviewStorybooks = await this.storybookRepository.count({
      where: { status: 2 },
    });
    const recommendedStorybooks = await this.storybookRepository.count({
      where: { isRecommended: 1 },
    });

    // 获取时间段内的新增绘本数
    const newStorybooks = await this.storybookRepository.count({
      where: {
        createdAt: Between(start, end),
      },
    });

    // 获取总浏览量
    const totalViews = await this.storybookRepository
      .createQueryBuilder('storybook')
      .select('SUM(storybook.viewCount)', 'totalViews')
      .getRawOne();

    // 获取总点赞量
    const totalLikes = await this.storybookRepository
      .createQueryBuilder('storybook')
      .select('SUM(storybook.likeCount)', 'totalLikes')
      .getRawOne();

    // 获取图像统计
    const totalImages = await this.imageRepository.count();
    const pendingAuditImages = await this.imageRepository.count({
      where: { auditStatus: 0 },
    });
    const rejectedImages = await this.imageRepository.count({
      where: { auditStatus: 2 },
    });

    return {
      totalStorybooks,
      publicStorybooks,
      pendingReviewStorybooks,
      recommendedStorybooks,
      newStorybooks,
      totalViews: totalViews.totalViews || 0,
      totalLikes: totalLikes.totalLikes || 0,
      totalImages,
      pendingAuditImages,
      rejectedImages,
    };
  }

  // ==================== AI绘图资源管理 ====================

  // 创建图像记录
  async createImage(userId: number, createImageDto: CreateImageDto): Promise<StorybookImageEntity> {
    // 创建新图像记录
    const image = this.imageRepository.create({
      ...createImageDto,
      userId
    });

    return this.imageRepository.save(image);
  }

  // 获取图像列表
  async getImages(options: {
    page?: number;
    limit?: number;
    userId?: number;
    imageType?: number;
    auditStatus?: number;
    storybookId?: number;
    pageId?: number;
    characterId?: number;
    keyword?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}): Promise<{ items: StorybookImageEntity[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      userId,
      imageType,
      auditStatus,
      storybookId,
      pageId,
      characterId,
      keyword,
      startDate,
      endDate
    } = options;

    const queryBuilder = this.imageRepository.createQueryBuilder('image');

    if (userId) {
      queryBuilder.andWhere('image.userId = :userId', { userId });
    }

    if (imageType !== undefined) {
      queryBuilder.andWhere('image.imageType = :imageType', { imageType });
    }

    if (auditStatus !== undefined) {
      queryBuilder.andWhere('image.auditStatus = :auditStatus', { auditStatus });
    }

    if (storybookId) {
      queryBuilder.andWhere('image.storybookId = :storybookId', { storybookId });
    }

    if (pageId) {
      queryBuilder.andWhere('image.pageId = :pageId', { pageId });
    }

    if (characterId) {
      queryBuilder.andWhere('image.characterId = :characterId', { characterId });
    }

    if (keyword) {
      queryBuilder.andWhere('(image.description LIKE :keyword OR image.prompt LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    if (startDate) {
      queryBuilder.andWhere('image.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('image.createdAt <= :endDate', { endDate });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy('image.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { items, total };
  }

  // 获取图像详情
  async getImageDetail(id: number): Promise<StorybookImageEntity> {
    const image = await this.imageRepository.findOne({
      where: { id },
      relations: ['storybook', 'page', 'character']
    });

    if (!image) {
      throw new NotFoundException('图像不存在');
    }

    return image;
  }

  // 更新图像
  async updateImage(id: number, updateImageDto: UpdateImageDto): Promise<StorybookImageEntity> {
    const image = await this.imageRepository.findOne({
      where: { id }
    });

    if (!image) {
      throw new NotFoundException('图像不存在');
    }

    // 更新图像
    Object.assign(image, updateImageDto);
    return this.imageRepository.save(image);
  }

  // 删除图像
  async deleteImage(id: number): Promise<void> {
    const image = await this.imageRepository.findOne({
      where: { id }
    });

    if (!image) {
      throw new NotFoundException('图像不存在');
    }

    await this.imageRepository.remove(image);
  }

  // 审核图像
  async auditImage(id: number, auditStatus: number, auditRemark?: string): Promise<StorybookImageEntity> {
    const image = await this.imageRepository.findOne({
      where: { id }
    });

    if (!image) {
      throw new NotFoundException('图像不存在');
    }

    // 更新审核状态
    image.auditStatus = auditStatus;
    if (auditRemark) {
      image.auditRemark = auditRemark;
    }

    return this.imageRepository.save(image);
  }

  // 设置图像质量评级
  async setImageQuality(id: number, qualityRating: number): Promise<StorybookImageEntity> {
    const image = await this.imageRepository.findOne({
      where: { id }
    });

    if (!image) {
      throw new NotFoundException('图像不存在');
    }

    // 检查评级范围
    if (qualityRating < 1 || qualityRating > 5) {
      throw new BadRequestException('质量评级必须在1-5之间');
    }

    // 更新质量评级
    image.qualityRating = qualityRating;
    return this.imageRepository.save(image);
  }

  // 获取图像生成配置
  async getImageGenerationConfig(): Promise<any> {
    const modelConfig = await this.getConfig('storybook_image_model');
    const model = modelConfig ? modelConfig.configVal : 'gpt-image-1';

    const sizeConfig = await this.getConfig('storybook_image_size');
    const size = sizeConfig ? sizeConfig.configVal : '1024x1024';

    const qualityConfig = await this.getConfig('storybook_image_quality');
    const quality = qualityConfig ? qualityConfig.configVal : 'hd';

    const styleConfig = await this.getConfig('storybook_image_style');
    const style = styleConfig ? styleConfig.configVal : 'natural';

    const fallbackServiceConfig = await this.getConfig('storybook_image_fallback_service');
    const fallbackService = fallbackServiceConfig ? fallbackServiceConfig.configVal : 'https://image.pollinations.ai/prompt/%s';

    const enabledConfig = await this.getConfig('storybook_image_generation_enabled');
    const enabled = enabledConfig ? enabledConfig.configVal === '1' : true;

    return {
      model,
      size,
      quality,
      style,
      fallbackService,
      enabled
    };
  }

  // 更新图像生成配置
  async updateImageGenerationConfig(config: {
    model?: string;
    size?: string;
    quality?: string;
    style?: string;
    fallbackService?: string;
    enabled?: boolean;
  }): Promise<any> {
    const { model, size, quality, style, fallbackService, enabled } = config;

    if (model) {
      await this.setConfig('storybook_image_model', model, '绘本创作默认使用的图像生成模型');
    }

    if (size) {
      await this.setConfig('storybook_image_size', size, '绘本创作默认使用的图像尺寸');
    }

    if (quality) {
      await this.setConfig('storybook_image_quality', quality, '绘本创作默认使用的图像质量');
    }

    if (style) {
      await this.setConfig('storybook_image_style', style, '绘本创作默认使用的图像风格');
    }

    if (fallbackService) {
      await this.setConfig('storybook_image_fallback_service', fallbackService, '备用图像生成服务');
    }

    if (enabled !== undefined) {
      await this.setConfig('storybook_image_generation_enabled', enabled ? '1' : '0', '是否启用图像生成功能(0:禁用,1:启用)');
    }

    return this.getImageGenerationConfig();
  }

  // 获取图像资源使用统计
  async getImageUsageStats(startDate?: string, endDate?: string): Promise<any> {
    // 解析日期
    const start = startDate ? new Date(startDate) : new Date();
    start.setDate(start.getDate() - 30); // 默认查询30天
    const end = endDate ? new Date(endDate) : new Date();

    // 获取时间段内的图像生成数量
    const totalImages = await this.imageRepository.count({
      where: {
        createdAt: Between(start, end),
      },
    });

    // 按类型统计
    const characterImages = await this.imageRepository.count({
      where: {
        imageType: 1,
        createdAt: Between(start, end),
      },
    });

    const pageImages = await this.imageRepository.count({
      where: {
        imageType: 2,
        createdAt: Between(start, end),
      },
    });

    const coverImages = await this.imageRepository.count({
      where: {
        imageType: 3,
        createdAt: Between(start, end),
      },
    });

    // 按模型统计
    const modelStats = await this.imageRepository
      .createQueryBuilder('image')
      .select('image.model', 'model')
      .addSelect('COUNT(image.id)', 'count')
      .where('image.createdAt BETWEEN :start AND :end', { start, end })
      .groupBy('image.model')
      .getRawMany();

    return {
      totalImages,
      characterImages,
      pageImages,
      coverImages,
      modelStats
    };
  }

  // ==================== 内容安全管理 ====================

  // 检查内容是否包含敏感词
  async checkContent(content: string, userId: number): Promise<{ safe: boolean; triggeredWords: string[] }> {
    try {
      // 使用现有的敏感词系统检查内容
      const triggeredWords = await this.badWordsService.checkBadWords(content, userId);

      // 如果没有触发敏感词，返回安全
      return {
        safe: triggeredWords.length === 0,
        triggeredWords
      };
    } catch (error) {
      // 如果敏感词检查抛出异常，说明内容不安全
      this.logger.error(`内容安全检查失败: ${error.message}`, error.stack);
      return {
        safe: false,
        triggeredWords: ['违规内容']
      };
    }
  }

  // 审核绘本内容
  async auditStorybookContent(id: number, userId: number): Promise<{ safe: boolean; issues: any[] }> {
    const storybook = await this.storybookRepository.findOne({
      where: { id },
      relations: ['pages']
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    const issues = [];
    let safe = true;

    // 检查标题
    const titleCheck = await this.checkContent(storybook.title, userId);
    if (!titleCheck.safe) {
      safe = false;
      issues.push({
        type: 'title',
        content: storybook.title,
        triggeredWords: titleCheck.triggeredWords
      });
    }

    // 检查描述
    if (storybook.description) {
      const descriptionCheck = await this.checkContent(storybook.description, userId);
      if (!descriptionCheck.safe) {
        safe = false;
        issues.push({
          type: 'description',
          content: storybook.description,
          triggeredWords: descriptionCheck.triggeredWords
        });
      }
    }

    // 检查页面内容
    if (storybook.pages && storybook.pages.length > 0) {
      for (const page of storybook.pages) {
        if (page.text) {
          const textCheck = await this.checkContent(page.text, userId);
          if (!textCheck.safe) {
            safe = false;
            issues.push({
              type: 'page',
              pageId: page.id,
              pageNumber: page.pageNumber,
              content: page.text,
              triggeredWords: textCheck.triggeredWords
            });
          }
        }

        if (page.imageDescription) {
          const descriptionCheck = await this.checkContent(page.imageDescription, userId);
          if (!descriptionCheck.safe) {
            safe = false;
            issues.push({
              type: 'pageImageDescription',
              pageId: page.id,
              pageNumber: page.pageNumber,
              content: page.imageDescription,
              triggeredWords: descriptionCheck.triggeredWords
            });
          }
        }
      }
    }

    return { safe, issues };
  }

  // 自动审核绘本
  async autoAuditStorybook(id: number, userId: number): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查权限
    if (storybook.userId !== userId) {
      throw new BadRequestException('无权操作此绘本');
    }

    // 只有处于审核中状态的绘本才能自动审核
    if (storybook.status !== 2) {
      throw new BadRequestException('只能审核状态为"审核中"的绘本');
    }

    // 进行内容安全检查
    const auditResult = await this.auditStorybookContent(id, userId);

    // 根据检查结果更新绘本状态
    if (auditResult.safe) {
      // 内容安全，通过审核
      storybook.status = 1; // 已发布
    } else {
      // 内容不安全，拒绝审核
      storybook.status = 3; // 已拒绝

      // 记录审核失败原因
      const reasons = auditResult.issues.map(issue =>
        `${issue.type === 'title' ? '标题' :
          issue.type === 'description' ? '描述' :
          issue.type === 'page' ? `第${issue.pageNumber}页文本` :
          `第${issue.pageNumber}页图片描述`} 包含敏感词: ${issue.triggeredWords.join(', ')}`
      ).join('; ');

      // 更新绘本描述，添加审核失败原因
      storybook.description = `${storybook.description || ''}\n\n审核失败原因: ${reasons}`;
    }

    return this.storybookRepository.save(storybook);
  }

  // 获取内容安全配置
  async getContentSafetyConfig(): Promise<any> {
    const autoAuditConfig = await this.getConfig('storybook_auto_audit');
    const autoAudit = autoAuditConfig ? autoAuditConfig.configVal === '1' : false;

    const sensitiveFilterConfig = await this.getConfig('storybook_sensitive_filter');
    const sensitiveFilter = sensitiveFilterConfig ? sensitiveFilterConfig.configVal === '1' : true;

    return {
      autoAudit,
      sensitiveFilter
    };
  }

  // 更新内容安全配置
  async updateContentSafetyConfig(config: { autoAudit?: boolean; sensitiveFilter?: boolean }): Promise<any> {
    const { autoAudit, sensitiveFilter } = config;

    if (autoAudit !== undefined) {
      await this.setConfig(
        'storybook_auto_audit',
        autoAudit ? '1' : '0',
        '是否启用绘本自动审核(0:禁用,1:启用)'
      );
    }

    if (sensitiveFilter !== undefined) {
      await this.setConfig(
        'storybook_sensitive_filter',
        sensitiveFilter ? '1' : '0',
        '是否启用敏感词过滤(0:禁用,1:启用)'
      );
    }

    return this.getContentSafetyConfig();
  }

  // 过滤内容中的敏感词
  async filterSensitiveContent(content: string, userId: number): Promise<string> {
    try {
      // 检查是否启用敏感词过滤
      const sensitiveFilterConfig = await this.getConfig('storybook_sensitive_filter');
      const sensitiveFilter = sensitiveFilterConfig ? sensitiveFilterConfig.configVal === '1' : true;

      if (!sensitiveFilter) {
        return content;
      }

      // 使用现有的敏感词系统检查内容
      const triggeredWords = await this.badWordsService.checkBadWords(content, userId);

      if (triggeredWords.length === 0) {
        return content;
      }

      // 构造正则表达式替换敏感词
      let filteredContent = content;
      for (const word of triggeredWords) {
        const regex = new RegExp(word, 'g');
        filteredContent = filteredContent.replace(regex, '*'.repeat(word.length));
      }

      return filteredContent;
    } catch (error) {
      // 如果敏感词检查抛出异常，返回原内容
      this.logger.error(`敏感词过滤失败: ${error.message}`, error.stack);
      return content;
    }
  }

  // 获取违规内容统计
  async getViolationStats(startDate?: string, endDate?: string): Promise<any> {
    // 这里需要调用BadWordsService的统计方法，但由于我们没有直接访问其内部实现
    // 我们可以提供一个简单的绘本相关的违规统计

    // 解析日期
    const start = startDate ? new Date(startDate) : new Date();
    start.setDate(start.getDate() - 30); // 默认查询30天
    const end = endDate ? new Date(endDate) : new Date();

    // 获取被拒绝的绘本数量
    const rejectedStorybooks = await this.storybookRepository.count({
      where: {
        status: 3, // 已拒绝
        updatedAt: Between(start, end)
      }
    });

    // 获取审核中的绘本数量
    const pendingStorybooks = await this.storybookRepository.count({
      where: {
        status: 2, // 审核中
        updatedAt: Between(start, end)
      }
    });

    // 获取被拒绝的图像数量
    const rejectedImages = await this.imageRepository.count({
      where: {
        auditStatus: 2, // 已拒绝
        updatedAt: Between(start, end)
      }
    });

    return {
      rejectedStorybooks,
      pendingStorybooks,
      rejectedImages,
      period: {
        start,
        end
      }
    };
  }
}
