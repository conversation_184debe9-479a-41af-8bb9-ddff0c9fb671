#!/bin/bash

# 错误处理函数
handle_error() {
  echo "\n\033[31m错误: 命令执行失败，错误代码: $?\033[0m"
  echo "\033[33m请查看上面的错误信息\033[0m"
  echo -e "按任意键继续..."
  read -n 1
  exit 1
}

# 捕获错误但不立即退出
trap 'handle_error' ERR


# 执行命令并检查结果
exec_cmd() {
  echo "\033[34m执行: $1\033[0m"
  eval "$1"
  local status=$?
  if [ $status -ne 0 ]; then
    echo "\033[31m命令 '$1' 执行失败，错误代码: $status\033[0m"
    return $status
  fi
  return 0
}

# 1. 构建所有项目
echo "\033[32m开始构建项目...\033[0m"
exec_cmd "pnpm install"

# 构建管理端
echo "\033[32m构建管理端...\033[0m"
exec_cmd "cd admin/ && pnpm build && cd .."

# 构建前端
echo "\033[32m构建前端...\033[0m"
exec_cmd "cd chat/ && pnpm build && cd .."

# 构建服务端
echo "\033[32m构建服务端...\033[0m"
exec_cmd "cd service/ && pnpm build && cd .."

# 2. 清理并创建目标目录
echo "\033[32m准备目标目录...\033[0m"

# 检查目标目录是否存在
if [ ! -d "../DeepCreateDeploy" ]; then
  echo "\033[31m错误: ../DeepCreateDeploy 目录不存在\033[0m"
  echo "\033[33m正在创建目录...\033[0m"
  exec_cmd "mkdir -p ../DeepCreateDeploy"
fi

exec_cmd "rm -rf ../DeepCreateDeploy/dist/* ../DeepCreateDeploy/public/admin/* ../DeepCreateDeploy/public/chat/*"
exec_cmd "mkdir -p ../DeepCreateDeploy/dist ../DeepCreateDeploy/public/admin ../DeepCreateDeploy/public/chat"

# 3. 复制服务端配置文件
echo "\033[32m复制服务端配置...\033[0m"
exec_cmd "cp service/pm2.conf.json ../DeepCreateDeploy/pm2.conf.json"
exec_cmd "cp service/package.json ../DeepCreateDeploy/package.json"

# 4. 复制配置文件
echo "\033[32m复制配置文件...\033[0m"
exec_cmd "cp service/.env.example ../DeepCreateDeploy/.env.example"
exec_cmd "cp service/.env.docker ../DeepCreateDeploy/.env.docker"
exec_cmd "cp service/.dockerignore ../DeepCreateDeploy/.dockerignore"
exec_cmd "cp service/Dockerfile ../DeepCreateDeploy/Dockerfile"
exec_cmd "cp service/docker-compose.yml ../DeepCreateDeploy/docker-compose.yml"

# 5. 复制构建产物
echo "\033[32m复制构建文件...\033[0m"

# 检查构建产物是否存在
if [ ! -d "service/dist" ]; then
  echo "\033[31m错误: service/dist 目录不存在，服务端构建可能失败\033[0m"
else
  exec_cmd "cp -r service/dist/* ../DeepCreateDeploy/dist"
fi

if [ ! -d "admin/dist" ]; then
  echo "\033[31m错误: admin/dist 目录不存在，管理端构建可能失败\033[0m"
else
  exec_cmd "cp -r admin/dist/* ../DeepCreateDeploy/public/admin"
fi

if [ ! -d "chat/dist" ]; then
  echo "\033[31m错误: chat/dist 目录不存在，前端构建可能失败\033[0m"
else
  exec_cmd "cp -r chat/dist/* ../DeepCreateDeploy/public/chat"
fi

# 显示打包结果汇总
echo "\n\033[32m=== 打包结果汇总 ===\033[0m"

# 检查各个构建产物是否存在
if [ ! -d "service/dist" ] || [ ! -d "admin/dist" ] || [ ! -d "chat/dist" ]; then
  echo "\033[33m警告: 部分构建产物不存在，打包可能不完整\033[0m"
else
  echo "\033[32m所有构建产物已成功生成\033[0m"
fi

if [ -d "../DeepCreateDeploy" ]; then
  echo "\033[32m所有文件已成功复制到 ../DeepCreateDeploy 目录\033[0m"
fi

echo "\n\033[32m打包完成\033[0m"
echo -e "按回车键退出..."
read
