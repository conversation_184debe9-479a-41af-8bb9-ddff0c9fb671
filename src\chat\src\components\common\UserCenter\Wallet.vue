<script setup lang="ts">
import { fetchGetRechargeLogAPI } from '@/api/balance';
import { fetchGetPackageAPI, fetchUseCramiAPI } from '@/api/crami';
import type { ResData } from '@/api/types';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { useAuthStore } from '@/store';
import {
  NButton,
  NCard,
  NDataTable,
  NDrawer,
  NDrawerContent,
  NGi,
  NGrid,
  NGridItem,
  NInput,
  NSpace,
  useMessage,
} from 'naive-ui';
import { computed, h, onMounted, ref } from 'vue';

const authStore = useAuthStore();
const ms = useMessage();
const { isMobile, isSmallMd } = useBasicLayout();

const userBalance = computed(() => authStore.userBalance);
const model3Name = computed(
  () => authStore.globalConfig.model3Name || t('goods.basicModelQuota')
);
const model4Name =
  computed(() => authStore.globalConfig.model4Name) ||
  t('goods.advancedModelQuota');
const drawMjName =
  computed(() => authStore.globalConfig.drawMjName) ||
  t('goods.drawingQuota');
const isHideModel3Point = computed(
  () => Number(authStore.globalConfig.isHideModel3Point) === 1
);
const isHideModel4Point = computed(
  () => Number(authStore.globalConfig.isHideModel4Point) === 1
);
const isHideDrawMjPoint = computed(
  () => Number(authStore.globalConfig.isHideDrawMjPoint) === 1
);

const loading = ref(false);
const rechargeLoading = ref(false);
const code = ref('');
const showDrawer = ref(false);
const packageList = ref([]);

const paginationReg = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageSizes: [10, 20, 30, 40],
  showSizePicker: true,
  prefix({ itemCount }: any) {
    return `共 ${itemCount} 条`;
  },
});

const columns = computed(() => {
  const cols: any[] = [
    {
      title: '充值时间',
      key: 'createTime',
    },
    {
      title: '充值类型',
      key: 'type',
      render(row: any) {
        return h('span', null, [
          row.type === 1
            ? '卡密充值'
            : '管理员充值',
        ]);
      },
    },
  ];

  if (!isHideModel3Point.value) {
    cols.splice(2, 0, {
      title: model3Name.value,
      key: 'model3Count',
    });
  }

  if (!isHideModel4Point.value) {
    cols.splice(3, 0, {
      title: model4Name.value,
      key: 'model4Count',
    });
  }

  if (!isHideDrawMjPoint.value) {
    cols.splice(4, 0, {
      title: drawMjName.value,
      key: 'drawMjCount',
    });
  }

  return cols;
});

const data = ref([]);

async function queryRechargeLog() {
  rechargeLoading.value = true;
  const res: ResData = await fetchGetRechargeLogAPI({
    page: paginationReg.value.page,
    size: paginationReg.value.pageSize,
  });
  const { rows, count } = res.data;
  data.value = rows;
  paginationReg.value.itemCount = count;
  rechargeLoading.value = false;
}

async function useCrami() {
  if (!code.value) {
    ms.warning('请输入卡密');
    return;
  }

  try {
    loading.value = true;
    await fetchUseCramiAPI({ code: code.value });
    ms.success('卡密兑换成功');
    code.value = '';
    queryRechargeLog();
    authStore.getUserInfo();
    loading.value = false;
  } catch (error) {
    ms.error('未知错误');
    loading.value = false;
  }
}

function openDrawer() {
  showDrawer.value = true;
}

async function openDrawerAfter() {
  const res: ResData = await fetchGetPackageAPI({ status: 1, size: 30 });
  packageList.value = res.data.rows;
}

const buyCramiAddress = computed(() => authStore.globalConfig?.buyCramiAddress);

function buyPackage() {
  window.open(buyCramiAddress.value);
}

onMounted(() => {
  queryRechargeLog();
});
</script>

<template>
  <div class="wallet-container">
    <!-- 余额卡片 -->
    <NCard>
      <template #header>
        <div class="text-base font-medium">用户钱包余额</div>
      </template>
      <div class="balance-cards">
        <NGrid :x-gap="16" :y-gap="16" :cols="isMobile ? 1 : 2">
          <!-- 基础模型余额 -->
          <NGridItem
            v-if="!isHideModel3Point"
            class="balance-card"
          >
            <div class="balance-card-content">
              <div class="balance-title">{{ model3Name }}</div>
              <div class="balance-value">
                {{ userBalance.sumModel3Count > 99999 ? '∞' : userBalance.sumModel3Count ?? 0 }}
              </div>
              <div class="balance-note">基础模型消费点数</div>
            </div>
          </NGridItem>

          <!-- 高级模型余额 -->
          <NGridItem
            v-if="!isHideModel4Point"
            class="balance-card"
          >
            <div class="balance-card-content">
              <div class="balance-title">{{ model4Name }}</div>
              <div class="balance-value">
                {{ userBalance.sumModel4Count > 99999 ? '∞' : userBalance.sumModel4Count ?? 0 }}
              </div>
              <div class="balance-note">高级模型消费点数</div>
            </div>
          </NGridItem>

          <!-- 绘图余额 -->
          <NGridItem
            v-if="!isHideDrawMjPoint"
            class="balance-card"
          >
            <div class="balance-card-content">
              <div class="balance-title">{{ drawMjName }}</div>
              <div class="balance-value">
                {{ userBalance.sumDrawMjCount > 99999 ? '∞' : userBalance.sumDrawMjCount ?? 0 }}
              </div>
              <div class="balance-note">绘图消费点数</div>
            </div>
          </NGridItem>

          <!-- 卡密兑换 -->
          <NGridItem class="balance-card">
            <div class="balance-card-content">
              <div class="balance-title">卡密兑换</div>
              <div class="card-exchange">
                <NInput
                  v-model:value="code"
                  placeholder="请输入卡密"
                  class="card-input"
                />
                <div class="card-buttons">
                  <NButton
                    type="primary"
                    :loading="loading"
                    class="hover-float transition-all duration-300"
                    @click="useCrami"
                  >
                    兑换
                  </NButton>
                  <NButton
                    v-if="buyCramiAddress"
                    type="success"
                    class="hover-float transition-all duration-300"
                    @click="openDrawer"
                  >
                    购买卡密
                  </NButton>
                </div>
              </div>
            </div>
          </NGridItem>
        </NGrid>
      </div>
    </NCard>

    <!-- 充值记录 -->
    <NCard class="mt-4">
      <template #header>
        <div class="text-base font-medium">充值记录</div>
      </template>
      <NDataTable
        :columns="columns"
        :loading="rechargeLoading"
        :scroll-x="800"
        :data="data"
        :max-height="300"
        :pagination="paginationReg"
        @update:page="queryRechargeLog"
        @update:page-size="queryRechargeLog"
        class="recharge-table"
      />
    </NCard>

    <!-- 套餐抽屉 -->
    <NDrawer
      v-model:show="showDrawer"
      :width="isSmallMd ? '100%' : 600"
      :placement="isSmallMd ? 'bottom' : 'right'"
      :on-after-enter="openDrawerAfter"
      class="package-drawer"
    >
      <NDrawerContent title="套餐购买" closable>
        <NGrid :x-gap="16" :y-gap="16" :cols="isSmallMd ? 1 : 2" class="package-grid">
          <NGridItem v-for="(item, index) in packageList" :key="index" class="package-item">
            <NCard size="small" embedded class="package-card">
              <template #header>
                <div class="package-header">
                  <div class="package-name">{{ item.name }}</div>
                </div>
              </template>
              <template #cover>
                <img :src="item.coverImg" class="package-cover" />
              </template>
              <div class="package-content">
                <p class="package-description">{{ item.des }}</p>
                <div class="package-details">
                  <div class="package-detail-item">
                    <span class="detail-label">{{ model3Name }}</span>
                    <span class="detail-value">{{ item.model3Count }}</span>
                  </div>
                  <div class="package-detail-item">
                    <span class="detail-label">{{ model4Name }}</span>
                    <span class="detail-value">{{ item.model4Count }}</span>
                  </div>
                  <div class="package-detail-item">
                    <span class="detail-label">{{ drawMjName }}</span>
                    <span class="detail-value">{{ item.drawMjCount }}</span>
                  </div>
                </div>
                <div class="package-price">
                  <span class="price-label">价格</span>
                  <span class="price-value">¥{{ item.price }}</span>
                </div>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>

        <template #footer>
          <div class="drawer-footer">
            <NButton
              type="primary"
              block
              @click="buyPackage"
              class="hover-float transition-all duration-300"
            >
              前往购买
            </NButton>
          </div>
        </template>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.wallet-container {
  padding: 8px 0;
}

.balance-cards {
  margin-top: 8px;
}

.balance-card {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.balance-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.balance-card-content {
  padding: 16px;
}

.balance-title {
  color: #95aac9;
  font-size: 14px;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.balance-note {
  font-size: 12px;
  color: #989898;
}

.card-exchange {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-input {
  width: 100%;
}

.card-buttons {
  display: flex;
  gap: 8px;
}

.recharge-table {
  margin-top: 8px;
}

.package-grid {
  margin-top: 16px;
}

.package-item {
  transition: all 0.3s ease;
}

.package-item:hover {
  transform: translateY(-4px);
}

.package-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-name {
  font-weight: bold;
}

.package-cover {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.package-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.package-description {
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.package-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.package-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 13px;
  color: #666;
}

.detail-value {
  font-weight: bold;
}

.package-price {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.price-label {
  font-size: 14px;
  color: #666;
}

.price-value {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

.drawer-footer {
  margin-top: 16px;
}

:deep(.dark) .balance-value {
  color: #e0e0e0;
}

:deep(.dark) .balance-card {
  border-color: rgba(255, 255, 255, 0.17);
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
