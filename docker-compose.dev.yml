version: '3.9'

services:
  mysql:
    image: mysql:5.7
    command: --mysql-native-password=ON --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    restart: always
    volumes:
      - ./data/mysql/:/var/lib/mysql/
      - ./src/service/sql/:/docker-entrypoint-initdb.d/ # 数据库文件放此目录可自动导入
    ports:
      - "3306:3306"
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: "123456"
      MYSQL_DATABASE: "chatgpt"
      MYSQL_USER: "chatgpt"
      MYSQL_PASSWORD: "123456"

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    environment:
      TZ: Asia/Shanghai # 指定时区
    volumes:
      - ./data/redis/:/data/
