<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NTabs, NTabPane, NCard, NButton } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import StoryStructure from './story-creation/StoryStructure.vue';
import CharacterPanel from './story-creation/CharacterPanel.vue';
import StoryboardPanel from './story-creation/StoryboardPanel.vue';

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 标签页状态
const activeTab = ref('structure');

// 保存所有内容
const saveAllContent = () => {
  window.$message?.success('所有内容已保存');
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="story-creation">
    <div class="creation-header">
      <h2 class="section-title">故事创作工坊</h2>
      <p class="section-description">设计你的故事、角色和分镜</p>

      <!-- 保存按钮 -->
      <div class="header-actions">
        <NButton type="primary" @click="saveAllContent" class="save-button">
          <span class="emoji-icon">💾</span>
          保存所有内容
        </NButton>
      </div>

      <!-- 创作类型切换 -->
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane name="structure" tab="故事结构">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:file-list-line" size="16" class="mr-1" />
              <span>故事结构</span>
            </div>
          </template>
        </NTabPane>
        <NTabPane name="characters" tab="角色设计">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:user-star-line" size="16" class="mr-1" />
              <span>角色设计</span>
            </div>
          </template>
        </NTabPane>
        <NTabPane name="storyboard" tab="分镜绘制">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:layout-line" size="16" class="mr-1" />
              <span>分镜绘制</span>
            </div>
          </template>
        </NTabPane>
      </NTabs>
    </div>

    <div class="creation-content">
      <!-- 故事结构 -->
      <StoryStructure 
        v-if="activeTab === 'structure'" 
        :project-data="projectData" 
      />

      <!-- 角色设计 -->
      <CharacterPanel 
        v-else-if="activeTab === 'characters'" 
        :project-data="projectData" 
      />

      <!-- 分镜绘制 -->
      <StoryboardPanel 
        v-else-if="activeTab === 'storyboard'" 
        :project-data="projectData" 
      />
    </div>
  </div>
</template>

<style scoped>
.story-creation {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.dark .story-creation {
  background-color: #1a1a1a;
}

.creation-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.dark .section-title {
  color: #e2e8f0;
}

.section-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.dark .section-description {
  color: #94a3b8;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.creation-content {
  flex: 1;
  overflow-y: auto;
}

.emoji-icon {
  font-size: 1.25rem;
  line-height: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .story-creation {
    padding: 0.75rem;
  }

  .section-title {
    font-size: 1.25rem;
  }

  .section-description {
    font-size: 0.75rem;
  }
}
</style>
