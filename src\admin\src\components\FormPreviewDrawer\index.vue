<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import FormPreview from '@/components/FormPreview/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  fields: {
    type: String,
    default: '[]'
  },
  formName: {
    type: String,
    default: ''
  },
  formDescription: {
    type: String,
    default: ''
  },
  appName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'submit']);

// 抽屉可见性
const drawerVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 抽屉标题
const drawerTitle = computed(() => {
  return `表单预览: ${props.formName || props.appName || '应用表单'}`;
});

// 处理表单提交
function handleFormSubmit(formData: Record<string, any>) {
  emit('submit', formData);
}

// 关闭抽屉
function closeDrawer() {
  drawerVisible.value = false;
}

// 验证表单字段
const isValidForm = computed(() => {
  try {
    const fields = JSON.parse(props.fields);
    return Array.isArray(fields) && fields.length > 0;
  } catch (error) {
    return false;
  }
});
</script>

<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    direction="rtl"
    size="50%"
    :destroy-on-close="false"
    :with-header="true"
  >
    <div class="form-preview-drawer-content">
      <div v-if="!isValidForm" class="form-preview-empty">
        <el-empty description="表单字段为空或格式不正确" />
      </div>
      <FormPreview
        v-else
        :fields="fields"
        :formName="formName"
        :formDescription="formDescription"
        @submit="handleFormSubmit"
      />
    </div>

    <template #footer>
      <div class="form-preview-drawer-footer">
        <el-button @click="closeDrawer">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.form-preview-drawer-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.form-preview-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-preview-drawer-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
