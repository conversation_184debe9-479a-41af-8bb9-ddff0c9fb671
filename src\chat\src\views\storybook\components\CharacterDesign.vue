<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NCard, NInput, NButton, NGrid, NGridItem, NForm, NFormItem, NSelect, NColorPicker, NModal, NRadio, NRadioGroup, NSpace, NPopover, NSpin } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 角色状态
const characters = ref(Array.isArray(props.projectData.outline?.characters)
  ? props.projectData.outline.characters
  : []);
const showCharacterModal = ref(false);
const isGeneratingImage = ref(false);
const currentCharacter = ref(null);
const isEditMode = ref(false);
const characterImage = ref('');


// AI生成状态
const authStore = useAuthStore();
const isGeneratingCharacter = ref(false);
const controller = ref(null);

// 新角色表单
const characterForm = reactive({
  id: 0,
  name: '',
  role: 'protagonist', // protagonist, antagonist, supporting
  age: '',
  appearance: '',
  personality: '',
  background: '',
  goals: '',
  visualNotes: '',
  image: ''
});

// 角色类型选项
const roleOptions = [
  { label: '主角', value: 'protagonist' },
  { label: '反派', value: 'antagonist' },
  { label: '配角', value: 'supporting' }
];

// 获取角色类型名称
const getRoleName = (role: string) => {
  const option = roleOptions.find(opt => opt.value === role);
  return option ? option.label : '未知';
};

// 获取角色类型样式
const getRoleClass = (role: string) => {
  switch (role) {
    case 'protagonist':
      return 'role-protagonist';
    case 'antagonist':
      return 'role-antagonist';
    case 'supporting':
      return 'role-supporting';
    default:
      return '';
  }
};

// 打开角色编辑器
const openCharacterEditor = (character = null) => {
  if (character) {
    // 编辑现有角色
    isEditMode.value = true;
    currentCharacter.value = character;
    Object.assign(characterForm, character);
    characterImage.value = character.image || '';
  } else {
    // 创建新角色
    isEditMode.value = false;
    currentCharacter.value = null;
    Object.assign(characterForm, {
      id: Date.now(),
      name: '',
      role: 'protagonist',
      age: '',
      appearance: '',
      personality: '',
      background: '',
      goals: '',
      visualNotes: '',
      image: ''
    });
    characterImage.value = '';
  }

  showCharacterModal.value = true;
};

// 生成角色图像
const generateCharacterImage = () => {
  if (!characterForm.appearance.trim()) {
    window.$message?.warning('请先描述角色外观');
    return;
  }

  isGeneratingImage.value = true;

  // 构建提示词
  const prompt = `${characterForm.name || '角色'}, ${characterForm.appearance}, 儿童绘本风格, 简单可爱, 白色背景`;

  // 使用 Pollinations API 生成图像
  characterImage.value = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}`;

  // 模拟生成延迟
  setTimeout(() => {
    isGeneratingImage.value = false;
    characterForm.image = characterImage.value;
  }, 2000);
};

// 保存角色
const saveCharacter = () => {
  if (!characterForm.name.trim()) {
    window.$message?.warning('请输入角色名称');
    return;
  }

  if (isEditMode.value) {
    // 更新现有角色
    const index = characters.value.findIndex(char => char.id === characterForm.id);
    if (index !== -1) {
      characters.value[index] = { ...characterForm };
    }
  } else {
    // 添加新角色
    characters.value.push({ ...characterForm });
  }

  // 更新项目数据
  props.projectData.outline = {
    ...props.projectData.outline,
    characters: characters.value
  };

  window.$message?.success(`角色${isEditMode.value ? '更新' : '创建'}成功`);
  showCharacterModal.value = false;
};

// 删除角色相关状态
const showDeleteModal = ref(false);
const characterToDelete = ref<{ id: number, name: string }>({ id: 0, name: '' });

// 显示删除确认弹窗
const showDeleteConfirmation = (id: number) => {
  const character = characters.value.find(char => char.id === id);
  if (character) {
    characterToDelete.value = {
      id: character.id,
      name: character.name
    };
    showDeleteModal.value = true;
  }
};

// 执行删除角色操作
const confirmDeleteCharacter = () => {
  const { id } = characterToDelete.value;
  const index = characters.value.findIndex(char => char.id === id);

  if (index !== -1) {
    // 从角色列表中删除
    characters.value.splice(index, 1);

    // 更新项目数据
    props.projectData.outline = {
      ...props.projectData.outline,
      characters: characters.value
    };

    // 更新localStorage
    try {
      localStorage.setItem('project-data', JSON.stringify(props.projectData));
      console.log('Project data updated in localStorage after character deletion');
    } catch (e) {
      console.warn('Failed to update project data in localStorage', e);
    }

    window.$message?.success('角色已删除');
    showDeleteModal.value = false;
  }
};

// 删除角色（触发确认弹窗）
const deleteCharacter = (id: number) => {
  showDeleteConfirmation(id);
};

// 生成随机角色
const generateRandomCharacter = async () => {
  try {
    isGeneratingCharacter.value = true;
    window.$message?.info('正在使用AI生成角色...');

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建提示词
    const prompt = `
请为儿童绘本创建一个生动有趣的角色。

请提供以下信息：
1. 角色名称：简短易记，适合儿童
2. 角色外观：详细描述角色的外观、服装、颜色等特点
3. 角色性格：描述角色的性格特点和行为习惯
4. 背景故事：简短的角色背景故事
5. 目标和动机：角色在故事中的目标和动机

要求：
- 角色应该适合3-8岁儿童
- 角色应该有积极向上的特质
- 角色应该有独特的视觉特征，便于识别
- 角色应该有成长空间和学习潜力
- 请使用简单易懂的中文描述

请按照以下格式返回：
角色名称：[名称]
角色外观：[外观描述]
角色性格：[性格描述]
背景故事：[背景故事]
目标和动机：[目标和动机]
`;

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        // 提取最新的响应内容
        responseText = text;

        // 尝试从响应中提取角色信息
        try {
          // 提取角色名称
          const nameMatch = responseText.match(/角色名称[：:]\s*(.+?)(?=\n|$)/);
          if (nameMatch && nameMatch[1]) {
            characterForm.name = nameMatch[1].trim();
          }

          // 提取角色外观
          const appearanceMatch = responseText.match(/角色外观[：:]\s*(.+?)(?=\n角色性格|$)/s);
          if (appearanceMatch && appearanceMatch[1]) {
            characterForm.appearance = appearanceMatch[1].trim();
          }

          // 提取角色性格
          const personalityMatch = responseText.match(/角色性格[：:]\s*(.+?)(?=\n背景故事|$)/s);
          if (personalityMatch && personalityMatch[1]) {
            characterForm.personality = personalityMatch[1].trim();
          }

          // 提取背景故事
          const backgroundMatch = responseText.match(/背景故事[：:]\s*(.+?)(?=\n目标和动机|$)/s);
          if (backgroundMatch && backgroundMatch[1]) {
            characterForm.background = backgroundMatch[1].trim();
          }

          // 提取目标和动机
          const goalsMatch = responseText.match(/目标和动机[：:]\s*(.+?)(?=\n|$)/s);
          if (goalsMatch && goalsMatch[1]) {
            characterForm.goals = goalsMatch[1].trim();
          }
        } catch (e) {
          console.error('解析AI响应时出错:', e);
        }
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

    // 自动生成图像
    if (characterForm.appearance) {
      generateCharacterImage();
    }

    window.$message?.success('AI角色生成成功');

  } catch (error) {
    console.error('AI角色生成失败:', error);
    window.$message?.error('AI角色生成失败，请稍后重试');

    // 如果AI生成失败，使用本地随机生成作为备选
    const animalTypes = ['小兔子', '小熊', '小狐狸', '小猫', '小狗', '小鸟', '小松鼠', '小猴子'];
    const colors = ['白色', '棕色', '灰色', '橙色', '黄色', '蓝色', '红色'];
    const personalities = ['友善', '勇敢', '聪明', '害羞', '好奇', '调皮', '温柔', '活泼'];
    const accessories = ['戴着红色围巾', '穿着蓝色背带裤', '戴着花环', '拿着魔法棒', '背着小书包', '戴着眼镜', '穿着彩色雨靴'];

    const randomAnimal = animalTypes[Math.floor(Math.random() * animalTypes.length)];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    const randomPersonality = personalities[Math.floor(Math.random() * personalities.length)];
    const randomAccessory = accessories[Math.floor(Math.random() * accessories.length)];

    characterForm.name = `${randomPersonality}的${randomAnimal}`;
    characterForm.appearance = `一只${randomColor}的${randomAnimal}，${randomAccessory}，有着大大的眼睛和可爱的笑容。`;
    characterForm.personality = `${randomPersonality}，喜欢冒险，总是乐于助人。`;
    characterForm.background = `住在森林里的一个小木屋中，有很多朋友。`;
    characterForm.goals = `想要探索世界，结交新朋友。`;

    // 自动生成图像
    generateCharacterImage();
  } finally {
    isGeneratingCharacter.value = false;
    controller.value = null;
  }
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="character-design">
    <div class="design-header">
      <div class="header-with-character">
        <div class="character-guide">
          <img src="https://image.pollinations.ai/prompt/cute%20cartoon%20fox%20with%20pencil%20and%20character%20design%20sheet,%20children%20book%20illustration,%20colorful,%20friendly,%20simple" alt="角色设计小狐狸" class="guide-character" />
        </div>
        <div class="header-content">
          <h2 class="page-title">角色设计</h2>
          <p class="page-description">创造故事中的精彩角色</p>
          <div class="sync-hint">
            <span class="sync-icon">✨</span>
            <span class="sync-text">设计生动有趣的角色，让故事更加精彩</span>
          </div>
        </div>
      </div>
    </div>

    <div class="design-content">
      <!-- 角色列表 -->
      <div class="story-guide-section small">
        <div class="guide-character-small">
          <img src="https://image.pollinations.ai/prompt/cute%20cartoon%20fox%20with%20character%20design%20sheet,%20children%20book%20illustration,%20colorful,%20friendly,%20simple" alt="角色设计小狐狸" />
        </div>
        <div class="guide-bubble">
          <p>嗨！我是角色设计小狐狸！让我们一起创造故事中的角色吧！每个好故事都需要有趣的角色！</p>
        </div>
      </div>

      <div class="characters-header">
        <h3 class="characters-title">我的角色</h3>
        <div class="character-actions">
          <NButton @click="openCharacterEditor()" class="add-character-btn">
            <SvgIcon name="ri:user-add-line" size="16" class="mr-1" />
            创建角色
          </NButton>
        </div>
      </div>

      <div v-if="characters.length === 0" class="empty-characters">
        <div class="empty-character-container">
          <img src="https://image.pollinations.ai/prompt/cute%20cartoon%20fox%20thinking%20about%20characters,%20thought%20bubble,%20children%20book%20illustration,%20colorful,%20friendly,%20simple" alt="思考角色的小狐狸" class="empty-character-image" />
          <div class="empty-text">
            <h4>还没有创建角色</h4>
            <p>点击"创建角色"按钮，开始设计你的第一个角色吧！</p>
          </div>
        </div>
      </div>

      <div v-else class="characters-grid">
        <NGrid :cols="isMobile ? 1 : 3" :x-gap="16" :y-gap="16">
          <NGridItem v-for="character in characters" :key="character.id">
            <NCard class="character-card animated-card" hoverable @click="openCharacterEditor(character)">
              <div class="character-image">
                <img v-if="character.image" :src="character.image" :alt="character.name" />
                <div v-else class="image-placeholder">
                  <SvgIcon name="ri:user-smile-line" size="48" />
                </div>
                <div :class="['character-role', getRoleClass(character.role)]">
                  {{ getRoleName(character.role) }}
                </div>
              </div>
              <div class="character-info">
                <h3 class="character-name">{{ character.name }}</h3>
                <p v-if="character.age" class="character-age">年龄: {{ character.age }}</p>
                <p class="character-description">{{ character.appearance }}</p>
              </div>
              <div class="character-actions">
                <NButton
                  size="small"
                  @click.stop="deleteCharacter(character.id)"
                  class="delete-btn"
                >
                  <SvgIcon name="ri:delete-bin-line" size="14" />
                </NButton>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>


    </div>

    <!-- 角色编辑器弹窗 -->
    <NModal
      v-model:show="showCharacterModal"
      preset="card"
      style="width: 90%; max-width: 900px;"
      :title="isEditMode ? '编辑角色' : '创建新角色'"
      :bordered="false"
      size="huge"
      class="character-modal"
    >
      <div class="character-editor">
        <div class="story-guide-section small right modal-guide">
          <div class="guide-bubble">
            <p>{{ isEditMode ? '修改角色的特点，让故事更精彩！' : '创造一个有趣的角色，让故事更生动！' }}</p>
          </div>
          <div class="guide-character-small">
            <img src="https://image.pollinations.ai/prompt/cute%20cartoon%20fox%20drawing%20characters,%20children%20book%20illustration,%20colorful,%20friendly,%20simple" alt="角色设计小狐狸" />
          </div>
        </div>

        <div class="editor-layout">
          <div class="editor-form">
            <NForm label-placement="left" label-width="auto" :model="characterForm">
              <NFormItem label="角色名称" required>
                <NInput v-model:value="characterForm.name" placeholder="输入角色名称" class="child-input" />
              </NFormItem>

              <NFormItem label="角色类型">
                <div class="character-role-selector">
                  <div class="role-selector-options">
                    <div
                      class="role-option"
                      :class="{ 'active': characterForm.role === 'protagonist' }"
                      @click="characterForm.role = 'protagonist'"
                    >
                      <div class="role-icon protagonist-icon">👑</div>
                      <div class="role-label">主角</div>
                    </div>
                    <div
                      class="role-option"
                      :class="{ 'active': characterForm.role === 'supporting' }"
                      @click="characterForm.role = 'supporting'"
                    >
                      <div class="role-icon supporting-icon">💛</div>
                      <div class="role-label">配角</div>
                    </div>
                    <div
                      class="role-option"
                      :class="{ 'active': characterForm.role === 'antagonist' }"
                      @click="characterForm.role = 'antagonist'"
                    >
                      <div class="role-icon antagonist-icon">⚡</div>
                      <div class="role-label">对手</div>
                    </div>
                  </div>
                </div>
              </NFormItem>

              <NFormItem label="年龄">
                <NInput v-model:value="characterForm.age" placeholder="角色的年龄" class="child-input" />
              </NFormItem>

              <NFormItem label="外观描述" required>
                <NInput
                  v-model:value="characterForm.appearance"
                  type="textarea"
                  placeholder="描述角色的外观、服装、颜色等"
                  class="child-input"
                />
              </NFormItem>

              <NFormItem label="性格特点">
                <NInput
                  v-model:value="characterForm.personality"
                  type="textarea"
                  placeholder="描述角色的性格、行为习惯等"
                  class="child-input"
                />
              </NFormItem>

              <NFormItem label="背景故事">
                <NInput
                  v-model:value="characterForm.background"
                  type="textarea"
                  placeholder="角色的背景故事、来历等"
                  class="child-input"
                />
              </NFormItem>

              <NFormItem label="目标和动机">
                <NInput
                  v-model:value="characterForm.goals"
                  type="textarea"
                  placeholder="角色在故事中的目标和动机"
                  class="child-input"
                />
              </NFormItem>

              <NFormItem label="视觉笔记">
                <NInput
                  v-model:value="characterForm.visualNotes"
                  type="textarea"
                  placeholder="关于角色设计的额外视觉笔记"
                  class="child-input"
                />
              </NFormItem>
            </NForm>

            <div class="form-actions">
              <NButton
                @click="generateRandomCharacter"
                class="random-btn"
                :loading="isGeneratingCharacter"
                :disabled="isGeneratingCharacter"
              >
                <template #icon>
                  <SvgIcon name="ri:magic-line" size="16" />
                </template>
                AI魔法生成角色
              </NButton>
              <NButton
                type="primary"
                @click="saveCharacter"
                :disabled="isGeneratingCharacter || isGeneratingImage"
                class="save-character-btn"
              >
                <template #icon>
                  <SvgIcon name="ri:save-line" size="16" />
                </template>
                保存角色
              </NButton>
            </div>
          </div>

          <div class="editor-preview">
            <div class="preview-header">
              <h3 class="preview-title">角色预览</h3>
              <NButton
                @click="generateCharacterImage"
                :loading="isGeneratingImage"
                :disabled="!characterForm.appearance"
                class="generate-image-btn"
              >
                <SvgIcon name="ri:image-add-line" size="16" class="mr-1" />
                生成图像
              </NButton>
            </div>

            <div class="preview-image">
              <div v-if="isGeneratingImage" class="generating-overlay">
                <NSpin size="large" />
                <p>正在生成角色图像...</p>
              </div>

              <img v-if="characterImage" :src="characterImage" :alt="characterForm.name" />
              <div v-else class="image-placeholder large">
                <SvgIcon name="ri:user-smile-line" size="64" />
                <p>点击"生成图像"创建角色图像</p>
              </div>
            </div>

            <div class="preview-info">
              <h3 v-if="characterForm.name" class="preview-name">{{ characterForm.name }}</h3>
              <div v-if="characterForm.role" class="preview-role" :class="getRoleClass(characterForm.role)">
                {{ getRoleName(characterForm.role) }}
              </div>
              <p v-if="characterForm.appearance" class="preview-description">{{ characterForm.appearance }}</p>
            </div>
          </div>
        </div>
      </div>
    </NModal>

    <!-- 删除确认弹窗 -->
    <NModal
      v-model:show="showDeleteModal"
      preset="dialog"
      title="确认删除"
      positive-text="删除"
      negative-text="取消"
      @positive-click="confirmDeleteCharacter"
    >
      <div class="delete-modal-content">
        <div class="delete-warning-icon">
          <span class="warning-icon">⚠️</span>
        </div>
        <p class="delete-warning-text">确定要删除角色"{{ characterToDelete.name }}"吗？此操作不可恢复。</p>
      </div>
    </NModal>
  </div>
</template>

<style scoped>
.character-design {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: #f8fafc;
  transition: all 0.3s ease;
}

.dark .character-design {
  background-color: #1e293b;
}

.design-header {
  margin-bottom: 2.5rem;
  position: relative;
  padding-bottom: 1.5rem;
  border-bottom: 2px dashed #e2e8f0;
  max-width: 100%;
}

.dark .design-header {
  border-bottom-color: #334155;
}

.header-with-character {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.character-guide {
  flex-shrink: 0;
}

.guide-character {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #60a5fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.guide-character:hover {
  transform: scale(1.05) rotate(5deg);
}

.header-content {
  flex-grow: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #3b82f6;
  letter-spacing: -0.025em;
  position: relative;
  display: inline-block;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 3rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  border-radius: 0.25rem;
}

.dark .page-title {
  color: #60a5fa;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.3);
}

.dark .page-title::after {
  background: linear-gradient(90deg, #8b5cf6, #93c5fd);
}

.page-description {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 1rem;
  max-width: 100%;
  line-height: 1.5;
}

.dark .page-description {
  color: #94a3b8;
}

.sync-hint {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: #f0f9ff;
  border-radius: 0.75rem;
  font-size: 1rem;
  color: #0369a1;
  border-left: 4px solid #0ea5e9;
  max-width: fit-content;
  margin-bottom: 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
  transition: all 0.2s ease;
}

.sync-hint:hover {
  background-color: #e0f2fe;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .sync-hint {
  background-color: #0c4a6e;
  color: #7dd3fc;
  border-left-color: #0ea5e9;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark .sync-hint:hover {
  background-color: #075985;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sync-icon {
  font-size: 1.25rem;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.design-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: fadeIn 0.5s ease-out;
}

.story-guide-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  background-color: #f0f9ff;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.story-guide-section.small {
  margin-bottom: 1.5rem;
  padding: 0.75rem;
}

.story-guide-section.right {
  flex-direction: row-reverse;
  justify-content: flex-end;
  margin-right: 1rem;
}

.story-guide-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .story-guide-section {
  background-color: #0c4a6e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .story-guide-section:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.guide-character-small {
  flex-shrink: 0;
}

.guide-character-small img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #60a5fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.guide-character-small img:hover {
  transform: scale(1.1) rotate(5deg);
}

.guide-bubble {
  position: relative;
  background-color: white;
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  max-width: 80%;
}

.guide-bubble::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -10px;
  transform: translateY(-50%);
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent white transparent transparent;
}

.story-guide-section.right .guide-bubble::before {
  left: auto;
  right: -10px;
  transform: translateY(-50%) rotate(180deg);
}

.dark .guide-bubble {
  background-color: #1e293b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .guide-bubble::before {
  border-color: transparent #1e293b transparent transparent;
}

.guide-bubble p {
  margin: 0;
  font-size: 1rem;
  color: #334155;
  line-height: 1.4;
}

.dark .guide-bubble p {
  color: #e2e8f0;
}

.characters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .characters-header {
  border-bottom-color: #334155;
}

.characters-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3b82f6;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark .characters-title {
  color: #60a5fa;
}

.character-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.add-character-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.add-character-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.dark .add-character-btn {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.4);
}

.dark .add-character-btn:hover {
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.5);
}

.empty-characters {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  background-color: #f8fafc;
  border-radius: 1rem;
  color: #94a3b8;
  border: 2px dashed #e2e8f0;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.empty-characters:hover {
  border-color: #93c5fd;
  background-color: #f1f5f9;
}

.dark .empty-characters {
  background-color: #1e293b;
  color: #94a3b8;
  border-color: #334155;
}

.dark .empty-characters:hover {
  border-color: #60a5fa;
  background-color: #0f172a;
}

.empty-character-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400px;
}

.empty-character-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  margin-bottom: 1.5rem;
  border: 3px solid #e2e8f0;
  transition: all 0.3s ease;
}

.empty-characters:hover .empty-character-image {
  transform: scale(1.05);
  border-color: #93c5fd;
}

.dark .empty-character-image {
  border-color: #334155;
}

.dark .empty-characters:hover .empty-character-image {
  border-color: #60a5fa;
}

.empty-text h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #3b82f6;
}

.dark .empty-text h4 {
  color: #60a5fa;
}

.empty-text p {
  font-size: 1rem;
  color: #64748b;
}

.dark .empty-text p {
  color: #94a3b8;
}

.characters-grid {
  margin-bottom: 1.5rem;
}

.character-card {
  height: 100%;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.dark .character-card {
  border-color: #334155;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.character-card.animated-card {
  animation: fadeIn 0.5s ease-out;
  animation-fill-mode: both;
}

.character-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
  border-color: #93c5fd;
}

.dark .character-card:hover {
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
  border-color: #60a5fa;
}

.character-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background-color: #f1f5f9;
}

.dark .character-image {
  background-color: #334155;
}

.character-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.character-card:hover .character-image img {
  transform: scale(1.05);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.image-placeholder.large {
  gap: 1rem;
}

.image-placeholder p {
  font-size: 0.875rem;
  text-align: center;
  max-width: 80%;
}

.character-role {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.35rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.role-protagonist {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.role-antagonist {
  background: linear-gradient(135deg, #ef4444, #f87171);
}

.role-supporting {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.character-info {
  padding: 1rem;
}

.character-name {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
  position: relative;
  display: inline-block;
}

.character-name::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 0;
  width: 2rem;
  height: 0.15rem;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  border-radius: 0.25rem;
}

.dark .character-name {
  color: #e2e8f0;
}

.character-age {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.dark .character-age {
  color: #94a3b8;
}

.character-description {
  font-size: 0.875rem;
  color: #475569;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.dark .character-description {
  color: #cbd5e1;
}

.character-card .character-actions {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.character-card:hover .character-actions {
  opacity: 1;
}

.delete-btn {
  background-color: rgba(255, 255, 255, 0.9);
  color: #ef4444;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background-color: #ef4444;
  color: white;
  transform: scale(1.1);
}

.dark .delete-btn {
  background-color: rgba(30, 41, 59, 0.9);
  color: #f87171;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark .delete-btn:hover {
  background-color: #ef4444;
  color: white;
}



@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* 角色编辑器弹窗样式 */
.character-modal {
  border-radius: 1rem;
  overflow: hidden;
}

.character-editor {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-guide {
  margin: 0 0 1rem 0;
}

.editor-layout {
  display: flex;
  gap: 2rem;
}

.editor-form {
  flex: 3;
}

.editor-preview {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 0;
}

.dark .preview-title {
  color: #60a5fa;
}

.preview-image {
  position: relative;
  height: 300px;
  border-radius: 1rem;
  overflow: hidden;
  background-color: #f1f5f9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.preview-image:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dark .preview-image {
  background-color: #334155;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dark .preview-image:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  z-index: 20;
}

.dark .generating-overlay {
  background-color: rgba(30, 41, 59, 0.9);
}

.preview-info {
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.preview-info:hover {
  background-color: #f1f5f9;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .preview-info {
  background-color: #1e293b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .preview-info:hover {
  background-color: #0f172a;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.preview-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
  position: relative;
  display: inline-block;
}

.preview-name::after {
  content: '';
  position: absolute;
  bottom: -0.25rem;
  left: 0;
  width: 2.5rem;
  height: 0.15rem;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  border-radius: 0.25rem;
}

.dark .preview-name {
  color: #e2e8f0;
}

.preview-role {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.75rem;
}

.preview-description {
  font-size: 0.95rem;
  color: #475569;
  line-height: 1.6;
}

.dark .preview-description {
  color: #cbd5e1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.random-btn {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.random-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
}

.dark .random-btn {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.5);
}

.dark .random-btn:hover {
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.6);
}

.save-character-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.save-character-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.dark .save-character-btn {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.5);
}

.dark .save-character-btn:hover {
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.6);
}

.generate-image-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.generate-image-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.dark .generate-image-btn {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.5);
}

.dark .generate-image-btn:hover {
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.6);
}

/* 角色类型选择器样式 */
.character-role-selector {
  margin: 0.5rem 0 1rem;
}

.role-selector-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.role-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.role-option.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.2);
}

.dark .role-option {
  background-color: #1e293b;
  border-color: #334155;
}

.dark .role-option:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.dark .role-option.active {
  border-color: #3b82f6;
  background-color: #1e40af;
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
}

.role-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.protagonist-icon {
  color: #3b82f6;
  background-color: #dbeafe;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.supporting-icon {
  color: #f59e0b;
  background-color: #fef3c7;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.antagonist-icon {
  color: #ef4444;
  background-color: #fee2e2;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.role-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .role-label {
  color: #e2e8f0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .character-design {
    padding: 1rem;
  }

  .header-with-character {
    flex-direction: column;
    text-align: center;
  }

  .page-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .sync-hint {
    margin: 1rem auto;
  }

  .editor-layout {
    flex-direction: column;
  }

  .preview-image {
    height: 250px;
  }

  .tips-content {
    grid-template-columns: 1fr;
  }

  .role-selector-options {
    justify-content: center;
  }
}

/* 删除确认弹窗样式 */
.delete-modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  text-align: center;
}

.delete-warning-icon {
  margin-bottom: 1rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fee2e2;
  border-radius: 50%;
}

.dark .delete-warning-icon {
  background-color: #7f1d1d;
}

.warning-icon {
  font-size: 1.5rem;
}

.delete-warning-text {
  font-size: 1rem;
  color: #1e293b;
  line-height: 1.5;
}

.dark .delete-warning-text {
  color: #e2e8f0;
}
</style>
