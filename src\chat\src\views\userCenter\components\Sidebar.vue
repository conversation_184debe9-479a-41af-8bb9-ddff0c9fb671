<script setup lang="ts">
import { fetchSyncVisitorDataAPI, fetchVisitorCountAPI } from '@/api/balance';
import type { ResData } from '@/api/types';
import defaultAvatar from '@/assets/avatar.png';
import { SvgIcon } from '@/components/common';
import { t } from '@/locales';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import { NAvatar, NButton, useMessage } from 'naive-ui';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update-collapsed']);

const useGlobalStore = useGlobalStoreWithOut();
const authStore = useAuthStore();
const router = useRouter();
const ms = useMessage();
const visitorCount = ref(0);

const userBalance = computed(() => authStore.userBalance);
const isUseWxLogin = computed(() => authStore.globalConfig?.isUseWxLogin);
const wechatRegisterStatus = computed(
  () => Number(authStore.globalConfig.wechatRegisterStatus) === 1
);
const model3Name = computed(
  () => authStore.globalConfig.model3Name || t('goods.basicModelQuota')
);
const model4Name =
  computed(() => authStore.globalConfig.model4Name) ||
  t('goods.advancedModelQuota');
const isBindWx = computed(() => authStore.userInfo.isBindWx);
const avatar = computed(() => authStore.userInfo.avatar ?? defaultAvatar);
const username = computed(() => authStore.userInfo.username ?? t('usercenter.notLoggedIn'));
const siteName = computed(() => authStore.globalConfig?.siteName || 'AIWeb');

async function getVisitorCount() {
  const res: ResData = await fetchVisitorCountAPI();
  visitorCount.value = res.data || 0;
}

async function syncVisitorData() {
  const res: ResData = await fetchSyncVisitorDataAPI();
  if (res.success) {
    ms.success(t('usercenter.syncComplete'));
  }
  getVisitorCount();
}

function logOut() {
  authStore.logOut();
  router.replace('/');
}

function handleUpdateCollapsed() {
  emit('update-collapsed', !props.collapsed);
}

function goToChat() {
  router.push('/chat');
}

onMounted(() => {
  getVisitorCount();
});
</script>

<template>
  <div class="h-full transition-all duration-300 animate__animated animate__fadeIn">
    <div
      class="flex flex-col h-full w-full bg-white dark:bg-gray-800 select-none transition-all duration-300 ds-shadow-md rounded-lg overflow-hidden border border-gray-100 dark:border-gray-700 backdrop-blur-sm mr-0"
    >
      <main class="flex flex-col h-full flex-1 overflow-hidden flex-shrink-0">
        <!-- 顶部区域 -->
        <div
          class="flex bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 w-full justify-between items-center px-4 py-3 border-b border-gray-100 dark:border-gray-700 rounded-t-lg shadow-sm"
        >
          <div class="flex items-center gap-3">
            <!-- 返回按钮 -->
            <button
              @click="goToChat"
              class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
              title="返回聊天"
            >
              <SvgIcon icon="ri:arrow-left-line" class="text-gray-600 dark:text-gray-300" />
            </button>
            
            <!-- 标题 -->
            <div class="font-medium text-gray-800 dark:text-gray-200 text-shadow-sm">{{ t('usercenter.personalCenter') }}</div>
          </div>
        </div>

        <!-- 用户信息区域 -->
        <div class="flex flex-col items-center px-4 py-6 border-b border-gray-100 dark:border-gray-700">
          <NAvatar :size="80" :src="avatar" :fallback-src="defaultAvatar" class="border-2 border-white dark:border-gray-700 shadow-md hover-float transition-all duration-300" />
          <div class="mt-3 text-lg font-medium text-gray-800 dark:text-gray-200">{{ username }}</div>
        </div>

        <!-- 使用记录区域 -->
        <div class="flex-1 overflow-y-auto px-4 py-4">
          <div class="text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
            {{ t('usercenter.myUsageRecord') }}
          </div>
          
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ model3Name }}</span>
              <span class="font-medium text-gray-800 dark:text-gray-200">
                {{ userBalance.useModel3Count || '0' }} {{ t('usercenter.points') }}
              </span>
            </div>
            
            <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ model4Name }}</span>
              <span class="font-medium text-gray-800 dark:text-gray-200">
                {{ userBalance.useModel4Count || '0' }} {{ t('usercenter.points') }}
              </span>
            </div>
          </div>

          <!-- 微信绑定 -->
          <div v-if="isUseWxLogin && wechatRegisterStatus" class="mt-4">
            <div class="text-base font-medium text-gray-700 dark:text-gray-300 mb-3">
              {{ t('usercenter.accountSettings') }}
            </div>
            
            <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ t('usercenter.bindWeChat') }}</span>
              <div>
                <NButton
                  v-if="!isBindWx"
                  size="small"
                  type="primary"
                  @click="useGlobalStore.updateBindwxDialog(true)"
                  class="hover-float transition-all duration-300"
                >
                  {{ t('usercenter.bind') }}
                </NButton>
                <span v-else class="text-sm text-green-500">{{ t('usercenter.weChatBound') }}</span>
              </div>
            </div>
          </div>

          <!-- 游客数据同步 -->
          <div v-if="visitorCount > 0" class="mt-4">
            <div class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ t('usercenter.visitorData') }}</span>
              <NButton 
                size="small" 
                type="primary" 
                @click="syncVisitorData"
                class="hover-float transition-all duration-300"
              >
                {{ t('usercenter.syncVisitorData') }}
              </NButton>
            </div>
          </div>
        </div>

        <!-- 底部区域 -->
        <div class="p-4 border-t border-gray-100 dark:border-gray-700">
          <NButton 
            block 
            type="error" 
            @click="logOut"
            class="hover-float transition-all duration-300"
          >
            <template #icon>
              <SvgIcon icon="ri:logout-box-line" />
            </template>
            {{ t('usercenter.logOut') }}
          </NButton>
        </div>

        <!-- 会员过期提示 -->
        <div
          v-if="userBalance.expirationTime"
          class="px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm font-medium"
        >
          {{ t('usercenter.membershipExpiration') }}: {{ userBalance.expirationTime }}
        </div>
      </main>
    </div>
  </div>
</template>
