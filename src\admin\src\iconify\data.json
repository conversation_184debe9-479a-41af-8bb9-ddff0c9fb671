[{"prefix": "ant-design", "info": {"name": "Ant Design Icons", "total": 789, "version": "4.3.1", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ant-design/ant-design-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/ant-design/ant-design-icons/blob/master/LICENSE"}, "samples": ["pushpin-filled", "pie-chart-outlined", "shopping-twotone"], "height": 16, "category": "General", "palette": false}, "icons": ["account-book-filled", "account-book-outlined", "account-book-twotone", "aim-outlined", "alert-filled", "alert-outlined", "alert-twotone", "alibaba-outlined", "align-center-outlined", "align-left-outlined", "align-right-outlined", "alipay-circle-filled", "alipay-circle-outlined", "alipay-outlined", "alipay-square-filled", "ali<PERSON>wang-filled", "aliwangwang-outlined", "aliyun-outlined", "amazon-circle-filled", "amazon-outlined", "amazon-square-filled", "android-filled", "android-outlined", "ant-cloud-outlined", "ant-design-outlined", "apartment-outlined", "api-filled", "api-outlined", "api-twotone", "apple-filled", "apple-outlined", "appstore-add-outlined", "appstore-filled", "appstore-outlined", "appstore-twotone", "area-chart-outlined", "arrow-down-outlined", "arrow-left-outlined", "arrow-right-outlined", "arrow-up-outlined", "arrows-alt-outlined", "audio-filled", "audio-muted-outlined", "audio-outlined", "audio-twotone", "audit-outlined", "backward-filled", "backward-outlined", "bank-filled", "bank-outlined", "bank-twotone", "bar-chart-outlined", "barcode-outlined", "bars-outlined", "behance-circle-filled", "behance-outlined", "behance-square-filled", "behance-square-outlined", "bell-filled", "bell-outlined", "bell-twotone", "bg-colors-outlined", "block-outlined", "bold-outlined", "book-filled", "book-outlined", "book-twotone", "border-bottom-outlined", "border-horizontal-outlined", "border-inner-outlined", "border-left-outlined", "border-outer-outlined", "border-outlined", "border-right-outlined", "border-top-outlined", "border-verticle-outlined", "borderless-table-outlined", "box-plot-filled", "box-plot-outlined", "box-plot-twotone", "branches-outlined", "bug-filled", "bug-outlined", "bug-twotone", "build-filled", "build-outlined", "build-twotone", "bulb-filled", "bulb-outlined", "bulb-twotone", "calculator-filled", "calculator-outlined", "calculator-twotone", "calendar-filled", "calendar-outlined", "calendar-twotone", "camera-filled", "camera-outlined", "camera-twotone", "car-filled", "car-outlined", "car-twotone", "caret-down-filled", "caret-down-outlined", "caret-left-filled", "caret-left-outlined", "caret-right-filled", "caret-right-outlined", "caret-up-filled", "caret-up-outlined", "carry-out-filled", "carry-out-outlined", "carry-out-twotone", "check-circle-filled", "check-circle-outlined", "check-circle-twotone", "check-outlined", "check-square-filled", "check-square-outlined", "check-square-twotone", "chrome-filled", "chrome-outlined", "ci-circle-filled", "ci-circle-outlined", "ci-circle-twotone", "ci-outlined", "ci-twotone", "clear-outlined", "clock-circle-filled", "clock-circle-outlined", "clock-circle-twotone", "close-circle-filled", "close-circle-outlined", "close-circle-twotone", "close-outlined", "close-square-filled", "close-square-outlined", "close-square-twotone", "cloud-download-outlined", "cloud-filled", "cloud-outlined", "cloud-server-outlined", "cloud-sync-outlined", "cloud-twotone", "cloud-upload-outlined", "cluster-outlined", "code-filled", "code-outlined", "code-sandbox-circle-filled", "code-sandbox-outlined", "code-sandbox-square-filled", "code-twotone", "codepen-circle-filled", "codepen-circle-outlined", "codepen-outlined", "codepen-square-filled", "coffee-outlined", "column-height-outlined", "column-width-outlined", "comment-outlined", "compass-filled", "compass-outlined", "compass-twotone", "compress-outlined", "console-sql-outlined", "contacts-filled", "contacts-outlined", "contacts-twotone", "container-filled", "container-outlined", "container-twotone", "control-filled", "control-outlined", "control-twotone", "copy-filled", "copy-outlined", "copy-twotone", "copyright-circle-filled", "copyright-circle-outlined", "copyright-circle-twotone", "copyright-outlined", "copyright-twotone", "credit-card-filled", "credit-card-outlined", "credit-card-twotone", "crown-filled", "crown-outlined", "crown-twotone", "customer-service-filled", "customer-service-outlined", "customer-service-twotone", "dash-outlined", "dashboard-filled", "dashboard-outlined", "dashboard-twotone", "database-filled", "database-outlined", "database-twotone", "delete-column-outlined", "delete-filled", "delete-outlined", "delete-row-outlined", "delete-twotone", "delivered-procedure-outlined", "deployment-unit-outlined", "desktop-outlined", "diff-filled", "diff-outlined", "diff-twotone", "dingding-outlined", "dingtalk-circle-filled", "dingtalk-outlined", "dingtalk-square-filled", "disconnect-outlined", "dislike-filled", "dislike-outlined", "dislike-twotone", "dollar-circle-filled", "dollar-circle-outlined", "dollar-circle-twotone", "dollar-outlined", "dollar-twotone", "dot-chart-outlined", "double-left-outlined", "double-right-outlined", "down-circle-filled", "down-circle-outlined", "down-circle-twotone", "down-outlined", "down-square-filled", "down-square-outlined", "down-square-twotone", "download-outlined", "drag-outlined", "dribbble-circle-filled", "dribbble-outlined", "dribbble-square-filled", "dribbble-square-outlined", "dropbox-circle-filled", "dropbox-outlined", "dropbox-square-filled", "edit-filled", "edit-outlined", "edit-twotone", "ellipsis-outlined", "enter-outlined", "environment-filled", "environment-outlined", "environment-twotone", "euro-circle-filled", "euro-circle-outlined", "euro-circle-twotone", "euro-outlined", "euro-twotone", "exception-outlined", "exclamation-circle-filled", "exclamation-circle-outlined", "exclamation-circle-twotone", "exclamation-outlined", "expand-alt-outlined", "expand-outlined", "experiment-filled", "experiment-outlined", "experiment-twotone", "export-outlined", "eye-filled", "eye-invisible-filled", "eye-invisible-outlined", "eye-invisible-twotone", "eye-outlined", "eye-twotone", "facebook-filled", "facebook-outlined", "fall-outlined", "fast-backward-filled", "fast-backward-outlined", "fast-forward-filled", "fast-forward-outlined", "field-binary-outlined", "field-number-outlined", "field-string-outlined", "field-time-outlined", "file-add-filled", "file-add-outlined", "file-add-twotone", "file-done-outlined", "file-excel-filled", "file-excel-outlined", "file-excel-twotone", "file-exclamation-filled", "file-exclamation-outlined", "file-exclamation-twotone", "file-filled", "file-gif-outlined", "file-image-filled", "file-image-outlined", "file-image-twotone", "file-jpg-outlined", "file-markdown-filled", "file-markdown-outlined", "file-markdown-twotone", "file-outlined", "file-pdf-filled", "file-pdf-outlined", "file-pdf-twotone", "file-ppt-filled", "file-ppt-outlined", "file-ppt-twotone", "file-protect-outlined", "file-search-outlined", "file-sync-outlined", "file-text-filled", "file-text-outlined", "file-text-twotone", "file-twotone", "file-unknown-filled", "file-unknown-outlined", "file-unknown-twotone", "file-word-filled", "file-word-outlined", "file-word-twotone", "file-zip-filled", "file-zip-outlined", "file-zip-twotone", "filter-filled", "filter-outlined", "filter-twotone", "fire-filled", "fire-outlined", "fire-twotone", "flag-filled", "flag-outlined", "flag-twotone", "folder-add-filled", "folder-add-outlined", "folder-add-twotone", "folder-filled", "folder-open-filled", "folder-open-outlined", "folder-open-twotone", "folder-outlined", "folder-twotone", "folder-view-outlined", "font-colors-outlined", "font-size-outlined", "fork-outlined", "form-outlined", "format-painter-filled", "format-painter-outlined", "forward-filled", "forward-outlined", "frown-filled", "frown-outlined", "frown-twotone", "fullscreen-exit-outlined", "fullscreen-outlined", "function-outlined", "fund-filled", "fund-outlined", "fund-projection-screen-outlined", "fund-twotone", "fund-view-outlined", "funnel-plot-filled", "funnel-plot-outlined", "funnel-plot-twotone", "gateway-outlined", "gif-outlined", "gift-filled", "gift-outlined", "gift-twotone", "github-filled", "github-outlined", "gitlab-filled", "gitlab-outlined", "global-outlined", "gold-filled", "gold-outlined", "gold-twotone", "golden-filled", "google-circle-filled", "google-outlined", "google-plus-circle-filled", "google-plus-outlined", "google-plus-square-filled", "google-square-filled", "group-outlined", "hdd-filled", "hdd-outlined", "hdd-twotone", "heart-filled", "heart-outlined", "heart-twotone", "heat-map-outlined", "highlight-filled", "highlight-outlined", "highlight-twotone", "history-outlined", "holder-outlined", "home-filled", "home-outlined", "home-twotone", "hourglass-filled", "hourglass-outlined", "hourglass-twotone", "html5-filled", "html5-outlined", "html5-twotone", "idcard-filled", "idcard-outlined", "idcard-twotone", "ie-circle-filled", "ie-outlined", "ie-square-filled", "import-outlined", "inbox-outlined", "info-circle-filled", "info-circle-outlined", "info-circle-twotone", "info-outlined", "insert-row-above-outlined", "insert-row-below-outlined", "insert-row-left-outlined", "insert-row-right-outlined", "instagram-filled", "instagram-outlined", "insurance-filled", "insurance-outlined", "insurance-twotone", "interaction-filled", "interaction-outlined", "interaction-twotone", "issues-close-outlined", "italic-outlined", "key-outlined", "laptop-outlined", "layout-filled", "layout-outlined", "layout-twotone", "left-circle-filled", "left-circle-outlined", "left-circle-twotone", "left-outlined", "left-square-filled", "left-square-outlined", "left-square-twotone", "like-filled", "like-outlined", "like-twotone", "line-chart-outlined", "line-height-outlined", "line-outlined", "link-outlined", "linkedin-filled", "linkedin-outlined", "loading-3-quarters-outlined", "loading-outlined", "lock-filled", "lock-outlined", "lock-twotone", "login-outlined", "logout-outlined", "mac-command-filled", "mac-command-outlined", "mail-filled", "mail-outlined", "mail-twotone", "man-outlined", "medicine-box-filled", "medicine-box-outlined", "medicine-box-twotone", "medium-circle-filled", "medium-outlined", "medium-square-filled", "medium-workmark-outlined", "meh-filled", "meh-outlined", "meh-twotone", "menu-fold-outlined", "menu-outlined", "menu-unfold-outlined", "merge-cells-outlined", "message-filled", "message-outlined", "message-twotone", "minus-circle-filled", "minus-circle-outlined", "minus-circle-twotone", "minus-outlined", "minus-square-filled", "minus-square-outlined", "minus-square-twotone", "mobile-filled", "mobile-outlined", "mobile-twotone", "money-collect-filled", "money-collect-outlined", "money-collect-twotone", "monitor-outlined", "more-outlined", "node-collapse-outlined", "node-expand-outlined", "node-index-outlined", "notification-filled", "notification-outlined", "notification-twotone", "number-outlined", "one-to-one-outlined", "ordered-list-outlined", "paper-clip-outlined", "partition-outlined", "pause-circle-filled", "pause-circle-outlined", "pause-circle-twotone", "pause-outlined", "pay-circle-filled", "pay-circle-outlined", "percentage-outlined", "phone-filled", "phone-outlined", "phone-twotone", "pic-center-outlined", "pic-left-outlined", "pic-right-outlined", "picture-filled", "picture-outlined", "picture-twotone", "pie-chart-filled", "pie-chart-outlined", "pie-chart-twotone", "play-circle-filled", "play-circle-outlined", "play-circle-twotone", "play-square-filled", "play-square-outlined", "play-square-twotone", "plus-circle-filled", "plus-circle-outlined", "plus-circle-twotone", "plus-outlined", "plus-square-filled", "plus-square-outlined", "plus-square-twotone", "pound-circle-filled", "pound-circle-outlined", "pound-circle-twotone", "pound-outlined", "poweroff-outlined", "printer-filled", "printer-outlined", "printer-twotone", "profile-filled", "profile-outlined", "profile-twotone", "project-filled", "project-outlined", "project-twotone", "property-safety-filled", "property-safety-outlined", "property-safety-twotone", "pull-request-outlined", "pushpin-filled", "pushpin-outlined", "pushpin-twotone", "qq-circle-filled", "qq-outlined", "qq-square-filled", "qrcode-outlined", "question-circle-filled", "question-circle-outlined", "question-circle-twotone", "question-outlined", "radar-chart-outlined", "radius-bottomleft-outlined", "radius-bottomright-outlined", "radius-setting-outlined", "radius-upleft-outlined", "radius-upright-outlined", "read-filled", "read-outlined", "reconciliation-filled", "reconciliation-outlined", "reconciliation-twotone", "red-envelope-filled", "red-envelope-outlined", "red-envelope-twotone", "reddit-circle-filled", "reddit-outlined", "reddit-square-filled", "redo-outlined", "reload-outlined", "rest-filled", "rest-outlined", "rest-twotone", "retweet-outlined", "right-circle-filled", "right-circle-outlined", "right-circle-twotone", "right-outlined", "right-square-filled", "right-square-outlined", "right-square-twotone", "rise-outlined", "robot-filled", "robot-outlined", "rocket-filled", "rocket-outlined", "rocket-twotone", "rollback-outlined", "rotate-left-outlined", "rotate-right-outlined", "safety-certificate-filled", "safety-certificate-outlined", "safety-certificate-twotone", "safety-outlined", "save-filled", "save-outlined", "save-twotone", "scan-outlined", "schedule-filled", "schedule-outlined", "schedule-twotone", "scissor-outlined", "search-outlined", "security-scan-filled", "security-scan-outlined", "security-scan-twotone", "select-outlined", "send-outlined", "setting-filled", "setting-outlined", "setting-twotone", "shake-outlined", "share-alt-outlined", "shop-filled", "shop-outlined", "shop-twotone", "shopping-cart-outlined", "shopping-filled", "shopping-outlined", "shopping-twotone", "shrink-outlined", "signal-filled", "sisternode-outlined", "sketch-circle-filled", "sketch-outlined", "sketch-square-filled", "skin-filled", "skin-outlined", "skin-twotone", "skype-filled", "skype-outlined", "slack-circle-filled", "slack-outlined", "slack-square-filled", "slack-square-outlined", "sliders-filled", "sliders-outlined", "sliders-twotone", "small-dash-outlined", "smile-filled", "smile-outlined", "smile-twotone", "snippets-filled", "snippets-outlined", "snippets-twotone", "solution-outlined", "sort-ascending-outlined", "sort-descending-outlined", "sound-filled", "sound-outlined", "sound-twotone", "split-cells-outlined", "star-filled", "star-outlined", "star-twotone", "step-backward-filled", "step-backward-outlined", "step-forward-filled", "step-forward-outlined", "stock-outlined", "stop-filled", "stop-outlined", "stop-twotone", "strikethrough-outlined", "subnode-outlined", "swap-left-outlined", "swap-outlined", "swap-right-outlined", "switcher-filled", "switcher-outlined", "switcher-twotone", "sync-outlined", "table-outlined", "tablet-filled", "tablet-outlined", "tablet-twotone", "tag-filled", "tag-outlined", "tag-twotone", "tags-filled", "tags-outlined", "tags-twotone", "taobao-circle-filled", "taobao-circle-outlined", "taobao-outlined", "taobao-square-filled", "team-outlined", "thunderbolt-filled", "thunderbolt-outlined", "thunderbolt-twotone", "to-top-outlined", "tool-filled", "tool-outlined", "tool-twotone", "trademark-circle-filled", "trademark-circle-outlined", "trademark-circle-twotone", "trademark-outlined", "transaction-outlined", "translation-outlined", "trophy-filled", "trophy-outlined", "trophy-twotone", "twitter-circle-filled", "twitter-outlined", "twitter-square-filled", "underline-outlined", "undo-outlined", "ungroup-outlined", "unlock-filled", "unlock-outlined", "unlock-twotone", "unordered-list-outlined", "up-circle-filled", "up-circle-outlined", "up-circle-twotone", "up-outlined", "up-square-filled", "up-square-outlined", "up-square-twotone", "upload-outlined", "usb-filled", "usb-outlined", "usb-twotone", "user-add-outlined", "user-delete-outlined", "user-outlined", "user-switch-outlined", "usergroup-add-outlined", "usergroup-delete-outlined", "verified-outlined", "vertical-align-bottom-outlined", "vertical-align-middle-outlined", "vertical-align-top-outlined", "vertical-left-outlined", "vertical-right-outlined", "video-camera-add-outlined", "video-camera-filled", "video-camera-outlined", "video-camera-twotone", "wallet-filled", "wallet-outlined", "wallet-twotone", "warning-filled", "warning-outlined", "warning-twotone", "wechat-filled", "wechat-outlined", "weibo-circle-filled", "weibo-circle-outlined", "weibo-outlined", "weibo-square-filled", "weibo-square-outlined", "whats-app-outlined", "wifi-outlined", "windows-filled", "windows-outlined", "woman-outlined", "yahoo-filled", "yahoo-outlined", "youtube-filled", "youtube-outlined", "yuque-filled", "yuque-outlined", "zhihu-circle-filled", "zhihu-outlined", "zhihu-square-filled", "zoom-in-outlined", "zoom-out-outlined"]}, {"prefix": "ep", "info": {"name": "Element Plus", "total": 293, "version": "2.3.1", "author": {"name": "Element Plus", "url": "https://github.com/element-plus/element-plus-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/element-plus/element-plus-icons/blob/main/packages/svg/package.json"}, "samples": ["home-filled", "partly-cloudy", "avatar"], "height": 32, "displayHeight": 16, "category": "General", "palette": false}, "icons": ["add-location", "aim", "alarm-clock", "apple", "arrow-down", "arrow-down-bold", "arrow-left", "arrow-left-bold", "arrow-right", "arrow-right-bold", "arrow-up", "arrow-up-bold", "avatar", "back", "baseball", "basketball", "bell", "bell-filled", "bicycle", "bottom", "bottom-left", "bottom-right", "bowl", "box", "briefcase", "brush", "brush-filled", "burger", "calendar", "camera", "camera-filled", "caret-bottom", "caret-left", "caret-right", "caret-top", "cellphone", "chat-dot-round", "chat-dot-square", "chat-line-round", "chat-line-square", "chat-round", "chat-square", "check", "checked", "cherry", "chicken", "chrome-filled", "circle-check", "circle-check-filled", "circle-close", "circle-close-filled", "circle-plus", "circle-plus-filled", "clock", "close", "close-bold", "cloudy", "coffee", "coffee-cup", "coin", "cold-drink", "collection", "collection-tag", "comment", "compass", "connection", "coordinate", "copy-document", "cpu", "credit-card", "crop", "d-arrow-left", "d-arrow-right", "d-caret", "data-analysis", "data-board", "data-line", "delete", "delete-filled", "delete-location", "dessert", "discount", "dish", "dish-dot", "document", "document-add", "document-checked", "document-copy", "document-delete", "document-remove", "download", "drizzling", "edit", "edit-pen", "eleme", "eleme-filled", "element-plus", "expand", "failed", "female", "files", "film", "filter", "finished", "first-aid-kit", "flag", "fold", "folder", "folder-add", "folder-checked", "folder-delete", "folder-opened", "folder-remove", "food", "football", "fork-spoon", "fries", "full-screen", "goblet", "goblet-full", "goblet-square", "goblet-square-full", "gold-medal", "goods", "goods-filled", "grape", "grid", "guide", "handbag", "headset", "help", "help-filled", "hide", "histogram", "home-filled", "hot-water", "house", "ice-cream", "ice-cream-round", "ice-cream-square", "ice-drink", "ice-tea", "info-filled", "iphone", "key", "knife-fork", "lightning", "link", "list", "loading", "location", "location-filled", "location-information", "lock", "lollipop", "magic-stick", "magnet", "male", "management", "map-location", "medal", "memo", "menu", "message", "message-box", "mic", "microphone", "milk-tea", "minus", "money", "monitor", "moon", "moon-night", "more", "more-filled", "mostly-cloudy", "mouse", "mug", "mute", "mute-notification", "no-smoking", "notebook", "notification", "odometer", "office-building", "open", "operation", "opportunity", "orange", "paperclip", "partly-cloudy", "pear", "phone", "phone-filled", "picture", "picture-filled", "picture-rounded", "pie-chart", "place", "platform", "plus", "pointer", "position", "postcard", "pouring", "present", "price-tag", "printer", "promotion", "quartz-watch", "question-filled", "rank", "reading", "reading-lamp", "refresh", "refresh-left", "refresh-right", "refrigerator", "remove", "remove-filled", "right", "scale-to-original", "school", "scissor", "search", "select", "sell", "semi-select", "service", "set-up", "setting", "share", "ship", "shop", "shopping-bag", "shopping-cart", "shopping-cart-full", "shopping-trolley", "smoking", "soccer", "sold-out", "sort", "sort-down", "sort-up", "stamp", "star", "star-filled", "stopwatch", "success-filled", "sugar", "suitcase", "suitcase-line", "sunny", "sunrise", "sunset", "switch", "switch-button", "switch-filled", "takeaway-box", "ticket", "tickets", "timer", "toilet-paper", "tools", "top", "top-left", "top-right", "trend-charts", "trophy", "trophy-base", "turn-off", "umbrella", "unlock", "upload", "upload-filled", "user", "user-filled", "van", "video-camera", "video-camera-filled", "video-pause", "video-play", "view", "wallet", "wallet-filled", "warn-triangle-filled", "warning", "warning-filled", "watch", "watermelon", "wind-power", "zoom-in", "zoom-out"]}, {"prefix": "flagpack", "info": {"name": "Flagpack", "total": 255, "version": "2.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Yummygum/flagpack-core"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/Yummygum/flagpack-core/blob/main/LICENSE"}, "samples": ["ci", "gb-ukm", "wf"], "height": 24, "category": "Maps / Flags", "palette": true}, "icons": ["ad", "ae", "af", "ag", "ai", "al", "am", "ao", "aq", "ar", "as", "at", "au", "aw", "ax", "az", "ba", "bb", "bd", "be", "bf", "bg", "bh", "bi", "bj", "bl", "bm", "bn", "bo", "bq-bo", "bq-sa", "bq-se", "br", "bs", "bt", "bv", "bw", "by", "bz", "ca", "cc", "cd", "cf", "cg", "ch", "ci", "ck", "cl", "cm", "cn", "co", "cr", "cu", "cv", "cw", "cx", "cy", "cz", "de", "dj", "dk", "dm", "do", "dz", "ec", "ee", "eg", "eh", "er", "es", "et", "fi", "fj", "fk", "fm", "fo", "fr", "ga", "gb", "gb-eng", "gb-nir", "gb-sct", "gb-ukm", "gb-wls", "gd", "ge", "gf", "gg", "gh", "gi", "gl", "gm", "gn", "gp", "gq", "gr", "gs", "gt", "gu", "gw", "gy", "hk", "hm", "hn", "hr", "ht", "hu", "id", "ie", "il", "im", "in", "io", "iq", "ir", "is", "it", "je", "jm", "jo", "jp", "ke", "kg", "kh", "ki", "km", "kn", "kp", "kr", "kw", "ky", "kz", "la", "lb", "lc", "li", "lk", "lr", "ls", "lt", "lu", "lv", "ly", "ma", "mc", "md", "me", "mf", "mg", "mh", "mk", "ml", "mm", "mn", "mo", "mp", "mq", "mr", "ms", "mt", "mu", "mv", "mw", "mx", "my", "mz", "na", "nc", "ne", "nf", "ng", "ni", "nl", "no", "np", "nr", "nu", "nz", "om", "pa", "pe", "pf", "pg", "ph", "pk", "pl", "pm", "pn", "pr", "ps", "pt", "pw", "py", "qa", "re", "ro", "rs", "ru", "rw", "sa", "sb", "sc", "sd", "se", "sg", "sh", "si", "sj", "sk", "sl", "sm", "sn", "so", "sr", "ss", "st", "sv", "sx", "sy", "sz", "tc", "td", "tf", "tg", "th", "tj", "tk", "tl", "tm", "tn", "to", "tr", "tt", "tv", "tw", "tz", "ua", "ug", "um", "us", "uy", "uz", "va", "vc", "ve", "vg", "vi", "vn", "vu", "wf", "ws", "ye", "yt", "za", "zm", "zw"]}, {"prefix": "icon-park", "info": {"name": "IconPark", "total": 2658, "version": "1.4.2", "author": {"name": "ByteDance", "url": "https://github.com/bytedance/IconPark"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/bytedance/IconPark/blob/master/LICENSE"}, "samples": ["add-one", "english-mustache", "basketball-clothes"], "height": 24, "category": "General", "palette": true}, "icons": ["a-cane", "abdominal", "abnormal", "acceleration", "accept-email", "acoustic", "activity-source", "ad", "ad-product", "add", "add-computer", "add-four", "add-item", "add-mode", "add-music", "add-one", "add-pic", "add-picture", "add-print", "add-subset", "add-subtract", "add-text", "add-text-two", "add-three", "add-two", "add-user", "add-web", "address-book", "adjacent-item", "adjustment", "adobe-illustrate", "adobe-indesign", "adobe-lightroom", "adobe-photoshop", "afferent", "afferent-four", "afferent-three", "afferent-two", "afro-pick", "agreement", "aiming", "air-bike", "air-conditioning", "airplane", "airplane-window", "airplane-window-one", "airplay", "airpods", "alarm", "alarm-clock", "align-bottom", "align-bottom-two", "align-horizontal-center-two", "align-horizontally", "align-left", "align-left-one", "align-left-two", "align-right", "align-right-one", "align-right-two", "align-text-both", "align-text-both-one", "align-text-bottom", "align-text-bottom-one", "align-text-center", "align-text-center-one", "align-text-left", "align-text-left-one", "align-text-middle", "align-text-middle-one", "align-text-right", "align-text-right-one", "align-text-top", "align-text-top-one", "align-top", "align-top-two", "align-vertical-center-two", "align-vertically", "alignment-bottom-center", "alignment-bottom-left", "alignment-bottom-right", "alignment-horizontal-bottom", "alignment-horizontal-center", "alignment-horizontal-top", "alignment-left-bottom", "alignment-left-center", "alignment-left-top", "alignment-right-bottom", "alignment-right-center", "alignment-right-top", "alignment-top-center", "alignment-top-left", "alignment-top-right", "alignment-vertical-center", "alignment-vertical-left", "alignment-vertical-right", "alipay", "all-application", "alphabetical-sorting", "alphabetical-sorting-two", "ambulance", "analysis", "anchor", "anchor-one", "anchor-round", "anchor-squre", "anchor-two", "android", "angry-face", "anguished-face", "announcement", "anti-corrosion", "aperture-priority", "api", "api-app", "app-store", "app-switch", "apple", "apple-one", "applet-closed", "application", "application-effect", "application-menu", "application-one", "application-two", "appointment", "aquarius", "arc-de-triomphe", "archers-bow", "archery", "area-map", "arena", "aries", "arithmetic", "arithmetic-buttons", "arithmetic-one", "arrow-circle-down", "arrow-circle-left", "arrow-circle-right", "arrow-circle-up", "arrow-down", "arrow-keys", "arrow-left", "arrow-left-down", "arrow-left-up", "arrow-right", "arrow-right-down", "arrow-right-up", "arrow-up", "assembly-line", "association", "asterisk", "asterisk-key", "astonished-face", "at-sign", "attention", "audio-file", "audit", "auto-focus", "auto-height-one", "auto-line-height", "auto-line-width", "auto-width", "auto-width-one", "avatar", "average", "aviation", "avocado", "avocado-one", "baby", "baby-app", "baby-bottle", "baby-car-seat", "baby-feet", "baby-meal", "baby-mobile", "baby-one", "baby-pants", "baby-sling", "baby-taste", "bachelor-cap", "bachelor-cap-one", "bachelor-cap-two", "back", "back-one", "background-color", "backpack", "bad", "bad-one", "bad-two", "badge", "badge-two", "badminton", "baggage-delay", "balance", "balance-one", "balance-two", "banana", "bank", "bank-card", "bank-card-one", "bank-card-two", "bank-transfer", "baokemeng", "bar-code", "barbecue", "barber-brush", "barber-clippers", "baseball", "baseball-bat", "baseball-cap", "basketball", "basketball-clothes", "basketball-one", "basketball-stand", "bat", "battery-charge", "battery-empty", "battery-failure", "battery-full", "battery-storage", "battery-tips", "battery-working", "battery-working-one", "beach-umbrella", "bear", "beauty", "beauty-instrument", "bedside", "bedside-two", "bee", "beer", "beer-mug", "behance", "bell-ring", "belt", "benz", "bezier-curve", "bib", "big-clock", "big-x", "bike", "bill", "bird", "birthday-cake", "bitcoin", "black-eight", "blackboard", "blade", "bless", "block", "block-eight", "block-five", "block-four", "block-nine", "block-one", "block-seven", "block-six", "block-ten", "block-three", "block-two", "blockchain", "blocks-and-arrows", "bloom", "blossom", "bluetooth", "boiler", "bolt-one", "bone", "book", "book-one", "book-open", "bookmark", "bookmark-one", "bookmark-three", "bookshelf", "booster-car-seat", "booth", "boots", "bottle", "bottle-one", "bottle-three", "bottle-two", "bottom-bar", "bottom-bar-one", "bow", "bowl", "bowl-one", "bowling", "box", "boxing", "boxing-one", "boy", "boy-one", "boy-stroller", "boy-two", "brain", "brake-pads", "branch", "branch-one", "branch-two", "brdige-three", "bread", "bread-machine", "bread-one", "breast-pump", "bridge-one", "bridge-two", "briefcase", "brightness", "bring-forward", "bring-to-front", "bring-to-front-one", "broadcast", "broadcast-one", "broadcast-radio", "browser", "browser-chrome", "browser-safari", "bubble-chart", "bug", "building-four", "building-one", "building-three", "building-two", "bullet-map", "bus", "bus-one", "bus-two", "butterfly", "buy", "bydesign", "bye", "bytedance", "bytedance-applets", "bytedance-mini-app", "cable-car", "cactus", "cake", "cake-five", "cake-four", "cake-one", "cake-three", "cake-two", "calculator", "calculator-one", "calendar", "calendar-dot", "calendar-thirty", "calendar-thirty-two", "calendar-three", "camera", "camera-five", "camera-four", "camera-one", "camera-three", "camera-two", "camp", "cancer", "candy", "canned-fruit", "capricornus", "car", "car-battery", "card-two", "cardioelectric", "carousel", "carousel-video", "carrot", "cast-screen", "castle", "cat", "category-management", "cattle", "cattle-zodiac", "caution", "cc", "cd", "ce-marking", "cell", "center-alignment", "certificate", "chafing-dish", "chafing-dish-one", "chair", "chair-one", "change", "change-date-sort", "charging-treasure", "chart-graph", "chart-histogram", "chart-histogram-one", "chart-histogram-two", "chart-line", "chart-line-area", "chart-pie", "chart-pie-one", "chart-proportion", "chart-ring", "chart-scatter", "chart-stock", "check", "check-correct", "check-in", "check-one", "check-small", "checkbox", "checkerboard", "checklist", "cheese", "chef-hat", "chef-hat-one", "cherry", "chess", "chess-one", "chest", "chicken", "chicken-leg", "chicken-zodiac", "child-with-pacifier", "children-cap", "children-pyramid", "chili", "chimney", "chinese", "chinese-one", "chinese-pavilion", "chip", "chopping-board", "chopsticks-fork", "christmas-tree", "christmas-tree-one", "church-one", "church-two", "circle-double-down", "circle-double-left", "circle-double-right", "circle-double-up", "circle-five-line", "circle-four", "circle-four-line", "circle-house", "circle-left-down", "circle-left-up", "circle-right-down", "circle-right-up", "circle-three", "circle-two-line", "circles-and-triangles", "circles-seven", "circular-connection", "circus", "city", "city-gate", "city-one", "clap", "classroom", "clear", "clear-format", "click", "click-tap", "click-tap-two", "click-to-fold", "clipboard", "clock-tower", "close", "close-one", "close-remind", "close-small", "close-wifi", "clothes-briefs", "clothes-cardigan", "clothes-crew-neck", "clothes-diapers", "clothes-gloves", "clothes-gloves-two", "clothes-hoodie", "clothes-pants", "clothes-pants-short", "clothes-pants-sweat", "clothes-short-sleeve", "clothes-skates", "clothes-suit", "clothes-sweater", "clothes-turtleneck", "clothes-windbreaker", "cloud-storage", "cloudy", "cloudy-night", "clue", "coat-hanger", "cocktail", "coconut-tree", "code", "code-brackets", "code-computer", "code-download", "code-laptop", "code-one", "coffee-machine", "cola", "collapse-text-input", "collect-computer", "collect-laptop", "collect-picture", "collection-files", "collection-records", "color-card", "color-filter", "column", "comb", "come", "command", "comment", "comment-one", "comments", "commodity", "communication", "commuter-bag", "compass", "compass-one", "components", "composition", "compression", "computer", "computer-one", "concept-sharing", "concern", "conditioner", "cone", "cones", "config", "confounded-face", "confused-face", "connect", "connect-address-one", "connect-address-two", "connection", "connection-arrow", "connection-box", "connection-point", "connection-point-two", "consignment", "consume", "contrast", "contrast-view", "contrast-view-circle", "control", "converging-gateway", "cook", "cooking", "cooking-pot", "cool", "cooperative-handshake", "coordinate-system", "copy", "copy-link", "copy-one", "copyright", "corner-down-left", "corner-down-right", "corner-left-down", "corner-left-up", "corner-right-down", "corner-right-up", "corner-up-left", "corner-up-right", "coronavirus", "correct", "cosmetic-brush", "coupon", "court", "cpu", "crab", "creation-date-sort", "creative", "credit", "crib", "croissant", "cross-ring", "cross-ring-two", "cross-society", "crown", "crown-three", "crown-two", "cruise", "crying-baby", "cube", "cube-five", "cube-four", "cube-three", "cube-two", "cup", "cup-four", "cup-one", "curling", "currency", "curve-adjustment", "customer", "cutting", "cutting-one", "cuvette", "cycle", "cycle-arrow", "cycle-movement", "cycle-one", "cylinder", "damage-map", "dark-mode", "dashboard", "dashboard-car", "dashboard-one", "dashboard-two", "data", "data-all", "data-arrival", "data-display", "data-file", "data-four", "data-lock", "data-null", "data-one", "data-screen", "data-server", "data-sheet", "data-switching", "data-three", "data-two", "data-user", "database-alert", "database-code", "database-config", "database-download", "database-enter", "database-fail", "database-first", "database-forbid", "database-lock", "database-network", "database-network-point", "database-point", "database-position", "database-power", "database-proportion", "database-search", "database-setting", "database-success", "database-sync", "database-time", "date-comes-back", "deadline-sort", "death-star", "deeplink", "deer", "degree-hat", "delete", "delete-five", "delete-four", "delete-key", "delete-mode", "delete-one", "delete-themes", "delete-three", "delete-two", "delivery", "deposit", "descend", "desk-lamp", "desk-lamp-one", "detection", "devices", "diamond", "diamond-necklace", "diamond-one", "diamond-ring", "diamond-three", "diamond-two", "diamonds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diapers-one", "difference-set", "digital-watches", "direction", "direction-adjustment", "direction-adjustment-three", "direction-adjustment-two", "disabaled-web", "disabled-computer", "disabled-laptop", "disabled-picture", "disappointed-face", "discovery-index", "disk", "disk-one", "disk-two", "dislike", "dislike-two", "display", "distortion", "distraught-face", "distribute-horizontal-spacing", "distribute-horizontally", "distribute-vertical-spacing", "distribute-vertically", "dividing-line", "dividing-line-one", "diving", "diving-bottle", "diving-suit", "division", "dizzy-face", "doc-add", "doc-detail", "doc-fail", "doc-search", "doc-search-two", "doc-success", "document-folder", "dog", "dog-zodiac", "dollar", "dolphin", "dome", "dome-light", "done-all", "dongchedi", "door-handle", "dot", "double-bed", "double-down", "double-left", "double-right", "double-up", "doughnut", "down", "down-c", "down-one", "down-picture", "down-small", "down-square", "down-two", "download", "download-computer", "download-four", "download-laptop", "download-one", "download-three", "download-two", "download-web", "drag", "dragon-zodiac", "dribble", "drink", "drone", "drone-one", "drop-down-list", "drop-shadow-down", "drop-shadow-left", "drop-shadow-right", "drop-shadow-up", "dropbox", "drumstick", "dubai", "duck", "dumbbel-line", "dumbbell", "dvi", "eagle", "earth", "easy", "ecg", "edit", "edit-movie", "edit-name", "edit-one", "edit-two", "editing", "editor", "eeg", "effects", "efferent-four", "efferent-three", "egg", "egg-one", "eggplant", "eiffel-tower", "eight-key", "electric-drill", "electric-iron", "electric-wave", "electrocardiogram", "electronic-door-lock", "electronic-locks-close", "electronic-locks-open", "electronic-pen", "elephant", "elevator", "email-block", "email-delect", "email-down", "email-fail", "email-lock", "email-push", "email-search", "email-security", "email-successfully", "emotion-happy", "emotion-unhappy", "empty", "end-time-sort", "endless", "endocrine", "endpoint-displacement", "endpoint-flat", "endpoint-round", "endpoint-square", "energy-socket", "engineering-brand", "engineering-vehicle", "english", "english-mustache", "enquire", "enter-key", "enter-key-one", "enter-the-keyboard", "entertainment", "envelope", "envelope-one", "equal-ratio", "equalizer", "erase", "error", "error-computer", "error-picture", "error-prompt", "escalators", "ethernet-off", "ethernet-on", "every-user", "excel", "excel-one", "exchange", "exchange-four", "exchange-one", "exchange-three", "exchange-two", "exclude-selection", "exclusive-gateway", "expand-down", "expand-down-one", "expand-left", "expand-left-and-right", "expand-right", "expand-text-input", "expand-up", "expenses", "expenses-one", "experiment", "experiment-one", "export", "express-delivery", "expressionless-face", "extend", "external-transmission", "eyebrow", "eyes", "f-eight-key", "f-five-key", "f-four-key", "f-n-key", "f-nine-key", "f-one-key", "f-seven-key", "f-six-key", "f-three-key", "f-two-key", "f-zero-key", "face-powder", "face-recognition", "face-with-smiling-open-eyes", "face-without-mouth", "facebook", "facebook-one", "facetime", "faceu", "facial-cleanser", "facial-mask", "factory-building", "fail-picture", "family", "fan", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "feelgood", "feelgood-one", "feiyu", "female", "fence-one", "fence-two", "ferris-wheel", "figma", "figma-component", "figma-flatten-selection", "figma-mask", "figma-reset-instance", "file-addition", "file-addition-one", "file-cabinet", "file-code", "file-code-one", "file-collection", "file-collection-one", "file-conversion", "file-conversion-one", "file-date", "file-date-one", "file-display", "file-display-one", "file-doc", "file-editing", "file-editing-one", "file-excel", "file-failed", "file-failed-one", "file-focus", "file-focus-one", "file-gif", "file-hash", "file-hash-one", "file-hiding", "file-hiding-one", "file-jpg", "file-lock", "file-lock-one", "file-music", "file-music-one", "file-pdf", "file-pdf-one", "file-ppt", "file-protection", "file-protection-one", "file-quality", "file-quality-one", "file-question", "file-removal", "file-removal-one", "file-search", "file-search-one", "file-search-two", "file-settings", "file-settings-one", "file-staff", "file-staff-one", "file-success", "file-success-one", "file-text", "file-text-one", "file-tips", "file-tips-one", "file-txt", "file-txt-one", "file-withdrawal", "file-withdrawal-one", "file-word", "file-zip", "fill", "film", "filter", "filter-one", "finance", "financing", "financing-one", "financing-two", "find", "find-one", "fingernail", "fingerprint", "fingerprint-three", "fingerprint-two", "fire", "fire-extinguisher", "fire-extinguisher-one", "fire-two", "fireworks", "first", "first-aid-kit", "fish", "fish-one", "fishing", "fist", "fitness", "five", "five-ellipses", "five-five", "five-key", "five-star-badge", "flag", "flash-payment", "flashlamp", "flashlight", "flask", "flight-airflow", "flight-safety", "flip-camera", "flip-horizontally", "flip-vertically", "flirt", "float", "floor-tile", "fm", "focus", "focus-one", "fog", "fold-up-one", "folder", "folder-block", "folder-block-one", "folder-close", "folder-code", "folder-code-one", "folder-conversion", "folder-conversion-one", "folder-download", "folder-failed", "folder-failed-one", "folder-focus", "folder-focus-one", "folder-lock", "folder-lock-one", "folder-minus", "folder-music", "folder-music-one", "folder-one", "folder-open", "folder-plus", "folder-protection", "folder-protection-one", "folder-quality", "folder-quality-one", "folder-search", "folder-search-one", "folder-settings", "folder-settings-one", "folder-success", "folder-success-one", "folder-upload", "folder-withdrawal", "folder-withdrawal-one", "follow-up-date-sort", "font-search", "font-size", "font-size-two", "foot", "football", "forbid", "fork", "fork-spoon", "form", "form-one", "format", "format-brush", "formula", "foundation-makeup", "four", "four-arrows", "four-four", "four-key", "four-leaves", "four-point-connection", "four-round-point-connection", "foursquare", "freeze-column", "freeze-line", "freezing-line-column", "french-fries", "friends-circle", "frigate", "frog", "frowning-face-whit-open-mouth", "fruiter", "full-dress-longuette", "full-screen", "full-screen-one", "full-screen-play", "full-screen-two", "full-selection", "fullwidth", "funds", "future-build-one", "future-build-three", "future-build-two", "game", "game-console", "game-console-one", "game-emoji", "game-handle", "game-ps", "game-three", "game-two", "gamepad", "garage", "garlic", "gas", "gastrointestinal", "gate", "gate-machine", "gauze", "gavel", "gemini", "general-branch", "geometric-flowers", "germs", "ghost", "gift", "gift-bag", "gift-box", "girl", "girl-one", "girl-two", "github", "github-one", "gitlab", "glasses", "glasses-one", "glasses-three", "globe", "glove", "go-ahead", "go-end", "go-on", "go-start", "goblet", "goblet-cracking", "goblet-full", "goblet-one", "gold-medal", "gold-medal-two", "golf-course", "gongfu", "good", "good-one", "good-two", "google", "google-ads", "gopro", "gps", "graphic-design", "graphic-design-two", "graphic-stitching", "graphic-stitching-four", "graphic-stitching-three", "great-wall", "green-house", "green-new-energy", "grid-four", "grid-nine", "grid-sixteen", "grid-three", "grid-two", "grimacing-face", "grinning-face", "grinning-face-with-open-mouth", "grinning-face-with-squinting-eyes", "grinning-face-with-tightly-closed-eyes", "grinning-face-with-tightly-closed-eyes-open-mouth", "group", "guide-board", "gymnastics", "gymnastics-one", "h", "h1", "h2", "h3", "hair-brush", "hair-clip", "hair-dryer", "hair-dryer-one", "halo", "hamburger", "hamburger-button", "hamburger-one", "hammer-and-anvil", "hand-cream", "hand-down", "hand-drag", "hand-left", "hand-painted-plate", "hand-right", "hand-up", "handbag", "handheld", "handle-a", "handle-b", "handle-c", "handle-down", "handle-left", "handle-right", "handle-round", "handle-square", "handle-triangle", "handle-up", "handle-x", "handle-y", "handle-z", "hands", "handwashing", "handwashing-fluid", "hanfu-chinese-style", "hanger", "hanger-one", "hanger-two", "hard-disk", "hard-disk-one", "harm", "hashtag-key", "hat", "hdd", "hdmi-cable", "hdmi-connector", "headphone-sound", "headset", "headset-one", "headset-two", "headwear", "health", "health-products", "healthy-recognition", "heart", "heart-ballon", "heart-rate", "heartbeat", "heater-resistor", "heavy-metal", "heavy-rain", "heavy-wind", "helmet", "helmet-one", "help", "helpcenter", "hexagon-one", "hexagon-strip", "hexagonal", "hi", "high-heeled-shoes", "high-light", "high-speed-rail", "hippo", "histogram", "history", "history-query", "hockey", "hold", "hold-interface", "hold-seeds", "holding-hands", "holy-sword", "home", "home-two", "homestay", "honey", "honey-one", "horizontal-spacing-between-items", "horizontal-tidy-up", "horizontally-centered", "horse-zodiac", "hospital", "hospital-bed", "hospital-four", "hospital-three", "hospital-two", "hot-air-balloon", "hot-pot", "hot-pot-one", "hotel", "hotel-do-not-clean", "hotel-please-clean", "hourglass", "hourglass-full", "hourglass-null", "html-five", "hunting-gear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i-mac", "icecream", "icecream-five", "icecream-four", "icecream-one", "icecream-three", "icecream-two", "id-card", "id-card-h", "id-card-v", "image-files", "imbalance", "import-and-export", "in-flight", "inbox", "inbox-download-r", "inbox-in", "inbox-out", "inbox-r", "inbox-success", "inbox-success-r", "inbox-upload-r", "inclusive-gateway", "income", "income-one", "incoming", "increase", "increase-the-scale", "indent-left", "indent-right", "index-finger", "induction-lock", "industrial-scales", "info", "infusion", "injection", "inline", "inner-shadow-bottom-left", "inner-shadow-bottom-right", "inner-shadow-down", "inner-shadow-left", "inner-shadow-right", "inner-shadow-top-left", "inner-shadow-top-right", "inner-shadow-up", "insert-card", "insert-table", "inspection", "instagram", "instagram-one", "install", "instruction", "intercom", "intermediate-mode", "internal-data", "internal-expansion", "internal-reduction", "internal-transmission", "international", "intersect-selection", "intersection", "invalid-files", "invert-camera", "invert-left", "invert-right", "ios-face-recognition", "ipad", "ipad-one", "iphone", "ipo", "iron", "iron-disable", "iron-three", "iron-two", "iwatch", "iwatch-one", "iwatch-two", "jewelry", "ji<PERSON><PERSON><PERSON><PERSON>", "journey", "joystick", "juice", "jump", "kagi-map", "kettle", "kettle-one", "key", "key-one", "key-two", "keyboard", "keyboard-one", "keyhole", "keyline", "kitchen-knife", "knife-fork", "koala-bear", "kungfu", "label", "ladder", "ladder-one", "lamp", "land-surveying", "landing", "landscape", "laptop", "laptop-computer", "laptop-one", "lark", "lark-one", "lattice-pattern", "layers", "layout-five", "layout-four", "layout-one", "layout-three", "layout-two", "leaf", "leaves", "leaves-one", "leaves-two", "led-diode", "left", "left-alignment", "left-and-right-branch", "left-bar", "left-branch", "left-c", "left-expand", "left-one", "left-small", "left-small-down", "left-small-up", "left-square", "left-two", "lemon", "lens-alignment", "leo", "level", "level-adjustment", "level-eight-title", "level-five-title", "level-four-title", "level-nine-title", "level-seven-title", "level-six-title", "libra", "lifebuoy", "light", "light-house", "light-member", "light-rain", "lightning", "like", "lincoln", "link", "link-break", "link-cloud", "link-cloud-faild", "link-cloud-sucess", "link-four", "link-in", "link-interrupt", "link-left", "link-one", "link-out", "link-right", "link-three", "link-two", "lip-gloss", "lip-tattoo", "lipstick", "lipstick-one", "liqueur", "list", "list-add", "list-alphabet", "list-bottom", "list-checkbox", "list-fail", "list-middle", "list-numbers", "list-one", "list-success", "list-top", "list-two", "list-view", "loading", "loading-four", "loading-one", "loading-three", "loading-two", "local", "local-pin", "local-two", "lock", "lock-one", "locking-computer", "locking-laptop", "locking-picture", "locking-web", "log", "login", "logout", "lollipop", "loop-once", "lotion", "lotus", "loudly-crying-face", "loudly-crying-face-whit-open-mouth", "love-and-help", "lower-branch", "luggage", "luminous", "lung", "mac-finder", "macadamia-nut", "magic", "magic-hat", "magic-wand", "magnet", "mail", "mail-download", "mail-edit", "mail-open", "mail-package", "mail-review", "mail-unpacking", "maill-one", "makeups", "male", "mall-bag", "manual-gear", "many-to-many", "map-distance", "map-draw", "map-road", "map-road-two", "map-two", "margin", "margin-one", "mark", "market", "market-analysis", "mascara", "mask", "mask-one", "mask-two", "maslow-pyramids", "massage-chair", "massage-chair-one", "massage-table", "master", "material", "material-three", "material-two", "maximum", "maya", "mayura-gesture", "me", "measuring-cup", "medal-one", "mediaeditor", "medical-box", "medical-files", "medical-mark", "medication-time", "medicine-bottle", "medicine-bottle-one", "medicine-chest", "memory", "memory-card", "memory-card-one", "memory-one", "men-jacket", "menu-fold", "menu-fold-one", "menu-unfold", "menu-unfold-one", "merge", "merge-cells", "message", "message-emoji", "message-failed", "message-one", "message-privacy", "message-search", "message-security", "message-sent", "message-success", "message-unread", "messages", "messages-one", "micro-sd", "micro-slr-camera", "microphone", "microphone-one", "microscope", "microscope-one", "microwave-oven", "microwaves", "middle-finger", "milk", "milk-one", "min", "mind-mapping", "mindmap-list", "mindmap-map", "mini-sd-card", "minus", "minus-the-bottom", "minus-the-top", "mirror", "mirror-one", "mirror-two", "misaligned-semicircle", "<PERSON><PERSON><PERSON><PERSON>", "modify", "modify-two", "monitor", "monitor-camera", "monitor-off", "monitor-one", "monitor-two", "monkey", "monkey-zodiac", "monument-one", "monument-two", "moon", "more", "more-app", "more-four", "more-one", "more-three", "more-two", "mosaic", "mountain", "mounted", "mouse", "mouse-one", "mouse-zodiac", "mouth", "move", "move-in", "move-in-one", "move-one", "movie", "movie-board", "moving-picture", "multi-circular", "multi-function-knife", "multi-picture-carousel", "multi-rectangle", "multi-ring", "multi-triangular", "multi-triangular-four", "multi-triangular-three", "multi-triangular-two", "multicast", "multilayer-sphere", "muscle", "museum-one", "museum-two", "music", "music-cd", "music-list", "music-menu", "music-one", "music-rhythm", "mute", "nail-polish", "nail-polish-one", "nasal", "natural-mode", "navigation", "necktie", "needle", "negative-dynamics", "nested-arrows", "nests", "network-drive", "network-tree", "neural", "neutral-face", "new-afferent", "new-computer", "new-<PERSON><PERSON><PERSON><PERSON><PERSON>", "new-efferent", "new-lark", "new-picture", "newlybuild", "newspaper-folding", "next", "nine-key", "nine-points-connected", "nintendo-switch", "nmr", "no-shooting", "node-flat", "node-round", "node-square", "noodles", "notebook", "notebook-and-pen", "notebook-one", "notepad", "notes", "nuclear-plant", "nurse-cap", "nut", "nutrition", "oceanengine", "octagon", "off-screen", "off-screen-one", "off-screen-two", "oil-industry", "okay", "one", "one-key", "one-one", "one-third-rotation", "one-to-many", "one-to-one", "onesies", "online-meeting", "open", "open-an-account", "open-door", "open-one", "optimize", "optional", "orange", "orange-one", "orange-station", "order", "ordered-list", "orthopedic", "oscillator", "other", "outbound", "outdoor", "outgoing", "oval-love", "oval-love-two", "oval-one", "oven", "oven-tray", "overall-reduction", "owl", "pacifier", "pad", "page", "page-template", "pagoda", "paint", "painted-eggshell", "painted-screen", "palace", "palm", "panda", "pangle", "panorama-horizontal", "panties", "paper-money", "paper-money-two", "paper-ship", "paperclip", "parabola", "parachute", "paragraph-alphabet", "paragraph-break", "paragraph-break-two", "paragraph-cut", "paragraph-rectangle", "paragraph-round", "paragraph-triangle", "paragraph-unfold", "parallel-gateway", "parallelogram", "parenting-book", "parking", "party-balloon", "passport", "passport-one", "pause", "pause-one", "pay-code", "pay-code-one", "pay-code-two", "payment-method", "paypal", "peach", "pear", "pearl-of-the-orient", "peas", "pencil", "pennant", "pentagon-one", "people", "people-bottom", "people-bottom-card", "people-delete", "people-delete-one", "people-download", "people-left", "people-minus", "people-minus-one", "people-plus", "people-plus-one", "people-right", "people-safe", "people-safe-one", "people-search", "people-search-one", "people-speak", "people-top", "people-top-card", "people-unknown", "people-upload", "peoples", "peoples-two", "percentage", "performance", "perfume", "perfumer-bottle", "period", "permissions", "personal-collection", "personal-privacy", "perspective", "pesticide", "petrol", "phone", "phone-booth", "phone-call", "phone-incoming", "phone-incoming-one", "phone-missed", "phone-off", "phone-one", "phone-outgoing", "phone-outgoing-one", "phone-telephone", "phone-two", "phone-video-call", "phonograph", "photograph", "piano", "pic", "pic-one", "picture", "picture-album", "picture-one", "pie", "pie-five", "pie-four", "pie-one", "pie-seven", "pie-six", "pie-three", "pie-two", "pig", "pig-zodiac", "pigeon", "pill", "pills", "pin", "pineapple", "pinwheel", "pisces", "pivot-table", "plan", "planet", "plastic-surgery", "platte", "play", "play-basketball", "play-cycle", "play-once", "play-one", "play-two", "play-volleyball", "play-wrong", "playback-progress", "plug", "plug-one", "plus", "plus-cross", "point", "point-out", "pokeball-one", "poker", "popcorn", "popcorn-one", "positive-dynamics", "pot", "potentiometer", "pound", "pound-sign", "pouting-face", "powder", "power", "power-supply", "power-supply-one", "powerpoint", "ppt", "pregnant-women", "preschool", "prescription", "press", "preview-close", "preview-close-one", "preview-open", "printer", "printer-one", "printer-two", "prison", "process-line", "projector", "projector-one", "projector-three", "projector-two", "proportional-scaling", "protect", "protection", "public-toilet", "pull-door", "pull-requests", "pumpkin", "pure-natural", "push-door", "pushpin", "puzzle", "pyramid", "pyramid-one", "qingniao-clue", "qiyeh<PERSON>", "quadrangular-pyramid", "quadrilateral", "quote", "rabbit", "rabbit-zodiac", "radar", "radar-chart", "radar-three", "radar-two", "radiation", "radio", "radio-nanny", "radio-one", "radio-two", "radish", "radish-one", "railway", "ranking", "ranking-list", "rattle", "rattle-one", "razor", "read-book", "receive", "receiver", "recent-views-sort", "record", "record-disc", "record-player", "rectangle", "rectangle-one", "rectangle-small", "rectangle-tear", "rectangle-x", "rectangular-circular-connection", "rectangular-circular-separation", "rectangular-vertebra", "recycle-bin", "recycling", "recycling-pool", "red-cross", "red-envelope", "red-envelopes", "redo", "reduce", "reduce-decimal-places", "reduce-one", "reduce-two", "reduce-user", "reel", "refraction", "refresh", "refresh-one", "refrigerator", "reject", "relational-graph", "relieved-face", "reload", "remind", "remind-disable", "remote-control", "remote-control-one", "renal", "renault", "repair", "replay-five", "replay-music", "report", "repositioning", "resistor", "respect", "resting", "retro-bag", "return", "reverse-lens", "reverse-lens-one", "reverse-operation-in", "reverse-operation-out", "reverse-rotation", "rice", "riding", "riding-one", "right", "right-angle", "right-bar", "right-branch", "right-branch-one", "right-branch-two", "right-c", "right-expand", "right-one", "right-run", "right-small", "right-small-down", "right-small-up", "right-square", "right-two", "right-user", "ring", "ring-one", "rings", "ripple", "road", "road-cone", "road-one", "road-sign", "road-sign-both", "robot", "robot-one", "robot-two", "rock", "rock-gesture", "rocket", "rocket-one", "rocking-horse", "rollerskates", "romper", "rope-skipping", "rope-skipping-one", "rotate", "rotate-one", "rotating-add", "rotating-forward", "rotation", "rotation-horizontal", "rotation-one", "rotation-vertical", "round", "round-caliper", "round-distortion", "round-mask", "round-socket", "round-trip", "router", "router-one", "row-height", "rowing", "rs-male", "rss", "rugby", "rugby-one", "rule-two", "ruler", "ruler-one", "run-left", "s-turn-down", "s-turn-left", "s-turn-right", "s-turn-up", "safe-retrieval", "sagittarius", "sailboat", "sailboat-one", "sailing", "sales-report", "sandals", "sandstorm", "sandwich", "sandwich-one", "sapling", "save", "save-one", "scale", "scale-one", "scallion", "scan", "scan-code", "scan-setting", "scanning", "scanning-two", "scatter-alignment", "schedule", "school", "scissors", "scoreboard", "scorpio", "screen-rotation", "screenshot", "screenshot-one", "screenshot-two", "screwdriver", "sd", "sd-card", "seal", "search", "seat", "security", "security-stall", "seedling", "selected", "selected-focus", "selfie", "send", "send-backward", "send-email", "send-one", "send-to-back", "sent-to-back", "seo", "seo-folder", "server", "set-off", "setting", "setting-computer", "setting-config", "setting-laptop", "setting-one", "setting-three", "setting-two", "setting-web", "seven-key", "shade", "shake", "share", "share-one", "share-sys", "share-three", "share-two", "shaver", "shaver-one", "shaving", "sheep-zodiac", "shield", "shield-add", "ship", "shop", "shopping", "shopping-bag", "shopping-bag-one", "shopping-cart", "shopping-cart-add", "shopping-cart-del", "shopping-cart-one", "shopping-cart-two", "shopping-mall", "short-skirt", "shorts", "shoulder-bag", "shovel", "shovel-one", "shower-head", "shrimp", "shuffle", "shuffle-one", "shutter-priority", "sickbed", "signal", "signal-one", "signal-strength", "signal-tower", "signal-tower-one", "sim", "sim-card", "single-bed", "sinusoid", "sippy-cup", "six", "six-circular-connection", "six-key", "six-points", "skate", "skates", "skating", "sketch", "skiing-nordic", "skull", "slave", "sleaves", "sleep", "sleep-one", "sleep-two", "slide", "slide-two", "sliding-horizontal", "sliding-vertical", "slightly-frowning-face-whit-open-mouth", "slightly-smiling-face", "slippers", "slippers-one", "sly-face-whit-smile", "smart-optimization", "smiling-face", "smiling-face-with-squinting-eyes", "snacks", "snake-zodiac", "snow", "snowflake", "snowman", "soap-bubble", "soccer", "soccer-one", "socks", "sofa", "sofa-two", "softball", "solar-energy", "solar-energy-one", "solid-state-disk", "sorcerer-hat", "sort", "sort-amount-down", "sort-amount-up", "sort-four", "sort-one", "sort-three", "sort-two", "sound", "sound-one", "sound-wave", "source-code", "soybean-milk-maker", "spa-candle", "space-colony", "spanner", "speaker", "speaker-one", "speed", "speed-one", "sperm", "sphere", "spider-man", "spikedshoes", "spinning-top", "split", "split-branch", "split-cells", "split-turn-down-left", "split-turn-down-right", "spoon", "sport", "sporting", "square", "square-small", "ssd", "stack-light", "stamp", "stand-up", "stapler", "star", "star-one", "start-time-sort", "steering-wheel", "s<PERSON><PERSON><PERSON>", "stereo-nesting", "stereo-one", "stereo-perspective", "stethoscope", "stickers", "stock-market", "stopwatch", "stopwatch-start", "storage-card-one", "storage-card-two", "straight-razor", "straw-hat", "stretching", "stretching-one", "strikethrough", "strongbox", "subtract-selection", "subtract-selection-one", "subway", "success", "success-picture", "sum", "sun", "sun-hat", "sun-one", "sunbath", "sunny", "sunrise", "sunset", "sunshade", "surprised-face-with-open-big-mouth", "surprised-face-with-open-mouth", "surveillance-cameras", "surveillance-cameras-one", "surveillance-cameras-two", "swallow", "sweater", "swimming-pool", "swimming-ring", "swimsuit", "swing", "swipe", "switch", "switch-button", "switch-contrast", "switch-nintendo", "switch-one", "switch-themes", "switch-track", "switching-done", "symbol", "symbol-double-x", "symmetry", "sync", "system", "t-shirt", "table", "table-file", "table-lamp", "table-report", "tabletennis", "tag", "tag-one", "tailoring", "tailoring-two", "taj-mahal", "take-off", "take-off-one", "<PERSON><PERSON><PERSON>", "tape", "tape-measure", "target", "target-one", "target-two", "taurus", "taxi", "tea", "tea-drink", "teapot", "teeth", "telegram", "telescope", "tencent-qq", "tennis", "tent", "tent-banner", "terminal", "termination-file", "terrace", "test-tube", "text", "text-bold", "text-italic", "text-message", "text-recognition", "text-rotation-down", "text-rotation-left", "text-rotation-none", "text-rotation-up", "text-style", "text-style-one", "text-underline", "text-wrap-overflow", "text-wrap-truncation", "textarea", "texture", "texture-two", "the-single-shoulder-bag", "theater", "theme", "thermometer", "thermometer-one", "thermos-cup", "thin", "thinking-problem", "three", "three-d-glasses", "three-hexagons", "three-key", "three-slashes", "three-three", "three-triangles", "thumbs-down", "thumbs-up", "thunderbolt", "thunderstorm", "thunderstorm-one", "ticket", "ticket-one", "tickets-checked", "tickets-one", "tickets-two", "tiger-zodiac", "tiktok", "time", "timed-mail", "timeline", "timer", "tips", "tips-one", "tire-swing", "title-level", "to-bottom", "to-bottom-one", "to-left", "to-right", "to-top", "to-top-one", "toilet", "tomato", "tool", "toolkit", "top-bar", "topbuzz", "topic", "topic-discussion", "torch", "tour-bus", "towel", "tower", "tower-of-babel", "tower-of-pisa", "toxins", "trace", "trademark", "traditional-chinese-medicine", "train", "transaction", "transaction-order", "transfer", "transfer-data", "transform", "translate", "translation", "transport", "transporter", "trapezoid", "tray", "treadmill", "treadmill-one", "treadmill-two", "treasure-chest", "tree", "tree-diagram", "tree-list", "tree-one", "tree-two", "trend", "trend-two", "trending-down", "trending-up", "triangle", "triangle-round-rectangle", "triangle-ruler", "triangular-pyramid", "trophy", "trousers-bell-bottoms", "truck", "trumpet", "trunk", "tub", "tuchong", "tumblr", "turkey", "turn-around", "turn-off-bluetooth", "turn-on", "tv", "tv-one", "twitter", "two", "two-dimensional-code", "two-dimensional-code-one", "two-dimensional-code-two", "two-ellipses", "two-fingers", "two-hands", "two-key", "two-semicircles", "two-triangles", "two-triangles-two", "two-two", "type-drive", "u-disk", "u-turn-down", "u-turn-left", "u-turn-right", "u-turn-up", "ulike<PERSON>", "umbrella", "umbrella-one", "umbrella-two", "undo", "ungroup", "unicast", "union-selection", "universal", "unlike", "unlink", "unlock", "unlock-one", "unordered-list", "up", "up-and-down", "up-c", "up-one", "up-small", "up-square", "up-two", "update-rotation", "upload", "upload-computer", "upload-laptop", "upload-logs", "upload-one", "upload-picture", "upload-three", "upload-two", "upload-web", "upside-down-face", "usb", "usb-memory-stick", "usb-micro-one", "usb-micro-two", "usb-one", "usb-type-c", "user", "user-business", "user-positioning", "user-to-user-transmission", "uterus", "vacation", "vacuum-cleaner", "vegetable-basket", "vegetables", "vertical-spacing-between-items", "vertical-tidy-up", "vertical-timeline", "vertically-centered", "vest", "vial", "vicia-faba", "video", "video-conference", "video-file", "video-one", "video-two", "videocamera", "videocamera-one", "viencharts", "view-grid-card", "view-grid-detail", "view-grid-list", "view-list", "viewfinder", "vigo", "vip", "vip-one", "virgo", "virtual-reality-glasses", "voice", "voice-input", "voice-message", "voice-off", "voice-one", "voicemail", "volkswagen", "volleyball", "volume-down", "volume-mute", "volume-notice", "volume-small", "volume-up", "vr-glasses", "waistline", "wallet", "wallet-one", "wallet-three", "wallet-two", "warehousing", "washing-machine", "washing-machine-one", "watch", "watch-one", "water", "water-level", "water-no", "water-rate", "water-rate-two", "waterfalls-h", "waterfalls-v", "watermelon", "watermelon-one", "waterpolo", "waterpolo-one", "waves", "waves-left", "waves-right", "weary-face", "web-page", "webcam", "wechat", "weibo", "weight", "weightlifting", "weixin-cards-offers", "weixin-favorites", "weixin-games", "weixin-market", "weixin-mini-app", "weixin-people-nearby", "weixin-scan", "weixin-search", "weixin-shake", "weixin-top-stories", "whale", "wheelchair", "whirlwind", "whistling", "whole-site-accelerator", "wifi", "wind", "wind-turbine", "windmill", "windmill-one", "windmill-two", "windows", "wingsuit-flying", "winking-face", "winking-face-with-open-eyes", "woman", "women", "women-coat", "woolen-hat", "word", "workbench", "worker", "world", "worried-face", "write", "writing-fluently", "wrong-user", "xia<PERSON>u", "xiaodu-home", "xigua", "xingfuli", "<PERSON><PERSON><PERSON>", "yep", "youtobe", "youtube", "zero-key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zip", "zoom", "zoom-in", "zoom-internal", "zoom-out"]}, {"prefix": "mdi", "info": {"name": "Material Design Icons", "total": 7447, "author": {"name": "Pictogrammers", "url": "https://github.com/Templarian/MaterialDesign"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Templarian/MaterialDesign/blob/master/LICENSE"}, "samples": ["account-check", "bell-alert-outline", "calendar-edit"], "height": 24, "category": "General", "palette": false}, "icons": ["ab-testing", "abacus", "abjad-arabic", "abjad-hebrew", "abugi<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "abugida-thai", "access-point", "access-point-check", "access-point-minus", "access-point-network", "access-point-network-off", "access-point-off", "access-point-plus", "access-point-remove", "account", "account-alert", "account-alert-outline", "account-arrow-down", "account-arrow-down-outline", "account-arrow-left", "account-arrow-left-outline", "account-arrow-right", "account-arrow-right-outline", "account-arrow-up", "account-arrow-up-outline", "account-badge", "account-badge-outline", "account-box", "account-box-edit-outline", "account-box-minus-outline", "account-box-multiple", "account-box-multiple-outline", "account-box-outline", "account-box-plus-outline", "account-cancel", "account-cancel-outline", "account-card", "account-card-outline", "account-cash", "account-cash-outline", "account-check", "account-check-outline", "account-child", "account-child-circle", "account-child-outline", "account-circle", "account-circle-outline", "account-clock", "account-clock-outline", "account-cog", "account-cog-outline", "account-convert", "account-convert-outline", "account-cowboy-hat", "account-cowboy-hat-outline", "account-credit-card", "account-credit-card-outline", "account-details", "account-details-outline", "account-edit", "account-edit-outline", "account-eye", "account-eye-outline", "account-file", "account-file-outline", "account-file-text", "account-file-text-outline", "account-filter", "account-filter-outline", "account-group", "account-group-outline", "account-hard-hat", "account-hard-hat-outline", "account-heart", "account-heart-outline", "account-injury", "account-injury-outline", "account-key", "account-key-outline", "account-lock", "account-lock-open", "account-lock-open-outline", "account-lock-outline", "account-minus", "account-minus-outline", "account-multiple", "account-multiple-check", "account-multiple-check-outline", "account-multiple-minus", "account-multiple-minus-outline", "account-multiple-outline", "account-multiple-plus", "account-multiple-plus-outline", "account-multiple-remove", "account-multiple-remove-outline", "account-music", "account-music-outline", "account-network", "account-network-off", "account-network-off-outline", "account-network-outline", "account-off", "account-off-outline", "account-outline", "account-plus", "account-plus-outline", "account-question", "account-question-outline", "account-reactivate", "account-reactivate-outline", "account-remove", "account-remove-outline", "account-school", "account-school-outline", "account-search", "account-search-outline", "account-settings", "account-settings-outline", "account-settings-variant", "account-star", "account-star-outline", "account-supervisor", "account-supervisor-circle", "account-supervisor-circle-outline", "account-supervisor-outline", "account-switch", "account-switch-outline", "account-sync", "account-sync-outline", "account-tag", "account-tag-outline", "account-tie", "account-tie-hat", "account-tie-hat-outline", "account-tie-outline", "account-tie-voice", "account-tie-voice-off", "account-tie-voice-off-outline", "account-tie-voice-outline", "account-tie-woman", "account-voice", "account-voice-off", "account-wrench", "account-wrench-outline", "accusoft", "ad-choices", "adchoices", "adjust", "adobe", "advertisements", "advertisements-off", "air-conditioner", "air-filter", "air-horn", "air-humidifier", "air-humidifier-off", "air-purifier", "air-purifier-off", "airbag", "airballoon", "airballoon-outline", "airplane", "airplane-alert", "airplane-check", "airplane-clock", "airplane-cog", "airplane-edit", "airplane-landing", "airplane-marker", "airplane-minus", "airplane-off", "airplane-plus", "airplane-remove", "airplane-search", "airplane-settings", "airplane-takeoff", "airport", "alarm", "alarm-bell", "alarm-check", "alarm-light", "alarm-light-off", "alarm-light-off-outline", "alarm-light-outline", "alarm-multiple", "alarm-note", "alarm-note-off", "alarm-off", "alarm-panel", "alarm-panel-outline", "alarm-plus", "alarm-snooze", "album", "alert", "alert-box", "alert-box-outline", "alert-circle", "alert-circle-check", "alert-circle-check-outline", "alert-circle-outline", "alert-decagram", "alert-decagram-outline", "alert-minus", "alert-minus-outline", "alert-octagon", "alert-octagon-outline", "alert-octagram", "alert-octagram-outline", "alert-outline", "alert-plus", "alert-plus-outline", "alert-remove", "alert-remove-outline", "alert-rhombus", "alert-rhombus-outline", "alien", "alien-outline", "align-horizontal-center", "align-horizontal-distribute", "align-horizontal-left", "align-horizontal-right", "align-vertical-bottom", "align-vertical-center", "align-vertical-distribute", "align-vertical-top", "all-inclusive", "all-inclusive-box", "all-inclusive-box-outline", "allergy", "allo", "alpha", "alpha-a", "alpha-a-box", "alpha-a-box-outline", "alpha-a-circle", "alpha-a-circle-outline", "alpha-b", "alpha-b-box", "alpha-b-box-outline", "alpha-b-circle", "alpha-b-circle-outline", "alpha-c", "alpha-c-box", "alpha-c-box-outline", "alpha-c-circle", "alpha-c-circle-outline", "alpha-d", "alpha-d-box", "alpha-d-box-outline", "alpha-d-circle", "alpha-d-circle-outline", "alpha-e", "alpha-e-box", "alpha-e-box-outline", "alpha-e-circle", "alpha-e-circle-outline", "alpha-f", "alpha-f-box", "alpha-f-box-outline", "alpha-f-circle", "alpha-f-circle-outline", "alpha-g", "alpha-g-box", "alpha-g-box-outline", "alpha-g-circle", "alpha-g-circle-outline", "alpha-h", "alpha-h-box", "alpha-h-box-outline", "alpha-h-circle", "alpha-h-circle-outline", "alpha-i", "alpha-i-box", "alpha-i-box-outline", "alpha-i-circle", "alpha-i-circle-outline", "alpha-j", "alpha-j-box", "alpha-j-box-outline", "alpha-j-circle", "alpha-j-circle-outline", "alpha-k", "alpha-k-box", "alpha-k-box-outline", "alpha-k-circle", "alpha-k-circle-outline", "alpha-l", "alpha-l-box", "alpha-l-box-outline", "alpha-l-circle", "alpha-l-circle-outline", "alpha-m", "alpha-m-box", "alpha-m-box-outline", "alpha-m-circle", "alpha-m-circle-outline", "alpha-n", "alpha-n-box", "alpha-n-box-outline", "alpha-n-circle", "alpha-n-circle-outline", "alpha-o", "alpha-o-box", "alpha-o-box-outline", "alpha-o-circle", "alpha-o-circle-outline", "alpha-p", "alpha-p-box", "alpha-p-box-outline", "alpha-p-circle", "alpha-p-circle-outline", "alpha-q", "alpha-q-box", "alpha-q-box-outline", "alpha-q-circle", "alpha-q-circle-outline", "alpha-r", "alpha-r-box", "alpha-r-box-outline", "alpha-r-circle", "alpha-r-circle-outline", "alpha-s", "alpha-s-box", "alpha-s-box-outline", "alpha-s-circle", "alpha-s-circle-outline", "alpha-t", "alpha-t-box", "alpha-t-box-outline", "alpha-t-circle", "alpha-t-circle-outline", "alpha-u", "alpha-u-box", "alpha-u-box-outline", "alpha-u-circle", "alpha-u-circle-outline", "alpha-v", "alpha-v-box", "alpha-v-box-outline", "alpha-v-circle", "alpha-v-circle-outline", "alpha-w", "alpha-w-box", "alpha-w-box-outline", "alpha-w-circle", "alpha-w-circle-outline", "alpha-x", "alpha-x-box", "alpha-x-box-outline", "alpha-x-circle", "alpha-x-circle-outline", "alpha-y", "alpha-y-box", "alpha-y-box-outline", "alpha-y-circle", "alpha-y-circle-outline", "alpha-z", "alpha-z-box", "alpha-z-box-outline", "alpha-z-circle", "alpha-z-circle-outline", "alphabet-aurebesh", "alphabet-cyrillic", "alphabet-greek", "alphabet-latin", "alphabet-piqad", "alphabet-tengwar", "alphabetical", "alphabetical-off", "alphabetical-variant", "alphabetical-variant-off", "altimeter", "amazon", "amazon-alexa", "amazon-drive", "ambulance", "ammunition", "ampersand", "amplifier", "amplifier-off", "anchor", "android", "android-auto", "android-debug-bridge", "android-head", "android-messages", "android-studio", "angle-acute", "angle-obtuse", "angle-right", "angular", "<PERSON><PERSON>s", "animation", "animation-outline", "animation-play", "animation-play-outline", "ansible", "antenna", "anvil", "apache-kafka", "api", "api-off", "apple", "apple-finder", "apple-icloud", "apple-ios", "apple-keyboard-caps", "apple-keyboard-command", "apple-keyboard-control", "apple-keyboard-option", "apple-keyboard-shift", "apple-safari", "application", "application-array", "application-array-outline", "application-braces", "application-braces-outline", "application-brackets", "application-brackets-outline", "application-cog", "application-cog-outline", "application-edit", "application-edit-outline", "application-export", "application-import", "application-outline", "application-parentheses", "application-parentheses-outline", "application-settings", "application-settings-outline", "application-variable", "application-variable-outline", "appnet", "approximately-equal", "approximately-equal-box", "apps", "apps-box", "arch", "archive", "archive-alert", "archive-alert-outline", "archive-arrow-down", "archive-arrow-down-outline", "archive-arrow-up", "archive-arrow-up-outline", "archive-cancel", "archive-cancel-outline", "archive-check", "archive-check-outline", "archive-clock", "archive-clock-outline", "archive-cog", "archive-cog-outline", "archive-edit", "archive-edit-outline", "archive-eye", "archive-eye-outline", "archive-lock", "archive-lock-open", "archive-lock-open-outline", "archive-lock-outline", "archive-marker", "archive-marker-outline", "archive-minus", "archive-minus-outline", "archive-music", "archive-music-outline", "archive-off", "archive-off-outline", "archive-outline", "archive-plus", "archive-plus-outline", "archive-refresh", "archive-refresh-outline", "archive-remove", "archive-remove-outline", "archive-search", "archive-search-outline", "archive-settings", "archive-settings-outline", "archive-star", "archive-star-outline", "archive-sync", "archive-sync-outline", "arm-flex", "arm-flex-outline", "arrange-bring-forward", "arrange-bring-to-front", "arrange-send-backward", "arrange-send-to-back", "arrow-all", "arrow-bottom-left", "arrow-bottom-left-bold-box", "arrow-bottom-left-bold-box-outline", "arrow-bottom-left-bold-outline", "arrow-bottom-left-thick", "arrow-bottom-left-thin", "arrow-bottom-left-thin-circle-outline", "arrow-bottom-right", "arrow-bottom-right-bold-box", "arrow-bottom-right-bold-box-outline", "arrow-bottom-right-bold-outline", "arrow-bottom-right-thick", "arrow-bottom-right-thin", "arrow-bottom-right-thin-circle-outline", "arrow-collapse", "arrow-collapse-all", "arrow-collapse-down", "arrow-collapse-horizontal", "arrow-collapse-left", "arrow-collapse-right", "arrow-collapse-up", "arrow-collapse-vertical", "arrow-decision", "arrow-decision-auto", "arrow-decision-auto-outline", "arrow-decision-outline", "arrow-down", "arrow-down-bold", "arrow-down-bold-box", "arrow-down-bold-box-outline", "arrow-down-bold-circle", "arrow-down-bold-circle-outline", "arrow-down-bold-hexagon-outline", "arrow-down-bold-outline", "arrow-down-box", "arrow-down-circle", "arrow-down-circle-outline", "arrow-down-drop-circle", "arrow-down-drop-circle-outline", "arrow-down-left", "arrow-down-left-bold", "arrow-down-right", "arrow-down-right-bold", "arrow-down-thick", "arrow-down-thin", "arrow-down-thin-circle-outline", "arrow-expand", "arrow-expand-all", "arrow-expand-down", "arrow-expand-horizontal", "arrow-expand-left", "arrow-expand-right", "arrow-expand-up", "arrow-expand-vertical", "arrow-horizontal-lock", "arrow-left", "arrow-left-bold", "arrow-left-bold-box", "arrow-left-bold-box-outline", "arrow-left-bold-circle", "arrow-left-bold-circle-outline", "arrow-left-bold-hexagon-outline", "arrow-left-bold-outline", "arrow-left-bottom", "arrow-left-bottom-bold", "arrow-left-box", "arrow-left-circle", "arrow-left-circle-outline", "arrow-left-drop-circle", "arrow-left-drop-circle-outline", "arrow-left-right", "arrow-left-right-bold", "arrow-left-right-bold-outline", "arrow-left-thick", "arrow-left-thin", "arrow-left-thin-circle-outline", "arrow-left-top", "arrow-left-top-bold", "arrow-oscillating", "arrow-oscillating-off", "arrow-projectile", "arrow-projectile-multiple", "arrow-right", "arrow-right-bold", "arrow-right-bold-box", "arrow-right-bold-box-outline", "arrow-right-bold-circle", "arrow-right-bold-circle-outline", "arrow-right-bold-hexagon-outline", "arrow-right-bold-outline", "arrow-right-bottom", "arrow-right-bottom-bold", "arrow-right-box", "arrow-right-circle", "arrow-right-circle-outline", "arrow-right-drop-circle", "arrow-right-drop-circle-outline", "arrow-right-thick", "arrow-right-thin", "arrow-right-thin-circle-outline", "arrow-right-top", "arrow-right-top-bold", "arrow-split-horizontal", "arrow-split-vertical", "arrow-top-left", "arrow-top-left-bold-box", "arrow-top-left-bold-box-outline", "arrow-top-left-bold-outline", "arrow-top-left-bottom-right", "arrow-top-left-bottom-right-bold", "arrow-top-left-thick", "arrow-top-left-thin", "arrow-top-left-thin-circle-outline", "arrow-top-right", "arrow-top-right-bold-box", "arrow-top-right-bold-box-outline", "arrow-top-right-bold-outline", "arrow-top-right-bottom-left", "arrow-top-right-bottom-left-bold", "arrow-top-right-thick", "arrow-top-right-thin", "arrow-top-right-thin-circle-outline", "arrow-u-down-left", "arrow-u-down-left-bold", "arrow-u-down-right", "arrow-u-down-right-bold", "arrow-u-left-bottom", "arrow-u-left-bottom-bold", "arrow-u-left-top", "arrow-u-left-top-bold", "arrow-u-right-bottom", "arrow-u-right-bottom-bold", "arrow-u-right-top", "arrow-u-right-top-bold", "arrow-u-up-left", "arrow-u-up-left-bold", "arrow-u-up-right", "arrow-u-up-right-bold", "arrow-up", "arrow-up-bold", "arrow-up-bold-box", "arrow-up-bold-box-outline", "arrow-up-bold-circle", "arrow-up-bold-circle-outline", "arrow-up-bold-hexagon-outline", "arrow-up-bold-outline", "arrow-up-box", "arrow-up-circle", "arrow-up-circle-outline", "arrow-up-down", "arrow-up-down-bold", "arrow-up-down-bold-outline", "arrow-up-drop-circle", "arrow-up-drop-circle-outline", "arrow-up-left", "arrow-up-left-bold", "arrow-up-right", "arrow-up-right-bold", "arrow-up-thick", "arrow-up-thin", "arrow-up-thin-circle-outline", "arrow-vertical-lock", "artboard", "artstation", "aspect-ratio", "assistant", "asterisk", "asterisk-circle-outline", "at", "atlassian", "atm", "atom", "atom-variant", "attachment", "attachment-check", "attachment-lock", "attachment-minus", "attachment-off", "attachment-plus", "attachment-remove", "atv", "audio-input-rca", "audio-input-stereo-minijack", "audio-input-xlr", "audio-video", "audio-video-off", "augmented-reality", "aurora", "auto-download", "auto-fix", "auto-mode", "auto-upload", "autorenew", "autorenew-off", "av-timer", "awning", "awning-outline", "aws", "axe", "axe-battle", "axis", "axis-arrow", "axis-arrow-info", "axis-arrow-lock", "axis-lock", "axis-x-arrow", "axis-x-arrow-lock", "axis-x-rotate-clockwise", "axis-x-rotate-counterclockwise", "axis-x-y-arrow-lock", "axis-y-arrow", "axis-y-arrow-lock", "axis-y-rotate-clockwise", "axis-y-rotate-counterclockwise", "axis-z-arrow", "axis-z-arrow-lock", "axis-z-rotate-clockwise", "axis-z-rotate-counterclockwise", "babel", "baby", "baby-bottle", "baby-bottle-outline", "baby-buggy", "baby-buggy-off", "baby-carriage", "baby-carriage-off", "baby-face", "baby-face-outline", "backburger", "backspace", "backspace-outline", "backspace-reverse", "backspace-reverse-outline", "backup-restore", "bacteria", "bacteria-outline", "badge-account", "badge-account-alert", "badge-account-alert-outline", "badge-account-horizontal", "badge-account-horizontal-outline", "badge-account-outline", "badminton", "bag-carry-on", "bag-carry-on-check", "bag-carry-on-off", "bag-checked", "bag-personal", "bag-personal-off", "bag-personal-off-outline", "bag-personal-outline", "bag-personal-plus", "bag-personal-plus-outline", "bag-personal-tag", "bag-personal-tag-outline", "bag-suitcase", "bag-suitcase-off", "bag-suitcase-off-outline", "bag-suitcase-outline", "baguette", "balcony", "balloon", "ballot", "ballot-outline", "ballot-recount", "ballot-recount-outline", "bandage", "bandcamp", "bank", "bank-check", "bank-circle", "bank-circle-outline", "bank-minus", "bank-off", "bank-off-outline", "bank-outline", "bank-plus", "bank-remove", "bank-transfer", "bank-transfer-in", "bank-transfer-out", "barcode", "barcode-off", "barcode-scan", "barley", "barley-off", "barn", "barrel", "barrel-outline", "baseball", "baseball-bat", "baseball-diamond", "baseball-diamond-outline", "baseball-outline", "basecamp", "bash", "basket", "basket-check", "basket-check-outline", "basket-fill", "basket-minus", "basket-minus-outline", "basket-off", "basket-off-outline", "basket-outline", "basket-plus", "basket-plus-outline", "basket-remove", "basket-remove-outline", "basket-unfill", "basketball", "basketball-hoop", "basketball-hoop-outline", "bat", "bathtub", "bathtub-outline", "battery", "battery-10", "battery-10-bluetooth", "battery-20", "battery-20-bluetooth", "battery-30", "battery-30-bluetooth", "battery-40", "battery-40-bluetooth", "battery-50", "battery-50-bluetooth", "battery-60", "battery-60-bluetooth", "battery-70", "battery-70-bluetooth", "battery-80", "battery-80-bluetooth", "battery-90", "battery-90-bluetooth", "battery-alert", "battery-alert-bluetooth", "battery-alert-variant", "battery-alert-variant-outline", "battery-arrow-down", "battery-arrow-down-outline", "battery-arrow-up", "battery-arrow-up-outline", "battery-bluetooth", "battery-bluetooth-variant", "battery-charging", "battery-charging-10", "battery-charging-100", "battery-charging-20", "battery-charging-30", "battery-charging-40", "battery-charging-50", "battery-charging-60", "battery-charging-70", "battery-charging-80", "battery-charging-90", "battery-charging-high", "battery-charging-low", "battery-charging-medium", "battery-charging-outline", "battery-charging-wireless", "battery-charging-wireless-10", "battery-charging-wireless-20", "battery-charging-wireless-30", "battery-charging-wireless-40", "battery-charging-wireless-50", "battery-charging-wireless-60", "battery-charging-wireless-70", "battery-charging-wireless-80", "battery-charging-wireless-90", "battery-charging-wireless-alert", "battery-charging-wireless-outline", "battery-check", "battery-check-outline", "battery-clock", "battery-clock-outline", "battery-heart", "battery-heart-outline", "battery-heart-variant", "battery-high", "battery-lock", "battery-lock-open", "battery-low", "battery-medium", "battery-minus", "battery-minus-outline", "battery-minus-variant", "battery-negative", "battery-off", "battery-off-outline", "battery-outline", "battery-plus", "battery-plus-outline", "battery-plus-variant", "battery-positive", "battery-remove", "battery-remove-outline", "battery-standard", "battery-sync", "battery-sync-outline", "battery-unknown", "battery-unknown-bluetooth", "battlenet", "beach", "beaker", "beaker-alert", "beaker-alert-outline", "beaker-check", "beaker-check-outline", "beaker-minus", "beaker-minus-outline", "beaker-outline", "beaker-plus", "beaker-plus-outline", "beaker-question", "beaker-question-outline", "beaker-remove", "beaker-remove-outline", "beam", "beats", "bed", "bed-clock", "bed-double", "bed-double-outline", "bed-empty", "bed-king", "bed-king-outline", "bed-outline", "bed-queen", "bed-queen-outline", "bed-single", "bed-single-outline", "bee", "bee-flower", "beehive-off-outline", "beehive-outline", "beekeeper", "beer", "beer-outline", "behance", "bell", "bell-alert", "bell-alert-outline", "bell-badge", "bell-badge-outline", "bell-cancel", "bell-cancel-outline", "bell-check", "bell-check-outline", "bell-circle", "bell-circle-outline", "bell-cog", "bell-cog-outline", "bell-minus", "bell-minus-outline", "bell-off", "bell-off-outline", "bell-outline", "bell-plus", "bell-plus-outline", "bell-remove", "bell-remove-outline", "bell-ring", "bell-ring-outline", "bell-sleep", "bell-sleep-outline", "bench", "bench-back", "beta", "betamax", "biathlon", "bicycle", "bicycle-basket", "bicycle-cargo", "bicycle-electric", "bicycle-penny-farthing", "bike", "bike-fast", "bike-pedal", "bike-pedal-clipless", "bike-pedal-mountain", "billboard", "billiards", "billiards-rack", "binoculars", "bio", "biohazard", "bird", "bitbucket", "bitcoin", "black-mesa", "blackberry", "blender", "blender-outline", "blender-software", "blinds", "blinds-horizontal", "blinds-horizontal-closed", "blinds-open", "blinds-vertical", "blinds-vertical-closed", "block-helper", "blogger", "blood-bag", "bluetooth", "bluetooth-audio", "bluetooth-connect", "bluetooth-off", "bluetooth-settings", "bluetooth-transfer", "blur", "blur-linear", "blur-off", "blur-radial", "bolt", "bomb", "bomb-off", "bone", "bone-off", "book", "book-account", "book-account-outline", "book-alert", "book-alert-outline", "book-alphabet", "book-arrow-down", "book-arrow-down-outline", "book-arrow-left", "book-arrow-left-outline", "book-arrow-right", "book-arrow-right-outline", "book-arrow-up", "book-arrow-up-outline", "book-cancel", "book-cancel-outline", "book-check", "book-check-outline", "book-clock", "book-clock-outline", "book-cog", "book-cog-outline", "book-cross", "book-edit", "book-edit-outline", "book-education", "book-education-outline", "book-heart", "book-heart-outline", "book-information-variant", "book-lock", "book-lock-open", "book-lock-open-outline", "book-lock-outline", "book-marker", "book-marker-outline", "book-minus", "book-minus-multiple", "book-minus-multiple-outline", "book-minus-outline", "book-multiple", "book-multiple-minus", "book-multiple-outline", "book-multiple-plus", "book-multiple-remove", "book-multiple-variant", "book-music", "book-music-outline", "book-off", "book-off-outline", "book-open", "book-open-blank-variant", "book-open-blank-variant-outline", "book-open-outline", "book-open-page-variant", "book-open-page-variant-outline", "book-open-variant", "book-open-variant-outline", "book-outline", "book-play", "book-play-outline", "book-plus", "book-plus-multiple", "book-plus-multiple-outline", "book-plus-outline", "book-refresh", "book-refresh-outline", "book-remove", "book-remove-multiple", "book-remove-multiple-outline", "book-remove-outline", "book-search", "book-search-outline", "book-settings", "book-settings-outline", "book-sync", "book-sync-outline", "book-variant", "book-variant-multiple", "bookmark", "bookmark-box", "bookmark-box-multiple", "bookmark-box-multiple-outline", "bookmark-box-outline", "bookmark-check", "bookmark-check-outline", "bookmark-minus", "bookmark-minus-outline", "bookmark-multiple", "bookmark-multiple-outline", "bookmark-music", "bookmark-music-outline", "bookmark-off", "bookmark-off-outline", "bookmark-outline", "bookmark-plus", "bookmark-plus-outline", "bookmark-remove", "bookmark-remove-outline", "bookshelf", "boom-gate", "boom-gate-alert", "boom-gate-alert-outline", "boom-gate-arrow-down", "boom-gate-arrow-down-outline", "boom-gate-arrow-up", "boom-gate-arrow-up-outline", "boom-gate-outline", "boom-gate-up", "boom-gate-up-outline", "boombox", "boomerang", "bootstrap", "border-all", "border-all-variant", "border-bottom", "border-bottom-variant", "border-color", "border-horizontal", "border-inside", "border-left", "border-left-variant", "border-none", "border-none-variant", "border-outside", "border-radius", "border-right", "border-right-variant", "border-style", "border-top", "border-top-variant", "border-vertical", "bottle-soda", "bottle-soda-classic", "bottle-soda-classic-outline", "bottle-soda-outline", "bottle-tonic", "bottle-tonic-outline", "bottle-tonic-plus", "bottle-tonic-plus-outline", "bottle-tonic-skull", "bottle-tonic-skull-outline", "bottle-wine", "bottle-wine-outline", "bow-arrow", "bow-tie", "bowl", "bowl-mix", "bowl-mix-outline", "bowl-outline", "bowling", "box", "box-cutter", "box-cutter-off", "box-download", "box-shadow", "box-upload", "boxing-glove", "boxing-gloves", "braille", "brain", "bread-slice", "bread-slice-outline", "bridge", "briefcase", "briefcase-account", "briefcase-account-outline", "briefcase-arrow-left-right", "briefcase-arrow-left-right-outline", "briefcase-arrow-up-down", "briefcase-arrow-up-down-outline", "briefcase-check", "briefcase-check-outline", "briefcase-clock", "briefcase-clock-outline", "briefcase-download", "briefcase-download-outline", "briefcase-edit", "briefcase-edit-outline", "briefcase-eye", "briefcase-eye-outline", "briefcase-minus", "briefcase-minus-outline", "briefcase-off", "briefcase-off-outline", "briefcase-outline", "briefcase-plus", "briefcase-plus-outline", "briefcase-remove", "briefcase-remove-outline", "briefcase-search", "briefcase-search-outline", "briefcase-upload", "briefcase-upload-outline", "briefcase-variant", "briefcase-variant-off", "briefcase-variant-off-outline", "briefcase-variant-outline", "brightness", "brightness-1", "brightness-2", "brightness-3", "brightness-4", "brightness-5", "brightness-6", "brightness-7", "brightness-auto", "brightness-percent", "broadcast", "broadcast-off", "broom", "brush", "brush-off", "brush-outline", "brush-variant", "bucket", "bucket-outline", "buffer", "buffet", "bug", "bug-check", "bug-check-outline", "bug-outline", "bug-pause", "bug-pause-outline", "bug-play", "bug-play-outline", "bug-stop", "bug-stop-outline", "bugle", "bulkhead-light", "bulldozer", "bullet", "bulletin-board", "bullhorn", "bullhorn-outline", "bullhorn-variant", "bullhorn-variant-outline", "bullseye", "bullseye-arrow", "bulma", "bunk-bed", "bunk-bed-outline", "bus", "bus-alert", "bus-articulated-end", "bus-articulated-front", "bus-clock", "bus-double-decker", "bus-electric", "bus-marker", "bus-multiple", "bus-school", "bus-side", "bus-sign", "bus-stop", "bus-stop-covered", "bus-stop-uncovered", "bus-wrench", "butterfly", "butterfly-outline", "button-cursor", "button-pointer", "cabin-a-frame", "cable-data", "cached", "cactus", "cake", "cake-layered", "cake-variant", "cake-variant-outline", "calculator", "calculator-off", "calculator-variant", "calculator-variant-outline", "calendar", "calendar-account", "calendar-account-outline", "calendar-alert", "calendar-alert-outline", "calendar-arrow-left", "calendar-arrow-right", "calendar-badge", "calendar-badge-outline", "calendar-blank", "calendar-blank-multiple", "calendar-blank-outline", "calendar-check", "calendar-check-outline", "calendar-clock", "calendar-clock-outline", "calendar-collapse-horizontal", "calendar-collapse-horizontal-outline", "calendar-cursor", "calendar-cursor-outline", "calendar-edit", "calendar-edit-outline", "calendar-end", "calendar-end-outline", "calendar-expand-horizontal", "calendar-expand-horizontal-outline", "calendar-export", "calendar-export-outline", "calendar-filter", "calendar-filter-outline", "calendar-heart", "calendar-heart-outline", "calendar-import", "calendar-import-outline", "calendar-lock", "calendar-lock-open", "calendar-lock-open-outline", "calendar-lock-outline", "calendar-minus", "calendar-minus-outline", "calendar-month", "calendar-month-outline", "calendar-multiple", "calendar-multiple-check", "calendar-multiselect", "calendar-multiselect-outline", "calendar-outline", "calendar-plus", "calendar-plus-outline", "calendar-question", "calendar-question-outline", "calendar-range", "calendar-range-outline", "calendar-refresh", "calendar-refresh-outline", "calendar-remove", "calendar-remove-outline", "calendar-search", "calendar-search-outline", "calendar-select", "calendar-star", "calendar-star-four-points", "calendar-star-outline", "calendar-start", "calendar-start-outline", "calendar-sync", "calendar-sync-outline", "calendar-text", "calendar-text-outline", "calendar-today", "calendar-today-outline", "calendar-week", "calendar-week-begin", "calendar-week-begin-outline", "calendar-week-end", "calendar-week-end-outline", "calendar-week-outline", "calendar-weekend", "calendar-weekend-outline", "call-made", "call-merge", "call-missed", "call-received", "call-split", "camcorder", "camcorder-off", "camera", "camera-account", "camera-burst", "camera-control", "camera-document", "camera-document-off", "camera-enhance", "camera-enhance-outline", "camera-flip", "camera-flip-outline", "camera-focus", "camera-front", "camera-front-variant", "camera-gopro", "camera-image", "camera-iris", "camera-lock", "camera-lock-open", "camera-lock-open-outline", "camera-lock-outline", "camera-marker", "camera-marker-outline", "camera-metering-center", "camera-metering-matrix", "camera-metering-partial", "camera-metering-spot", "camera-off", "camera-off-outline", "camera-outline", "camera-party-mode", "camera-plus", "camera-plus-outline", "camera-rear", "camera-rear-variant", "camera-retake", "camera-retake-outline", "camera-switch", "camera-switch-outline", "camera-timer", "camera-wireless", "camera-wireless-outline", "campfire", "cancel", "candelabra", "candelabra-fire", "candle", "candy", "candy-off", "candy-off-outline", "candy-outline", "candycane", "cannabis", "cannabis-off", "caps-lock", "car", "car-2-plus", "car-3-plus", "car-arrow-left", "car-arrow-right", "car-back", "car-battery", "car-brake-abs", "car-brake-alert", "car-brake-fluid-level", "car-brake-hold", "car-brake-low-pressure", "car-brake-parking", "car-brake-retarder", "car-brake-temperature", "car-brake-worn-linings", "car-child-seat", "car-clock", "car-clutch", "car-cog", "car-connected", "car-convertable", "car-convertible", "car-coolant-level", "car-cruise-control", "car-defrost-front", "car-defrost-rear", "car-door", "car-door-lock", "car-door-lock-open", "car-electric", "car-electric-outline", "car-emergency", "car-esp", "car-estate", "car-hatchback", "car-info", "car-key", "car-lifted-pickup", "car-light-alert", "car-light-dimmed", "car-light-fog", "car-light-high", "car-limousine", "car-multiple", "car-off", "car-outline", "car-parking-lights", "car-pickup", "car-search", "car-search-outline", "car-seat", "car-seat-cooler", "car-seat-heater", "car-select", "car-settings", "car-shift-pattern", "car-side", "car-speed-limiter", "car-sports", "car-tire-alert", "car-traction-control", "car-turbocharger", "car-wash", "car-windshield", "car-windshield-outline", "car-wireless", "car-wrench", "carabiner", "caravan", "card", "card-account-details", "card-account-details-outline", "card-account-details-star", "card-account-details-star-outline", "card-account-mail", "card-account-mail-outline", "card-account-phone", "card-account-phone-outline", "card-bulleted", "card-bulleted-off", "card-bulleted-off-outline", "card-bulleted-outline", "card-bulleted-settings", "card-bulleted-settings-outline", "card-minus", "card-minus-outline", "card-multiple", "card-multiple-outline", "card-off", "card-off-outline", "card-outline", "card-plus", "card-plus-outline", "card-remove", "card-remove-outline", "card-search", "card-search-outline", "card-text", "card-text-outline", "cards", "cards-club", "cards-club-outline", "cards-diamond", "cards-diamond-outline", "cards-heart", "cards-heart-outline", "cards-outline", "cards-playing", "cards-playing-club", "cards-playing-club-multiple", "cards-playing-club-multiple-outline", "cards-playing-club-outline", "cards-playing-diamond", "cards-playing-diamond-multiple", "cards-playing-diamond-multiple-outline", "cards-playing-diamond-outline", "cards-playing-heart", "cards-playing-heart-multiple", "cards-playing-heart-multiple-outline", "cards-playing-heart-outline", "cards-playing-outline", "cards-playing-spade", "cards-playing-spade-multiple", "cards-playing-spade-multiple-outline", "cards-playing-spade-outline", "cards-spade", "cards-spade-outline", "cards-variant", "carrot", "cart", "cart-arrow-down", "cart-arrow-right", "cart-arrow-up", "cart-check", "cart-heart", "cart-minus", "cart-off", "cart-outline", "cart-percent", "cart-plus", "cart-remove", "cart-variant", "case-sensitive-alt", "cash", "cash-100", "cash-check", "cash-clock", "cash-edit", "cash-fast", "cash-lock", "cash-lock-open", "cash-marker", "cash-minus", "cash-multiple", "cash-off", "cash-plus", "cash-refund", "cash-register", "cash-remove", "cash-sync", "cash-usd", "cash-usd-outline", "cassette", "cast", "cast-audio", "cast-audio-variant", "cast-connected", "cast-education", "cast-off", "cast-variant", "castle", "cat", "cctv", "cctv-off", "ceiling-fan", "ceiling-fan-light", "ceiling-light", "ceiling-light-multiple", "ceiling-light-multiple-outline", "ceiling-light-outline", "cellphone", "cellphone-android", "cellphone-arrow-down", "cellphone-arrow-down-variant", "cellphone-basic", "cellphone-charging", "cellphone-check", "cellphone-cog", "cellphone-dock", "cellphone-information", "cellphone-iphone", "cellphone-key", "cellphone-link", "cellphone-link-off", "cellphone-lock", "cellphone-marker", "cellphone-message", "cellphone-message-off", "cellphone-nfc", "cellphone-nfc-off", "cellphone-off", "cellphone-play", "cellphone-remove", "cellphone-screenshot", "cellphone-settings", "cellphone-sound", "cellphone-text", "cellphone-wireless", "centos", "certificate", "certificate-outline", "chair-rolling", "chair-school", "chandelier", "charity", "charity-search", "chart-arc", "chart-areaspline", "chart-areaspline-variant", "chart-bar", "chart-bar-stacked", "chart-bell-curve", "chart-bell-curve-cumulative", "chart-box", "chart-box-multiple", "chart-box-multiple-outline", "chart-box-outline", "chart-box-plus-outline", "chart-bubble", "chart-donut", "chart-donut-variant", "chart-gantt", "chart-histogram", "chart-line", "chart-line-stacked", "chart-line-variant", "chart-multiline", "chart-multiple", "chart-pie", "chart-pie-outline", "chart-ppf", "chart-sankey", "chart-sankey-variant", "chart-scatter-plot", "chart-scatter-plot-hexbin", "chart-timeline", "chart-timeline-variant", "chart-timeline-variant-shimmer", "chart-tree", "chart-waterfall", "chat", "chat-alert", "chat-alert-outline", "chat-minus", "chat-minus-outline", "chat-outline", "chat-plus", "chat-plus-outline", "chat-processing", "chat-processing-outline", "chat-question", "chat-question-outline", "chat-remove", "chat-remove-outline", "chat-sleep", "chat-sleep-outline", "check", "check-all", "check-bold", "check-bookmark", "check-circle", "check-circle-outline", "check-decagram", "check-decagram-outline", "check-network", "check-network-outline", "check-outline", "check-underline", "check-underline-circle", "check-underline-circle-outline", "checkbook", "checkbook-arrow-left", "checkbook-arrow-right", "checkbox-blank", "checkbox-blank-badge", "checkbox-blank-badge-outline", "checkbox-blank-circle", "checkbox-blank-circle-outline", "checkbox-blank-off", "checkbox-blank-off-outline", "checkbox-blank-outline", "checkbox-intermediate", "checkbox-intermediate-variant", "checkbox-marked", "checkbox-marked-circle", "checkbox-marked-circle-auto-outline", "checkbox-marked-circle-minus-outline", "checkbox-marked-circle-outline", "checkbox-marked-circle-plus-outline", "checkbox-marked-outline", "checkbox-multiple-blank", "checkbox-multiple-blank-circle", "checkbox-multiple-blank-circle-outline", "checkbox-multiple-blank-outline", "checkbox-multiple-marked", "checkbox-multiple-marked-circle", "checkbox-multiple-marked-circle-outline", "checkbox-multiple-marked-outline", "checkbox-multiple-outline", "checkbox-outline", "checkerboard", "checkerboard-minus", "checkerboard-plus", "checkerboard-remove", "cheese", "cheese-off", "chef-hat", "chemical-weapon", "chess-bishop", "chess-king", "chess-knight", "chess-pawn", "chess-queen", "chess-rook", "chevron-double-down", "chevron-double-left", "chevron-double-right", "chevron-double-up", "chevron-down", "chevron-down-box", "chevron-down-box-outline", "chevron-down-circle", "chevron-down-circle-outline", "chevron-left", "chevron-left-box", "chevron-left-box-outline", "chevron-left-circle", "chevron-left-circle-outline", "chevron-right", "chevron-right-box", "chevron-right-box-outline", "chevron-right-circle", "chevron-right-circle-outline", "chevron-triple-down", "chevron-triple-left", "chevron-triple-right", "chevron-triple-up", "chevron-up", "chevron-up-box", "chevron-up-box-outline", "chevron-up-circle", "chevron-up-circle-outline", "chili-alert", "chili-alert-outline", "chili-hot", "chili-hot-outline", "chili-medium", "chili-medium-outline", "chili-mild", "chili-mild-outline", "chili-off", "chili-off-outline", "chip", "church", "church-outline", "cigar", "cigar-off", "circle", "circle-box", "circle-box-outline", "circle-double", "circle-edit-outline", "circle-expand", "circle-half", "circle-half-full", "circle-medium", "circle-multiple", "circle-multiple-outline", "circle-off-outline", "circle-opacity", "circle-outline", "circle-slice-1", "circle-slice-2", "circle-slice-3", "circle-slice-4", "circle-slice-5", "circle-slice-6", "circle-slice-7", "circle-slice-8", "circle-small", "circular-saw", "cisco-webex", "city", "city-switch", "city-variant", "city-variant-outline", "clipboard", "clipboard-account", "clipboard-account-outline", "clipboard-alert", "clipboard-alert-outline", "clipboard-arrow-down", "clipboard-arrow-down-outline", "clipboard-arrow-left", "clipboard-arrow-left-outline", "clipboard-arrow-right", "clipboard-arrow-right-outline", "clipboard-arrow-up", "clipboard-arrow-up-outline", "clipboard-check", "clipboard-check-multiple", "clipboard-check-multiple-outline", "clipboard-check-outline", "clipboard-clock", "clipboard-clock-outline", "clipboard-edit", "clipboard-edit-outline", "clipboard-file", "clipboard-file-outline", "clipboard-flow", "clipboard-flow-outline", "clipboard-list", "clipboard-list-outline", "clipboard-minus", "clipboard-minus-outline", "clipboard-multiple", "clipboard-multiple-outline", "clipboard-off", "clipboard-off-outline", "clipboard-outline", "clipboard-play", "clipboard-play-multiple", "clipboard-play-multiple-outline", "clipboard-play-outline", "clipboard-plus", "clipboard-plus-outline", "clipboard-pulse", "clipboard-pulse-outline", "clipboard-remove", "clipboard-remove-outline", "clipboard-search", "clipboard-search-outline", "clipboard-text", "clipboard-text-clock", "clipboard-text-clock-outline", "clipboard-text-multiple", "clipboard-text-multiple-outline", "clipboard-text-off", "clipboard-text-off-outline", "clipboard-text-outline", "clipboard-text-play", "clipboard-text-play-outline", "clipboard-text-search", "clipboard-text-search-outline", "clippy", "clock", "clock-alert", "clock-alert-outline", "clock-check", "clock-check-outline", "clock-digital", "clock-edit", "clock-edit-outline", "clock-end", "clock-fast", "clock-in", "clock-minus", "clock-minus-outline", "clock-out", "clock-outline", "clock-plus", "clock-plus-outline", "clock-remove", "clock-remove-outline", "clock-star-four-points", "clock-star-four-points-outline", "clock-start", "clock-time-eight", "clock-time-eight-outline", "clock-time-eleven", "clock-time-eleven-outline", "clock-time-five", "clock-time-five-outline", "clock-time-four", "clock-time-four-outline", "clock-time-nine", "clock-time-nine-outline", "clock-time-one", "clock-time-one-outline", "clock-time-seven", "clock-time-seven-outline", "clock-time-six", "clock-time-six-outline", "clock-time-ten", "clock-time-ten-outline", "clock-time-three", "clock-time-three-outline", "clock-time-twelve", "clock-time-twelve-outline", "clock-time-two", "clock-time-two-outline", "close", "close-box", "close-box-multiple", "close-box-multiple-outline", "close-box-outline", "close-circle", "close-circle-multiple", "close-circle-multiple-outline", "close-circle-outline", "close-network", "close-network-outline", "close-octagon", "close-octagon-outline", "close-outline", "close-thick", "closed-caption", "closed-caption-outline", "cloud", "cloud-alert", "cloud-alert-outline", "cloud-arrow-down", "cloud-arrow-down-outline", "cloud-arrow-left", "cloud-arrow-left-outline", "cloud-arrow-right", "cloud-arrow-right-outline", "cloud-arrow-up", "cloud-arrow-up-outline", "cloud-braces", "cloud-cancel", "cloud-cancel-outline", "cloud-check", "cloud-check-outline", "cloud-check-variant", "cloud-check-variant-outline", "cloud-circle", "cloud-circle-outline", "cloud-clock", "cloud-clock-outline", "cloud-cog", "cloud-cog-outline", "cloud-download", "cloud-download-outline", "cloud-key", "cloud-key-outline", "cloud-lock", "cloud-lock-open", "cloud-lock-open-outline", "cloud-lock-outline", "cloud-minus", "cloud-minus-outline", "cloud-off", "cloud-off-outline", "cloud-outline", "cloud-percent", "cloud-percent-outline", "cloud-plus", "cloud-plus-outline", "cloud-print", "cloud-print-outline", "cloud-question", "cloud-question-outline", "cloud-refresh", "cloud-refresh-outline", "cloud-refresh-variant", "cloud-refresh-variant-outline", "cloud-remove", "cloud-remove-outline", "cloud-search", "cloud-search-outline", "cloud-sync", "cloud-sync-outline", "cloud-tags", "cloud-upload", "cloud-upload-outline", "clouds", "clover", "clover-outline", "coach-lamp", "coach-lamp-variant", "coat-rack", "code-array", "code-block-braces", "code-block-brackets", "code-block-parentheses", "code-block-tags", "code-braces", "code-braces-box", "code-brackets", "code-equal", "code-greater-than", "code-greater-than-or-equal", "code-json", "code-less-than", "code-less-than-or-equal", "code-not-equal", "code-not-equal-variant", "code-parentheses", "code-parentheses-box", "code-string", "code-tags", "code-tags-check", "codepen", "coffee", "coffee-maker", "coffee-maker-check", "coffee-maker-check-outline", "coffee-maker-outline", "coffee-off", "coffee-off-outline", "coffee-outline", "coffee-to-go", "coffee-to-go-outline", "coffin", "cog", "cog-box", "cog-clockwise", "cog-counterclockwise", "cog-off", "cog-off-outline", "cog-outline", "cog-pause", "cog-pause-outline", "cog-play", "cog-play-outline", "cog-refresh", "cog-refresh-outline", "cog-stop", "cog-stop-outline", "cog-sync", "cog-sync-outline", "cog-transfer", "cog-transfer-outline", "cogs", "collage", "collapse-all", "collapse-all-outline", "color-helper", "comma", "comma-box", "comma-box-outline", "comma-circle", "comma-circle-outline", "comment", "comment-account", "comment-account-outline", "comment-alert", "comment-alert-outline", "comment-arrow-left", "comment-arrow-left-outline", "comment-arrow-right", "comment-arrow-right-outline", "comment-bookmark", "comment-bookmark-outline", "comment-check", "comment-check-outline", "comment-edit", "comment-edit-outline", "comment-eye", "comment-eye-outline", "comment-flash", "comment-flash-outline", "comment-minus", "comment-minus-outline", "comment-multiple", "comment-multiple-outline", "comment-off", "comment-off-outline", "comment-outline", "comment-plus", "comment-plus-outline", "comment-processing", "comment-processing-outline", "comment-question", "comment-question-outline", "comment-quote", "comment-quote-outline", "comment-remove", "comment-remove-outline", "comment-search", "comment-search-outline", "comment-text", "comment-text-multiple", "comment-text-multiple-outline", "comment-text-outline", "compare", "compare-horizontal", "compare-remove", "compare-vertical", "compass", "compass-off", "compass-off-outline", "compass-outline", "compass-rose", "compost", "concourse-ci", "cone", "cone-off", "connection", "console", "console-line", "console-network", "console-network-outline", "consolidate", "contactless-payment", "contactless-payment-circle", "contactless-payment-circle-outline", "contacts", "contacts-outline", "contain", "contain-end", "contain-start", "content-copy", "content-cut", "content-duplicate", "content-paste", "content-save", "content-save-alert", "content-save-alert-outline", "content-save-all", "content-save-all-outline", "content-save-check", "content-save-check-outline", "content-save-cog", "content-save-cog-outline", "content-save-edit", "content-save-edit-outline", "content-save-minus", "content-save-minus-outline", "content-save-move", "content-save-move-outline", "content-save-off", "content-save-off-outline", "content-save-outline", "content-save-plus", "content-save-plus-outline", "content-save-settings", "content-save-settings-outline", "contrast", "contrast-box", "contrast-circle", "controller", "controller-classic", "controller-classic-outline", "controller-off", "controller-xbox", "cookie", "cookie-alert", "cookie-alert-outline", "cookie-check", "cookie-check-outline", "cookie-clock", "cookie-clock-outline", "cookie-cog", "cookie-cog-outline", "cookie-edit", "cookie-edit-outline", "cookie-lock", "cookie-lock-outline", "cookie-minus", "cookie-minus-outline", "cookie-off", "cookie-off-outline", "cookie-outline", "cookie-plus", "cookie-plus-outline", "cookie-refresh", "cookie-refresh-outline", "cookie-remove", "cookie-remove-outline", "cookie-settings", "cookie-settings-outline", "coolant-temperature", "copyleft", "copyright", "<PERSON><PERSON>", "corn", "corn-off", "cosine-wave", "counter", "countertop", "countertop-outline", "cow", "cow-off", "cpu-32-bit", "cpu-64-bit", "cradle", "cradle-outline", "crane", "creation", "creation-outline", "creative-commons", "credit-card", "credit-card-check", "credit-card-check-outline", "credit-card-chip", "credit-card-chip-outline", "credit-card-clock", "credit-card-clock-outline", "credit-card-edit", "credit-card-edit-outline", "credit-card-fast", "credit-card-fast-outline", "credit-card-lock", "credit-card-lock-outline", "credit-card-marker", "credit-card-marker-outline", "credit-card-minus", "credit-card-minus-outline", "credit-card-multiple", "credit-card-multiple-outline", "credit-card-off", "credit-card-off-outline", "credit-card-outline", "credit-card-plus", "credit-card-plus-outline", "credit-card-refresh", "credit-card-refresh-outline", "credit-card-refund", "credit-card-refund-outline", "credit-card-remove", "credit-card-remove-outline", "credit-card-scan", "credit-card-scan-outline", "credit-card-search", "credit-card-search-outline", "credit-card-settings", "credit-card-settings-outline", "credit-card-sync", "credit-card-sync-outline", "credit-card-wireless", "credit-card-wireless-off", "credit-card-wireless-off-outline", "credit-card-wireless-outline", "cricket", "crop", "crop-free", "crop-landscape", "crop-portrait", "crop-rotate", "crop-square", "cross", "cross-bol<PERSON>i", "cross-celtic", "cross-outline", "crosshairs", "crosshairs-gps", "crosshairs-off", "crosshairs-question", "crowd", "crown", "crown-circle", "crown-circle-outline", "crown-outline", "cryengine", "crystal-ball", "cube", "cube-off", "cube-off-outline", "cube-outline", "cube-scan", "cube-send", "cube-unfolded", "cup", "cup-off", "cup-off-outline", "cup-outline", "cup-water", "cupboard", "cupboard-outline", "cupcake", "curling", "currency-bdt", "currency-brl", "currency-btc", "currency-chf", "currency-cny", "currency-eth", "currency-eur", "currency-eur-off", "currency-fra", "currency-gbp", "currency-ils", "currency-inr", "currency-jpy", "currency-krw", "currency-kzt", "currency-mnt", "currency-ngn", "currency-php", "currency-rial", "currency-rub", "currency-rupee", "currency-sign", "currency-thb", "currency-try", "currency-twd", "currency-uah", "currency-usd", "currency-usd-circle", "currency-usd-circle-outline", "currency-usd-off", "current-ac", "current-dc", "cursor-default", "cursor-default-click", "cursor-default-click-outline", "cursor-default-gesture", "cursor-default-gesture-outline", "cursor-default-outline", "cursor-move", "cursor-pointer", "cursor-text", "curtains", "curtains-closed", "cylinder", "cylinder-off", "dance-ballroom", "dance-pole", "data", "data-matrix", "data-matrix-edit", "data-matrix-minus", "data-matrix-plus", "data-matrix-remove", "data-matrix-scan", "database", "database-alert", "database-alert-outline", "database-arrow-down", "database-arrow-down-outline", "database-arrow-left", "database-arrow-left-outline", "database-arrow-right", "database-arrow-right-outline", "database-arrow-up", "database-arrow-up-outline", "database-check", "database-check-outline", "database-clock", "database-clock-outline", "database-cog", "database-cog-outline", "database-edit", "database-edit-outline", "database-export", "database-export-outline", "database-eye", "database-eye-off", "database-eye-off-outline", "database-eye-outline", "database-import", "database-import-outline", "database-lock", "database-lock-outline", "database-marker", "database-marker-outline", "database-minus", "database-minus-outline", "database-off", "database-off-outline", "database-outline", "database-plus", "database-plus-outline", "database-refresh", "database-refresh-outline", "database-remove", "database-remove-outline", "database-search", "database-search-outline", "database-settings", "database-settings-outline", "database-sync", "database-sync-outline", "death-star", "death-star-variant", "deathly-hallows", "debian", "debug-step-into", "debug-step-out", "debug-step-over", "decagram", "decagram-outline", "decimal", "decimal-comma", "decimal-comma-decrease", "decimal-comma-increase", "decimal-decrease", "decimal-increase", "delete", "delete-alert", "delete-alert-outline", "delete-circle", "delete-circle-outline", "delete-clock", "delete-clock-outline", "delete-empty", "delete-empty-outline", "delete-forever", "delete-forever-outline", "delete-off", "delete-off-outline", "delete-outline", "delete-restore", "delete-sweep", "delete-sweep-outline", "delete-variant", "delta", "desk", "desk-lamp", "desk-lamp-off", "desk-lamp-on", "deskphone", "desktop-classic", "desktop-mac", "desktop-mac-dashboard", "desktop-tower", "desktop-tower-monitor", "details", "dev-to", "developer-board", "deviantart", "devices", "dharma<PERSON><PERSON>", "diabetes", "dialpad", "diameter", "diameter-outline", "diameter-variant", "diamond", "diamond-outline", "diamond-stone", "diaper-outline", "dice", "dice-1", "dice-1-outline", "dice-2", "dice-2-outline", "dice-3", "dice-3-outline", "dice-4", "dice-4-outline", "dice-5", "dice-5-outline", "dice-6", "dice-6-outline", "dice-d10", "dice-d10-outline", "dice-d12", "dice-d12-outline", "dice-d20", "dice-d20-outline", "dice-d4", "dice-d4-outline", "dice-d6", "dice-d6-outline", "dice-d8", "dice-d8-outline", "dice-multiple", "dice-multiple-outline", "digital-ocean", "dip-switch", "directions", "directions-fork", "disc", "disc-alert", "disc-player", "discord", "dishwasher", "dishwasher-alert", "dishwasher-off", "disk", "disk-alert", "disk-player", "disqus", "disqus-outline", "distribute-horizontal-center", "distribute-horizontal-left", "distribute-horizontal-right", "distribute-vertical-bottom", "distribute-vertical-center", "distribute-vertical-top", "diversify", "diving", "diving-flippers", "diving-helmet", "diving-scuba", "diving-scuba-flag", "diving-scuba-mask", "diving-scuba-tank", "diving-scuba-tank-multiple", "diving-snorkel", "division", "division-box", "dlna", "dna", "dns", "dns-outline", "do-not-disturb", "dock-bottom", "dock-left", "dock-right", "dock-top", "dock-window", "docker", "doctor", "document", "dog", "dog-service", "dog-side", "dog-side-off", "dolby", "dolly", "dolphin", "domain", "domain-off", "domain-plus", "domain-remove", "domain-switch", "dome-light", "domino-mask", "donkey", "door", "door-closed", "door-closed-cancel", "door-closed-lock", "door-open", "door-sliding", "door-sliding-lock", "door-sliding-open", "doorbell", "doorbell-video", "dot-net", "dots-circle", "dots-grid", "dots-hexagon", "dots-horizontal", "dots-horizontal-circle", "dots-horizontal-circle-outline", "dots-square", "dots-triangle", "dots-vertical", "dots-vertical-circle", "dots-vertical-circle-outline", "douban", "download", "download-box", "download-box-outline", "download-circle", "download-circle-outline", "download-lock", "download-lock-outline", "download-multiple", "download-multiple-outline", "download-network", "download-network-outline", "download-off", "download-off-outline", "download-outline", "drag", "drag-horizontal", "drag-horizontal-variant", "drag-variant", "drag-vertical", "drag-vertical-variant", "drama-masks", "draw", "draw-pen", "drawing", "drawing-box", "dresser", "dresser-outline", "dribbble", "dribbble-box", "drone", "dropbox", "drupal", "duck", "dumbbell", "dump-truck", "ear-hearing", "ear-hearing-loop", "ear-hearing-off", "earbuds", "earbuds-off", "earbuds-off-outline", "earbuds-outline", "earth", "earth-arrow-down", "earth-arrow-left", "earth-arrow-right", "earth-arrow-up", "earth-box", "earth-box-minus", "earth-box-off", "earth-box-plus", "earth-box-remove", "earth-minus", "earth-off", "earth-plus", "earth-remove", "ebay", "egg", "egg-easter", "egg-fried", "egg-off", "egg-off-outline", "egg-outline", "eiffel-tower", "eight-track", "eject", "eject-circle", "eject-circle-outline", "eject-outline", "electric-switch", "electric-switch-closed", "electron-framework", "elephant", "elevation-decline", "elevation-rise", "elevator", "elevator-down", "elevator-passenger", "elevator-passenger-off", "elevator-passenger-off-outline", "elevator-passenger-outline", "elevator-up", "ellipse", "ellipse-outline", "email", "email-alert", "email-alert-outline", "email-arrow-left", "email-arrow-left-outline", "email-arrow-right", "email-arrow-right-outline", "email-box", "email-check", "email-check-outline", "email-edit", "email-edit-outline", "email-fast", "email-fast-outline", "email-heart-outline", "email-lock", "email-lock-outline", "email-mark-as-unread", "email-minus", "email-minus-outline", "email-multiple", "email-multiple-outline", "email-newsletter", "email-off", "email-off-outline", "email-open", "email-open-heart-outline", "email-open-multiple", "email-open-multiple-outline", "email-open-outline", "email-outline", "email-plus", "email-plus-outline", "email-remove", "email-remove-outline", "email-seal", "email-seal-outline", "email-search", "email-search-outline", "email-sync", "email-sync-outline", "email-variant", "ember", "emby", "emoticon", "emoticon-angry", "emoticon-angry-outline", "emoticon-confused", "emoticon-confused-outline", "emoticon-cool", "emoticon-cool-outline", "emoticon-cry", "emoticon-cry-outline", "emoticon-dead", "emoticon-dead-outline", "emoticon-devil", "emoticon-devil-outline", "emoticon-excited", "emoticon-excited-outline", "emoticon-frown", "emoticon-frown-outline", "emoticon-happy", "emoticon-happy-outline", "emoticon-kiss", "emoticon-kiss-outline", "emoticon-lol", "emoticon-lol-outline", "emoticon-minus", "emoticon-minus-outline", "emoticon-neutral", "emoticon-neutral-outline", "emoticon-outline", "emoticon-plus", "emoticon-plus-outline", "emoticon-poop", "emoticon-poop-outline", "emoticon-remove", "emoticon-remove-outline", "emoticon-sad", "emoticon-sad-outline", "emoticon-sick", "emoticon-sick-outline", "emoticon-tongue", "emoticon-tongue-outline", "emoticon-wink", "emoticon-wink-outline", "engine", "engine-off", "engine-off-outline", "engine-outline", "epsilon", "equal", "equal-box", "equalizer", "equalizer-outline", "eraser", "eraser-variant", "escalator", "escalator-box", "escalator-down", "escalator-up", "eslint", "et", "ethereum", "ethernet", "ethernet-cable", "ethernet-cable-off", "ethernet-off", "etsy", "ev-plug-ccs1", "ev-plug-ccs2", "ev-plug-chademo", "ev-plug-tesla", "ev-plug-type1", "ev-plug-type2", "ev-station", "eventbrite", "evernote", "excavator", "exclamation", "exclamation-thick", "exit-run", "exit-to-app", "expand-all", "expand-all-outline", "expansion-card", "expansion-card-variant", "exponent", "exponent-box", "export", "export-variant", "eye", "eye-arrow-left", "eye-arrow-left-outline", "eye-arrow-right", "eye-arrow-right-outline", "eye-check", "eye-check-outline", "eye-circle", "eye-circle-outline", "eye-closed", "eye-lock", "eye-lock-open", "eye-lock-open-outline", "eye-lock-outline", "eye-minus", "eye-minus-outline", "eye-off", "eye-off-outline", "eye-outline", "eye-plus", "eye-plus-outline", "eye-refresh", "eye-refresh-outline", "eye-remove", "eye-remove-outline", "eye-settings", "eye-settings-outline", "eyedropper", "eyedropper-minus", "eyedropper-off", "eyedropper-plus", "eyedropper-remove", "eyedropper-variant", "face-agent", "face-man", "face-man-outline", "face-man-profile", "face-man-shimmer", "face-man-shimmer-outline", "face-mask", "face-mask-outline", "face-recognition", "face-woman", "face-woman-outline", "face-woman-profile", "face-woman-shimmer", "face-woman-shimmer-outline", "facebook", "facebook-box", "facebook-gaming", "facebook-messenger", "facebook-workplace", "factory", "family-tree", "fan", "fan-alert", "fan-auto", "fan-chevron-down", "fan-chevron-up", "fan-clock", "fan-minus", "fan-off", "fan-plus", "fan-remove", "fan-speed-1", "fan-speed-2", "fan-speed-3", "fast-forward", "fast-forward-10", "fast-forward-15", "fast-forward-30", "fast-forward-45", "fast-forward-5", "fast-forward-60", "fast-forward-outline", "faucet", "faucet-variant", "fax", "feather", "feature-search", "feature-search-outline", "fedora", "fence", "fence-electric", "fencing", "ferris-wheel", "ferry", "file", "file-account", "file-account-outline", "file-alert", "file-alert-outline", "file-arrow-left-right", "file-arrow-left-right-outline", "file-arrow-up-down", "file-arrow-up-down-outline", "file-cabinet", "file-cad", "file-cad-box", "file-cancel", "file-cancel-outline", "file-certificate", "file-certificate-outline", "file-chart", "file-chart-check", "file-chart-check-outline", "file-chart-outline", "file-check", "file-check-outline", "file-clock", "file-clock-outline", "file-cloud", "file-cloud-outline", "file-code", "file-code-outline", "file-cog", "file-cog-outline", "file-compare", "file-delimited", "file-delimited-outline", "file-document", "file-document-alert", "file-document-alert-outline", "file-document-arrow-right", "file-document-arrow-right-outline", "file-document-check", "file-document-check-outline", "file-document-edit", "file-document-edit-outline", "file-document-minus", "file-document-minus-outline", "file-document-multiple", "file-document-multiple-outline", "file-document-outline", "file-document-plus", "file-document-plus-outline", "file-document-refresh", "file-document-refresh-outline", "file-document-remove", "file-document-remove-outline", "file-download", "file-download-outline", "file-edit", "file-edit-outline", "file-excel", "file-excel-box", "file-excel-box-outline", "file-excel-outline", "file-export", "file-export-outline", "file-eye", "file-eye-outline", "file-find", "file-find-outline", "file-gif-box", "file-hidden", "file-image", "file-image-box", "file-image-marker", "file-image-marker-outline", "file-image-minus", "file-image-minus-outline", "file-image-outline", "file-image-plus", "file-image-plus-outline", "file-image-remove", "file-image-remove-outline", "file-import", "file-import-outline", "file-jpg-box", "file-key", "file-key-outline", "file-link", "file-link-outline", "file-lock", "file-lock-open", "file-lock-open-outline", "file-lock-outline", "file-marker", "file-marker-outline", "file-minus", "file-minus-outline", "file-move", "file-move-outline", "file-multiple", "file-multiple-outline", "file-music", "file-music-outline", "file-outline", "file-pdf", "file-pdf-box", "file-pdf-box-outline", "file-pdf-outline", "file-percent", "file-percent-outline", "file-phone", "file-phone-outline", "file-plus", "file-plus-outline", "file-png-box", "file-powerpoint", "file-powerpoint-box", "file-powerpoint-box-outline", "file-powerpoint-outline", "file-presentation-box", "file-question", "file-question-outline", "file-refresh", "file-refresh-outline", "file-remove", "file-remove-outline", "file-replace", "file-replace-outline", "file-restore", "file-restore-outline", "file-rotate-left", "file-rotate-left-outline", "file-rotate-right", "file-rotate-right-outline", "file-search", "file-search-outline", "file-send", "file-send-outline", "file-settings", "file-settings-outline", "file-sign", "file-star", "file-star-four-points", "file-star-four-points-outline", "file-star-outline", "file-swap", "file-swap-outline", "file-sync", "file-sync-outline", "file-table", "file-table-box", "file-table-box-multiple", "file-table-box-multiple-outline", "file-table-box-outline", "file-table-outline", "file-tree", "file-tree-outline", "file-undo", "file-undo-outline", "file-upload", "file-upload-outline", "file-video", "file-video-outline", "file-word", "file-word-box", "file-word-box-outline", "file-word-outline", "file-xml", "file-xml-box", "fill", "film", "filmstrip", "filmstrip-box", "filmstrip-box-multiple", "filmstrip-off", "filter", "filter-check", "filter-check-outline", "filter-cog", "filter-cog-outline", "filter-menu", "filter-menu-outline", "filter-minus", "filter-minus-outline", "filter-multiple", "filter-multiple-outline", "filter-off", "filter-off-outline", "filter-outline", "filter-plus", "filter-plus-outline", "filter-remove", "filter-remove-outline", "filter-settings", "filter-settings-outline", "filter-variant", "filter-variant-minus", "filter-variant-plus", "filter-variant-remove", "finance", "find-replace", "fingerprint", "fingerprint-off", "fire", "fire-alert", "fire-circle", "fire-extinguisher", "fire-hydrant", "fire-hydrant-alert", "fire-hydrant-off", "fire-off", "fire-station", "fire-truck", "firebase", "firefox", "fireplace", "fireplace-off", "firewire", "firework", "firework-off", "fish", "fish-off", "fishbowl", "fishbowl-outline", "fit-to-page", "fit-to-page-outline", "fit-to-screen", "fit-to-screen-outline", "flag", "flag-checkered", "flag-checkered-variant", "flag-minus", "flag-minus-outline", "flag-off", "flag-off-outline", "flag-outline", "flag-outline-variant", "flag-plus", "flag-plus-outline", "flag-remove", "flag-remove-outline", "flag-triangle", "flag-variant", "flag-variant-minus", "flag-variant-minus-outline", "flag-variant-off", "flag-variant-off-outline", "flag-variant-outline", "flag-variant-plus", "flag-variant-plus-outline", "flag-variant-remove", "flag-variant-remove-outline", "flare", "flash", "flash-alert", "flash-alert-outline", "flash-auto", "flash-off", "flash-off-outline", "flash-outline", "flash-red-eye", "flash-triangle", "flash-triangle-outline", "flashlight", "flashlight-off", "flask", "flask-empty", "flask-empty-minus", "flask-empty-minus-outline", "flask-empty-off", "flask-empty-off-outline", "flask-empty-outline", "flask-empty-plus", "flask-empty-plus-outline", "flask-empty-remove", "flask-empty-remove-outline", "flask-minus", "flask-minus-outline", "flask-off", "flask-off-outline", "flask-outline", "flask-plus", "flask-plus-outline", "flask-remove", "flask-remove-outline", "flask-round-bottom", "flask-round-bottom-empty", "flask-round-bottom-empty-outline", "flask-round-bottom-outline", "flattr", "fleur-de-lis", "flickr", "flickr-after", "flickr-before", "flip-horizontal", "flip-to-back", "flip-to-front", "flip-vertical", "floor-1", "floor-2", "floor-3", "floor-a", "floor-b", "floor-g", "floor-l", "floor-lamp", "floor-lamp-dual", "floor-lamp-dual-outline", "floor-lamp-outline", "floor-lamp-torchiere", "floor-lamp-torchiere-outline", "floor-lamp-torchiere-variant", "floor-lamp-torchiere-variant-outline", "floor-plan", "floppy", "floppy-variant", "flower", "flower-outline", "flower-pollen", "flower-pollen-outline", "flower-poppy", "flower-tulip", "flower-tulip-outline", "focus-auto", "focus-field", "focus-field-horizontal", "focus-field-vertical", "folder", "folder-account", "folder-account-outline", "folder-alert", "folder-alert-outline", "folder-arrow-down", "folder-arrow-down-outline", "folder-arrow-left", "folder-arrow-left-outline", "folder-arrow-left-right", "folder-arrow-left-right-outline", "folder-arrow-right", "folder-arrow-right-outline", "folder-arrow-up", "folder-arrow-up-down", "folder-arrow-up-down-outline", "folder-arrow-up-outline", "folder-cancel", "folder-cancel-outline", "folder-check", "folder-check-outline", "folder-clock", "folder-clock-outline", "folder-cog", "folder-cog-outline", "folder-download", "folder-download-outline", "folder-edit", "folder-edit-outline", "folder-eye", "folder-eye-outline", "folder-file", "folder-file-outline", "folder-google-drive", "folder-heart", "folder-heart-outline", "folder-hidden", "folder-home", "folder-home-outline", "folder-image", "folder-information", "folder-information-outline", "folder-key", "folder-key-network", "folder-key-network-outline", "folder-key-outline", "folder-lock", "folder-lock-open", "folder-lock-open-outline", "folder-lock-outline", "folder-marker", "folder-marker-outline", "folder-minus", "folder-minus-outline", "folder-move", "folder-move-outline", "folder-multiple", "folder-multiple-image", "folder-multiple-outline", "folder-multiple-plus", "folder-multiple-plus-outline", "folder-music", "folder-music-outline", "folder-network", "folder-network-outline", "folder-off", "folder-off-outline", "folder-open", "folder-open-outline", "folder-outline", "folder-outline-lock", "folder-play", "folder-play-outline", "folder-plus", "folder-plus-outline", "folder-pound", "folder-pound-outline", "folder-question", "folder-question-outline", "folder-refresh", "folder-refresh-outline", "folder-remove", "folder-remove-outline", "folder-search", "folder-search-outline", "folder-settings", "folder-settings-outline", "folder-star", "folder-star-multiple", "folder-star-multiple-outline", "folder-star-outline", "folder-swap", "folder-swap-outline", "folder-sync", "folder-sync-outline", "folder-table", "folder-table-outline", "folder-text", "folder-text-outline", "folder-upload", "folder-upload-outline", "folder-wrench", "folder-wrench-outline", "folder-zip", "folder-zip-outline", "font-awesome", "food", "food-apple", "food-apple-outline", "food-croissant", "food-drumstick", "food-drumstick-off", "food-drumstick-off-outline", "food-drumstick-outline", "food-fork-drink", "food-halal", "food-hot-dog", "food-kosher", "food-off", "food-off-outline", "food-outline", "food-steak", "food-steak-off", "food-takeout-box", "food-takeout-box-outline", "food-turkey", "food-variant", "food-variant-off", "foot-print", "football", "football-australian", "football-helmet", "footer", "forest", "forest-outline", "forklift", "form-dropdown", "form-select", "form-textarea", "form-textbox", "form-textbox-lock", "form-textbox-password", "format-align-bottom", "format-align-center", "format-align-justify", "format-align-left", "format-align-middle", "format-align-right", "format-align-top", "format-annotation-minus", "format-annotation-plus", "format-bold", "format-clear", "format-color", "format-color-fill", "format-color-highlight", "format-color-marker-cancel", "format-color-text", "format-columns", "format-float-center", "format-float-left", "format-float-none", "format-float-right", "format-font", "format-font-size-decrease", "format-font-size-increase", "format-header-1", "format-header-2", "format-header-3", "format-header-4", "format-header-5", "format-header-6", "format-header-decrease", "format-header-down", "format-header-equal", "format-header-increase", "format-header-pound", "format-header-up", "format-horizontal-align-center", "format-horizontal-align-left", "format-horizontal-align-right", "format-indent-decrease", "format-indent-increase", "format-italic", "format-letter-case", "format-letter-case-lower", "format-letter-case-upper", "format-letter-ends-with", "format-letter-matches", "format-letter-spacing", "format-letter-spacing-variant", "format-letter-starts-with", "format-line-height", "format-line-spacing", "format-line-style", "format-line-weight", "format-list-bulleted", "format-list-bulleted-square", "format-list-bulleted-triangle", "format-list-bulleted-type", "format-list-checkbox", "format-list-checks", "format-list-group", "format-list-group-plus", "format-list-numbered", "format-list-numbered-rtl", "format-list-text", "format-list-triangle", "format-overline", "format-page-break", "format-page-split", "format-paint", "format-paragraph", "format-paragraph-spacing", "format-pilcrow", "format-pilcrow-arrow-left", "format-pilcrow-arrow-right", "format-quote-close", "format-quote-close-outline", "format-quote-open", "format-quote-open-outline", "format-rotate-90", "format-section", "format-size", "format-strikethrough", "format-strikethrough-variant", "format-subscript", "format-superscript", "format-text", "format-text-rotation-angle-down", "format-text-rotation-angle-up", "format-text-rotation-down", "format-text-rotation-down-vertical", "format-text-rotation-none", "format-text-rotation-up", "format-text-rotation-vertical", "format-text-variant", "format-text-variant-outline", "format-text-wrapping-clip", "format-text-wrapping-overflow", "format-text-wrapping-wrap", "format-textbox", "format-title", "format-underline", "format-underline-wavy", "format-vertical-align-bottom", "format-vertical-align-center", "format-vertical-align-top", "format-wrap-inline", "format-wrap-square", "format-wrap-tight", "format-wrap-top-bottom", "forum", "forum-minus", "forum-minus-outline", "forum-outline", "forum-plus", "forum-plus-outline", "forum-remove", "forum-remove-outline", "forward", "forwardburger", "fountain", "fountain-pen", "fountain-pen-tip", "foursquare", "fraction-one-half", "freebsd", "french-fries", "frequently-asked-questions", "fridge", "fridge-alert", "fridge-alert-outline", "fridge-bottom", "fridge-industrial", "fridge-industrial-alert", "fridge-industrial-alert-outline", "fridge-industrial-off", "fridge-industrial-off-outline", "fridge-industrial-outline", "fridge-off", "fridge-off-outline", "fridge-outline", "fridge-top", "fridge-variant", "fridge-variant-alert", "fridge-variant-alert-outline", "fridge-variant-off", "fridge-variant-off-outline", "fridge-variant-outline", "fruit-cherries", "fruit-cherries-off", "fruit-citrus", "fruit-citrus-off", "fruit-grapes", "fruit-grapes-outline", "fruit-pear", "fruit-pineapple", "fruit-watermelon", "fuel", "fuel-cell", "fullscreen", "fullscreen-exit", "function", "function-variant", "furigana-horizontal", "furigana-vertical", "fuse", "fuse-alert", "fuse-blade", "fuse-off", "gamepad", "gamepad-circle", "gamepad-circle-down", "gamepad-circle-left", "gamepad-circle-outline", "gamepad-circle-right", "gamepad-circle-up", "gamepad-down", "gamepad-left", "gamepad-outline", "gamepad-right", "gamepad-round", "gamepad-round-down", "gamepad-round-left", "gamepad-round-outline", "gamepad-round-right", "gamepad-round-up", "gamepad-square", "gamepad-square-outline", "gamepad-up", "gamepad-variant", "gamepad-variant-outline", "gamma", "gantry-crane", "garage", "garage-alert", "garage-alert-variant", "garage-lock", "garage-open", "garage-open-variant", "garage-variant", "garage-variant-lock", "gas-burner", "gas-cylinder", "gas-station", "gas-station-in-use", "gas-station-in-use-outline", "gas-station-off", "gas-station-off-outline", "gas-station-outline", "gate", "gate-alert", "gate-and", "gate-arrow-left", "gate-arrow-right", "gate-buffer", "gate-nand", "gate-nor", "gate-not", "gate-open", "gate-or", "gate-xnor", "gate-xor", "gatsby", "gauge", "gauge-empty", "gauge-full", "gauge-low", "gavel", "gender-female", "gender-male", "gender-male-female", "gender-male-female-variant", "gender-non-binary", "gender-transgender", "generator-mobile", "generator-portable", "generator-stationary", "gentoo", "gesture", "gesture-double-tap", "gesture-pinch", "gesture-spread", "gesture-swipe", "gesture-swipe-down", "gesture-swipe-horizontal", "gesture-swipe-left", "gesture-swipe-right", "gesture-swipe-up", "gesture-swipe-vertical", "gesture-tap", "gesture-tap-box", "gesture-tap-button", "gesture-tap-hold", "gesture-two-double-tap", "gesture-two-tap", "ghost", "ghost-off", "ghost-off-outline", "ghost-outline", "gif", "gift", "gift-off", "gift-off-outline", "gift-open", "gift-open-outline", "gift-outline", "git", "github", "github-box", "github-face", "gitlab", "glass-cocktail", "glass-cocktail-off", "glass-flute", "glass-fragile", "glass-mug", "glass-mug-off", "glass-mug-variant", "glass-mug-variant-off", "glass-pint-outline", "glass-stange", "glass-tulip", "glass-wine", "glassdoor", "glasses", "globe-light", "globe-light-outline", "globe-model", "gmail", "gnome", "go-kart", "go-kart-track", "gog", "gold", "golf", "golf-cart", "golf-tee", "gondola", "goodreads", "google", "google-ads", "google-allo", "google-analytics", "google-assistant", "google-cardboard", "google-chrome", "google-circles", "google-circles-communities", "google-circles-extended", "google-circles-group", "google-classroom", "google-cloud", "google-downasaur", "google-drive", "google-earth", "google-fit", "google-glass", "google-hangouts", "google-home", "google-keep", "google-lens", "google-maps", "google-my-business", "google-nearby", "google-pages", "google-photos", "google-physical-web", "google-play", "google-plus", "google-plus-box", "google-podcast", "google-spreadsheet", "google-street-view", "google-translate", "google-wallet", "gradient-horizontal", "gradient-vertical", "grain", "graph", "graph-outline", "graphql", "grass", "grave-stone", "grease-pencil", "greater-than", "greater-than-or-equal", "greenhouse", "grid", "grid-large", "grid-off", "grill", "grill-outline", "group", "guitar-acoustic", "guitar-electric", "guitar-pick", "guitar-pick-outline", "guy-fawkes-mask", "gymnastics", "hail", "hair-dryer", "hair-dryer-outline", "halloween", "hamburger", "hamburger-check", "hamburger-minus", "hamburger-off", "hamburger-plus", "hamburger-remove", "hammer", "hammer-screwdriver", "hammer-sickle", "hammer-wrench", "hand-back-left", "hand-back-left-off", "hand-back-left-off-outline", "hand-back-left-outline", "hand-back-right", "hand-back-right-off", "hand-back-right-off-outline", "hand-back-right-outline", "hand-clap", "hand-clap-off", "hand-coin", "hand-coin-outline", "hand-cycle", "hand-extended", "hand-extended-outline", "hand-front-left", "hand-front-left-outline", "hand-front-right", "hand-front-right-outline", "hand-heart", "hand-heart-outline", "hand-left", "hand-okay", "hand-peace", "hand-peace-variant", "hand-pointing-down", "hand-pointing-left", "hand-pointing-right", "hand-pointing-up", "hand-right", "hand-saw", "hand-wash", "hand-wash-outline", "hand-water", "hand-wave", "hand-wave-outline", "handball", "handcuffs", "hands-pray", "handshake", "handshake-outline", "hanger", "hangouts", "hard-hat", "harddisk", "harddisk-plus", "harddisk-remove", "hat-fedora", "hazard-lights", "hdmi-port", "hdr", "hdr-off", "head", "head-alert", "head-alert-outline", "head-check", "head-check-outline", "head-cog", "head-cog-outline", "head-dots-horizontal", "head-dots-horizontal-outline", "head-flash", "head-flash-outline", "head-heart", "head-heart-outline", "head-lightbulb", "head-lightbulb-outline", "head-minus", "head-minus-outline", "head-outline", "head-plus", "head-plus-outline", "head-question", "head-question-outline", "head-remove", "head-remove-outline", "head-snowflake", "head-snowflake-outline", "head-sync", "head-sync-outline", "headphones", "headphones-bluetooth", "headphones-box", "headphones-off", "headphones-settings", "headset", "headset-dock", "headset-off", "heart", "heart-box", "heart-box-outline", "heart-broken", "heart-broken-outline", "heart-circle", "heart-circle-outline", "heart-cog", "heart-cog-outline", "heart-flash", "heart-half", "heart-half-full", "heart-half-outline", "heart-minus", "heart-minus-outline", "heart-multiple", "heart-multiple-outline", "heart-off", "heart-off-outline", "heart-outline", "heart-plus", "heart-plus-outline", "heart-pulse", "heart-remove", "heart-remove-outline", "heart-search", "heart-settings", "heart-settings-outline", "heat-pump", "heat-pump-outline", "heat-wave", "heating-coil", "helicopter", "help", "help-box", "help-box-multiple", "help-box-multiple-outline", "help-box-outline", "help-circle", "help-circle-outline", "help-network", "help-network-outline", "help-rhombus", "help-rhombus-outline", "hexadecimal", "hexagon", "hexagon-multiple", "hexagon-multiple-outline", "hexagon-outline", "hexagon-slice-1", "hexagon-slice-2", "hexagon-slice-3", "hexagon-slice-4", "hexagon-slice-5", "hexagon-slice-6", "hexagram", "hexagram-outline", "high-definition", "high-definition-box", "highway", "hiking", "history", "hockey-puck", "hockey-sticks", "hololens", "home", "home-account", "home-alert", "home-alert-outline", "home-analytics", "home-assistant", "home-automation", "home-battery", "home-battery-outline", "home-circle", "home-circle-outline", "home-city", "home-city-outline", "home-clock", "home-clock-outline", "home-currency-usd", "home-edit", "home-edit-outline", "home-export-outline", "home-flood", "home-floor-0", "home-floor-1", "home-floor-2", "home-floor-3", "home-floor-a", "home-floor-b", "home-floor-g", "home-floor-l", "home-floor-negative-1", "home-group", "home-group-minus", "home-group-plus", "home-group-remove", "home-heart", "home-import-outline", "home-lightbulb", "home-lightbulb-outline", "home-lightning-bolt", "home-lightning-bolt-outline", "home-lock", "home-lock-open", "home-map-marker", "home-minus", "home-minus-outline", "home-modern", "home-off", "home-off-outline", "home-outline", "home-percent", "home-percent-outline", "home-plus", "home-plus-outline", "home-remove", "home-remove-outline", "home-roof", "home-search", "home-search-outline", "home-silo", "home-silo-outline", "home-sound-in", "home-sound-in-outline", "home-sound-out", "home-sound-out-outline", "home-switch", "home-switch-outline", "home-thermometer", "home-thermometer-outline", "home-variant", "home-variant-outline", "hook", "hook-off", "hoop-house", "hops", "horizontal-rotate-clockwise", "horizontal-rotate-counterclockwise", "horse", "horse-human", "horse-variant", "horse-variant-fast", "horseshoe", "hospital", "hospital-box", "hospital-box-outline", "hospital-building", "hospital-marker", "hot-tub", "hours-12", "hours-24", "<PERSON>uzz", "houzz-box", "hub", "hub-outline", "hubspot", "hulu", "human", "human-baby-changing-table", "human-cane", "human-capacity-decrease", "human-capacity-increase", "human-child", "human-dolly", "human-edit", "human-female", "human-female-boy", "human-female-dance", "human-female-female", "human-female-female-child", "human-female-girl", "human-greeting", "human-greeting-proximity", "human-greeting-variant", "human-handsdown", "human-handsup", "human-male", "human-male-board", "human-male-board-poll", "human-male-boy", "human-male-child", "human-male-female", "human-male-female-child", "human-male-girl", "human-male-height", "human-male-height-variant", "human-male-male", "human-male-male-child", "human-non-binary", "human-pregnant", "human-queue", "human-scooter", "human-walker", "human-wheelchair", "human-white-cane", "humble-bundle", "hurricane", "hvac", "hvac-off", "hydraulic-oil-level", "hydraulic-oil-temperature", "hydro-power", "hydrogen-station", "ice-cream", "ice-cream-off", "ice-pop", "id-card", "identifier", "ideogram-cjk", "ideogram-cjk-variant", "image", "image-album", "image-area", "image-area-close", "image-auto-adjust", "image-broken", "image-broken-variant", "image-check", "image-check-outline", "image-edit", "image-edit-outline", "image-filter-black-white", "image-filter-center-focus", "image-filter-center-focus-strong", "image-filter-center-focus-strong-outline", "image-filter-center-focus-weak", "image-filter-drama", "image-filter-drama-outline", "image-filter-frames", "image-filter-hdr", "image-filter-hdr-outline", "image-filter-none", "image-filter-tilt-shift", "image-filter-vintage", "image-frame", "image-lock", "image-lock-outline", "image-marker", "image-marker-outline", "image-minus", "image-minus-outline", "image-move", "image-multiple", "image-multiple-outline", "image-off", "image-off-outline", "image-outline", "image-plus", "image-plus-outline", "image-refresh", "image-refresh-outline", "image-remove", "image-remove-outline", "image-search", "image-search-outline", "image-size-select-actual", "image-size-select-large", "image-size-select-small", "image-sync", "image-sync-outline", "image-text", "import", "inbox", "inbox-arrow-down", "inbox-arrow-down-outline", "inbox-arrow-up", "inbox-arrow-up-outline", "inbox-full", "inbox-full-outline", "inbox-multiple", "inbox-multiple-outline", "inbox-outline", "inbox-remove", "inbox-remove-outline", "incognito", "incognito-circle", "incognito-circle-off", "incognito-off", "indent", "induction", "infinity", "information", "information-box", "information-box-outline", "information-off", "information-off-outline", "information-outline", "information-slab-box", "information-slab-box-outline", "information-slab-circle", "information-slab-circle-outline", "information-slab-symbol", "information-symbol", "information-variant", "information-variant-box", "information-variant-box-outline", "information-variant-circle", "information-variant-circle-outline", "instagram", "instapaper", "instrument-triangle", "integrated-circuit-chip", "invert-colors", "invert-colors-off", "invoice", "invoice-arrow-left", "invoice-arrow-left-outline", "invoice-arrow-right", "invoice-arrow-right-outline", "invoice-check", "invoice-check-outline", "invoice-clock", "invoice-clock-outline", "invoice-edit", "invoice-edit-outline", "invoice-export-outline", "invoice-fast", "invoice-fast-outline", "invoice-import", "invoice-import-outline", "invoice-list", "invoice-list-outline", "invoice-minus", "invoice-minus-outline", "invoice-multiple", "invoice-multiple-outline", "invoice-outline", "invoice-plus", "invoice-plus-outline", "invoice-remove", "invoice-remove-outline", "invoice-send", "invoice-send-outline", "invoice-text", "invoice-text-arrow-left", "invoice-text-arrow-left-outline", "invoice-text-arrow-right", "invoice-text-arrow-right-outline", "invoice-text-check", "invoice-text-check-outline", "invoice-text-clock", "invoice-text-clock-outline", "invoice-text-edit", "invoice-text-edit-outline", "invoice-text-fast", "invoice-text-fast-outline", "invoice-text-minus", "invoice-text-minus-outline", "invoice-text-multiple", "invoice-text-multiple-outline", "invoice-text-outline", "invoice-text-plus", "invoice-text-plus-outline", "invoice-text-remove", "invoice-text-remove-outline", "invoice-text-send", "invoice-text-send-outline", "iobroker", "ip", "ip-network", "ip-network-outline", "ip-outline", "ipod", "iron", "iron-board", "iron-outline", "island", "island-variant", "itunes", "iv-bag", "jabber", "jeepney", "jellyfish", "jellyfish-outline", "jira", "j<PERSON>y", "jsfiddle", "jump-rope", "kabaddi", "kangaroo", "karate", "kayaking", "keg", "kettle", "kettle-alert", "kettle-alert-outline", "kettle-off", "kettle-off-outline", "kettle-outline", "kettle-pour-over", "kettle-steam", "kettle-steam-outline", "kettlebell", "key", "key-alert", "key-alert-outline", "key-arrow-right", "key-chain", "key-chain-variant", "key-change", "key-link", "key-minus", "key-outline", "key-plus", "key-remove", "key-star", "key-variant", "key-wireless", "keyboard", "keyboard-backspace", "keyboard-caps", "keyboard-close", "keyboard-close-outline", "keyboard-esc", "keyboard-f1", "keyboard-f10", "keyboard-f11", "keyboard-f12", "keyboard-f2", "keyboard-f3", "keyboard-f4", "keyboard-f5", "keyboard-f6", "keyboard-f7", "keyboard-f8", "keyboard-f9", "keyboard-off", "keyboard-off-outline", "keyboard-outline", "keyboard-return", "keyboard-settings", "keyboard-settings-outline", "keyboard-space", "keyboard-tab", "keyboard-tab-reverse", "keyboard-variant", "khanda", "kickstarter", "kite", "kite-outline", "kitesurfing", "klingon", "knife", "knife-military", "knob", "koala", "kodi", "kubernetes", "label", "label-multiple", "label-multiple-outline", "label-off", "label-off-outline", "label-outline", "label-percent", "label-percent-outline", "label-variant", "label-variant-outline", "ladder", "ladybug", "lambda", "lamp", "lamp-outline", "lamps", "lamps-outline", "lan", "lan-check", "lan-connect", "lan-disconnect", "lan-pending", "land-fields", "land-plots", "land-plots-circle", "land-plots-circle-variant", "land-plots-marker", "land-rows-horizontal", "land-rows-vertical", "landslide", "landslide-outline", "language-c", "language-cpp", "language-csharp", "language-css3", "language-fortran", "language-go", "language-haskell", "language-html5", "language-java", "language-javascript", "language-jsx", "language-kotlin", "language-lua", "language-markdown", "language-markdown-outline", "language-php", "language-python", "language-python-text", "language-r", "language-ruby", "language-ruby-on-rails", "language-rust", "language-swift", "language-typescript", "language-xaml", "laptop", "laptop-account", "laptop-chromebook", "laptop-mac", "laptop-off", "laptop-windows", "laravel", "laser-pointer", "lasso", "lastfm", "lastpass", "latitude", "launch", "lava-lamp", "layers", "layers-edit", "layers-minus", "layers-off", "layers-off-outline", "layers-outline", "layers-plus", "layers-remove", "layers-search", "layers-search-outline", "layers-triple", "layers-triple-outline", "lead-pencil", "leaf", "leaf-circle", "leaf-circle-outline", "leaf-maple", "leaf-maple-off", "leaf-off", "leak", "leak-off", "lectern", "led-off", "led-on", "led-outline", "led-strip", "led-strip-variant", "led-strip-variant-off", "led-variant-off", "led-variant-on", "led-variant-outline", "leek", "less-than", "less-than-or-equal", "library", "library-books", "library-outline", "library-shelves", "license", "lifebuoy", "light-flood-down", "light-flood-up", "light-recessed", "light-switch", "light-switch-off", "lightbulb", "lightbulb-alert", "lightbulb-alert-outline", "lightbulb-auto", "lightbulb-auto-outline", "lightbulb-cfl", "lightbulb-cfl-off", "lightbulb-cfl-spiral", "lightbulb-cfl-spiral-off", "lightbulb-fluorescent-tube", "lightbulb-fluorescent-tube-outline", "lightbulb-group", "lightbulb-group-off", "lightbulb-group-off-outline", "lightbulb-group-outline", "lightbulb-multiple", "lightbulb-multiple-off", "lightbulb-multiple-off-outline", "lightbulb-multiple-outline", "lightbulb-night", "lightbulb-night-outline", "lightbulb-off", "lightbulb-off-outline", "lightbulb-on", "lightbulb-on-10", "lightbulb-on-20", "lightbulb-on-30", "lightbulb-on-40", "lightbulb-on-50", "lightbulb-on-60", "lightbulb-on-70", "lightbulb-on-80", "lightbulb-on-90", "lightbulb-on-outline", "lightbulb-outline", "lightbulb-question", "lightbulb-question-outline", "lightbulb-spot", "lightbulb-spot-off", "lightbulb-variant", "lightbulb-variant-outline", "lighthouse", "lighthouse-on", "lightning-bolt", "lightning-bolt-circle", "lightning-bolt-outline", "line-scan", "lingerie", "link", "link-box", "link-box-outline", "link-box-variant", "link-box-variant-outline", "link-circle", "link-circle-outline", "link-edit", "link-lock", "link-off", "link-plus", "link-variant", "link-variant-minus", "link-variant-off", "link-variant-plus", "link-variant-remove", "linkedin", "linode", "linux", "linux-mint", "lipstick", "liquid-spot", "liquor", "list-box", "list-box-outline", "list-status", "litecoin", "loading", "location-enter", "location-exit", "lock", "lock-alert", "lock-alert-outline", "lock-check", "lock-check-outline", "lock-clock", "lock-minus", "lock-minus-outline", "lock-off", "lock-off-outline", "lock-open", "lock-open-alert", "lock-open-alert-outline", "lock-open-check", "lock-open-check-outline", "lock-open-minus", "lock-open-minus-outline", "lock-open-outline", "lock-open-plus", "lock-open-plus-outline", "lock-open-remove", "lock-open-remove-outline", "lock-open-variant", "lock-open-variant-outline", "lock-outline", "lock-pattern", "lock-percent", "lock-percent-open", "lock-percent-open-outline", "lock-percent-open-variant", "lock-percent-open-variant-outline", "lock-percent-outline", "lock-plus", "lock-plus-outline", "lock-question", "lock-remove", "lock-remove-outline", "lock-reset", "lock-smart", "locker", "locker-multiple", "login", "login-variant", "logout", "logout-variant", "longitude", "looks", "lotion", "lotion-outline", "lotion-plus", "lotion-plus-outline", "loupe", "lumx", "lungs", "lyft", "mace", "magazine-pistol", "magazine-rifle", "magic-staff", "magnet", "magnet-on", "magnify", "magnify-close", "magnify-expand", "magnify-minus", "magnify-minus-cursor", "magnify-minus-outline", "magnify-plus", "magnify-plus-cursor", "magnify-plus-outline", "magnify-remove-cursor", "magnify-remove-outline", "magnify-scan", "mail", "mail-ru", "mailbox", "mailbox-open", "mailbox-open-outline", "mailbox-open-up", "mailbox-open-up-outline", "mailbox-outline", "mailbox-up", "mailbox-up-outline", "manjaro", "map", "map-check", "map-check-outline", "map-clock", "map-clock-outline", "map-legend", "map-marker", "map-marker-account", "map-marker-account-outline", "map-marker-alert", "map-marker-alert-outline", "map-marker-check", "map-marker-check-outline", "map-marker-circle", "map-marker-distance", "map-marker-down", "map-marker-left", "map-marker-left-outline", "map-marker-minus", "map-marker-minus-outline", "map-marker-multiple", "map-marker-multiple-outline", "map-marker-off", "map-marker-off-outline", "map-marker-outline", "map-marker-path", "map-marker-plus", "map-marker-plus-outline", "map-marker-question", "map-marker-question-outline", "map-marker-radius", "map-marker-radius-outline", "map-marker-remove", "map-marker-remove-outline", "map-marker-remove-variant", "map-marker-right", "map-marker-right-outline", "map-marker-star", "map-marker-star-outline", "map-marker-up", "map-minus", "map-outline", "map-plus", "map-search", "map-search-outline", "mapbox", "margin", "marker", "marker-cancel", "marker-check", "mastodon", "mastodon-variant", "material-design", "material-ui", "math-compass", "math-cos", "math-integral", "math-integral-box", "math-log", "math-norm", "math-norm-box", "math-sin", "math-tan", "matrix", "maxcdn", "medal", "medal-outline", "medical-bag", "medical-cotton-swab", "medication", "medication-outline", "meditation", "medium", "meetup", "memory", "memory-arrow-down", "menorah", "menorah-fire", "menu", "menu-close", "menu-down", "menu-down-outline", "menu-left", "menu-left-outline", "menu-open", "menu-right", "menu-right-outline", "menu-swap", "menu-swap-outline", "menu-up", "menu-up-outline", "merge", "message", "message-alert", "message-alert-outline", "message-arrow-left", "message-arrow-left-outline", "message-arrow-right", "message-arrow-right-outline", "message-badge", "message-badge-outline", "message-bookmark", "message-bookmark-outline", "message-bulleted", "message-bulleted-off", "message-check", "message-check-outline", "message-cog", "message-cog-outline", "message-draw", "message-fast", "message-fast-outline", "message-flash", "message-flash-outline", "message-image", "message-image-outline", "message-lock", "message-lock-outline", "message-minus", "message-minus-outline", "message-off", "message-off-outline", "message-outline", "message-plus", "message-plus-outline", "message-processing", "message-processing-outline", "message-question", "message-question-outline", "message-reply", "message-reply-outline", "message-reply-text", "message-reply-text-outline", "message-settings", "message-settings-outline", "message-star", "message-star-outline", "message-text", "message-text-clock", "message-text-clock-outline", "message-text-fast", "message-text-fast-outline", "message-text-lock", "message-text-lock-outline", "message-text-outline", "message-video", "meteor", "meter-electric", "meter-electric-outline", "meter-gas", "meter-gas-outline", "metronome", "metronome-tick", "micro-sd", "microphone", "microphone-message", "microphone-message-off", "microphone-minus", "microphone-off", "microphone-outline", "microphone-plus", "microphone-question", "microphone-question-outline", "microphone-settings", "microphone-variant", "microphone-variant-off", "microscope", "microsoft", "microsoft-access", "microsoft-azure", "microsoft-azure-devops", "microsoft-bing", "microsoft-dynamics-365", "microsoft-edge", "microsoft-edge-legacy", "microsoft-excel", "microsoft-internet-explorer", "microsoft-office", "microsoft-onedrive", "microsoft-onenote", "microsoft-outlook", "microsoft-powerpoint", "microsoft-sharepoint", "microsoft-teams", "microsoft-visual-studio", "microsoft-visual-studio-code", "microsoft-windows", "microsoft-windows-classic", "microsoft-word", "microsoft-xbox", "microsoft-xbox-controller", "microsoft-xbox-controller-battery-alert", "microsoft-xbox-controller-battery-charging", "microsoft-xbox-controller-battery-empty", "microsoft-xbox-controller-battery-full", "microsoft-xbox-controller-battery-low", "microsoft-xbox-controller-battery-medium", "microsoft-xbox-controller-battery-unknown", "microsoft-xbox-controller-menu", "microsoft-xbox-controller-off", "microsoft-xbox-controller-view", "microsoft-yammer", "microwave", "microwave-off", "middleware", "middleware-outline", "midi", "midi-input", "midi-port", "mine", "minecraft", "mini-sd", "minidisc", "minus", "minus-box", "minus-box-multiple", "minus-box-multiple-outline", "minus-box-outline", "minus-circle", "minus-circle-multiple", "minus-circle-multiple-outline", "minus-circle-off", "minus-circle-off-outline", "minus-circle-outline", "minus-network", "minus-network-outline", "minus-thick", "mirror", "mirror-rectangle", "mirror-variant", "mixcloud", "mixed-martial-arts", "mixed-reality", "mixer", "molecule", "molecule-co", "molecule-co2", "monitor", "monitor-account", "monitor-arrow-down", "monitor-arrow-down-variant", "monitor-cellphone", "monitor-cellphone-star", "monitor-dashboard", "monitor-edit", "monitor-eye", "monitor-lock", "monitor-multiple", "monitor-off", "monitor-screenshot", "monitor-share", "monitor-shimmer", "monitor-small", "monitor-speaker", "monitor-speaker-off", "monitor-star", "monitor-vertical", "moon-first-quarter", "moon-full", "moon-last-quarter", "moon-new", "moon-waning-crescent", "moon-waning-gibbous", "moon-waxing-crescent", "moon-waxing-gibbous", "moped", "moped-electric", "moped-electric-outline", "moped-outline", "more", "mortar-pestle", "mortar-pestle-plus", "mosque", "mosque-outline", "mother-heart", "mother-nurse", "motion", "motion-outline", "motion-pause", "motion-pause-outline", "motion-play", "motion-play-outline", "motion-sensor", "motion-sensor-off", "motorbike", "motorbike-electric", "motorbike-off", "mouse", "mouse-bluetooth", "mouse-left-click", "mouse-left-click-outline", "mouse-move-down", "mouse-move-up", "mouse-move-vertical", "mouse-off", "mouse-outline", "mouse-right-click", "mouse-right-click-outline", "mouse-scroll-wheel", "mouse-variant", "mouse-variant-off", "move-resize", "move-resize-variant", "movie", "movie-check", "movie-check-outline", "movie-cog", "movie-cog-outline", "movie-edit", "movie-edit-outline", "movie-filter", "movie-filter-outline", "movie-minus", "movie-minus-outline", "movie-off", "movie-off-outline", "movie-open", "movie-open-check", "movie-open-check-outline", "movie-open-cog", "movie-open-cog-outline", "movie-open-edit", "movie-open-edit-outline", "movie-open-minus", "movie-open-minus-outline", "movie-open-off", "movie-open-off-outline", "movie-open-outline", "movie-open-play", "movie-open-play-outline", "movie-open-plus", "movie-open-plus-outline", "movie-open-remove", "movie-open-remove-outline", "movie-open-settings", "movie-open-settings-outline", "movie-open-star", "movie-open-star-outline", "movie-outline", "movie-play", "movie-play-outline", "movie-plus", "movie-plus-outline", "movie-remove", "movie-remove-outline", "movie-roll", "movie-search", "movie-search-outline", "movie-settings", "movie-settings-outline", "movie-star", "movie-star-outline", "mower", "mower-bag", "mower-bag-on", "mower-on", "muffin", "multicast", "multimedia", "multiplication", "multiplication-box", "mushroom", "mushroom-off", "mushroom-off-outline", "mushroom-outline", "music", "music-accidental-double-flat", "music-accidental-double-sharp", "music-accidental-flat", "music-accidental-natural", "music-accidental-sharp", "music-box", "music-box-multiple", "music-box-multiple-outline", "music-box-outline", "music-circle", "music-circle-outline", "music-clef-alto", "music-clef-bass", "music-clef-treble", "music-note", "music-note-bluetooth", "music-note-bluetooth-off", "music-note-eighth", "music-note-eighth-dotted", "music-note-half", "music-note-half-dotted", "music-note-minus", "music-note-off", "music-note-off-outline", "music-note-outline", "music-note-plus", "music-note-quarter", "music-note-quarter-dotted", "music-note-sixteenth", "music-note-sixteenth-dotted", "music-note-whole", "music-note-whole-dotted", "music-off", "music-rest-eighth", "music-rest-half", "music-rest-quarter", "music-rest-sixteenth", "music-rest-whole", "mustache", "nail", "nas", "nativescript", "nature", "nature-outline", "nature-people", "nature-people-outline", "navigation", "navigation-outline", "navigation-variant", "navigation-variant-outline", "near-me", "necklace", "needle", "needle-off", "nest-thermostat", "netflix", "network", "network-off", "network-off-outline", "network-outline", "network-pos", "network-strength-1", "network-strength-1-alert", "network-strength-2", "network-strength-2-alert", "network-strength-3", "network-strength-3-alert", "network-strength-4", "network-strength-4-alert", "network-strength-4-cog", "network-strength-alert", "network-strength-alert-outline", "network-strength-off", "network-strength-off-outline", "network-strength-outline", "new-box", "newspaper", "newspaper-check", "newspaper-minus", "newspaper-plus", "newspaper-remove", "newspaper-variant", "newspaper-variant-multiple", "newspaper-variant-multiple-outline", "newspaper-variant-outline", "nfc", "nfc-off", "nfc-search-variant", "nfc-tap", "nfc-variant", "nfc-variant-off", "ninja", "nintendo-game-boy", "nintendo-switch", "nintendo-wii", "nintendo-wiiu", "nix", "nodejs", "noodles", "not-equal", "not-equal-variant", "note", "note-alert", "note-alert-outline", "note-check", "note-check-outline", "note-edit", "note-edit-outline", "note-minus", "note-minus-outline", "note-multiple", "note-multiple-outline", "note-off", "note-off-outline", "note-outline", "note-plus", "note-plus-outline", "note-remove", "note-remove-outline", "note-search", "note-search-outline", "note-text", "note-text-outline", "notebook", "notebook-check", "notebook-check-outline", "notebook-edit", "notebook-edit-outline", "notebook-heart", "notebook-heart-outline", "notebook-minus", "notebook-minus-outline", "notebook-multiple", "notebook-outline", "notebook-plus", "notebook-plus-outline", "notebook-remove", "notebook-remove-outline", "notification-clear-all", "npm", "npm-variant", "npm-variant-outline", "nuke", "null", "numeric", "numeric-0", "numeric-0-box", "numeric-0-box-multiple", "numeric-0-box-multiple-outline", "numeric-0-box-outline", "numeric-0-circle", "numeric-0-circle-outline", "numeric-1", "numeric-1-box", "numeric-1-box-multiple", "numeric-1-box-multiple-outline", "numeric-1-box-outline", "numeric-1-circle", "numeric-1-circle-outline", "numeric-10", "numeric-10-box", "numeric-10-box-multiple", "numeric-10-box-multiple-outline", "numeric-10-box-outline", "numeric-10-circle", "numeric-10-circle-outline", "numeric-2", "numeric-2-box", "numeric-2-box-multiple", "numeric-2-box-multiple-outline", "numeric-2-box-outline", "numeric-2-circle", "numeric-2-circle-outline", "numeric-3", "numeric-3-box", "numeric-3-box-multiple", "numeric-3-box-multiple-outline", "numeric-3-box-outline", "numeric-3-circle", "numeric-3-circle-outline", "numeric-4", "numeric-4-box", "numeric-4-box-multiple", "numeric-4-box-multiple-outline", "numeric-4-box-outline", "numeric-4-circle", "numeric-4-circle-outline", "numeric-5", "numeric-5-box", "numeric-5-box-multiple", "numeric-5-box-multiple-outline", "numeric-5-box-outline", "numeric-5-circle", "numeric-5-circle-outline", "numeric-6", "numeric-6-box", "numeric-6-box-multiple", "numeric-6-box-multiple-outline", "numeric-6-box-outline", "numeric-6-circle", "numeric-6-circle-outline", "numeric-7", "numeric-7-box", "numeric-7-box-multiple", "numeric-7-box-multiple-outline", "numeric-7-box-outline", "numeric-7-circle", "numeric-7-circle-outline", "numeric-8", "numeric-8-box", "numeric-8-box-multiple", "numeric-8-box-multiple-outline", "numeric-8-box-outline", "numeric-8-circle", "numeric-8-circle-outline", "numeric-9", "numeric-9-box", "numeric-9-box-multiple", "numeric-9-box-multiple-outline", "numeric-9-box-outline", "numeric-9-circle", "numeric-9-circle-outline", "numeric-9-plus", "numeric-9-plus-box", "numeric-9-plus-box-multiple", "numeric-9-plus-box-multiple-outline", "numeric-9-plus-box-outline", "numeric-9-plus-circle", "numeric-9-plus-circle-outline", "numeric-negative-1", "numeric-off", "numeric-positive-1", "nut", "nutrition", "nuxt", "oar", "ocarina", "oci", "ocr", "octagon", "octagon-outline", "octagram", "octagram-edit", "octagram-edit-outline", "octagram-minus", "octagram-minus-outline", "octagram-outline", "octagram-plus", "octagram-plus-outline", "octahedron", "octahedron-off", "odnoklassniki", "offer", "office-building", "office-building-cog", "office-building-cog-outline", "office-building-marker", "office-building-marker-outline", "office-building-minus", "office-building-minus-outline", "office-building-outline", "office-building-plus", "office-building-plus-outline", "office-building-remove", "office-building-remove-outline", "oil", "oil-lamp", "oil-level", "oil-temperature", "om", "omega", "one-up", "onedrive", "onenote", "onepassword", "opacity", "open-in-app", "open-in-new", "open-source-initiative", "openid", "opera", "orbit", "orbit-variant", "order-alphabetical-ascending", "order-alphabetical-descending", "order-bool-ascending", "order-bool-ascending-variant", "order-bool-descending", "order-bool-descending-variant", "order-numeric-ascending", "order-numeric-descending", "origin", "ornament", "ornament-variant", "outbox", "outdent", "outdoor-lamp", "outlook", "overscan", "owl", "pac-man", "package", "package-check", "package-down", "package-up", "package-variant", "package-variant-closed", "package-variant-closed-check", "package-variant-closed-minus", "package-variant-closed-plus", "package-variant-closed-remove", "package-variant-minus", "package-variant-plus", "package-variant-remove", "page-first", "page-last", "page-layout-body", "page-layout-footer", "page-layout-header", "page-layout-header-footer", "page-layout-sidebar-left", "page-layout-sidebar-right", "page-next", "page-next-outline", "page-previous", "page-previous-outline", "pail", "pail-minus", "pail-minus-outline", "pail-off", "pail-off-outline", "pail-outline", "pail-plus", "pail-plus-outline", "pail-remove", "pail-remove-outline", "palette", "palette-advanced", "palette-outline", "palette-swatch", "palette-swatch-outline", "palette-swatch-variant", "palm-tree", "pan", "pan-bottom-left", "pan-bottom-right", "pan-down", "pan-horizontal", "pan-left", "pan-right", "pan-top-left", "pan-top-right", "pan-up", "pan-vertical", "panda", "pandora", "panorama", "panorama-fisheye", "panorama-horizontal", "panorama-horizontal-outline", "panorama-outline", "panorama-sphere", "panorama-sphere-outline", "panorama-variant", "panorama-variant-outline", "panorama-vertical", "panorama-vertical-outline", "panorama-wide-angle", "panorama-wide-angle-outline", "paper-cut-vertical", "paper-roll", "paper-roll-outline", "paperclip", "paperclip-check", "paperclip-lock", "paperclip-minus", "paperclip-off", "paperclip-plus", "paperclip-remove", "parachute", "parachute-outline", "paragliding", "parking", "party-popper", "passport", "passport-alert", "passport-biometric", "passport-cancel", "passport-check", "passport-minus", "passport-plus", "passport-remove", "pasta", "patio-heater", "patreon", "pause", "pause-box", "pause-box-outline", "pause-circle", "pause-circle-outline", "pause-octagon", "pause-octagon-outline", "paw", "paw-off", "paw-off-outline", "paw-outline", "paypal", "peace", "peanut", "peanut-off", "peanut-off-outline", "peanut-outline", "pen", "pen-lock", "pen-minus", "pen-off", "pen-plus", "pen-remove", "pencil", "pencil-box", "pencil-box-multiple", "pencil-box-multiple-outline", "pencil-box-outline", "pencil-circle", "pencil-circle-outline", "pencil-lock", "pencil-lock-outline", "pencil-minus", "pencil-minus-outline", "pencil-off", "pencil-off-outline", "pencil-outline", "pencil-plus", "pencil-plus-outline", "pencil-remove", "pencil-remove-outline", "pencil-ruler", "pencil-ruler-outline", "penguin", "pentagon", "pentagon-outline", "pentagram", "percent", "percent-box", "percent-box-outline", "percent-circle", "percent-circle-outline", "percent-outline", "periodic-table", "periscope", "perspective-less", "perspective-more", "ph", "phone", "phone-alert", "phone-alert-outline", "phone-bluetooth", "phone-bluetooth-outline", "phone-cancel", "phone-cancel-outline", "phone-check", "phone-check-outline", "phone-classic", "phone-classic-off", "phone-clock", "phone-dial", "phone-dial-outline", "phone-forward", "phone-forward-outline", "phone-hangup", "phone-hangup-outline", "phone-in-talk", "phone-in-talk-outline", "phone-incoming", "phone-incoming-outgoing", "phone-incoming-outgoing-outline", "phone-incoming-outline", "phone-lock", "phone-lock-outline", "phone-log", "phone-log-outline", "phone-message", "phone-message-outline", "phone-minus", "phone-minus-outline", "phone-missed", "phone-missed-outline", "phone-off", "phone-off-outline", "phone-outgoing", "phone-outgoing-outline", "phone-outline", "phone-paused", "phone-paused-outline", "phone-plus", "phone-plus-outline", "phone-refresh", "phone-refresh-outline", "phone-remove", "phone-remove-outline", "phone-return", "phone-return-outline", "phone-ring", "phone-ring-outline", "phone-rotate-landscape", "phone-rotate-portrait", "phone-settings", "phone-settings-outline", "phone-sync", "phone-sync-outline", "phone-voip", "pi", "pi-box", "pi-hole", "piano", "piano-off", "pickaxe", "picture-in-picture-bottom-right", "picture-in-picture-bottom-right-outline", "picture-in-picture-top-right", "picture-in-picture-top-right-outline", "pier", "pier-crane", "pig", "pig-variant", "pig-variant-outline", "piggy-bank", "piggy-bank-outline", "pill", "pill-multiple", "pill-off", "pillar", "pin", "pin-off", "pin-off-outline", "pin-outline", "pine-tree", "pine-tree-box", "pine-tree-fire", "pine-tree-variant", "pine-tree-variant-outline", "pinterest", "pinterest-box", "pinwheel", "pinwheel-outline", "pipe", "pipe-disconnected", "pipe-leak", "pipe-valve", "pipe-wrench", "pirate", "pistol", "piston", "pitchfork", "pizza", "plane-car", "plane-train", "play", "play-box", "play-box-edit-outline", "play-box-lock", "play-box-lock-open", "play-box-lock-open-outline", "play-box-lock-outline", "play-box-multiple", "play-box-multiple-outline", "play-box-outline", "play-circle", "play-circle-outline", "play-network", "play-network-outline", "play-outline", "play-pause", "play-protected-content", "play-speed", "playlist-check", "playlist-edit", "playlist-minus", "playlist-music", "playlist-music-outline", "playlist-play", "playlist-plus", "playlist-remove", "playlist-star", "plex", "pliers", "plus", "plus-box", "plus-box-multiple", "plus-box-multiple-outline", "plus-box-outline", "plus-circle", "plus-circle-multiple", "plus-circle-multiple-outline", "plus-circle-outline", "plus-lock", "plus-lock-open", "plus-minus", "plus-minus-box", "plus-minus-variant", "plus-network", "plus-network-outline", "plus-outline", "plus-thick", "pocket", "podcast", "podium", "podium-bronze", "podium-gold", "podium-silver", "point-of-sale", "pokeball", "pokemon-go", "poker-chip", "polaroid", "police-badge", "police-badge-outline", "police-station", "poll", "polo", "polymer", "pool", "pool-thermometer", "popcorn", "post", "post-lamp", "post-outline", "postage-stamp", "pot", "pot-mix", "pot-mix-outline", "pot-outline", "pot-steam", "pot-steam-outline", "pound", "pound-box", "pound-box-outline", "power", "power-cycle", "power-off", "power-on", "power-plug", "power-plug-battery", "power-plug-battery-outline", "power-plug-off", "power-plug-off-outline", "power-plug-outline", "power-settings", "power-sleep", "power-socket", "power-socket-au", "power-socket-ch", "power-socket-de", "power-socket-eu", "power-socket-fr", "power-socket-it", "power-socket-jp", "power-socket-uk", "power-socket-us", "power-standby", "powershell", "prescription", "presentation", "presentation-play", "pretzel", "prezi", "printer", "printer-3d", "printer-3d-nozzle", "printer-3d-nozzle-alert", "printer-3d-nozzle-alert-outline", "printer-3d-nozzle-heat", "printer-3d-nozzle-heat-outline", "printer-3d-nozzle-off", "printer-3d-nozzle-off-outline", "printer-3d-nozzle-outline", "printer-3d-off", "printer-alert", "printer-check", "printer-eye", "printer-off", "printer-off-outline", "printer-outline", "printer-pos", "printer-pos-alert", "printer-pos-alert-outline", "printer-pos-cancel", "printer-pos-cancel-outline", "printer-pos-check", "printer-pos-check-outline", "printer-pos-cog", "printer-pos-cog-outline", "printer-pos-edit", "printer-pos-edit-outline", "printer-pos-minus", "printer-pos-minus-outline", "printer-pos-network", "printer-pos-network-outline", "printer-pos-off", "printer-pos-off-outline", "printer-pos-outline", "printer-pos-pause", "printer-pos-pause-outline", "printer-pos-play", "printer-pos-play-outline", "printer-pos-plus", "printer-pos-plus-outline", "printer-pos-refresh", "printer-pos-refresh-outline", "printer-pos-remove", "printer-pos-remove-outline", "printer-pos-star", "printer-pos-star-outline", "printer-pos-stop", "printer-pos-stop-outline", "printer-pos-sync", "printer-pos-sync-outline", "printer-pos-wrench", "printer-pos-wrench-outline", "printer-search", "printer-settings", "printer-wireless", "priority-high", "priority-low", "professional-hexagon", "progress-alert", "progress-check", "progress-clock", "progress-close", "progress-download", "progress-helper", "progress-pencil", "progress-question", "progress-star", "progress-star-four-points", "progress-tag", "progress-upload", "progress-wrench", "projector", "projector-off", "projector-screen", "projector-screen-off", "projector-screen-off-outline", "projector-screen-outline", "projector-screen-variant", "projector-screen-variant-off", "projector-screen-variant-off-outline", "projector-screen-variant-outline", "propane-tank", "propane-tank-outline", "protocol", "publish", "publish-off", "pulse", "pump", "pump-off", "pumpkin", "purse", "purse-outline", "puzzle", "puzzle-check", "puzzle-check-outline", "puzzle-edit", "puzzle-edit-outline", "puzzle-heart", "puzzle-heart-outline", "puzzle-minus", "puzzle-minus-outline", "puzzle-outline", "puzzle-plus", "puzzle-plus-outline", "puzzle-remove", "puzzle-remove-outline", "puzzle-star", "puzzle-star-outline", "pyramid", "pyramid-off", "qi", "qqchat", "qrcode", "qrcode-edit", "qrcode-minus", "qrcode-plus", "qrcode-remove", "qrcode-scan", "quadcopter", "quality-high", "quality-low", "quality-medium", "queue-first-in-last-out", "quick-reply", "quicktime", "quora", "rabbit", "rabbit-variant", "rabbit-variant-outline", "racing-helmet", "racquetball", "radar", "radiator", "radiator-disabled", "radiator-off", "radio", "radio-am", "radio-fm", "radio-handheld", "radio-off", "radio-tower", "radioactive", "radioactive-circle", "radioactive-circle-outline", "radioactive-off", "radiobox-blank", "radiobox-indeterminate-variant", "radiobox-marked", "radiology-box", "radiology-box-outline", "radius", "radius-outline", "railroad-light", "rake", "raspberry-pi", "raw", "raw-off", "ray-end", "ray-end-arrow", "ray-start", "ray-start-arrow", "ray-start-end", "ray-start-vertex-end", "ray-vertex", "razor-double-edge", "razor-single-edge", "rdio", "react", "read", "receipt", "receipt-clock", "receipt-clock-outline", "receipt-outline", "receipt-send", "receipt-send-outline", "receipt-text", "receipt-text-arrow-left", "receipt-text-arrow-left-outline", "receipt-text-arrow-right", "receipt-text-arrow-right-outline", "receipt-text-check", "receipt-text-check-outline", "receipt-text-clock", "receipt-text-clock-outline", "receipt-text-edit", "receipt-text-edit-outline", "receipt-text-minus", "receipt-text-minus-outline", "receipt-text-outline", "receipt-text-plus", "receipt-text-plus-outline", "receipt-text-remove", "receipt-text-remove-outline", "receipt-text-send", "receipt-text-send-outline", "record", "record-circle", "record-circle-outline", "record-player", "record-rec", "rectangle", "rectangle-outline", "recycle", "recycle-variant", "reddit", "redhat", "redo", "redo-variant", "reflect-horizontal", "reflect-vertical", "refresh", "refresh-auto", "refresh-circle", "regex", "registered-trademark", "reiterate", "relation-many-to-many", "relation-many-to-one", "relation-many-to-one-or-many", "relation-many-to-only-one", "relation-many-to-zero-or-many", "relation-many-to-zero-or-one", "relation-one-or-many-to-many", "relation-one-or-many-to-one", "relation-one-or-many-to-one-or-many", "relation-one-or-many-to-only-one", "relation-one-or-many-to-zero-or-many", "relation-one-or-many-to-zero-or-one", "relation-one-to-many", "relation-one-to-one", "relation-one-to-one-or-many", "relation-one-to-only-one", "relation-one-to-zero-or-many", "relation-one-to-zero-or-one", "relation-only-one-to-many", "relation-only-one-to-one", "relation-only-one-to-one-or-many", "relation-only-one-to-only-one", "relation-only-one-to-zero-or-many", "relation-only-one-to-zero-or-one", "relation-zero-or-many-to-many", "relation-zero-or-many-to-one", "relation-zero-or-many-to-one-or-many", "relation-zero-or-many-to-only-one", "relation-zero-or-many-to-zero-or-many", "relation-zero-or-many-to-zero-or-one", "relation-zero-or-one-to-many", "relation-zero-or-one-to-one", "relation-zero-or-one-to-one-or-many", "relation-zero-or-one-to-only-one", "relation-zero-or-one-to-zero-or-many", "relation-zero-or-one-to-zero-or-one", "relative-scale", "reload", "reload-alert", "reminder", "remote", "remote-desktop", "remote-off", "remote-tv", "remote-tv-off", "rename", "rename-box", "rename-box-outline", "rename-outline", "reorder-horizontal", "reorder-vertical", "repeat", "repeat-off", "repeat-once", "repeat-variant", "replay", "reply", "reply-all", "reply-all-outline", "reply-circle", "reply-outline", "reproduction", "resistor", "resistor-nodes", "resize", "resize-bottom-right", "responsive", "restart", "restart-alert", "restart-off", "restore", "restore-alert", "rewind", "rewind-10", "rewind-15", "rewind-30", "rewind-45", "rewind-5", "rewind-60", "rewind-outline", "rhombus", "rhombus-medium", "rhombus-medium-outline", "rhombus-outline", "rhombus-split", "rhombus-split-outline", "ribbon", "rice", "rickshaw", "rickshaw-electric", "ring", "rivet", "road", "road-variant", "robber", "robot", "robot-angry", "robot-angry-outline", "robot-confused", "robot-confused-outline", "robot-dead", "robot-dead-outline", "robot-excited", "robot-excited-outline", "robot-happy", "robot-happy-outline", "robot-industrial", "robot-industrial-outline", "robot-love", "robot-love-outline", "robot-mower", "robot-mower-outline", "robot-off", "robot-off-outline", "robot-outline", "robot-vacuum", "robot-vacuum-alert", "robot-vacuum-off", "robot-vacuum-variant", "robot-vacuum-variant-alert", "robot-vacuum-variant-off", "rocket", "rocket-launch", "rocket-launch-outline", "rocket-outline", "rodent", "roller-shade", "roller-shade-closed", "roller-skate", "roller-skate-off", "rollerblade", "rollerblade-off", "rollupjs", "rolodex", "rolodex-outline", "roman-numeral-1", "roman-numeral-10", "roman-numeral-2", "roman-numeral-3", "roman-numeral-4", "roman-numeral-5", "roman-numeral-6", "roman-numeral-7", "roman-numeral-8", "roman-numeral-9", "room-service", "room-service-outline", "rotate-360", "rotate-3d", "rotate-3d-variant", "rotate-left", "rotate-left-variant", "rotate-orbit", "rotate-right", "rotate-right-variant", "rounded-corner", "router", "router-network", "router-network-wireless", "router-wireless", "router-wireless-off", "router-wireless-settings", "routes", "routes-clock", "rowing", "rss", "rss-box", "rss-off", "rug", "rugby", "ruler", "ruler-square", "ruler-square-compass", "run", "run-fast", "rv-truck", "sack", "sack-outline", "sack-percent", "safe", "safe-square", "safe-square-outline", "safety-goggles", "safety-googles", "sail-boat", "sail-boat-sink", "sale", "sale-outline", "salesforce", "sass", "satellite", "satellite-uplink", "satellite-variant", "sausage", "sausage-off", "saw-blade", "sawtooth-wave", "saxophone", "scale", "scale-balance", "scale-bathroom", "scale-off", "scale-unbalanced", "scan-helper", "scanner", "scanner-off", "scatter-plot", "scatter-plot-outline", "scent", "scent-off", "school", "school-outline", "scissors-cutting", "scooter", "scooter-electric", "scoreboard", "scoreboard-outline", "screen-rotation", "screen-rotation-lock", "screw-flat-top", "screw-lag", "screw-machine-flat-top", "screw-machine-round-top", "screw-round-top", "screwdriver", "script", "script-outline", "script-text", "script-text-key", "script-text-key-outline", "script-text-outline", "script-text-play", "script-text-play-outline", "sd", "seal", "seal-variant", "search-web", "seat", "seat-flat", "seat-flat-angled", "seat-individual-suite", "seat-legroom-extra", "seat-legroom-normal", "seat-legroom-reduced", "seat-outline", "seat-passenger", "seat-recline-extra", "seat-recline-normal", "seatbelt", "security", "security-close", "security-network", "seed", "seed-off", "seed-off-outline", "seed-outline", "seed-plus", "seed-plus-outline", "seesaw", "segment", "select", "select-all", "select-arrow-down", "select-arrow-up", "select-color", "select-compare", "select-drag", "select-group", "select-inverse", "select-marker", "select-multiple", "select-multiple-marker", "select-off", "select-place", "select-remove", "select-search", "selection", "selection-drag", "selection-ellipse", "selection-ellipse-arrow-inside", "selection-ellipse-remove", "selection-lasso", "selection-marker", "selection-multiple", "selection-multiple-marker", "selection-off", "selection-remove", "selection-search", "semantic-web", "send", "send-check", "send-check-outline", "send-circle", "send-circle-outline", "send-clock", "send-clock-outline", "send-lock", "send-lock-outline", "send-outline", "send-variant", "send-variant-clock", "send-variant-clock-outline", "send-variant-outline", "serial-port", "server", "server-minus", "server-minus-outline", "server-network", "server-network-off", "server-network-outline", "server-off", "server-outline", "server-plus", "server-plus-outline", "server-remove", "server-security", "set-all", "set-center", "set-center-right", "set-left", "set-left-center", "set-left-right", "set-merge", "set-none", "set-right", "set-split", "set-square", "set-top-box", "settings-helper", "shaker", "shaker-outline", "shape", "shape-circle-plus", "shape-outline", "shape-oval-plus", "shape-plus", "shape-plus-outline", "shape-polygon-plus", "shape-rectangle-plus", "shape-square-plus", "shape-square-rounded-plus", "share", "share-all", "share-all-outline", "share-circle", "share-off", "share-off-outline", "share-outline", "share-variant", "share-variant-outline", "shark", "shark-fin", "shark-fin-outline", "shark-off", "sheep", "shield", "shield-account", "shield-account-outline", "shield-account-variant", "shield-account-variant-outline", "shield-airplane", "shield-airplane-outline", "shield-alert", "shield-alert-outline", "shield-bug", "shield-bug-outline", "shield-car", "shield-check", "shield-check-outline", "shield-cross", "shield-cross-outline", "shield-crown", "shield-crown-outline", "shield-edit", "shield-edit-outline", "shield-half", "shield-half-full", "shield-home", "shield-home-outline", "shield-key", "shield-key-outline", "shield-link-variant", "shield-link-variant-outline", "shield-lock", "shield-lock-open", "shield-lock-open-outline", "shield-lock-outline", "shield-moon", "shield-moon-outline", "shield-off", "shield-off-outline", "shield-outline", "shield-plus", "shield-plus-outline", "shield-refresh", "shield-refresh-outline", "shield-remove", "shield-remove-outline", "shield-search", "shield-star", "shield-star-outline", "shield-sun", "shield-sun-outline", "shield-sword", "shield-sword-outline", "shield-sync", "shield-sync-outline", "shimmer", "ship-wheel", "shipping-pallet", "shoe-ballet", "shoe-cleat", "shoe-formal", "shoe-heel", "shoe-print", "shoe-sneaker", "shopify", "shopping", "shopping-music", "shopping-outline", "shopping-search", "shopping-search-outline", "shore", "shovel", "shovel-off", "shower", "shower-head", "shredder", "shuffle", "shuffle-disabled", "shuffle-variant", "shuriken", "sickle", "sigma", "sigma-lower", "sign-caution", "sign-direction", "sign-direction-minus", "sign-direction-plus", "sign-direction-remove", "sign-language", "sign-language-outline", "sign-pole", "sign-real-estate", "sign-text", "sign-yield", "signal", "signal-2g", "signal-3g", "signal-4g", "signal-5g", "signal-cellular-1", "signal-cellular-2", "signal-cellular-3", "signal-cellular-outline", "signal-distance-variant", "signal-hspa", "signal-hspa-plus", "signal-off", "signal-variant", "signature", "signature-freehand", "signature-image", "signature-text", "silo", "silo-outline", "silverware", "silverware-clean", "silverware-fork", "silverware-fork-knife", "silverware-spoon", "silverware-variant", "sim", "sim-alert", "sim-alert-outline", "sim-off", "sim-off-outline", "sim-outline", "simple-icons", "sina-weibo", "sine-wave", "sitemap", "sitemap-outline", "size-l", "size-m", "size-s", "size-xl", "size-xs", "size-xxl", "size-xxs", "size-xxxl", "skate", "skate-off", "skateboard", "skateboarding", "skew-less", "skew-more", "ski", "ski-cross-country", "ski-water", "skip-backward", "skip-backward-outline", "skip-forward", "skip-forward-outline", "skip-next", "skip-next-circle", "skip-next-circle-outline", "skip-next-outline", "skip-previous", "skip-previous-circle", "skip-previous-circle-outline", "skip-previous-outline", "skull", "skull-crossbones", "skull-crossbones-outline", "skull-outline", "skull-scan", "skull-scan-outline", "skype", "skype-business", "slack", "slackware", "slash-forward", "slash-forward-box", "sledding", "sleep", "sleep-off", "slide", "slope-downhill", "slope-uphill", "slot-machine", "slot-machine-outline", "smart-card", "smart-card-off", "smart-card-off-outline", "smart-card-outline", "smart-card-reader", "smart-card-reader-outline", "smog", "smoke", "smoke-detector", "smoke-detector-alert", "smoke-detector-alert-outline", "smoke-detector-off", "smoke-detector-off-outline", "smoke-detector-outline", "smoke-detector-variant", "smoke-detector-variant-alert", "smoke-detector-variant-off", "smoking", "smoking-off", "smoking-pipe", "smoking-pipe-off", "snail", "snake", "snapchat", "snowboard", "snowflake", "snowflake-alert", "snowflake-check", "snowflake-melt", "snowflake-off", "snowflake-thermometer", "snowflake-variant", "snowman", "snowmobile", "snowshoeing", "soccer", "soccer-field", "social-distance-2-meters", "social-distance-6-feet", "sofa", "sofa-outline", "sofa-single", "sofa-single-outline", "solar-panel", "solar-panel-large", "solar-power", "solar-power-variant", "solar-power-variant-outline", "soldering-iron", "solid", "sony-playstation", "sort", "sort-alphabetical-ascending", "sort-alphabetical-ascending-variant", "sort-alphabetical-descending", "sort-alphabetical-descending-variant", "sort-alphabetical-variant", "sort-ascending", "sort-bool-ascending", "sort-bool-ascending-variant", "sort-bool-descending", "sort-bool-descending-variant", "sort-calendar-ascending", "sort-calendar-descending", "sort-clock-ascending", "sort-clock-ascending-outline", "sort-clock-descending", "sort-clock-descending-outline", "sort-descending", "sort-numeric-ascending", "sort-numeric-ascending-variant", "sort-numeric-descending", "sort-numeric-descending-variant", "sort-numeric-variant", "sort-reverse-variant", "sort-variant", "sort-variant-lock", "sort-variant-lock-open", "sort-variant-off", "sort-variant-remove", "soundbar", "soundcloud", "source-branch", "source-branch-check", "source-branch-minus", "source-branch-plus", "source-branch-refresh", "source-branch-remove", "source-branch-sync", "source-commit", "source-commit-end", "source-commit-end-local", "source-commit-local", "source-commit-next-local", "source-commit-start", "source-commit-start-next-local", "source-fork", "source-merge", "source-pull", "source-repository", "source-repository-multiple", "soy-sauce", "soy-sauce-off", "spa", "spa-outline", "space-invaders", "space-station", "spade", "speaker", "speaker-bluetooth", "speaker-message", "speaker-multiple", "speaker-off", "speaker-pause", "speaker-play", "speaker-stop", "speaker-wireless", "spear", "speedometer", "speedometer-medium", "speedometer-slow", "spellcheck", "sphere", "sphere-off", "spider", "spider-outline", "spider-thread", "spider-web", "spirit-level", "split-horizontal", "split-vertical", "spoon-sugar", "spotify", "spotlight", "spotlight-beam", "spray", "spray-bottle", "spreadsheet", "sprinkler", "sprinkler-fire", "sprinkler-variant", "sprout", "sprout-outline", "square", "square-circle", "square-circle-outline", "square-edit-outline", "square-inc", "square-inc-cash", "square-medium", "square-medium-outline", "square-off", "square-off-outline", "square-opacity", "square-outline", "square-root", "square-root-box", "square-rounded", "square-rounded-badge", "square-rounded-badge-outline", "square-rounded-outline", "square-small", "square-wave", "squeegee", "ssh", "stack-exchange", "stack-overflow", "stackpath", "stadium", "stadium-outline", "stadium-variant", "stairs", "stairs-box", "stairs-down", "stairs-up", "stamper", "standard-definition", "star", "star-box", "star-box-multiple", "star-box-multiple-outline", "star-box-outline", "star-check", "star-check-outline", "star-circle", "star-circle-outline", "star-cog", "star-cog-outline", "star-crescent", "star-david", "star-face", "star-four-points", "star-four-points-box", "star-four-points-box-outline", "star-four-points-circle", "star-four-points-circle-outline", "star-four-points-outline", "star-four-points-small", "star-half", "star-half-full", "star-minus", "star-minus-outline", "star-off", "star-off-outline", "star-outline", "star-plus", "star-plus-outline", "star-remove", "star-remove-outline", "star-settings", "star-settings-outline", "star-shooting", "star-shooting-outline", "star-three-points", "star-three-points-outline", "state-machine", "steam", "steam-box", "steering", "steering-off", "step-backward", "step-backward-2", "step-forward", "step-forward-2", "stethoscope", "sticker", "sticker-alert", "sticker-alert-outline", "sticker-check", "sticker-check-outline", "sticker-circle-outline", "sticker-emoji", "sticker-minus", "sticker-minus-outline", "sticker-outline", "sticker-plus", "sticker-plus-outline", "sticker-remove", "sticker-remove-outline", "sticker-text", "sticker-text-outline", "stocking", "stomach", "stool", "stool-outline", "stop", "stop-circle", "stop-circle-outline", "storage-tank", "storage-tank-outline", "store", "store-24-hour", "store-alert", "store-alert-outline", "store-check", "store-check-outline", "store-clock", "store-clock-outline", "store-cog", "store-cog-outline", "store-edit", "store-edit-outline", "store-marker", "store-marker-outline", "store-minus", "store-minus-outline", "store-off", "store-off-outline", "store-outline", "store-plus", "store-plus-outline", "store-remove", "store-remove-outline", "store-search", "store-search-outline", "store-settings", "store-settings-outline", "storefront", "storefront-check", "storefront-check-outline", "storefront-edit", "storefront-edit-outline", "storefront-minus", "storefront-minus-outline", "storefront-outline", "storefront-plus", "storefront-plus-outline", "storefront-remove", "storefront-remove-outline", "stove", "strategy", "strava", "stretch-to-page", "stretch-to-page-outline", "string-lights", "string-lights-off", "subdirectory-arrow-left", "subdirectory-arrow-right", "submarine", "subtitles", "subtitles-outline", "subway", "subway-alert-variant", "subway-variant", "summit", "sun-angle", "sun-angle-outline", "sun-clock", "sun-clock-outline", "sun-compass", "sun-snowflake", "sun-snowflake-variant", "sun-thermometer", "sun-thermometer-outline", "sun-wireless", "sun-wireless-outline", "sunglasses", "surfing", "surround-sound", "surround-sound-2-0", "surround-sound-2-1", "surround-sound-3-1", "surround-sound-5-1", "surround-sound-5-1-2", "surround-sound-7-1", "svg", "swap-horizontal", "swap-horizontal-bold", "swap-horizontal-circle", "swap-horizontal-circle-outline", "swap-horizontal-hidden", "swap-horizontal-variant", "swap-vertical", "swap-vertical-bold", "swap-vertical-circle", "swap-vertical-circle-outline", "swap-vertical-variant", "swim", "switch", "sword", "sword-cross", "syllabary-hangul", "syllabary-hiragana", "syllabary-katakana", "syllabary-katakana-halfwidth", "symbol", "symfony", "synagogue", "synagogue-outline", "sync", "sync-alert", "sync-circle", "sync-off", "tab", "tab-minus", "tab-plus", "tab-remove", "tab-search", "tab-unselected", "table", "table-account", "table-alert", "table-arrow-down", "table-arrow-left", "table-arrow-right", "table-arrow-up", "table-border", "table-cancel", "table-chair", "table-check", "table-clock", "table-cog", "table-column", "table-column-plus-after", "table-column-plus-before", "table-column-remove", "table-column-width", "table-edit", "table-eye", "table-eye-off", "table-filter", "table-furniture", "table-headers-eye", "table-headers-eye-off", "table-heart", "table-key", "table-large", "table-large-plus", "table-large-remove", "table-lock", "table-merge-cells", "table-minus", "table-multiple", "table-network", "table-of-contents", "table-off", "table-picnic", "table-pivot", "table-plus", "table-question", "table-refresh", "table-remove", "table-row", "table-row-height", "table-row-plus-after", "table-row-plus-before", "table-row-remove", "table-search", "table-settings", "table-split-cell", "table-star", "table-sync", "table-tennis", "tablet", "tablet-android", "tablet-cellphone", "tablet-dashboard", "tablet-ipad", "taco", "tag", "tag-arrow-down", "tag-arrow-down-outline", "tag-arrow-left", "tag-arrow-left-outline", "tag-arrow-right", "tag-arrow-right-outline", "tag-arrow-up", "tag-arrow-up-outline", "tag-check", "tag-check-outline", "tag-edit", "tag-edit-outline", "tag-faces", "tag-heart", "tag-heart-outline", "tag-hidden", "tag-minus", "tag-minus-outline", "tag-multiple", "tag-multiple-outline", "tag-off", "tag-off-outline", "tag-outline", "tag-plus", "tag-plus-outline", "tag-remove", "tag-remove-outline", "tag-search", "tag-search-outline", "tag-text", "tag-text-outline", "tailwind", "tally-mark-1", "tally-mark-2", "tally-mark-3", "tally-mark-4", "tally-mark-5", "tangram", "tank", "tanker-truck", "tape-drive", "tape-measure", "target", "target-account", "target-variant", "taxi", "tea", "tea-outline", "teamspeak", "teamviewer", "teddy-bear", "telegram", "telescope", "television", "television-ambient-light", "television-box", "television-classic", "television-classic-off", "television-guide", "television-off", "television-pause", "television-play", "television-shimmer", "television-speaker", "television-speaker-off", "television-stop", "temperature-celsius", "temperature-fahrenheit", "temperature-kelvin", "temple-buddhist", "temple-buddhist-outline", "temple-hindu", "temple-hindu-outline", "tennis", "tennis-ball", "tennis-ball-outline", "tent", "terraform", "terrain", "test-tube", "test-tube-empty", "test-tube-off", "text", "text-account", "text-box", "text-box-check", "text-box-check-outline", "text-box-edit", "text-box-edit-outline", "text-box-minus", "text-box-minus-outline", "text-box-multiple", "text-box-multiple-outline", "text-box-outline", "text-box-plus", "text-box-plus-outline", "text-box-remove", "text-box-remove-outline", "text-box-search", "text-box-search-outline", "text-long", "text-recognition", "text-search", "text-search-variant", "text-shadow", "text-short", "texture", "texture-box", "theater", "theme-light-dark", "thermometer", "thermometer-alert", "thermometer-auto", "thermometer-bluetooth", "thermometer-check", "thermometer-chevron-down", "thermometer-chevron-up", "thermometer-high", "thermometer-lines", "thermometer-low", "thermometer-minus", "thermometer-off", "thermometer-plus", "thermometer-probe", "thermometer-probe-off", "thermometer-water", "thermostat", "thermostat-auto", "thermostat-box", "thermostat-box-auto", "thermostat-cog", "thought-bubble", "thought-bubble-outline", "thumb-down", "thumb-down-outline", "thumb-up", "thumb-up-outline", "thumbs-up-down", "thumbs-up-down-outline", "ticket", "ticket-account", "ticket-confirmation", "ticket-confirmation-outline", "ticket-outline", "ticket-percent", "ticket-percent-outline", "tie", "tilde", "tilde-off", "timelapse", "timeline", "timeline-alert", "timeline-alert-outline", "timeline-check", "timeline-check-outline", "timeline-clock", "timeline-clock-outline", "timeline-minus", "timeline-minus-outline", "timeline-outline", "timeline-plus", "timeline-plus-outline", "timeline-question", "timeline-question-outline", "timeline-remove", "timeline-remove-outline", "timeline-text", "timeline-text-outline", "timer", "timer-10", "timer-3", "timer-alert", "timer-alert-outline", "timer-cancel", "timer-cancel-outline", "timer-check", "timer-check-outline", "timer-cog", "timer-cog-outline", "timer-edit", "timer-edit-outline", "timer-lock", "timer-lock-open", "timer-lock-open-outline", "timer-lock-outline", "timer-marker", "timer-marker-outline", "timer-minus", "timer-minus-outline", "timer-music", "timer-music-outline", "timer-off", "timer-off-outline", "timer-outline", "timer-pause", "timer-pause-outline", "timer-play", "timer-play-outline", "timer-plus", "timer-plus-outline", "timer-refresh", "timer-refresh-outline", "timer-remove", "timer-remove-outline", "timer-sand", "timer-sand-complete", "timer-sand-empty", "timer-sand-full", "timer-sand-paused", "timer-settings", "timer-settings-outline", "timer-star", "timer-star-outline", "timer-stop", "timer-stop-outline", "timer-sync", "timer-sync-outline", "timetable", "tire", "toaster", "toaster-off", "toaster-oven", "toggle-switch", "toggle-switch-off", "toggle-switch-off-outline", "toggle-switch-outline", "toggle-switch-variant", "toggle-switch-variant-off", "toilet", "toolbox", "toolbox-outline", "tools", "tooltip", "tooltip-account", "tooltip-cellphone", "tooltip-check", "tooltip-check-outline", "tooltip-edit", "tooltip-edit-outline", "tooltip-image", "tooltip-image-outline", "tooltip-minus", "tooltip-minus-outline", "tooltip-outline", "tooltip-plus", "tooltip-plus-outline", "tooltip-question", "tooltip-question-outline", "tooltip-remove", "tooltip-remove-outline", "tooltip-text", "tooltip-text-outline", "tooth", "tooth-outline", "toothbrush", "toothbrush-electric", "toothbrush-paste", "tor", "torch", "tortoise", "toslink", "touch-text-outline", "tournament", "tow-truck", "tower-beach", "tower-fire", "town-hall", "toy-brick", "toy-brick-marker", "toy-brick-marker-outline", "toy-brick-minus", "toy-brick-minus-outline", "toy-brick-outline", "toy-brick-plus", "toy-brick-plus-outline", "toy-brick-remove", "toy-brick-remove-outline", "toy-brick-search", "toy-brick-search-outline", "track-light", "track-light-off", "trackpad", "trackpad-lock", "tractor", "tractor-variant", "trademark", "traffic-cone", "traffic-light", "traffic-light-outline", "train", "train-bus", "train-car", "train-car-autorack", "train-car-box", "train-car-box-full", "train-car-box-open", "train-car-caboose", "train-car-centerbeam", "train-car-centerbeam-full", "train-car-container", "train-car-flatbed", "train-car-flatbed-car", "train-car-flatbed-tank", "train-car-gondola", "train-car-gondola-full", "train-car-hopper", "train-car-hopper-covered", "train-car-hopper-full", "train-car-intermodal", "train-car-passenger", "train-car-passenger-door", "train-car-passenger-door-open", "train-car-passenger-variant", "train-car-tank", "train-variant", "tram", "tram-side", "transcribe", "transcribe-close", "transfer", "transfer-down", "transfer-left", "transfer-right", "transfer-up", "transit-connection", "transit-connection-horizontal", "transit-connection-variant", "transit-detour", "transit-skip", "transit-transfer", "transition", "transition-masked", "translate", "translate-off", "translate-variant", "transmission-tower", "transmission-tower-export", "transmission-tower-import", "transmission-tower-off", "trash-can", "trash-can-outline", "tray", "tray-alert", "tray-arrow-down", "tray-arrow-up", "tray-full", "tray-minus", "tray-plus", "tray-remove", "treasure-chest", "treasure-chest-outline", "tree", "tree-outline", "trello", "trending-down", "trending-neutral", "trending-up", "triangle", "triangle-down", "triangle-down-outline", "triangle-outline", "triangle-small-down", "triangle-small-up", "triangle-wave", "triforce", "trophy", "trophy-award", "trophy-broken", "trophy-outline", "trophy-variant", "trophy-variant-outline", "truck", "truck-alert", "truck-alert-outline", "truck-cargo-container", "truck-check", "truck-check-outline", "truck-delivery", "truck-delivery-outline", "truck-fast", "truck-fast-outline", "truck-flatbed", "truck-minus", "truck-minus-outline", "truck-off-road", "truck-off-road-off", "truck-outline", "truck-plus", "truck-plus-outline", "truck-remove", "truck-remove-outline", "truck-snowflake", "truck-trailer", "trumpet", "tshirt-crew", "tshirt-crew-outline", "tshirt-v", "tshirt-v-outline", "tsunami", "tumble-dryer", "tumble-dryer-alert", "tumble-dryer-off", "tumblr", "tumblr-box", "tumblr-reblog", "tune", "tune-variant", "tune-vertical", "tune-vertical-variant", "tunnel", "tunnel-outline", "turbine", "turkey", "turnstile", "turnstile-outline", "turtle", "twitch", "twitter", "twitter-box", "twitter-circle", "two-factor-authentication", "typewriter", "uber", "ubisoft", "ubuntu", "ufo", "ufo-outline", "ultra-high-definition", "umbraco", "umbrella", "umbrella-beach", "umbrella-beach-outline", "umbrella-closed", "umbrella-closed-outline", "umbrella-closed-variant", "umbrella-outline", "underwear-outline", "undo", "undo-variant", "unfold-less-horizontal", "unfold-less-vertical", "unfold-more-horizontal", "unfold-more-vertical", "ungroup", "unicode", "unicorn", "unicorn-variant", "unicycle", "unity", "unreal", "untappd", "update", "upload", "upload-box", "upload-box-outline", "upload-circle", "upload-circle-outline", "upload-lock", "upload-lock-outline", "upload-multiple", "upload-multiple-outline", "upload-network", "upload-network-outline", "upload-off", "upload-off-outline", "upload-outline", "usb", "usb-c-port", "usb-flash-drive", "usb-flash-drive-outline", "usb-port", "vacuum", "vacuum-outline", "valve", "valve-closed", "valve-open", "van-passenger", "van-utility", "vanish", "vanish-quarter", "vanity-light", "variable", "variable-box", "vector-arrange-above", "vector-arrange-below", "vector-bezier", "vector-circle", "vector-circle-variant", "vector-combine", "vector-curve", "vector-difference", "vector-difference-ab", "vector-difference-ba", "vector-ellipse", "vector-intersection", "vector-line", "vector-link", "vector-point", "vector-point-edit", "vector-point-minus", "vector-point-plus", "vector-point-select", "vector-polygon", "vector-polygon-variant", "vector-polyline", "vector-polyline-edit", "vector-polyline-minus", "vector-polyline-plus", "vector-polyline-remove", "vector-radius", "vector-rectangle", "vector-selection", "vector-square", "vector-square-close", "vector-square-edit", "vector-square-minus", "vector-square-open", "vector-square-plus", "vector-square-remove", "vector-triangle", "vector-union", "venmo", "vhs", "vibrate", "vibrate-off", "video", "video-2d", "video-3d", "video-3d-off", "video-3d-variant", "video-4k-box", "video-account", "video-box", "video-box-off", "video-check", "video-check-outline", "video-high-definition", "video-image", "video-input-antenna", "video-input-component", "video-input-hdmi", "video-input-scart", "video-input-svideo", "video-marker", "video-marker-outline", "video-minus", "video-minus-outline", "video-off", "video-off-outline", "video-outline", "video-plus", "video-plus-outline", "video-stabilization", "video-standard-definition", "video-switch", "video-switch-outline", "video-vintage", "video-wireless", "video-wireless-outline", "view-agenda", "view-agenda-outline", "view-array", "view-array-outline", "view-carousel", "view-carousel-outline", "view-column", "view-column-outline", "view-comfy", "view-comfy-outline", "view-compact", "view-compact-outline", "view-dashboard", "view-dashboard-edit", "view-dashboard-edit-outline", "view-dashboard-outline", "view-dashboard-variant", "view-dashboard-variant-outline", "view-day", "view-day-outline", "view-gallery", "view-gallery-outline", "view-grid", "view-grid-compact", "view-grid-outline", "view-grid-plus", "view-grid-plus-outline", "view-headline", "view-list", "view-list-outline", "view-module", "view-module-outline", "view-parallel", "view-parallel-outline", "view-quilt", "view-quilt-outline", "view-sequential", "view-sequential-outline", "view-split-horizontal", "view-split-vertical", "view-stream", "view-stream-outline", "view-week", "view-week-outline", "vimeo", "vine", "violin", "virtual-reality", "virus", "virus-off", "virus-off-outline", "virus-outline", "vk", "vk-box", "vk-circle", "vlc", "voicemail", "volcano", "volcano-outline", "volleyball", "volume", "volume-equal", "volume-high", "volume-low", "volume-medium", "volume-minus", "volume-mute", "volume-off", "volume-plus", "volume-source", "volume-variant-off", "volume-vibrate", "vote", "vote-outline", "vpn", "v<PERSON><PERSON><PERSON>", "vuetify", "walk", "wall", "wall-fire", "wall-sconce", "wall-sconce-flat", "wall-sconce-flat-outline", "wall-sconce-flat-variant", "wall-sconce-flat-variant-outline", "wall-sconce-outline", "wall-sconce-round", "wall-sconce-round-outline", "wall-sconce-round-variant", "wall-sconce-round-variant-outline", "wall-sconce-variant", "wallet", "wallet-bifold", "wallet-bifold-outline", "wallet-giftcard", "wallet-membership", "wallet-outline", "wallet-plus", "wallet-plus-outline", "wallet-travel", "wallpaper", "wan", "wardrobe", "wardrobe-outline", "warehouse", "washing-machine", "washing-machine-alert", "washing-machine-off", "watch", "watch-export", "watch-export-variant", "watch-import", "watch-import-variant", "watch-variant", "watch-vibrate", "watch-vibrate-off", "water", "water-alert", "water-alert-outline", "water-boiler", "water-boiler-alert", "water-boiler-auto", "water-boiler-off", "water-check", "water-check-outline", "water-circle", "water-minus", "water-minus-outline", "water-off", "water-off-outline", "water-opacity", "water-outline", "water-percent", "water-percent-alert", "water-plus", "water-plus-outline", "water-polo", "water-pump", "water-pump-off", "water-remove", "water-remove-outline", "water-sync", "water-thermometer", "water-thermometer-outline", "water-well", "water-well-outline", "waterfall", "watering-can", "watering-can-outline", "watermark", "wave", "wave-arrow-down", "wave-arrow-up", "wave-undercurrent", "waveform", "waves", "waves-arrow-left", "waves-arrow-right", "waves-arrow-up", "waze", "weather-cloudy", "weather-cloudy-alert", "weather-cloudy-arrow-right", "weather-cloudy-clock", "weather-dust", "weather-fog", "weather-hail", "weather-hazy", "weather-hurricane", "weather-hurricane-outline", "weather-lightning", "weather-lightning-rainy", "weather-moonset", "weather-moonset-down", "weather-moonset-up", "weather-night", "weather-night-partly-cloudy", "weather-partly-cloudy", "weather-partly-lightning", "weather-partly-rainy", "weather-partly-snowy", "weather-partly-snowy-rainy", "weather-pouring", "weather-rainy", "weather-snowy", "weather-snowy-heavy", "weather-snowy-rainy", "weather-sunny", "weather-sunny-alert", "weather-sunny-off", "weather-sunset", "weather-sunset-down", "weather-sunset-up", "weather-tornado", "weather-windy", "weather-windy-variant", "web", "web-box", "web-cancel", "web-check", "web-clock", "web-minus", "web-off", "web-plus", "web-refresh", "web-remove", "web-sync", "webcam", "webcam-off", "webhook", "webpack", "webrtc", "wechat", "weight", "weight-gram", "weight-kilogram", "weight-lifter", "weight-pound", "whatsapp", "wheel-barrow", "wheelchair", "wheelchair-accessibility", "whistle", "whistle-outline", "white-balance-auto", "white-balance-incandescent", "white-balance-iridescent", "white-balance-sunny", "widgets", "widgets-outline", "wifi", "wifi-alert", "wifi-arrow-down", "wifi-arrow-left", "wifi-arrow-left-right", "wifi-arrow-right", "wifi-arrow-up", "wifi-arrow-up-down", "wifi-cancel", "wifi-check", "wifi-cog", "wifi-lock", "wifi-lock-open", "wifi-marker", "wifi-minus", "wifi-off", "wifi-plus", "wifi-refresh", "wifi-remove", "wifi-settings", "wifi-star", "wifi-strength-1", "wifi-strength-1-alert", "wifi-strength-1-lock", "wifi-strength-1-lock-open", "wifi-strength-2", "wifi-strength-2-alert", "wifi-strength-2-lock", "wifi-strength-2-lock-open", "wifi-strength-3", "wifi-strength-3-alert", "wifi-strength-3-lock", "wifi-strength-3-lock-open", "wifi-strength-4", "wifi-strength-4-alert", "wifi-strength-4-lock", "wifi-strength-4-lock-open", "wifi-strength-alert-outline", "wifi-strength-lock-open-outline", "wifi-strength-lock-outline", "wifi-strength-off", "wifi-strength-off-outline", "wifi-strength-outline", "wifi-sync", "wikipedia", "wind-power", "wind-power-outline", "wind-turbine", "wind-turbine-alert", "wind-turbine-check", "window-close", "window-closed", "window-closed-variant", "window-maximize", "window-minimize", "window-open", "window-open-variant", "window-restore", "window-shutter", "window-shutter-alert", "window-shutter-auto", "window-shutter-cog", "window-shutter-open", "window-shutter-settings", "windsock", "wiper", "wiper-wash", "wiper-wash-alert", "wizard-hat", "wordpress", "wrap", "wrap-disabled", "wrench", "wrench-check", "wrench-check-outline", "wrench-clock", "wrench-clock-outline", "wrench-cog", "wrench-cog-outline", "wrench-outline", "wunderlist", "xamarin", "xamarin-outline", "xda", "xing", "xing-circle", "xml", "xmpp", "y-combinator", "yahoo", "yammer", "yeast", "yelp", "yin-yang", "yoga", "youtube", "youtube-gaming", "youtube-studio", "youtube-subscription", "youtube-tv", "yurt", "z-wave", "zend", "zigbee", "zip-box", "zip-box-outline", "zip-disk", "zodiac-aquarius", "zodiac-aries", "zodiac-cancer", "zodiac-capricorn", "zodiac-gemini", "zodiac-leo", "zodiac-libra", "zodiac-pisces", "zodiac-sagittarius", "zodiac-scorpio", "zodiac-taurus", "zodiac-virgo"]}, {"prefix": "ri", "info": {"name": "Remix Icon", "total": 2704, "version": "4.0.0", "author": {"name": "Remix Design", "url": "https://github.com/Remix-Design/RemixIcon"}, "license": {"title": "Apache 2.0", "spdx": "Apache-2.0", "url": "https://github.com/Remix-Design/RemixIcon/blob/master/License"}, "samples": ["lock-2-line", "mark-pen-fill", "moon-line"], "height": 24, "category": "General", "palette": false}, "icons": ["24-hours-fill", "24-hours-line", "4k-fill", "4k-line", "a-b", "account-box-fill", "account-box-line", "account-circle-fill", "account-circle-line", "account-pin-box-fill", "account-pin-box-line", "account-pin-circle-fill", "account-pin-circle-line", "add-box-fill", "add-box-line", "add-circle-fill", "add-circle-line", "add-fill", "add-line", "admin-fill", "admin-line", "advertisement-fill", "advertisement-line", "ai-generate", "airplay-fill", "airplay-line", "alarm-fill", "alarm-line", "alarm-warning-fill", "alarm-warning-line", "album-fill", "album-line", "alert-fill", "alert-line", "aliens-fill", "aliens-line", "align-bottom", "align-center", "align-justify", "align-left", "align-right", "align-top", "align-vertically", "alipay-fill", "alipay-line", "amazon-fill", "amazon-line", "anchor-fill", "anchor-line", "ancient-gate-fill", "ancient-gate-line", "ancient-pavilion-fill", "ancient-pavilion-line", "android-fill", "android-line", "angularjs-fill", "angularjs-line", "anticlockwise-2-fill", "anticlockwise-2-line", "anticlockwise-fill", "anticlockwise-line", "app-store-fill", "app-store-line", "apple-fill", "apple-line", "apps-2-fill", "apps-2-line", "apps-fill", "apps-line", "archive-2-fill", "archive-2-line", "archive-drawer-fill", "archive-drawer-line", "archive-fill", "archive-line", "archive-stack-fill", "archive-stack-line", "armchair-fill", "armchair-line", "arrow-down-circle-fill", "arrow-down-circle-line", "arrow-down-double-fill", "arrow-down-double-line", "arrow-down-fill", "arrow-down-line", "arrow-down-s-fill", "arrow-down-s-line", "arrow-drop-down-fill", "arrow-drop-down-line", "arrow-drop-left-fill", "arrow-drop-left-line", "arrow-drop-right-fill", "arrow-drop-right-line", "arrow-drop-up-fill", "arrow-drop-up-line", "arrow-go-back-fill", "arrow-go-back-line", "arrow-go-forward-fill", "arrow-go-forward-line", "arrow-left-circle-fill", "arrow-left-circle-line", "arrow-left-double-fill", "arrow-left-double-line", "arrow-left-down-fill", "arrow-left-down-line", "arrow-left-fill", "arrow-left-line", "arrow-left-right-fill", "arrow-left-right-line", "arrow-left-s-fill", "arrow-left-s-line", "arrow-left-up-fill", "arrow-left-up-line", "arrow-right-circle-fill", "arrow-right-circle-line", "arrow-right-double-fill", "arrow-right-double-line", "arrow-right-down-fill", "arrow-right-down-line", "arrow-right-fill", "arrow-right-line", "arrow-right-s-fill", "arrow-right-s-line", "arrow-right-up-fill", "arrow-right-up-line", "arrow-turn-back-fill", "arrow-turn-back-line", "arrow-turn-forward-fill", "arrow-turn-forward-line", "arrow-up-circle-fill", "arrow-up-circle-line", "arrow-up-double-fill", "arrow-up-double-line", "arrow-up-down-fill", "arrow-up-down-line", "arrow-up-fill", "arrow-up-line", "arrow-up-s-fill", "arrow-up-s-line", "artboard-2-fill", "artboard-2-line", "artboard-fill", "artboard-line", "article-fill", "article-line", "aspect-ratio-fill", "aspect-ratio-line", "asterisk", "at-fill", "at-line", "attachment-2", "attachment-fill", "attachment-line", "auction-fill", "auction-line", "award-fill", "award-line", "baidu-fill", "baidu-line", "ball-pen-fill", "ball-pen-line", "bank-card-2-fill", "bank-card-2-line", "bank-card-fill", "bank-card-line", "bank-fill", "bank-line", "bar-chart-2-fill", "bar-chart-2-line", "bar-chart-box-fill", "bar-chart-box-line", "bar-chart-fill", "bar-chart-grouped-fill", "bar-chart-grouped-line", "bar-chart-horizontal-fill", "bar-chart-horizontal-line", "bar-chart-line", "barcode-box-fill", "barcode-box-line", "barcode-fill", "barcode-line", "bard-fill", "bard-line", "barricade-fill", "barricade-line", "base-station-fill", "base-station-line", "basketball-fill", "basketball-line", "battery-2-charge-fill", "battery-2-charge-line", "battery-2-fill", "battery-2-line", "battery-charge-fill", "battery-charge-line", "battery-fill", "battery-line", "battery-low-fill", "battery-low-line", "battery-saver-fill", "battery-saver-line", "battery-share-fill", "battery-share-line", "bear-smile-fill", "bear-smile-line", "beer-fill", "beer-line", "behance-fill", "behance-line", "bell-fill", "bell-line", "bike-fill", "bike-line", "bilibili-fill", "bilibili-line", "bill-fill", "bill-line", "billiards-fill", "billiards-line", "bit-coin-fill", "bit-coin-line", "blaze-fill", "blaze-line", "blender-fill", "blender-line", "blogger-fill", "blogger-line", "bluetooth-connect-fill", "bluetooth-connect-line", "bluetooth-fill", "bluetooth-line", "blur-off-fill", "blur-off-line", "bnb-fill", "bnb-line", "body-scan-fill", "body-scan-line", "bold", "book-2-fill", "book-2-line", "book-3-fill", "book-3-line", "book-fill", "book-line", "book-marked-fill", "book-marked-line", "book-open-fill", "book-open-line", "book-read-fill", "book-read-line", "booklet-fill", "booklet-line", "bookmark-2-fill", "bookmark-2-line", "bookmark-3-fill", "bookmark-3-line", "bookmark-fill", "bookmark-line", "bootstrap-fill", "bootstrap-line", "bowl-fill", "bowl-line", "box-1-fill", "box-1-line", "box-2-fill", "box-2-line", "box-3-fill", "box-3-line", "boxing-fill", "boxing-line", "braces-fill", "braces-line", "brackets-fill", "brackets-line", "brain-fill", "brain-line", "bread-fill", "bread-line", "briefcase-2-fill", "briefcase-2-line", "briefcase-3-fill", "briefcase-3-line", "briefcase-4-fill", "briefcase-4-line", "briefcase-5-fill", "briefcase-5-line", "briefcase-fill", "briefcase-line", "bring-forward", "bring-to-front", "broadcast-fill", "broadcast-line", "brush-2-fill", "brush-2-line", "brush-3-fill", "brush-3-line", "brush-4-fill", "brush-4-line", "brush-fill", "brush-line", "btc-fill", "btc-line", "bubble-chart-fill", "bubble-chart-line", "bug-2-fill", "bug-2-line", "bug-fill", "bug-line", "building-2-fill", "building-2-line", "building-3-fill", "building-3-line", "building-4-fill", "building-4-line", "building-fill", "building-line", "bus-2-fill", "bus-2-line", "bus-fill", "bus-line", "bus-wifi-fill", "bus-wifi-line", "cactus-fill", "cactus-line", "cake-2-fill", "cake-2-line", "cake-3-fill", "cake-3-line", "cake-fill", "cake-line", "calculator-fill", "calculator-line", "calendar-2-fill", "calendar-2-line", "calendar-check-fill", "calendar-check-line", "calendar-close-fill", "calendar-close-line", "calendar-event-fill", "calendar-event-line", "calendar-fill", "calendar-line", "calendar-schedule-fill", "calendar-schedule-line", "calendar-todo-fill", "calendar-todo-line", "calendar-view", "camera-2-fill", "camera-2-line", "camera-3-fill", "camera-3-line", "camera-fill", "camera-lens-fill", "camera-lens-line", "camera-line", "camera-off-fill", "camera-off-line", "camera-switch-fill", "camera-switch-line", "candle-fill", "candle-line", "capsule-fill", "capsule-line", "car-fill", "car-line", "car-washing-fill", "car-washing-line", "caravan-fill", "caravan-line", "carousel-view", "cash-fill", "cash-line", "cast-fill", "cast-line", "cellphone-fill", "cellphone-line", "celsius-fill", "celsius-line", "centos-fill", "centos-line", "character-recognition-fill", "character-recognition-line", "charging-pile-2-fill", "charging-pile-2-line", "charging-pile-fill", "charging-pile-line", "chat-1-fill", "chat-1-line", "chat-2-fill", "chat-2-line", "chat-3-fill", "chat-3-line", "chat-4-fill", "chat-4-line", "chat-check-fill", "chat-check-line", "chat-delete-fill", "chat-delete-line", "chat-download-fill", "chat-download-line", "chat-follow-up-fill", "chat-follow-up-line", "chat-forward-fill", "chat-forward-line", "chat-heart-fill", "chat-heart-line", "chat-history-fill", "chat-history-line", "chat-new-fill", "chat-new-line", "chat-off-fill", "chat-off-line", "chat-poll-fill", "chat-poll-line", "chat-private-fill", "chat-private-line", "chat-quote-fill", "chat-quote-line", "chat-settings-fill", "chat-settings-line", "chat-smile-2-fill", "chat-smile-2-line", "chat-smile-3-fill", "chat-smile-3-line", "chat-smile-fill", "chat-smile-line", "chat-thread-fill", "chat-thread-line", "chat-upload-fill", "chat-upload-line", "chat-voice-fill", "chat-voice-line", "check-double-fill", "check-double-line", "check-fill", "check-line", "checkbox-blank-circle-fill", "checkbox-blank-circle-line", "checkbox-blank-fill", "checkbox-blank-line", "checkbox-circle-fill", "checkbox-circle-line", "checkbox-fill", "checkbox-indeterminate-fill", "checkbox-indeterminate-line", "checkbox-line", "checkbox-multiple-blank-fill", "checkbox-multiple-blank-line", "checkbox-multiple-fill", "checkbox-multiple-line", "china-railway-fill", "china-railway-line", "chrome-fill", "chrome-line", "circle-fill", "circle-line", "clapperboard-fill", "clapperboard-line", "clipboard-fill", "clipboard-line", "clockwise-2-fill", "clockwise-2-line", "clockwise-fill", "clockwise-line", "close-circle-fill", "close-circle-line", "close-fill", "close-line", "closed-captioning-fill", "closed-captioning-line", "cloud-fill", "cloud-line", "cloud-off-fill", "cloud-off-line", "cloud-windy-fill", "cloud-windy-line", "cloudy-2-fill", "cloudy-2-line", "cloudy-fill", "cloudy-line", "code-block", "code-box-fill", "code-box-line", "code-fill", "code-line", "code-s-fill", "code-s-line", "code-s-slash-fill", "code-s-slash-line", "code-view", "codepen-fill", "codepen-line", "coin-fill", "coin-line", "coins-fill", "coins-line", "collage-fill", "collage-line", "color-filter-fill", "color-filter-line", "command-fill", "command-line", "community-fill", "community-line", "compass-2-fill", "compass-2-line", "compass-3-fill", "compass-3-line", "compass-4-fill", "compass-4-line", "compass-discover-fill", "compass-discover-line", "compass-fill", "compass-line", "compasses-2-fill", "compasses-2-line", "compasses-fill", "compasses-line", "computer-fill", "computer-line", "contacts-book-2-fill", "contacts-book-2-line", "contacts-book-3-fill", "contacts-book-3-line", "contacts-book-fill", "contacts-book-line", "contacts-book-upload-fill", "contacts-book-upload-line", "contacts-fill", "contacts-line", "contract-fill", "contract-left-fill", "contract-left-line", "contract-left-right-fill", "contract-left-right-line", "contract-line", "contract-right-fill", "contract-right-line", "contract-up-down-fill", "contract-up-down-line", "contrast-2-fill", "contrast-2-line", "contrast-drop-2-fill", "contrast-drop-2-line", "contrast-drop-fill", "contrast-drop-line", "contrast-fill", "contrast-line", "copilot-fill", "copilot-line", "copper-coin-fill", "copper-coin-line", "copper-diamond-fill", "copper-diamond-line", "copyleft-fill", "copyleft-line", "copyright-fill", "copyright-line", "coreos-fill", "coreos-line", "corner-down-left-fill", "corner-down-left-line", "corner-down-right-fill", "corner-down-right-line", "corner-left-down-fill", "corner-left-down-line", "corner-left-up-fill", "corner-left-up-line", "corner-right-down-fill", "corner-right-down-line", "corner-right-up-fill", "corner-right-up-line", "corner-up-left-double-fill", "corner-up-left-double-line", "corner-up-left-fill", "corner-up-left-line", "corner-up-right-double-fill", "corner-up-right-double-line", "corner-up-right-fill", "corner-up-right-line", "coupon-2-fill", "coupon-2-line", "coupon-3-fill", "coupon-3-line", "coupon-4-fill", "coupon-4-line", "coupon-5-fill", "coupon-5-line", "coupon-fill", "coupon-line", "cpu-fill", "cpu-line", "creative-commons-by-fill", "creative-commons-by-line", "creative-commons-fill", "creative-commons-line", "creative-commons-nc-fill", "creative-commons-nc-line", "creative-commons-nd-fill", "creative-commons-nd-line", "creative-commons-sa-fill", "creative-commons-sa-line", "creative-commons-zero-fill", "creative-commons-zero-line", "criminal-fill", "criminal-line", "crop-2-fill", "crop-2-line", "crop-fill", "crop-line", "cross-fill", "cross-line", "crosshair-2-fill", "crosshair-2-line", "crosshair-fill", "crosshair-line", "css3-fill", "css3-line", "cup-fill", "cup-line", "currency-fill", "currency-line", "cursor-fill", "cursor-line", "customer-service-2-fill", "customer-service-2-line", "customer-service-fill", "customer-service-line", "dashboard-2-fill", "dashboard-2-line", "dashboard-3-fill", "dashboard-3-line", "dashboard-fill", "dashboard-line", "database-2-fill", "database-2-line", "database-fill", "database-line", "delete-back-2-fill", "delete-back-2-line", "delete-back-fill", "delete-back-line", "delete-bin-2-fill", "delete-bin-2-line", "delete-bin-3-fill", "delete-bin-3-line", "delete-bin-4-fill", "delete-bin-4-line", "delete-bin-5-fill", "delete-bin-5-line", "delete-bin-6-fill", "delete-bin-6-line", "delete-bin-7-fill", "delete-bin-7-line", "delete-bin-fill", "delete-bin-line", "delete-column", "delete-row", "device-fill", "device-line", "device-recover-fill", "device-recover-line", "dice-1-fill", "dice-1-line", "dice-2-fill", "dice-2-line", "dice-3-fill", "dice-3-line", "dice-4-fill", "dice-4-line", "dice-5-fill", "dice-5-line", "dice-6-fill", "dice-6-line", "dice-fill", "dice-line", "dingding-fill", "dingding-line", "direction-fill", "direction-line", "disc-fill", "disc-line", "discord-fill", "discord-line", "discount-percent-fill", "discount-percent-line", "discuss-fill", "discuss-line", "dislike-fill", "dislike-line", "disqus-fill", "disqus-line", "divide-fill", "divide-line", "donut-chart-fill", "donut-chart-line", "door-closed-fill", "door-closed-line", "door-fill", "door-line", "door-lock-box-fill", "door-lock-box-line", "door-lock-fill", "door-lock-line", "door-open-fill", "door-open-line", "dossier-fill", "dossier-line", "douban-fill", "douban-line", "double-quotes-l", "double-quotes-r", "download-2-fill", "download-2-line", "download-cloud-2-fill", "download-cloud-2-line", "download-cloud-fill", "download-cloud-line", "download-fill", "download-line", "draft-fill", "draft-line", "drag-drop-fill", "drag-drop-line", "drag-move-2-fill", "drag-move-2-line", "drag-move-fill", "drag-move-line", "draggable", "dribbble-fill", "dribbble-line", "drinks-2-fill", "drinks-2-line", "drinks-fill", "drinks-line", "drive-fill", "drive-line", "drizzle-fill", "drizzle-line", "drop-fill", "drop-line", "dropbox-fill", "dropbox-line", "dropdown-list", "dual-sim-1-fill", "dual-sim-1-line", "dual-sim-2-fill", "dual-sim-2-line", "dv-fill", "dv-line", "dvd-fill", "dvd-line", "e-bike-2-fill", "e-bike-2-line", "e-bike-fill", "e-bike-line", "earth-fill", "earth-line", "earthquake-fill", "earthquake-line", "edge-fill", "edge-line", "edge-new-fill", "edge-new-line", "edit-2-fill", "edit-2-line", "edit-box-fill", "edit-box-line", "edit-circle-fill", "edit-circle-line", "edit-fill", "edit-line", "eject-fill", "eject-line", "emoji-sticker-fill", "emoji-sticker-line", "emotion-2-fill", "emotion-2-line", "emotion-fill", "emotion-happy-fill", "emotion-happy-line", "emotion-laugh-fill", "emotion-laugh-line", "emotion-line", "emotion-normal-fill", "emotion-normal-line", "emotion-sad-fill", "emotion-sad-line", "emotion-unhappy-fill", "emotion-unhappy-line", "empathize-fill", "empathize-line", "emphasis", "emphasis-cn", "english-input", "equal-fill", "equal-line", "equalizer-2-fill", "equalizer-2-line", "equalizer-3-fill", "equalizer-3-line", "equalizer-fill", "equalizer-line", "eraser-fill", "eraser-line", "error-warning-fill", "error-warning-line", "eth-fill", "eth-line", "evernote-fill", "evernote-line", "exchange-2-fill", "exchange-2-line", "exchange-box-fill", "exchange-box-line", "exchange-cny-fill", "exchange-cny-line", "exchange-dollar-fill", "exchange-dollar-line", "exchange-fill", "exchange-funds-fill", "exchange-funds-line", "exchange-line", "expand-left-fill", "expand-left-line", "expand-left-right-fill", "expand-left-right-line", "expand-right-fill", "expand-right-line", "expand-up-down-fill", "expand-up-down-line", "export-fill", "export-line", "external-link-fill", "external-link-line", "eye-2-fill", "eye-2-line", "eye-close-fill", "eye-close-line", "eye-fill", "eye-line", "eye-off-fill", "eye-off-line", "facebook-box-fill", "facebook-box-line", "facebook-circle-fill", "facebook-circle-line", "facebook-fill", "facebook-line", "fahrenheit-fill", "fahrenheit-line", "feedback-fill", "feedback-line", "file-2-fill", "file-2-line", "file-3-fill", "file-3-line", "file-4-fill", "file-4-line", "file-add-fill", "file-add-line", "file-chart-2-fill", "file-chart-2-line", "file-chart-fill", "file-chart-line", "file-check-fill", "file-check-line", "file-close-fill", "file-close-line", "file-cloud-fill", "file-cloud-line", "file-code-fill", "file-code-line", "file-copy-2-fill", "file-copy-2-line", "file-copy-fill", "file-copy-line", "file-damage-fill", "file-damage-line", "file-download-fill", "file-download-line", "file-edit-fill", "file-edit-line", "file-excel-2-fill", "file-excel-2-line", "file-excel-fill", "file-excel-line", "file-fill", "file-forbid-fill", "file-forbid-line", "file-gif-fill", "file-gif-line", "file-history-fill", "file-history-line", "file-hwp-fill", "file-hwp-line", "file-image-fill", "file-image-line", "file-info-fill", "file-info-line", "file-line", "file-list-2-fill", "file-list-2-line", "file-list-3-fill", "file-list-3-line", "file-list-fill", "file-list-line", "file-lock-fill", "file-lock-line", "file-marked-fill", "file-marked-line", "file-music-fill", "file-music-line", "file-paper-2-fill", "file-paper-2-line", "file-paper-fill", "file-paper-line", "file-pdf-2-fill", "file-pdf-2-line", "file-pdf-fill", "file-pdf-line", "file-ppt-2-fill", "file-ppt-2-line", "file-ppt-fill", "file-ppt-line", "file-reduce-fill", "file-reduce-line", "file-search-fill", "file-search-line", "file-settings-fill", "file-settings-line", "file-shield-2-fill", "file-shield-2-line", "file-shield-fill", "file-shield-line", "file-shred-fill", "file-shred-line", "file-text-fill", "file-text-line", "file-transfer-fill", "file-transfer-line", "file-unknow-fill", "file-unknow-line", "file-upload-fill", "file-upload-line", "file-user-fill", "file-user-line", "file-video-fill", "file-video-line", "file-warning-fill", "file-warning-line", "file-word-2-fill", "file-word-2-line", "file-word-fill", "file-word-line", "file-zip-fill", "file-zip-line", "film-fill", "film-line", "filter-2-fill", "filter-2-line", "filter-3-fill", "filter-3-line", "filter-fill", "filter-line", "filter-off-fill", "filter-off-line", "find-replace-fill", "find-replace-line", "finder-fill", "finder-line", "fingerprint-2-fill", "fingerprint-2-line", "fingerprint-fill", "fingerprint-line", "fire-fill", "fire-line", "firefox-fill", "firefox-line", "first-aid-kit-fill", "first-aid-kit-line", "flag-2-fill", "flag-2-line", "flag-fill", "flag-line", "flashlight-fill", "flashlight-line", "flask-fill", "flask-line", "flickr-fill", "flickr-line", "flight-land-fill", "flight-land-line", "flight-takeoff-fill", "flight-takeoff-line", "flood-fill", "flood-line", "flow-chart", "flower-fill", "flower-line", "flutter-fill", "flutter-line", "focus-2-fill", "focus-2-line", "focus-3-fill", "focus-3-line", "focus-fill", "focus-line", "focus-mode", "foggy-fill", "foggy-line", "folder-2-fill", "folder-2-line", "folder-3-fill", "folder-3-line", "folder-4-fill", "folder-4-line", "folder-5-fill", "folder-5-line", "folder-6-fill", "folder-6-line", "folder-add-fill", "folder-add-line", "folder-chart-2-fill", "folder-chart-2-line", "folder-chart-fill", "folder-chart-line", "folder-check-fill", "folder-check-line", "folder-close-fill", "folder-close-line", "folder-cloud-fill", "folder-cloud-line", "folder-download-fill", "folder-download-line", "folder-fill", "folder-forbid-fill", "folder-forbid-line", "folder-history-fill", "folder-history-line", "folder-image-fill", "folder-image-line", "folder-info-fill", "folder-info-line", "folder-keyhole-fill", "folder-keyhole-line", "folder-line", "folder-lock-fill", "folder-lock-line", "folder-music-fill", "folder-music-line", "folder-open-fill", "folder-open-line", "folder-received-fill", "folder-received-line", "folder-reduce-fill", "folder-reduce-line", "folder-settings-fill", "folder-settings-line", "folder-shared-fill", "folder-shared-line", "folder-shield-2-fill", "folder-shield-2-line", "folder-shield-fill", "folder-shield-line", "folder-transfer-fill", "folder-transfer-line", "folder-unknow-fill", "folder-unknow-line", "folder-upload-fill", "folder-upload-line", "folder-user-fill", "folder-user-line", "folder-video-fill", "folder-video-line", "folder-warning-fill", "folder-warning-line", "folder-zip-fill", "folder-zip-line", "folders-fill", "folders-line", "font-color", "font-family", "font-mono", "font-sans", "font-sans-serif", "font-size", "font-size-2", "football-fill", "football-line", "footprint-fill", "footprint-line", "forbid-2-fill", "forbid-2-line", "forbid-fill", "forbid-line", "format-clear", "forward-10-fill", "forward-10-line", "forward-15-fill", "forward-15-line", "forward-30-fill", "forward-30-line", "forward-5-fill", "forward-5-line", "fridge-fill", "fridge-line", "fullscreen-exit-fill", "fullscreen-exit-line", "fullscreen-fill", "fullscreen-line", "function-fill", "function-line", "functions", "funds-box-fill", "funds-box-line", "funds-fill", "funds-line", "gallery-fill", "gallery-line", "gallery-upload-fill", "gallery-upload-line", "gallery-view", "gallery-view-2", "game-fill", "game-line", "gamepad-fill", "gamepad-line", "gas-station-fill", "gas-station-line", "gatsby-fill", "gatsby-line", "genderless-fill", "genderless-line", "ghost-2-fill", "ghost-2-line", "ghost-fill", "ghost-line", "ghost-smile-fill", "ghost-smile-line", "gift-2-fill", "gift-2-line", "gift-fill", "gift-line", "git-branch-fill", "git-branch-line", "git-close-pull-request-fill", "git-close-pull-request-line", "git-commit-fill", "git-commit-line", "git-fork-fill", "git-fork-line", "git-merge-fill", "git-merge-line", "git-pull-request-fill", "git-pull-request-line", "git-repository-commits-fill", "git-repository-commits-line", "git-repository-fill", "git-repository-line", "git-repository-private-fill", "git-repository-private-line", "github-fill", "github-line", "gitlab-fill", "gitlab-line", "glasses-2-fill", "glasses-2-line", "glasses-fill", "glasses-line", "global-fill", "global-line", "globe-fill", "globe-line", "goblet-fill", "goblet-line", "goggles-fill", "goggles-line", "google-fill", "google-line", "google-play-fill", "google-play-line", "government-fill", "government-line", "gps-fill", "gps-line", "gradienter-fill", "gradienter-line", "graduation-cap-fill", "graduation-cap-line", "grid-fill", "grid-line", "group-2-fill", "group-2-line", "group-fill", "group-line", "guide-fill", "guide-line", "h-1", "h-2", "h-3", "h-4", "h-5", "h-6", "hail-fill", "hail-line", "hammer-fill", "hammer-line", "hand", "hand-coin-fill", "hand-coin-line", "hand-heart-fill", "hand-heart-line", "hand-sanitizer-fill", "hand-sanitizer-line", "handbag-fill", "handbag-line", "hard-drive-2-fill", "hard-drive-2-line", "hard-drive-3-fill", "hard-drive-3-line", "hard-drive-fill", "hard-drive-line", "hashtag", "haze-2-fill", "haze-2-line", "haze-fill", "haze-line", "hd-fill", "hd-line", "heading", "headphone-fill", "headphone-line", "health-book-fill", "health-book-line", "heart-2-fill", "heart-2-line", "heart-3-fill", "heart-3-line", "heart-add-fill", "heart-add-line", "heart-fill", "heart-line", "heart-pulse-fill", "heart-pulse-line", "hearts-fill", "hearts-line", "heavy-showers-fill", "heavy-showers-line", "hexagon-fill", "hexagon-line", "history-fill", "history-line", "home-2-fill", "home-2-line", "home-3-fill", "home-3-line", "home-4-fill", "home-4-line", "home-5-fill", "home-5-line", "home-6-fill", "home-6-line", "home-7-fill", "home-7-line", "home-8-fill", "home-8-line", "home-fill", "home-gear-fill", "home-gear-line", "home-heart-fill", "home-heart-line", "home-line", "home-office-fill", "home-office-line", "home-smile-2-fill", "home-smile-2-line", "home-smile-fill", "home-smile-line", "home-wifi-fill", "home-wifi-line", "honor-of-kings-fill", "honor-of-kings-line", "honour-fill", "honour-line", "hospital-fill", "hospital-line", "hotel-bed-fill", "hotel-bed-line", "hotel-fill", "hotel-line", "hotspot-fill", "hotspot-line", "hourglass-2-fill", "hourglass-2-line", "hourglass-fill", "hourglass-line", "hq-fill", "hq-line", "html5-fill", "html5-line", "ie-fill", "ie-line", "image-2-fill", "image-2-line", "image-add-fill", "image-add-line", "image-circle-fill", "image-circle-line", "image-edit-fill", "image-edit-line", "image-fill", "image-line", "import-fill", "import-line", "inbox-2-fill", "inbox-2-line", "inbox-archive-fill", "inbox-archive-line", "inbox-fill", "inbox-line", "inbox-unarchive-fill", "inbox-unarchive-line", "increase-decrease-fill", "increase-decrease-line", "indent-decrease", "indent-increase", "indeterminate-circle-fill", "indeterminate-circle-line", "infinity-fill", "infinity-line", "info-i", "information-2-fill", "information-2-line", "information-fill", "information-line", "infrared-thermometer-fill", "infrared-thermometer-line", "ink-bottle-fill", "ink-bottle-line", "input-cursor-move", "input-field", "input-method-fill", "input-method-line", "insert-column-left", "insert-column-right", "insert-row-bottom", "insert-row-top", "instagram-fill", "instagram-line", "install-fill", "install-line", "instance-fill", "instance-line", "invision-fill", "invision-line", "italic", "javascript-fill", "javascript-line", "kakao-talk-fill", "kakao-talk-line", "kanban-view", "kanban-view-2", "key-2-fill", "key-2-line", "key-fill", "key-line", "keyboard-box-fill", "keyboard-box-line", "keyboard-fill", "keyboard-line", "keynote-fill", "keynote-line", "kick-fill", "kick-line", "knife-blood-fill", "knife-blood-line", "knife-fill", "knife-line", "landscape-fill", "landscape-line", "layout-2-fill", "layout-2-line", "layout-3-fill", "layout-3-line", "layout-4-fill", "layout-4-line", "layout-5-fill", "layout-5-line", "layout-6-fill", "layout-6-line", "layout-bottom-2-fill", "layout-bottom-2-line", "layout-bottom-fill", "layout-bottom-line", "layout-column-fill", "layout-column-line", "layout-fill", "layout-grid-fill", "layout-grid-line", "layout-left-2-fill", "layout-left-2-line", "layout-left-fill", "layout-left-line", "layout-line", "layout-masonry-fill", "layout-masonry-line", "layout-right-2-fill", "layout-right-2-line", "layout-right-fill", "layout-right-line", "layout-row-fill", "layout-row-line", "layout-top-2-fill", "layout-top-2-line", "layout-top-fill", "layout-top-line", "leaf-fill", "leaf-line", "lifebuoy-fill", "lifebuoy-line", "lightbulb-fill", "lightbulb-flash-fill", "lightbulb-flash-line", "lightbulb-line", "line-chart-fill", "line-chart-line", "line-fill", "line-height", "line-line", "link", "link-m", "link-unlink", "link-unlink-m", "linkedin-box-fill", "linkedin-box-line", "linkedin-fill", "linkedin-line", "links-fill", "links-line", "list-check", "list-check-2", "list-check-3", "list-indefinite", "list-ordered", "list-ordered-2", "list-radio", "list-settings-fill", "list-settings-line", "list-unordered", "list-view", "live-fill", "live-line", "loader-2-fill", "loader-2-line", "loader-3-fill", "loader-3-line", "loader-4-fill", "loader-4-line", "loader-5-fill", "loader-5-line", "loader-fill", "loader-line", "lock-2-fill", "lock-2-line", "lock-fill", "lock-line", "lock-password-fill", "lock-password-line", "lock-star-fill", "lock-star-line", "lock-unlock-fill", "lock-unlock-line", "login-box-fill", "login-box-line", "login-circle-fill", "login-circle-line", "logout-box-fill", "logout-box-line", "logout-box-r-fill", "logout-box-r-line", "logout-circle-fill", "logout-circle-line", "logout-circle-r-fill", "logout-circle-r-line", "loop-left-fill", "loop-left-line", "loop-right-fill", "loop-right-line", "luggage-cart-fill", "luggage-cart-line", "luggage-deposit-fill", "luggage-deposit-line", "lungs-fill", "lungs-line", "mac-fill", "mac-line", "macbook-fill", "macbook-line", "magic-fill", "magic-line", "mail-add-fill", "mail-add-line", "mail-check-fill", "mail-check-line", "mail-close-fill", "mail-close-line", "mail-download-fill", "mail-download-line", "mail-fill", "mail-forbid-fill", "mail-forbid-line", "mail-line", "mail-lock-fill", "mail-lock-line", "mail-open-fill", "mail-open-line", "mail-send-fill", "mail-send-line", "mail-settings-fill", "mail-settings-line", "mail-star-fill", "mail-star-line", "mail-unread-fill", "mail-unread-line", "mail-volume-fill", "mail-volume-line", "map-2-fill", "map-2-line", "map-fill", "map-line", "map-pin-2-fill", "map-pin-2-line", "map-pin-3-fill", "map-pin-3-line", "map-pin-4-fill", "map-pin-4-line", "map-pin-5-fill", "map-pin-5-line", "map-pin-add-fill", "map-pin-add-line", "map-pin-fill", "map-pin-line", "map-pin-range-fill", "map-pin-range-line", "map-pin-time-fill", "map-pin-time-line", "map-pin-user-fill", "map-pin-user-line", "mark-pen-fill", "mark-pen-line", "markdown-fill", "markdown-line", "markup-fill", "markup-line", "mastercard-fill", "mastercard-line", "mastodon-fill", "mastodon-line", "medal-2-fill", "medal-2-line", "medal-fill", "medal-line", "medicine-bottle-fill", "medicine-bottle-line", "medium-fill", "medium-line", "megaphone-fill", "megaphone-line", "memories-fill", "memories-line", "men-fill", "men-line", "mental-health-fill", "mental-health-line", "menu-2-fill", "menu-2-line", "menu-3-fill", "menu-3-line", "menu-4-fill", "menu-4-line", "menu-5-fill", "menu-5-line", "menu-add-fill", "menu-add-line", "menu-fill", "menu-fold-fill", "menu-fold-line", "menu-line", "menu-search-fill", "menu-search-line", "menu-unfold-fill", "menu-unfold-line", "merge-cells-horizontal", "merge-cells-vertical", "message-2-fill", "message-2-line", "message-3-fill", "message-3-line", "message-fill", "message-line", "messenger-fill", "messenger-line", "meta-fill", "meta-line", "meteor-fill", "meteor-line", "mic-2-fill", "mic-2-line", "mic-fill", "mic-line", "mic-off-fill", "mic-off-line", "mickey-fill", "mickey-line", "microscope-fill", "microscope-line", "microsoft-fill", "microsoft-line", "microsoft-loop-fill", "microsoft-loop-line", "mind-map", "mini-program-fill", "mini-program-line", "mist-fill", "mist-line", "money-cny-box-fill", "money-cny-box-line", "money-cny-circle-fill", "money-cny-circle-line", "money-dollar-box-fill", "money-dollar-box-line", "money-dollar-circle-fill", "money-dollar-circle-line", "money-euro-box-fill", "money-euro-box-line", "money-euro-circle-fill", "money-euro-circle-line", "money-pound-box-fill", "money-pound-box-line", "money-pound-circle-fill", "money-pound-circle-line", "money-rupee-circle-fill", "money-rupee-circle-line", "moon-clear-fill", "moon-clear-line", "moon-cloudy-fill", "moon-cloudy-line", "moon-fill", "moon-foggy-fill", "moon-foggy-line", "moon-line", "more-2-fill", "more-2-line", "more-fill", "more-line", "motorbike-fill", "motorbike-line", "mouse-fill", "mouse-line", "movie-2-fill", "movie-2-line", "movie-fill", "movie-line", "music-2-fill", "music-2-line", "music-fill", "music-line", "mv-fill", "mv-line", "navigation-fill", "navigation-line", "netease-cloud-music-fill", "netease-cloud-music-line", "netflix-fill", "netflix-line", "news-fill", "news-line", "newspaper-fill", "newspaper-line", "nft-fill", "nft-line", "node-tree", "notification-2-fill", "notification-2-line", "notification-3-fill", "notification-3-line", "notification-4-fill", "notification-4-line", "notification-badge-fill", "notification-badge-line", "notification-fill", "notification-line", "notification-off-fill", "notification-off-line", "notion-fill", "notion-line", "npmjs-fill", "npmjs-line", "number-0", "number-1", "number-2", "number-3", "number-4", "number-5", "number-6", "number-7", "number-8", "number-9", "numbers-fill", "numbers-line", "nurse-fill", "nurse-line", "octagon-fill", "octagon-line", "oil-fill", "oil-line", "omega", "open-arm-fill", "open-arm-line", "open-source-fill", "open-source-line", "openai-fill", "openai-line", "openbase-fill", "openbase-line", "opera-fill", "opera-line", "order-play-fill", "order-play-line", "organization-chart", "outlet-2-fill", "outlet-2-line", "outlet-fill", "outlet-line", "overline", "p2p-fill", "p2p-line", "page-separator", "pages-fill", "pages-line", "paint-brush-fill", "paint-brush-line", "paint-fill", "paint-line", "palette-fill", "palette-line", "pantone-fill", "pantone-line", "paragraph", "parent-fill", "parent-line", "parentheses-fill", "parentheses-line", "parking-box-fill", "parking-box-line", "parking-fill", "parking-line", "pass-expired-fill", "pass-expired-line", "pass-pending-fill", "pass-pending-line", "pass-valid-fill", "pass-valid-line", "passport-fill", "passport-line", "patreon-fill", "patreon-line", "pause-circle-fill", "pause-circle-line", "pause-fill", "pause-line", "pause-mini-fill", "pause-mini-line", "paypal-fill", "paypal-line", "pen-nib-fill", "pen-nib-line", "pencil-fill", "pencil-line", "pencil-ruler-2-fill", "pencil-ruler-2-line", "pencil-ruler-fill", "pencil-ruler-line", "pentagon-fill", "pentagon-line", "percent-fill", "percent-line", "phone-camera-fill", "phone-camera-line", "phone-fill", "phone-find-fill", "phone-find-line", "phone-line", "phone-lock-fill", "phone-lock-line", "picture-in-picture-2-fill", "picture-in-picture-2-line", "picture-in-picture-exit-fill", "picture-in-picture-exit-line", "picture-in-picture-fill", "picture-in-picture-line", "pie-chart-2-fill", "pie-chart-2-line", "pie-chart-box-fill", "pie-chart-box-line", "pie-chart-fill", "pie-chart-line", "pin-distance-fill", "pin-distance-line", "ping-pong-fill", "ping-pong-line", "pinterest-fill", "pinterest-line", "pinyin-input", "pixelfed-fill", "pixelfed-line", "plane-fill", "plane-line", "planet-fill", "planet-line", "plant-fill", "plant-line", "play-circle-fill", "play-circle-line", "play-fill", "play-line", "play-list-2-fill", "play-list-2-line", "play-list-add-fill", "play-list-add-line", "play-list-fill", "play-list-line", "play-mini-fill", "play-mini-line", "playstation-fill", "playstation-line", "plug-2-fill", "plug-2-line", "plug-fill", "plug-line", "polaroid-2-fill", "polaroid-2-line", "polaroid-fill", "polaroid-line", "police-car-fill", "police-car-line", "presentation-fill", "presentation-line", "price-tag-2-fill", "price-tag-2-line", "price-tag-3-fill", "price-tag-3-line", "price-tag-fill", "price-tag-line", "printer-cloud-fill", "printer-cloud-line", "printer-fill", "printer-line", "product-hunt-fill", "product-hunt-line", "profile-fill", "profile-line", "progress-1-fill", "progress-1-line", "progress-2-fill", "progress-2-line", "progress-3-fill", "progress-3-line", "progress-4-fill", "progress-4-line", "progress-5-fill", "progress-5-line", "progress-6-fill", "progress-6-line", "progress-7-fill", "progress-7-line", "progress-8-fill", "progress-8-line", "prohibited-fill", "prohibited-line", "projector-2-fill", "projector-2-line", "projector-fill", "projector-line", "psychotherapy-fill", "psychotherapy-line", "pulse-fill", "pulse-line", "pushpin-2-fill", "pushpin-2-line", "pushpin-fill", "pushpin-line", "puzzle-2-fill", "puzzle-2-line", "puzzle-fill", "puzzle-line", "qq-fill", "qq-line", "qr-code-fill", "qr-code-line", "qr-scan-2-fill", "qr-scan-2-line", "qr-scan-fill", "qr-scan-line", "question-answer-fill", "question-answer-line", "question-fill", "question-line", "question-mark", "questionnaire-fill", "questionnaire-line", "quill-pen-fill", "quill-pen-line", "quote-text", "radar-fill", "radar-line", "radio-2-fill", "radio-2-line", "radio-button-fill", "radio-button-line", "radio-fill", "radio-line", "rainbow-fill", "rainbow-line", "rainy-fill", "rainy-line", "ram-2-fill", "ram-2-line", "ram-fill", "ram-line", "reactjs-fill", "reactjs-line", "receipt-fill", "receipt-line", "record-circle-fill", "record-circle-line", "record-mail-fill", "record-mail-line", "rectangle-fill", "rectangle-line", "recycle-fill", "recycle-line", "red-packet-fill", "red-packet-line", "reddit-fill", "reddit-line", "refresh-fill", "refresh-line", "refund-2-fill", "refund-2-line", "refund-fill", "refund-line", "registered-fill", "registered-line", "remix-run-fill", "remix-run-line", "remixicon-fill", "remixicon-line", "remote-control-2-fill", "remote-control-2-line", "remote-control-fill", "remote-control-line", "repeat-2-fill", "repeat-2-line", "repeat-fill", "repeat-line", "repeat-one-fill", "repeat-one-line", "replay-10-fill", "replay-10-line", "replay-15-fill", "replay-15-line", "replay-30-fill", "replay-30-line", "replay-5-fill", "replay-5-line", "reply-all-fill", "reply-all-line", "reply-fill", "reply-line", "reserved-fill", "reserved-line", "rest-time-fill", "rest-time-line", "restart-fill", "restart-line", "restaurant-2-fill", "restaurant-2-line", "restaurant-fill", "restaurant-line", "rewind-fill", "rewind-line", "rewind-mini-fill", "rewind-mini-line", "rfid-fill", "rfid-line", "rhythm-fill", "rhythm-line", "riding-fill", "riding-line", "road-map-fill", "road-map-line", "roadster-fill", "roadster-line", "robot-2-fill", "robot-2-line", "robot-3-fill", "robot-3-line", "robot-fill", "robot-line", "rocket-2-fill", "rocket-2-line", "rocket-fill", "rocket-line", "rotate-lock-fill", "rotate-lock-line", "rounded-corner", "route-fill", "route-line", "router-fill", "router-line", "rss-fill", "rss-line", "ruler-2-fill", "ruler-2-line", "ruler-fill", "ruler-line", "run-fill", "run-line", "safari-fill", "safari-line", "safe-2-fill", "safe-2-line", "safe-fill", "safe-line", "sailboat-fill", "sailboat-line", "save-2-fill", "save-2-line", "save-3-fill", "save-3-line", "save-fill", "save-line", "scales-2-fill", "scales-2-line", "scales-3-fill", "scales-3-line", "scales-fill", "scales-line", "scan-2-fill", "scan-2-line", "scan-fill", "scan-line", "school-fill", "school-line", "scissors-2-fill", "scissors-2-line", "scissors-cut-fill", "scissors-cut-line", "scissors-fill", "scissors-line", "screenshot-2-fill", "screenshot-2-line", "screenshot-fill", "screenshot-line", "sd-card-fill", "sd-card-line", "sd-card-mini-fill", "sd-card-mini-line", "search-2-fill", "search-2-line", "search-eye-fill", "search-eye-line", "search-fill", "search-line", "secure-payment-fill", "secure-payment-line", "seedling-fill", "seedling-line", "send-backward", "send-plane-2-fill", "send-plane-2-line", "send-plane-fill", "send-plane-line", "send-to-back", "sensor-fill", "sensor-line", "seo-fill", "seo-line", "separator", "server-fill", "server-line", "service-fill", "service-line", "settings-2-fill", "settings-2-line", "settings-3-fill", "settings-3-line", "settings-4-fill", "settings-4-line", "settings-5-fill", "settings-5-line", "settings-6-fill", "settings-6-line", "settings-fill", "settings-line", "shadow-fill", "shadow-line", "shake-hands-fill", "shake-hands-line", "shape-2-fill", "shape-2-line", "shape-fill", "shape-line", "shapes-fill", "shapes-line", "share-2-fill", "share-2-line", "share-box-fill", "share-box-line", "share-circle-fill", "share-circle-line", "share-fill", "share-forward-2-fill", "share-forward-2-line", "share-forward-box-fill", "share-forward-box-line", "share-forward-fill", "share-forward-line", "share-line", "shield-check-fill", "shield-check-line", "shield-cross-fill", "shield-cross-line", "shield-fill", "shield-flash-fill", "shield-flash-line", "shield-keyhole-fill", "shield-keyhole-line", "shield-line", "shield-star-fill", "shield-star-line", "shield-user-fill", "shield-user-line", "shining-2-fill", "shining-2-line", "shining-fill", "shining-line", "ship-2-fill", "ship-2-line", "ship-fill", "ship-line", "shirt-fill", "shirt-line", "shopping-bag-2-fill", "shopping-bag-2-line", "shopping-bag-3-fill", "shopping-bag-3-line", "shopping-bag-fill", "shopping-bag-line", "shopping-basket-2-fill", "shopping-basket-2-line", "shopping-basket-fill", "shopping-basket-line", "shopping-cart-2-fill", "shopping-cart-2-line", "shopping-cart-fill", "shopping-cart-line", "showers-fill", "showers-line", "shuffle-fill", "shuffle-line", "shut-down-fill", "shut-down-line", "side-bar-fill", "side-bar-line", "sidebar-fold-fill", "sidebar-fold-line", "sidebar-unfold-fill", "sidebar-unfold-line", "signal-tower-fill", "signal-tower-line", "signal-wifi-1-fill", "signal-wifi-1-line", "signal-wifi-2-fill", "signal-wifi-2-line", "signal-wifi-3-fill", "signal-wifi-3-line", "signal-wifi-error-fill", "signal-wifi-error-line", "signal-wifi-fill", "signal-wifi-line", "signal-wifi-off-fill", "signal-wifi-off-line", "signpost-fill", "signpost-line", "sim-card-2-fill", "sim-card-2-line", "sim-card-fill", "sim-card-line", "single-quotes-l", "single-quotes-r", "sip-fill", "sip-line", "sketching", "skip-back-fill", "skip-back-line", "skip-back-mini-fill", "skip-back-mini-line", "skip-down-fill", "skip-down-line", "skip-forward-fill", "skip-forward-line", "skip-forward-mini-fill", "skip-forward-mini-line", "skip-left-fill", "skip-left-line", "skip-right-fill", "skip-right-line", "skip-up-fill", "skip-up-line", "skull-2-fill", "skull-2-line", "skull-fill", "skull-line", "skype-fill", "skype-line", "slack-fill", "slack-line", "slash-commands", "slash-commands-2", "slice-fill", "slice-line", "slideshow-2-fill", "slideshow-2-line", "slideshow-3-fill", "slideshow-3-line", "slideshow-4-fill", "slideshow-4-line", "slideshow-fill", "slideshow-line", "slideshow-view", "slow-down-fill", "slow-down-line", "smartphone-fill", "smartphone-line", "snapchat-fill", "snapchat-line", "snowy-fill", "snowy-line", "sofa-fill", "sofa-line", "sort-alphabet-asc", "sort-alphabet-desc", "sort-asc", "sort-desc", "sort-number-asc", "sort-number-desc", "sound-module-fill", "sound-module-line", "soundcloud-fill", "soundcloud-line", "space", "space-ship-fill", "space-ship-line", "spam-2-fill", "spam-2-line", "spam-3-fill", "spam-3-line", "spam-fill", "spam-line", "sparkling-2-fill", "sparkling-2-line", "sparkling-fill", "sparkling-line", "speak-fill", "speak-line", "speaker-2-fill", "speaker-2-line", "speaker-3-fill", "speaker-3-line", "speaker-fill", "speaker-line", "spectrum-fill", "spectrum-line", "speed-fill", "speed-line", "speed-mini-fill", "speed-mini-line", "speed-up-fill", "speed-up-line", "split-cells-horizontal", "split-cells-vertical", "spotify-fill", "spotify-line", "spy-fill", "spy-line", "square-fill", "square-line", "stack-fill", "stack-line", "stack-overflow-fill", "stack-overflow-line", "stacked-view", "stackshare-fill", "stackshare-line", "star-fill", "star-half-fill", "star-half-line", "star-half-s-fill", "star-half-s-line", "star-line", "star-s-fill", "star-s-line", "star-smile-fill", "star-smile-line", "steam-fill", "steam-line", "steering-2-fill", "steering-2-line", "steering-fill", "steering-line", "stethoscope-fill", "stethoscope-line", "sticky-note-2-fill", "sticky-note-2-line", "sticky-note-add-fill", "sticky-note-add-line", "sticky-note-fill", "sticky-note-line", "stock-fill", "stock-line", "stop-circle-fill", "stop-circle-line", "stop-fill", "stop-line", "stop-mini-fill", "stop-mini-line", "store-2-fill", "store-2-line", "store-3-fill", "store-3-line", "store-fill", "store-line", "strikethrough", "strikethrough-2", "subscript", "subscript-2", "subtract-fill", "subtract-line", "subway-fill", "subway-line", "subway-wifi-fill", "subway-wifi-line", "suitcase-2-fill", "suitcase-2-line", "suitcase-3-fill", "suitcase-3-line", "suitcase-fill", "suitcase-line", "sun-cloudy-fill", "sun-cloudy-line", "sun-fill", "sun-foggy-fill", "sun-foggy-line", "sun-line", "supabase-fill", "supabase-line", "superscript", "superscript-2", "surgical-mask-fill", "surgical-mask-line", "surround-sound-fill", "surround-sound-line", "survey-fill", "survey-line", "svelte-fill", "svelte-line", "swap-2-fill", "swap-2-line", "swap-3-fill", "swap-3-line", "swap-box-fill", "swap-box-line", "swap-fill", "swap-line", "switch-fill", "switch-line", "sword-fill", "sword-line", "syringe-fill", "syringe-line", "t-box-fill", "t-box-line", "t-shirt-2-fill", "t-shirt-2-line", "t-shirt-air-fill", "t-shirt-air-line", "t-shirt-fill", "t-shirt-line", "table-2", "table-3", "table-alt-fill", "table-alt-line", "table-fill", "table-line", "table-view", "tablet-fill", "tablet-line", "takeaway-fill", "takeaway-line", "taobao-fill", "taobao-line", "tape-fill", "tape-line", "task-fill", "task-line", "taxi-fill", "taxi-line", "taxi-wifi-fill", "taxi-wifi-line", "team-fill", "team-line", "telegram-fill", "telegram-line", "temp-cold-fill", "temp-cold-line", "temp-hot-fill", "temp-hot-line", "tent-fill", "tent-line", "terminal-box-fill", "terminal-box-line", "terminal-fill", "terminal-line", "terminal-window-fill", "terminal-window-line", "test-tube-fill", "test-tube-line", "text", "text-block", "text-direction-l", "text-direction-r", "text-snippet", "text-spacing", "text-wrap", "thermometer-fill", "thermometer-line", "threads-fill", "threads-line", "thumb-down-fill", "thumb-down-line", "thumb-up-fill", "thumb-up-line", "thunderstorms-fill", "thunderstorms-line", "ticket-2-fill", "ticket-2-line", "ticket-fill", "ticket-line", "tiktok-fill", "tiktok-line", "time-fill", "time-line", "time-zone-fill", "time-zone-line", "timeline-view", "timer-2-fill", "timer-2-line", "timer-fill", "timer-flash-fill", "timer-flash-line", "timer-line", "todo-fill", "todo-line", "toggle-fill", "toggle-line", "token-swap-fill", "token-swap-line", "tools-fill", "tools-line", "tornado-fill", "tornado-line", "trademark-fill", "trademark-line", "traffic-light-fill", "traffic-light-line", "train-fill", "train-line", "train-wifi-fill", "train-wifi-line", "translate", "translate-2", "travesti-fill", "travesti-line", "treasure-map-fill", "treasure-map-line", "tree-fill", "tree-line", "trello-fill", "trello-line", "triangle-fill", "triangle-line", "trophy-fill", "trophy-line", "truck-fill", "truck-line", "tumblr-fill", "tumblr-line", "tv-2-fill", "tv-2-line", "tv-fill", "tv-line", "twitch-fill", "twitch-line", "twitter-fill", "twitter-line", "twitter-x-fill", "twitter-x-line", "typhoon-fill", "typhoon-line", "u-disk-fill", "u-disk-line", "ubuntu-fill", "ubuntu-line", "umbrella-fill", "umbrella-line", "underline", "uninstall-fill", "uninstall-line", "unpin-fill", "unpin-line", "unsplash-fill", "unsplash-line", "upload-2-fill", "upload-2-line", "upload-cloud-2-fill", "upload-cloud-2-line", "upload-cloud-fill", "upload-cloud-line", "upload-fill", "upload-line", "usb-fill", "usb-line", "user-2-fill", "user-2-line", "user-3-fill", "user-3-line", "user-4-fill", "user-4-line", "user-5-fill", "user-5-line", "user-6-fill", "user-6-line", "user-add-fill", "user-add-line", "user-fill", "user-follow-fill", "user-follow-line", "user-forbid-fill", "user-forbid-line", "user-heart-fill", "user-heart-line", "user-line", "user-location-fill", "user-location-line", "user-received-2-fill", "user-received-2-line", "user-received-fill", "user-received-line", "user-search-fill", "user-search-line", "user-settings-fill", "user-settings-line", "user-shared-2-fill", "user-shared-2-line", "user-shared-fill", "user-shared-line", "user-smile-fill", "user-smile-line", "user-star-fill", "user-star-line", "user-unfollow-fill", "user-unfollow-line", "user-voice-fill", "user-voice-line", "verified-badge-fill", "verified-badge-line", "video-add-fill", "video-add-line", "video-chat-fill", "video-chat-line", "video-download-fill", "video-download-line", "video-fill", "video-line", "video-upload-fill", "video-upload-line", "vidicon-2-fill", "vidicon-2-line", "vidicon-fill", "vidicon-line", "vimeo-fill", "vimeo-line", "vip-crown-2-fill", "vip-crown-2-line", "vip-crown-fill", "vip-crown-line", "vip-diamond-fill", "vip-diamond-line", "vip-fill", "vip-line", "virus-fill", "virus-line", "visa-fill", "visa-line", "vk-fill", "vk-line", "voice-recognition-fill", "voice-recognition-line", "voiceprint-fill", "voiceprint-line", "volume-down-fill", "volume-down-line", "volume-mute-fill", "volume-mute-line", "volume-off-vibrate-fill", "volume-off-vibrate-line", "volume-up-fill", "volume-up-line", "volume-vibrate-fill", "volume-vibrate-line", "vuejs-fill", "vuejs-line", "walk-fill", "walk-line", "wallet-2-fill", "wallet-2-line", "wallet-3-fill", "wallet-3-line", "wallet-fill", "wallet-line", "water-flash-fill", "water-flash-line", "water-percent-fill", "water-percent-line", "webcam-fill", "webcam-line", "wechat-2-fill", "wechat-2-line", "wechat-channels-fill", "wechat-channels-line", "wechat-fill", "wechat-line", "wechat-pay-fill", "wechat-pay-line", "weibo-fill", "weibo-line", "whatsapp-fill", "whatsapp-line", "wheelchair-fill", "wheelchair-line", "wifi-fill", "wifi-line", "wifi-off-fill", "wifi-off-line", "window-2-fill", "window-2-line", "window-fill", "window-line", "windows-fill", "windows-line", "windy-fill", "windy-line", "wireless-charging-fill", "wireless-charging-line", "women-fill", "women-line", "wordpress-fill", "wordpress-line", "wubi-input", "xbox-fill", "xbox-line", "xing-fill", "xing-line", "xrp-fill", "xrp-line", "xtz-fill", "xtz-line", "youtube-fill", "youtube-line", "yuque-fill", "yuque-line", "zcool-fill", "zcool-line", "zhihu-fill", "zhihu-line", "zoom-in-fill", "zoom-in-line", "zoom-out-fill", "zoom-out-line", "zzz-fill", "zzz-line"]}, {"prefix": "logos", "info": {"name": "SVG Logos", "total": 1812, "author": {"name": "<PERSON>", "url": "https://github.com/gilbarbara/logos"}, "license": {"title": "CC0", "spdx": "CC0-1.0", "url": "https://raw.githubusercontent.com/gilbarbara/logos/master/LICENSE.txt"}, "samples": ["angular-icon", "firefox", "google-drive"], "category": "Brands / Social", "palette": true}, "icons": ["100tb", "500px", "6px", "active-campaign", "active-campaign-icon", "admob", "adobe-after-effects", "adobe-animate", "adobe-dreamweaver", "adobe-illustrator", "adobe-incopy", "adobe-indesign", "adobe-lightroom", "adobe-photoshop", "adobe-premiere", "adobe-xd", "ad<PERSON><PERSON><PERSON><PERSON>", "adonisjs-icon", "adroll", "adyen", "aerogear", "aerospike", "aerospike-icon", "aha", "ai", "airbnb", "airbnb-icon", "airbrake", "airflow", "airflow-icon", "airtable", "aix", "<PERSON><PERSON><PERSON>", "akka", "alfresco", "algolia", "alpinej<PERSON>", "alpinejs-icon", "altair", "amazon-chime", "amazon-connect", "amd", "amex", "amex-digital", "amp", "amp-icon", "ampersand", "amplication", "amplication-icon", "amplitude", "amplitude-icon", "analog", "android", "android-icon", "android-vertical", "angellist", "angular", "angular-icon", "ansible", "ant-design", "anthropic", "anthropic-icon", "apache", "apache-camel", "apache-cloudstack", "apache-flink", "apache-flink-icon", "apache-spark", "apache-superset", "apache-superset-icon", "api-ai", "apiary", "apigee", "apitools", "apollos<PERSON><PERSON>", "apostrophe", "appbase", "appbaseio", "appbaseio-icon", "appcelerator", "appcenter", "appcenter-icon", "appcircle", "appcircle-icon", "appcode", "appdynamics", "appdynamics-icon", "appfog", "apphub", "appium", "apple", "apple-app-store", "apple-pay", "applitools", "applitools-icon", "appmaker", "apportable", "appsignal", "appsignal-icon", "apptentive", "appveyor", "appwrite", "appwrite-icon", "arang<PERSON><PERSON>", "arangodb-icon", "arc", "architect", "architect-icon", "<PERSON><PERSON><PERSON>", "a<PERSON><PERSON><PERSON>", "argo", "argo-icon", "arm", "armory", "armory-icon", "asana", "asana-icon", "<PERSON><PERSON><PERSON><PERSON>", "assembla", "assembla-icon", "astro", "astro-icon", "astronomer", "async-api", "async-api-icon", "atlassian", "atom", "atom-icon", "atomic", "atomic-icon", "<PERSON><PERSON><PERSON><PERSON>", "atomicojs-icon", "aurelia", "aurora", "aurous", "auth0", "auth0-icon", "authy", "autocode", "autoit", "autoprefixer", "ava", "awesome", "aws", "aws-amplify", "aws-api-gateway", "aws-app-mesh", "aws-appflow", "aws-appsync", "aws-athena", "aws-aurora", "aws-backup", "aws-batch", "aws-certificate-manager", "aws-cloudformation", "aws-cloudfront", "aws-cloudsearch", "aws-cloudtrail", "aws-cloudwatch", "aws-codebuild", "aws-codecommit", "aws-codedeploy", "aws-codepipeline", "aws-codestar", "aws-cognito", "aws-config", "aws-documentdb", "aws-dynamodb", "aws-ec2", "aws-ecs", "aws-eks", "aws-elastic-beanstalk", "aws-elastic-cache", "aws-elasticache", "aws-elb", "aws-eventbridge", "aws-fargate", "aws-glacier", "aws-glue", "aws-iam", "aws-keyspaces", "aws-kinesis", "aws-kms", "aws-lake-formation", "aws-lambda", "aws-lightsail", "aws-mobilehub", "aws-mq", "aws-msk", "aws-neptune", "aws-open-search", "aws-opsworks", "aws-quicksight", "aws-rds", "aws-redshift", "aws-route53", "aws-s3", "aws-secrets-manager", "aws-ses", "aws-shield", "aws-sns", "aws-sqs", "aws-step-functions", "aws-systems-manager", "aws-timestream", "aws-vpc", "aws-waf", "aws-xray", "axios", "azure", "azure-icon", "babel", "backbone", "backbone-icon", "backerkit", "baker-street", "balena", "bamboo", "base", "basecamp", "basecamp-icon", "basekit", "baseline", "bash", "bash-icon", "batch", "beats", "behance", "bem", "bem-2", "<PERSON><PERSON><PERSON>", "bing", "biomejs", "bitballoon", "bitbar", "bitbucket", "bitcoin", "bitnami", "bitrise", "bitrise-icon", "blender", "blitzjs", "blitzjs-icon", "blocs", "blogger", "blossom", "bluemix", "blueprint", "bluetooth", "booqable", "booqable-icon", "bootstrap", "bosun", "botanalytics", "bourbon", "bower", "bowtie", "box", "brackets", "<PERSON><PERSON><PERSON>", "branch", "branch-icon", "brandfolder", "brandfolder-icon", "brave", "braze", "braze-icon", "broadcom", "broadcom-icon", "broccoli", "brotli", "browserify", "browserify-icon", "browserling", "browserslist", "browserstack", "browsersync", "brunch", "bubble", "bubble-icon", "buck", "buddy", "buffer", "bugherd", "bugherd-icon", "<PERSON><PERSON>", "bugsnag", "bugsnag-icon", "builder-io", "builder-io-icon", "buildkite", "buildkite-icon", "bulma", "bun", "bunny-net", "bunny-net-icon", "c", "c-plusplus", "c-sharp", "cachet", "caffe2", "cakephp", "cakephp-icon", "calibre", "calibre-icon", "campaignmonitor", "campaignmonitor-icon", "campfire", "canjs", "capacitorjs", "capacitorjs-icon", "capistrano", "carbide", "cardano", "cardano-icon", "cassandra", "celluloid", "centos", "centos-icon", "certbot", "ceylon", "chai", "chalk", "changetip", "chargebee", "chargebee-icon", "chartblocks", "chef", "chevereto", "chroma", "chromatic", "chromatic-icon", "chrome", "chrome-web-store", "cinder", "<PERSON><PERSON>", "cirrus", "cirrus-ci", "clickdeploy", "clio-lang", "clion", "cljs", "clojure", "close", "cloud9", "cloudacademy", "cloudacademy-icon", "cloudant", "cloudcraft", "cloudera", "cloudflare", "cloudflare-icon", "cloudflare-workers", "cloudflare-workers-icon", "cloudinary", "cloudinary-icon", "cloudlinux", "clusterhq", "cobalt", "cockpit", "cocoapods", "coda", "coda-icon", "codacy", "codebase", "codebeat", "codecademy", "codeception", "codeclimate", "codeclimate-icon", "codecov", "codecov-icon", "codefactor", "codefactor-icon", "codefund", "codefund-icon", "codeigniter", "codeigniter-icon", "codepen", "codepen-icon", "codepicnic", "codepush", "codersrank", "codersrank-icon", "coderwall", "codesandbox", "codesandbox-icon", "codeschool", "codesee", "codesee-icon", "codeship", "codio", "codium", "codium-icon", "codrops", "coffeescript", "commitizen", "compass", "component", "componentkit", "compose", "compose-multiplatform", "composer", "conan-io", "concourse", "concrete5", "concretecms", "concretecms-icon", "conda", "confluence", "consul", "containership", "contentful", "convox", "convox-icon", "copyleft", "copyleft-pirate", "corda", "<PERSON><PERSON>", "coreos", "coreos-icon", "couchbase", "couchdb", "couchdb-icon", "coursera", "coveralls", "coverity", "cpanel", "craft", "craftcms", "crashlytics", "crateio", "create-react-app", "createjs", "crittercism", "cross-browser-testing", "crossbrowsertesting", "crossplane", "crossplane-icon", "crowdprocess", "crucible", "crystal", "css-3", "css-3-official", "cssnext", "cube", "cube-icon", "cucumber", "curl", "customerio", "customerio-icon", "cyclejs", "cypress", "cypress-icon", "d3", "dailydev", "dailydev-icon", "daisyui", "danfo", "dapulse", "dart", "dashlane", "dashlane-icon", "dat", "data-station", "database-labs", "datadog", "datadog-icon", "datagrip", "datasette", "datasette-icon", "dataspell", "datocms", "datocms-icon", "dbt", "dbt-icon", "dcos", "dcos-icon", "debian", "delicious", "delicious-burger", "delighted", "delighted-icon", "deno", "dependabot", "dependencyci", "deploy", "deployhq", "deployhq-icon", "de<PERSON><PERSON>", "derby", "descript", "descript-icon", "designernews", "desk", "dev", "dev-icon", "deviantart", "deviantart-icon", "dgraph", "dgraph-icon", "dialogflow", "digital-ocean", "digital-ocean-icon", "dimer", "dinersclub", "discord", "discord-icon", "discourse", "discourse-icon", "discover", "disqus", "diste<PERSON>", "divshot", "django", "django-icon", "dockbit", "docker", "docker-icon", "doctrine", "<PERSON>cusaurus", "dojo", "dojo-icon", "dojo-toolkit", "dolt", "dotcloud", "dotnet", "doubleclick", "dovetail", "dovetail-icon", "dreamfactory", "dreamhost", "dribbble", "dribbble-icon", "drift", "drip", "drizzle", "drizzle-icon", "drone", "drone-icon", "drools", "drools-icon", "dropbox", "dropmark", "dropzone", "drupal", "drupal-icon", "duckduck<PERSON>", "dynatrace", "dynatrace-icon", "dyndns", "eager", "ebanx", "eclipse", "eclipse-icon", "ecma", "<PERSON>b", "edgio", "edgio-icon", "editorconfig", "effect", "effect-icon", "effector", "egghead", "elasticbox", "elasticpath", "elasticpath-icon", "elasticsearch", "electron", "element", "elemental-ui", "elementary", "eleventy", "ello", "elm", "elm-classic", "elo", "emacs", "emacs-classic", "embedly", "ember", "ember-tomster", "emmet", "enact", "engine-yard", "engine-yard-icon", "<PERSON><PERSON><PERSON>", "envoy", "envoy-icon", "envoyer", "envoyproxy", "enyo", "epsagon", "epsagon-icon", "eraser", "eraser-icon", "erlang", "es6", "esbuild", "esdoc", "eslint", "eslint-old", "eta", "eta-icon", "etcd", "ethereum", "ethereum-color", "ethers", "ethnio", "eventbrite", "eventbrite-icon", "eventsentry", "evergreen", "evergreen-icon", "expo", "expo-icon", "exponent", "express", "fabric", "fabric-io", "facebook", "falcor", "famous", "<PERSON><PERSON><PERSON>", "fastapi-icon", "fastify", "fastify-icon", "fastlane", "fastly", "fauna", "fauna-icon", "<PERSON><PERSON><PERSON>", "fedora", "fetch", "ffmpeg", "ffmpeg-icon", "figma", "firebase", "firefox", "flannel", "flarum", "flask", "flat-ui", "flattr", "flattr-icon", "fleep", "flexible-gs", "flickr", "flickr-icon", "flight", "flocker", "floodio", "flow", "flowxo", "<PERSON>oy<PERSON><PERSON>", "flutter", "flux", "fluxxor", "fly", "fly-icon", "flyjs", "fogbugz", "fogbugz-icon", "fomo", "fomo-icon", "font-awesome", "forest", "<PERSON>admin", "forestadmin-icon", "forever", "formkeep", "fortran", "foundation", "foundationdb", "foundationdb-icon", "framed", "framer", "framework7", "framework7-icon", "freebsd", "freedcamp", "freedcamp-icon", "freedomdefined", "fresh", "frontapp", "fsharp", "fuchsia", "galliumos", "game-analytics", "game-analytics-icon", "ganache", "ganache-icon", "gatsby", "gaugeio", "geekbot", "geetest", "geetest-icon", "get-satisfaction", "getyourguide", "ghost", "giantswarm", "gin", "git", "git-icon", "gitboard", "github", "github-actions", "github-copilot", "github-icon", "github-octocat", "gitkraken", "gitlab", "gitter", "gitup", "glamorous", "glamorous-icon", "gleam", "glimmer<PERSON>s", "glint", "glitch", "glitch-icon", "gnome", "gnome-icon", "gnu", "gnu-net", "gnupg", "gnupg-icon", "go", "gocd", "godot", "godot-icon", "gohorse", "goland", "gomix", "google", "google-2014", "google-360suite", "google-admob", "google-ads", "google-adsense", "google-adwords", "google-analytics", "google-bard", "google-bard-icon", "google-calendar", "google-cloud", "google-cloud-functions", "google-cloud-platform", "google-cloud-run", "google-currents", "google-data-studio", "google-developers", "google-developers-icon", "google-domains", "google-domains-icon", "google-drive", "google-fit", "google-gemini", "google-gmail", "google-gsuite", "google-home", "google-icon", "google-inbox", "google-keep", "google-maps", "google-marketing-platform", "google-meet", "google-one", "google-optimize", "google-palm", "google-pay", "google-pay-icon", "google-photos", "google-play", "google-play-console", "google-play-console-icon", "google-play-icon", "google-plus", "google-search-console", "google-tag-manager", "google-wallet", "google-workspace", "gopher", "gordon", "gradio", "gradio-icon", "gradle", "grafana", "grails", "grammarly", "grammarly-icon", "grape", "graphcool", "graphene", "graphql", "gratipay", "grav", "gravatar", "gravatar-icon", "graylog", "graylog-icon", "greensock", "greensock-icon", "gridsome", "gridsome-icon", "grommet", "groovehq", "grove", "growth-book", "growth-book-icon", "grpc", "grunt", "gulp", "gunicorn", "gunjs", "gusto", "gwt", "hack", "hacker-one", "hadoop", "haiku", "haiku-icon", "haml", "hanami", "handlebars", "hapi", "hardhat", "hardhat-icon", "harness", "harness-icon", "harrow", "hashicorp", "hashicorp-icon", "<PERSON><PERSON><PERSON>", "hashnode-icon", "haskell", "haskell-icon", "<PERSON><PERSON>", "hasura-icon", "haxe", "haxl", "hbase", "h<PERSON><PERSON>a", "hcaptcha-icon", "headlessui", "headlessui-icon", "heap", "heap-icon", "helm", "helpscout", "helpscout-icon", "hermes", "<PERSON><PERSON>", "heroku-icon", "<PERSON><PERSON>-redis", "heron", "hexo", "hhvm", "hibernate", "highcharts", "hip<PERSON>t", "hiper<PERSON>", "hoa", "homebrew", "hono", "hoodie", "hookstate", "hootsuite", "hootsuite-icon", "horizon", "hosted-graphite", "hostgator", "hostgator-icon", "hotjar", "hotjar-icon", "houndci", "html-5", "html5-boilerplate", "httpie", "httpie-icon", "hubspot", "hugging-face", "hugging-face-icon", "huggy", "hugo", "humongous", "hyper", "hyperapp", "ibm", "ieee", "ietf", "ifttt", "imagemin", "imba", "imba-icon", "immer", "immer-icon", "immutable", "impala", "importio", "incident", "incident-icon", "infer", "inferno", "influxdb", "influxdb-icon", "ink", "insomnia", "instagram", "instagram-icon", "intel", "intellij-idea", "intercom", "intercom-icon", "internetexplorer", "invision", "invision-icon", "io", "ionic", "ionic-icon", "ios", "iron", "iron-icon", "itsalive", "itsalive-icon", "jade", "jamstack", "jamstack-icon", "jasmine", "java", "javascript", "jcb", "j<PERSON><PERSON><PERSON>", "jelastic", "jelastic-icon", "jenkins", "jest", "jetbrains", "jetbrains-icon", "jetbrains-space", "jetbrains-space-icon", "jfrog", "j<PERSON><PERSON>", "j<PERSON>ter-icon", "jira", "j<PERSON><PERSON>", "jotai", "j<PERSON>y", "jquery-mobile", "j<PERSON><PERSON>", "jsbin", "jscs", "js<PERSON><PERSON><PERSON>", "jsdom", "jsfiddle", "json", "json-ld", "jspm", "jss", "juju", "julia", "jup<PERSON><PERSON>", "jwt", "jwt-icon", "kafka", "kafka-icon", "kaios", "kallithea", "karma", "katalon", "katalon-icon", "kde", "keen", "kemal", "keycdn", "keycdn-icon", "keydb", "keydb-icon", "keymetrics", "keystonejs", "khan-academy", "khan-academy-icon", "kibana", "kickstarter", "kickstarter-icon", "kinto", "kinto-icon", "kin<PERSON>", "kirby", "kirby-icon", "kiss<PERSON>s", "kissmetrics-monochromatic", "kitematic", "klou<PERSON>s", "knex", "knockout", "koa", "kong", "kong-icon", "kontena", "kops", "kore", "koreio", "kotlin", "kotlin-icon", "kraken", "krakenjs", "ktor", "ktor-icon", "kubernetes", "kustomer", "languagetool", "laravel", "lastfm", "lateral", "lateral-icon", "launchdarkly", "launchdarkly-icon", "launchkit", "launchrock", "leaflet", "leankit", "leankit-icon", "lerna", "less", "lets-cloud", "letsencrypt", "leveldb", "lexical", "lexical-icon", "librato", "liftweb", "lighthouse", "lightstep", "lightstep-icon", "lighttpd", "linear", "linear-icon", "linkedin", "linkedin-icon", "linkerd", "linode", "linux-mint", "linux-tux", "lit", "lit-icon", "litmus", "loader", "locent", "lodash", "logentries", "loggly", "logmatic", "logstash", "lookback", "looker", "looker-icon", "loom", "loom-icon", "loopback", "loopback-icon", "losant", "lotus", "lua", "lucene", "lucene-net", "lumen", "<PERSON>yn<PERSON>", "macos", "macosx", "madge", "maestro", "mageia", "magento", "magneto", "mailchimp", "mailchimp-freddie", "maildeveloper", "mailgun", "mailgun-icon", "mailjet", "mailjet-icon", "<PERSON><PERSON><PERSON><PERSON>", "mandrill", "mandrill-shield", "<PERSON><PERSON>s", "manjaro", "mantine", "mantine-icon", "mantl", "manuscript", "mapbox", "mapbox-icon", "maps-me", "mapzen", "mapzen-icon", "ma<PERSON>b", "mariadb-icon", "marionette", "markdown", "marko", "marvel", "mastercard", "mastodon", "mastodon-icon", "material-ui", "materializecss", "matomo", "matomo-icon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matplotlib-icon", "matter", "matter-icon", "mattermost", "mattermost-icon", "mautic", "mautic-icon", "maven", "maxcdn", "mdn", "mdx", "meanio", "medium", "medium-icon", "medusa", "medusa-icon", "meilisearch", "memcached", "memgraph", "memsql", "memsql-icon", "mention", "mercurial", "mern", "mesos", "mesosphere", "messenger", "meta", "meta-icon", "metabase", "metamask", "metamask-icon", "meteor", "meteor-icon", "micro", "micro-icon", "micro-python", "microcosm", "micron", "micron-icon", "microsoft", "microsoft-azure", "microsoft-edge", "microsoft-icon", "microsoft-onedrive", "microsoft-power-bi", "microsoft-teams", "microsoft-windows", "microsoft-windows-icon", "mida", "mida-icon", "middleman", "midjourney", "milligram", "million", "million-icon", "mil<PERSON><PERSON>", "milvus-icon", "mindsdb", "mindsdb-icon", "mint-lang", "mio", "mist", "mistral-ai", "mistral-ai-icon", "mithril", "mixmax", "mixpanel", "mlab", "mobx", "mocha", "mockflow", "mockflow-icon", "modernizr", "modulus", "modx", "modx-icon", "moltin", "moltin-icon", "momentjs", "monday", "monday-icon", "monero", "mongodb", "mongodb-icon", "mongolab", "mono", "moon", "mootools", "morpheus", "morpheus-icon", "mozilla", "mparticle", "mparticle-icon", "mps", "mps-icon", "msw", "msw-icon", "multipass", "mysql", "mysql-icon", "myth", "<PERSON><PERSON>", "namecheap", "nanonets", "nasm", "nativescript", "nats", "nats-icon", "neat", "neo4j", "neon", "neon-icon", "neonmetrics", "neovim", "<PERSON><PERSON><PERSON>", "net", "netbeans", "netflix", "netflix-icon", "netlify", "netlify-icon", "netuitive", "neverinstall", "neverinstall-icon", "new-relic", "new-relic-icon", "nextjs", "nextjs-icon", "nginx", "ngrok", "nhost", "nhost-icon", "nightwatch", "nim-lang", "nocodb", "nodal", "node-sass", "nodebots", "<PERSON><PERSON><PERSON>", "nodejs", "nodejs-icon", "nodejs-icon-alt", "nodemon", "nodeos", "nodewebkit", "nomad", "nomad-icon", "notion", "notion-icon", "now", "<PERSON><PERSON>i", "npm", "npm-2", "npm-icon", "nuclide", "numpy", "nuodb", "nuxt", "nuxt-icon", "nvidia", "nvm", "nx", "o<PERSON>h", "observablehq", "obsidian", "obsidian-icon", "ocaml", "octodns", "octopus-deploy", "okta", "okta-icon", "olapic", "olark", "onesignal", "opbeat", "open-graph", "open-zeppelin", "open-zeppelin-icon", "openai", "openai-icon", "openapi", "openapi-icon", "opencart", "opencollective", "opencv", "openframeworks", "opengl", "openjs-foundation", "openjs-foundation-icon", "openlayers", "opensearch", "opensearch-icon", "openshift", "opensource", "openstack", "openstack-icon", "opentelemetry", "opentelemetry-icon", "opera", "opsee", "opsgenie", "opsmatic", "optimizely", "optimizely-icon", "oracle", "oreilly", "origami", "origin", "oshw", "osquery", "otto", "overloop", "overloop-icon", "p5js", "packer", "pagekit", "pagekite", "<PERSON><PERSON><PERSON><PERSON>", "pagerduty-icon", "panda", "pandacss", "pandacss-icon", "pandas", "pandas-icon", "parcel", "parcel-icon", "parse", "<PERSON><PERSON><PERSON><PERSON>", "partytown", "partytown-icon", "passbolt", "passbolt-icon", "passport", "patreon", "payload", "paypal", "peer5", "pepperoni", "percona", "percy", "percy-icon", "perf-rocks", "periscope", "perl", "phalcon", "phoenix", "phonegap", "phonegap-bot", "php", "php-alt", "phpstorm", "picasa", "pinecone", "pinecone-icon", "pingdom", "pingy", "pinia", "pinterest", "pipedream", "pipedrive", "pipefy", "pivotal-tracker", "pixate", "pixelapse", "pixijs", "pkg", "planetscale", "planless", "planless-icon", "plasmic", "plastic-scm", "platformio", "play", "playwright", "pluralsight", "pluralsight-icon", "pm2", "pm2-icon", "pnpm", "pocket-base", "podio", "poeditor", "polymer", "positionly", "postcss", "postgraphile", "postgresql", "posthog", "posthog-icon", "postman", "postman-icon", "pouchdb", "preact", "precursor", "prerender", "prerender-icon", "prestashop", "presto", "presto-icon", "prettier", "prisma", "prismic", "prismic-icon", "processing", "processwire", "processwire-icon", "productboard", "productboard-icon", "producteev", "producthunt", "progress", "prometheus", "promises", "proofy", "prospect", "protoio", "protonet", "protractor", "prott", "pug", "pulumi", "pulumi-icon", "pumpkindb", "puppet", "puppet-icon", "puppeteer", "puppy-linux", "purescript", "purescript-icon", "pushbullet", "pusher", "pusher-icon", "pwa", "pycharm", "pypi", "pyscript", "python", "pytorch", "pytorch-icon", "pyup", "q", "qdrant", "qdrant-icon", "qlik", "qordoba", "qt", "qualcomm", "quarkus", "quarkus-icon", "quay", "quobyte", "quora", "qwik", "qwik-icon", "r-lang", "rabbitmq", "rabbitmq-icon", "rackspace", "rackspace-icon", "rails", "ramda", "raml", "rancher", "rancher-icon", "randomcolor", "<PERSON><PERSON><PERSON>", "raspberry-pi", "rax", "react", "react-query", "react-query-icon", "react-router", "react-spring", "react-styleguidist", "reactivex", "realm", "reapp", "reasonml", "reasonml-icon", "recaptcha", "recoil", "recoil-icon", "reddit", "reddit-icon", "redhat", "redhat-icon", "redis", "redsmin", "redspread", "redux", "redux-observable", "redux-saga", "redwoodjs", "refactor", "reindex", "relay", "release", "remergr", "remix", "remix-icon", "renovatebot", "replay", "replay-icon", "replit", "replit-icon", "require", "rescript", "rescript-icon", "rest", "rest-li", "rethinkdb", "retool", "retool-icon", "riak", "rider", "riot", "risingwave", "risingwave-icon", "rkt", "rocket-chat", "rocket-chat-icon", "rocksdb", "rocky-linux", "rocky-linux-icon", "rollbar", "rollbar-icon", "rollupjs", "rome", "rome-icon", "ros", "rsa", "rsmq", "rubocop", "ruby", "rubygems", "rubymine", "rum", "run-above", "runnable", "runscope", "rush", "rush-icon", "rust", "rxdb", "safari", "sagui", "sails", "salesforce", "saltstack", "sameroom", "samsung", "sanity", "sap", "sass", "sass-doc", "saucelabs", "scala", "scaledrone", "scaphold", "scribd", "scribd-icon", "seaborn", "seaborn-icon", "section", "section-icon", "sectionio", "segment", "segment-icon", "selenium", "semantic-release", "semantic-ui", "semantic-web", "semaphore", "semaph<PERSON><PERSON>", "sencha", "sendgrid", "sendgrid-icon", "seneca", "sensu", "sensu-icon", "sentry", "sentry-icon", "sequelize", "serveless", "serverless", "<PERSON><PERSON>ock", "sherlock-icon", "shields", "shipit", "shippable", "shogun", "shopify", "shortcut", "shortcut-icon", "sidekick", "sidekiq", "sidekiq-icon", "signal", "sigstore", "sigstore-icon", "sinatra", "singlestore", "singlestore-icon", "siphon", "sitepoint", "sk-hynix", "skaffolder", "sketch", "<PERSON><PERSON>p", "skylight", "skype", "slack", "slack-icon", "slides", "slim", "smartling", "smashingmagazine", "snap-svg", "snaplet", "snaplet-icon", "snowflake", "snowflake-icon", "snowpack", "snupps", "snyk", "socket-io", "solarwinds", "solid", "solidity", "solidjs", "solidjs-icon", "solr", "sonarcloud", "sonarcloud-icon", "sonarlint", "sonarlint-icon", "sonarqube", "soundcloud", "sourcegraph", "sourcetrail", "sourcetree", "spark", "sparkcentral", "sparkpost", "speakerdeck", "speedcurve", "spidermonkey", "spidermonkey-icon", "spinnaker", "splunk", "spotify", "spotify-icon", "spree", "spring", "spring-icon", "sqldep", "sqlite", "square", "squarespace", "sst", "sst-icon", "stability-ai", "stability-ai-icon", "stackbit", "stackbit-icon", "stackblitz", "stackblitz-icon", "stackoverflow", "stackoverflow-icon", "stackshare", "<PERSON>mith", "stash", "stately", "stately-icon", "statuspage", "stdlib", "stdlib-icon", "steam", "steemit", "stenciljs", "stenciljs-icon", "stepsize", "stepsize-icon", "steroids", "stetho", "stickermule", "stigg", "stigg-icon", "stimulus", "stimulus-icon", "stitch", "stoplight", "stormpath", "storyblocks", "storyblocks-icon", "storyblok", "storyblok-icon", "storybook", "storybook-icon", "strapi", "strapi-icon", "streamlit", "strider", "stripe", "strongloop", "struts", "styleci", "stylefmt", "stylelint", "stylis", "stylus", "stytch", "sublimetext", "sublimetext-icon", "subversion", "sugarss", "supabase", "supabase-icon", "supergiant", "supersonic", "supertokens", "supertokens-icon", "supportkit", "surge", "surrealdb", "surrealdb-icon", "survicate", "survicate-icon", "suse", "susy", "svelte", "svelte-icon", "svelte-kit", "svg", "svgator", "swagger", "swc", "swift", "swiftype", "swimm", "swr", "symfony", "sysdig", "sysdig-icon", "t3", "tableau", "tableau-icon", "taiga", "tailwindcss", "tailwindcss-icon", "tapcart", "tapcart-icon", "targetprocess", "taskade", "taskade-icon", "tastejs", "tauri", "tealium", "teamcity", "teamgrid", "teamwork", "teamwork-icon", "tectonic", "telegram", "tensorflow", "terminal", "terraform", "terraform-icon", "terser", "terser-icon", "testcafe", "testing-library", "testlodge", "testmunk", "thimble", "threejs", "thymel<PERSON><PERSON>", "thymeleaf-icon", "tidal", "tidal-icon", "tiktok", "tiktok-icon", "titon", "tnw", "todoist", "todoist-icon", "todomvc", "tomcat", "toml", "tor", "tor-browser", "torus", "tra<PERSON>r", "trac", "trace", "travis-ci", "travis-ci-monochrome", "treasuredata", "treasuredata-icon", "treehouse", "treehouse-icon", "trello", "trpc", "truffle", "truffle-icon", "tsmc", "tsnode", "tsu", "tsuru", "tumblr", "tumblr-icon", "tunein", "tuple", "turbopack", "turbopack-icon", "turborepo", "turborepo-icon", "turret", "tutsplus", "tutum", "twi<PERSON>", "twilio-icon", "twitch", "twitter", "typeform", "typeform-icon", "typeorm", "typescript", "typescript-icon", "typescript-icon-round", "typesense", "typesense-icon", "typo3", "typo3-icon", "ubuntu", "udacity", "udacity-icon", "udemy", "udemy-icon", "uikit", "umu", "unbounce", "unbounce-icon", "undertow", "unionpay", "unitjs", "unito", "unito-icon", "unity", "unocss", "unrealengine", "unrealengine-icon", "upcase", "upstash", "upstash-icon", "upwork", "user-testing", "user-testing-icon", "uservoice", "uservoice-icon", "uwsgi", "v8", "v8-ignition", "v8-turbofan", "vaadin", "vaddy", "vagrant", "vagrant-icon", "vault", "vault-icon", "vector", "vector-timber", "vercel", "vercel-icon", "verda<PERSON><PERSON>", "verdaccio-icon", "vernemq", "victoro<PERSON>", "vim", "vimeo", "vimeo-icon", "vine", "visa", "visaelectron", "visual-studio", "visual-studio-code", "visual-website-optimizer", "vite<PERSON><PERSON>", "vitess", "vitest", "vivaldi", "vivaldi-icon", "vlang", "void", "vue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vueuse", "vulkan", "vultr", "vultr-icon", "vwo", "w3c", "waffle", "waffle-icon", "wagtail", "wakatime", "walkme", "watchman", "waypoint", "waypoint-icon", "wayscript", "wayscript-icon", "wearos", "weave", "web-dev", "web-dev-icon", "web-fundamentals", "web3js", "webassembly", "webcomponents", "webdriverio", "webflow", "webgpu", "web<PERSON>t", "webhint-icon", "webhooks", "webix", "webix-icon", "webkit", "webmin", "webpack", "webplatform", "webrtc", "websocket", "webstorm", "webtask", "webtorrent", "weebly", "<PERSON><PERSON><PERSON>", "whalar", "whalar-icon", "whatsapp", "whatsapp-icon", "whatsapp-monochrome-icon", "whatwg", "wicket", "wicket-icon", "wifi", "wildfly", "windi-css", "winglang", "winglang-icon", "wire", "wiredtree", "wix", "wmr", "woocommerce", "woocommerce-icon", "woopra", "wordpress", "wordpress-icon", "wordpress-icon-alt", "workboard", "workos", "workos-icon", "workplace", "workplace-icon", "wpengine", "wufoo", "x-ray-goggles", "xamarin", "xampp", "xata", "xata-icon", "xcart", "xcode", "xero", "xplenty", "xray-for-jira", "xstate", "xtend", "xwiki", "xwiki-icon", "yahoo", "yaml", "yammer", "yandex-ru", "yarn", "ycombinator", "yeoman", "yii", "youtrack", "youtube", "youtube-icon", "yugabyte", "yugabyte-icon", "zabbix", "zapier", "zapier-icon", "zeit", "zeit-icon", "zend-framework", "zendesk", "zendesk-icon", "zenhub", "zenhub-icon", "z<PERSON>lin", "zeroheight", "zeroheight-icon", "zest", "zig", "zigbee", "zod", "zoho", "zoom", "zoom-icon", "zorin-os", "zsh", "zube", "<PERSON><PERSON>", "zulip-icon", "zwave"]}, {"prefix": "twe<PERSON><PERSON>", "info": {"name": "Twitter Emoji", "total": 3668, "author": {"name": "Twitter", "url": "https://github.com/twitter/twemoji"}, "license": {"title": "CC BY 4.0", "spdx": "CC-BY-4.0", "url": "https://creativecommons.org/licenses/by/4.0/"}, "samples": ["anguished-face", "duck", "crossed-swords"], "height": 36, "displayHeight": 18, "category": "<PERSON><PERSON><PERSON>", "palette": true}, "icons": ["1st-place-medal", "2nd-place-medal", "3rd-place-medal", "a-button-blood-type", "ab-button-blood-type", "abacus", "accordion", "adhesive-bandage", "admission-tickets", "adult", "adult-dark-skin-tone", "adult-light-skin-tone", "adult-medium-dark-skin-tone", "adult-medium-light-skin-tone", "adult-medium-skin-tone", "aerial-tramway", "airplane", "airplane-arrival", "airplane-departure", "alarm-clock", "alembic", "alien", "alien-monster", "ambulance", "american-football", "amphora", "anatomical-heart", "anchor", "anger-symbol", "angry-face", "angry-face-with-horns", "anguished-face", "ant", "antenna-bars", "anxious-face-with-sweat", "aquarius", "aries", "articulated-lorry", "artist", "artist-dark-skin-tone", "artist-light-skin-tone", "artist-medium-dark-skin-tone", "artist-medium-light-skin-tone", "artist-medium-skin-tone", "artist-palette", "astonished-face", "astronaut", "astronaut-dark-skin-tone", "astronaut-light-skin-tone", "astronaut-medium-dark-skin-tone", "astronaut-medium-light-skin-tone", "astronaut-medium-skin-tone", "atm-sign", "atom-symbol", "auto-rickshaw", "automobile", "avocado", "axe", "b-button-blood-type", "baby", "baby-angel", "baby-angel-dark-skin-tone", "baby-angel-light-skin-tone", "baby-angel-medium-dark-skin-tone", "baby-angel-medium-light-skin-tone", "baby-angel-medium-skin-tone", "baby-bottle", "baby-chick", "baby-dark-skin-tone", "baby-light-skin-tone", "baby-medium-dark-skin-tone", "baby-medium-light-skin-tone", "baby-medium-skin-tone", "baby-symbol", "back-arrow", "backhand-index-pointing-down", "backhand-index-pointing-down-dark-skin-tone", "backhand-index-pointing-down-light-skin-tone", "backhand-index-pointing-down-medium-dark-skin-tone", "backhand-index-pointing-down-medium-light-skin-tone", "backhand-index-pointing-down-medium-skin-tone", "backhand-index-pointing-left", "backhand-index-pointing-left-dark-skin-tone", "backhand-index-pointing-left-light-skin-tone", "backhand-index-pointing-left-medium-dark-skin-tone", "backhand-index-pointing-left-medium-light-skin-tone", "backhand-index-pointing-left-medium-skin-tone", "backhand-index-pointing-right", "backhand-index-pointing-right-dark-skin-tone", "backhand-index-pointing-right-light-skin-tone", "backhand-index-pointing-right-medium-dark-skin-tone", "backhand-index-pointing-right-medium-light-skin-tone", "backhand-index-pointing-right-medium-skin-tone", "backhand-index-pointing-up", "backhand-index-pointing-up-dark-skin-tone", "backhand-index-pointing-up-light-skin-tone", "backhand-index-pointing-up-medium-dark-skin-tone", "backhand-index-pointing-up-medium-light-skin-tone", "backhand-index-pointing-up-medium-skin-tone", "backpack", "bacon", "badger", "badminton", "bagel", "baggage-claim", "baguette-bread", "balance-scale", "bald", "ballet-shoes", "balloon", "ballot-box-with-ballot", "banana", "banjo", "bank", "bar-chart", "barber-pole", "baseball", "basket", "basketball", "bat", "bathtub", "battery", "beach-with-umbrella", "beaming-face-with-smiling-eyes", "beans", "bear", "bearded-person", "bearded-person-dark-skin-tone", "bearded-person-light-skin-tone", "bearded-person-medium-dark-skin-tone", "bearded-person-medium-light-skin-tone", "bearded-person-medium-skin-tone", "beating-heart", "beaver", "bed", "beer-mug", "beetle", "bell", "bell-pepper", "bell-with-slash", "bellhop-bell", "bento-box", "beverage-box", "bicycle", "bikini", "billed-cap", "biohazard", "bird", "birthday-cake", "bison", "biting-lip", "black-cat", "black-circle", "black-flag", "black-heart", "black-large-square", "black-medium-small-square", "black-medium-square", "black-nib", "black-small-square", "black-square-button", "blossom", "blowfish", "blue-book", "blue-circle", "blue-heart", "blue-square", "blueberries", "boar", "bomb", "bone", "bookmark", "bookmark-tabs", "books", "boomerang", "bottle-with-popping-cork", "bouquet", "bow-and-arrow", "bowl-with-spoon", "bowling", "boxing-glove", "boy", "boy-dark-skin-tone", "boy-light-skin-tone", "boy-medium-dark-skin-tone", "boy-medium-light-skin-tone", "boy-medium-skin-tone", "brain", "bread", "breast-feeding", "breast-feeding-dark-skin-tone", "breast-feeding-light-skin-tone", "breast-feeding-medium-dark-skin-tone", "breast-feeding-medium-light-skin-tone", "breast-feeding-medium-skin-tone", "brick", "bridge-at-night", "briefcase", "briefs", "bright-button", "broccoli", "broken-heart", "broom", "brown-circle", "brown-heart", "brown-square", "bubble-tea", "bubbles", "bucket", "bug", "building-construction", "bullet-train", "bullseye", "burrito", "bus", "bus-stop", "bust-in-silhouette", "busts-in-silhouette", "butter", "butterfly", "cactus", "calendar", "call-me-hand", "call-me-hand-dark-skin-tone", "call-me-hand-light-skin-tone", "call-me-hand-medium-dark-skin-tone", "call-me-hand-medium-light-skin-tone", "call-me-hand-medium-skin-tone", "camel", "camera", "camera-with-flash", "camping", "cancer", "candle", "candy", "canned-food", "canoe", "capricorn", "card-file-box", "card-index", "card-index-dividers", "carousel-horse", "carp-streamer", "carpentry-saw", "carrot", "castle", "cat", "cat-face", "cat-with-tears-of-joy", "cat-with-wry-smile", "chains", "chair", "chart-decreasing", "chart-increasing", "chart-increasing-with-yen", "check-box-with-check", "check-mark", "check-mark-button", "cheese-wedge", "chequered-flag", "cherries", "cherry-blossom", "chess-pawn", "chestnut", "chicken", "child", "child-dark-skin-tone", "child-light-skin-tone", "child-medium-dark-skin-tone", "child-medium-light-skin-tone", "child-medium-skin-tone", "children-crossing", "chipmunk", "chocolate-bar", "chopsticks", "christmas-tree", "church", "cigarette", "cinema", "circled-m", "circus-tent", "cityscape", "cityscape-at-dusk", "cl-button", "clamp", "clapper-board", "clapping-hands", "clapping-hands-dark-skin-tone", "clapping-hands-light-skin-tone", "clapping-hands-medium-dark-skin-tone", "clapping-hands-medium-light-skin-tone", "clapping-hands-medium-skin-tone", "classical-building", "clinking-beer-mugs", "clinking-glasses", "clipboard", "clockwise-vertical-arrows", "closed-book", "closed-mailbox-with-lowered-flag", "closed-mailbox-with-raised-flag", "closed-umbrella", "cloud", "cloud-with-lightning", "cloud-with-lightning-and-rain", "cloud-with-rain", "cloud-with-snow", "clown-face", "club-suit", "clutch-bag", "coat", "cockroach", "cocktail-glass", "coconut", "coffin", "coin", "cold-face", "collision", "comet", "compass", "computer-disk", "computer-mouse", "confetti-ball", "confounded-face", "confused-face", "construction", "construction-worker", "construction-worker-dark-skin-tone", "construction-worker-light-skin-tone", "construction-worker-medium-dark-skin-tone", "construction-worker-medium-light-skin-tone", "construction-worker-medium-skin-tone", "control-knobs", "convenience-store", "cook", "cook-dark-skin-tone", "cook-light-skin-tone", "cook-medium-dark-skin-tone", "cook-medium-light-skin-tone", "cook-medium-skin-tone", "cooked-rice", "cookie", "cooking", "cool-button", "copyright", "coral", "couch-and-lamp", "counterclockwise-arrows-button", "couple-with-heart", "couple-with-heart-dark-skin-tone", "couple-with-heart-light-skin-tone", "couple-with-heart-man-man", "couple-with-heart-man-man-dark-skin-tone", "couple-with-heart-man-man-dark-skin-tone-light-skin-tone", "couple-with-heart-man-man-dark-skin-tone-medium-dark-skin-tone", "couple-with-heart-man-man-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-man-man-dark-skin-tone-medium-skin-tone", "couple-with-heart-man-man-light-skin-tone", "couple-with-heart-man-man-light-skin-tone-dark-skin-tone", "couple-with-heart-man-man-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-man-man-light-skin-tone-medium-light-skin-tone", "couple-with-heart-man-man-light-skin-tone-medium-skin-tone", "couple-with-heart-man-man-medium-dark-skin-tone", "couple-with-heart-man-man-medium-dark-skin-tone-dark-skin-tone", "couple-with-heart-man-man-medium-dark-skin-tone-light-skin-tone", "couple-with-heart-man-man-medium-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-man-man-medium-dark-skin-tone-medium-skin-tone", "couple-with-heart-man-man-medium-light-skin-tone", "couple-with-heart-man-man-medium-light-skin-tone-dark-skin-tone", "couple-with-heart-man-man-medium-light-skin-tone-light-skin-tone", "couple-with-heart-man-man-medium-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-man-man-medium-light-skin-tone-medium-skin-tone", "couple-with-heart-man-man-medium-skin-tone", "couple-with-heart-man-man-medium-skin-tone-dark-skin-tone", "couple-with-heart-man-man-medium-skin-tone-light-skin-tone", "couple-with-heart-man-man-medium-skin-tone-medium-dark-skin-tone", "couple-with-heart-man-man-medium-skin-tone-medium-light-skin-tone", "couple-with-heart-medium-dark-skin-tone", "couple-with-heart-medium-light-skin-tone", "couple-with-heart-medium-skin-tone", "couple-with-heart-person-person-dark-skin-tone-light-skin-tone", "couple-with-heart-person-person-dark-skin-tone-medium-dark-skin-tone", "couple-with-heart-person-person-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-person-person-dark-skin-tone-medium-skin-tone", "couple-with-heart-person-person-light-skin-tone-dark-skin-tone", "couple-with-heart-person-person-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-person-person-light-skin-tone-medium-light-skin-tone", "couple-with-heart-person-person-light-skin-tone-medium-skin-tone", "couple-with-heart-person-person-medium-dark-skin-tone-dark-skin-tone", "couple-with-heart-person-person-medium-dark-skin-tone-light-skin-tone", "couple-with-heart-person-person-medium-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-person-person-medium-dark-skin-tone-medium-skin-tone", "couple-with-heart-person-person-medium-light-skin-tone-dark-skin-tone", "couple-with-heart-person-person-medium-light-skin-tone-light-skin-tone", "couple-with-heart-person-person-medium-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-person-person-medium-light-skin-tone-medium-skin-tone", "couple-with-heart-person-person-medium-skin-tone-dark-skin-tone", "couple-with-heart-person-person-medium-skin-tone-light-skin-tone", "couple-with-heart-person-person-medium-skin-tone-medium-dark-skin-tone", "couple-with-heart-person-person-medium-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-man", "couple-with-heart-woman-man-dark-skin-tone", "couple-with-heart-woman-man-dark-skin-tone-light-skin-tone", "couple-with-heart-woman-man-dark-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-man-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-man-dark-skin-tone-medium-skin-tone", "couple-with-heart-woman-man-light-skin-tone", "couple-with-heart-woman-man-light-skin-tone-dark-skin-tone", "couple-with-heart-woman-man-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-man-light-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-man-light-skin-tone-medium-skin-tone", "couple-with-heart-woman-man-medium-dark-skin-tone", "couple-with-heart-woman-man-medium-dark-skin-tone-dark-skin-tone", "couple-with-heart-woman-man-medium-dark-skin-tone-light-skin-tone", "couple-with-heart-woman-man-medium-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-man-medium-dark-skin-tone-medium-skin-tone", "couple-with-heart-woman-man-medium-light-skin-tone", "couple-with-heart-woman-man-medium-light-skin-tone-dark-skin-tone", "couple-with-heart-woman-man-medium-light-skin-tone-light-skin-tone", "couple-with-heart-woman-man-medium-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-man-medium-light-skin-tone-medium-skin-tone", "couple-with-heart-woman-man-medium-skin-tone", "couple-with-heart-woman-man-medium-skin-tone-dark-skin-tone", "couple-with-heart-woman-man-medium-skin-tone-light-skin-tone", "couple-with-heart-woman-man-medium-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-man-medium-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-woman", "couple-with-heart-woman-woman-dark-skin-tone", "couple-with-heart-woman-woman-dark-skin-tone-light-skin-tone", "couple-with-heart-woman-woman-dark-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-woman-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-woman-dark-skin-tone-medium-skin-tone", "couple-with-heart-woman-woman-light-skin-tone", "couple-with-heart-woman-woman-light-skin-tone-dark-skin-tone", "couple-with-heart-woman-woman-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-woman-light-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-woman-light-skin-tone-medium-skin-tone", "couple-with-heart-woman-woman-medium-dark-skin-tone", "couple-with-heart-woman-woman-medium-dark-skin-tone-dark-skin-tone", "couple-with-heart-woman-woman-medium-dark-skin-tone-light-skin-tone", "couple-with-heart-woman-woman-medium-dark-skin-tone-medium-light-skin-tone", "couple-with-heart-woman-woman-medium-dark-skin-tone-medium-skin-tone", "couple-with-heart-woman-woman-medium-light-skin-tone", "couple-with-heart-woman-woman-medium-light-skin-tone-dark-skin-tone", "couple-with-heart-woman-woman-medium-light-skin-tone-light-skin-tone", "couple-with-heart-woman-woman-medium-light-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-woman-medium-light-skin-tone-medium-skin-tone", "couple-with-heart-woman-woman-medium-skin-tone", "couple-with-heart-woman-woman-medium-skin-tone-dark-skin-tone", "couple-with-heart-woman-woman-medium-skin-tone-light-skin-tone", "couple-with-heart-woman-woman-medium-skin-tone-medium-dark-skin-tone", "couple-with-heart-woman-woman-medium-skin-tone-medium-light-skin-tone", "cow", "cow-face", "cowboy-hat-face", "crab", "crayon", "credit-card", "crescent-moon", "cricket", "cricket-game", "crocodile", "croissant", "cross-mark", "cross-mark-button", "crossed-fingers", "crossed-fingers-dark-skin-tone", "crossed-fingers-light-skin-tone", "crossed-fingers-medium-dark-skin-tone", "crossed-fingers-medium-light-skin-tone", "crossed-fingers-medium-skin-tone", "crossed-flags", "crossed-swords", "crown", "crutch", "crying-cat", "crying-face", "crystal-ball", "cucumber", "cup-with-straw", "cupcake", "curling-stone", "curly-haired", "curly-loop", "currency-exchange", "curry-rice", "custard", "customs", "cut-of-meat", "cyclone", "dagger", "dango", "dark-skin-tone", "dashing-away", "deaf-man", "deaf-man-dark-skin-tone", "deaf-man-light-skin-tone", "deaf-man-medium-dark-skin-tone", "deaf-man-medium-light-skin-tone", "deaf-man-medium-skin-tone", "deaf-person", "deaf-person-dark-skin-tone", "deaf-person-light-skin-tone", "deaf-person-medium-dark-skin-tone", "deaf-person-medium-light-skin-tone", "deaf-person-medium-skin-tone", "deaf-woman", "deaf-woman-dark-skin-tone", "deaf-woman-light-skin-tone", "deaf-woman-medium-dark-skin-tone", "deaf-woman-medium-light-skin-tone", "deaf-woman-medium-skin-tone", "deciduous-tree", "deer", "delivery-truck", "department-store", "derelict-house", "desert", "desert-island", "desktop-computer", "detective", "detective-dark-skin-tone", "detective-light-skin-tone", "detective-medium-dark-skin-tone", "detective-medium-light-skin-tone", "detective-medium-skin-tone", "diamond-suit", "diamond-with-a-dot", "dim-button", "disappointed-face", "disguised-face", "divide", "diving-mask", "diya-lamp", "dizzy", "dna", "dodo", "dog", "dog-face", "dollar-banknote", "dolphin", "door", "dotted-line-face", "dotted-six-pointed-star", "double-curly-loop", "double-exclamation-mark", "doughnut", "dove", "down-arrow", "down-left-arrow", "down-right-arrow", "downcast-face-with-sweat", "downwards-button", "dragon", "dragon-face", "dress", "drooling-face", "drop-of-blood", "droplet", "drum", "duck", "dumpling", "dvd", "e-mail", "eagle", "ear", "ear-dark-skin-tone", "ear-light-skin-tone", "ear-medium-dark-skin-tone", "ear-medium-light-skin-tone", "ear-medium-skin-tone", "ear-of-corn", "ear-with-hearing-aid", "ear-with-hearing-aid-dark-skin-tone", "ear-with-hearing-aid-light-skin-tone", "ear-with-hearing-aid-medium-dark-skin-tone", "ear-with-hearing-aid-medium-light-skin-tone", "ear-with-hearing-aid-medium-skin-tone", "egg", "eggplant", "eight-oclock", "eight-pointed-star", "eight-spoked-asterisk", "eight-thirty", "eject-button", "electric-plug", "elephant", "elevator", "eleven-oclock", "eleven-thirty", "elf", "elf-dark-skin-tone", "elf-light-skin-tone", "elf-medium-dark-skin-tone", "elf-medium-light-skin-tone", "elf-medium-skin-tone", "empty-nest", "end-arrow", "enraged-face", "envelope", "envelope-with-arrow", "euro-banknote", "evergreen-tree", "ewe", "exclamation-question-mark", "exploding-head", "expressionless-face", "eye", "eye-in-speech-bubble", "eyes", "face-blowing-a-kiss", "face-exhaling", "face-holding-back-tears", "face-in-clouds", "face-savoring-food", "face-screaming-in-fear", "face-vomiting", "face-with-crossed-out-eyes", "face-with-diagonal-mouth", "face-with-hand-over-mouth", "face-with-head-bandage", "face-with-medical-mask", "face-with-monocle", "face-with-open-eyes-and-hand-over-mouth", "face-with-open-mouth", "face-with-peeking-eye", "face-with-raised-eyebrow", "face-with-rolling-eyes", "face-with-spiral-eyes", "face-with-steam-from-nose", "face-with-symbols-on-mouth", "face-with-tears-of-joy", "face-with-thermometer", "face-with-tongue", "face-without-mouth", "factory", "factory-worker", "factory-worker-dark-skin-tone", "factory-worker-light-skin-tone", "factory-worker-medium-dark-skin-tone", "factory-worker-medium-light-skin-tone", "factory-worker-medium-skin-tone", "fairy", "fairy-dark-skin-tone", "fairy-light-skin-tone", "fairy-medium-dark-skin-tone", "fairy-medium-light-skin-tone", "fairy-medium-skin-tone", "falafel", "fallen-leaf", "family", "family-man-boy", "family-man-boy-boy", "family-man-girl", "family-man-girl-boy", "family-man-girl-girl", "family-man-man-boy", "family-man-man-boy-boy", "family-man-man-girl", "family-man-man-girl-boy", "family-man-man-girl-girl", "family-man-woman-boy", "family-man-woman-boy-boy", "family-man-woman-girl", "family-man-woman-girl-boy", "family-man-woman-girl-girl", "family-woman-boy", "family-woman-boy-boy", "family-woman-girl", "family-woman-girl-boy", "family-woman-girl-girl", "family-woman-woman-boy", "family-woman-woman-boy-boy", "family-woman-woman-girl", "family-woman-woman-girl-boy", "family-woman-woman-girl-girl", "farmer", "farmer-dark-skin-tone", "farmer-light-skin-tone", "farmer-medium-dark-skin-tone", "farmer-medium-light-skin-tone", "farmer-medium-skin-tone", "fast-down-button", "fast-forward-button", "fast-reverse-button", "fast-up-button", "fax-machine", "fearful-face", "feather", "female-sign", "ferris-wheel", "ferry", "field-hockey", "file-cabinet", "file-folder", "film-frames", "film-projector", "fire", "fire-engine", "fire-extinguisher", "firecracker", "firefighter", "firefighter-dark-skin-tone", "firefighter-light-skin-tone", "firefighter-medium-dark-skin-tone", "firefighter-medium-light-skin-tone", "firefighter-medium-skin-tone", "fireworks", "first-quarter-moon", "first-quarter-moon-face", "fish", "fish-cake-with-swirl", "fishing-pole", "five-oclock", "five-thirty", "flag-afghanistan", "flag-aland-islands", "flag-albania", "flag-algeria", "flag-american-samoa", "flag-andorra", "flag-angola", "flag-anguilla", "flag-antarctica", "flag-antigua-and-barbuda", "flag-argentina", "flag-armenia", "flag-aruba", "flag-ascension-island", "flag-australia", "flag-austria", "flag-azerbaijan", "flag-bahamas", "flag-bahrain", "flag-bangladesh", "flag-barbados", "flag-belarus", "flag-belgium", "flag-belize", "flag-benin", "flag-bermuda", "flag-bhu<PERSON>", "flag-bolivia", "flag-bosnia-and-herzegovina", "flag-botswana", "flag-bouvet-island", "flag-brazil", "flag-british-indian-ocean-territory", "flag-british-virgin-islands", "flag-brunei", "flag-bulgaria", "flag-burkina-faso", "flag-burundi", "flag-cambodia", "flag-cameroon", "flag-canada", "flag-canary-islands", "flag-cape-verde", "flag-caribbean-netherlands", "flag-cayman-islands", "flag-central-african-republic", "flag-ceuta-and-melilla", "flag-chad", "flag-chile", "flag-china", "flag-christmas-island", "flag-clipperton-island", "flag-cocos-keeling-islands", "flag-colombia", "flag-comoros", "flag-congo-brazzaville", "flag-congo-kinshasa", "flag-cook-islands", "flag-costa-rica", "flag-cote-divoire", "flag-croatia", "flag-cuba", "flag-curacao", "flag-cyprus", "flag-czechia", "flag-denmark", "flag-diego-garcia", "flag-djibouti", "flag-dominica", "flag-dominican-republic", "flag-ecuador", "flag-egypt", "flag-el-salvador", "flag-england", "flag-equatorial-guinea", "flag-eritrea", "flag-estonia", "flag-<PERSON><PERSON><PERSON>", "flag-ethiopia", "flag-european-union", "flag-falkland-islands", "flag-faroe-islands", "flag-fiji", "flag-finland", "flag-for-flag-afghanistan", "flag-for-flag-albania", "flag-for-flag-algeria", "flag-for-flag-american-samoa", "flag-for-flag-andorra", "flag-for-flag-angola", "flag-for-flag-antigua-and-barbuda", "flag-for-flag-argentina", "flag-for-flag-aruba", "flag-for-flag-ascension-island", "flag-for-flag-australia", "flag-for-flag-azerbaijan", "flag-for-flag-bahamas", "flag-for-flag-bangladesh", "flag-for-flag-barbados", "flag-for-flag-belarus", "flag-for-flag-belize", "flag-for-flag-bermuda", "flag-for-flag-bolivia", "flag-for-flag-bosnia-and-herzegovina", "flag-for-flag-brazil", "flag-for-flag-british-indian-ocean-territory", "flag-for-flag-burkina-faso", "flag-for-flag-burundi", "flag-for-flag-canary-islands", "flag-for-flag-cape-verde", "flag-for-flag-caribbean-netherlands", "flag-for-flag-cayman-islands", "flag-for-flag-central-african-republic", "flag-for-flag-ceuta-and-melilla", "flag-for-flag-china", "flag-for-flag-christmas-island", "flag-for-flag-cocos-keeling-islands", "flag-for-flag-comoros", "flag-for-flag-cook-islands", "flag-for-flag-costa-rica", "flag-for-flag-croatia", "flag-for-flag-cuba", "flag-for-flag-cyprus", "flag-for-flag-djibouti", "flag-for-flag-dominica", "flag-for-flag-dominican-republic", "flag-for-flag-ecuador", "flag-for-flag-egypt", "flag-for-flag-el-salvador", "flag-for-flag-equatorial-guinea", "flag-for-flag-<PERSON><PERSON><PERSON>", "flag-for-flag-ethiopia", "flag-for-flag-european-union", "flag-for-flag-falkland-islands", "flag-for-flag-fiji", "flag-for-flag-french-guiana", "flag-for-flag-french-polynesia", "flag-for-flag-french-southern-territories", "flag-for-flag-ghana", "flag-for-flag-gibraltar", "flag-for-flag-greenland", "flag-for-flag-grenada", "flag-for-flag-guam", "flag-for-flag-guatemala", "flag-for-flag-guinea-bissau", "flag-for-flag-guyana", "flag-for-flag-haiti", "flag-for-flag-honduras", "flag-for-flag-hong-kong-sar-china", "flag-for-flag-india", "flag-for-flag-isle-of-man", "flag-for-flag-israel", "flag-for-flag-japan", "flag-for-flag-jersey", "flag-for-flag-j<PERSON>an", "flag-for-flag-kazakhstan", "flag-for-flag-kenya", "flag-for-flag-kiribati", "flag-for-flag-kosovo", "flag-for-flag-laos", "flag-for-flag-lebanon", "flag-for-flag-liberia", "flag-for-flag-liechtenstein", "flag-for-flag-<PERSON><PERSON><PERSON>", "flag-for-flag-malaysia", "flag-for-flag-malta", "flag-for-flag-marshall-islands", "flag-for-flag-mauritania", "flag-for-flag-mayotte", "flag-for-flag-mexico", "flag-for-flag-micronesia", "flag-for-flag-<PERSON>ova", "flag-for-flag-montserrat", "flag-for-flag-morocco", "flag-for-flag-mozambique", "flag-for-flag-namibia", "flag-for-flag-nauru", "flag-for-flag-nepal", "flag-for-flag-new-caledonia", "flag-for-flag-new-zealand", "flag-for-flag-nicar<PERSON><PERSON>", "flag-for-flag-niger", "flag-for-flag-niue", "flag-for-flag-northern-mariana-islands", "flag-for-flag-pakistan", "flag-for-flag-palau", "flag-for-flag-panama", "flag-for-flag-papua-new-guinea", "flag-for-flag-paraguay", "flag-for-flag-philippines", "flag-for-flag-pitcairn-islands", "flag-for-flag-puerto-rico", "flag-for-flag-qatar", "flag-for-flag-reunion", "flag-for-flag-<PERSON><PERSON><PERSON>", "flag-for-flag-sa<PERSON>a", "flag-for-flag-sao-tome-and-principe", "flag-for-flag-saudi-arabia", "flag-for-flag-seychelles", "flag-for-flag-singapore", "flag-for-flag-sint-maarten", "flag-for-flag-slovenia", "flag-for-flag-solomon-islands", "flag-for-flag-south-georgia-and-south-sandwich-islands", "flag-for-flag-south-korea", "flag-for-flag-south-sudan", "flag-for-flag-sri-lanka", "flag-for-flag-st-bar<PERSON><PERSON><PERSON>", "flag-for-flag-st-helena", "flag-for-flag-st-kitts-and-nevis", "flag-for-flag-st-lucia", "flag-for-flag-st-pierre-and-miquelon", "flag-for-flag-st-vincent-and-grenadines", "flag-for-flag-syria", "flag-for-flag-taiwan", "flag-for-flag-timor-leste", "flag-for-flag-tokelau", "flag-for-flag-tristan-<PERSON>-<PERSON><PERSON>a", "flag-for-flag-tunisia", "flag-for-flag-turkmenistan", "flag-for-flag-tuvalu", "flag-for-flag-uganda", "flag-for-flag-united-kingdom", "flag-for-flag-united-nations", "flag-for-flag-united-states", "flag-for-flag-uruguay", "flag-for-flag-us-virgin-islands", "flag-for-flag-vanuatu", "flag-for-flag-vatican-city", "flag-for-flag-venezu<PERSON>", "flag-for-flag-wallis-and-futuna", "flag-for-flag-western-sahara", "flag-for-flag-zimbabwe", "flag-france", "flag-french-guiana", "flag-french-polynesia", "flag-french-southern-territories", "flag-gabon", "flag-gambia", "flag-georgia", "flag-germany", "flag-ghana", "flag-gibraltar", "flag-greece", "flag-greenland", "flag-grenada", "flag-guadeloupe", "flag-guam", "flag-guatemala", "flag-guernsey", "flag-guinea", "flag-guinea-bissau", "flag-guyana", "flag-haiti", "flag-heard-and-mcdonald-islands", "flag-honduras", "flag-hong-kong-sar-china", "flag-hungary", "flag-iceland", "flag-in-hole", "flag-india", "flag-indonesia", "flag-iran", "flag-iraq", "flag-ireland", "flag-isle-of-man", "flag-israel", "flag-italy", "flag-jamaica", "flag-japan", "flag-jersey", "flag-j<PERSON>an", "flag-kazakhstan", "flag-kenya", "flag-kiribati", "flag-kosovo", "flag-kuwait", "flag-kyrgyzstan", "flag-laos", "flag-latvia", "flag-lebanon", "flag-lesotho", "flag-liberia", "flag-libya", "flag-liechtenstein", "flag-lithuania", "flag-luxembourg", "flag-macao-sar-china", "flag-madagascar", "flag-malawi", "flag-malaysia", "flag-maldives", "flag-mali", "flag-malta", "flag-marshall-islands", "flag-martinique", "flag-mauritania", "flag-mauri<PERSON>", "flag-mayotte", "flag-mexico", "flag-micronesia", "flag-moldova", "flag-monaco", "flag-mongolia", "flag-montenegro", "flag-montserrat", "flag-morocco", "flag-mozambique", "flag-myanmar-burma", "flag-namibia", "flag-nauru", "flag-nepal", "flag-netherlands", "flag-new-caledonia", "flag-new-zealand", "flag-nicaragua", "flag-niger", "flag-nigeria", "flag-niue", "flag-norfolk-island", "flag-north-korea", "flag-north-macedonia", "flag-northern-mariana-islands", "flag-norway", "flag-oman", "flag-pakistan", "flag-palau", "flag-palestinian-territories", "flag-panama", "flag-papua-new-guinea", "flag-paraguay", "flag-peru", "flag-philippines", "flag-pitcairn-islands", "flag-poland", "flag-portugal", "flag-puerto-rico", "flag-qatar", "flag-reunion", "flag-romania", "flag-russia", "flag-rwan<PERSON>", "flag-samoa", "flag-san-marino", "flag-sao-tome-and-principe", "flag-saudi-arabia", "flag-scotland", "flag-senegal", "flag-serbia", "flag-seychelles", "flag-sierra-leone", "flag-singapore", "flag-sint-maarten", "flag-slov<PERSON><PERSON>", "flag-slovenia", "flag-solomon-islands", "flag-somalia", "flag-south-africa", "flag-south-georgia-and-south-sandwich-islands", "flag-south-korea", "flag-south-sudan", "flag-spain", "flag-sri-lanka", "flag-st-bar<PERSON><PERSON><PERSON>", "flag-st-helena", "flag-st-kitts-and-nevis", "flag-st-lucia", "flag-st-martin", "flag-st-pierre-and-miquelon", "flag-st-vincent-and-grenadines", "flag-sudan", "flag-suriname", "flag-svalbard-and-jan-mayen", "flag-sweden", "flag-switzerland", "flag-syria", "flag-taiwan", "flag-tajikistan", "flag-tanzania", "flag-thailand", "flag-timor-leste", "flag-togo", "flag-to<PERSON><PERSON>", "flag-tonga", "flag-trinidad-and-tobago", "flag-tristan-da-cunha", "flag-tunisia", "flag-tur<PERSON><PERSON>", "flag-turkmenistan", "flag-turks-and-caicos-islands", "flag-tuvalu", "flag-uganda", "flag-ukraine", "flag-united-arab-emirates", "flag-united-kingdom", "flag-united-nations", "flag-united-states", "flag-uruguay", "flag-us-outlying-islands", "flag-us-virgin-islands", "flag-uzbekistan", "flag-vanuatu", "flag-vatican-city", "flag-venezuela", "flag-vietnam", "flag-wales", "flag-wallis-and-futuna", "flag-western-sahara", "flag-yemen", "flag-zambia", "flag-zimbabwe", "flamingo", "flashlight", "flat-shoe", "flatbread", "fleur-de-lis", "flexed-biceps", "flexed-biceps-dark-skin-tone", "flexed-biceps-light-skin-tone", "flexed-biceps-medium-dark-skin-tone", "flexed-biceps-medium-light-skin-tone", "flexed-biceps-medium-skin-tone", "floppy-disk", "flower-playing-cards", "flushed-face", "fly", "flying-disc", "flying-saucer", "fog", "foggy", "folded-hands", "folded-hands-dark-skin-tone", "folded-hands-light-skin-tone", "folded-hands-medium-dark-skin-tone", "folded-hands-medium-light-skin-tone", "folded-hands-medium-skin-tone", "fondue", "foot", "foot-dark-skin-tone", "foot-light-skin-tone", "foot-medium-dark-skin-tone", "foot-medium-light-skin-tone", "foot-medium-skin-tone", "footprints", "fork-and-knife", "fork-and-knife-with-plate", "fortune-cookie", "fountain", "fountain-pen", "four-leaf-clover", "four-oclock", "four-thirty", "fox", "framed-picture", "free-button", "french-fries", "fried-shrimp", "frog", "front-facing-baby-chick", "frowning-face", "frowning-face-with-open-mouth", "fuel-pump", "full-moon", "full-moon-face", "funeral-urn", "game-die", "garlic", "gear", "gem-stone", "gemini", "genie", "ghost", "giraffe", "girl", "girl-dark-skin-tone", "girl-light-skin-tone", "girl-medium-dark-skin-tone", "girl-medium-light-skin-tone", "girl-medium-skin-tone", "glass-of-milk", "glasses", "globe-showing-americas", "globe-showing-asia-australia", "globe-showing-europe-africa", "globe-with-meridians", "gloves", "glowing-star", "goal-net", "goat", "goblin", "goggles", "gorilla", "graduation-cap", "grapes", "green-apple", "green-book", "green-circle", "green-heart", "green-salad", "green-square", "grimacing-face", "grinning-cat", "grinning-cat-with-smiling-eyes", "grinning-face", "grinning-face-with-big-eyes", "grinning-face-with-smiling-eyes", "grinning-face-with-sweat", "grinning-squinting-face", "growing-heart", "guard", "guard-dark-skin-tone", "guard-light-skin-tone", "guard-medium-dark-skin-tone", "guard-medium-light-skin-tone", "guard-medium-skin-tone", "guide-dog", "guitar", "hamburger", "hammer", "hammer-and-pick", "hammer-and-wrench", "hamsa", "hamster", "hand-with-fingers-splayed", "hand-with-fingers-splayed-dark-skin-tone", "hand-with-fingers-splayed-light-skin-tone", "hand-with-fingers-splayed-medium-dark-skin-tone", "hand-with-fingers-splayed-medium-light-skin-tone", "hand-with-fingers-splayed-medium-skin-tone", "hand-with-index-finger-and-thumb-crossed", "hand-with-index-finger-and-thumb-crossed-dark-skin-tone", "hand-with-index-finger-and-thumb-crossed-light-skin-tone", "hand-with-index-finger-and-thumb-crossed-medium-dark-skin-tone", "hand-with-index-finger-and-thumb-crossed-medium-light-skin-tone", "hand-with-index-finger-and-thumb-crossed-medium-skin-tone", "handbag", "handshake", "handshake-dark-skin-tone", "handshake-dark-skin-tone-light-skin-tone", "handshake-dark-skin-tone-medium-dark-skin-tone", "handshake-dark-skin-tone-medium-light-skin-tone", "handshake-dark-skin-tone-medium-skin-tone", "handshake-light-skin-tone", "handshake-light-skin-tone-dark-skin-tone", "handshake-light-skin-tone-medium-dark-skin-tone", "handshake-light-skin-tone-medium-light-skin-tone", "handshake-light-skin-tone-medium-skin-tone", "handshake-medium-dark-skin-tone", "handshake-medium-dark-skin-tone-dark-skin-tone", "handshake-medium-dark-skin-tone-light-skin-tone", "handshake-medium-dark-skin-tone-medium-light-skin-tone", "handshake-medium-dark-skin-tone-medium-skin-tone", "handshake-medium-light-skin-tone", "handshake-medium-light-skin-tone-dark-skin-tone", "handshake-medium-light-skin-tone-light-skin-tone", "handshake-medium-light-skin-tone-medium-dark-skin-tone", "handshake-medium-light-skin-tone-medium-skin-tone", "handshake-medium-skin-tone", "handshake-medium-skin-tone-dark-skin-tone", "handshake-medium-skin-tone-light-skin-tone", "handshake-medium-skin-tone-medium-dark-skin-tone", "handshake-medium-skin-tone-medium-light-skin-tone", "hatching-chick", "headphone", "headstone", "health-worker", "health-worker-dark-skin-tone", "health-worker-light-skin-tone", "health-worker-medium-dark-skin-tone", "health-worker-medium-light-skin-tone", "health-worker-medium-skin-tone", "hear-no-evil-monkey", "heart-decoration", "heart-exclamation", "heart-hands", "heart-hands-dark-skin-tone", "heart-hands-light-skin-tone", "heart-hands-medium-dark-skin-tone", "heart-hands-medium-light-skin-tone", "heart-hands-medium-skin-tone", "heart-on-fire", "heart-suit", "heart-with-arrow", "heart-with-ribbon", "heavy-dollar-sign", "heavy-equals-sign", "hedgehog", "helicopter", "herb", "hibiscus", "high-heeled-shoe", "high-speed-train", "high-voltage", "hiking-boot", "hindu-temple", "hippopotamus", "hole", "hollow-red-circle", "honey-pot", "honeybee", "hook", "horizontal-traffic-light", "horse", "horse-face", "horse-racing", "horse-racing-dark-skin-tone", "horse-racing-light-skin-tone", "horse-racing-medium-dark-skin-tone", "horse-racing-medium-light-skin-tone", "horse-racing-medium-skin-tone", "hospital", "hot-beverage", "hot-dog", "hot-face", "hot-pepper", "hot-springs", "hotel", "hourglass-done", "hourglass-not-done", "house", "house-with-garden", "houses", "hugging-face", "hundred-points", "hushed-face", "hut", "ice", "ice-cream", "ice-hockey", "ice-skate", "id-button", "identification-card", "inbox-tray", "incoming-envelope", "index-pointing-at-the-viewer", "index-pointing-at-the-viewer-dark-skin-tone", "index-pointing-at-the-viewer-light-skin-tone", "index-pointing-at-the-viewer-medium-dark-skin-tone", "index-pointing-at-the-viewer-medium-light-skin-tone", "index-pointing-at-the-viewer-medium-skin-tone", "index-pointing-up", "index-pointing-up-dark-skin-tone", "index-pointing-up-light-skin-tone", "index-pointing-up-medium-dark-skin-tone", "index-pointing-up-medium-light-skin-tone", "index-pointing-up-medium-skin-tone", "infinity", "information", "input-latin-letters", "input-latin-lowercase", "input-latin-uppercase", "input-numbers", "input-symbols", "jack-o-lantern", "japanese-acceptable-button", "japanese-application-button", "japanese-bargain-button", "japanese-castle", "japanese-congratulations-button", "japanese-discount-button", "japanese-dolls", "japanese-free-of-charge-button", "japanese-here-button", "japanese-monthly-amount-button", "japanese-no-vacancy-button", "japanese-not-free-of-charge-button", "japanese-open-for-business-button", "japanese-passing-grade-button", "japanese-post-office", "japanese-prohibited-button", "japanese-reserved-button", "japanese-secret-button", "japanese-service-charge-button", "japanese-symbol-for-beginner", "japanese-vacancy-button", "jar", "jeans", "joker", "joystick", "judge", "judge-dark-skin-tone", "judge-light-skin-tone", "judge-medium-dark-skin-tone", "judge-medium-light-skin-tone", "judge-medium-skin-tone", "kaaba", "kangaroo", "key", "keyboard", "keycap-0", "keycap-1", "keycap-10", "keycap-2", "keycap-3", "keycap-4", "keycap-5", "keycap-6", "keycap-7", "keycap-8", "keycap-9", "keycap-asterisk", "keycap-pound", "kick-scooter", "kimono", "kiss", "kiss-dark-skin-tone", "kiss-light-skin-tone", "kiss-man-man", "kiss-man-man-dark-skin-tone", "kiss-man-man-dark-skin-tone-light-skin-tone", "kiss-man-man-dark-skin-tone-medium-dark-skin-tone", "kiss-man-man-dark-skin-tone-medium-light-skin-tone", "kiss-man-man-dark-skin-tone-medium-skin-tone", "kiss-man-man-light-skin-tone", "kiss-man-man-light-skin-tone-dark-skin-tone", "kiss-man-man-light-skin-tone-medium-dark-skin-tone", "kiss-man-man-light-skin-tone-medium-light-skin-tone", "kiss-man-man-light-skin-tone-medium-skin-tone", "kiss-man-man-medium-dark-skin-tone", "kiss-man-man-medium-dark-skin-tone-dark-skin-tone", "kiss-man-man-medium-dark-skin-tone-light-skin-tone", "kiss-man-man-medium-dark-skin-tone-medium-light-skin-tone", "kiss-man-man-medium-dark-skin-tone-medium-skin-tone", "kiss-man-man-medium-light-skin-tone", "kiss-man-man-medium-light-skin-tone-dark-skin-tone", "kiss-man-man-medium-light-skin-tone-light-skin-tone", "kiss-man-man-medium-light-skin-tone-medium-dark-skin-tone", "kiss-man-man-medium-light-skin-tone-medium-skin-tone", "kiss-man-man-medium-skin-tone", "kiss-man-man-medium-skin-tone-dark-skin-tone", "kiss-man-man-medium-skin-tone-light-skin-tone", "kiss-man-man-medium-skin-tone-medium-dark-skin-tone", "kiss-man-man-medium-skin-tone-medium-light-skin-tone", "kiss-mark", "kiss-medium-dark-skin-tone", "kiss-medium-light-skin-tone", "kiss-medium-skin-tone", "kiss-person-person-dark-skin-tone-light-skin-tone", "kiss-person-person-dark-skin-tone-medium-dark-skin-tone", "kiss-person-person-dark-skin-tone-medium-light-skin-tone", "kiss-person-person-dark-skin-tone-medium-skin-tone", "kiss-person-person-light-skin-tone-dark-skin-tone", "kiss-person-person-light-skin-tone-medium-dark-skin-tone", "kiss-person-person-light-skin-tone-medium-light-skin-tone", "kiss-person-person-light-skin-tone-medium-skin-tone", "kiss-person-person-medium-dark-skin-tone-dark-skin-tone", "kiss-person-person-medium-dark-skin-tone-light-skin-tone", "kiss-person-person-medium-dark-skin-tone-medium-light-skin-tone", "kiss-person-person-medium-dark-skin-tone-medium-skin-tone", "kiss-person-person-medium-light-skin-tone-dark-skin-tone", "kiss-person-person-medium-light-skin-tone-light-skin-tone", "kiss-person-person-medium-light-skin-tone-medium-dark-skin-tone", "kiss-person-person-medium-light-skin-tone-medium-skin-tone", "kiss-person-person-medium-skin-tone-dark-skin-tone", "kiss-person-person-medium-skin-tone-light-skin-tone", "kiss-person-person-medium-skin-tone-medium-dark-skin-tone", "kiss-person-person-medium-skin-tone-medium-light-skin-tone", "kiss-woman-man", "kiss-woman-man-dark-skin-tone", "kiss-woman-man-dark-skin-tone-light-skin-tone", "kiss-woman-man-dark-skin-tone-medium-dark-skin-tone", "kiss-woman-man-dark-skin-tone-medium-light-skin-tone", "kiss-woman-man-dark-skin-tone-medium-skin-tone", "kiss-woman-man-light-skin-tone", "kiss-woman-man-light-skin-tone-dark-skin-tone", "kiss-woman-man-light-skin-tone-medium-dark-skin-tone", "kiss-woman-man-light-skin-tone-medium-light-skin-tone", "kiss-woman-man-light-skin-tone-medium-skin-tone", "kiss-woman-man-medium-dark-skin-tone", "kiss-woman-man-medium-dark-skin-tone-dark-skin-tone", "kiss-woman-man-medium-dark-skin-tone-light-skin-tone", "kiss-woman-man-medium-dark-skin-tone-medium-light-skin-tone", "kiss-woman-man-medium-dark-skin-tone-medium-skin-tone", "kiss-woman-man-medium-light-skin-tone", "kiss-woman-man-medium-light-skin-tone-dark-skin-tone", "kiss-woman-man-medium-light-skin-tone-light-skin-tone", "kiss-woman-man-medium-light-skin-tone-medium-dark-skin-tone", "kiss-woman-man-medium-light-skin-tone-medium-skin-tone", "kiss-woman-man-medium-skin-tone", "kiss-woman-man-medium-skin-tone-dark-skin-tone", "kiss-woman-man-medium-skin-tone-light-skin-tone", "kiss-woman-man-medium-skin-tone-medium-dark-skin-tone", "kiss-woman-man-medium-skin-tone-medium-light-skin-tone", "kiss-woman-woman", "kiss-woman-woman-dark-skin-tone", "kiss-woman-woman-dark-skin-tone-light-skin-tone", "kiss-woman-woman-dark-skin-tone-medium-dark-skin-tone", "kiss-woman-woman-dark-skin-tone-medium-light-skin-tone", "kiss-woman-woman-dark-skin-tone-medium-skin-tone", "kiss-woman-woman-light-skin-tone", "kiss-woman-woman-light-skin-tone-dark-skin-tone", "kiss-woman-woman-light-skin-tone-medium-dark-skin-tone", "kiss-woman-woman-light-skin-tone-medium-light-skin-tone", "kiss-woman-woman-light-skin-tone-medium-skin-tone", "kiss-woman-woman-medium-dark-skin-tone", "kiss-woman-woman-medium-dark-skin-tone-dark-skin-tone", "kiss-woman-woman-medium-dark-skin-tone-light-skin-tone", "kiss-woman-woman-medium-dark-skin-tone-medium-light-skin-tone", "kiss-woman-woman-medium-dark-skin-tone-medium-skin-tone", "kiss-woman-woman-medium-light-skin-tone", "kiss-woman-woman-medium-light-skin-tone-dark-skin-tone", "kiss-woman-woman-medium-light-skin-tone-light-skin-tone", "kiss-woman-woman-medium-light-skin-tone-medium-dark-skin-tone", "kiss-woman-woman-medium-light-skin-tone-medium-skin-tone", "kiss-woman-woman-medium-skin-tone", "kiss-woman-woman-medium-skin-tone-dark-skin-tone", "kiss-woman-woman-medium-skin-tone-light-skin-tone", "kiss-woman-woman-medium-skin-tone-medium-dark-skin-tone", "kiss-woman-woman-medium-skin-tone-medium-light-skin-tone", "kissing-cat", "kissing-face", "kissing-face-with-closed-eyes", "kissing-face-with-smiling-eyes", "kitchen-knife", "kite", "kiwi-fruit", "knocked-out-face", "knot", "koala", "lab-coat", "label", "lacrosse", "ladder", "lady-beetle", "laptop", "large-blue-diamond", "large-orange-diamond", "last-quarter-moon", "last-quarter-moon-face", "last-track-button", "latin-cross", "leaf-fluttering-in-wind", "leafy-green", "ledger", "left-arrow", "left-arrow-curving-right", "left-facing-fist", "left-facing-fist-dark-skin-tone", "left-facing-fist-light-skin-tone", "left-facing-fist-medium-dark-skin-tone", "left-facing-fist-medium-light-skin-tone", "left-facing-fist-medium-skin-tone", "left-luggage", "left-right-arrow", "left-speech-bubble", "leftwards-hand", "leftwards-hand-dark-skin-tone", "leftwards-hand-light-skin-tone", "leftwards-hand-medium-dark-skin-tone", "leftwards-hand-medium-light-skin-tone", "leftwards-hand-medium-skin-tone", "leg", "leg-dark-skin-tone", "leg-light-skin-tone", "leg-medium-dark-skin-tone", "leg-medium-light-skin-tone", "leg-medium-skin-tone", "lemon", "leo", "leopard", "letter-a", "letter-b", "letter-c", "letter-d", "letter-e", "letter-f", "letter-g", "letter-h", "letter-i", "letter-j", "letter-k", "letter-l", "letter-m", "letter-n", "letter-o", "letter-p", "letter-q", "letter-r", "letter-s", "letter-t", "letter-u", "letter-v", "letter-w", "letter-x", "letter-y", "letter-z", "level-slider", "libra", "light-bulb", "light-rail", "light-skin-tone", "link", "linked-paperclips", "lion", "lipstick", "litter-in-bin-sign", "lizard", "llama", "lobster", "locked", "locked-with-key", "locked-with-pen", "locomotive", "lollipop", "long-drum", "lotion-bottle", "lotus", "loudly-crying-face", "loudspeaker", "love-hotel", "love-letter", "love-you-gesture", "love-you-gesture-dark-skin-tone", "love-you-gesture-light-skin-tone", "love-you-gesture-medium-dark-skin-tone", "love-you-gesture-medium-light-skin-tone", "love-you-gesture-medium-skin-tone", "low-battery", "luggage", "lungs", "lying-face", "mage", "mage-dark-skin-tone", "mage-light-skin-tone", "mage-medium-dark-skin-tone", "mage-medium-light-skin-tone", "mage-medium-skin-tone", "magic-wand", "magnet", "magnifying-glass-tilted-left", "magnifying-glass-tilted-right", "mahjong-red-dragon", "male-sign", "mammoth", "man", "man-and-woman-holding-hands", "man-artist", "man-artist-dark-skin-tone", "man-artist-light-skin-tone", "man-artist-medium-dark-skin-tone", "man-artist-medium-light-skin-tone", "man-artist-medium-skin-tone", "man-astronaut", "man-astronaut-dark-skin-tone", "man-astronaut-light-skin-tone", "man-astronaut-medium-dark-skin-tone", "man-astronaut-medium-light-skin-tone", "man-astronaut-medium-skin-tone", "man-bald", "man-beard", "man-biking", "man-biking-dark-skin-tone", "man-biking-light-skin-tone", "man-biking-medium-dark-skin-tone", "man-biking-medium-light-skin-tone", "man-biking-medium-skin-tone", "man-blond-hair", "man-bouncing-ball", "man-bouncing-ball-dark-skin-tone", "man-bouncing-ball-light-skin-tone", "man-bouncing-ball-medium-dark-skin-tone", "man-bouncing-ball-medium-light-skin-tone", "man-bouncing-ball-medium-skin-tone", "man-bowing", "man-bowing-dark-skin-tone", "man-bowing-light-skin-tone", "man-bowing-medium-dark-skin-tone", "man-bowing-medium-light-skin-tone", "man-bowing-medium-skin-tone", "man-cartwheeling", "man-cartwheeling-dark-skin-tone", "man-cartwheeling-light-skin-tone", "man-cartwheeling-medium-dark-skin-tone", "man-cartwheeling-medium-light-skin-tone", "man-cartwheeling-medium-skin-tone", "man-climbing", "man-climbing-dark-skin-tone", "man-climbing-light-skin-tone", "man-climbing-medium-dark-skin-tone", "man-climbing-medium-light-skin-tone", "man-climbing-medium-skin-tone", "man-construction-worker", "man-construction-worker-dark-skin-tone", "man-construction-worker-light-skin-tone", "man-construction-worker-medium-dark-skin-tone", "man-construction-worker-medium-light-skin-tone", "man-construction-worker-medium-skin-tone", "man-cook", "man-cook-dark-skin-tone", "man-cook-light-skin-tone", "man-cook-medium-dark-skin-tone", "man-cook-medium-light-skin-tone", "man-cook-medium-skin-tone", "man-curly-hair", "man-dancing", "man-dancing-dark-skin-tone", "man-dancing-light-skin-tone", "man-dancing-medium-dark-skin-tone", "man-dancing-medium-light-skin-tone", "man-dancing-medium-skin-tone", "man-dark-skin-tone", "man-dark-skin-tone-bald", "man-dark-skin-tone-beard", "man-dark-skin-tone-blond-hair", "man-dark-skin-tone-curly-hair", "man-dark-skin-tone-red-hair", "man-dark-skin-tone-white-hair", "man-detective", "man-detective-dark-skin-tone", "man-detective-light-skin-tone", "man-detective-medium-dark-skin-tone", "man-detective-medium-light-skin-tone", "man-detective-medium-skin-tone", "man-elf", "man-elf-dark-skin-tone", "man-elf-light-skin-tone", "man-elf-medium-dark-skin-tone", "man-elf-medium-light-skin-tone", "man-elf-medium-skin-tone", "man-facepalming", "man-facepalming-dark-skin-tone", "man-facepalming-light-skin-tone", "man-facepalming-medium-dark-skin-tone", "man-facepalming-medium-light-skin-tone", "man-facepalming-medium-skin-tone", "man-factory-worker", "man-factory-worker-dark-skin-tone", "man-factory-worker-light-skin-tone", "man-factory-worker-medium-dark-skin-tone", "man-factory-worker-medium-light-skin-tone", "man-factory-worker-medium-skin-tone", "man-fairy", "man-fairy-dark-skin-tone", "man-fairy-light-skin-tone", "man-fairy-medium-dark-skin-tone", "man-fairy-medium-light-skin-tone", "man-fairy-medium-skin-tone", "man-farmer", "man-farmer-dark-skin-tone", "man-farmer-light-skin-tone", "man-farmer-medium-dark-skin-tone", "man-farmer-medium-light-skin-tone", "man-farmer-medium-skin-tone", "man-feeding-baby", "man-feeding-baby-dark-skin-tone", "man-feeding-baby-light-skin-tone", "man-feeding-baby-medium-dark-skin-tone", "man-feeding-baby-medium-light-skin-tone", "man-feeding-baby-medium-skin-tone", "man-firefighter", "man-firefighter-dark-skin-tone", "man-firefighter-light-skin-tone", "man-firefighter-medium-dark-skin-tone", "man-firefighter-medium-light-skin-tone", "man-firefighter-medium-skin-tone", "man-frowning", "man-frowning-dark-skin-tone", "man-frowning-light-skin-tone", "man-frowning-medium-dark-skin-tone", "man-frowning-medium-light-skin-tone", "man-frowning-medium-skin-tone", "man-genie", "man-gesturing-no", "man-gesturing-no-dark-skin-tone", "man-gesturing-no-light-skin-tone", "man-gesturing-no-medium-dark-skin-tone", "man-gesturing-no-medium-light-skin-tone", "man-gesturing-no-medium-skin-tone", "man-gesturing-ok", "man-gesturing-ok-dark-skin-tone", "man-gesturing-ok-light-skin-tone", "man-gesturing-ok-medium-dark-skin-tone", "man-gesturing-ok-medium-light-skin-tone", "man-gesturing-ok-medium-skin-tone", "man-getting-haircut", "man-getting-haircut-dark-skin-tone", "man-getting-haircut-light-skin-tone", "man-getting-haircut-medium-dark-skin-tone", "man-getting-haircut-medium-light-skin-tone", "man-getting-haircut-medium-skin-tone", "man-getting-massage", "man-getting-massage-dark-skin-tone", "man-getting-massage-light-skin-tone", "man-getting-massage-medium-dark-skin-tone", "man-getting-massage-medium-light-skin-tone", "man-getting-massage-medium-skin-tone", "man-golfing", "man-golfing-dark-skin-tone", "man-golfing-light-skin-tone", "man-golfing-medium-dark-skin-tone", "man-golfing-medium-light-skin-tone", "man-golfing-medium-skin-tone", "man-guard", "man-guard-dark-skin-tone", "man-guard-light-skin-tone", "man-guard-medium-dark-skin-tone", "man-guard-medium-light-skin-tone", "man-guard-medium-skin-tone", "man-health-worker", "man-health-worker-dark-skin-tone", "man-health-worker-light-skin-tone", "man-health-worker-medium-dark-skin-tone", "man-health-worker-medium-light-skin-tone", "man-health-worker-medium-skin-tone", "man-in-lotus-position", "man-in-lotus-position-dark-skin-tone", "man-in-lotus-position-light-skin-tone", "man-in-lotus-position-medium-dark-skin-tone", "man-in-lotus-position-medium-light-skin-tone", "man-in-lotus-position-medium-skin-tone", "man-in-manual-wheelchair", "man-in-manual-wheelchair-dark-skin-tone", "man-in-manual-wheelchair-light-skin-tone", "man-in-manual-wheelchair-medium-dark-skin-tone", "man-in-manual-wheelchair-medium-light-skin-tone", "man-in-manual-wheelchair-medium-skin-tone", "man-in-motorized-wheelchair", "man-in-motorized-wheelchair-dark-skin-tone", "man-in-motorized-wheelchair-light-skin-tone", "man-in-motorized-wheelchair-medium-dark-skin-tone", "man-in-motorized-wheelchair-medium-light-skin-tone", "man-in-motorized-wheelchair-medium-skin-tone", "man-in-steamy-room", "man-in-steamy-room-dark-skin-tone", "man-in-steamy-room-light-skin-tone", "man-in-steamy-room-medium-dark-skin-tone", "man-in-steamy-room-medium-light-skin-tone", "man-in-steamy-room-medium-skin-tone", "man-in-suit-levitating", "man-in-suit-levitating-dark-skin-tone", "man-in-suit-levitating-light-skin-tone", "man-in-suit-levitating-medium-dark-skin-tone", "man-in-suit-levitating-medium-light-skin-tone", "man-in-suit-levitating-medium-skin-tone", "man-in-tuxedo", "man-in-tuxedo-dark-skin-tone", "man-in-tuxedo-light-skin-tone", "man-in-tuxedo-medium-dark-skin-tone", "man-in-tuxedo-medium-light-skin-tone", "man-in-tuxedo-medium-skin-tone", "man-judge", "man-judge-dark-skin-tone", "man-judge-light-skin-tone", "man-judge-medium-dark-skin-tone", "man-judge-medium-light-skin-tone", "man-judge-medium-skin-tone", "man-juggling", "man-juggling-dark-skin-tone", "man-juggling-light-skin-tone", "man-juggling-medium-dark-skin-tone", "man-juggling-medium-light-skin-tone", "man-juggling-medium-skin-tone", "man-kneeling", "man-kneeling-dark-skin-tone", "man-kneeling-light-skin-tone", "man-kneeling-medium-dark-skin-tone", "man-kneeling-medium-light-skin-tone", "man-kneeling-medium-skin-tone", "man-lifting-weights", "man-lifting-weights-dark-skin-tone", "man-lifting-weights-light-skin-tone", "man-lifting-weights-medium-dark-skin-tone", "man-lifting-weights-medium-light-skin-tone", "man-lifting-weights-medium-skin-tone", "man-light-skin-tone", "man-light-skin-tone-bald", "man-light-skin-tone-beard", "man-light-skin-tone-blond-hair", "man-light-skin-tone-curly-hair", "man-light-skin-tone-red-hair", "man-light-skin-tone-white-hair", "man-mage", "man-mage-dark-skin-tone", "man-mage-light-skin-tone", "man-mage-medium-dark-skin-tone", "man-mage-medium-light-skin-tone", "man-mage-medium-skin-tone", "man-mechanic", "man-mechanic-dark-skin-tone", "man-mechanic-light-skin-tone", "man-mechanic-medium-dark-skin-tone", "man-mechanic-medium-light-skin-tone", "man-mechanic-medium-skin-tone", "man-medium-dark-skin-tone", "man-medium-dark-skin-tone-bald", "man-medium-dark-skin-tone-beard", "man-medium-dark-skin-tone-blond-hair", "man-medium-dark-skin-tone-curly-hair", "man-medium-dark-skin-tone-red-hair", "man-medium-dark-skin-tone-white-hair", "man-medium-light-skin-tone", "man-medium-light-skin-tone-bald", "man-medium-light-skin-tone-beard", "man-medium-light-skin-tone-blond-hair", "man-medium-light-skin-tone-curly-hair", "man-medium-light-skin-tone-red-hair", "man-medium-light-skin-tone-white-hair", "man-medium-skin-tone", "man-medium-skin-tone-bald", "man-medium-skin-tone-beard", "man-medium-skin-tone-blond-hair", "man-medium-skin-tone-curly-hair", "man-medium-skin-tone-red-hair", "man-medium-skin-tone-white-hair", "man-mountain-biking", "man-mountain-biking-dark-skin-tone", "man-mountain-biking-light-skin-tone", "man-mountain-biking-medium-dark-skin-tone", "man-mountain-biking-medium-light-skin-tone", "man-mountain-biking-medium-skin-tone", "man-office-worker", "man-office-worker-dark-skin-tone", "man-office-worker-light-skin-tone", "man-office-worker-medium-dark-skin-tone", "man-office-worker-medium-light-skin-tone", "man-office-worker-medium-skin-tone", "man-pilot", "man-pilot-dark-skin-tone", "man-pilot-light-skin-tone", "man-pilot-medium-dark-skin-tone", "man-pilot-medium-light-skin-tone", "man-pilot-medium-skin-tone", "man-playing-handball", "man-playing-handball-dark-skin-tone", "man-playing-handball-light-skin-tone", "man-playing-handball-medium-dark-skin-tone", "man-playing-handball-medium-light-skin-tone", "man-playing-handball-medium-skin-tone", "man-playing-water-polo", "man-playing-water-polo-dark-skin-tone", "man-playing-water-polo-light-skin-tone", "man-playing-water-polo-medium-dark-skin-tone", "man-playing-water-polo-medium-light-skin-tone", "man-playing-water-polo-medium-skin-tone", "man-police-officer", "man-police-officer-dark-skin-tone", "man-police-officer-light-skin-tone", "man-police-officer-medium-dark-skin-tone", "man-police-officer-medium-light-skin-tone", "man-police-officer-medium-skin-tone", "man-pouting", "man-pouting-dark-skin-tone", "man-pouting-light-skin-tone", "man-pouting-medium-dark-skin-tone", "man-pouting-medium-light-skin-tone", "man-pouting-medium-skin-tone", "man-raising-hand", "man-raising-hand-dark-skin-tone", "man-raising-hand-light-skin-tone", "man-raising-hand-medium-dark-skin-tone", "man-raising-hand-medium-light-skin-tone", "man-raising-hand-medium-skin-tone", "man-red-hair", "man-rowing-boat", "man-rowing-boat-dark-skin-tone", "man-rowing-boat-light-skin-tone", "man-rowing-boat-medium-dark-skin-tone", "man-rowing-boat-medium-light-skin-tone", "man-rowing-boat-medium-skin-tone", "man-running", "man-running-dark-skin-tone", "man-running-light-skin-tone", "man-running-medium-dark-skin-tone", "man-running-medium-light-skin-tone", "man-running-medium-skin-tone", "man-scientist", "man-scientist-dark-skin-tone", "man-scientist-light-skin-tone", "man-scientist-medium-dark-skin-tone", "man-scientist-medium-light-skin-tone", "man-scientist-medium-skin-tone", "man-shrugging", "man-shrugging-dark-skin-tone", "man-shrugging-light-skin-tone", "man-shrugging-medium-dark-skin-tone", "man-shrugging-medium-light-skin-tone", "man-shrugging-medium-skin-tone", "man-singer", "man-singer-dark-skin-tone", "man-singer-light-skin-tone", "man-singer-medium-dark-skin-tone", "man-singer-medium-light-skin-tone", "man-singer-medium-skin-tone", "man-standing", "man-standing-dark-skin-tone", "man-standing-light-skin-tone", "man-standing-medium-dark-skin-tone", "man-standing-medium-light-skin-tone", "man-standing-medium-skin-tone", "man-student", "man-student-dark-skin-tone", "man-student-light-skin-tone", "man-student-medium-dark-skin-tone", "man-student-medium-light-skin-tone", "man-student-medium-skin-tone", "man-superhero", "man-superhero-dark-skin-tone", "man-superhero-light-skin-tone", "man-superhero-medium-dark-skin-tone", "man-superhero-medium-light-skin-tone", "man-superhero-medium-skin-tone", "man-supervillain", "man-supervillain-dark-skin-tone", "man-supervillain-light-skin-tone", "man-supervillain-medium-dark-skin-tone", "man-supervillain-medium-light-skin-tone", "man-supervillain-medium-skin-tone", "man-surfing", "man-surfing-dark-skin-tone", "man-surfing-light-skin-tone", "man-surfing-medium-dark-skin-tone", "man-surfing-medium-light-skin-tone", "man-surfing-medium-skin-tone", "man-swimming", "man-swimming-dark-skin-tone", "man-swimming-light-skin-tone", "man-swimming-medium-dark-skin-tone", "man-swimming-medium-light-skin-tone", "man-swimming-medium-skin-tone", "man-teacher", "man-teacher-dark-skin-tone", "man-teacher-light-skin-tone", "man-teacher-medium-dark-skin-tone", "man-teacher-medium-light-skin-tone", "man-teacher-medium-skin-tone", "man-technologist", "man-technologist-dark-skin-tone", "man-technologist-light-skin-tone", "man-technologist-medium-dark-skin-tone", "man-technologist-medium-light-skin-tone", "man-technologist-medium-skin-tone", "man-tipping-hand", "man-tipping-hand-dark-skin-tone", "man-tipping-hand-light-skin-tone", "man-tipping-hand-medium-dark-skin-tone", "man-tipping-hand-medium-light-skin-tone", "man-tipping-hand-medium-skin-tone", "man-vampire", "man-vampire-dark-skin-tone", "man-vampire-light-skin-tone", "man-vampire-medium-dark-skin-tone", "man-vampire-medium-light-skin-tone", "man-vampire-medium-skin-tone", "man-walking", "man-walking-dark-skin-tone", "man-walking-light-skin-tone", "man-walking-medium-dark-skin-tone", "man-walking-medium-light-skin-tone", "man-walking-medium-skin-tone", "man-wearing-turban", "man-wearing-turban-dark-skin-tone", "man-wearing-turban-light-skin-tone", "man-wearing-turban-medium-dark-skin-tone", "man-wearing-turban-medium-light-skin-tone", "man-wearing-turban-medium-skin-tone", "man-white-hair", "man-with-veil", "man-with-veil-dark-skin-tone", "man-with-veil-light-skin-tone", "man-with-veil-medium-dark-skin-tone", "man-with-veil-medium-light-skin-tone", "man-with-veil-medium-skin-tone", "man-with-white-cane", "man-with-white-cane-dark-skin-tone", "man-with-white-cane-light-skin-tone", "man-with-white-cane-medium-dark-skin-tone", "man-with-white-cane-medium-light-skin-tone", "man-with-white-cane-medium-skin-tone", "man-zombie", "mango", "mans-shoe", "mantelpiece-clock", "manual-wheelchair", "map-of-japan", "maple-leaf", "martial-arts-uniform", "mate", "meat-on-bone", "mechanic", "mechanic-dark-skin-tone", "mechanic-light-skin-tone", "mechanic-medium-dark-skin-tone", "mechanic-medium-light-skin-tone", "mechanic-medium-skin-tone", "mechanical-arm", "mechanical-leg", "medical-symbol", "medium-dark-skin-tone", "medium-light-skin-tone", "medium-skin-tone", "megaphone", "melon", "melting-face", "memo", "men-holding-hands", "men-holding-hands-dark-skin-tone", "men-holding-hands-dark-skin-tone-light-skin-tone", "men-holding-hands-dark-skin-tone-medium-dark-skin-tone", "men-holding-hands-dark-skin-tone-medium-light-skin-tone", "men-holding-hands-dark-skin-tone-medium-skin-tone", "men-holding-hands-light-skin-tone", "men-holding-hands-light-skin-tone-dark-skin-tone", "men-holding-hands-light-skin-tone-medium-dark-skin-tone", "men-holding-hands-light-skin-tone-medium-light-skin-tone", "men-holding-hands-light-skin-tone-medium-skin-tone", "men-holding-hands-medium-dark-skin-tone", "men-holding-hands-medium-dark-skin-tone-dark-skin-tone", "men-holding-hands-medium-dark-skin-tone-light-skin-tone", "men-holding-hands-medium-dark-skin-tone-medium-light-skin-tone", "men-holding-hands-medium-dark-skin-tone-medium-skin-tone", "men-holding-hands-medium-light-skin-tone", "men-holding-hands-medium-light-skin-tone-dark-skin-tone", "men-holding-hands-medium-light-skin-tone-light-skin-tone", "men-holding-hands-medium-light-skin-tone-medium-dark-skin-tone", "men-holding-hands-medium-light-skin-tone-medium-skin-tone", "men-holding-hands-medium-skin-tone", "men-holding-hands-medium-skin-tone-dark-skin-tone", "men-holding-hands-medium-skin-tone-light-skin-tone", "men-holding-hands-medium-skin-tone-medium-dark-skin-tone", "men-holding-hands-medium-skin-tone-medium-light-skin-tone", "men-with-bunny-ears", "men-wrestling", "mending-heart", "menorah", "mens-room", "mermaid", "mermaid-dark-skin-tone", "mermaid-light-skin-tone", "mermaid-medium-dark-skin-tone", "mermaid-medium-light-skin-tone", "mermaid-medium-skin-tone", "merman", "merman-dark-skin-tone", "merman-light-skin-tone", "merman-medium-dark-skin-tone", "merman-medium-light-skin-tone", "merman-medium-skin-tone", "me<PERSON><PERSON>", "merperson-dark-skin-tone", "merperson-light-skin-tone", "merperson-medium-dark-skin-tone", "merperson-medium-light-skin-tone", "merperson-medium-skin-tone", "metro", "microbe", "microphone", "microscope", "middle-finger", "middle-finger-dark-skin-tone", "middle-finger-light-skin-tone", "middle-finger-medium-dark-skin-tone", "middle-finger-medium-light-skin-tone", "middle-finger-medium-skin-tone", "military-helmet", "military-medal", "milky-way", "minibus", "minus", "mirror", "mirror-ball", "moai", "mobile-phone", "mobile-phone-off", "mobile-phone-with-arrow", "money-bag", "money-mouth-face", "money-with-wings", "monkey", "monkey-face", "monorail", "moon-cake", "moon-viewing-ceremony", "mosque", "mosquito", "motor-boat", "motor-scooter", "motorcycle", "motorized-wheelchair", "motorway", "mount-fuji", "mountain", "mountain-cableway", "mountain-railway", "mouse", "mouse-face", "mouse-trap", "mouth", "movie-camera", "mrs-claus", "mrs-claus-dark-skin-tone", "mrs-claus-light-skin-tone", "mrs-claus-medium-dark-skin-tone", "mrs-claus-medium-light-skin-tone", "mrs-claus-medium-skin-tone", "multiply", "mushroom", "musical-keyboard", "musical-note", "musical-notes", "musical-score", "muted-speaker", "mx-claus", "mx-claus-dark-skin-tone", "mx-claus-light-skin-tone", "mx-claus-medium-dark-skin-tone", "mx-claus-medium-light-skin-tone", "mx-claus-medium-skin-tone", "nail-polish", "nail-polish-dark-skin-tone", "nail-polish-light-skin-tone", "nail-polish-medium-dark-skin-tone", "nail-polish-medium-light-skin-tone", "nail-polish-medium-skin-tone", "name-badge", "national-park", "nauseated-face", "nazar-amulet", "necktie", "nerd-face", "nest-with-eggs", "nesting-dolls", "neutral-face", "new-button", "new-moon", "new-moon-face", "newspaper", "next-track-button", "ng-button", "night-with-stars", "nine-oclock", "nine-thirty", "ninja", "ninja-dark-skin-tone", "ninja-light-skin-tone", "ninja-medium-dark-skin-tone", "ninja-medium-light-skin-tone", "ninja-medium-skin-tone", "no-bicycles", "no-entry", "no-littering", "no-mobile-phones", "no-one-under-eighteen", "no-pedestrians", "no-smoking", "non-potable-water", "nose", "nose-dark-skin-tone", "nose-light-skin-tone", "nose-medium-dark-skin-tone", "nose-medium-light-skin-tone", "nose-medium-skin-tone", "notebook", "notebook-with-decorative-cover", "nut-and-bolt", "o-button-blood-type", "octopus", "oden", "office-building", "office-worker", "office-worker-dark-skin-tone", "office-worker-light-skin-tone", "office-worker-medium-dark-skin-tone", "office-worker-medium-light-skin-tone", "office-worker-medium-skin-tone", "ogre", "oil-drum", "ok-button", "ok-hand", "ok-hand-dark-skin-tone", "ok-hand-light-skin-tone", "ok-hand-medium-dark-skin-tone", "ok-hand-medium-light-skin-tone", "ok-hand-medium-skin-tone", "old-key", "old-man", "old-man-dark-skin-tone", "old-man-light-skin-tone", "old-man-medium-dark-skin-tone", "old-man-medium-light-skin-tone", "old-man-medium-skin-tone", "old-woman", "old-woman-dark-skin-tone", "old-woman-light-skin-tone", "old-woman-medium-dark-skin-tone", "old-woman-medium-light-skin-tone", "old-woman-medium-skin-tone", "older-adult", "older-adult-dark-skin-tone", "older-adult-light-skin-tone", "older-adult-medium-dark-skin-tone", "older-adult-medium-light-skin-tone", "older-adult-medium-skin-tone", "older-person", "older-person-dark-skin-tone", "older-person-light-skin-tone", "older-person-medium-dark-skin-tone", "older-person-medium-light-skin-tone", "older-person-medium-skin-tone", "olive", "om", "on-exclamation-arrow", "oncoming-automobile", "oncoming-bus", "oncoming-fist", "oncoming-fist-dark-skin-tone", "oncoming-fist-light-skin-tone", "oncoming-fist-medium-dark-skin-tone", "oncoming-fist-medium-light-skin-tone", "oncoming-fist-medium-skin-tone", "oncoming-police-car", "oncoming-taxi", "one-oclock", "one-piece-swimsuit", "one-thirty", "onion", "open-book", "open-file-folder", "open-hands", "open-hands-dark-skin-tone", "open-hands-light-skin-tone", "open-hands-medium-dark-skin-tone", "open-hands-medium-light-skin-tone", "open-hands-medium-skin-tone", "open-mailbox-with-lowered-flag", "open-mailbox-with-raised-flag", "ophi<PERSON>us", "optical-disk", "orange-book", "orange-circle", "orange-heart", "orange-square", "orangutan", "orthodox-cross", "otter", "outbox-tray", "owl", "ox", "oyster", "p-button", "package", "page-facing-up", "page-with-curl", "pager", "paintbrush", "palm-down-hand", "palm-down-hand-dark-skin-tone", "palm-down-hand-light-skin-tone", "palm-down-hand-medium-dark-skin-tone", "palm-down-hand-medium-light-skin-tone", "palm-down-hand-medium-skin-tone", "palm-tree", "palm-up-hand", "palm-up-hand-dark-skin-tone", "palm-up-hand-light-skin-tone", "palm-up-hand-medium-dark-skin-tone", "palm-up-hand-medium-light-skin-tone", "palm-up-hand-medium-skin-tone", "palms-up-together", "palms-up-together-dark-skin-tone", "palms-up-together-light-skin-tone", "palms-up-together-medium-dark-skin-tone", "palms-up-together-medium-light-skin-tone", "palms-up-together-medium-skin-tone", "pancakes", "panda", "paperclip", "parachute", "parrot", "part-alternation-mark", "party-popper", "partying-face", "passenger-ship", "passport-control", "pause-button", "paw-prints", "peace-symbol", "peach", "peacock", "peanuts", "pear", "pen", "pencil", "penguin", "pensive-face", "people-holding-hands", "people-holding-hands-dark-skin-tone", "people-holding-hands-dark-skin-tone-light-skin-tone", "people-holding-hands-dark-skin-tone-medium-dark-skin-tone", "people-holding-hands-dark-skin-tone-medium-light-skin-tone", "people-holding-hands-dark-skin-tone-medium-skin-tone", "people-holding-hands-light-skin-tone", "people-holding-hands-light-skin-tone-dark-skin-tone", "people-holding-hands-light-skin-tone-medium-dark-skin-tone", "people-holding-hands-light-skin-tone-medium-light-skin-tone", "people-holding-hands-light-skin-tone-medium-skin-tone", "people-holding-hands-medium-dark-skin-tone", "people-holding-hands-medium-dark-skin-tone-dark-skin-tone", "people-holding-hands-medium-dark-skin-tone-light-skin-tone", "people-holding-hands-medium-dark-skin-tone-medium-light-skin-tone", "people-holding-hands-medium-dark-skin-tone-medium-skin-tone", "people-holding-hands-medium-light-skin-tone", "people-holding-hands-medium-light-skin-tone-dark-skin-tone", "people-holding-hands-medium-light-skin-tone-light-skin-tone", "people-holding-hands-medium-light-skin-tone-medium-dark-skin-tone", "people-holding-hands-medium-light-skin-tone-medium-skin-tone", "people-holding-hands-medium-skin-tone", "people-holding-hands-medium-skin-tone-dark-skin-tone", "people-holding-hands-medium-skin-tone-light-skin-tone", "people-holding-hands-medium-skin-tone-medium-dark-skin-tone", "people-holding-hands-medium-skin-tone-medium-light-skin-tone", "people-hugging", "people-with-bunny-ears", "people-wrestling", "performing-arts", "persevering-face", "person", "person-bald", "person-beard", "person-biking", "person-biking-dark-skin-tone", "person-biking-light-skin-tone", "person-biking-medium-dark-skin-tone", "person-biking-medium-light-skin-tone", "person-biking-medium-skin-tone", "person-blond-hair", "person-bouncing-ball", "person-bouncing-ball-dark-skin-tone", "person-bouncing-ball-light-skin-tone", "person-bouncing-ball-medium-dark-skin-tone", "person-bouncing-ball-medium-light-skin-tone", "person-bouncing-ball-medium-skin-tone", "person-bowing", "person-bowing-dark-skin-tone", "person-bowing-light-skin-tone", "person-bowing-medium-dark-skin-tone", "person-bowing-medium-light-skin-tone", "person-bowing-medium-skin-tone", "person-cartwheeling", "person-cartwheeling-dark-skin-tone", "person-cartwheeling-light-skin-tone", "person-cartwheeling-medium-dark-skin-tone", "person-cartwheeling-medium-light-skin-tone", "person-cartwheeling-medium-skin-tone", "person-climbing", "person-climbing-dark-skin-tone", "person-climbing-light-skin-tone", "person-climbing-medium-dark-skin-tone", "person-climbing-medium-light-skin-tone", "person-climbing-medium-skin-tone", "person-curly-hair", "person-dark-skin-tone", "person-dark-skin-tone-bald", "person-dark-skin-tone-beard", "person-dark-skin-tone-blond-hair", "person-dark-skin-tone-curly-hair", "person-dark-skin-tone-red-hair", "person-dark-skin-tone-white-hair", "person-facepalming", "person-facepalming-dark-skin-tone", "person-facepalming-light-skin-tone", "person-facepalming-medium-dark-skin-tone", "person-facepalming-medium-light-skin-tone", "person-facepalming-medium-skin-tone", "person-feeding-baby", "person-feeding-baby-dark-skin-tone", "person-feeding-baby-light-skin-tone", "person-feeding-baby-medium-dark-skin-tone", "person-feeding-baby-medium-light-skin-tone", "person-feeding-baby-medium-skin-tone", "person-fencing", "person-frowning", "person-frowning-dark-skin-tone", "person-frowning-light-skin-tone", "person-frowning-medium-dark-skin-tone", "person-frowning-medium-light-skin-tone", "person-frowning-medium-skin-tone", "person-gesturing-no", "person-gesturing-no-dark-skin-tone", "person-gesturing-no-light-skin-tone", "person-gesturing-no-medium-dark-skin-tone", "person-gesturing-no-medium-light-skin-tone", "person-gesturing-no-medium-skin-tone", "person-gesturing-ok", "person-gesturing-ok-dark-skin-tone", "person-gesturing-ok-light-skin-tone", "person-gesturing-ok-medium-dark-skin-tone", "person-gesturing-ok-medium-light-skin-tone", "person-gesturing-ok-medium-skin-tone", "person-getting-haircut", "person-getting-haircut-dark-skin-tone", "person-getting-haircut-light-skin-tone", "person-getting-haircut-medium-dark-skin-tone", "person-getting-haircut-medium-light-skin-tone", "person-getting-haircut-medium-skin-tone", "person-getting-massage", "person-getting-massage-dark-skin-tone", "person-getting-massage-light-skin-tone", "person-getting-massage-medium-dark-skin-tone", "person-getting-massage-medium-light-skin-tone", "person-getting-massage-medium-skin-tone", "person-golfing", "person-golfing-dark-skin-tone", "person-golfing-light-skin-tone", "person-golfing-medium-dark-skin-tone", "person-golfing-medium-light-skin-tone", "person-golfing-medium-skin-tone", "person-in-bed", "person-in-bed-dark-skin-tone", "person-in-bed-light-skin-tone", "person-in-bed-medium-dark-skin-tone", "person-in-bed-medium-light-skin-tone", "person-in-bed-medium-skin-tone", "person-in-lotus-position", "person-in-lotus-position-dark-skin-tone", "person-in-lotus-position-light-skin-tone", "person-in-lotus-position-medium-dark-skin-tone", "person-in-lotus-position-medium-light-skin-tone", "person-in-lotus-position-medium-skin-tone", "person-in-manual-wheelchair", "person-in-manual-wheelchair-dark-skin-tone", "person-in-manual-wheelchair-light-skin-tone", "person-in-manual-wheelchair-medium-dark-skin-tone", "person-in-manual-wheelchair-medium-light-skin-tone", "person-in-manual-wheelchair-medium-skin-tone", "person-in-motorized-wheelchair", "person-in-motorized-wheelchair-dark-skin-tone", "person-in-motorized-wheelchair-light-skin-tone", "person-in-motorized-wheelchair-medium-dark-skin-tone", "person-in-motorized-wheelchair-medium-light-skin-tone", "person-in-motorized-wheelchair-medium-skin-tone", "person-in-steamy-room", "person-in-steamy-room-dark-skin-tone", "person-in-steamy-room-light-skin-tone", "person-in-steamy-room-medium-dark-skin-tone", "person-in-steamy-room-medium-light-skin-tone", "person-in-steamy-room-medium-skin-tone", "person-in-suit-levitating", "person-in-suit-levitating-dark-skin-tone", "person-in-suit-levitating-light-skin-tone", "person-in-suit-levitating-medium-dark-skin-tone", "person-in-suit-levitating-medium-light-skin-tone", "person-in-suit-levitating-medium-skin-tone", "person-in-tuxedo", "person-in-tuxedo-dark-skin-tone", "person-in-tuxedo-light-skin-tone", "person-in-tuxedo-medium-dark-skin-tone", "person-in-tuxedo-medium-light-skin-tone", "person-in-tuxedo-medium-skin-tone", "person-juggling", "person-juggling-dark-skin-tone", "person-juggling-light-skin-tone", "person-juggling-medium-dark-skin-tone", "person-juggling-medium-light-skin-tone", "person-juggling-medium-skin-tone", "person-kneeling", "person-kneeling-dark-skin-tone", "person-kneeling-light-skin-tone", "person-kneeling-medium-dark-skin-tone", "person-kneeling-medium-light-skin-tone", "person-kneeling-medium-skin-tone", "person-lifting-weights", "person-lifting-weights-dark-skin-tone", "person-lifting-weights-light-skin-tone", "person-lifting-weights-medium-dark-skin-tone", "person-lifting-weights-medium-light-skin-tone", "person-lifting-weights-medium-skin-tone", "person-light-skin-tone", "person-light-skin-tone-bald", "person-light-skin-tone-beard", "person-light-skin-tone-blond-hair", "person-light-skin-tone-curly-hair", "person-light-skin-tone-red-hair", "person-light-skin-tone-white-hair", "person-medium-dark-skin-tone", "person-medium-dark-skin-tone-bald", "person-medium-dark-skin-tone-beard", "person-medium-dark-skin-tone-blond-hair", "person-medium-dark-skin-tone-curly-hair", "person-medium-dark-skin-tone-red-hair", "person-medium-dark-skin-tone-white-hair", "person-medium-light-skin-tone", "person-medium-light-skin-tone-bald", "person-medium-light-skin-tone-beard", "person-medium-light-skin-tone-blond-hair", "person-medium-light-skin-tone-curly-hair", "person-medium-light-skin-tone-red-hair", "person-medium-light-skin-tone-white-hair", "person-medium-skin-tone", "person-medium-skin-tone-bald", "person-medium-skin-tone-beard", "person-medium-skin-tone-blond-hair", "person-medium-skin-tone-curly-hair", "person-medium-skin-tone-red-hair", "person-medium-skin-tone-white-hair", "person-mountain-biking", "person-mountain-biking-dark-skin-tone", "person-mountain-biking-light-skin-tone", "person-mountain-biking-medium-dark-skin-tone", "person-mountain-biking-medium-light-skin-tone", "person-mountain-biking-medium-skin-tone", "person-playing-handball", "person-playing-handball-dark-skin-tone", "person-playing-handball-light-skin-tone", "person-playing-handball-medium-dark-skin-tone", "person-playing-handball-medium-light-skin-tone", "person-playing-handball-medium-skin-tone", "person-playing-water-polo", "person-playing-water-polo-dark-skin-tone", "person-playing-water-polo-light-skin-tone", "person-playing-water-polo-medium-dark-skin-tone", "person-playing-water-polo-medium-light-skin-tone", "person-playing-water-polo-medium-skin-tone", "person-pouting", "person-pouting-dark-skin-tone", "person-pouting-light-skin-tone", "person-pouting-medium-dark-skin-tone", "person-pouting-medium-light-skin-tone", "person-pouting-medium-skin-tone", "person-raising-hand", "person-raising-hand-dark-skin-tone", "person-raising-hand-light-skin-tone", "person-raising-hand-medium-dark-skin-tone", "person-raising-hand-medium-light-skin-tone", "person-raising-hand-medium-skin-tone", "person-red-hair", "person-rowing-boat", "person-rowing-boat-dark-skin-tone", "person-rowing-boat-light-skin-tone", "person-rowing-boat-medium-dark-skin-tone", "person-rowing-boat-medium-light-skin-tone", "person-rowing-boat-medium-skin-tone", "person-running", "person-running-dark-skin-tone", "person-running-light-skin-tone", "person-running-medium-dark-skin-tone", "person-running-medium-light-skin-tone", "person-running-medium-skin-tone", "person-shrugging", "person-shrugging-dark-skin-tone", "person-shrugging-light-skin-tone", "person-shrugging-medium-dark-skin-tone", "person-shrugging-medium-light-skin-tone", "person-shrugging-medium-skin-tone", "person-standing", "person-standing-dark-skin-tone", "person-standing-light-skin-tone", "person-standing-medium-dark-skin-tone", "person-standing-medium-light-skin-tone", "person-standing-medium-skin-tone", "person-surfing", "person-surfing-dark-skin-tone", "person-surfing-light-skin-tone", "person-surfing-medium-dark-skin-tone", "person-surfing-medium-light-skin-tone", "person-surfing-medium-skin-tone", "person-swimming", "person-swimming-dark-skin-tone", "person-swimming-light-skin-tone", "person-swimming-medium-dark-skin-tone", "person-swimming-medium-light-skin-tone", "person-swimming-medium-skin-tone", "person-taking-bath", "person-taking-bath-dark-skin-tone", "person-taking-bath-light-skin-tone", "person-taking-bath-medium-dark-skin-tone", "person-taking-bath-medium-light-skin-tone", "person-taking-bath-medium-skin-tone", "person-tipping-hand", "person-tipping-hand-dark-skin-tone", "person-tipping-hand-light-skin-tone", "person-tipping-hand-medium-dark-skin-tone", "person-tipping-hand-medium-light-skin-tone", "person-tipping-hand-medium-skin-tone", "person-walking", "person-walking-dark-skin-tone", "person-walking-light-skin-tone", "person-walking-medium-dark-skin-tone", "person-walking-medium-light-skin-tone", "person-walking-medium-skin-tone", "person-wearing-turban", "person-wearing-turban-dark-skin-tone", "person-wearing-turban-light-skin-tone", "person-wearing-turban-medium-dark-skin-tone", "person-wearing-turban-medium-light-skin-tone", "person-wearing-turban-medium-skin-tone", "person-white-hair", "person-with-crown", "person-with-crown-dark-skin-tone", "person-with-crown-light-skin-tone", "person-with-crown-medium-dark-skin-tone", "person-with-crown-medium-light-skin-tone", "person-with-crown-medium-skin-tone", "person-with-skullcap", "person-with-skullcap-dark-skin-tone", "person-with-skullcap-light-skin-tone", "person-with-skullcap-medium-dark-skin-tone", "person-with-skullcap-medium-light-skin-tone", "person-with-skullcap-medium-skin-tone", "person-with-veil", "person-with-veil-dark-skin-tone", "person-with-veil-light-skin-tone", "person-with-veil-medium-dark-skin-tone", "person-with-veil-medium-light-skin-tone", "person-with-veil-medium-skin-tone", "person-with-white-cane", "person-with-white-cane-dark-skin-tone", "person-with-white-cane-light-skin-tone", "person-with-white-cane-medium-dark-skin-tone", "person-with-white-cane-medium-light-skin-tone", "person-with-white-cane-medium-skin-tone", "petri-dish", "pick", "pickup-truck", "pie", "pig", "pig-face", "pig-nose", "pile-of-poo", "pill", "pilot", "pilot-dark-skin-tone", "pilot-light-skin-tone", "pilot-medium-dark-skin-tone", "pilot-medium-light-skin-tone", "pilot-medium-skin-tone", "pinata", "pinched-fingers", "pinched-fingers-dark-skin-tone", "pinched-fingers-light-skin-tone", "pinched-fingers-medium-dark-skin-tone", "pinched-fingers-medium-light-skin-tone", "pinched-fingers-medium-skin-tone", "pinching-hand", "pinching-hand-dark-skin-tone", "pinching-hand-light-skin-tone", "pinching-hand-medium-dark-skin-tone", "pinching-hand-medium-light-skin-tone", "pinching-hand-medium-skin-tone", "pine-decoration", "pineapple", "ping-pong", "pirate-flag", "pisces", "pizza", "placard", "place-of-worship", "play-button", "play-or-pause-button", "playground-slide", "pleading-face", "plunger", "plus", "polar-bear", "police-car", "police-car-light", "police-officer", "police-officer-dark-skin-tone", "police-officer-light-skin-tone", "police-officer-medium-dark-skin-tone", "police-officer-medium-light-skin-tone", "police-officer-medium-skin-tone", "poodle", "pool-8-ball", "popcorn", "post-office", "postal-horn", "postbox", "pot-of-food", "potable-water", "potato", "potted-plant", "poultry-leg", "pound-banknote", "pouring-liquid", "pouting-cat", "prayer-beads", "pregnant-man", "pregnant-man-dark-skin-tone", "pregnant-man-light-skin-tone", "pregnant-man-medium-dark-skin-tone", "pregnant-man-medium-light-skin-tone", "pregnant-man-medium-skin-tone", "pregnant-person", "pregnant-person-dark-skin-tone", "pregnant-person-light-skin-tone", "pregnant-person-medium-dark-skin-tone", "pregnant-person-medium-light-skin-tone", "pregnant-person-medium-skin-tone", "pregnant-woman", "pregnant-woman-dark-skin-tone", "pregnant-woman-light-skin-tone", "pregnant-woman-medium-dark-skin-tone", "pregnant-woman-medium-light-skin-tone", "pregnant-woman-medium-skin-tone", "pretzel", "prince", "prince-dark-skin-tone", "prince-light-skin-tone", "prince-medium-dark-skin-tone", "prince-medium-light-skin-tone", "prince-medium-skin-tone", "princess", "princess-dark-skin-tone", "princess-light-skin-tone", "princess-medium-dark-skin-tone", "princess-medium-light-skin-tone", "princess-medium-skin-tone", "printer", "prohibited", "purple-circle", "purple-heart", "purple-square", "purse", "pushpin", "puzzle-piece", "rabbit", "rabbit-face", "raccoon", "racing-car", "radio", "radio-button", "radioactive", "railway-car", "railway-track", "rainbow", "rainbow-flag", "raised-back-of-hand", "raised-back-of-hand-dark-skin-tone", "raised-back-of-hand-light-skin-tone", "raised-back-of-hand-medium-dark-skin-tone", "raised-back-of-hand-medium-light-skin-tone", "raised-back-of-hand-medium-skin-tone", "raised-fist", "raised-fist-dark-skin-tone", "raised-fist-light-skin-tone", "raised-fist-medium-dark-skin-tone", "raised-fist-medium-light-skin-tone", "raised-fist-medium-skin-tone", "raised-hand", "raised-hand-dark-skin-tone", "raised-hand-light-skin-tone", "raised-hand-medium-dark-skin-tone", "raised-hand-medium-light-skin-tone", "raised-hand-medium-skin-tone", "raising-hands", "raising-hands-dark-skin-tone", "raising-hands-light-skin-tone", "raising-hands-medium-dark-skin-tone", "raising-hands-medium-light-skin-tone", "raising-hands-medium-skin-tone", "ram", "rat", "razor", "receipt", "record-button", "recycling-symbol", "red-apple", "red-circle", "red-envelope", "red-exclamation-mark", "red-haired", "red-heart", "red-paper-lantern", "red-question-mark", "red-square", "red-triangle-pointed-down", "red-triangle-pointed-up", "registered", "relieved-face", "reminder-ribbon", "repeat-button", "repeat-single-button", "rescue-workers-helmet", "restroom", "reverse-button", "revolving-hearts", "rhinoceros", "ribbon", "rice-ball", "rice-cracker", "right-anger-bubble", "right-arrow", "right-arrow-curving-down", "right-arrow-curving-left", "right-arrow-curving-up", "right-facing-fist", "right-facing-fist-dark-skin-tone", "right-facing-fist-light-skin-tone", "right-facing-fist-medium-dark-skin-tone", "right-facing-fist-medium-light-skin-tone", "right-facing-fist-medium-skin-tone", "rightwards-hand", "rightwards-hand-dark-skin-tone", "rightwards-hand-light-skin-tone", "rightwards-hand-medium-dark-skin-tone", "rightwards-hand-medium-light-skin-tone", "rightwards-hand-medium-skin-tone", "ring", "ring-buoy", "ringed-planet", "roasted-sweet-potato", "robot", "rock", "rocket", "roll-of-paper", "rolled-up-newspaper", "roller-coaster", "roller-skate", "rolling-on-the-floor-laughing", "rooster", "rose", "rosette", "round-pushpin", "rugby-football", "running-shirt", "running-shoe", "sad-but-relieved-face", "safety-pin", "safety-vest", "sagittarius", "sailboat", "sake", "salt", "saluting-face", "sandwich", "santa-claus", "santa-claus-dark-skin-tone", "santa-claus-light-skin-tone", "santa-claus-medium-dark-skin-tone", "santa-claus-medium-light-skin-tone", "santa-claus-medium-skin-tone", "sari", "satellite", "satellite-antenna", "sauropod", "saxophone", "scarf", "school", "scientist", "scientist-dark-skin-tone", "scientist-light-skin-tone", "scientist-medium-dark-skin-tone", "scientist-medium-light-skin-tone", "scientist-medium-skin-tone", "scissors", "scorpio", "scorpion", "screwdriver", "scroll", "seal", "seat", "see-no-evil-monkey", "seedling", "selfie", "selfie-dark-skin-tone", "selfie-light-skin-tone", "selfie-medium-dark-skin-tone", "selfie-medium-light-skin-tone", "selfie-medium-skin-tone", "service-dog", "seven-oclock", "seven-thirty", "sewing-needle", "shallow-pan-of-food", "shamrock", "shark", "shaved-ice", "sheaf-of-rice", "shibuya-109-department-store", "shield", "shinto-shrine", "ship", "shooting-star", "shopping-bags", "shopping-cart", "shortcake", "shorts", "shower", "shrimp", "shuffle-tracks-button", "shushing-face", "sign-of-the-horns", "sign-of-the-horns-dark-skin-tone", "sign-of-the-horns-light-skin-tone", "sign-of-the-horns-medium-dark-skin-tone", "sign-of-the-horns-medium-light-skin-tone", "sign-of-the-horns-medium-skin-tone", "singer", "singer-dark-skin-tone", "singer-light-skin-tone", "singer-medium-dark-skin-tone", "singer-medium-light-skin-tone", "singer-medium-skin-tone", "six-oclock", "six-thirty", "skateboard", "skier", "skier-dark-skin-tone", "skier-light-skin-tone", "skier-medium-dark-skin-tone", "skier-medium-light-skin-tone", "skier-medium-skin-tone", "skis", "skull", "skull-and-crossbones", "skunk", "sled", "sleeping-face", "sleepy-face", "slightly-frowning-face", "slightly-smiling-face", "slot-machine", "sloth", "small-airplane", "small-blue-diamond", "small-orange-diamond", "smiling-cat-with-heart-eyes", "smiling-face", "smiling-face-with-halo", "smiling-face-with-heart-eyes", "smiling-face-with-hearts", "smiling-face-with-horns", "smiling-face-with-open-hands", "smiling-face-with-smiling-eyes", "smiling-face-with-sunglasses", "smiling-face-with-tear", "smirking-face", "snail", "snake", "sneezing-face", "snow-capped-mountain", "snowboarder", "snowboarder-dark-skin-tone", "snowboarder-light-skin-tone", "snowboarder-medium-dark-skin-tone", "snowboarder-medium-light-skin-tone", "snowboarder-medium-skin-tone", "snowflake", "snowman", "snowman-without-snow", "soap", "soccer-ball", "socks", "soft-ice-cream", "softball", "soon-arrow", "sos-button", "spade-suit", "spaghetti", "sparkle", "sparkler", "sparkles", "sparkling-heart", "speak-no-evil-monkey", "speaker-high-volume", "speaker-low-volume", "speaker-medium-volume", "speaking-head", "speech-balloon", "speedboat", "spider", "spider-web", "spiral-calendar", "spiral-notepad", "spiral-shell", "sponge", "spoon", "sport-utility-vehicle", "sports-medal", "spouting-whale", "squid", "squinting-face-with-tongue", "stadium", "star", "star-and-crescent", "star-of-david", "star-struck", "station", "statue-of-liberty", "steaming-bowl", "stethoscope", "stop-button", "stop-sign", "stopwatch", "straight-ruler", "strawberry", "student", "student-dark-skin-tone", "student-light-skin-tone", "student-medium-dark-skin-tone", "student-medium-light-skin-tone", "student-medium-skin-tone", "studio-microphone", "stuffed-flatbread", "sun", "sun-behind-cloud", "sun-behind-large-cloud", "sun-behind-rain-cloud", "sun-behind-small-cloud", "sun-with-face", "sunflower", "sunglasses", "sunrise", "sunrise-over-mountains", "sunset", "superhero", "superhero-dark-skin-tone", "superhero-light-skin-tone", "superhero-medium-dark-skin-tone", "superhero-medium-light-skin-tone", "superhero-medium-skin-tone", "supervillain", "supervillain-dark-skin-tone", "supervillain-light-skin-tone", "supervillain-medium-dark-skin-tone", "supervillain-medium-light-skin-tone", "supervillain-medium-skin-tone", "sushi", "suspension-railway", "swan", "sweat-droplets", "synagogue", "syringe", "t-rex", "t-shirt", "taco", "takeout-box", "tamale", "tanabata-tree", "tangerine", "taurus", "taxi", "teacher", "teacher-dark-skin-tone", "teacher-light-skin-tone", "teacher-medium-dark-skin-tone", "teacher-medium-light-skin-tone", "teacher-medium-skin-tone", "teacup-without-handle", "teapot", "tear-off-calendar", "technologist", "technologist-dark-skin-tone", "technologist-light-skin-tone", "technologist-medium-dark-skin-tone", "technologist-medium-light-skin-tone", "technologist-medium-skin-tone", "teddy-bear", "telephone", "telephone-receiver", "telescope", "television", "ten-oclock", "ten-thirty", "tennis", "tent", "test-tube", "thermometer", "thinking-face", "thong-sandal", "thought-balloon", "thread", "three-oclock", "three-thirty", "thumbs-down", "thumbs-down-dark-skin-tone", "thumbs-down-light-skin-tone", "thumbs-down-medium-dark-skin-tone", "thumbs-down-medium-light-skin-tone", "thumbs-down-medium-skin-tone", "thumbs-up", "thumbs-up-dark-skin-tone", "thumbs-up-light-skin-tone", "thumbs-up-medium-dark-skin-tone", "thumbs-up-medium-light-skin-tone", "thumbs-up-medium-skin-tone", "ticket", "tiger", "tiger-face", "timer-clock", "tired-face", "toilet", "tokyo-tower", "tomato", "tongue", "toolbox", "tooth", "toothbrush", "top-arrow", "top-hat", "tornado", "trackball", "tractor", "trade-mark", "train", "tram", "tram-car", "transgender-flag", "transgender-symbol", "triangular-flag", "triangular-ruler", "trident-emblem", "troll", "trolleybus", "trophy", "tropical-drink", "tropical-fish", "trumpet", "tulip", "tumbler-glass", "turkey", "turtle", "twelve-oclock", "twelve-thirty", "two-hearts", "two-hump-camel", "two-men-holding-hands", "two-oclock", "two-thirty", "two-women-holding-hands", "umbrella", "umbrella-on-ground", "umbrella-with-rain-drops", "unamused-face", "unicorn", "unlocked", "up-arrow", "up-down-arrow", "up-exclamation-button", "up-left-arrow", "up-right-arrow", "upside-down-face", "upwards-button", "vampire", "vampire-dark-skin-tone", "vampire-light-skin-tone", "vampire-medium-dark-skin-tone", "vampire-medium-light-skin-tone", "vampire-medium-skin-tone", "vertical-traffic-light", "vibration-mode", "victory-hand", "victory-hand-dark-skin-tone", "victory-hand-light-skin-tone", "victory-hand-medium-dark-skin-tone", "victory-hand-medium-light-skin-tone", "victory-hand-medium-skin-tone", "video-camera", "video-game", "videocassette", "violin", "virgo", "volcano", "volleyball", "vs-button", "vulcan-salute", "vulcan-salute-dark-skin-tone", "vulcan-salute-light-skin-tone", "vulcan-salute-medium-dark-skin-tone", "vulcan-salute-medium-light-skin-tone", "vulcan-salute-medium-skin-tone", "waffle", "waning-crescent-moon", "waning-gibbous-moon", "warning", "wastebasket", "watch", "water-buffalo", "water-closet", "water-pistol", "water-wave", "watermelon", "waving-hand", "waving-hand-dark-skin-tone", "waving-hand-light-skin-tone", "waving-hand-medium-dark-skin-tone", "waving-hand-medium-light-skin-tone", "waving-hand-medium-skin-tone", "wavy-dash", "waxing-crescent-moon", "waxing-gibbous-moon", "weary-cat", "weary-face", "wedding", "whale", "wheel", "wheel-of-dharma", "wheelchair-symbol", "white-cane", "white-circle", "white-exclamation-mark", "white-flag", "white-flower", "white-haired", "white-heart", "white-large-square", "white-medium-small-square", "white-medium-square", "white-question-mark", "white-small-square", "white-square-button", "wilted-flower", "wind-chime", "wind-face", "window", "wine-glass", "winking-face", "winking-face-with-tongue", "wolf", "woman", "woman-and-man-holding-hands", "woman-and-man-holding-hands-dark-skin-tone", "woman-and-man-holding-hands-dark-skin-tone-light-skin-tone", "woman-and-man-holding-hands-dark-skin-tone-medium-dark-skin-tone", "woman-and-man-holding-hands-dark-skin-tone-medium-light-skin-tone", "woman-and-man-holding-hands-dark-skin-tone-medium-skin-tone", "woman-and-man-holding-hands-light-skin-tone", "woman-and-man-holding-hands-light-skin-tone-dark-skin-tone", "woman-and-man-holding-hands-light-skin-tone-medium-dark-skin-tone", "woman-and-man-holding-hands-light-skin-tone-medium-light-skin-tone", "woman-and-man-holding-hands-light-skin-tone-medium-skin-tone", "woman-and-man-holding-hands-medium-dark-skin-tone", "woman-and-man-holding-hands-medium-dark-skin-tone-dark-skin-tone", "woman-and-man-holding-hands-medium-dark-skin-tone-light-skin-tone", "woman-and-man-holding-hands-medium-dark-skin-tone-medium-light-skin-tone", "woman-and-man-holding-hands-medium-dark-skin-tone-medium-skin-tone", "woman-and-man-holding-hands-medium-light-skin-tone", "woman-and-man-holding-hands-medium-light-skin-tone-dark-skin-tone", "woman-and-man-holding-hands-medium-light-skin-tone-light-skin-tone", "woman-and-man-holding-hands-medium-light-skin-tone-medium-dark-skin-tone", "woman-and-man-holding-hands-medium-light-skin-tone-medium-skin-tone", "woman-and-man-holding-hands-medium-skin-tone", "woman-and-man-holding-hands-medium-skin-tone-dark-skin-tone", "woman-and-man-holding-hands-medium-skin-tone-light-skin-tone", "woman-and-man-holding-hands-medium-skin-tone-medium-dark-skin-tone", "woman-and-man-holding-hands-medium-skin-tone-medium-light-skin-tone", "woman-artist", "woman-artist-dark-skin-tone", "woman-artist-light-skin-tone", "woman-artist-medium-dark-skin-tone", "woman-artist-medium-light-skin-tone", "woman-artist-medium-skin-tone", "woman-astronaut", "woman-astronaut-dark-skin-tone", "woman-astronaut-light-skin-tone", "woman-astronaut-medium-dark-skin-tone", "woman-astronaut-medium-light-skin-tone", "woman-astronaut-medium-skin-tone", "woman-bald", "woman-beard", "woman-biking", "woman-biking-dark-skin-tone", "woman-biking-light-skin-tone", "woman-biking-medium-dark-skin-tone", "woman-biking-medium-light-skin-tone", "woman-biking-medium-skin-tone", "woman-blond-hair", "woman-bouncing-ball", "woman-bouncing-ball-dark-skin-tone", "woman-bouncing-ball-light-skin-tone", "woman-bouncing-ball-medium-dark-skin-tone", "woman-bouncing-ball-medium-light-skin-tone", "woman-bouncing-ball-medium-skin-tone", "woman-bowing", "woman-bowing-dark-skin-tone", "woman-bowing-light-skin-tone", "woman-bowing-medium-dark-skin-tone", "woman-bowing-medium-light-skin-tone", "woman-bowing-medium-skin-tone", "woman-cartwheeling", "woman-cartwheeling-dark-skin-tone", "woman-cartwheeling-light-skin-tone", "woman-cartwheeling-medium-dark-skin-tone", "woman-cartwheeling-medium-light-skin-tone", "woman-cartwheeling-medium-skin-tone", "woman-climbing", "woman-climbing-dark-skin-tone", "woman-climbing-light-skin-tone", "woman-climbing-medium-dark-skin-tone", "woman-climbing-medium-light-skin-tone", "woman-climbing-medium-skin-tone", "woman-construction-worker", "woman-construction-worker-dark-skin-tone", "woman-construction-worker-light-skin-tone", "woman-construction-worker-medium-dark-skin-tone", "woman-construction-worker-medium-light-skin-tone", "woman-construction-worker-medium-skin-tone", "woman-cook", "woman-cook-dark-skin-tone", "woman-cook-light-skin-tone", "woman-cook-medium-dark-skin-tone", "woman-cook-medium-light-skin-tone", "woman-cook-medium-skin-tone", "woman-curly-hair", "woman-dancing", "woman-dancing-dark-skin-tone", "woman-dancing-light-skin-tone", "woman-dancing-medium-dark-skin-tone", "woman-dancing-medium-light-skin-tone", "woman-dancing-medium-skin-tone", "woman-dark-skin-tone", "woman-dark-skin-tone-bald", "woman-dark-skin-tone-beard", "woman-dark-skin-tone-blond-hair", "woman-dark-skin-tone-curly-hair", "woman-dark-skin-tone-red-hair", "woman-dark-skin-tone-white-hair", "woman-detective", "woman-detective-dark-skin-tone", "woman-detective-light-skin-tone", "woman-detective-medium-dark-skin-tone", "woman-detective-medium-light-skin-tone", "woman-detective-medium-skin-tone", "woman-elf", "woman-elf-dark-skin-tone", "woman-elf-light-skin-tone", "woman-elf-medium-dark-skin-tone", "woman-elf-medium-light-skin-tone", "woman-elf-medium-skin-tone", "woman-facepalming", "woman-facepalming-dark-skin-tone", "woman-facepalming-light-skin-tone", "woman-facepalming-medium-dark-skin-tone", "woman-facepalming-medium-light-skin-tone", "woman-facepalming-medium-skin-tone", "woman-factory-worker", "woman-factory-worker-dark-skin-tone", "woman-factory-worker-light-skin-tone", "woman-factory-worker-medium-dark-skin-tone", "woman-factory-worker-medium-light-skin-tone", "woman-factory-worker-medium-skin-tone", "woman-fairy", "woman-fairy-dark-skin-tone", "woman-fairy-light-skin-tone", "woman-fairy-medium-dark-skin-tone", "woman-fairy-medium-light-skin-tone", "woman-fairy-medium-skin-tone", "woman-farmer", "woman-farmer-dark-skin-tone", "woman-farmer-light-skin-tone", "woman-farmer-medium-dark-skin-tone", "woman-farmer-medium-light-skin-tone", "woman-farmer-medium-skin-tone", "woman-feeding-baby", "woman-feeding-baby-dark-skin-tone", "woman-feeding-baby-light-skin-tone", "woman-feeding-baby-medium-dark-skin-tone", "woman-feeding-baby-medium-light-skin-tone", "woman-feeding-baby-medium-skin-tone", "woman-firefighter", "woman-firefighter-dark-skin-tone", "woman-firefighter-light-skin-tone", "woman-firefighter-medium-dark-skin-tone", "woman-firefighter-medium-light-skin-tone", "woman-firefighter-medium-skin-tone", "woman-frowning", "woman-frowning-dark-skin-tone", "woman-frowning-light-skin-tone", "woman-frowning-medium-dark-skin-tone", "woman-frowning-medium-light-skin-tone", "woman-frowning-medium-skin-tone", "woman-genie", "woman-gesturing-no", "woman-gesturing-no-dark-skin-tone", "woman-gesturing-no-light-skin-tone", "woman-gesturing-no-medium-dark-skin-tone", "woman-gesturing-no-medium-light-skin-tone", "woman-gesturing-no-medium-skin-tone", "woman-gesturing-ok", "woman-gesturing-ok-dark-skin-tone", "woman-gesturing-ok-light-skin-tone", "woman-gesturing-ok-medium-dark-skin-tone", "woman-gesturing-ok-medium-light-skin-tone", "woman-gesturing-ok-medium-skin-tone", "woman-getting-haircut", "woman-getting-haircut-dark-skin-tone", "woman-getting-haircut-light-skin-tone", "woman-getting-haircut-medium-dark-skin-tone", "woman-getting-haircut-medium-light-skin-tone", "woman-getting-haircut-medium-skin-tone", "woman-getting-massage", "woman-getting-massage-dark-skin-tone", "woman-getting-massage-light-skin-tone", "woman-getting-massage-medium-dark-skin-tone", "woman-getting-massage-medium-light-skin-tone", "woman-getting-massage-medium-skin-tone", "woman-golfing", "woman-golfing-dark-skin-tone", "woman-golfing-light-skin-tone", "woman-golfing-medium-dark-skin-tone", "woman-golfing-medium-light-skin-tone", "woman-golfing-medium-skin-tone", "woman-guard", "woman-guard-dark-skin-tone", "woman-guard-light-skin-tone", "woman-guard-medium-dark-skin-tone", "woman-guard-medium-light-skin-tone", "woman-guard-medium-skin-tone", "woman-health-worker", "woman-health-worker-dark-skin-tone", "woman-health-worker-light-skin-tone", "woman-health-worker-medium-dark-skin-tone", "woman-health-worker-medium-light-skin-tone", "woman-health-worker-medium-skin-tone", "woman-in-lotus-position", "woman-in-lotus-position-dark-skin-tone", "woman-in-lotus-position-light-skin-tone", "woman-in-lotus-position-medium-dark-skin-tone", "woman-in-lotus-position-medium-light-skin-tone", "woman-in-lotus-position-medium-skin-tone", "woman-in-manual-wheelchair", "woman-in-manual-wheelchair-dark-skin-tone", "woman-in-manual-wheelchair-light-skin-tone", "woman-in-manual-wheelchair-medium-dark-skin-tone", "woman-in-manual-wheelchair-medium-light-skin-tone", "woman-in-manual-wheelchair-medium-skin-tone", "woman-in-motorized-wheelchair", "woman-in-motorized-wheelchair-dark-skin-tone", "woman-in-motorized-wheelchair-light-skin-tone", "woman-in-motorized-wheelchair-medium-dark-skin-tone", "woman-in-motorized-wheelchair-medium-light-skin-tone", "woman-in-motorized-wheelchair-medium-skin-tone", "woman-in-steamy-room", "woman-in-steamy-room-dark-skin-tone", "woman-in-steamy-room-light-skin-tone", "woman-in-steamy-room-medium-dark-skin-tone", "woman-in-steamy-room-medium-light-skin-tone", "woman-in-steamy-room-medium-skin-tone", "woman-in-suit-levitating", "woman-in-suit-levitating-dark-skin-tone", "woman-in-suit-levitating-light-skin-tone", "woman-in-suit-levitating-medium-dark-skin-tone", "woman-in-suit-levitating-medium-light-skin-tone", "woman-in-suit-levitating-medium-skin-tone", "woman-in-tuxedo", "woman-in-tuxedo-dark-skin-tone", "woman-in-tuxedo-light-skin-tone", "woman-in-tuxedo-medium-dark-skin-tone", "woman-in-tuxedo-medium-light-skin-tone", "woman-in-tuxedo-medium-skin-tone", "woman-judge", "woman-judge-dark-skin-tone", "woman-judge-light-skin-tone", "woman-judge-medium-dark-skin-tone", "woman-judge-medium-light-skin-tone", "woman-judge-medium-skin-tone", "woman-juggling", "woman-juggling-dark-skin-tone", "woman-juggling-light-skin-tone", "woman-juggling-medium-dark-skin-tone", "woman-juggling-medium-light-skin-tone", "woman-juggling-medium-skin-tone", "woman-kneeling", "woman-kneeling-dark-skin-tone", "woman-kneeling-light-skin-tone", "woman-kneeling-medium-dark-skin-tone", "woman-kneeling-medium-light-skin-tone", "woman-kneeling-medium-skin-tone", "woman-lifting-weights", "woman-lifting-weights-dark-skin-tone", "woman-lifting-weights-light-skin-tone", "woman-lifting-weights-medium-dark-skin-tone", "woman-lifting-weights-medium-light-skin-tone", "woman-lifting-weights-medium-skin-tone", "woman-light-skin-tone", "woman-light-skin-tone-bald", "woman-light-skin-tone-beard", "woman-light-skin-tone-blond-hair", "woman-light-skin-tone-curly-hair", "woman-light-skin-tone-red-hair", "woman-light-skin-tone-white-hair", "woman-mage", "woman-mage-dark-skin-tone", "woman-mage-light-skin-tone", "woman-mage-medium-dark-skin-tone", "woman-mage-medium-light-skin-tone", "woman-mage-medium-skin-tone", "woman-mechanic", "woman-mechanic-dark-skin-tone", "woman-mechanic-light-skin-tone", "woman-mechanic-medium-dark-skin-tone", "woman-mechanic-medium-light-skin-tone", "woman-mechanic-medium-skin-tone", "woman-medium-dark-skin-tone", "woman-medium-dark-skin-tone-bald", "woman-medium-dark-skin-tone-beard", "woman-medium-dark-skin-tone-blond-hair", "woman-medium-dark-skin-tone-curly-hair", "woman-medium-dark-skin-tone-red-hair", "woman-medium-dark-skin-tone-white-hair", "woman-medium-light-skin-tone", "woman-medium-light-skin-tone-bald", "woman-medium-light-skin-tone-beard", "woman-medium-light-skin-tone-blond-hair", "woman-medium-light-skin-tone-curly-hair", "woman-medium-light-skin-tone-red-hair", "woman-medium-light-skin-tone-white-hair", "woman-medium-skin-tone", "woman-medium-skin-tone-bald", "woman-medium-skin-tone-beard", "woman-medium-skin-tone-blond-hair", "woman-medium-skin-tone-curly-hair", "woman-medium-skin-tone-red-hair", "woman-medium-skin-tone-white-hair", "woman-mountain-biking", "woman-mountain-biking-dark-skin-tone", "woman-mountain-biking-light-skin-tone", "woman-mountain-biking-medium-dark-skin-tone", "woman-mountain-biking-medium-light-skin-tone", "woman-mountain-biking-medium-skin-tone", "woman-office-worker", "woman-office-worker-dark-skin-tone", "woman-office-worker-light-skin-tone", "woman-office-worker-medium-dark-skin-tone", "woman-office-worker-medium-light-skin-tone", "woman-office-worker-medium-skin-tone", "woman-pilot", "woman-pilot-dark-skin-tone", "woman-pilot-light-skin-tone", "woman-pilot-medium-dark-skin-tone", "woman-pilot-medium-light-skin-tone", "woman-pilot-medium-skin-tone", "woman-playing-handball", "woman-playing-handball-dark-skin-tone", "woman-playing-handball-light-skin-tone", "woman-playing-handball-medium-dark-skin-tone", "woman-playing-handball-medium-light-skin-tone", "woman-playing-handball-medium-skin-tone", "woman-playing-water-polo", "woman-playing-water-polo-dark-skin-tone", "woman-playing-water-polo-light-skin-tone", "woman-playing-water-polo-medium-dark-skin-tone", "woman-playing-water-polo-medium-light-skin-tone", "woman-playing-water-polo-medium-skin-tone", "woman-police-officer", "woman-police-officer-dark-skin-tone", "woman-police-officer-light-skin-tone", "woman-police-officer-medium-dark-skin-tone", "woman-police-officer-medium-light-skin-tone", "woman-police-officer-medium-skin-tone", "woman-pouting", "woman-pouting-dark-skin-tone", "woman-pouting-light-skin-tone", "woman-pouting-medium-dark-skin-tone", "woman-pouting-medium-light-skin-tone", "woman-pouting-medium-skin-tone", "woman-raising-hand", "woman-raising-hand-dark-skin-tone", "woman-raising-hand-light-skin-tone", "woman-raising-hand-medium-dark-skin-tone", "woman-raising-hand-medium-light-skin-tone", "woman-raising-hand-medium-skin-tone", "woman-red-hair", "woman-rowing-boat", "woman-rowing-boat-dark-skin-tone", "woman-rowing-boat-light-skin-tone", "woman-rowing-boat-medium-dark-skin-tone", "woman-rowing-boat-medium-light-skin-tone", "woman-rowing-boat-medium-skin-tone", "woman-running", "woman-running-dark-skin-tone", "woman-running-light-skin-tone", "woman-running-medium-dark-skin-tone", "woman-running-medium-light-skin-tone", "woman-running-medium-skin-tone", "woman-scientist", "woman-scientist-dark-skin-tone", "woman-scientist-light-skin-tone", "woman-scientist-medium-dark-skin-tone", "woman-scientist-medium-light-skin-tone", "woman-scientist-medium-skin-tone", "woman-shrugging", "woman-shrugging-dark-skin-tone", "woman-shrugging-light-skin-tone", "woman-shrugging-medium-dark-skin-tone", "woman-shrugging-medium-light-skin-tone", "woman-shrugging-medium-skin-tone", "woman-singer", "woman-singer-dark-skin-tone", "woman-singer-light-skin-tone", "woman-singer-medium-dark-skin-tone", "woman-singer-medium-light-skin-tone", "woman-singer-medium-skin-tone", "woman-standing", "woman-standing-dark-skin-tone", "woman-standing-light-skin-tone", "woman-standing-medium-dark-skin-tone", "woman-standing-medium-light-skin-tone", "woman-standing-medium-skin-tone", "woman-student", "woman-student-dark-skin-tone", "woman-student-light-skin-tone", "woman-student-medium-dark-skin-tone", "woman-student-medium-light-skin-tone", "woman-student-medium-skin-tone", "woman-superhero", "woman-superhero-dark-skin-tone", "woman-superhero-light-skin-tone", "woman-superhero-medium-dark-skin-tone", "woman-superhero-medium-light-skin-tone", "woman-superhero-medium-skin-tone", "woman-supervillain", "woman-supervillain-dark-skin-tone", "woman-supervillain-light-skin-tone", "woman-supervillain-medium-dark-skin-tone", "woman-supervillain-medium-light-skin-tone", "woman-supervillain-medium-skin-tone", "woman-surfing", "woman-surfing-dark-skin-tone", "woman-surfing-light-skin-tone", "woman-surfing-medium-dark-skin-tone", "woman-surfing-medium-light-skin-tone", "woman-surfing-medium-skin-tone", "woman-swimming", "woman-swimming-dark-skin-tone", "woman-swimming-light-skin-tone", "woman-swimming-medium-dark-skin-tone", "woman-swimming-medium-light-skin-tone", "woman-swimming-medium-skin-tone", "woman-teacher", "woman-teacher-dark-skin-tone", "woman-teacher-light-skin-tone", "woman-teacher-medium-dark-skin-tone", "woman-teacher-medium-light-skin-tone", "woman-teacher-medium-skin-tone", "woman-technologist", "woman-technologist-dark-skin-tone", "woman-technologist-light-skin-tone", "woman-technologist-medium-dark-skin-tone", "woman-technologist-medium-light-skin-tone", "woman-technologist-medium-skin-tone", "woman-tipping-hand", "woman-tipping-hand-dark-skin-tone", "woman-tipping-hand-light-skin-tone", "woman-tipping-hand-medium-dark-skin-tone", "woman-tipping-hand-medium-light-skin-tone", "woman-tipping-hand-medium-skin-tone", "woman-vampire", "woman-vampire-dark-skin-tone", "woman-vampire-light-skin-tone", "woman-vampire-medium-dark-skin-tone", "woman-vampire-medium-light-skin-tone", "woman-vampire-medium-skin-tone", "woman-walking", "woman-walking-dark-skin-tone", "woman-walking-light-skin-tone", "woman-walking-medium-dark-skin-tone", "woman-walking-medium-light-skin-tone", "woman-walking-medium-skin-tone", "woman-wearing-turban", "woman-wearing-turban-dark-skin-tone", "woman-wearing-turban-light-skin-tone", "woman-wearing-turban-medium-dark-skin-tone", "woman-wearing-turban-medium-light-skin-tone", "woman-wearing-turban-medium-skin-tone", "woman-white-hair", "woman-with-headscarf", "woman-with-headscarf-dark-skin-tone", "woman-with-headscarf-light-skin-tone", "woman-with-headscarf-medium-dark-skin-tone", "woman-with-headscarf-medium-light-skin-tone", "woman-with-headscarf-medium-skin-tone", "woman-with-veil", "woman-with-veil-dark-skin-tone", "woman-with-veil-light-skin-tone", "woman-with-veil-medium-dark-skin-tone", "woman-with-veil-medium-light-skin-tone", "woman-with-veil-medium-skin-tone", "woman-with-white-cane", "woman-with-white-cane-dark-skin-tone", "woman-with-white-cane-light-skin-tone", "woman-with-white-cane-medium-dark-skin-tone", "woman-with-white-cane-medium-light-skin-tone", "woman-with-white-cane-medium-skin-tone", "woman-zombie", "womans-boot", "womans-clothes", "womans-hat", "womans-sandal", "women-holding-hands", "women-holding-hands-dark-skin-tone", "women-holding-hands-dark-skin-tone-light-skin-tone", "women-holding-hands-dark-skin-tone-medium-dark-skin-tone", "women-holding-hands-dark-skin-tone-medium-light-skin-tone", "women-holding-hands-dark-skin-tone-medium-skin-tone", "women-holding-hands-light-skin-tone", "women-holding-hands-light-skin-tone-dark-skin-tone", "women-holding-hands-light-skin-tone-medium-dark-skin-tone", "women-holding-hands-light-skin-tone-medium-light-skin-tone", "women-holding-hands-light-skin-tone-medium-skin-tone", "women-holding-hands-medium-dark-skin-tone", "women-holding-hands-medium-dark-skin-tone-dark-skin-tone", "women-holding-hands-medium-dark-skin-tone-light-skin-tone", "women-holding-hands-medium-dark-skin-tone-medium-light-skin-tone", "women-holding-hands-medium-dark-skin-tone-medium-skin-tone", "women-holding-hands-medium-light-skin-tone", "women-holding-hands-medium-light-skin-tone-dark-skin-tone", "women-holding-hands-medium-light-skin-tone-light-skin-tone", "women-holding-hands-medium-light-skin-tone-medium-dark-skin-tone", "women-holding-hands-medium-light-skin-tone-medium-skin-tone", "women-holding-hands-medium-skin-tone", "women-holding-hands-medium-skin-tone-dark-skin-tone", "women-holding-hands-medium-skin-tone-light-skin-tone", "women-holding-hands-medium-skin-tone-medium-dark-skin-tone", "women-holding-hands-medium-skin-tone-medium-light-skin-tone", "women-with-bunny-ears", "women-wrestling", "womens-room", "wood", "woozy-face", "world-map", "worm", "worried-face", "wrapped-gift", "wrench", "writing-hand", "writing-hand-dark-skin-tone", "writing-hand-light-skin-tone", "writing-hand-medium-dark-skin-tone", "writing-hand-medium-light-skin-tone", "writing-hand-medium-skin-tone", "x-ray", "yarn", "yawning-face", "yellow-circle", "yellow-heart", "yellow-square", "yen-banknote", "yin-yang", "yo-yo", "zany-face", "zebra", "zipper-mouth-face", "zombie", "zzz"]}, {"prefix": "vscode-icons", "info": {"name": "VSCode Icons", "total": 1240, "version": "12.6.0", "author": {"name": "<PERSON>", "url": "https://github.com/vscode-icons/vscode-icons"}, "license": {"title": "MIT", "spdx": "MIT", "url": "https://github.com/vscode-icons/vscode-icons/blob/master/LICENSE"}, "samples": ["file-type-actionscript2", "file-type-json", "file-type-manifest"], "height": 32, "displayHeight": 16, "category": "General", "palette": true}, "icons": ["default-file", "default-folder", "default-folder-opened", "default-root-folder", "default-root-folder-opened", "file-type-access", "file-type-access2", "file-type-actionscript", "file-type-actionscript2", "file-type-ada", "file-type-advpl", "file-type-affinitydesigner", "file-type-affinityphoto", "file-type-affinitypublisher", "file-type-ai", "file-type-ai2", "file-type-al", "file-type-al-dal", "file-type-allcontributors", "file-type-angular", "file-type-ansible", "file-type-antlr", "file-type-anyscript", "file-type-apache", "file-type-apex", "file-type-api-extractor", "file-type-apib", "file-type-apib2", "file-type-apl", "file-type-applescript", "file-type-appscript", "file-type-appsemble", "file-type-appveyor", "file-type-arduino", "file-type-asciidoc", "file-type-asp", "file-type-aspx", "file-type-assembly", "file-type-astro", "file-type-astroconfig", "file-type-ats", "file-type-audio", "file-type-aurelia", "file-type-autohotkey", "file-type-autoit", "file-type-avif", "file-type-avro", "file-type-awk", "file-type-aws", "file-type-azure", "file-type-azurepipelines", "file-type-babel", "file-type-babel2", "file-type-ballerina", "file-type-bat", "file-type-bats", "file-type-bazaar", "file-type-bazel", "file-type-bazel-ignore", "file-type-bazel-version", "file-type-befunge", "file-type-bicep", "file-type-biml", "file-type-binary", "file-type-biome", "file-type-bitbucketpipeline", "file-type-bithound", "file-type-blade", "file-type-blitzbasic", "file-type-bolt", "file-type-bosque", "file-type-bower", "file-type-bower2", "file-type-browserslist", "file-type-buckbuild", "file-type-buf", "file-type-bun", "file-type-bundler", "file-type-bunfig", "file-type-c", "file-type-c-al", "file-type-c2", "file-type-c3", "file-type-cabal", "file-type-caddy", "file-type-cake", "file-type-cakephp", "file-type-capacitor", "file-type-cargo", "file-type-casc", "file-type-cddl", "file-type-cert", "file-type-ceylon", "file-type-cf", "file-type-cf2", "file-type-cfc", "file-type-cfc2", "file-type-cfm", "file-type-cfm2", "file-type-cheader", "file-type-chef", "file-type-chef-cookbook", "file-type-circleci", "file-type-class", "file-type-clojure", "file-type-clojurescript", "file-type-cloudfoundry", "file-type-cmake", "file-type-cobol", "file-type-codacy", "file-type-codeclimate", "file-type-codecov", "file-type-codekit", "file-type-codeowners", "file-type-codeql", "file-type-coffeelint", "file-type-coffeescript", "file-type-commitizen", "file-type-commitlint", "file-type-compass", "file-type-composer", "file-type-conan", "file-type-conda", "file-type-config", "file-type-confluence", "file-type-coveralls", "file-type-cpp", "file-type-cpp2", "file-type-cpp3", "file-type-cppheader", "file-type-crowdin", "file-type-crystal", "file-type-csharp", "file-type-csharp2", "file-type-cspell", "file-type-csproj", "file-type-css", "file-type-csscomb", "file-type-csslint", "file-type-cssmap", "file-type-cucumber", "file-type-cuda", "file-type-cvs", "file-type-cypress", "file-type-cypress-spec", "file-type-cython", "file-type-dal", "file-type-darcs", "file-type-dartlang", "file-type-dartlang-generated", "file-type-dartlang-ignore", "file-type-datadog", "file-type-db", "file-type-delphi", "file-type-deno", "file-type-denoify", "file-type-dependabot", "file-type-dependencies", "file-type-devcontainer", "file-type-dhall", "file-type-diff", "file-type-django", "file-type-dlang", "file-type-docker", "file-type-docker2", "file-type-dockertest", "file-type-dockertest2", "file-type-docpad", "file-type-docz", "file-type-dojo", "file-type-doppler", "file-type-dotenv", "file-type-dotjs", "file-type-doxygen", "file-type-drawio", "file-type-drone", "file-type-drools", "file-type-dustjs", "file-type-dvc", "file-type-dylan", "file-type-earthly", "file-type-eas-metadata", "file-type-edge", "file-type-edge2", "file-type-editorconfig", "file-type-eex", "file-type-ejs", "file-type-elastic", "file-type-elasticbeanstalk", "file-type-eleventy", "file-type-eleventy2", "file-type-elixir", "file-type-elm", "file-type-elm2", "file-type-emacs", "file-type-ember", "file-type-ensime", "file-type-eps", "file-type-erb", "file-type-erlang", "file-type-erlang2", "file-type-esbuild", "file-type-eslint", "file-type-eslint2", "file-type-esphome", "file-type-excel", "file-type-excel2", "file-type-expo", "file-type-falcon", "file-type-fantasticon", "file-type-fauna", "file-type-favicon", "file-type-fbx", "file-type-firebase", "file-type-firebasehosting", "file-type-firestore", "file-type-fitbit", "file-type-fla", "file-type-flareact", "file-type-flash", "file-type-floobits", "file-type-flow", "file-type-flutter", "file-type-flutter-package", "file-type-font", "file-type-formkit", "file-type-fortran", "file-type-fossa", "file-type-fossil", "file-type-freemarker", "file-type-fsharp", "file-type-fsharp2", "file-type-fsproj", "file-type-fthtml", "file-type-funding", "file-type-fusebox", "file-type-galen", "file-type-galen2", "file-type-gamemaker", "file-type-gamemaker2", "file-type-gamemaker81", "file-type-gatsby", "file-type-gcloud", "file-type-gcode", "file-type-gdscript", "file-type-genstat", "file-type-git", "file-type-git2", "file-type-gitlab", "file-type-gitpod", "file-type-glide", "file-type-glitter", "file-type-glsl", "file-type-glyphs", "file-type-gnu", "file-type-gnuplot", "file-type-go", "file-type-go-aqua", "file-type-go-black", "file-type-go-fuchsia", "file-type-go-gopher", "file-type-go-lightblue", "file-type-go-package", "file-type-go-white", "file-type-go-work", "file-type-go-yellow", "file-type-goctl", "file-type-godot", "file-type-gradle", "file-type-gradle2", "file-type-grain", "file-type-graphql", "file-type-graphql-config", "file-type-graphviz", "file-type-greenkeeper", "file-type-gridsome", "file-type-groovy", "file-type-groovy2", "file-type-grunt", "file-type-gulp", "file-type-haml", "file-type-handlebars", "file-type-handlebars2", "file-type-harbour", "file-type-hardhat", "file-type-hashicorp", "file-type-haskell", "file-type-haskell2", "file-type-haxe", "file-type-haxecheckstyle", "file-type-haxedevelop", "file-type-helix", "file-type-helm", "file-type-hjson", "file-type-hlsl", "file-type-homeassistant", "file-type-horusec", "file-type-host", "file-type-html", "file-type-htmlhint", "file-type-http", "file-type-hunspell", "file-type-husky", "file-type-hy", "file-type-hygen", "file-type-hypr", "file-type-icl", "file-type-idris", "file-type-idrisbin", "file-type-idrispkg", "file-type-image", "file-type-imba", "file-type-inc", "file-type-infopath", "file-type-informix", "file-type-ini", "file-type-ink", "file-type-innosetup", "file-type-io", "file-type-iodine", "file-type-ionic", "file-type-jake", "file-type-janet", "file-type-jar", "file-type-jasmine", "file-type-java", "file-type-jbuilder", "file-type-jekyll", "file-type-jenkins", "file-type-jest", "file-type-jest-snapshot", "file-type-jinja", "file-type-jpm", "file-type-js", "file-type-js-official", "file-type-jsbeautify", "file-type-jsconfig", "file-type-jscpd", "file-type-jshint", "file-type-jsmap", "file-type-json", "file-type-json-official", "file-type-json-schema", "file-type-json2", "file-type-json5", "file-type-jsonld", "file-type-jsonnet", "file-type-jsp", "file-type-jss", "file-type-julia", "file-type-julia2", "file-type-jupyter", "file-type-k", "file-type-karma", "file-type-key", "file-type-kitchenci", "file-type-kite", "file-type-kivy", "file-type-kos", "file-type-kotlin", "file-type-kusto", "file-type-latino", "file-type-layout", "file-type-lerna", "file-type-less", "file-type-lex", "file-type-license", "file-type-licensebat", "file-type-light-actionscript2", "file-type-light-ada", "file-type-light-apl", "file-type-light-babel", "file-type-light-babel2", "file-type-light-cabal", "file-type-light-circleci", "file-type-light-cloudfoundry", "file-type-light-codacy", "file-type-light-codeclimate", "file-type-light-codeowners", "file-type-light-config", "file-type-light-crystal", "file-type-light-cypress", "file-type-light-cypress-spec", "file-type-light-db", "file-type-light-deno", "file-type-light-dhall", "file-type-light-docpad", "file-type-light-drone", "file-type-light-eas-metadata", "file-type-light-eleventy", "file-type-light-eleventy2", "file-type-light-esphome", "file-type-light-expo", "file-type-light-firebasehosting", "file-type-light-fla", "file-type-light-font", "file-type-light-gamemaker2", "file-type-light-gradle", "file-type-light-hashicorp", "file-type-light-hjson", "file-type-light-ini", "file-type-light-io", "file-type-light-js", "file-type-light-jsconfig", "file-type-light-jsmap", "file-type-light-json", "file-type-light-json-schema", "file-type-light-json5", "file-type-light-jsonld", "file-type-light-kite", "file-type-light-lerna", "file-type-light-mailing", "file-type-light-mdx", "file-type-light-mlang", "file-type-light-mustache", "file-type-light-netlify", "file-type-light-next", "file-type-light-nim", "file-type-light-nx", "file-type-light-objidconfig", "file-type-light-openhab", "file-type-light-pcl", "file-type-light-pnpm", "file-type-light-prettier", "file-type-light-prisma", "file-type-light-purescript", "file-type-light-quasar", "file-type-light-razzle", "file-type-light-rehype", "file-type-light-remark", "file-type-light-replit", "file-type-light-retext", "file-type-light-rome", "file-type-light-rubocop", "file-type-light-rust", "file-type-light-rust-toolchain", "file-type-light-shaderlab", "file-type-light-solidity", "file-type-light-spin", "file-type-light-stylelint", "file-type-light-stylus", "file-type-light-symfony", "file-type-light-systemd", "file-type-light-systemverilog", "file-type-light-testcafe", "file-type-light-testjs", "file-type-light-tex", "file-type-light-todo", "file-type-light-toit", "file-type-light-toml", "file-type-light-tree", "file-type-light-turbo", "file-type-light-unibeautify", "file-type-light-vash", "file-type-light-vercel", "file-type-light-vsix", "file-type-light-vsixmanifest", "file-type-light-xfl", "file-type-light-yaml", "file-type-light-zeit", "file-type-lighthouse", "file-type-lilypond", "file-type-lime", "file-type-lintstagedrc", "file-type-liquid", "file-type-lisp", "file-type-livescript", "file-type-lnk", "file-type-locale", "file-type-log", "file-type-lolcode", "file-type-lsl", "file-type-lua", "file-type-luau", "file-type-lync", "file-type-mailing", "file-type-manifest", "file-type-manifest-bak", "file-type-manifest-skip", "file-type-map", "file-type-mariadb", "file-type-markdown", "file-type-markdownlint", "file-type-markdownlint-ignore", "file-type-marko", "file-type-markojs", "file-type-master-co", "file-type-matlab", "file-type-maven", "file-type-maxscript", "file-type-maya", "file-type-mdx", "file-type-mediawiki", "file-type-mercurial", "file-type-mermaid", "file-type-meson", "file-type-meteor", "file-type-mjml", "file-type-mlang", "file-type-mocha", "file-type-modernizr", "file-type-mojo", "file-type-mojolicious", "file-type-moleculer", "file-type-mondoo", "file-type-mongo", "file-type-monotone", "file-type-motif", "file-type-mson", "file-type-mustache", "file-type-mysql", "file-type-ndst", "file-type-nearly", "file-type-nest-adapter-js", "file-type-nest-adapter-ts", "file-type-nest-controller-js", "file-type-nest-controller-ts", "file-type-nest-decorator-js", "file-type-nest-decorator-ts", "file-type-nest-filter-js", "file-type-nest-filter-ts", "file-type-nest-gateway-js", "file-type-nest-gateway-ts", "file-type-nest-guard-js", "file-type-nest-guard-ts", "file-type-nest-interceptor-js", "file-type-nest-interceptor-ts", "file-type-nest-middleware-js", "file-type-nest-middleware-ts", "file-type-nest-module-js", "file-type-nest-module-ts", "file-type-nest-pipe-js", "file-type-nest-pipe-ts", "file-type-nest-service-js", "file-type-nest-service-ts", "file-type-nestjs", "file-type-netlify", "file-type-next", "file-type-ng-component-css", "file-type-ng-component-dart", "file-type-ng-component-html", "file-type-ng-component-js", "file-type-ng-component-js2", "file-type-ng-component-less", "file-type-ng-component-sass", "file-type-ng-component-scss", "file-type-ng-component-ts", "file-type-ng-component-ts2", "file-type-ng-controller-js", "file-type-ng-controller-ts", "file-type-ng-directive-dart", "file-type-ng-directive-js", "file-type-ng-directive-js2", "file-type-ng-directive-ts", "file-type-ng-directive-ts2", "file-type-ng-guard-dart", "file-type-ng-guard-js", "file-type-ng-guard-ts", "file-type-ng-interceptor-dart", "file-type-ng-interceptor-js", "file-type-ng-interceptor-ts", "file-type-ng-module-dart", "file-type-ng-module-js", "file-type-ng-module-js2", "file-type-ng-module-ts", "file-type-ng-module-ts2", "file-type-ng-pipe-dart", "file-type-ng-pipe-js", "file-type-ng-pipe-js2", "file-type-ng-pipe-ts", "file-type-ng-pipe-ts2", "file-type-ng-routing-dart", "file-type-ng-routing-js", "file-type-ng-routing-js2", "file-type-ng-routing-ts", "file-type-ng-routing-ts2", "file-type-ng-service-dart", "file-type-ng-service-js", "file-type-ng-service-js2", "file-type-ng-service-ts", "file-type-ng-service-ts2", "file-type-ng-smart-component-dart", "file-type-ng-smart-component-js", "file-type-ng-smart-component-js2", "file-type-ng-smart-component-ts", "file-type-ng-smart-component-ts2", "file-type-ng-tailwind", "file-type-nginx", "file-type-nim", "file-type-nimble", "file-type-ninja", "file-type-nix", "file-type-njsproj", "file-type-noc", "file-type-node", "file-type-node2", "file-type-nodemon", "file-type-npm", "file-type-nsi", "file-type-nsri", "file-type-nsri-integrity", "file-type-nuget", "file-type-numpy", "file-type-nunjucks", "file-type-nuxt", "file-type-nx", "file-type-nyc", "file-type-objectivec", "file-type-objectivecpp", "file-type-objidconfig", "file-type-ocaml", "file-type-ocaml-intf", "file-type-ogone", "file-type-onenote", "file-type-opencl", "file-type-openhab", "file-type-openscad", "file-type-org", "file-type-outlook", "file-type-ovpn", "file-type-package", "file-type-paket", "file-type-patch", "file-type-pcl", "file-type-pddl", "file-type-pddl-happenings", "file-type-pddl-plan", "file-type-pdf2", "file-type-peeky", "file-type-perl", "file-type-perl2", "file-type-perl6", "file-type-pgsql", "file-type-photoshop", "file-type-photoshop2", "file-type-php", "file-type-php2", "file-type-php3", "file-type-phpcsfixer", "file-type-phpstan", "file-type-phpunit", "file-type-phraseapp", "file-type-pine", "file-type-pip", "file-type-pipeline", "file-type-plantuml", "file-type-platformio", "file-type-playwright", "file-type-plsql", "file-type-plsql-package", "file-type-plsql-package-body", "file-type-plsql-package-header", "file-type-plsql-package-spec", "file-type-pm2", "file-type-pnpm", "file-type-poedit", "file-type-poetry", "file-type-polymer", "file-type-pony", "file-type-postcss", "file-type-postcssconfig", "file-type-postman", "file-type-powerpoint", "file-type-powerpoint2", "file-type-powershell", "file-type-powershell-format", "file-type-powershell-psd", "file-type-powershell-psd2", "file-type-powershell-psm", "file-type-powershell-psm2", "file-type-powershell-types", "file-type-powershell2", "file-type-preact", "file-type-precommit", "file-type-prettier", "file-type-prisma", "file-type-processinglang", "file-type-procfile", "file-type-progress", "file-type-prolog", "file-type-prometheus", "file-type-protobuf", "file-type-protractor", "file-type-publiccode", "file-type-publisher", "file-type-pug", "file-type-pulumi", "file-type-puppet", "file-type-purescript", "file-type-pyret", "file-type-python", "file-type-pythowo", "file-type-pytyped", "file-type-pyup", "file-type-q", "file-type-qbs", "file-type-qlikview", "file-type-qml", "file-type-qmldir", "file-type-qsharp", "file-type-quasar", "file-type-r", "file-type-racket", "file-type-rails", "file-type-rake", "file-type-raml", "file-type-razor", "file-type-razzle", "file-type-reactjs", "file-type-reacttemplate", "file-type-reactts", "file-type-reason", "file-type-red", "file-type-registry", "file-type-rego", "file-type-rehype", "file-type-remark", "file-type-renovate", "file-type-replit", "file-type-rescript", "file-type-rest", "file-type-retext", "file-type-rexx", "file-type-riot", "file-type-rmd", "file-type-robotframework", "file-type-robots", "file-type-rollup", "file-type-rome", "file-type-ron", "file-type-rproj", "file-type-rspec", "file-type-rubocop", "file-type-ruby", "file-type-rust", "file-type-rust-toolchain", "file-type-sails", "file-type-saltstack", "file-type-san", "file-type-sapphire-framework-cli", "file-type-sas", "file-type-sass", "file-type-sbt", "file-type-scala", "file-type-scilab", "file-type-script", "file-type-scss", "file-type-scss2", "file-type-sdlang", "file-type-sentry", "file-type-sequelize", "file-type-serverless", "file-type-shaderlab", "file-type-shell", "file-type-shuttle", "file-type-silverstripe", "file-type-sino", "file-type-siyuan", "file-type-sketch", "file-type-skipper", "file-type-slang", "file-type-slashup", "file-type-slice", "file-type-slim", "file-type-sln", "file-type-sln2", "file-type-smarty", "file-type-snapcraft", "file-type-snaplet", "file-type-snort", "file-type-snyk", "file-type-solidarity", "file-type-solidity", "file-type-source", "file-type-spacengine", "file-type-sparql", "file-type-spin", "file-type-sqf", "file-type-sql", "file-type-sqlite", "file-type-squirrel", "file-type-sss", "file-type-stan", "file-type-stata", "file-type-stencil", "file-type-storyboard", "file-type-storybook", "file-type-stryker", "file-type-stylable", "file-type-style", "file-type-styled", "file-type-stylelint", "file-type-stylish-haskell", "file-type-stylus", "file-type-sublime", "file-type-subversion", "file-type-svelte", "file-type-svelteconfig", "file-type-svg", "file-type-swagger", "file-type-swc", "file-type-swift", "file-type-swig", "file-type-symfony", "file-type-systemd", "file-type-systemverilog", "file-type-t4tt", "file-type-tailwind", "file-type-tamagui", "file-type-taskfile", "file-type-tauri", "file-type-tcl", "file-type-teal", "file-type-tera", "file-type-terraform", "file-type-test", "file-type-testcafe", "file-type-testjs", "file-type-testts", "file-type-tex", "file-type-text", "file-type-textile", "file-type-tfs", "file-type-tiltfile", "file-type-todo", "file-type-toit", "file-type-toml", "file-type-tox", "file-type-travis", "file-type-tree", "file-type-tres", "file-type-truffle", "file-type-trunk", "file-type-tsbuildinfo", "file-type-tscn", "file-type-tsconfig", "file-type-tsconfig-official", "file-type-tslint", "file-type-tt", "file-type-ttcn", "file-type-tuc", "file-type-turbo", "file-type-twig", "file-type-typedoc", "file-type-typescript", "file-type-typescript-official", "file-type-typescriptdef", "file-type-typescriptdef-official", "file-type-typo3", "file-type-uiua", "file-type-unibeautify", "file-type-unison", "file-type-unlicense", "file-type-unocss", "file-type-vagrant", "file-type-vala", "file-type-vanilla-extract", "file-type-vapi", "file-type-vapor", "file-type-vash", "file-type-vb", "file-type-vba", "file-type-vbhtml", "file-type-vbproj", "file-type-vcxproj", "file-type-velocity", "file-type-vercel", "file-type-verilog", "file-type-vhdl", "file-type-video", "file-type-view", "file-type-vim", "file-type-vite", "file-type-vitest", "file-type-vlang", "file-type-volt", "file-type-vscode", "file-type-vscode-insiders", "file-type-vscode-test", "file-type-vscode2", "file-type-vscode3", "file-type-vsix", "file-type-vsixmanifest", "file-type-vue", "file-type-vueconfig", "file-type-wallaby", "file-type-wally", "file-type-wasm", "file-type-watchmanconfig", "file-type-wdio", "file-type-webp", "file-type-webpack", "file-type-wenyan", "file-type-we<PERSON>ker", "file-type-wgsl", "file-type-wikitext", "file-type-windi", "file-type-wolfram", "file-type-word", "file-type-word2", "file-type-wpml", "file-type-wurst", "file-type-wxml", "file-type-wxss", "file-type-xcode", "file-type-xfl", "file-type-xib", "file-type-xliff", "file-type-xmake", "file-type-xml", "file-type-xquery", "file-type-xsl", "file-type-yacc", "file-type-yaml", "file-type-yamllint", "file-type-yandex", "file-type-yang", "file-type-yarn", "file-type-yeoman", "file-type-zeit", "file-type-zig", "file-type-zip", "file-type-zip2", "folder-type-android", "folder-type-android-opened", "folder-type-api", "folder-type-api-opened", "folder-type-app", "folder-type-app-opened", "folder-type-arangodb", "folder-type-arangodb-opened", "folder-type-asset", "folder-type-asset-opened", "folder-type-audio", "folder-type-audio-opened", "folder-type-aurelia", "folder-type-aurelia-opened", "folder-type-aws", "folder-type-aws-opened", "folder-type-azure", "folder-type-azure-opened", "folder-type-azurepipelines", "folder-type-azurepipelines-opened", "folder-type-binary", "folder-type-binary-opened", "folder-type-bloc", "folder-type-bloc-opened", "folder-type-blueprint", "folder-type-blueprint-opened", "folder-type-bot", "folder-type-bot-opened", "folder-type-bower", "folder-type-bower-opened", "folder-type-buildkite", "folder-type-buildkite-opened", "folder-type-cake", "folder-type-cake-opened", "folder-type-certificate", "folder-type-certificate-opened", "folder-type-changesets", "folder-type-changesets-opened", "folder-type-chef", "folder-type-chef-opened", "folder-type-circleci", "folder-type-circleci-opened", "folder-type-cli", "folder-type-cli-opened", "folder-type-client", "folder-type-client-opened", "folder-type-cmake", "folder-type-cmake-opened", "folder-type-common", "folder-type-common-opened", "folder-type-component", "folder-type-component-opened", "folder-type-composer", "folder-type-composer-opened", "folder-type-config", "folder-type-config-opened", "folder-type-controller", "folder-type-controller-opened", "folder-type-coverage", "folder-type-coverage-opened", "folder-type-css", "folder-type-css-opened", "folder-type-cubit", "folder-type-cubit-opened", "folder-type-cypress", "folder-type-cypress-opened", "folder-type-dapr", "folder-type-dapr-opened", "folder-type-datadog", "folder-type-datadog-opened", "folder-type-db", "folder-type-db-opened", "folder-type-debian", "folder-type-debian-opened", "folder-type-dependabot", "folder-type-dependabot-opened", "folder-type-devcontainer", "folder-type-devcontainer-opened", "folder-type-dist", "folder-type-dist-opened", "folder-type-docker", "folder-type-docker-opened", "folder-type-docs", "folder-type-docs-opened", "folder-type-e2e", "folder-type-e2e-opened", "folder-type-elasticbeanstalk", "folder-type-elasticbeanstalk-opened", "folder-type-electron", "folder-type-electron-opened", "folder-type-expo", "folder-type-expo-opened", "folder-type-favicon", "folder-type-favicon-opened", "folder-type-flow", "folder-type-flow-opened", "folder-type-fonts", "folder-type-fonts-opened", "folder-type-gcp", "folder-type-gcp-opened", "folder-type-git", "folder-type-git-opened", "folder-type-github", "folder-type-github-opened", "folder-type-gitlab", "folder-type-gitlab-opened", "folder-type-gradle", "folder-type-gradle-opened", "folder-type-graphql", "folder-type-graphql-opened", "folder-type-grunt", "folder-type-grunt-opened", "folder-type-gulp", "folder-type-gulp-opened", "folder-type-haxelib", "folder-type-haxelib-opened", "folder-type-helper", "folder-type-helper-opened", "folder-type-hook", "folder-type-hook-opened", "folder-type-husky", "folder-type-husky-opened", "folder-type-idea", "folder-type-idea-opened", "folder-type-images", "folder-type-images-opened", "folder-type-include", "folder-type-include-opened", "folder-type-interfaces", "folder-type-interfaces-opened", "folder-type-ios", "folder-type-ios-opened", "folder-type-js", "folder-type-js-opened", "folder-type-json", "folder-type-json-official", "folder-type-json-official-opened", "folder-type-json-opened", "folder-type-kubernetes", "folder-type-kubernetes-opened", "folder-type-less", "folder-type-less-opened", "folder-type-library", "folder-type-library-opened", "folder-type-light-cypress", "folder-type-light-cypress-opened", "folder-type-light-electron", "folder-type-light-electron-opened", "folder-type-light-expo", "folder-type-light-expo-opened", "folder-type-light-fonts", "folder-type-light-fonts-opened", "folder-type-light-gradle", "folder-type-light-gradle-opened", "folder-type-light-meteor", "folder-type-light-meteor-opened", "folder-type-light-mysql", "folder-type-light-mysql-opened", "folder-type-light-node", "folder-type-light-node-opened", "folder-type-light-redux", "folder-type-light-redux-opened", "folder-type-light-sass", "folder-type-light-sass-opened", "folder-type-linux", "folder-type-linux-opened", "folder-type-locale", "folder-type-locale-opened", "folder-type-log", "folder-type-log-opened", "folder-type-macos", "folder-type-macos-opened", "folder-type-mariadb", "folder-type-mariadb-opened", "folder-type-maven", "folder-type-maven-opened", "folder-type-memcached", "folder-type-memcached-opened", "folder-type-meteor", "folder-type-meteor-opened", "folder-type-middleware", "folder-type-middleware-opened", "folder-type-minikube", "folder-type-minikube-opened", "folder-type-mjml", "folder-type-mjml-opened", "folder-type-mock", "folder-type-mock-opened", "folder-type-model", "folder-type-model-opened", "folder-type-module", "folder-type-module-opened", "folder-type-mojo", "folder-type-mojo-opened", "folder-type-mongodb", "folder-type-mongodb-opened", "folder-type-mysql", "folder-type-mysql-opened", "folder-type-next", "folder-type-next-opened", "folder-type-nginx", "folder-type-nginx-opened", "folder-type-nix", "folder-type-nix-opened", "folder-type-node", "folder-type-node-opened", "folder-type-notebooks", "folder-type-notebooks-opened", "folder-type-notification", "folder-type-notification-opened", "folder-type-nuget", "folder-type-nuget-opened", "folder-type-nuxt", "folder-type-nuxt-opened", "folder-type-package", "folder-type-package-opened", "folder-type-paket", "folder-type-paket-opened", "folder-type-php", "folder-type-php-opened", "folder-type-platformio", "folder-type-platformio-opened", "folder-type-plugin", "folder-type-plugin-opened", "folder-type-prisma", "folder-type-prisma-opened", "folder-type-private", "folder-type-private-opened", "folder-type-public", "folder-type-public-opened", "folder-type-python", "folder-type-python-opened", "folder-type-ravendb", "folder-type-ravendb-opened", "folder-type-redis", "folder-type-redis-opened", "folder-type-redux", "folder-type-redux-opened", "folder-type-route", "folder-type-route-opened", "folder-type-sass", "folder-type-sass-opened", "folder-type-script", "folder-type-script-opened", "folder-type-server", "folder-type-server-opened", "folder-type-services", "folder-type-services-opened", "folder-type-shared", "folder-type-shared-opened", "folder-type-snaplet", "folder-type-snaplet-opened", "folder-type-spin", "folder-type-spin-opened", "folder-type-src", "folder-type-src-opened", "folder-type-sso", "folder-type-sso-opened", "folder-type-story", "folder-type-story-opened", "folder-type-style", "folder-type-style-opened", "folder-type-svelte", "folder-type-svelte-opened", "folder-type-tauri", "folder-type-tauri-opened", "folder-type-temp", "folder-type-temp-opened", "folder-type-template", "folder-type-template-opened", "folder-type-test", "folder-type-test-opened", "folder-type-theme", "folder-type-theme-opened", "folder-type-tools", "folder-type-tools-opened", "folder-type-travis", "folder-type-travis-opened", "folder-type-trunk", "folder-type-trunk-opened", "folder-type-turbo", "folder-type-turbo-opened", "folder-type-typescript", "folder-type-typescript-opened", "folder-type-typings", "folder-type-typings-opened", "folder-type-typings2", "folder-type-typings2-opened", "folder-type-vagrant", "folder-type-vagrant-opened", "folder-type-vercel", "folder-type-vercel-opened", "folder-type-video", "folder-type-video-opened", "folder-type-view", "folder-type-view-opened", "folder-type-vs", "folder-type-vs-opened", "folder-type-vs2", "folder-type-vs2-opened", "folder-type-vscode", "folder-type-vscode-opened", "folder-type-vscode-test", "folder-type-vscode-test-opened", "folder-type-vscode-test2", "folder-type-vscode-test2-opened", "folder-type-vscode-test3", "folder-type-vscode-test3-opened", "folder-type-vscode2", "folder-type-vscode2-opened", "folder-type-vscode3", "folder-type-vscode3-opened", "folder-type-webpack", "folder-type-webpack-opened", "folder-type-windows", "folder-type-windows-opened", "folder-type-www", "folder-type-www-opened", "folder-type-yarn", "folder-type-yarn-opened"]}]