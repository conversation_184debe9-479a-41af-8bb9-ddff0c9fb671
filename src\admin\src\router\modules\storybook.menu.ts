import type { RouteRecordRaw } from 'vue-router';

function Layout() {
  return import('@/layouts/index.vue');
}

const routes: RouteRecordRaw = {
  path: '/storybook',
  component: Layout,
  redirect: '/storybook/list',
  name: 'StorybookMenu',
  meta: {
    title: '绘本管理',
    icon: 'mdi:book-open-page-variant',
  },
  children: [
    {
      path: 'list',
      name: 'StorybookList',
      component: () => import('@/views/storybook/index.vue'),
      meta: {
        title: '绘本列表',
        icon: 'mdi:book-multiple',
      },
    },
    {
      path: 'detail/:id',
      name: 'StorybookDetail',
      component: () => import('@/views/storybook/detail.vue'),
      meta: {
        title: '绘本详情',
        icon: 'mdi:book-open-variant',
        activeMenu: '/storybook/list',
        menu: false,
      },
    },
    {
      path: 'images',
      name: 'StorybookImages',
      component: () => import('@/views/storybook/images.vue'),
      meta: {
        title: '绘本图片',
        icon: 'mdi:image-multiple',
      },
    },
    {
      path: 'template',
      name: 'StorybookTemplate',
      component: () => import('@/views/storybook/template.vue'),
      meta: {
        title: '绘本模板',
        icon: 'mdi:file-document-multiple',
      },
    },
    {
      path: 'prompt',
      name: 'StorybookPrompt',
      component: () => import('@/views/storybook/prompt.vue'),
      meta: {
        title: '提示词管理',
        icon: 'mdi:text-box',
      },
    },
    {
      path: 'statistics',
      name: 'StorybookStatistics',
      component: () => import('@/views/storybook/statistics.vue'),
      meta: {
        title: '统计分析',
        icon: 'mdi:chart-line',
      },
    },
    {
      path: 'config',
      name: 'StorybookConfig',
      component: () => import('@/views/storybook/config.vue'),
      meta: {
        title: '绘本配置',
        icon: 'mdi:cog',
      },
    },
  ],
};

export default routes;
