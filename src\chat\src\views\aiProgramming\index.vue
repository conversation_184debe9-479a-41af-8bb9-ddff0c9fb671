<template>
    <div class="h-screen flex flex-col overflow-hidden fixed-viewport">
        <div
            class="flex items-center justify-between bg-white dark:bg-gray-800 py-2 px-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
            <h1 class="text-xl font-bold text-gray-800 dark:text-gray-200">DeepCreate</h1>
        </div>

        <div class="flex-1 flex overflow-hidden" :class="[isMobile ? 'flex-col' : 'flex-row']">
            <!-- 代码编辑区域 -->
            <CodeEditor v-model="editableCode" :dark="isDarkMode" :class="[isMobile ? 'w-full' : 'w-2/5']"
                :style="{ height: isMobile ? 'calc(50vh - 30px)' : '100%' }" ref="codeEditorRef"
                class="overflow-hidden" />

            <!-- 预览区域 -->
            <DevicePreview :code="editableCode" :device-type="deviceType" :is-landscape="isLandscape"
                :is-fullscreen="isFullscreen" :current-device-style="currentDeviceStyle"
                :preview-container-class="previewContainerClass" :fullscreen-container-class="fullscreenContainerClass"
                @change-device="changeDeviceType" @toggle-orientation="toggleOrientation"
                @toggle-fullscreen="toggleFullscreen" @refresh="refreshPreview"
                @container-resize="handleContainerResize" :class="[
                    isMobile ? 'w-full' : 'w-3/5',
                    'device-preview-wrapper'
                ]" :style="{ height: isMobile ? 'calc(50vh - 30px)' : '100%' }" class="overflow-hidden" />
        </div>

        <!-- 悬浮按钮组 -->
        <div class="floating-menu fixed bottom-6 right-10 z-70" ref="mainMenuRef">
            <!-- 主按钮 -->
            <button @click.stop="toggleMainMenu"
                class="main-button flex items-center justify-center p-3 rounded-full shadow-xl transition-all duration-300"
                :class="{ 'active': showMainMenu }">
                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <circle cx="12" cy="12" r="10" stroke-width="2" />
                    <path d="M12 8v8M8 12h8" stroke-width="2" stroke-linecap="round" />
                </svg>
            </button>

            <!-- 功能按钮菜单 -->
            <transition name="scale-up">
                <div v-show="showMainMenu"
                    class="menu-buttons absolute bottom-16 right-0 flex flex-col items-end space-y-3">
                    <!-- 聊天按钮 -->
                    <div class="menu-item flex items-center space-x-2" :class="{ 'show-item': showMainMenu }"
                        style="--index: 0;">
                        <span
                            class="button-label bg-white dark:bg-gray-800 px-3 py-1 rounded-lg text-sm shadow-md mr-2 opacity-0 transform translate-x-4 transition-all duration-300"
                            :class="{ 'show-label': showMainMenu }">
                            聊天对话
                        </span>
                        <button @click="navigateToChat(); closeMainMenu()"
                            class="floating-button chat-button flex items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </button>
                    </div>

                    <!-- 格式化代码按钮 -->
                    <div class="menu-item flex items-center space-x-2" :class="{ 'show-item': showMainMenu }"
                        style="--index: 1;">
                        <span
                            class="button-label bg-white dark:bg-gray-800 px-3 py-1 rounded-lg text-sm shadow-md mr-2 opacity-0 transform translate-x-4 transition-all duration-300"
                            :class="{ 'show-label': showMainMenu }">
                            格式化代码
                        </span>
                        <button @click="formatCode(); closeMainMenu()"
                            class="floating-button format-button flex items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3zm0 0h16M9 3v4m6-4v4" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 17l3-3m0 0l3-3m-3 3V8" />
                            </svg>
                        </button>
                    </div>

                    <!-- 保存按钮 -->
                    <div class="menu-item flex items-center space-x-2" ref="shareSaveControlsRef"
                        :class="{ 'show-item': showMainMenu }" style="--index: 1.5;">
                        <span
                            class="button-label bg-white dark:bg-gray-800 px-3 py-1 rounded-lg text-sm shadow-md mr-2 opacity-0 transform translate-x-4 transition-all duration-300"
                            :class="{ 'show-label': showMainMenu }">
                            保存
                        </span>
                        <!-- 保存主按钮 -->
                        <button @click.stop="toggleShareSaveControls(); $event.stopPropagation()"
                            class="floating-button share-save-button flex items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 12h14M12 5v14" />
                            </svg>
                        </button>

                        <!-- 分享与保存二级菜单 -->
                        <transition name="slide-up">
                            <div v-if="showShareSaveControls"
                                class="share-save-controls-menu absolute bottom-0 right-16 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 mb-2 flex flex-col space-y-4 min-w-[200px]"
                                @click.stop>
                                <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 border-b pb-2 dark:border-gray-700">保存</div>





                                <!-- 下载HTML -->
                                <button @click="downloadHTML()"
                                    class="download-button flex items-center px-3 py-2.5 rounded-md bg-gray-50 dark:bg-gray-700 transition-all duration-200 text-gray-700 dark:text-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                    </svg>
                                    <span>下载HTML</span>
                                </button>
                            </div>
                        </transition>
                    </div>

                    <!-- 全屏按钮 -->
                    <div class="menu-item flex items-center space-x-2" :class="{ 'show-item': showMainMenu }"
                        style="--index: 2;">
                        <span
                            class="button-label bg-white dark:bg-gray-800 px-3 py-1 rounded-lg text-sm shadow-md mr-2 opacity-0 transform translate-x-4 transition-all duration-300"
                            :class="{ 'show-label': showMainMenu }">
                            全屏
                        </span>
                        <button @click="toggleFullscreen(); closeMainMenu()"
                            class="floating-button fullscreen-button flex items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300">
                            <svg v-if="isFullscreen" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path
                                    d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <svg v-else class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor">
                                <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" />
                            </svg>
                        </button>
                    </div>

                    <!-- 设备控制按钮 -->
                    <div class="menu-item flex items-center space-x-2" ref="deviceControlsRef"
                        :class="{ 'show-item': showMainMenu }" style="--index: 3;">
                        <span
                            class="button-label bg-white dark:bg-gray-800 px-3 py-1 rounded-lg text-sm shadow-md mr-2 opacity-0 transform translate-x-4 transition-all duration-300"
                            :class="{ 'show-label': showMainMenu }">
                            设备
                        </span>
                        <!-- 设备控制主按钮 -->
                        <button @click.stop="toggleDeviceControls(); $event.stopPropagation()"
                            class="floating-button device-button flex items-center justify-center p-3 rounded-full shadow-lg transition-all duration-300">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor">
                                <rect x="5" y="2" width="14" height="20" rx="2" ry="2" stroke-width="2" />
                                <line x1="12" y1="18" x2="12" y2="18.01" stroke-width="2" stroke-linecap="round" />
                            </svg>
                        </button>

                        <!-- 设备控制二级菜单 -->
                        <transition name="slide-up">
                            <div v-if="showDeviceControls"
                                class="device-controls-menu absolute bottom-0 right-16 bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 mb-2 flex flex-col space-y-4 min-w-[200px]"
                                @click.stop>
                                <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 border-b pb-2 dark:border-gray-700">设备预览设置</div>

                                <!-- 设备类型选择 -->
                                <div class="flex flex-col space-y-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">设备类型</div>
                                    <div
                                        class="device-selector-group bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex items-center justify-between">
                                        <button @click="changeDeviceType('mobile'); closeDeviceControls()" :class="[
                                            'device-selector-button flex items-center justify-center p-2 rounded-md transition-all duration-200',
                                            deviceType === 'mobile' ? 'active-device' : 'inactive-device'
                                        ]">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor">
                                                <rect x="7" y="4" width="10" height="16" rx="2" ry="2"
                                                    stroke-width="2" />
                                                <line x1="12" y1="18" x2="12" y2="18.01" stroke-width="2"
                                                    stroke-linecap="round" />
                                            </svg>
                                            <span class="ml-1 text-xs">手机</span>
                                        </button>
                                        <button @click="changeDeviceType('tablet'); closeDeviceControls()" :class="[
                                            'device-selector-button flex items-center justify-center p-2 rounded-md transition-all duration-200',
                                            deviceType === 'tablet' ? 'active-device' : 'inactive-device'
                                        ]">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor">
                                                <rect x="4" y="4" width="16" height="16" rx="2" ry="2"
                                                    stroke-width="2" />
                                                <line x1="12" y1="17" x2="12" y2="17.01" stroke-width="2"
                                                    stroke-linecap="round" />
                                            </svg>
                                            <span class="ml-1 text-xs">平板</span>
                                        </button>
                                        <button @click="changeDeviceType('desktop'); closeDeviceControls()" :class="[
                                            'device-selector-button flex items-center justify-center p-2 rounded-md transition-all duration-200',
                                            deviceType === 'desktop' ? 'active-device' : 'inactive-device'
                                        ]">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor">
                                                <rect x="2" y="4" width="20" height="14" rx="2" ry="2"
                                                    stroke-width="2" />
                                                <line x1="8" y1="20" x2="16" y2="20" stroke-width="2"
                                                    stroke-linecap="round" />
                                                <line x1="12" y1="18" x2="12" y2="20" stroke-width="2" />
                                            </svg>
                                            <span class="ml-1 text-xs">电脑</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- 横竖屏切换 -->
                                <div v-if="deviceType !== 'desktop'" class="flex flex-col space-y-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">屏幕方向</div>
                                    <div
                                        class="orientation-selector-group bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex items-center justify-between">
                                        <button @click="toggleOrientation(); closeDeviceControls()"
                                            class="orientation-button flex items-center justify-center p-2 rounded-md transition-all duration-200 flex-1"
                                            :class="[isLandscape ? 'inactive-orientation' : 'active-orientation']">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor">
                                                <rect x="5" y="3" width="14" height="18" rx="2" ry="2"
                                                    stroke-width="2" />
                                            </svg>
                                            <span class="ml-1 text-xs">竖屏</span>
                                        </button>
                                        <button @click="toggleOrientation(); closeDeviceControls()"
                                            class="orientation-button flex items-center justify-center p-2 rounded-md transition-all duration-200 flex-1"
                                            :class="[isLandscape ? 'active-orientation' : 'inactive-orientation']">
                                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor">
                                                <rect x="3" y="5" width="18" height="14" rx="2" ry="2"
                                                    stroke-width="2" />
                                            </svg>
                                            <span class="ml-1 text-xs">横屏</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- 刷新按钮 -->
                                <button @click="refreshPreview(); closeDeviceControls()"
                                    class="refresh-button flex items-center px-3 py-2.5 rounded-md bg-gray-50 dark:bg-gray-700 transition-all duration-200 text-gray-700 dark:text-gray-300">
                                    <svg class="h-5 w-5 mr-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor">
                                        <path d="M23 4v6h-6M1 20v-6h6" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <span>刷新预览</span>
                                </button>
                            </div>
                        </transition>
                    </div>
                </div>
            </transition>
        </div>


    </div>


</template>

<script lang="ts" setup>
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAppStore } from '@/store';
import { useMessage } from 'naive-ui';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';


// 导入组件
import CodeEditor from './components/CodeEditor.vue';
import DevicePreview from './components/DevicePreview.vue';


// 导入组合式API
import { useCodeFormat } from './composables/useCodeFormat';
import { useDevicePreview, useDeviceWithFullscreen } from './composables/useDevicePreview';
import { useFullscreen } from './composables/useFullscreen';


const message = useMessage();
const appStore = useAppStore();
const { isMobile } = useBasicLayout();
const codeEditorRef = ref<InstanceType<typeof CodeEditor> | null>(null);
const router = useRouter();



// 控制分享与保存菜单展开/收起的状态
const showShareSaveControls = ref(false);
const shareSaveControlsRef = ref<HTMLElement | null>(null);

// 控制设备菜单展开/收起的状态
const showDeviceControls = ref(false);
const deviceControlsRef = ref<HTMLElement | null>(null);

// 控制主菜单展开/收起的状态
const showMainMenu = ref(false);
const mainMenuRef = ref<HTMLElement | null>(null);

// 切换设备控制菜单的显示状态
const toggleDeviceControls = () => {
    showDeviceControls.value = !showDeviceControls.value;
    // 如果打开设备控制菜单，确保分享与保存菜单是关闭的
    if (showDeviceControls.value) {
        showShareSaveControls.value = false;
    }
};

// 关闭设备控制菜单
const closeDeviceControls = () => {
    showDeviceControls.value = false;
};

// 切换分享与保存菜单的显示状态
const toggleShareSaveControls = () => {
    showShareSaveControls.value = !showShareSaveControls.value;
    // 如果打开分享与保存菜单，确保设备控制菜单是关闭的
    if (showShareSaveControls.value) {
        showDeviceControls.value = false;
    }
};

// 关闭分享与保存菜单
const closeShareSaveControls = () => {
    showShareSaveControls.value = false;
};

// 切换主菜单的显示状态
const toggleMainMenu = () => {
    showMainMenu.value = !showMainMenu.value;
    // 如果打开主菜单，确保其他控制菜单是关闭的
    if (showMainMenu.value) {
        showDeviceControls.value = false;
        showShareSaveControls.value = false;
    }
};

// 关闭主菜单
const closeMainMenu = () => {
    showMainMenu.value = false;
};

// 点击外部关闭设备控制菜单
const handleOutsideClick = (event: MouseEvent) => {
    if (showDeviceControls.value && deviceControlsRef.value && !deviceControlsRef.value.contains(event.target as Node)) {
        showDeviceControls.value = false;
    }

    if (showShareSaveControls.value && shareSaveControlsRef.value && !shareSaveControlsRef.value.contains(event.target as Node)) {
        showShareSaveControls.value = false;
    }
};

// 点击外部关闭主菜单
const handleOutsideClickForMainMenu = (event: MouseEvent) => {
    if (showMainMenu.value && mainMenuRef.value && !mainMenuRef.value.contains(event.target as Node)) {
        showMainMenu.value = false;
    }
};

// 监听点击事件
onMounted(() => {
    document.addEventListener('click', handleOutsideClick);
    document.addEventListener('click', handleOutsideClickForMainMenu);
});

onUnmounted(() => {
    document.removeEventListener('click', handleOutsideClick);
    document.removeEventListener('click', handleOutsideClickForMainMenu);
});

// 导航到聊天页面函数
const navigateToChat = () => {
    router.push('/chat');
};



// 下载HTML函数
const downloadHTML = () => {
    if (!editableCode.value || editableCode.value.trim() === '') {
        message.error('HTML内容不能为空，请输入内容后再下载');
        return;
    }

    try {
        // 创建Blob对象
        const blob = new Blob([editableCode.value], { type: 'text/html' });
        // 创建URL
        const url = URL.createObjectURL(blob);
        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = 'deepcreate_project.html';
        // 添加到文档并触发点击
        document.body.appendChild(link);
        link.click();
        // 清理
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        message.success('HTML文件下载成功');
        closeShareSaveControls();
    } catch (error: any) {
        console.error('下载HTML失败:', error);
        message.error('下载HTML失败，请稍后再试');
    }
};

// HTML内容初始示例
const initialHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DeepCreate</title>
  <style>
    :root {
      --primary: #4361ee;
      --secondary: #3a0ca3;
      --background: #050816;
      --text: #ffffff;
      --accent: #915eff;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', system-ui, sans-serif;
      background-color: var(--background);
      color: var(--text);
      overflow: hidden;
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .canvas-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      opacity: 0.8;
    }

    .content {
      z-index: 10;
      text-align: center;
      position: relative;
      transform-style: preserve-3d;
      perspective: 1000px;
    }

    .title {
      font-size: clamp(2rem, 8vw, 5rem);
      font-weight: 900;
      margin-bottom: 1rem;
      position: relative;
      text-transform: uppercase;
      letter-spacing: -1px;
      display: inline-block;
      background: linear-gradient(90deg, var(--primary), var(--accent));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      transform: translateZ(50px);
      animation: float 6s ease-in-out infinite, reveal 2s ease forwards;
      opacity: 0;
    }

    .subtitle {
      font-size: clamp(1.2rem, 3vw, 2rem);
      font-weight: 400;
      margin-top: 0.5rem;
      text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      opacity: 0;
      transform: translateZ(25px);
      animation: slideUp 1.5s 0.5s forwards, float 6s ease-in-out 1s infinite;
    }

    .glow {
      position: absolute;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, var(--accent) 0%, rgba(0,0,0,0) 70%);
      border-radius: 50%;
      opacity: 0.15;
      z-index: -1;
      pointer-events: none;
      transition: transform 0.3s ease, opacity 0.3s ease;
    }

    .title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      width: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary), var(--accent));
      transform: translateX(-50%);
      animation: expandLine 1.5s 1s forwards;
    }

    @keyframes float {
      0%, 100% { transform: translateZ(50px) translateY(0); }
      50% { transform: translateZ(50px) translateY(-10px); }
    }

    @keyframes slideUp {
      from { transform: translateZ(25px) translateY(20px); opacity: 0; }
      to { transform: translateZ(25px) translateY(0); opacity: 1; }
    }

    @keyframes reveal {
      from { opacity: 0; clip-path: polygon(0 0, 0 0, 0 100%, 0 100%); }
      to { opacity: 1; clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); }
    }

    @keyframes expandLine {
      from { width: 0; }
      to { width: 60%; }
    }

    /* Particle dots in background */
    .dots {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1;
    }

    .dot {
      position: absolute;
      width: 3px;
      height: 3px;
      background: white;
      border-radius: 50%;
      opacity: 0.3;
      animation: moveDot 20s linear infinite;
    }

    @keyframes moveDot {
      0% { transform: translateY(0) translateX(0); opacity: 0; }
      10% { opacity: 0.5; }
      90% { opacity: 0.5; }
      100% { transform: translateY(-100vh) translateX(100px); opacity: 0; }
    }
  </style>
</head>
<body>
  <div class="canvas-container" id="canvas"></div>

  <div class="dots">
    <!-- Particles will be added by JS -->
  </div>

  <div class="content">
    <h1 class="title">DeepCreate</h1>
    <p class="subtitle">创造无限可能</p>
  </div>

  <div class="glow" id="glow"></div>

  <script>
    // Create dots effect
    const dotsContainer = document.querySelector('.dots');
    const dotsCount = 50;

    for (let i = 0; i < dotsCount; i++) {
      const dot = document.createElement('div');
      dot.classList.add('dot');
      dot.style.left = \`\${Math.random() * 100}%\`;
      dot.style.top = \`\${Math.random() * 100}%\`;
      dot.style.animationDuration = \`\${15 + Math.random() * 15}s\`;
      dot.style.animationDelay = \`\${Math.random() * 5}s\`;
      dotsContainer.appendChild(dot);
    }

    // SVG background animation with minimal code
    const canvas = document.getElementById('canvas');

    // Create SVG waves
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');
    svg.style.position = 'absolute';
    canvas.appendChild(svg);

    // Create gradient
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    svg.appendChild(defs);

    const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    gradient.setAttribute('id', 'wave-gradient');
    gradient.setAttribute('x1', '0%');
    gradient.setAttribute('y1', '0%');
    gradient.setAttribute('x2', '100%');
    gradient.setAttribute('y2', '100%');
    defs.appendChild(gradient);

    const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('stop-color', '#4361ee');
    stop1.setAttribute('stop-opacity', '0.3');
    gradient.appendChild(stop1);

    const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('offset', '100%');
    stop2.setAttribute('stop-color', '#915eff');
    stop2.setAttribute('stop-opacity', '0.3');
    gradient.appendChild(stop2);

    // Create two wave paths
    for (let i = 0; i < 2; i++) {
      const wave = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      wave.setAttribute('fill', 'url(#wave-gradient)');
      wave.setAttribute('opacity', i === 0 ? '0.7' : '0.5');
      svg.appendChild(wave);

      animateWave(wave, i);
    }

    function animateWave(wave, index) {
      const startTime = Date.now();
      const speed = index === 0 ? 8000 : 10000; // Different speeds for each wave
      const heightAdjust = index === 0 ? 0.6 : 0.8;

      function updateWave() {
        const time = (Date.now() - startTime) / speed;
        const width = window.innerWidth;
        const height = window.innerHeight;

        let d = \`M 0 \${height * 0.5}\`;

        for (let x = 0; x <= width; x += 20) {
          // Different wave patterns for each wave
          const y = Math.sin(x * 0.01 + time * 2 * Math.PI) * 50 * heightAdjust + height * 0.5;
          d += \` L \${x} \${y}\`;
        }

        d += \` L \${width} \${height} L 0 \${height} Z\`;
        wave.setAttribute('d', d);

        requestAnimationFrame(updateWave);
      }

      updateWave();
    }

    // Mouse follow effect for the glow
    const glow = document.getElementById('glow');
    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    // Touch support
    document.addEventListener('touchmove', (e) => {
      mouseX = e.touches[0].clientX;
      mouseY = e.touches[0].clientY;
    });

    function updateGlow() {
      glow.style.transform = \`translate(\${mouseX - 100}px, \${mouseY - 100}px)\`;
      requestAnimationFrame(updateGlow);
    }

    updateGlow();

    // 3D effect on mousemove
    const content = document.querySelector('.content');
    const title = document.querySelector('.title');
    const subtitle = document.querySelector('.subtitle');

    document.addEventListener('mousemove', (e) => {
      const x = e.clientX;
      const y = e.clientY;

      // Calculate rotation based on mouse position
      const rotateX = (y - window.innerHeight / 2) * 0.01;
      const rotateY = (window.innerWidth / 2 - x) * 0.01;

      content.style.transform = \`rotateX(\${rotateX}deg) rotateY(\${rotateY}deg)\`;
    });
  <\/script>
</body>
</html>`;

const editableCode = ref(initialHtml);

// 使用设备预览相关逻辑
const {
    deviceType,
    isLandscape,
    previewContainerClass,
    changeDeviceType,
    toggleOrientation: originalToggleOrientation,
    calculateDeviceScale
} = useDevicePreview();

// 使用代码格式化逻辑
const { prettifyHTML } = useCodeFormat();

// 使用全屏模式逻辑
const {
    isFullscreen,
    toggleFullscreen,
    fullscreenContainerClass
} = useFullscreen(deviceType, isLandscape);

// 使用结合全屏模式的设备样式
const {
    currentDeviceStyle,
    calculateFullscreenScale
} = useDeviceWithFullscreen(deviceType, isLandscape, isFullscreen);





// 检测暗黑模式
const isDarkMode = computed(() => {
    return appStore.theme === 'dark';
});

// 刷新预览
const refreshPreview = () => {
    // 调用DevicePreview组件的刷新方法
};

// 格式化代码
const formatCode = () => {
    try {
        // 使用CodeEditor组件的格式化方法
        codeEditorRef.value?.formatCode(prettifyHTML);

        // 显示成功消息
        message.success('代码格式化成功');
    } catch (err) {
        console.error('格式化失败:', err);
        message.error('代码格式化失败');
    }
};

// 重写toggleOrientation函数，添加刷新功能
const toggleOrientation = () => {
    // 调用原始函数
    originalToggleOrientation();

    // 给iframe内容添加延迟，让旋转动画更流畅
    setTimeout(() => {
        refreshPreview();
    }, 300); // 与旋转动画时长匹配
};

// 处理预览容器尺寸变化
const handleContainerResize = (size: { width: number, height: number }) => {
    if (isFullscreen.value) {
        // 全屏模式下计算最佳缩放比例
        const scale = calculateFullscreenScale(size.width, size.height);
        const previewContainer = document.getElementById('preview-container');
        if (previewContainer) {
            previewContainer.style.setProperty('--device-scale', scale.toString());
        }
    } else {
        // 非全屏模式下计算设备缩放比例
        calculateDeviceScale(size.width, size.height);
    }
};
</script>

<style lang="less" scoped>
.device-frame {
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform-origin: center center;
    max-height: 100%;
    max-width: 100%;
}

/* 为横竖屏切换添加特殊样式 */
.orientation-change {
    transition: all 0.3s ease-in-out;
}

/* 全局确保平板设备在所有情况下不超出屏幕 */
.tablet-device {
    /* 通用缩放以防其他媒体查询未生效 */
    transform: scale(0.5) !important;
    transform-origin: center center !important;
}

/* 支持动态缩放的平板设备类 */
.tablet-device.auto-scaled-device {
    transform: scale(var(--device-scale, 0.5)) !important;
}

@media (min-height: 900px) {
    .tablet-device:not(.landscape):not(.auto-scaled-device) {
        transform: scale(0.65) !important;
    }
}

@media (max-height: 850px) and (min-height: 801px) {
    .tablet-device:not(.landscape):not(.auto-scaled-device) {
        transform: scale(0.55) !important;
    }
}

@media (max-height: 800px) and (min-height: 701px) {
    .tablet-device:not(.landscape):not(.auto-scaled-device) {
        transform: scale(0.45) !important;
    }
}

@media (max-height: 700px) and (min-height: 601px) {
    .tablet-device:not(.landscape):not(.auto-scaled-device) {
        transform: scale(0.40) !important;
    }
}

@media (max-height: 600px) {
    .tablet-device:not(.landscape):not(.auto-scaled-device) {
        transform: scale(0.35) !important;
    }
}

/* 移动设备样式 */
.mobile-device {
    border-radius: 36px;
    border: 8px solid #333;
    background-color: #333;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.25),
        inset 0 0 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    max-height: calc(100% - 20px);
    max-width: 100%;
    margin: 0 auto;
    animation: float 6s ease-in-out infinite;
    will-change: transform;

    /* 微妙的悬浮效果 */
    @keyframes float {

        0%,
        100% {
            transform: translateY(0) rotateX(0);
        }

        50% {
            transform: translateY(-5px) rotateX(2deg);
        }
    }

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 26px;
        background-color: #222;
        border-radius: 0 0 18px 18px;
        z-index: 1;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    &.landscape {
        border-radius: 36px;
        transform-origin: center center;
        animation: float-landscape 6s ease-in-out infinite;

        /* 横屏时的悬浮效果 */
        @keyframes float-landscape {

            0%,
            100% {
                transform: translateY(0) rotateY(0);
            }

            50% {
                transform: translateY(-5px) rotateY(2deg);
            }
        }

        &:before {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 26px;
            height: 120px;
            border-radius: 0 18px 18px 0;
        }
    }

    .dark & {
        border-color: #222;
        background-color: #222;

        &:before {
            background-color: #181818;
        }
    }

    /* 全屏模式下的特殊样式 */
    .fullscreen-container & {
        transform-origin: center center;
        margin: 0 auto;
        animation: none;
    }
}

/* 全屏模式下的横竖屏修正 */
.fullscreen-container .preview-frame.landscape {
    transform-origin: center;
}

/* 平板设备样式 */
.tablet-device {
    border-radius: 20px;
    border: 12px solid #333;
    background-color: #333;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.15),
        0 12px 24px rgba(0, 0, 0, 0.2),
        inset 0 0 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 0 auto;
    transform-origin: center center;
    animation: float-tablet 8s ease-in-out infinite;
    will-change: transform;

    /* 平板的悬浮效果 */
    @keyframes float-tablet {

        0%,
        100% {
            transform: translateY(0) rotateX(0);
        }

        50% {
            transform: translateY(-6px) rotateX(1deg);
        }
    }

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 8px;
        background-color: #222;
        border-radius: 0 0 10px 10px;
        z-index: 1;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    &.landscape {
        border-radius: 20px;
        transform-origin: center center;
        animation: float-tablet-landscape 8s ease-in-out infinite;

        /* 横屏平板的悬浮效果 */
        @keyframes float-tablet-landscape {

            0%,
            100% {
                transform: translateY(0) rotateY(0);
            }

            50% {
                transform: translateY(-6px) rotateY(1deg);
            }
        }

        &:before {
            top: 50%;
            left: 0;
            transform: translateY(-50%);
            width: 8px;
            height: 60px;
            border-radius: 0 10px 10px 0;
        }
    }

    .dark & {
        border-color: #222;
        background-color: #222;

        &:before {
            background-color: #181818;
        }
    }

    /* 全屏模式下移除动画 */
    .fullscreen-container & {
        animation: none;
    }
}

/* 桌面设备样式 */
.desktop-frame {
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: white;
    position: relative;
    transition: all 0.3s ease;

    /* 微妙的悬浮阴影效果 */
    &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* 添加顶部控制条 */
    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 28px;
        background-color: #f1f1f1;
        border-bottom: 1px solid #ddd;
        border-radius: 8px 8px 0 0;
        z-index: 2;
    }

    /* 添加窗口控制按钮 */
    &:after {
        content: '';
        position: absolute;
        top: 9px;
        left: 10px;
        width: 46px;
        height: 10px;
        z-index: 3;
        background-image: radial-gradient(circle, #ff5f57 5px, #ff5f57 5px, transparent 5px),
            radial-gradient(circle, #ffbd2e 5px, #ffbd2e 5px, transparent 5px),
            radial-gradient(circle, #28c940 5px, #28c940 5px, transparent 5px);
        background-position: 0 0, 16px 0, 32px 0;
        background-repeat: no-repeat;
    }

    /* 调整iframe位置适应窗口标题栏 */
    iframe {
        position: absolute;
        top: 28px;
        left: 0;
        width: 100%;
        height: calc(100% - 28px);
        border: none;
        background-color: white;
        z-index: 1;
        scrollbar-width: thin;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
    }

    .dark & {
        border-color: #444;
        background-color: #222;

        &:before {
            background-color: #2a2a2a;
            border-color: #444;
        }

        iframe {
            background-color: #222;
        }
    }
}

/* 旋转时的过渡效果 */
.portrait,
.landscape {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.landscape {
    transform: rotate(0deg); // 重置可能的旋转
}

/* 设备自动缩放模式 */
.auto-scaled-device {
    transition: transform 0.35s cubic-bezier(0.34, 1.56, 0.64, 1); // 弹性缓动
}

/* 设备切换动画 */
.device-change-enter-active,
.device-change-leave-active {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.device-change-enter-from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
}

.device-change-leave-to {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
}

/* 横竖屏切换动画 */
.rotate-enter-active,
.rotate-leave-active {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: center center;
}

.rotate-enter-from {
    opacity: 0;
    transform: rotate(90deg) scale(0.8);
}

.rotate-leave-to {
    opacity: 0;
    transform: rotate(-90deg) scale(0.8);
}

.rotating {
    animation: device-rotate 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes device-rotate {
    0% {
        transform: rotate(0) scale(1);
    }

    50% {
        transform: rotate(45deg) scale(0.9);
    }

    100% {
        transform: rotate(90deg) scale(1);
    }
}

/* 全屏模式过渡 */
.fullscreen-transition-enter-active,
.fullscreen-transition-leave-active {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.fullscreen-transition-enter-from,
.fullscreen-transition-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

/* 固定视口布局相关样式 */
.fixed-viewport {
    .device-preview-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
}

/* 确保在固定高度的布局中内容都适应 */
.h-screen {

    .CodeEditor,
    .DevicePreview {
        max-height: 100%;
    }
}

/* 设备预览封装器 */
.device-preview-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
        z-index: -1;
    }

    .dark & {
        &::before {
            background: radial-gradient(circle at center, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
        }
    }
}

/* 设备控制按钮 */
.device-button {
    background-color: #8b5cf6;

    .dark & {
        background-color: #a78bfa;
    }
}

/* 屏幕方向变换动画 */
.rotate-enter-active,
.rotate-leave-active {
    transition: all 0.5s ease;
}

.rotate-enter-from,
.rotate-leave-to {
    transform: rotate(90deg) scale(0.9);
    opacity: 0;
}

/* 设备切换动画 */
.device-change-enter-active,
.device-change-leave-active {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.device-change-enter-from,
.device-change-leave-to {
    transform: scale(0.9);
    opacity: 0;
}

/* 旋转动画 */
.rotating {
    animation: device-rotate 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes device-rotate {
    0% {
        transform: rotate(0) scale(1);
    }

    50% {
        transform: rotate(45deg) scale(0.9);
    }

    100% {
        transform: rotate(90deg) scale(1);
    }
}

/* 全屏模式过渡 */
.fullscreen-transition-enter-active,
.fullscreen-transition-leave-active {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.fullscreen-transition-enter-from,
.fullscreen-transition-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

/* 悬浮按钮组样式 */
.floating-menu {
    z-index: 70;
    /* 大于全屏容器的z-index(60)，确保在所有元素之上 */
    display: flex;
    flex-direction: column;
    gap: 10px;

    /* 全屏模式下调整悬浮按钮位置 */
    .fullscreen-container & {
        bottom: 20px;
        right: 20px;
    }
}

/* 悬浮按钮基础样式 */
.floating-button {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
    background-color: #4f46e5;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
        filter: brightness(1.1);
    }

    &:active {
        transform: translateY(0);
        filter: brightness(0.95);
    }

    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    /* 暗色模式调整 */
    .dark & {
        background-color: #6366f1;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 图标样式统一 */
    svg {
        width: 20px;
        height: 20px;
        stroke-width: 2px;
    }
}

/* 功能按钮样式 - 使用不同的色调而非渐变 */
.chat-button {
    background-color: #6366f1;

    .dark & {
        background-color: #818cf8;
    }
}

.format-button {
    background-color: #4f46e5;

    .dark & {
        background-color: #6366f1;
    }
}

.share-button {
    background-color: #0ea5e9;

    .dark & {
        background-color: #38bdf8;
    }
}

.fullscreen-button {
    background-color: #10b981;

    .dark & {
        background-color: #34d399;
    }
}

.device-button {
    background-color: #8b5cf6;

    .dark & {
        background-color: #a78bfa;
    }
}

.projects-button {
    background-color: #f59e0b;

    .dark & {
        background-color: #fbbf24;
    }
}

/* 设备控制菜单样式 */
.device-controls-menu, .share-save-controls-menu {
    z-index: 75;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    border-radius: 12px;

    .dark & {
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    }
}

/* 设备选择按钮样式 */
.device-selector-button {
    &:not(:last-child) {
        margin-right: 1px;
    }
}

/* 活跃状态的设备按钮样式 */
.active-device,
.active-orientation {
    background-color: white;
    color: #4f46e5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-weight: 500;

    .dark & {
        background-color: #1e293b;
        color: #6366f1;
    }

    svg {
        stroke: currentColor;
    }
}

/* 非活跃状态的设备按钮样式 */
.inactive-device,
.inactive-orientation {
    color: #64748b;

    .dark & {
        color: #94a3b8;
    }

    &:hover {
        color: #334155;
        background-color: rgba(255, 255, 255, 0.5);

        .dark & {
            color: #cbd5e1;
            background-color: rgba(255, 255, 255, 0.05);
        }
    }
}

/* 设备控制选择器组样式 */
.device-selector-group,
.orientation-selector-group {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;

    .dark & {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
}

/* 下拉菜单动画 */
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.3s ease;
    transform-origin: bottom right;
}

.slide-up-enter-from,
.slide-up-leave-to {
    transform: scale(0.95) translateY(10px);
    opacity: 0;
}

/* 主按钮样式 */
.main-button {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    background-color: #4f46e5;
    color: white;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 72;
    position: relative;

    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 4px 10px rgba(0, 0, 0, 0.15);
        filter: brightness(1.1);
    }

    &:active {
        transform: translateY(-1px);
        filter: brightness(0.95);
    }

    svg {
        transition: transform 0.3s ease;
        width: 24px;
        height: 24px;
    }

    .dark & {
        background-color: #6366f1;
    }
}

/* 主按钮旋转效果 */
.main-button svg {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

.main-button.active svg {
    transform: rotate(45deg);
}

/* 菜单容器样式 */
.menu-buttons {
    min-width: 240px;
}

/* 按钮标签样式 */
.button-label {
    transition: all 0.3s ease;
    color: #4b5563;
    font-weight: 500;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);

    .dark & {
        color: #e5e7eb;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    &.show-label {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 菜单项动画延迟 */
.menu-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transition-delay: calc(var(--index, 0) * 0.05s);
    transform: scale(0);
    opacity: 0;
    transform-origin: center right;

    &.show-item {
        transform: scale(1);
        opacity: 1;
    }
}

/* 菜单展开动画 */
.scale-up-enter-active,
.scale-up-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom right;
}

.scale-up-enter-from,
.scale-up-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

/* 设备控制菜单位置调整 */
.device-controls-menu, .share-save-controls-menu {
    z-index: 75;
}

/* 分享与保存按钮样式 */
.share-save-button {
    background-color: #3b82f6;
    color: white;
    transition: all 0.3s ease;

    &:hover {
        background-color: #2563eb;
        transform: translateY(-2px);
        filter: brightness(1.1);
    }

    &:active {
        transform: translateY(0);
        filter: brightness(0.95);
    }

    .dark & {
        background-color: #4f46e5;
    }
}

/* 子菜单按钮样式 */
.save-project-button, .share-button, .download-button {
    transition: all 0.2s ease;
    border-radius: 8px;

    &:hover {
        transform: translateY(-2px);
        background-color: #f3f4f6;
    }

    &:active {
        transform: translateY(0);
        background-color: #e5e7eb;
    }

    .dark & {
        &:hover {
            background-color: #374151;
        }

        &:active {
            background-color: #4b5563;
        }
    }

    svg {
        transition: transform 0.2s ease;
    }

    &:hover svg {
        transform: scale(1.1);
    }
}

/* 固定视口布局相关样式 */
.fixed-viewport {
    .preview-frame {
        max-height: 100%;
        max-width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        scale: 0.95;
        /* 稍微缩小以确保在容器内完全可见 */
    }

    /* 移动设备在固定视口内的显示调整 */
    .mobile-device {
        @media (max-height: 700px) {
            scale: 0.85;
            /* 在较小屏幕上进一步缩小 */
        }
    }

    /* 平板设备竖屏在固定视口的特殊处理 - 使用外部的响应式缩放 */
    .tablet-device:not(.landscape) {
        margin: 0 auto;
        max-height: 90vh !important;
        transform-origin: center center !important;

        /* 确保使用动态缩放 */
        &.auto-scaled-device {
            height: 90vh !important;
            transform: scale(var(--device-scale, 0.5)) !important;
        }
    }
}

/* 确保在固定高度的布局中内容都适应 */
.h-screen {

    .CodeEditor,
    .DevicePreview {
        max-height: 100%;
    }
}
</style>