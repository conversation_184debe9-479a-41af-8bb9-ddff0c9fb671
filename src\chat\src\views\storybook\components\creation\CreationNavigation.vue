<template>
  <div class="creation-navigation">
    <button
      v-if="currentStep > 1"
      class="nav-btn back-btn"
      @click="$emit('previous')"
    >
      <span class="btn-icon">⬅️</span> 上一步
    </button>
    <div v-else class="nav-spacer"></div>

    <!-- 中间插槽用于放置步骤条 -->
    <div class="steps-container">
      <slot name="steps"></slot>
    </div>

    <button
      class="nav-btn next-btn"
      @click="$emit('next')"
      :disabled="currentStep === totalSteps"
    >
      {{ currentStep === totalSteps ? '完成创作' : '下一步' }}
      <span class="btn-icon">{{ currentStep === totalSteps ? '🎉' : '➡️' }}</span>
    </button>
  </div>
</template>

<script setup>
const props = defineProps({
  currentStep: {
    type: Number,
    required: true
  },
  totalSteps: {
    type: Number,
    required: true
  }
});

defineEmits(['next', 'previous']);
</script>

<style scoped>
.creation-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  position: relative;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.25rem;
  border-radius: 0.75rem;
  font-size: 0.9rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  z-index: 1;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  z-index: -1;
}

.back-btn {
  background-color: #f1f5f9;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.dark .back-btn {
  background-color: #334155;
  color: #94a3b8;
  border-color: #475569;
}

.back-btn:hover {
  background-color: #e2e8f0;
  color: #475569;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.dark .back-btn:hover {
  background-color: #475569;
  color: #e2e8f0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.back-btn:active {
  transform: translateY(-1px);
}

.next-btn {
  background-color: #3b82f6;
  color: white;
  border: 1px solid #60a5fa;
}

.dark .next-btn {
  background-color: #60a5fa;
  color: #1e293b;
  border-color: #3b82f6;
}

.next-btn:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.4);
}

.dark .next-btn:hover {
  background-color: #93c5fd;
  box-shadow: 0 6px 15px rgba(96, 165, 250, 0.4);
}

.next-btn:active {
  transform: translateY(-1px);
}

.next-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.dark .next-btn:disabled {
  background-color: #1e40af;
  opacity: 0.7;
}

.btn-icon {
  margin: 0 0.3rem;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.back-btn:hover .btn-icon {
  transform: translateX(-2px);
}

.next-btn:hover .btn-icon {
  transform: translateX(2px);
}

.nav-spacer {
  min-width: 120px;
}

.steps-container {
  flex: 1;
  margin: 0 1rem;
}
</style>
