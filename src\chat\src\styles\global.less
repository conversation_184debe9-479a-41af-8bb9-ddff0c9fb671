/* 引入 Tailwind CSS 的基础、组件和工具类 */
// @import 'tailwindcss/base';
// @import 'tailwindcss/components';
// @import 'tailwindcss/utilities';

/* 引入绘本创作步骤统一样式 */
@import './story-creation-steps.less';

html,
body,
#app {
  height: 100%;
  overflow: hidden;
}

// div {
//   @apply text-gray-950 dark:text-gray-100;
// }

body {
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, Ubuntu, Cantarell, "Noto Sans", sans-serif, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  @apply text-gray-950 dark:text-gray-100 !important;
  background-image: radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.05) 1px, transparent 0);
  background-size: 24px 24px;
}

.dark body {
  background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.05) 1px, transparent 0);
  background-size: 24px 24px;
}

// 全局焦点样式
* :focus {
  outline: none;
}

/* 确保三栏布局的一致性 */
.n-layout-sider {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.n-layout {
  background-color: transparent !important;
}

/* 确保三栏之间的间距一致 */
.n-layout-sider:not(.n-layout-sider--collapsed) + div {
  padding-left: 12px; /* 与 gap-3 保持一致，3 * 4px = 12px */
}

/* 侧边栏收起时去除左侧间距 */
.n-layout-sider.n-layout-sider--collapsed + div {
  padding-left: 0;
}

/* 确保三栏布局的容器样式一致 */
.n-layout {
  gap: 0 !important; /* 移除默认间距，我们在内部元素中控制间距 */
}

.n-layout-sider {
  margin-right: 0 !important;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 侧边栏收起时的样式 */
.n-layout-sider--collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  opacity: 0;
}

/* 确保所有卡片具有一致的圆角和阴影 */
.chat-container, .preview-container, .sider-container {
  border-radius: 0.5rem;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.dark .chat-container, .dark .preview-container, .dark .sider-container {
  background-color: #1f2937;
}

// 选中文本样式
::selection {
  color: #fff;
  background: var(--primary-color, #4f46e5);
}

/* 悬停效果 */
.hover-float {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.dark .hover-float:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark :hover::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 按钮动画效果 */
button {
  transition: all 0.3s ease;
}

/* 输入框动画效果 */
input, textarea {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
}

input:focus, textarea:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 卡片动画效果 */
.card, .message-container, .shadow-sm, .shadow-md, .shadow-lg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 链接动画效果 */
a {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 图标动画效果 */
svg, img {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 列表项动画效果 */
li {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 下拉菜单动画效果 */
.dropdown, .menu {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 模态框动画效果 */
.modal, .dialog {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 分享模态框样式 - 全局设置 */
/* 注意：主要样式已移至组件内部 */

/* 提示框动画效果 */
.tooltip, .popover {
  transition: all 0.3s ease;
}

/* 标签动画效果 */
.tag, .badge {
  transition: all 0.3s ease;
}

/* 表单元素动画效果 */
.form-control, .input-group {
  transition: all 0.3s ease;
}

/* 导航动画效果 */
.nav, .navbar {
  transition: all 0.3s ease;
}

/* 表格动画效果 */
table, tr, td, th {
  transition: all 0.3s ease;
}

/* 卡片悬停效果 */
.card-hover {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);
}

.dark .card-hover:hover {
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 按钮悬停效果 */
.btn-hover {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .btn-hover:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.btn-hover:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 悬浮效果 */
.hover-float {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.dark .hover-float:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

/* 图片悬停效果 */
.img-hover {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.img-hover:hover {
  transform: scale(1.05);
  filter: brightness(1.05);
}

/* 链接悬停效果 */
.link-hover {
  transition: color 0.3s ease, text-decoration 0.3s ease;
}

.link-hover:hover {
  text-decoration: underline;
}

/* 图标旋转动画 */
.icon-spin {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 呼吸动画效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑入动画 */
.slide-in {
  animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* 滑入右侧动画 */
.slide-in-right {
  animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* 滑入左侧动画 */
.slide-in-left {
  animation: slideInLeft 0.5s ease-in-out;
}

@keyframes slideInLeft {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* 缩放动画 */
.scale-in {
  animation: scaleIn 0.5s ease-in-out;
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 弹跳动画 */
.bounce {
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

/* 闪烁动画 */
.blink {
  animation: blink 1s ease-in-out infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 波纹动画 */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.5s, opacity 1s;
}

.ripple:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* 阴影效果 */
.ds-shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ds-shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ds-shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.dark .ds-shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .ds-shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.dark .ds-shadow-lg {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
}
