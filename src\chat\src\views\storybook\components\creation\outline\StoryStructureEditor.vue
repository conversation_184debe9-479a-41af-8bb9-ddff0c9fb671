<template>
  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">📖</span>
      讲讲你的故事吧！
      <div class="example-story-btn">
        <button
          ref="buttonRef"
          type="button"
          class="load-example-btn"
          @click.stop="showExampleStories = !showExampleStories"
        >
          <span class="btn-icon">💡</span> 加载示例故事
        </button>
        <div
          ref="dropdownRef"
          class="example-stories-dropdown"
          v-if="showExampleStories"
          @click.stop
        >
          <div class="dropdown-header">
            <span class="dropdown-title">选择一个示例故事</span>
          </div>
          <div
            v-for="(story, index) in exampleStories"
            :key="index"
            class="example-story-item"
            @click="loadExampleStory(story)"
          >
            <span class="story-icon">{{ story.icon }}</span>
            <span class="story-title">{{ story.title }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="story-structure">
      <div class="structure-part animated-part">
        <div class="part-label">
          <span class="part-number">1</span>
          <span>故事是怎么开始的？</span>
        </div>
        <div class="story-starters">
          <button
            v-for="starter in storyStarters"
            :key="starter"
            type="button"
            class="story-starter-btn"
            @click="beginning = starter"
          >
            {{ starter }}
          </button>
        </div>
        <NInput
          v-model:value="beginning"
          type="textarea"
          placeholder="故事是如何开始的？介绍主角和故事背景"
          :autosize="{ minRows: 3, maxRows: 5 }"
          class="child-input"
        />
      </div>

      <div class="structure-connector">
        <div class="connector-line"></div>
        <div class="connector-arrow">⬇️</div>
      </div>

      <div class="structure-part animated-part">
        <div class="part-label">
          <span class="part-number">2</span>
          <span>接下来发生了什么？</span>
        </div>
        <NInput
          v-model:value="middle"
          type="textarea"
          placeholder="主角遇到了什么问题或挑战？发生了什么有趣的事情？"
          :autosize="{ minRows: 3, maxRows: 5 }"
          class="child-input"
        />
      </div>

      <div class="structure-connector">
        <div class="connector-line"></div>
        <div class="connector-arrow">⬇️</div>
      </div>

      <div class="structure-part animated-part">
        <div class="part-label">
          <span class="part-number">3</span>
          <span>最精彩的部分是什么？</span>
        </div>
        <NInput
          v-model:value="climax"
          type="textarea"
          placeholder="故事中最紧张或最重要的时刻是什么？"
          :autosize="{ minRows: 3, maxRows: 5 }"
          class="child-input"
        />
      </div>

      <div class="structure-connector">
        <div class="connector-line"></div>
        <div class="connector-arrow">⬇️</div>
      </div>

      <div class="structure-part animated-part">
        <div class="part-label">
          <span class="part-number">4</span>
          <span>故事是怎么结束的？</span>
        </div>
        <NInput
          v-model:value="ending"
          type="textarea"
          placeholder="故事如何结束？主角学到了什么？"
          :autosize="{ minRows: 3, maxRows: 5 }"
          class="child-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NInput } from 'naive-ui';

// 故事开头选项
const storyStarters = [
  '很久很久以前，在一个美丽的森林里...',
  '从前有一个小朋友，他的名字叫...',
  '在一个神奇的王国里，住着...'
];

// 示例故事下拉菜单状态
const showExampleStories = ref(false);
const dropdownRef = ref(null);
const buttonRef = ref(null);

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  if (
    showExampleStories.value &&
    dropdownRef.value &&
    !dropdownRef.value.contains(event.target) &&
    buttonRef.value &&
    !buttonRef.value.contains(event.target)
  ) {
    showExampleStories.value = false;
  }
};

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除点击事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 示例故事列表
const exampleStories = [
  {
    icon: '🐰',
    title: '小兔子的勇气',
    beginning: '在一片郁郁葱葱的森林里，住着一只名叫跳跳的小白兔。跳跳非常胆小，总是害怕尝试新事物，每天只敢在自己的小窝附近活动。',
    middle: '一天，森林里举办了一场寻宝比赛。所有动物都很兴奋，但跳跳却不敢参加。他的好朋友小松鼠鼓励他："跳跳，这是个锻炼勇气的好机会！"在朋友的鼓励下，跳跳决定尝试一下。',
    climax: '比赛开始后，跳跳需要穿过一条湍急的小溪。他站在溪边，腿发抖，几乎要放弃。这时，他想起了朋友的鼓励，深吸一口气，鼓起勇气一跃而过！虽然他的脚被水打湿了，但他成功了！',
    ending: '最终，跳跳没有找到宝藏，但他发现了更重要的东西——勇气。从那以后，跳跳变得更加勇敢，愿意尝试新事物。他明白了，有时候迈出第一步是最难的，但只要勇敢尝试，就能发现自己比想象中更强大。'
  },
  {
    icon: '🌈',
    title: '彩虹桥的秘密',
    beginning: '小镇上有一个传说，每当下雨后出现彩虹时，如果能找到彩虹的尽头，就会发现一个神奇的秘密。小女孩莉莉一直梦想着能找到彩虹的尽头。',
    middle: '一天下午，一场大雨过后，天空中出现了一道美丽的彩虹。莉莉立刻骑上自行车，追寻彩虹的方向。她穿过小镇，经过田野，来到了一片从未去过的森林。',
    climax: '在森林深处，莉莉发现彩虹似乎落在一个小湖泊上。当她走近湖边，彩虹的倒影在水中形成了一个完整的圆。就在这时，湖面上出现了七彩的光芒，照亮了整个湖泊！',
    ending: '莉莉惊讶地发现，彩虹的秘密不是什么宝藏，而是这个美丽的景象本身。她拍下照片，带回小镇与大家分享。从此，每当出现彩虹，小镇的人们都会一起去湖边欣赏这奇妙的景象。莉莉明白了，有时候最美的秘密就是大自然的奇迹，而分享这些奇迹能带给更多人快乐。'
  },
  {
    icon: '🐉',
    title: '友善的小龙',
    beginning: '在一座高高的山上，住着一条名叫火花的小龙。虽然火花能喷火，但他其实非常友善。可惜山下的村民们都害怕龙，从来没有人敢接近他，这让火花感到很孤独。',
    middle: '一天，一个名叫小明的男孩在森林里迷路了。天色已晚，小明又冷又怕。火花发现了小明，想要帮助他。但当火花靠近时，小明吓得直哭。火花不知道该怎么办，最后决定用自己的火焰点燃了一堆木柴，让小明能够取暖。',
    climax: '小明惊讶地发现这条龙并没有伤害他，反而在帮助他。渐渐地，小明不再害怕火花。他们开始交谈，成为了朋友。第二天，火花带着小明飞回了村庄。村民们一开始都很害怕，但小明告诉大家火花是如何救了他。',
    ending: '村民们终于明白了不应该仅仅因为外表或传说就判断他人。他们邀请火花参加村里的庆典，火花用他的火焰表演了精彩的烟火秀。从此以后，火花不再孤独，他成为了村庄的守护者，而村民们也学会了不要以貌取人，真正的友谊需要相互了解和信任。'
  },
  {
    icon: '🌟',
    title: '星星的礼物',
    beginning: '小女孩小雨非常喜欢观察夜空中的星星。每天晚上，她都会坐在窗前，数着星星，对它们说悄悄话。她总是想知道星星是什么样子的，它们是否能听到她说话。',
    middle: '一天晚上，小雨看到一颗流星划过天空。她闭上眼睛许愿："我希望能和星星做朋友。"奇妙的事情发生了，一个发着光的小点落在了她的窗台上。那是一颗会说话的小星星！小星星告诉小雨，它听到了她的心愿，特地来和她做朋友。',
    climax: '小星星告诉小雨，它可以带她去看宇宙中最美丽的景象，但只有一晚上的时间。小雨非常兴奋，她和小星星一起踏上了奇妙的旅程。他们飞过银河，看到了彩色的星云，还遇到了其他友好的星星。这是小雨做过的最美丽的梦。',
    ending: '天亮时，小雨发现自己回到了床上。窗台上有一颗闪闪发光的小石头，那是小星星留给她的礼物。虽然小雨不能每天都去太空旅行，但她知道只要抬头看天空，她的朋友们就在那里。这颗星星石头成为了她最珍贵的宝物，提醒她梦想和友谊的力量。'
  }
];

// 使用defineModel实现双向绑定
const beginning = defineModel('beginning');
const middle = defineModel('middle');
const climax = defineModel('climax');
const ending = defineModel('ending');

// 加载示例故事
const loadExampleStory = (story) => {
  beginning.value = story.beginning;
  middle.value = story.middle;
  climax.value = story.climax;
  ending.value = story.ending;
  showExampleStories.value = false;

  // 显示成功提示
  if (window.$message) {
    window.$message.success(`已加载"${story.title}"的故事内容`);
  }
};
</script>

<style scoped>
.form-group {
  margin-bottom: 2.5rem;
  width: 100%;
  position: relative;
}

.form-group.animated-form-group {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-label {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  justify-content: space-between;
  flex-wrap: wrap;
}

.dark .form-label {
  color: #60a5fa;
}

.form-icon {
  font-size: 1.5rem;
}

/* 示例故事按钮样式 */
.example-story-btn {
  position: relative;
  margin-left: auto;
}

.load-example-btn {
  background-color: #f0f9ff;
  border: 2px solid #bae6fd;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  color: #0369a1;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.load-example-btn:hover {
  background-color: #e0f2fe;
  border-color: #38bdf8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .load-example-btn {
  background-color: #0c4a6e;
  border-color: #0284c7;
  color: #7dd3fc;
}

.dark .load-example-btn:hover {
  background-color: #075985;
  border-color: #0ea5e9;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  font-size: 1.2rem;
}

/* 示例故事下拉菜单 */
.example-stories-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 0.75rem;
  margin-top: 0.5rem;
  z-index: 100;
  border: 2px solid #bae6fd;
  animation: fadeIn 0.3s ease-out;
}

.dark .example-stories-dropdown {
  background-color: #1e293b;
  border-color: #0284c7;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 下拉菜单标题 */
.dropdown-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .dropdown-header {
  border-bottom-color: #334155;
}

.dropdown-title {
  font-weight: 600;
  color: #3b82f6;
  font-size: 0.95rem;
}

.dark .dropdown-title {
  color: #60a5fa;
}

.example-story-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-story-item:hover {
  background-color: #f0f9ff;
  transform: translateX(5px);
}

.dark .example-story-item:hover {
  background-color: #0c4a6e;
}

.story-icon {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: #f0f9ff;
  border-radius: 50%;
  border: 2px solid #bae6fd;
}

.dark .story-icon {
  background-color: #0c4a6e;
  border-color: #0284c7;
}

.story-title {
  font-weight: 600;
  color: #0369a1;
  font-size: 1rem;
}

.dark .story-title {
  color: #7dd3fc;
}

.story-structure {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  background-color: #f8fafc;
  padding: 1.5rem;
  border-radius: 1rem;
  border: 2px dashed #93c5fd;
  transition: all 0.3s ease;
  position: relative;
  margin-top: 1rem;
}

.story-structure:hover {
  background-color: #f1f5f9;
  border-color: #60a5fa;
}

.dark .story-structure {
  background-color: #1e293b;
  border-color: #60a5fa;
}

.dark .story-structure:hover {
  background-color: #0f172a;
  border-color: #3b82f6;
}

.structure-part {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  background-color: white;
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.dark .structure-part {
  background-color: #334155;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.structure-part.animated-part {
  animation: popIn 0.5s ease-out;
  animation-fill-mode: both;
}

.structure-part:nth-child(1) { animation-delay: 0.1s; }
.structure-part:nth-child(3) { animation-delay: 0.3s; }
.structure-part:nth-child(5) { animation-delay: 0.5s; }
.structure-part:nth-child(7) { animation-delay: 0.7s; }

@keyframes popIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

.structure-part:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dark .structure-part:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.structure-connector {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 40px;
  position: relative;
}

.connector-line {
  width: 4px;
  height: 100%;
  background-color: #93c5fd;
  position: absolute;
  top: 0;
  z-index: 1;
}

.connector-arrow {
  font-size: 1.5rem;
  position: absolute;
  bottom: -5px;
  z-index: 2;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.part-label {
  font-weight: 700;
  color: #3b82f6;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.dark .part-label {
  color: #60a5fa;
}

.part-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #3b82f6;
  color: white;
  border-radius: 50%;
  font-weight: 700;
  font-size: 1rem;
}

.dark .part-number {
  background-color: #60a5fa;
  color: #0f172a;
}

.story-starters {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.story-starter-btn {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 1rem;
  color: #0369a1;
  cursor: pointer;
  transition: all 0.2s ease;
}

.story-starter-btn:hover {
  background-color: #e0f2fe;
  border-color: #7dd3fc;
  transform: translateX(5px);
}

.dark .story-starter-btn {
  background-color: #0c4a6e;
  border-color: #0284c7;
  color: #7dd3fc;
}

.dark .story-starter-btn:hover {
  background-color: #075985;
  border-color: #0ea5e9;
}

.child-input {
  font-size: 1.1rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.child-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .child-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .child-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .example-story-btn {
    margin-left: 0;
    align-self: flex-start;
  }

  .example-stories-dropdown {
    width: 250px;
    right: auto;
    left: 0;
  }
}
</style>
