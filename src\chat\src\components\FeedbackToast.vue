<template>
  <Teleport to="body">
    <Transition name="fade">
      <div 
        v-if="visible" 
        class="fixed bottom-4 right-4 px-4 py-2 rounded-md shadow-lg transition-opacity duration-300"
        :class="type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'"
        role="alert"
        aria-live="polite"
      >
        {{ message }}
      </div>
    </Transition>
  </Teleport>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const props = defineProps<{
  message: string;
  type?: 'success' | 'error';
  duration?: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

// 监听visible变化，自动隐藏
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 如果显示，则设置定时器自动隐藏
    setTimeout(() => {
      emit('update:visible', false);
    }, props.duration || 3000);
  }
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
