<template>
  <StoryCreationLayout v-model:current-step="currentStep" v-model:active-tab="activeTab">
    <!-- 内容区域 -->
    <div class="workspace-content">
      <!-- 绘本创作模式 -->
      <template v-if="activeTab === 'outline'">
        <StoryOutlineCreator v-if="currentStep === 1" :project-data="projectData" />
        <CharacterCreator v-else-if="currentStep === 2" :project-data="projectData" />
        <StoryPageCreator v-else-if="currentStep === 3" :project-data="projectData" />
        <StoryFinalizer v-else-if="currentStep === 4" :project-data="projectData" />
      </template>

      <!-- 图书馆模式 -->
      <ReferenceLibrary v-else-if="activeTab === 'reference'" :project-data="projectData" />

      <!-- 我的作品模式 -->
      <WorksList
        v-else-if="activeTab === 'my-works'"
        @create="handleCreateNewWork"
      />
    </div>
  </StoryCreationLayout>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import StoryCreationLayout from './StoryCreationLayout.vue';
import StoryOutlineCreator from './StoryOutlineCreator.vue';
import CharacterCreator from './CharacterCreator.vue';
import StoryPageCreator from './StoryPageCreator.vue';
import StoryFinalizer from './StoryFinalizer.vue';
import ReferenceLibrary from '../ReferenceLibrary.vue';
import WorksList from '../works/WorksList.vue';

const props = defineProps({
  projectData: Object,
  currentStep: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['step-change', 'update:current-step']);

// 使用计算属性来处理currentStep的双向绑定
const currentStep = computed({
  get: () => props.currentStep,
  set: (value) => {
    emit('update:current-step', value);
    emit('step-change', value);
  }
});

// 默认为outline
const activeTab = ref('outline');

// 监听步骤变化，触发事件
watch(currentStep, (newStep) => {
  emit('step-change', newStep);
});

const selectTemplate = (template) => {
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }

  // 保存模板
  props.projectData.outline.template = template;
  props.projectData.title = template.title || props.projectData.title;

  // 初始化必要的数据结构
  if (!props.projectData.outline.characters) {
    props.projectData.outline.characters = {};
  }

  if (!props.projectData.outline.scenes) {
    props.projectData.outline.scenes = [];
  }

  // 从模板中复制结构信息
  if (template.structure) {
    props.projectData.outline.theme = template.structure.theme || '';
    props.projectData.outline.ageGroup = template.structure.ageGroup || '';
    props.projectData.outline.mainIdea = template.structure.mainIdea || '';
    props.projectData.outline.beginning = template.structure.beginning || '';
    props.projectData.outline.middle = template.structure.middle || '';
    props.projectData.outline.ending = template.structure.ending || '';
  }

  // 自动进入下一步
  nextStep();
};

const nextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

// 处理创建新作品
const handleCreateNewWork = () => {
  // 切换到绘本创作模式
  activeTab.value = 'outline';
  // 重置到第一步
  currentStep.value = 1;
  console.log('从我的作品页面切换到创建新作品');

  // 检查是否有创建新故事的标记
  const isCreatingNew = localStorage.getItem('storybook-creating-new') === 'true';
  if (isCreatingNew) {
    console.log('检测到创建新故事标记，准备创建新故事');
    // 清除标记
    localStorage.removeItem('storybook-creating-new');

    // 创建一个新的故事项目
    if (props.projectData) {
      // 重置项目数据
      Object.assign(props.projectData, {
        title: '新故事',
        description: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 0,
        isPublic: 0,
        pageCount: 0,
        wordCount: 0,
        characters: [],
        pages: []
      });
      console.log('已创建新故事项目数据');
    }
  }
};

// 组件挂载时的处理
onMounted(() => {
  console.log('CreationWorkspace组件已挂载，当前activeTab:', activeTab.value);
});
</script>

<style scoped>
.workspace-content {
  background-color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(226, 232, 240, 0.8);
  height: 100%;
  overflow-y: auto;
}

.dark .workspace-content {
  background-color: #252542;
  border-color: rgba(50, 50, 93, 0.25);
}
</style>
