import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor, HttpException, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { Observable, catchError, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { Result } from '@/common/result';

@Injectable()
export class TransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): any {
    return next.handle().pipe(
      map((data) => {
        const response = context.switchToHttp().getResponse();
        const request = context.switchToHttp().getRequest();
        response.statusCode = 200;

        /* 检查是否为流式响应或特殊路由，如果是则直接返回数据不进行包装 */
        /* 微信类支付类通知接口需要原样输出 */
        if (request.path.includes('notify')) {
          return data;
        }

        /* 聊天流式响应接口需要原样输出 */
        if (request.path.includes('/chatgpt/chat-process') &&
            response.getHeader('Content-type')?.includes('application/octet-stream')) {
          Logger.log('检测到流式响应请求，跳过响应转换', 'TransformInterceptor');
          return data;
        }

        const message = response.status < 400 ? null : response.statusText;
        return Result.success(data, message);
      }),
      catchError((error) => {
        const statusCode = error.status || 500;
        const message = (error.response || 'Internal server error') as string;
        return throwError(new HttpException(message, statusCode));
      }),
    );
  }
}
