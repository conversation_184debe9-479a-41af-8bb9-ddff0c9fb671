<template>
  <div class="view-storybook">
    <div class="page-header">
      <div class="header-left">
        <h1>{{ storybook.title || '未命名绘本' }}</h1>
        <p class="subtitle">{{ storybook.description || '暂无描述' }}</p>
      </div>

      <div class="header-actions">
        <NButton @click="goBack">返回列表</NButton>
        <NButton type="primary" class="ml-4" @click="editStorybook">编辑绘本</NButton>
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <NSpin size="large" />
      <p>加载中...</p>
    </div>

    <div v-else class="page-content">
      <NCard title="绘本信息">
        <div class="storybook-info">
          <div class="cover-container">
            <img
              v-if="storybook.coverImg"
              :src="storybook.coverImg"
              :alt="storybook.title"
              class="cover-image"
            />
            <div v-else class="cover-placeholder">
              <SvgIcon name="ri:book-open-line" size="64" />
            </div>
          </div>

          <div class="info-details">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span>{{ formatDate(storybook.createdAt) }}</span>
            </div>

            <div class="info-item">
              <span class="label">最后编辑：</span>
              <span>{{ formatDate(storybook.lastEditedAt || storybook.updatedAt) }}</span>
            </div>

            <div class="info-item">
              <span class="label">页数：</span>
              <span>{{ storybook.pageCount || 0 }} 页</span>
            </div>

            <div class="info-item">
              <span class="label">字数：</span>
              <span>{{ storybook.wordCount || 0 }} 字</span>
            </div>

            <div class="info-item">
              <span class="label">状态：</span>
              <NTag :type="statusType">{{ statusText }}</NTag>
            </div>
          </div>
        </div>
      </NCard>

      <NCard title="绘本预览" class="mt-4">
        <div class="preview-container">
          <div v-if="storybook.pages && storybook.pages.length > 0" class="pages-preview">
            <div
              v-for="page in storybook.pages"
              :key="page.pageNumber"
              class="page-item"
              @click="viewPage(page)"
            >
              <div class="page-number">{{ page.pageNumber }}</div>
              <div class="page-thumbnail">
                <img v-if="page.imageUrl" :src="page.imageUrl" :alt="`页面 ${page.pageNumber}`" />
                <div v-else class="thumbnail-placeholder">
                  <SvgIcon name="ri:image-line" size="32" />
                </div>
              </div>
              <div class="page-text">{{ truncateText(page.text) }}</div>
            </div>
          </div>

          <div v-else class="empty-pages">
            <NEmpty description="暂无页面内容">
              <template #extra>
                <NButton @click="editStorybook">开始创作</NButton>
              </template>
            </NEmpty>
          </div>
        </div>
      </NCard>

      <div class="action-buttons mt-4">
        <NButton @click="readStorybook">
          <template #icon>
            <SvgIcon name="ri:book-read-line" size="18" />
          </template>
          阅读绘本
        </NButton>

        <NButton @click="exportStorybook">
          <template #icon>
            <SvgIcon name="ri:download-line" size="18" />
          </template>
          导出绘本
        </NButton>

        <NButton @click="shareStorybook">
          <template #icon>
            <SvgIcon name="ri:share-line" size="18" />
          </template>
          分享绘本
        </NButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NCard, NButton, NSpin, NEmpty, NTag } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { useWorksStore } from '@/store/modules/works';

const router = useRouter();
const route = useRoute();
const worksStore = useWorksStore();

// 状态
const loading = ref(true);
const storybook = ref({
  id: '',
  title: '',
  description: '',
  coverImg: '',
  status: 0,
  pageCount: 0,
  wordCount: 0,
  createdAt: '',
  updatedAt: '',
  lastEditedAt: '',
  pages: []
});

// 状态文本
const statusText = computed(() => {
  switch (storybook.value.status) {
    case 0: return '草稿';
    case 1: return '已完成';
    case 2: return '已发布';
    default: return '未知状态';
  }
});

// 状态标签类型
const statusType = computed(() => {
  switch (storybook.value.status) {
    case 0: return 'warning';
    case 1: return 'success';
    case 2: return 'info';
    default: return 'default';
  }
});

// 获取绘本详情
const fetchStorybook = async () => {
  const id = route.params.id;
  if (!id) {
    window.$message?.error('绘本ID不存在');
    router.push('/storybook');
    return;
  }

  try {
    // 调用API获取绘本详情
    const result = await worksStore.getStorybookDetail(Number(id));

    // 更新状态
    storybook.value = {
      ...result,
      // 如果后端没有返回pages字段，则初始化为空数组
      pages: result.pages || []
    };
  } catch (error) {
    console.error('获取绘本详情失败:', error);
    window.$message?.error('获取绘本详情失败');
    router.push('/storybook');
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知';

  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 截断文本
const truncateText = (text) => {
  if (!text) return '暂无内容';
  return text.length > 30 ? text.substring(0, 30) + '...' : text;
};

// 查看页面
const viewPage = (page) => {
  // 实现页面预览功能
};

// 编辑绘本
const editStorybook = () => {
  router.push(`/storybook/edit/${storybook.value.id}`);
};

// 阅读绘本
const readStorybook = () => {
  router.push(`/storybook/reader/${storybook.value.id}`);
};

// 导出绘本
const exportStorybook = () => {
  // 实现导出功能
};

// 分享绘本
const shareStorybook = () => {
  router.push(`/storybook/share/${storybook.value.id}`);
};

// 返回列表
const goBack = () => {
  router.push('/storybook');
};

// 组件挂载时获取绘本详情
onMounted(() => {
  fetchStorybook();
});
</script>

<style scoped>
.view-storybook {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.dark .subtitle {
  color: #aaa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
}

.storybook-info {
  display: flex;
  gap: 24px;
}

.cover-container {
  width: 200px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #aaa;
}

.dark .cover-placeholder {
  background-color: #333;
  color: #666;
}

.info-details {
  flex: 1;
}

.info-item {
  margin-bottom: 12px;
}

.label {
  font-weight: 500;
  margin-right: 8px;
}

.preview-container {
  margin-top: 16px;
}

.pages-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
}

.page-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.page-item:hover {
  transform: translateY(-5px);
}

.page-number {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.page-thumbnail {
  height: 120px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .page-thumbnail {
  background-color: #333;
}

.page-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #aaa;
}

.dark .thumbnail-placeholder {
  color: #666;
}

.page-text {
  padding: 8px;
  font-size: 12px;
  height: 48px;
  overflow: hidden;
  background-color: white;
}

.dark .page-text {
  background-color: #1f1f1f;
}

.empty-pages {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-4 {
  margin-left: 16px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  .storybook-info {
    flex-direction: column;
  }

  .cover-container {
    width: 100%;
    height: 200px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }
}
</style>
