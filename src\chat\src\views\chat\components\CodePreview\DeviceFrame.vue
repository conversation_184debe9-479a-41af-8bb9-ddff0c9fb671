<template>
    <div class="device-frame" :class="[
        `device-${deviceType}`,
        { 'landscape': isLandscape },
        { 'fullscreen': isFullscreen },
        deviceClass
    ]" :style="deviceStyle" role="img" :aria-label="`${deviceType}设备预览${isLandscape ? '，横屏模式' : ''}`">
        <div class="device-screen">
            <!-- 设备元素 - 手机 -->
            <div v-if="deviceType === 'mobile'" class="device-notch" aria-hidden="true">
                <!-- 现代挑孔摄像头 -->
                <div class="punch-hole"></div>
            </div>

            <iframe ref="previewIframeRef" :srcDoc="htmlContent"
                class="w-full h-full bg-white transition-all duration-500"
                title="代码预览内容"
                aria-label="生成的HTML页面预览"></iframe>

            <!-- 编辑工具栏 -->
            <div v-if="isEditMode" class="edit-toolbar">
                <button @click="toggleElementSelector" class="edit-btn" :class="{ 'active': isSelectingElement }">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.6,2.4 L12.2,3.8 L16.2,7.8 L17.6,6.4 L13.6,2.4 Z M11.5,4.5 L2,14 L2,18 L6,18 L15.5,8.5 L11.5,4.5 Z"></path>
                    </svg>
                    <span>选择元素</span>
                </button>
                <button @click="exitEditMode" class="edit-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span>退出编辑</span>
                </button>
            </div>
        </div>

        <!-- 元素编辑面板 -->
        <div v-if="isEditMode && selectedElement" class="element-edit-panel">
            <div class="panel-header">
                <span>编辑元素: {{ getElementDescription() }}</span>
                <button @click="closeElementPanel" class="close-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            <div class="panel-content">
                <div class="edit-section">
                    <label>内容</label>
                    <textarea v-model="elementContent" @input="updateElementContent" class="content-editor"></textarea>
                </div>
                <div class="edit-section">
                    <label>样式</label>
                    <div class="style-editor">
                        <div class="style-row">
                            <label>颜色</label>
                            <input type="color" v-model="elementStyles.color" @input="updateElementStyles">
                        </div>
                        <div class="style-row">
                            <label>背景色</label>
                            <input type="color" v-model="elementStyles.backgroundColor" @input="updateElementStyles">
                        </div>
                        <div class="style-row">
                            <label>字体大小</label>
                            <select v-model="elementStyles.fontSize" @change="updateElementStyles">
                                <option value="12px">12px</option>
                                <option value="14px">14px</option>
                                <option value="16px">16px</option>
                                <option value="18px">18px</option>
                                <option value="20px">20px</option>
                                <option value="24px">24px</option>
                                <option value="28px">28px</option>
                                <option value="32px">32px</option>
                            </select>
                        </div>
                        <div class="style-row">
                            <label>字体粗细</label>
                            <select v-model="elementStyles.fontWeight" @change="updateElementStyles">
                                <option value="normal">正常</option>
                                <option value="bold">粗体</option>
                            </select>
                        </div>
                        <div class="style-row">
                            <label>对齐方式</label>
                            <select v-model="elementStyles.textAlign" @change="updateElementStyles">
                                <option value="left">左对齐</option>
                                <option value="center">居中</option>
                                <option value="right">右对齐</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="panel-actions">
                    <button @click="applyChanges" class="apply-btn">应用更改</button>
                </div>
            </div>
        </div>

        <!-- 使用通用加载指示器组件 -->
        <LoadingIndicator
            :visible="isLoading"
            message="加载预览中..."
            overlay-class="bg-white bg-opacity-70 dark:bg-gray-800 dark:bg-opacity-70"
        />
    </div>
</template>

<script lang="ts" setup>
import { defineExpose, ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import { useMessage } from 'naive-ui';

const message = useMessage();
const emit = defineEmits(['update:htmlContent', 'element-edited']);

const props = defineProps({
    deviceType: {
        type: String as () => 'mobile' | 'tablet' | 'desktop',
        required: true
    },
    isLandscape: {
        type: Boolean,
        default: false
    },
    isFullscreen: {
        type: Boolean,
        default: false
    },
    htmlContent: {
        type: String,
        required: true
    },
    deviceStyle: {
        type: Object,
        default: () => ({})
    },
    deviceClass: {
        type: String,
        default: ''
    },
    'aria-live': {
        type: String,
        default: 'off'
    }
});

const previewIframeRef = ref<HTMLIFrameElement | null>(null);
const isLoading = ref(false);
const isEditMode = ref(false);
const isSelectingElement = ref(false);
const selectedElement = ref<HTMLElement | null>(null);
const elementContent = ref('');
const elementStyles = ref({
    color: '',
    backgroundColor: '',
    fontSize: '',
    fontWeight: '',
    textAlign: ''
});
const originalHtml = ref('');
const highlightedElements = ref<HTMLElement[]>([]);

// 获取元素描述
const getElementDescription = () => {
    if (!selectedElement.value) return '';

    const tagName = selectedElement.value.tagName.toLowerCase();
    const id = selectedElement.value.id ? `#${selectedElement.value.id}` : '';
    const className = selectedElement.value.className ? `.${selectedElement.value.className.split(' ')[0]}` : '';

    return `${tagName}${id}${className}`;
};

// 切换编辑模式
const toggleEditMode = () => {
    isEditMode.value = !isEditMode.value;

    if (isEditMode.value) {
        // 进入编辑模式
        originalHtml.value = props.htmlContent;
        setupElementSelector();
    } else {
        // 退出编辑模式
        cleanupElementSelector();
        selectedElement.value = null;
    }
};

// 退出编辑模式
const exitEditMode = () => {
    isEditMode.value = false;
    isSelectingElement.value = false;
    selectedElement.value = null;
    cleanupElementSelector();
};

// 切换元素选择器
const toggleElementSelector = () => {
    isSelectingElement.value = !isSelectingElement.value;

    if (isSelectingElement.value) {
        setupElementSelector();
    } else {
        cleanupElementSelector();
    }
};

// 设置元素选择器
const setupElementSelector = () => {
    if (!previewIframeRef.value) return;

    const iframe = previewIframeRef.value;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // 添加鼠标移动事件监听
    iframeDocument.addEventListener('mousemove', handleMouseMove);
    iframeDocument.addEventListener('click', handleElementClick);

    // 添加选择器样式
    const style = iframeDocument.createElement('style');
    style.id = 'element-selector-style';
    style.textContent = `
        .element-highlight {
            outline: 2px dashed #4f46e5 !important;
            outline-offset: 2px !important;
            cursor: pointer !important;
            position: relative !important;
        }
    `;
    iframeDocument.head.appendChild(style);
};

// 清理元素选择器
const cleanupElementSelector = () => {
    if (!previewIframeRef.value) return;

    const iframe = previewIframeRef.value;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // 移除事件监听
    iframeDocument.removeEventListener('mousemove', handleMouseMove);
    iframeDocument.removeEventListener('click', handleElementClick);

    // 移除所有高亮
    highlightedElements.value.forEach(el => {
        el.classList.remove('element-highlight');
    });
    highlightedElements.value = [];

    // 移除选择器样式
    const style = iframeDocument.getElementById('element-selector-style');
    if (style) {
        style.remove();
    }
};

// 处理鼠标移动
const handleMouseMove = (e: MouseEvent) => {
    if (!isSelectingElement.value || !previewIframeRef.value) return;

    const iframe = previewIframeRef.value;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // 移除之前的高亮
    highlightedElements.value.forEach(el => {
        el.classList.remove('element-highlight');
    });
    highlightedElements.value = [];

    // 获取当前鼠标下的元素
    const element = iframeDocument.elementFromPoint(e.clientX, e.clientY) as HTMLElement;

    if (element && element.tagName !== 'HTML' && element.tagName !== 'BODY') {
        // 添加高亮
        element.classList.add('element-highlight');
        highlightedElements.value.push(element);
    }
};

// 处理元素点击
const handleElementClick = (e: MouseEvent) => {
    if (!isSelectingElement.value || !previewIframeRef.value) return;

    e.preventDefault();
    e.stopPropagation();

    const iframe = previewIframeRef.value;
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

    if (!iframeDocument) return;

    // 获取点击的元素
    const element = iframeDocument.elementFromPoint(e.clientX, e.clientY) as HTMLElement;

    if (element && element.tagName !== 'HTML' && element.tagName !== 'BODY') {
        // 选中元素
        selectedElement.value = element;
        isSelectingElement.value = false;

        // 获取元素内容和样式
        elementContent.value = element.innerHTML;

        // 获取计算样式
        const computedStyle = window.getComputedStyle(element);
        elementStyles.value = {
            color: rgbToHex(computedStyle.color),
            backgroundColor: rgbToHex(computedStyle.backgroundColor),
            fontSize: computedStyle.fontSize,
            fontWeight: computedStyle.fontWeight,
            textAlign: computedStyle.textAlign
        };

        // 清理选择器
        cleanupElementSelector();
    }
};

// RGB颜色转十六进制
const rgbToHex = (rgb: string) => {
    // 默认返回黑色
    if (!rgb || rgb === 'rgba(0, 0, 0, 0)' || rgb === 'transparent') return '#000000';

    // 解析RGB值
    const rgbMatch = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (rgbMatch) {
        const r = parseInt(rgbMatch[1]);
        const g = parseInt(rgbMatch[2]);
        const b = parseInt(rgbMatch[3]);

        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    }

    // 解析RGBA值
    const rgbaMatch = rgb.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d*(?:\.\d+)?)\)$/);
    if (rgbaMatch) {
        const r = parseInt(rgbaMatch[1]);
        const g = parseInt(rgbaMatch[2]);
        const b = parseInt(rgbaMatch[3]);

        return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    }

    return rgb; // 如果已经是十六进制或其他格式，直接返回
};

// 更新元素内容
const updateElementContent = () => {
    if (!selectedElement.value) return;

    selectedElement.value.innerHTML = elementContent.value;
};

// 更新元素样式
const updateElementStyles = () => {
    if (!selectedElement.value) return;

    // 应用样式
    Object.entries(elementStyles.value).forEach(([property, value]) => {
        if (value) {
            selectedElement.value!.style[property as any] = value;
        }
    });
};

// 应用更改
const applyChanges = () => {
    if (!previewIframeRef.value || !selectedElement.value) return;

    try {
        // 获取更新后的HTML
        const iframe = previewIframeRef.value;
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

        if (!iframeDocument) return;

        // 获取完整的HTML
        const updatedHtml = iframeDocument.documentElement.outerHTML;

        // 发送更新事件
        emit('update:htmlContent', updatedHtml);
        emit('element-edited', {
            element: getElementDescription(),
            content: elementContent.value,
            styles: elementStyles.value
        });

        message.success('已应用更改');

        // 关闭编辑面板
        selectedElement.value = null;
    } catch (error) {
        console.error('应用更改时出错:', error);
        message.error('应用更改失败');
    }
};

// 关闭元素面板
const closeElementPanel = () => {
    selectedElement.value = null;
};

// 刷新预览
const refreshPreview = () => {
    if (!previewIframeRef.value) {
        console.warn('预览iframe元素不存在');
        return;
    }

    try {
        // 显示加载状态
        isLoading.value = true;

        const iframe = previewIframeRef.value;
        console.log('刷新预览，当前内容长度:', props.htmlContent.length);

        // 添加加载状态监听
        iframe.onload = () => {
            console.log('预览iframe已加载完成');
            // 隐藏加载状态
            isLoading.value = false;

            // 尝试执行一些基本的错误处理
            try {
                const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
                if (iframeDocument) {
                    // 添加错误处理
                    const errorHandler = `
                        window.onerror = function(message, source, lineno, colno, error) {
                            console.error('iframe内容错误:', message);
                            return true; // 防止错误向上传播
                        };
                    `;

                    // 将错误处理注入到iframe
                    const script = iframeDocument.createElement('script');
                    script.textContent = errorHandler;
                    iframeDocument.head.appendChild(script);

                    // 如果处于编辑模式，重新设置选择器
                    if (isEditMode.value && isSelectingElement.value) {
                        setupElementSelector();
                    }
                }
            } catch (innerError) {
                console.warn('添加iframe错误处理失败:', innerError);
            }
        };

        // 设置内容
        iframe.srcdoc = props.htmlContent;

        // 设置超时，防止加载状态一直显示
        setTimeout(() => {
            isLoading.value = false;
        }, 3000);
    } catch (error) {
        console.error('刷新预览时出错:', error);
        isLoading.value = false;
    }
};

// 暴露方法给父组件
defineExpose({
    refreshPreview,
    toggleEditMode,
    isEditMode
});
</script>

<style scoped>
.device-frame {
    position: relative;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.5s ease;
    transform: scale(var(--device-scale, 1));
    transform-origin: center center;
    /* 添加微妙的光沾效果 */
    background-image: linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%);
    /* 确保设备居中 */
    margin: 0 auto;
    max-width: 100%;
    max-height: 100%;
}

/* 手机设备样式 - 现代风格 */
.device-mobile {
    width: 375px;
    height: 667px;
    border: 10px solid #222;
    border-radius: 36px;
    /* 确保在小屏幕上不会超出边界 */
    max-width: calc(100% - 20px);
    max-height: calc(100% - 20px);
}

.device-mobile.landscape {
    width: 667px;
    height: 375px;
}

/* 平板设备样式 - 现代风格 */
.device-tablet {
    width: 500px;
    height: 700px;
    border: 14px solid #222;
    border-radius: 24px;
    /* 确保在小屏幕上不会超出边界 */
    max-width: calc(100% - 28px);
    max-height: calc(100% - 28px);
    /* 添加过渡效果，使切换更流畅 */
    transition: width 0.5s ease, height 0.5s ease, transform 0.5s ease;
}

.device-tablet.landscape {
    width: 700px;
    height: 500px;
    /* 横屏模式下的特殊样式 */
    border-radius: 20px;
}

/* 桌面设备样式 - 现代风格 */
.device-desktop {
    width: 100%;
    height: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    /* 确保在小屏幕上不会超出边界 */
    max-width: calc(100% - 2px);
    max-height: calc(100% - 2px);
}

/* 挑孔摄像头样式 */
.device-notch {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    z-index: 20;
    pointer-events: none;
    display: flex;
    justify-content: center;
    align-items: center;
}

.punch-hole {
    width: 12px;
    height: 12px;
    background-color: #111;
    border-radius: 50%;
    position: relative;
    margin-top: 6px;
    /* 摄像头镜头效果 */
    box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.5), 0 0 1px rgba(0, 0, 0, 0.3);
    /* 添加微妙的光沾效果 */
    background-image: radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* 横屏模式下的挑孔摄像头调整 */
.device-mobile.landscape .device-notch {
    top: 0;
    left: 0;
    right: auto;
    bottom: 0;
    width: 20px;
    height: 100%;
    justify-content: flex-start;
    align-items: center;
}

.device-mobile.landscape .punch-hole {
    margin-top: 0;
    margin-left: 6px;
}

/* 全屏模式样式 */
.device-frame.fullscreen {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 100px rgba(0, 0, 0, 0.1);
    /* 全屏模式下的特殊光效 */
    background-image: linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0.1) 100%);
}

/* 旋转动画 */
.rotating {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 设备屏幕样式 */
.device-screen {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 26px;
    background-color: white;
    /* 添加过渡效果 */
    transition: border-radius 0.5s ease;
}

.device-mobile .device-screen {
    border-radius: 26px;
}

.device-tablet .device-screen {
    border-radius: 16px;
}

.device-tablet.landscape .device-screen {
    /* 横屏模式下的屏幕圆角更小 */
    border-radius: 12px;
}

.device-desktop .device-screen {
    border-radius: 4px;
}

/* 屏幕圆角调整 */
.device-mobile .device-screen {
    border-radius: 26px;
}

.device-mobile.landscape .device-screen {
    border-radius: 26px;
}

/* 移动设备上的特殊样式 */
@media (max-width: 640px) {
    .device-mobile {
        border-width: 8px;
        border-radius: 28px;
    }

    .device-mobile .device-screen {
        border-radius: 20px;
    }

    .device-tablet {
        border-width: 10px;
        border-radius: 20px;
    }

    .device-tablet .device-screen {
        border-radius: 12px;
    }

    /* 在小屏幕上调整挑孔摄像头大小 */
    .device-mobile .punch-hole {
        width: 10px;
        height: 10px;
        margin-top: 5px;
    }

    .device-mobile.landscape .punch-hole {
        margin-top: 0;
        margin-left: 5px;
    }

    /* 在小屏幕上调整设备尺寸 */
    .device-mobile {
        max-width: calc(100% - 16px);
        max-height: calc(100% - 16px);
    }

    .device-tablet {
        max-width: calc(100% - 20px);
        max-height: calc(100% - 20px);
    }
}

/* 编辑工具栏样式 */
.edit-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    display: flex;
    gap: 8px;
}

.dark .edit-toolbar {
    background-color: rgba(31, 41, 55, 0.9);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.edit-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    border-radius: 4px;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    color: #374151;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dark .edit-btn {
    background-color: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
}

.edit-btn:hover {
    background-color: #e5e7eb;
}

.dark .edit-btn:hover {
    background-color: #4b5563;
}

.edit-btn.active {
    background-color: #4f46e5;
    border-color: #4338ca;
    color: white;
}

.dark .edit-btn.active {
    background-color: #6366f1;
    border-color: #4f46e5;
}

/* 元素编辑面板样式 */
.element-edit-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 100;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.dark .element-edit-panel {
    background-color: #1f2937;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid #374151;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 500;
}

.dark .panel-header {
    background-color: #111827;
    border-bottom: 1px solid #374151;
    color: #e5e7eb;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 4px;
}

.dark .close-btn {
    color: #9ca3af;
}

.close-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.dark .close-btn:hover {
    background-color: #374151;
    color: #e5e7eb;
}

.panel-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.edit-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.edit-section label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.dark .edit-section label {
    color: #e5e7eb;
}

.content-editor {
    width: 100%;
    min-height: 100px;
    padding: 8px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
    resize: vertical;
}

.dark .content-editor {
    background-color: #111827;
    border-color: #374151;
    color: #e5e7eb;
}

.style-editor {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.style-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.style-row label {
    font-size: 13px;
    font-weight: normal;
}

.style-row input, .style-row select {
    padding: 4px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background-color: white;
}

.dark .style-row input, .dark .style-row select {
    background-color: #111827;
    border-color: #374151;
    color: #e5e7eb;
}

.panel-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

.apply-btn {
    padding: 8px 16px;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.apply-btn:hover {
    background-color: #4338ca;
}
</style>
