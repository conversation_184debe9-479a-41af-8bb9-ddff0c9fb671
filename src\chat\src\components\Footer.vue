<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-logo">
        <img :src="logoPath" alt="Logo" class="logo" />
        <h2 class="footer-site-name">{{ siteName }}</h2>
      </div>
      <div class="footer-links">
        <div class="footer-links-group">
          <h3 class="footer-links-title">导航</h3>
          <a href="#" class="footer-link">首页</a>
          <a href="#features" class="footer-link">创作类型</a>
          <a href="#community" class="footer-link">社区作品</a>
          <a href="#about" class="footer-link">关于我们</a>
        </div>
        <div class="footer-links-group">
          <h3 class="footer-links-title">创作</h3>
          <a @click="navigateToFeature('/chat?preset=programming')" class="footer-link">AI编程</a>
          <a @click="navigateToFeature('/chat?preset=writing')" class="footer-link">AI写作</a>
          <a @click="navigateToFeature('/chat?preset=picturebook')" class="footer-link">AI绘本</a>
          <a @click="navigateToFeature('/chat?preset=music')" class="footer-link">AI音乐</a>
        </div>
        <div class="footer-links-group">
          <h3 class="footer-links-title">支持</h3>
          <a href="#" class="footer-link">帮助中心</a>
          <a href="#" class="footer-link">联系我们</a>
          <a href="#" class="footer-link">隐私政策</a>
          <a href="#" class="footer-link">服务条款</a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p class="copyright">
        版权所有 © {{ new Date().getFullYear() }} {{ globalConfig?.companyName || siteName }}. 保留所有权利.
        <a v-if="globalConfig?.filingNumber" class="filing-number" href="https://beian.miit.gov.cn" target="_blank">
          {{ globalConfig?.filingNumber }}
        </a>
      </p>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/store';
import logo from '@/assets/logo.png';

const router = useRouter();
const authStore = useAuthStore();
const isLogin = computed(() => authStore.isLogin);
const siteName = computed(() => authStore.globalConfig?.siteName || 'DeepCreate');
const logoPath = computed(() => authStore.globalConfig.clientLogoPath || logo);
const globalConfig = computed(() => authStore.globalConfig);

// 导航到特定创作类型
function navigateToFeature(path) {
  if (!isLogin.value) {
    authStore.setLoginDialog(true);
    return;
  }
  router.push(path);
}
</script>

<style scoped>
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-gray-300);
  padding-top: 4rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  flex: 1 1 300px;
}

.logo {
  height: 2.5rem;
  width: 2.5rem;
  object-fit: contain;
}

.footer-site-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  flex: 2 1 600px;
}

.footer-links-group {
  flex: 1 1 150px;
}

.footer-links-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1.5rem;
}

.footer-link {
  display: block;
  color: var(--color-gray-400);
  margin-bottom: 0.75rem;
  text-decoration: none;
  transition: color 0.2s ease;
  cursor: pointer;
}

.footer-link:hover {
  color: white;
}

.footer-bottom {
  margin-top: 4rem;
  padding: 1.5rem 0;
  border-top: 1px solid var(--color-gray-800);
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-gray-500);
}

.filing-number {
  color: var(--color-gray-500);
  text-decoration: none;
  margin-left: 0.5rem;
}

.filing-number:hover {
  color: var(--color-gray-400);
  text-decoration: underline;
}

@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
    gap: 2rem;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 2rem;
  }
}
</style>
