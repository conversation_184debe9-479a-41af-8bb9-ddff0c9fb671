import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsBoolean, IsArray } from 'class-validator';

export class CreateStorybookDto {
  @ApiProperty({ description: '绘本标题' })
  @IsNotEmpty({ message: '标题不能为空' })
  @IsString()
  title: string;

  @ApiProperty({ description: '绘本描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '封面图片URL', required: false })
  @IsOptional()
  @IsString()
  coverImg?: string;

  @ApiProperty({ description: '是否公开(0:私有,1:公开)', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  isPublic?: number;

  @ApiProperty({ description: '绘本内容JSON', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ description: '来源', required: false, default: 'storybook' })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ description: '标签', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({ description: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  status?: number;

  @ApiProperty({ description: '所属文件夹ID', required: false })
  @IsOptional()
  @IsNumber()
  folderId?: number;

  @ApiProperty({ description: '页数', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  pageCount?: number;

  @ApiProperty({ description: '字数', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  wordCount?: number;
}
