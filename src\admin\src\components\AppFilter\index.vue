<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import type { FormInstance } from 'element-plus';

const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  initialFilter: {
    type: Object,
    default: () => ({
      catId: '',
      name: '',
      status: '',
      page: 1,
      size: 10
    })
  }
});

const emit = defineEmits(['filter', 'reset']);

// 过滤表单
const filterForm = reactive({
  catId: props.initialFilter.catId || '',
  name: props.initialFilter.name || '',
  status: props.initialFilter.status || '',
  page: props.initialFilter.page || 1,
  size: props.initialFilter.size || 10
});

// 表单引用
const formRef = ref<FormInstance>();

// 监听初始过滤器变化
watch(() => props.initialFilter, (newVal) => {
  Object.assign(filterForm, newVal);
}, { deep: true });

// 应用过滤器
function applyFilter() {
  emit('filter', { ...filterForm });
}

// 重置过滤器
function resetFilter() {
  if (formRef.value) {
    formRef.value.resetFields();
    filterForm.page = 1;
    emit('reset', formRef.value);
  }
}

// 处理回车键
function handleEnterKey() {
  applyFilter();
}

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
];
</script>

<template>
  <div class="app-filter">
    <el-form ref="formRef" :inline="true" :model="filterForm" class="filter-form">
      <el-form-item label="应用分类">
        <el-select
          v-model="filterForm.catId"
          placeholder="请选择应用分类"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="item in categories"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="应用名称">
        <el-input
          v-model="filterForm.name"
          placeholder="应用名称[模糊搜索]"
          clearable
          @keydown.enter.prevent="handleEnterKey"
        />
      </el-form-item>

      <el-form-item label="应用状态">
        <el-select
          v-model="filterForm.status"
          placeholder="请选择应用状态"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <div class="filter-actions">
          <el-button type="primary" @click="applyFilter">
            <el-icon><Search /></el-icon> 查询
          </el-button>
          <el-button @click="resetFilter">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.app-filter {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
  }

  .filter-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
    width: 100%;
  }

  .filter-form :deep(.el-form-item__content) {
    width: 100%;
  }

  .filter-form :deep(.el-input),
  .filter-form :deep(.el-select) {
    width: 100% !important;
  }
}
</style>
