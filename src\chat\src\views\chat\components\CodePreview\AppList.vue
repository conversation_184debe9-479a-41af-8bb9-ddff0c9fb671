<template>
  <div class="h-full flex flex-col overflow-hidden bg-white dark:bg-gray-800">
    <!-- 顶部区域 -->
    <div class="sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">

      <!-- 应用分类 -->
      <div class="flex items-center space-x-2 px-4 py-2 overflow-x-auto scrollbar-hide">
        <div
          v-for="(item, index) in catList"
          :key="index"
          @click="handleChangeCatId(item.id)"
          :class="{
            'bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 border-primary-500 dark:border-primary-400 shadow-sm':
              activeCatId === item.id,
            'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 border-transparent': activeCatId !== item.id,
          }"
          class="cursor-pointer whitespace-nowrap rounded-full px-4 py-1.5 flex-none transition-all duration-300 text-sm font-medium border"
        >
          {{ item.name }}
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="px-4 py-3">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            v-model="keyword"
            class="w-full rounded-lg border border-gray-300 dark:border-gray-600 pl-10 pr-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 shadow-sm transition-all duration-300"
            placeholder="搜索应用名称"
            type="search"
          />
        </div>
      </div>
    </div>

    <!-- 应用列表 -->
    <div class="flex-1 overflow-y-auto px-4 py-3">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-full">
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-t-2 border-primary-500"></div>
          <p class="mt-4 text-gray-500 dark:text-gray-400">加载应用中...</p>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!filteredApps.length" class="flex justify-center items-center h-full">
        <div class="text-center p-8 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 max-w-md mx-auto">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <p class="text-xl font-bold text-gray-700 dark:text-gray-300">暂无应用</p>
          <p class="text-sm mt-3 max-w-xs mx-auto text-gray-500 dark:text-gray-400">
            当前分类下没有找到应用，请尝试其他分类或清除搜索条件
          </p>
          <button
            v-if="keyword || activeCatId !== 0"
            @click="resetFilters"
            class="mt-4 px-4 py-2 bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400 rounded-lg font-medium text-sm hover:bg-primary-100 dark:hover:bg-primary-800 transition-colors duration-300"
          >
            清除筛选条件
          </button>
        </div>
      </div>

      <!-- 应用卡片网格 -->
      <div v-else class="grid grid-cols-1 sm:grid-cols-2 gap-4 animate-fade-in">
        <div
          v-for="item in filteredApps"
          :key="item.id"
          @click="handleRunApp(item)"
          class="app-card cursor-pointer flex items-start gap-4 rounded-xl bg-white dark:bg-gray-800 p-4 hover:shadow-lg dark:hover:bg-gray-700 border border-gray-100 dark:border-gray-700 transition-all duration-300"
        >
          <!-- 应用图标 -->
          <div
            v-if="item.coverImg && item.coverImg.startsWith('emoji:')"
            class="flex-shrink-0 rounded-lg overflow-hidden shadow-sm w-14 h-14 flex items-center justify-center bg-gray-100 dark:bg-gray-700"
          >
            <span class="text-4xl">{{ item.coverImg.replace('emoji:', '') }}</span>
          </div>
          <div
            v-else-if="item.coverImg"
            class="flex-shrink-0 rounded-lg overflow-hidden shadow-sm"
          >
            <img
              :src="item.coverImg"
              class="w-14 h-14 object-cover"
              alt="app-image"
            />
          </div>
          <div
            v-else
            :class="[
              bgRandomColor()(item),
              'flex-shrink-0 rounded-lg w-14 h-14 flex items-center justify-center shadow-sm',
            ]"
          >
            <span class="text-white text-base font-bold">{{ item.name.slice(0, 2) }}</span>
          </div>

          <!-- 应用信息 -->
          <div class="flex-grow flex flex-col min-w-0">
            <div class="flex items-center justify-between">
              <h3 class="font-semibold text-base text-gray-800 dark:text-gray-200 line-clamp-1 overflow-hidden text-ellipsis pr-2">
                {{ item.name }}
              </h3>
              <button
                @click.stop="handleCollect(item)"
                class="flex-shrink-0 text-gray-400 hover:text-yellow-400 transition-colors duration-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                :class="{'text-yellow-400': isMineApp(item)}"
                :title="isMineApp(item) ? '取消收藏' : '收藏应用'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </button>
            </div>
            <p class="text-sm line-clamp-2 text-gray-500 dark:text-gray-400 mt-1 mb-2">
              {{ item.des || '暂无描述' }}
            </p>
            <div class="flex items-center mt-auto pt-1">
              <span class="text-xs px-2 py-1 rounded-full bg-primary-50 dark:bg-primary-900 text-primary-600 dark:text-primary-400">
                {{ getCatName(item.catId) }}
              </span>
              <span class="ml-auto text-xs text-gray-400 dark:text-gray-500">
                {{ formatDate(item.updatedAt) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import PinyinMatch from 'pinyin-match';
import { fetchQueryAppCatsAPI, fetchQueryAppsAPI, fetchCollectAppAPI } from '@/api/appStore';
import { useAppCatStore } from '@/store';
import type { ResData } from '@/api/types';

interface App {
  id: number;
  name: string;
  des: string;
  coverImg: string;
  catId: number;
  appCount: number;
  demoData: string;
  loading?: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AppCat {
  id: number;
  name: string;
  coverImg: string;
  des: string;
}

const router = useRouter();
const message = useMessage();
const appCatStore = useAppCatStore();

const loading = ref(false);
const keyword = ref('');
const appList = ref<App[]>([]);
const catList = ref<AppCat[]>([]);
const activeCatId = ref(0);
const mineApps = computed(() => appCatStore.mineApps);

// 获取更新应用数量的函数
const updateAppCount = inject('updateAppCount', (count: number) => {});

// 过滤后的应用列表
const filteredApps = computed(() => {
  if (keyword.value) {
    // 使用拼音模糊搜索，支持中文和拼音
    const keywordLower = keyword.value.toLowerCase();
    return appList.value.filter((item) =>
      PinyinMatch.match(item.name, keywordLower)
    );
  }
  if (activeCatId.value === 0) return appList.value;
  return appList.value.filter((item) => item.catId === activeCatId.value);
});

// 监听应用数量变化
watch(() => filteredApps.value.length, (count) => {
  updateAppCount(count);
});

// 判断是否是收藏的应用
function isMineApp(app: App) {
  return mineApps.value.some((item: any) => item.appId === app.id);
}

// 随机背景色
function bgRandomColor() {
  const hues = [
    'bg-gradient-to-br from-blue-500 to-blue-600',
    'bg-gradient-to-br from-red-500 to-red-600',
    'bg-gradient-to-br from-green-500 to-green-600',
    'bg-gradient-to-br from-yellow-500 to-yellow-600',
    'bg-gradient-to-br from-purple-500 to-purple-600',
    'bg-gradient-to-br from-pink-500 to-pink-600',
    'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'bg-gradient-to-br from-teal-500 to-teal-600',
  ];
  // 使用应用ID或名称的哈希值确保同一应用始终有相同的颜色
  const getHashCode = (str) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = ((hash << 5) - hash) + str.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }
    return Math.abs(hash);
  };

  const getColorByName = (name) => {
    const index = getHashCode(name) % hues.length;
    return hues[index];
  };

  return (item) => getColorByName(item.name);
}

// 切换分类
function handleChangeCatId(id: number) {
  activeCatId.value = id;
}

// 收藏/取消收藏应用
async function handleCollect(app: App) {
  app.loading = true;
  try {
    const res: ResData = await fetchCollectAppAPI({ appId: app.id });
    message.success(res.data);
    await appCatStore.queryMineApps();
    app.loading = false;
  } catch (error) {
    app.loading = false;
    message.error('操作失败，请稍后再试');
  }
}

// 运行应用
function handleRunApp(app: App) {
  const appIdAsNumber = Number(app.id);
  router.replace({ path: '/chat', query: { appId: appIdAsNumber } });
}

// 查询应用分类
async function queryCats() {
  try {
    const res: ResData = await fetchQueryAppCatsAPI();
    const defaultCat = {
      id: 0,
      name: '全部分类',
      coverImg: '',
      des: '',
    };
    catList.value = [defaultCat, ...res?.data?.rows];
  } catch (error) {
    console.error('获取应用分类失败:', error);
  }
}

// 查询应用列表
async function queryApps() {
  loading.value = true;
  try {
    const res: ResData = await fetchQueryAppsAPI();
    appList.value = res?.data?.rows.map((item: App) => {
      item.loading = false;
      return item;
    });
  } catch (error) {
    console.error('获取应用列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function resetFilters() {
  keyword.value = '';
  activeCatId.value = 0;
}

// 获取分类名称
function getCatName(catId: number) {
  const cat = catList.value.find(cat => cat.id === catId);
  return cat ? cat.name : '未分类';
}

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return '';

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}周前`;
  } else if (diffDays < 365) {
    return `${Math.floor(diffDays / 30)}月前`;
  } else {
    return `${Math.floor(diffDays / 365)}年前`;
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  await queryCats();
  await queryApps();
  await appCatStore.queryMineApps();

  // 初始化时更新应用数量
  updateAppCount(filteredApps.value.length);
});
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.app-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.app-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.8s ease;
}

.app-card:hover::before {
  transform: translateX(100%);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}
</style>
