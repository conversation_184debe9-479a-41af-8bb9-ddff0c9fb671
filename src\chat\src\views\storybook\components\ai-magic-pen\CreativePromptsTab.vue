<script setup lang="ts">
import { ref, computed } from 'vue';
import { NInput, NButton, NSelect, NCard } from 'naive-ui';

const props = defineProps<{
  projectData: any;
}>();

// 当前选择的提示类别
const selectedCategory = ref('story');

// 提示类别
const categories = [
  { label: '故事创意', value: 'story' },
  { label: '角色设计', value: 'character' },
  { label: '场景描述', value: 'scene' },
  { label: '情节发展', value: 'plot' },
  { label: '教育主题', value: 'educational' }
];

// 创意提示数据
const creativePrompts = {
  story: [
    {
      title: '友谊的力量',
      description: '讲述两个性格迥异的孩子如何通过合作解决问题，建立深厚友谊的故事。',
      tips: '可以设置一个需要两人不同技能共同解决的挑战。'
    },
    {
      title: '勇敢面对恐惧',
      description: '主角害怕某样东西（黑暗、雷声、陌生人等），通过故事学会面对和克服恐惧。',
      tips: '让主角逐步接触恐惧源，每次都获得一点小成功。'
    },
    {
      title: '探索未知世界',
      description: '主角发现一个神秘的地方（可以是森林、洞穴、老房子等），开始一段奇妙冒险。',
      tips: '在探索过程中融入知识点，如动植物知识、历史或科学原理。'
    }
  ],
  character: [
    {
      title: '好奇心旺盛的探险家',
      description: '一个总是问"为什么"的角色，喜欢探索和发现新事物。',
      tips: '可以给角色一个标志性的装备，如放大镜、笔记本或特殊的背包。'
    },
    {
      title: '害羞但才华横溢',
      description: '一个内向的角色，有特殊才能但不善于表达自己。',
      tips: '设计情节让角色在关键时刻克服羞怯，展示才华帮助他人。'
    },
    {
      title: '调皮但善良的捣蛋鬼',
      description: '喜欢恶作剧但内心善良的角色，总是无意中制造麻烦又努力弥补。',
      tips: '通过故事展示角色的成长，从只顾自己开心到考虑他人感受。'
    }
  ],
  scene: [
    {
      title: '魔法森林',
      description: '一个充满奇妙植物和小动物的森林，可能有会说话的树或发光的花朵。',
      tips: '使用丰富的感官描述：颜色、声音、气味，让场景更加生动。'
    },
    {
      title: '繁忙的城市街道',
      description: '热闹的城市场景，有各种商店、行人和街头艺人。',
      tips: '通过小细节展现城市的多样性和包容性，如不同文化背景的人们和食物。'
    },
    {
      title: '神秘的地下洞穴',
      description: '一个隐藏的洞穴系统，可能有水晶、地下河流或古老的壁画。',
      tips: '可以融入地质知识，解释钟乳石和石笋是如何形成的。'
    }
  ],
  plot: [
    {
      title: '失而复得',
      description: '主角丢失了珍贵的物品，在寻找过程中学到重要的人生课程。',
      tips: '让寻找过程中的经历比失物本身更有价值。'
    },
    {
      title: '误会与和解',
      description: '角色之间因误会产生冲突，最终通过沟通和理解解决问题。',
      tips: '展示倾听和换位思考的重要性。'
    },
    {
      title: '意外的责任',
      description: '主角意外获得照顾某物/某人的责任（如一只小动物或一株植物），从中学习责任感。',
      tips: '展示责任既有挑战也有回报。'
    }
  ],
  educational: [
    {
      title: '环保意识',
      description: '通过故事教导孩子保护环境、减少污染和爱护动植物。',
      tips: '用具体、可行的小行动展示孩子如何为环保做贡献。'
    },
    {
      title: '多元文化理解',
      description: '介绍不同文化的传统、节日或生活方式，促进文化理解和尊重。',
      tips: '避免刻板印象，强调文化的丰富性和共通价值。'
    },
    {
      title: '情绪管理',
      description: '帮助孩子认识、表达和管理自己的情绪，如愤怒、悲伤、嫉妒等。',
      tips: '提供具体的情绪管理策略，如深呼吸、数数或寻求帮助。'
    }
  ]
};

// 根据当前类别获取提示
const currentPrompts = computed(() => {
  return creativePrompts[selectedCategory.value] || [];
});

// 自定义搜索
const searchQuery = ref('');
const isSearching = ref(false);
const searchResults = ref([]);

// 搜索提示
const searchPrompts = () => {
  if (!searchQuery.value.trim()) {
    window.$message?.warning('请输入搜索内容');
    return;
  }

  isSearching.value = true;
  
  // 模拟搜索过程
  setTimeout(() => {
    // 在所有类别中搜索匹配的提示
    const results = [];
    
    Object.keys(creativePrompts).forEach(category => {
      const categoryLabel = categories.find(c => c.value === category)?.label || category;
      
      creativePrompts[category].forEach(prompt => {
        if (
          prompt.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          prompt.description.toLowerCase().includes(searchQuery.value.toLowerCase())
        ) {
          results.push({
            ...prompt,
            category: categoryLabel
          });
        }
      });
    });
    
    searchResults.value = results;
    isSearching.value = false;
  }, 500);
};

// 使用提示
const usePrompt = (prompt) => {
  // 这里可以根据需要处理使用提示的逻辑
  // 例如，可以将提示添加到项目的灵感中
  
  if (!props.projectData.inspirations) {
    props.projectData.inspirations = [];
  }
  
  props.projectData.inspirations.push({
    id: Date.now(),
    content: `${prompt.title}\n\n${prompt.description}\n\n提示: ${prompt.tips}`,
    timestamp: new Date().toISOString()
  });
  
  window.$message?.success('已添加到灵感收集');
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  searchResults.value = [];
};
</script>

<template>
  <div class="creative-prompts-container">
    <div class="prompts-header">
      <h3>创意提示库</h3>
      <p>浏览各种创意提示，激发你的灵感</p>
    </div>

    <div class="search-section">
      <NInput
        v-model:value="searchQuery"
        placeholder="搜索创意提示..."
        @keydown.enter="searchPrompts"
      >
        <template #suffix>
          <NButton text @click="searchPrompts" :disabled="isSearching">
            🔍
          </NButton>
        </template>
      </NInput>
    </div>

    <!-- 搜索结果 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <div class="results-header">
        <h4>搜索结果 ({{ searchResults.length }})</h4>
        <NButton size="small" text @click="clearSearch">清除</NButton>
      </div>

      <div class="prompts-grid">
        <NCard
          v-for="(prompt, index) in searchResults"
          :key="index"
          class="prompt-card"
        >
          <div class="prompt-category">{{ prompt.category }}</div>
          <h4 class="prompt-title">{{ prompt.title }}</h4>
          <p class="prompt-description">{{ prompt.description }}</p>
          <div class="prompt-tips">
            <span class="tips-label">创作提示:</span>
            <span class="tips-content">{{ prompt.tips }}</span>
          </div>
          <NButton
            size="small"
            type="primary"
            class="use-prompt-btn"
            @click="usePrompt(prompt)"
          >
            添加到灵感
          </NButton>
        </NCard>
      </div>
    </div>

    <!-- 分类浏览 -->
    <div v-else class="browse-section">
      <div class="category-selector">
        <NSelect
          v-model:value="selectedCategory"
          :options="categories"
          placeholder="选择提示类别"
        />
      </div>

      <div class="prompts-grid">
        <NCard
          v-for="(prompt, index) in currentPrompts"
          :key="index"
          class="prompt-card"
        >
          <h4 class="prompt-title">{{ prompt.title }}</h4>
          <p class="prompt-description">{{ prompt.description }}</p>
          <div class="prompt-tips">
            <span class="tips-label">创作提示:</span>
            <span class="tips-content">{{ prompt.tips }}</span>
          </div>
          <NButton
            size="small"
            type="primary"
            class="use-prompt-btn"
            @click="usePrompt(prompt)"
          >
            添加到灵感
          </NButton>
        </NCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
.creative-prompts-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.prompts-header {
  margin-bottom: 1.5rem;
}

.prompts-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.dark .prompts-header h3 {
  color: #f3f4f6;
}

.prompts-header p {
  color: #6b7280;
  font-size: 0.875rem;
}

.dark .prompts-header p {
  color: #9ca3af;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-results {
  margin-bottom: 1.5rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.results-header h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.dark .results-header h4 {
  color: #f3f4f6;
}

.category-selector {
  margin-bottom: 1.5rem;
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  overflow-y: auto;
}

.prompt-card {
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.prompt-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.prompt-category {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  background-color: #e5e7eb;
  color: #4b5563;
  border-radius: 0.25rem;
}

.dark .prompt-category {
  background-color: #374151;
  color: #d1d5db;
}

.prompt-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1f2937;
}

.dark .prompt-title {
  color: #f3f4f6;
}

.prompt-description {
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  color: #4b5563;
  flex-grow: 1;
}

.dark .prompt-description {
  color: #9ca3af;
}

.prompt-tips {
  font-size: 0.875rem;
  background-color: #f3f4f6;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.dark .prompt-tips {
  background-color: #1f2937;
}

.tips-label {
  font-weight: 600;
  color: #4b5563;
  margin-right: 0.25rem;
}

.dark .tips-label {
  color: #d1d5db;
}

.tips-content {
  color: #6b7280;
}

.dark .tips-content {
  color: #9ca3af;
}

.use-prompt-btn {
  align-self: flex-end;
}
</style>
