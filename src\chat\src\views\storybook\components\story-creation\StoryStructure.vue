<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NCard, NInput, NButton, NSpace, NForm, NFormItem, NSelect, NModal, NSpin } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 大纲状态
const showAIAssistModal = ref(false);
const aiPrompt = ref('');
const isGenerating = ref(false);
const aiResponse = ref('');

// AI助手状态
const authStore = useAuthStore();
const controller = ref(null);

// 故事结构
const storyStructure = reactive({
  title: props.projectData.title || '',
  theme: props.projectData.outline?.theme || '',
  targetAge: props.projectData.outline?.targetAge || '',
  mainIdea: props.projectData.outline?.mainIdea || '',
  beginning: props.projectData.outline?.beginning || '',
  middle: props.projectData.outline?.middle || '',
  end: props.projectData.outline?.end || '',
  moralLesson: props.projectData.outline?.moralLesson || ''
});

// 年龄段选项
const ageOptions = [
  { label: '3-5岁', value: '3-5' },
  { label: '6-8岁', value: '6-8' },
  { label: '9-12岁', value: '9-12' }
];

// 打开AI助手
const openAIAssist = () => {
  aiPrompt.value = '请帮我创建一个适合儿童的故事大纲，包括主题、开头、中间、结尾和寓意。';
  showAIAssistModal.value = true;
};

// 生成AI建议
const generateAIResponse = async () => {
  if (!aiPrompt.value.trim()) {
    window.$message?.warning('请输入提示词');
    return;
  }

  try {
    isGenerating.value = true;
    aiResponse.value = '';

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建提示词
    let prompt = `
作为儿童绘本创作助手，请根据以下要求创建一个故事大纲：

${aiPrompt.value}

请按照以下格式回答：
主题：[故事的主题]
主要理念：[故事想要传达的主要理念]
开头：[故事的开头部分]
中间：[故事的中间部分]
结尾：[故事的结尾部分]
寓意：[故事的寓意或教育意义]
`;

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        // 提取最新的响应内容
        responseText = text;
        aiResponse.value = responseText;
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

  } catch (error) {
    console.error('生成失败:', error);
    window.$message?.error('生成失败，请稍后重试');
  } finally {
    isGenerating.value = false;
    controller.value = null;
  }
};

// 应用AI建议
const applyAIResponse = () => {
  if (!aiResponse.value) return;

  const response = aiResponse.value;

  // 提取主题
  const themeMatch = response.match(/主题[：:]([\s\S]*?)(?=主要理念[：:]|$)/i);
  if (themeMatch && themeMatch[1]) {
    storyStructure.theme = themeMatch[1].trim();
  }

  // 提取主要理念
  const ideaMatch = response.match(/主要理念[：:]([\s\S]*?)(?=开头[：:]|$)/i);
  if (ideaMatch && ideaMatch[1]) {
    storyStructure.mainIdea = ideaMatch[1].trim();
  }

  // 提取开头
  const beginningMatch = response.match(/开头[：:]([\s\S]*?)(?=中间[：:]|$)/i);
  if (beginningMatch && beginningMatch[1]) {
    storyStructure.beginning = beginningMatch[1].trim();
  }

  // 提取中间
  const middleMatch = response.match(/中间[：:]([\s\S]*?)(?=结尾[：:]|$)/i);
  if (middleMatch && middleMatch[1]) {
    storyStructure.middle = middleMatch[1].trim();
  }

  // 提取结尾
  const endMatch = response.match(/结尾[：:]([\s\S]*?)(?=寓意[：:]|$)/i);
  if (endMatch && endMatch[1]) {
    storyStructure.end = endMatch[1].trim();
  }

  // 提取寓意
  const moralMatch = response.match(/寓意[：:]([\s\S]*?)(?=$)/i);
  if (moralMatch && moralMatch[1]) {
    storyStructure.moralLesson = moralMatch[1].trim();
  }

  // 保存更改
  saveOutline();

  window.$message?.success('AI建议已应用到故事大纲');
  showAIAssistModal.value = false;
};

// 保存大纲
const saveOutline = () => {
  props.projectData.title = storyStructure.title;
  props.projectData.outline = {
    ...props.projectData.outline,
    ...storyStructure
  };
  window.$message?.success('故事大纲已保存');
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="story-structure">
    <NCard class="structure-card">
      <div class="card-header">
        <h3 class="card-title">故事基本结构</h3>
        <NButton size="small" @click="openAIAssist" class="ai-assist-btn">
          <SvgIcon name="ri:robot-line" size="14" class="mr-1" />
          AI助手
        </NButton>
      </div>

      <NForm label-placement="left" label-width="auto" :model="storyStructure">
        <NFormItem label="故事标题" required>
          <NInput v-model:value="storyStructure.title" placeholder="输入故事标题" />
        </NFormItem>

        <NFormItem label="故事主题">
          <NInput v-model:value="storyStructure.theme" placeholder="故事的主题或中心思想" />
        </NFormItem>

        <NFormItem label="目标年龄">
          <NSelect v-model:value="storyStructure.targetAge" :options="ageOptions" placeholder="选择目标年龄段" />
        </NFormItem>

        <NFormItem label="主要理念">
          <NInput
            v-model:value="storyStructure.mainIdea"
            type="textarea"
            placeholder="故事想要传达的主要理念或教育目标"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </NFormItem>

        <NFormItem label="故事开头">
          <NInput
            v-model:value="storyStructure.beginning"
            type="textarea"
            placeholder="故事的开始部分，介绍角色和背景"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </NFormItem>

        <NFormItem label="故事中间">
          <NInput
            v-model:value="storyStructure.middle"
            type="textarea"
            placeholder="故事的中间部分，包括冲突和发展"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </NFormItem>

        <NFormItem label="故事结尾">
          <NInput
            v-model:value="storyStructure.end"
            type="textarea"
            placeholder="故事的结尾部分，包括解决方案和结局"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </NFormItem>

        <NFormItem label="故事寓意">
          <NInput
            v-model:value="storyStructure.moralLesson"
            type="textarea"
            placeholder="故事的寓意或教育意义"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </NFormItem>

        <div class="form-actions">
          <NButton type="primary" @click="saveOutline">保存大纲</NButton>
        </div>
      </NForm>
    </NCard>

    <!-- AI助手弹窗 -->
    <NModal
      v-model:show="showAIAssistModal"
      preset="card"
      style="width: 90%; max-width: 700px;"
      title="AI创作助手"
      :bordered="false"
      size="huge"
    >
      <div class="ai-assist-modal">
        <div class="prompt-section">
          <h3 class="section-subtitle">输入提示词</h3>
          <NInput
            v-model:value="aiPrompt"
            type="textarea"
            placeholder="描述你想要的故事类型、主题或元素..."
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
          <div class="prompt-actions">
            <NButton type="primary" @click="generateAIResponse" :loading="isGenerating">
              生成建议
            </NButton>
          </div>
        </div>

        <div class="response-section">
          <h3 class="section-subtitle">AI建议</h3>
          <div class="response-content">
            <NSpin v-if="isGenerating" />
            <div v-else-if="aiResponse" class="response-text">
              {{ aiResponse }}
            </div>
            <div v-else class="empty-response">
              <SvgIcon name="ri:robot-line" size="48" class="empty-icon" />
              <p>点击"生成建议"获取AI创作建议</p>
            </div>
          </div>
          <div class="response-actions">
            <NButton @click="showAIAssistModal = false">取消</NButton>
            <NButton type="primary" @click="applyAIResponse" :disabled="!aiResponse">
              应用建议
            </NButton>
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<style scoped>
.story-structure {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.structure-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .structure-card {
  background-color: #1e293b;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .card-title {
  color: #e2e8f0;
}

.ai-assist-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.ai-assist-modal {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-subtitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .section-subtitle {
  color: #e2e8f0;
}

.prompt-section,
.response-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.prompt-actions,
.response-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.response-content {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
}

.dark .response-content {
  background-color: #0f172a;
  border-color: #334155;
}

.response-text {
  white-space: pre-wrap;
  font-size: 0.875rem;
  line-height: 1.5;
}

.empty-response {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-title {
    font-size: 1.125rem;
  }

  .section-subtitle {
    font-size: 0.875rem;
  }

  .response-content {
    min-height: 150px;
  }
}
</style>
