<template>
  <div class="step-page-container">
    <div class="step-header">
      <h2 class="step-title">完成绘本</h2>
      <p class="step-description">预览你的故事并生成完整绘本</p>
    </div>

    <div class="content-card">
      <div class="card-title">
        <span class="card-title-icon">📚</span>
        {{ projectData.title || '我的绘本故事' }}
      </div>
      <div class="preview-meta">
        <span class="preview-age" v-if="projectData.outline?.ageGroup">适合年龄: {{ getAgeGroupLabel(projectData.outline.ageGroup) }}</span>
        <span class="preview-theme" v-if="projectData.outline?.theme">主题: {{ projectData.outline.theme }}</span>
      </div>

      <div class="preview-content">
        <!-- 故事结构预览 -->
        <div class="preview-section">
          <h5 class="section-title">故事结构</h5>
          <div class="section-content">
            <div class="structure-item" v-if="projectData.outline?.mainIdea">
              <div class="item-label">主要内容:</div>
              <div class="item-value">{{ projectData.outline.mainIdea }}</div>
            </div>
            <div class="structure-item" v-if="projectData.outline?.beginning">
              <div class="item-label">开始:</div>
              <div class="item-value">{{ projectData.outline.beginning }}</div>
            </div>
            <div class="structure-item" v-if="projectData.outline?.middle">
              <div class="item-label">中间:</div>
              <div class="item-value">{{ projectData.outline.middle }}</div>
            </div>
            <div class="structure-item" v-if="projectData.outline?.ending">
              <div class="item-label">结尾:</div>
              <div class="item-value">{{ projectData.outline.ending }}</div>
            </div>
          </div>
        </div>

        <!-- 角色预览 -->
        <div class="preview-section">
          <h5 class="section-title">故事角色</h5>
          <div class="section-content">
            <div class="characters-grid">
              <div
                v-for="character in getCharacters()"
                :key="character.id"
                class="character-card"
              >
                <div class="character-image">
                  <img v-if="character.image" :src="character.image" :alt="character.name" />
                  <div v-else class="image-placeholder">
                    <span class="emoji-icon">{{ getCharacterEmoji(character) }}</span>
                  </div>
                </div>
                <div class="character-info">
                  <div class="character-name">{{ character.name }}</div>
                  <div class="character-traits" v-if="character.traits && character.traits.length > 0">
                    <span
                      v-for="trait in character.traits"
                      :key="trait"
                      class="trait-badge"
                    >
                      {{ getTraitLabel(trait) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 场景预览 -->
        <div class="preview-section">
          <h5 class="section-title">故事场景</h5>
          <div class="section-content">
            <div class="scenes-grid">
              <div
                v-for="(scene, index) in getScenes()"
                :key="scene.id"
                class="scene-card"
              >
                <div class="scene-image">
                  <img v-if="scene.image" :src="scene.image" :alt="scene.title" />
                  <div v-else class="image-placeholder">
                    <span class="emoji-icon">🏞️</span>
                  </div>
                  <div class="scene-number">{{ index + 1 }}</div>
                </div>
                <div class="scene-info">
                  <div class="scene-setting">{{ getSceneSetting(scene) }}</div>
                  <div class="scene-description">{{ scene.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="actions-container">
        <button class="magic-generate-btn" @click="generateStorybook">
          <span>✨</span> 生成完整绘本
        </button>
        <button class="secondary-btn" @click="shareStory">
          <span>🔗</span> 分享故事
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  projectData: Object
});

const isGenerating = ref(false);
const isSharing = ref(false);

const getAgeGroupLabel = (ageGroup) => {
  const ageGroups = {
    '3-5': '3-5岁',
    '6-8': '6-8岁',
    '9-12': '9-12岁'
  };
  return ageGroups[ageGroup] || ageGroup;
};

const getCharacters = () => {
  if (!props.projectData.outline?.characters) {
    return [];
  }

  // 将角色对象转换为数组
  return Object.values(props.projectData.outline.characters)
    .filter(char => char && char.name) // 只返回有效且有名字的角色
    .map(char => ({
      ...char,
      traits: char.traits || [] // 确保traits是数组
    }));
};

const getCharacterEmoji = (character) => {
  const typeEmojis = {
    boy: '👦',
    girl: '👧',
    animal: '🐶',
    magical: '🧚'
  };
  return typeEmojis[character.characterType] || '👤';
};

const getTraitLabel = (traitValue) => {
  const traits = {
    brave: '勇敢',
    smart: '聪明',
    kind: '友善',
    curious: '好奇',
    shy: '害羞',
    mischievous: '调皮',
    creative: '创意',
    funny: '幽默',
    stubborn: '固执',
    optimistic: '乐观',
    cautious: '谨慎',
    helpful: '热心'
  };
  return traits[traitValue] || traitValue;
};

const getScenes = () => {
  const scenes = props.projectData.outline?.scenes || [];
  return scenes.map(scene => ({
    ...scene,
    characters: scene.characters || [] // 确保characters是数组
  }));
};

const getSceneSetting = (scene) => {
  const settings = {
    forest: '🌳 森林',
    school: '🏫 学校',
    home: '🏠 家里',
    park: '🏞️ 公园',
    beach: '🏖️ 海边',
    mountain: '⛰️ 山上',
    city: '🏙️ 城市',
    farm: '🚜 农场',
    zoo: '🦁 动物园',
    space: '🚀 太空',
    underwater: '🐠 海底',
    castle: '🏰 童话城堡'
  };
  return settings[scene.setting] || scene.setting;
};

const generateStorybook = () => {
  isGenerating.value = true;

  // 这里将来可以接入AI生成功能
  // 现在先用简单的模拟

  setTimeout(() => {
    isGenerating.value = false;
    window.$message?.success('绘本生成成功！');

    // 跳转到绘本预览页面
    // 这里可以添加跳转逻辑
  }, 2000);
};

const shareStory = () => {
  isSharing.value = true;

  // 这里将来可以接入分享功能
  // 现在先用简单的模拟

  setTimeout(() => {
    isSharing.value = false;
    window.$message?.success('分享链接已生成！');

    // 显示分享链接
    // 这里可以添加分享逻辑
  }, 1000);
};
</script>

<style scoped>
/* 使用全局统一样式，这里只添加特定于此组件的样式 */

.preview-header {
  margin-bottom: 0.75rem;
  text-align: center;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-meta {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  color: #64748b;
  font-size: 0.8rem;
}

.dark .preview-meta {
  color: #94a3b8;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-section {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 0.75rem;
}

.dark .preview-section {
  background-color: #0f172a;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.25rem;
}

.dark .section-title {
  color: #e2e8f0;
  border-bottom-color: #334155;
}

.section-content {
  color: #475569;
}

.dark .section-content {
  color: #cbd5e1;
}

.structure-item {
  margin-bottom: 0.5rem;
}

.item-label {
  font-weight: 600;
  margin-bottom: 0.15rem;
  color: #64748b;
  font-size: 0.8rem;
}

.dark .item-label {
  color: #94a3b8;
}

.item-value {
  background-color: white;
  padding: 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  line-height: 1.4;
}

.dark .item-value {
  background-color: #1e293b;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
}

.character-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .character-card {
  background-color: #1e293b;
}

.character-image {
  height: 100px;
  position: relative;
  overflow: hidden;
  background-color: #f1f5f9;
}

.dark .character-image {
  background-color: #334155;
}

.character-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.emoji-icon {
  font-size: 2.5rem;
}

.character-info {
  padding: 0.5rem;
}

.character-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.dark .character-name {
  color: #e2e8f0;
}

.character-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.15rem;
}

.trait-badge {
  background-color: #eff6ff;
  color: #3b82f6;
  padding: 0.1rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
}

.dark .trait-badge {
  background-color: #1e40af;
  color: #93c5fd;
}

.scenes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 0.75rem;
}

.scene-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .scene-card {
  background-color: #1e293b;
}

.scene-image {
  height: 100px;
  position: relative;
  overflow: hidden;
  background-color: #f1f5f9;
}

.dark .scene-image {
  background-color: #334155;
}

.scene-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scene-number {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.scene-info {
  padding: 0.5rem;
}

.scene-setting {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.dark .scene-setting {
  color: #e2e8f0;
}

.scene-description {
  font-size: 0.8rem;
  color: #475569;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.dark .scene-description {
  color: #cbd5e1;
}

.preview-meta {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  color: #64748b;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.dark .preview-meta {
  color: #94a3b8;
}

@media (max-width: 768px) {
  .preview-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}
</style>
