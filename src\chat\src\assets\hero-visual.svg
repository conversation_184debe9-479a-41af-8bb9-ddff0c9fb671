<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="500" viewBox="0 0 600 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="300" cy="250" r="200" fill="url(#paint0_radial)" opacity="0.1"/>
  
  <!-- 中心大脑 -->
  <g filter="url(#filter0_d)">
    <path d="M300 150C355.228 150 400 194.772 400 250C400 305.228 355.228 350 300 350C244.772 350 200 305.228 200 250C200 194.772 244.772 150 300 150Z" fill="url(#paint1_linear)"/>
    <path d="M300 160C349.706 160 390 200.294 390 250C390 299.706 349.706 340 300 340C250.294 340 210 299.706 210 250C210 200.294 250.294 160 300 160Z" fill="url(#paint2_radial)"/>
    <path d="M300 180C338.66 180 370 211.34 370 250C370 288.66 338.66 320 300 320C261.34 320 230 288.66 230 250C230 211.34 261.34 180 300 180Z" fill="url(#paint3_linear)"/>
    <path d="M300 200C327.614 200 350 222.386 350 250C350 277.614 327.614 300 300 300C272.386 300 250 277.614 250 250C250 222.386 272.386 200 300 200Z" fill="url(#paint4_radial)"/>
  </g>
  
  <!-- 连接线和节点 -->
  <g opacity="0.8">
    <!-- 编程节点 -->
    <circle cx="150" cy="150" r="30" fill="url(#paint5_linear)" filter="url(#filter1_d)"/>
    <path d="M150 135L165 150L150 165L135 150L150 135Z" fill="white"/>
    <path d="M150 180L300 250" stroke="url(#paint6_linear)" stroke-width="2" stroke-dasharray="4 4"/>
    
    <!-- 写作节点 -->
    <circle cx="120" cy="300" r="30" fill="url(#paint7_linear)" filter="url(#filter1_d)"/>
    <path d="M110 290H130V295H125V310H115V295H110V290Z" fill="white"/>
    <path d="M120 300L300 250" stroke="url(#paint8_linear)" stroke-width="2" stroke-dasharray="4 4"/>
    
    <!-- 绘本节点 -->
    <circle cx="450" cy="150" r="30" fill="url(#paint9_linear)" filter="url(#filter1_d)"/>
    <path d="M440 140H460V160H440V140Z" fill="white"/>
    <path d="M450 180L300 250" stroke="url(#paint10_linear)" stroke-width="2" stroke-dasharray="4 4"/>
    
    <!-- 音乐节点 -->
    <circle cx="480" cy="300" r="30" fill="url(#paint11_linear)" filter="url(#filter1_d)"/>
    <path d="M470 290L490 290L480 310L470 290Z" fill="white"/>
    <path d="M480 300L300 250" stroke="url(#paint12_linear)" stroke-width="2" stroke-dasharray="4 4"/>
  </g>
  
  <!-- 浮动粒子 -->
  <circle cx="200" cy="200" r="5" fill="#4F46E5" opacity="0.7">
    <animate attributeName="cy" values="200;190;200" dur="3s" repeatCount="indefinite" />
  </circle>
  <circle cx="400" cy="200" r="5" fill="#06B6D4" opacity="0.7">
    <animate attributeName="cy" values="200;210;200" dur="4s" repeatCount="indefinite" />
  </circle>
  <circle cx="250" cy="350" r="5" fill="#8B5CF6" opacity="0.7">
    <animate attributeName="cy" values="350;340;350" dur="2.5s" repeatCount="indefinite" />
  </circle>
  <circle cx="350" cy="350" r="5" fill="#EC4899" opacity="0.7">
    <animate attributeName="cy" values="350;360;350" dur="3.5s" repeatCount="indefinite" />
  </circle>
  
  <!-- 光效 -->
  <circle cx="300" cy="250" r="220" stroke="url(#paint13_linear)" stroke-width="1" opacity="0.3">
    <animate attributeName="r" values="220;230;220" dur="4s" repeatCount="indefinite" />
  </circle>
  <circle cx="300" cy="250" r="240" stroke="url(#paint14_linear)" stroke-width="1" opacity="0.2">
    <animate attributeName="r" values="240;250;240" dur="5s" repeatCount="indefinite" />
  </circle>
  
  <!-- 定义渐变 -->
  <defs>
    <filter id="filter0_d" x="190" y="140" width="220" height="220" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.310 0 0 0 0 0.275 0 0 0 0 0.898 0 0 0 0.5 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    <filter id="filter1_d" x="0" y="0" width="30" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(300 250) rotate(90) scale(200)">
      <stop stop-color="#4F46E5"/>
      <stop offset="1" stop-color="#4F46E5" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="paint1_linear" x1="200" y1="150" x2="400" y2="350" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4F46E5"/>
      <stop offset="1" stop-color="#06B6D4"/>
    </linearGradient>
    <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(300 250) rotate(90) scale(90)">
      <stop stop-color="#8B5CF6"/>
      <stop offset="1" stop-color="#4F46E5"/>
    </radialGradient>
    <linearGradient id="paint3_linear" x1="230" y1="180" x2="370" y2="320" gradientUnits="userSpaceOnUse">
      <stop stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#4F46E5"/>
    </linearGradient>
    <radialGradient id="paint4_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(300 250) rotate(90) scale(50)">
      <stop stop-color="#EC4899"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </radialGradient>
    <linearGradient id="paint5_linear" x1="120" y1="120" x2="180" y2="180" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4F46E5"/>
      <stop offset="1" stop-color="#06B6D4"/>
    </linearGradient>
    <linearGradient id="paint6_linear" x1="150" y1="180" x2="300" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4F46E5"/>
      <stop offset="1" stop-color="#06B6D4"/>
    </linearGradient>
    <linearGradient id="paint7_linear" x1="90" y1="270" x2="150" y2="330" gradientUnits="userSpaceOnUse">
      <stop stop-color="#8B5CF6"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="paint8_linear" x1="120" y1="300" x2="300" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#8B5CF6"/>
      <stop offset="1" stop-color="#3B82F6"/>
    </linearGradient>
    <linearGradient id="paint9_linear" x1="420" y1="120" x2="480" y2="180" gradientUnits="userSpaceOnUse">
      <stop stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#4338CA"/>
    </linearGradient>
    <linearGradient id="paint10_linear" x1="450" y1="180" x2="300" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#4338CA"/>
    </linearGradient>
    <linearGradient id="paint11_linear" x1="450" y1="270" x2="510" y2="330" gradientUnits="userSpaceOnUse">
      <stop stop-color="#EC4899"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </linearGradient>
    <linearGradient id="paint12_linear" x1="480" y1="300" x2="300" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#EC4899"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </linearGradient>
    <linearGradient id="paint13_linear" x1="80" y1="250" x2="520" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4F46E5"/>
      <stop offset="0.5" stop-color="#06B6D4"/>
      <stop offset="1" stop-color="#4F46E5"/>
    </linearGradient>
    <linearGradient id="paint14_linear" x1="60" y1="250" x2="540" y2="250" gradientUnits="userSpaceOnUse">
      <stop stop-color="#8B5CF6"/>
      <stop offset="0.5" stop-color="#EC4899"/>
      <stop offset="1" stop-color="#8B5CF6"/>
    </linearGradient>
  </defs>
</svg>
