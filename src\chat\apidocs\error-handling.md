# 错误处理规范

## 错误响应格式

当API请求发生错误时，服务器会返回相应的HTTP状态码和错误信息。错误响应的格式如下：

```json
{
  "code": 400,
  "success": false,
  "message": "错误信息"
}
```

## HTTP状态码

| 状态码 | 说明 |
| ------ | ---- |
| 400 | 请求参数错误 |
| 401 | 未授权或token已过期 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 502 | 网关错误 |
| 503 | 服务不可用 |
| 504 | 网关超时 |

## 常见错误信息

### 认证相关错误

- "亲爱的用户,请登录后继续操作,我们正在等您的到来！" - 未登录或token已过期
- "登录失败，用户凭证无效。" - 用户名或密码错误
- "验证码已过期，请重新发送！" - 验证码过期
- "验证码填写错误，请重新输入！" - 验证码错误

### 用户相关错误

- "用户名已存在！" - 注册时用户名已被占用
- "当前用户信息失效、请重新登录！" - 用户信息不存在或已失效

### AI模型相关错误

- "[Inter Error] 服务端错误[400]" - 服务端错误
- "[Inter Error] 服务出现错误、请稍后再试一次吧[401]" - 服务错误
- "[Inter Error] 服务器拒绝访问，请稍后再试 | Server refused to access, please try again later[403]" - 服务器拒绝访问
- "[Inter Error] 当前key调用频率过高、请重新对话再试一次吧[429]" - 调用频率过高
- "[Inter Error] 错误的网关 |  Bad Gateway[502]" - 网关错误
- "[Inter Error] 服务器繁忙，请稍后再试 | Server is busy, please try again later[503]" - 服务器繁忙
- "[Inter Error] 网关超时 | Gateway Time-out[504]" - 网关超时
- "[Inter Error] 服务器繁忙，请稍后再试 | Internal Server Error[500]" - 服务器内部错误

### 绘画相关错误

- "当前请求已过载，请稍后再试！" - 服务器负载过高
- "当前模型key已被封禁、已冻结当前调用Key、尝试重新对话试试吧！" - API密钥问题
- "生成图像失败，请检查你的提示词是否有非法描述！" - 提示词可能包含不适当内容

## 前端错误处理建议

1. **全局错误处理**：设置全局的HTTP请求拦截器，统一处理错误响应。

```javascript
// 使用axios的例子
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error.response) {
      const { status, data } = error.response;
      
      // 处理401未授权错误
      if (status === 401) {
        // 清除本地token
        localStorage.removeItem('token');
        // 跳转到登录页
        router.push('/login');
      }
      
      // 显示错误消息
      if (data && data.message) {
        showErrorMessage(data.message);
      } else {
        showErrorMessage('请求失败，请稍后再试');
      }
    } else {
      showErrorMessage('网络错误，请检查您的网络连接');
    }
    
    return Promise.reject(error);
  }
);

// 显示错误消息的函数
function showErrorMessage(message) {
  // 使用你的UI库的消息提示组件
  // 例如Element UI的Message组件
  Message.error(message);
}
```

2. **特定接口错误处理**：对于特定接口，可能需要特殊的错误处理逻辑。

```javascript
try {
  const response = await api.chatProcess(params);
  // 处理成功响应
} catch (error) {
  // 处理特定错误
  if (error.response && error.response.data.message.includes('调用频率过高')) {
    // 显示特定错误消息
    showErrorMessage('请求过于频繁，请稍后再试');
  } else {
    // 使用全局错误处理
    throw error;
  }
}
```

3. **流式响应错误处理**：对于流式响应，需要特殊处理。

```javascript
const response = await fetch('/chatgpt/chat-process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(params)
});

if (!response.ok) {
  // 处理HTTP错误
  const errorData = await response.json();
  throw new Error(errorData.message || '请求失败');
}

// 处理流式响应
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const text = decoder.decode(value);
  const lines = text.split('\n').filter(line => line.trim());
  
  for (const line of lines) {
    try {
      const data = JSON.parse(line);
      if (data.errMsg) {
        // 处理流中的错误消息
        showErrorMessage(data.errMsg);
        break;
      }
      // 处理正常数据
      handleStreamData(data);
    } catch (e) {
      console.error('解析流数据失败:', e);
    }
  }
}
```
