<template>
  <div class="student-profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="back-button" @click="goBack">
          <NButton quaternary size="large">
            <template #icon>
              <NIcon><ArrowBackOutline /></NIcon>
            </template>
            返回班级管理
          </NButton>
        </div>
        
        <div class="student-header">
          <div class="student-avatar-section">
            <NAvatar :size="80" :src="student.avatar">
              {{ student.name.charAt(0) }}
            </NAvatar>
            <div 
              class="status-indicator"
              :class="{ 'online': student.isOnline, 'offline': !student.isOnline }"
            ></div>
          </div>
          
          <div class="student-basic-info">
            <h1 class="student-name">{{ student.name }}</h1>
            <div class="student-details">
              <span class="detail-item">学号: {{ student.studentId }}</span>
              <span class="detail-item">分组: {{ getGroupName(student.groupId) }}</span>
              <span class="detail-item">入学: {{ formatDate(student.joinDate) }}</span>
            </div>
            <div class="student-status">
              <NTag :type="student.isOnline ? 'success' : 'default'" size="small">
                {{ student.isOnline ? '在线' : '离线' }}
              </NTag>
              <span class="last-active">最后活跃: {{ formatTimeAgo(student.lastActive) }}</span>
            </div>
          </div>
          
          <div class="header-actions">
            <NButton type="primary" @click="showContactModal = true">
              <template #icon>
                <NIcon><CallOutline /></NIcon>
              </template>
              联系家长
            </NButton>
            <NButton @click="showEditModal = true">
              <template #icon>
                <NIcon><CreateOutline /></NIcon>
              </template>
              编辑信息
            </NButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 学生统计概览 -->
    <div class="stats-overview">
      <NGrid :cols="2" :sm-cols="4" :x-gap="20" :y-gap="20">
        <NGridItem>
          <NCard class="stat-card">
            <div class="stat-content">
              <div class="stat-icon works">
                <NIcon size="24"><BookOutline /></NIcon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ student.worksCount }}</div>
                <div class="stat-label">创作作品</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <div class="stat-content">
              <div class="stat-icon score">
                <NIcon size="24"><TrophyOutline /></NIcon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ student.avgScore }}</div>
                <div class="stat-label">平均分数</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <div class="stat-content">
              <div class="stat-icon progress">
                <NIcon size="24"><TrendingUpOutline /></NIcon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ student.progress }}%</div>
                <div class="stat-label">学习进度</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <div class="stat-content">
              <div class="stat-icon time">
                <NIcon size="24"><TimeOutline /></NIcon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ student.studyHours }}h</div>
                <div class="stat-label">学习时长</div>
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <NTabs v-model:value="activeTab" type="line" size="large">
        <!-- 学习进度 -->
        <NTabPane name="progress" tab="学习进度">
          <div class="progress-section">
            <div class="progress-overview">
              <NCard title="整体进度">
                <div class="progress-circle-wrapper">
                  <div class="progress-circle">
                    <NProgress
                      type="circle"
                      :percentage="student.progress"
                      :color="getProgressColor(student.progress)"
                      :stroke-width="8"
                      :size="120"
                    >
                      <div class="progress-center">
                        <div class="progress-percent">{{ student.progress }}%</div>
                        <div class="progress-status">{{ getProgressStatus(student.progress) }}</div>
                      </div>
                    </NProgress>
                  </div>
                  <div class="progress-details">
                    <div class="detail-row">
                      <span class="detail-label">已完成课程:</span>
                      <span class="detail-value">{{ student.completedCourses }}/{{ student.totalCourses }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">当前阶段:</span>
                      <span class="detail-value">{{ student.currentStage }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">预计完成:</span>
                      <span class="detail-value">{{ student.estimatedCompletion }}</span>
                    </div>
                  </div>
                </div>
              </NCard>
            </div>
            
            <div class="skills-progress">
              <NCard title="技能掌握情况">
                <div class="skills-list">
                  <div v-for="skill in student.skills" :key="skill.name" class="skill-item">
                    <div class="skill-header">
                      <span class="skill-name">{{ skill.name }}</span>
                      <span class="skill-level">{{ skill.level }}%</span>
                    </div>
                    <NProgress
                      :percentage="skill.level"
                      :color="getSkillColor(skill.level)"
                      :show-indicator="false"
                      :height="8"
                    />
                  </div>
                </div>
              </NCard>
            </div>
            
            <div class="learning-path">
              <NCard title="学习路径">
                <div class="path-timeline">
                  <div v-for="(milestone, index) in student.learningPath" :key="index" class="milestone">
                    <div class="milestone-marker" :class="{ 'completed': milestone.completed, 'current': milestone.current }">
                      <NIcon v-if="milestone.completed" size="16"><CheckmarkCircleOutline /></NIcon>
                      <NIcon v-else-if="milestone.current" size="16"><PlayCircleOutline /></NIcon>
                      <div v-else class="milestone-dot"></div>
                    </div>
                    <div class="milestone-content">
                      <div class="milestone-title">{{ milestone.title }}</div>
                      <div class="milestone-description">{{ milestone.description }}</div>
                      <div class="milestone-date">{{ formatDate(milestone.date) }}</div>
                    </div>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>

        <!-- 作品作业 -->
        <NTabPane name="works" tab="作品作业">
          <div class="works-section">
            <div class="works-toolbar">
              <div class="toolbar-left">
                <NSelect
                  v-model:value="worksFilter"
                  :options="worksFilterOptions"
                  placeholder="筛选类型"
                  style="width: 150px;"
                />
                <NSelect
                  v-model:value="worksSort"
                  :options="worksSortOptions"
                  placeholder="排序方式"
                  style="width: 150px;"
                />
              </div>
              <div class="toolbar-right">
                <NButton @click="exportWorks">
                  <template #icon>
                    <NIcon><DownloadOutline /></NIcon>
                  </template>
                  导出作品
                </NButton>
              </div>
            </div>
            
            <div class="works-grid">
              <NGrid :cols="1" :sm-cols="2" :lg-cols="3" :x-gap="20" :y-gap="20">
                <NGridItem v-for="work in filteredWorks" :key="work.id">
                  <NCard class="work-card" hoverable @click="viewWork(work)">
                    <div class="work-preview">
                      <img v-if="work.thumbnail" :src="work.thumbnail" :alt="work.title" class="work-image" />
                      <div v-else class="work-placeholder">
                        <NIcon size="48"><DocumentTextOutline /></NIcon>
                      </div>
                      <div class="work-type-badge">
                        <NTag :type="getWorkTypeColor(work.type)" size="small">
                          {{ work.type }}
                        </NTag>
                      </div>
                    </div>
                    
                    <div class="work-info">
                      <h4 class="work-title">{{ work.title }}</h4>
                      <p class="work-description">{{ work.description }}</p>
                      
                      <div class="work-meta">
                        <div class="meta-row">
                          <span class="meta-label">创作时间:</span>
                          <span class="meta-value">{{ formatDate(work.createdAt) }}</span>
                        </div>
                        <div class="meta-row">
                          <span class="meta-label">状态:</span>
                          <NTag :type="getWorkStatusColor(work.status)" size="small">
                            {{ work.status }}
                          </NTag>
                        </div>
                        <div v-if="work.score" class="meta-row">
                          <span class="meta-label">评分:</span>
                          <span class="meta-value score">{{ work.score }}/100</span>
                        </div>
                      </div>
                      
                      <div class="work-actions">
                        <NButton size="small" @click.stop="viewWork(work)">查看详情</NButton>
                        <NButton size="small" @click.stop="gradeWork(work)" v-if="!work.score">评分</NButton>
                        <NDropdown
                          :options="getWorkActions(work)"
                          @select="handleWorkAction($event, work)"
                          @click.stop
                        >
                          <NButton size="small" quaternary>
                            <template #icon>
                              <NIcon><EllipsisHorizontalOutline /></NIcon>
                            </template>
                          </NButton>
                        </NDropdown>
                      </div>
                    </div>
                  </NCard>
                </NGridItem>
              </NGrid>
            </div>
          </div>
        </NTabPane>

        <!-- 互动记录 -->
        <NTabPane name="interactions" tab="互动记录">
          <div class="interactions-section">
            <div class="interactions-timeline">
              <div v-for="interaction in student.interactions" :key="interaction.id" class="interaction-item">
                <div class="interaction-time">
                  {{ formatDateTime(interaction.time) }}
                </div>
                <div class="interaction-content">
                  <div class="interaction-header">
                    <div class="interaction-type">
                      <NIcon :component="getInteractionIcon(interaction.type)" />
                      <span>{{ interaction.type }}</span>
                    </div>
                    <div class="interaction-subject">{{ interaction.subject }}</div>
                  </div>
                  <div class="interaction-message">{{ interaction.message }}</div>
                  <div v-if="interaction.response" class="interaction-response">
                    <div class="response-label">学生回复:</div>
                    <div class="response-content">{{ interaction.response }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </NTabPane>

        <!-- 家长沟通 -->
        <NTabPane name="communication" tab="家长沟通">
          <div class="communication-section">
            <div class="communication-toolbar">
              <NButton type="primary" @click="showContactModal = true">
                <template #icon>
                  <NIcon><MailOutline /></NIcon>
                </template>
                发送消息
              </NButton>
              <NButton @click="generateReport">
                <template #icon>
                  <NIcon><DocumentTextOutline /></NIcon>
                </template>
                生成报告
              </NButton>
            </div>
            
            <div class="parent-info">
              <NCard title="家长信息">
                <div class="parent-details">
                  <div class="detail-item">
                    <span class="detail-label">联系人:</span>
                    <span class="detail-value">{{ student.parentName || '未设置' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">联系电话:</span>
                    <span class="detail-value">{{ student.contact }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">关系:</span>
                    <span class="detail-value">{{ student.parentRelation || '未设置' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">沟通偏好:</span>
                    <span class="detail-value">{{ student.communicationPreference || '电话' }}</span>
                  </div>
                </div>
              </NCard>
            </div>
            
            <div class="communication-history">
              <NCard title="沟通记录">
                <div class="communication-list">
                  <div v-for="record in student.communicationHistory" :key="record.id" class="communication-record">
                    <div class="record-header">
                      <div class="record-type">
                        <NIcon :component="getCommunicationIcon(record.type)" />
                        <span>{{ record.type }}</span>
                      </div>
                      <div class="record-time">{{ formatDateTime(record.time) }}</div>
                    </div>
                    <div class="record-content">{{ record.content }}</div>
                    <div class="record-status">
                      <NTag :type="record.status === 'replied' ? 'success' : 'warning'" size="small">
                        {{ record.status === 'replied' ? '已回复' : '待回复' }}
                      </NTag>
                    </div>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>
      </NTabs>
    </div>

    <!-- 联系家长弹窗 -->
    <NModal
      v-model:show="showContactModal"
      preset="card"
      title="联系家长"
      style="width: 600px;"
      :bordered="false"
      size="huge"
    >
      <NForm ref="contactFormRef" :model="contactForm" :rules="contactRules">
        <NFormItem label="沟通方式" path="method">
          <NRadioGroup v-model:value="contactForm.method">
            <NSpace>
              <NRadio value="message">发送消息</NRadio>
              <NRadio value="call">电话沟通</NRadio>
              <NRadio value="meeting">预约面谈</NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="主题" path="subject">
          <NInput v-model:value="contactForm.subject" placeholder="请输入沟通主题" />
        </NFormItem>
        
        <NFormItem label="内容" path="content">
          <NInput
            v-model:value="contactForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入沟通内容..."
          />
        </NFormItem>
        
        <NFormItem v-if="contactForm.method === 'meeting'" label="预约时间" path="scheduledTime">
          <NDatePicker
            v-model:value="contactForm.scheduledTime"
            type="datetime"
            clearable
            style="width: 100%;"
          />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <div class="modal-actions">
          <NButton @click="showContactModal = false">取消</NButton>
          <NButton type="primary" @click="sendContact">
            {{ contactForm.method === 'call' ? '记录通话' : contactForm.method === 'meeting' ? '预约面谈' : '发送消息' }}
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 编辑学生信息弹窗 -->
    <NModal
      v-model:show="showEditModal"
      preset="card"
      title="编辑学生信息"
      style="width: 500px;"
      :bordered="false"
      size="huge"
    >
      <NForm ref="editFormRef" :model="editForm" :rules="editRules">
        <NFormItem label="学生姓名" path="name">
          <NInput v-model:value="editForm.name" placeholder="请输入学生姓名" />
        </NFormItem>
        
        <NFormItem label="学号" path="studentId">
          <NInput v-model:value="editForm.studentId" placeholder="请输入学号" />
        </NFormItem>
        
        <NFormItem label="所属分组" path="groupId">
          <NSelect
            v-model:value="editForm.groupId"
            :options="groupOptions"
            placeholder="选择分组"
          />
        </NFormItem>
        
        <NFormItem label="家长姓名" path="parentName">
          <NInput v-model:value="editForm.parentName" placeholder="请输入家长姓名" />
        </NFormItem>
        
        <NFormItem label="联系电话" path="contact">
          <NInput v-model:value="editForm.contact" placeholder="请输入联系电话" />
        </NFormItem>
        
        <NFormItem label="家长关系" path="parentRelation">
          <NSelect
            v-model:value="editForm.parentRelation"
            :options="relationOptions"
            placeholder="选择关系"
          />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <div class="modal-actions">
          <NButton @click="showEditModal = false">取消</NButton>
          <NButton type="primary" @click="saveEdit">保存修改</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import {
  NCard,
  NGrid,
  NGridItem,
  NButton,
  NIcon,
  NAvatar,
  NTag,
  NTabs,
  NTabPane,
  NProgress,
  NSelect,
  NModal,
  NForm,
  NFormItem,
  NInput,
  NRadioGroup,
  NRadio,
  NSpace,
  NDatePicker,
  NDropdown
} from 'naive-ui';
import {
  ArrowBackOutline,
  CallOutline,
  CreateOutline,
  BookOutline,
  TrophyOutline,
  TrendingUpOutline,
  TimeOutline,
  CheckmarkCircleOutline,
  PlayCircleOutline,
  DownloadOutline,
  DocumentTextOutline,
  EllipsisHorizontalOutline,
  MailOutline,
  ChatbubbleEllipsesOutline,
  SchoolOutline,
  GameControllerOutline
} from '@vicons/ionicons5';

const router = useRouter();
const route = useRoute();
const message = useMessage();

// 响应式状态
const activeTab = ref('progress');
const worksFilter = ref('all');
const worksSort = ref('latest');
const showContactModal = ref(false);
const showEditModal = ref(false);

// 表单引用
const contactFormRef = ref();
const editFormRef = ref();

// 表单数据
const contactForm = ref({
  method: 'message',
  subject: '',
  content: '',
  scheduledTime: null
});

const editForm = ref({
  name: '',
  studentId: '',
  groupId: null,
  parentName: '',
  contact: '',
  parentRelation: ''
});

// 学生数据（模拟从路由参数获取）
const student = ref({
  id: parseInt(route.params.id as string) || 1,
  name: '张小明',
  studentId: 'S001',
  avatar: '',
  isOnline: true,
  groupId: 1,
  contact: '138****1234',
  parentName: '张爸爸',
  parentRelation: '父亲',
  communicationPreference: '微信',
  joinDate: new Date('2024-09-01'),
  lastActive: new Date(Date.now() - 30 * 60 * 1000),
  worksCount: 12,
  avgScore: 92,
  progress: 85,
  studyHours: 48,
  completedCourses: 8,
  totalCourses: 12,
  currentStage: '中级创作',
  estimatedCompletion: '2024年12月',
  skills: [
    { name: '创意写作', level: 92 },
    { name: '绘本制作', level: 85 },
    { name: 'AI辅助创作', level: 78 },
    { name: '故事构思', level: 88 },
    { name: '角色设计', level: 82 }
  ],
  learningPath: [
    {
      title: '基础入门',
      description: '了解AI创作工具基本使用',
      date: new Date('2024-09-01'),
      completed: true,
      current: false
    },
    {
      title: '故事创作',
      description: '学习故事结构和角色塑造',
      date: new Date('2024-09-15'),
      completed: true,
      current: false
    },
    {
      title: '绘本制作',
      description: '图文结合的创作技巧',
      date: new Date('2024-10-01'),
      completed: false,
      current: true
    },
    {
      title: '高级创作',
      description: '复杂情节和深度表达',
      date: new Date('2024-11-01'),
      completed: false,
      current: false
    }
  ],
  works: [
    {
      id: 1,
      title: '小兔子的冒险',
      description: '一个关于勇气和友谊的故事',
      type: '绘本',
      status: '已完成',
      score: 95,
      createdAt: new Date('2024-11-20'),
      thumbnail: '/images/work1.jpg'
    },
    {
      id: 2,
      title: '未来科技城',
      description: '科幻主题的创作作品',
      type: '故事',
      status: '进行中',
      score: null,
      createdAt: new Date('2024-11-25'),
      thumbnail: null
    },
    {
      id: 3,
      title: '动物王国',
      description: '介绍各种动物的科普作品',
      type: '科普',
      status: '已完成',
      score: 88,
      createdAt: new Date('2024-11-15'),
      thumbnail: '/images/work3.jpg'
    }
  ],
  interactions: [
    {
      id: 1,
      type: '课堂提问',
      subject: 'AI创作原理',
      message: '老师，AI是怎么帮助我们创作的？',
      response: '很好的问题！AI通过学习大量的文本和图像...',
      time: new Date('2024-11-28 10:30:00')
    },
    {
      id: 2,
      type: '作业反馈',
      subject: '故事结构练习',
      message: '请完善故事的结尾部分，让主题更加明确',
      response: '好的老师，我会重新思考结尾的表达方式',
      time: new Date('2024-11-27 14:20:00')
    }
  ],
  communicationHistory: [
    {
      id: 1,
      type: '学习报告',
      content: '张小明同学本月在AI创作方面表现优异，作品质量不断提升...',
      time: new Date('2024-11-25 16:00:00'),
      status: 'replied'
    },
    {
      id: 2,
      type: '课程提醒',
      content: '明天有AI绘本创作课程，请提前准备相关材料',
      time: new Date('2024-11-27 18:30:00'),
      status: 'sent'
    }
  ]
});

// 选项配置
const groupOptions = [
  { label: '创意小组', value: 1 },
  { label: '探索小组', value: 2 },
  { label: '未分组', value: null }
];

const relationOptions = [
  { label: '父亲', value: '父亲' },
  { label: '母亲', value: '母亲' },
  { label: '爷爷', value: '爷爷' },
  { label: '奶奶', value: '奶奶' },
  { label: '其他', value: '其他' }
];

const worksFilterOptions = [
  { label: '全部类型', value: 'all' },
  { label: '绘本', value: '绘本' },
  { label: '故事', value: '故事' },
  { label: '科普', value: '科普' }
];

const worksSortOptions = [
  { label: '最新创建', value: 'latest' },
  { label: '评分最高', value: 'score' },
  { label: '完成状态', value: 'status' }
];

// 计算属性
const filteredWorks = computed(() => {
  let filtered = student.value.works;
  
  // 类型过滤
  if (worksFilter.value !== 'all') {
    filtered = filtered.filter(work => work.type === worksFilter.value);
  }
  
  // 排序
  filtered.sort((a, b) => {
    switch (worksSort.value) {
      case 'score':
        const scoreA = a.score || 0;
        const scoreB = b.score || 0;
        return scoreB - scoreA;
      case 'status':
        if (a.status === b.status) return 0;
        return a.status === '已完成' ? -1 : 1;
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });
  
  return filtered;
});

// 表单验证规则
const contactRules = {
  subject: [
    { required: true, message: '请输入沟通主题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入沟通内容', trigger: 'blur' }
  ]
};

const editRules = {
  name: [
    { required: true, message: '请输入学生姓名', trigger: 'blur' }
  ],
  studentId: [
    { required: true, message: '请输入学号', trigger: 'blur' }
  ]
};

// 方法
const goBack = () => {
  router.push('/teacher/class');
};

const getGroupName = (groupId: number | null) => {
  const group = groupOptions.find(g => g.value === groupId);
  return group ? group.label : '未分组';
};

const getProgressColor = (progress: number) => {
  if (progress >= 90) return '#52c41a';
  if (progress >= 70) return '#1890ff';
  if (progress >= 50) return '#faad14';
  return '#ff4d4f';
};

const getProgressStatus = (progress: number) => {
  if (progress >= 90) return '优秀';
  if (progress >= 70) return '良好';
  if (progress >= 50) return '一般';
  return '需加强';
};

const getSkillColor = (level: number) => {
  if (level >= 85) return '#52c41a';
  if (level >= 70) return '#1890ff';
  if (level >= 60) return '#faad14';
  return '#ff4d4f';
};

const getWorkTypeColor = (type: string) => {
  const colorMap: { [key: string]: string } = {
    '绘本': 'info',
    '故事': 'success',
    '科普': 'warning'
  };
  return colorMap[type] || 'default';
};

const getWorkStatusColor = (status: string) => {
  const colorMap: { [key: string]: string } = {
    '已完成': 'success',
    '进行中': 'warning',
    '未开始': 'default'
  };
  return colorMap[status] || 'default';
};

const getWorkActions = (work: any) => [
  { key: 'view', label: '查看详情' },
  { key: 'edit', label: '编辑作品' },
  { key: 'download', label: '下载作品' },
  { key: 'share', label: '分享作品' }
];

const getInteractionIcon = (type: string) => {
  const iconMap: { [key: string]: any } = {
    '课堂提问': ChatbubbleEllipsesOutline,
    '作业反馈': BookOutline,
    '课程活动': SchoolOutline,
    '游戏互动': GameControllerOutline
  };
  return iconMap[type] || ChatbubbleEllipsesOutline;
};

const getCommunicationIcon = (type: string) => {
  const iconMap: { [key: string]: any } = {
    '学习报告': DocumentTextOutline,
    '课程提醒': MailOutline,
    '成绩通知': TrophyOutline,
    '家长会': SchoolOutline
  };
  return iconMap[type] || MailOutline;
};

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN');
};

const formatDateTime = (date: Date) => {
  return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatTimeAgo = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return `${days}天前`;
  }
};

const viewWork = (work: any) => {
  message.info(`查看作品: ${work.title}`);
};

const gradeWork = (work: any) => {
  message.info(`为作品"${work.title}"评分`);
};

const handleWorkAction = (key: string, work: any) => {
  switch (key) {
    case 'view':
      viewWork(work);
      break;
    case 'edit':
      message.info('编辑作品功能开发中...');
      break;
    case 'download':
      message.info('下载作品功能开发中...');
      break;
    case 'share':
      message.info('分享作品功能开发中...');
      break;
  }
};

const exportWorks = () => {
  message.info('导出作品功能开发中...');
};

const sendContact = () => {
  contactFormRef.value?.validate((errors: any) => {
    if (!errors) {
      // 发送联系逻辑
      const record = {
        id: Date.now(),
        type: contactForm.value.subject,
        content: contactForm.value.content,
        time: new Date(),
        status: 'sent'
      };
      student.value.communicationHistory.unshift(record);
      
      showContactModal.value = false;
      contactForm.value = {
        method: 'message',
        subject: '',
        content: '',
        scheduledTime: null
      };
      message.success('联系信息已发送');
    }
  });
};

const saveEdit = () => {
  editFormRef.value?.validate((errors: any) => {
    if (!errors) {
      // 保存编辑逻辑
      Object.assign(student.value, editForm.value);
      showEditModal.value = false;
      message.success('学生信息已更新');
    }
  });
};

const generateReport = () => {
  message.info('生成学习报告功能开发中...');
};

onMounted(() => {
  // 初始化编辑表单
  editForm.value = {
    name: student.value.name,
    studentId: student.value.studentId,
    groupId: student.value.groupId,
    parentName: student.value.parentName,
    contact: student.value.contact,
    parentRelation: student.value.parentRelation
  };
});
</script>

<style scoped lang="scss">
.student-profile-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  
  .header-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .back-button {
    align-self: flex-start;
  }
  
  .student-header {
    display: flex;
    align-items: center;
    gap: 24px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
    }
  }
  
  .student-avatar-section {
    position: relative;
    
    .status-indicator {
      position: absolute;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 3px solid white;
      bottom: 4px;
      right: 4px;
      
      &.online { background: #52c41a; }
      &.offline { background: #d9d9d9; }
    }
  }
  
  .student-basic-info {
    flex: 1;
    
    .student-name {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 12px 0;
    }
    
    .student-details {
      display: flex;
      gap: 24px;
      margin-bottom: 8px;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 8px;
      }
      
      .detail-item {
        font-size: 14px;
        opacity: 0.9;
      }
    }
    
    .student-status {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .last-active {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }
}

.stats-overview {
  margin-bottom: 32px;
  
  .stat-card {
    height: 100%;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    
    &.works { background: #3b82f6; }
    &.score { background: #f59e0b; }
    &.progress { background: #10b981; }
    &.time { background: #8b5cf6; }
  }
  
  .stat-info {
    flex: 1;
  }
  
  .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 4px;
  }
  
  .stat-label {
    color: #6b7280;
    font-size: 14px;
  }
}

.main-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
  
  .progress-overview {
    grid-row: span 2;
    
    @media (max-width: 1024px) {
      grid-row: span 1;
    }
  }
  
  .progress-circle-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    
    .progress-circle {
      .progress-center {
        text-align: center;
        
        .progress-percent {
          font-size: 18px;
          font-weight: 700;
          color: #1f2937;
        }
        
        .progress-status {
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
    
    .progress-details {
      width: 100%;
      
      .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .detail-label {
          color: #6b7280;
          font-size: 14px;
        }
        
        .detail-value {
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }
  
  .skills-list {
    .skill-item {
      margin-bottom: 16px;
      
      .skill-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .skill-name {
          font-weight: 500;
          color: #1f2937;
        }
        
        .skill-level {
          font-size: 14px;
          color: #6b7280;
        }
      }
    }
  }
  
  .learning-path {
    grid-column: span 2;
    
    @media (max-width: 1024px) {
      grid-column: span 1;
    }
  }
  
  .path-timeline {
    .milestone {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;
      
      .milestone-marker {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 2px solid #e5e7eb;
        background: white;
        
        &.completed {
          border-color: #52c41a;
          background: #52c41a;
          color: white;
        }
        
        &.current {
          border-color: #1890ff;
          background: #1890ff;
          color: white;
        }
        
        .milestone-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #d1d5db;
        }
      }
      
      .milestone-content {
        flex: 1;
        
        .milestone-title {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
        }
        
        .milestone-description {
          color: #6b7280;
          font-size: 14px;
          margin-bottom: 4px;
        }
        
        .milestone-date {
          color: #9ca3af;
          font-size: 12px;
        }
      }
    }
  }
}

.works-section {
  .works-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
    }
  }
  
  .work-card {
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  .work-preview {
    position: relative;
    margin-bottom: 16px;
    
    .work-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 8px;
    }
    
    .work-placeholder {
      width: 100%;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f3f4f6;
      border-radius: 8px;
      color: #9ca3af;
    }
    
    .work-type-badge {
      position: absolute;
      top: 8px;
      right: 8px;
    }
  }
  
  .work-info {
    .work-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 8px 0;
      color: #1f2937;
    }
    
    .work-description {
      color: #6b7280;
      font-size: 14px;
      margin: 0 0 16px 0;
      line-height: 1.4;
    }
    
    .work-meta {
      margin-bottom: 16px;
      
      .meta-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 14px;
        
        .meta-label {
          color: #6b7280;
        }
        
        .meta-value {
          color: #1f2937;
          
          &.score {
            font-weight: 600;
            color: #f59e0b;
          }
        }
      }
    }
    
    .work-actions {
      display: flex;
      gap: 8px;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.interactions-section {
  .interactions-timeline {
    .interaction-item {
      border-left: 3px solid #e5e7eb;
      padding-left: 20px;
      margin-bottom: 24px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 8px;
        width: 9px;
        height: 9px;
        border-radius: 50%;
        background: #3b82f6;
      }
      
      .interaction-time {
        font-size: 12px;
        color: #9ca3af;
        margin-bottom: 8px;
      }
      
      .interaction-content {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        
        .interaction-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .interaction-type {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            font-weight: 500;
            color: #3b82f6;
          }
          
          .interaction-subject {
            font-size: 14px;
            color: #6b7280;
          }
        }
        
        .interaction-message {
          color: #1f2937;
          margin-bottom: 8px;
          line-height: 1.5;
        }
        
        .interaction-response {
          border-top: 1px solid #e5e7eb;
          padding-top: 8px;
          margin-top: 8px;
          
          .response-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
          }
          
          .response-content {
            color: #4b5563;
            font-style: italic;
          }
        }
      }
    }
  }
}

.communication-section {
  .communication-toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
  }
  
  .parent-info {
    margin-bottom: 24px;
    
    .parent-details {
      .detail-item {
        display: flex;
        margin-bottom: 12px;
        
        .detail-label {
          width: 100px;
          color: #6b7280;
          font-size: 14px;
        }
        
        .detail-value {
          color: #1f2937;
          font-weight: 500;
        }
      }
    }
  }
  
  .communication-list {
    .communication-record {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      
      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .record-type {
          display: flex;
          align-items: center;
          gap: 6px;
          font-weight: 500;
          color: #1f2937;
        }
        
        .record-time {
          font-size: 12px;
          color: #6b7280;
        }
      }
      
      .record-content {
        color: #4b5563;
        margin-bottom: 8px;
        line-height: 1.5;
      }
      
      .record-status {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .student-profile-page {
    padding: 16px;
  }
  
  .progress-section {
    grid-template-columns: 1fr !important;
  }
  
  .works-toolbar {
    flex-direction: column !important;
    gap: 16px !important;
    align-items: stretch !important;
    
    .toolbar-left {
      flex-direction: column !important;
    }
  }
}
</style> 