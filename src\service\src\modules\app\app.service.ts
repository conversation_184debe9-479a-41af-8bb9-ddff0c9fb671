import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Request } from 'express';
import { DataSource, In, IsNull, Like, MoreThan, Not, Repository } from 'typeorm';
import { BatchImportAppsDto } from './dto/batchImportApps.dto';
import { AppEntity } from './app.entity';
import { AppCatsEntity } from './appCats.entity';
import { FormEntity } from './form.entity';
import { CollectAppDto } from './dto/collectApp.dto';
import { CreateAppDto } from './dto/createApp.dto';
import { CreateCatsDto } from './dto/createCats.dto';
import { OperateAppDto } from './dto/deleteApp.dto';
import { DeleteCatsDto } from './dto/deleteCats.dto';
import { QuerAppDto } from './dto/queryApp.dto';
import { QuerCatsDto } from './dto/queryCats.dto';
import { UpdateAppDto } from './dto/updateApp.dto';
import { UpdateCatsDto } from './dto/updateCats.dto';
import { UserAppsEntity } from './userApps.entity';
import { FormService } from './form.service';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  constructor(
    @InjectRepository(AppCatsEntity)
    private readonly appCatsEntity: Repository<AppCatsEntity>,
    @InjectRepository(AppEntity)
    private readonly appEntity: Repository<AppEntity>,
    @InjectRepository(UserAppsEntity)
    private readonly userAppsEntity: Repository<UserAppsEntity>,
    @InjectRepository(FormEntity)
    private readonly formEntity: Repository<FormEntity>,
    private readonly formService: FormService,
    private readonly dataSource: DataSource
  ) {}

  async createAppCat(body: CreateCatsDto) {
    const { name } = body;
    const c = await this.appCatsEntity.findOne({ where: { name } });
    if (c) {
      throw new HttpException('该分类名称已存在！', HttpStatus.BAD_REQUEST);
    }
    return await this.appCatsEntity.save(body);
  }

  async delAppCat(body: DeleteCatsDto) {
    const { id } = body;
    const c = await this.appCatsEntity.findOne({ where: { id } });
    if (!c) {
      throw new HttpException('该分类不存在！', HttpStatus.BAD_REQUEST);
    }
    const count = await this.appEntity.count({ where: { catId: id } });
    if (count > 0) {
      throw new HttpException(
        '该分类下存在App，不可删除！',
        HttpStatus.BAD_REQUEST
      );
    }
    const res = await this.appCatsEntity.delete(id);
    if (res.affected > 0) return '删除成功';
    throw new HttpException('删除失败！', HttpStatus.BAD_REQUEST);
  }

  async updateAppCats(body: UpdateCatsDto) {
    const { id, name } = body;
    const c = await this.appCatsEntity.findOne({
      where: { name, id: Not(id) },
    });
    if (c) {
      throw new HttpException('该分类名称已存在！', HttpStatus.BAD_REQUEST);
    }
    const res = await this.appCatsEntity.update({ id }, body);
    if (res.affected > 0) return '修改成功';
    throw new HttpException('修改失败！', HttpStatus.BAD_REQUEST);
  }

  async queryOneCat(params) {
    try {
      const { id } = params;
      if (!id) {
        this.logger.warn('查询应用详情失败: 缺少应用ID');
        throw new HttpException('缺失必要参数！', HttpStatus.BAD_REQUEST);
      }

      const app = await this.appEntity.findOne({ where: { id } });
      if (!app) {
        this.logger.warn(`查询应用详情失败: 应用ID ${id} 不存在`);
        throw new HttpException('应用不存在！', HttpStatus.BAD_REQUEST);
      }

      const {
        demoData: demo,
        coverImg,
        des,
        name,
        isFixedModel,
        isGPTs,
        appModel,
      } = app;

      // 使用表单服务查询应用关联的表单
      const forms = await this.formService.getFormsByAppId(id);

      this.logger.log(`查询应用详情成功: ID=${id}, 名称="${name}", 关联表单数量=${forms.length}`);
      return {
        demoData: demo ? demo.split('\n') : [],
        coverImg,
        des,
        name,
        isGPTs,
        isFixedModel,
        appModel,
        forms: forms || [],
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`查询应用详情时发生未知错误: ${error.message}`, error.stack);
      throw new HttpException('查询应用详情失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async appCatsList(query: QuerCatsDto) {
    const { page = 1, size = 10, name, status } = query;
    const where: any = {};
    name && (where.name = Like(`%${name}%`));
    [0, 1, '0', '1'].includes(status) && (where.status = status);
    const [rows, count] = await this.appCatsEntity.findAndCount({
      where,
      order: { order: 'DESC' },
      skip: (page - 1) * size,
      take: size,
    });
    // 查出所有分类下对应的App数量
    const catIds = rows.map((item) => item.id);
    const apps = await this.appEntity.find({ where: { catId: In(catIds) } });
    const appCountMap = {};
    apps.forEach((item) => {
      if (appCountMap[item.catId]) {
        appCountMap[item.catId] += 1;
      } else {
        appCountMap[item.catId] = 1;
      }
    });
    rows.forEach((item: any) => (item.appCount = appCountMap[item.id] || 0));
    return { rows, count };
  }

  async appList(req: Request, query: QuerAppDto, orderKey = 'id') {
    const { page = 1, size = 10, name, status, catId, role } = query;
    const where: any = { isSystemReserved: 0 };
    name && (where.name = Like(`%${name}%`));
    catId && (where.catId = catId);
    role && (where.role = role);
    status && (where.status = status);
    const [rows, count] = await this.appEntity.findAndCount({
      where,
      order: { [orderKey]: 'DESC' },
      skip: (page - 1) * size,
      take: size,
    });
    const catIds = rows.map((item) => item.catId);
    const cats = await this.appCatsEntity.find({ where: { id: In(catIds) } });
    rows.forEach((item: any) => {
      const cat = cats.find((c) => c.id === item.catId);
      item.catName = cat ? cat.name : '';
    });
    if (req?.user?.role !== 'super') {
      rows.forEach((item: any) => {
        delete item.preset;
      });
    }
    return { rows, count };
  }

  async frontAppList(req: Request, query: QuerAppDto, orderKey = 'id') {
    const { page = 1, size = 1000, name, catId, role } = query;
    const where: any = [
      {
        status: In([1, 4]),
        userId: IsNull(),
        public: false,
        isSystemReserved: 0,
      },
      { userId: MoreThan(0), public: true },
    ];
    const [rows, count] = await this.appEntity.findAndCount({
      where,
      order: { order: 'DESC' },
      skip: (page - 1) * size,
      take: size,
    });
    const catIds = rows.map((item) => item.catId);
    const cats = await this.appCatsEntity.find({ where: { id: In(catIds) } });
    rows.forEach((item: any) => {
      const cat = cats.find((c) => c.id === item.catId);
      item.catName = cat ? cat.name : '';
    });
    if (req?.user?.role !== 'super') {
      rows.forEach((item: any) => {
        delete item.preset;
      });
    }
    return { rows, count };
  }

  async searchAppList(body: any) {
    console.log('搜索App列表', body);
    const { page = 1, size = 1000, keyword } = body;
    console.log(`搜索关键词：${keyword}`);

    // 基础查询条件，可以根据实际情况调整
    let baseWhere: any = [
      {
        status: In([1, 4]),
        userId: IsNull(),
        public: false,
        isSystemReserved: 0,
      },
      { userId: MoreThan(0), public: true },
    ];

    console.log('初始查询条件：', JSON.stringify(baseWhere));

    // 如果存在关键字，修改查询条件以同时搜索 name 和 description
    if (keyword) {
      baseWhere = baseWhere.map((condition) => ({
        ...condition,
        name: Like(`%${keyword}%`),
      }));
      console.log('更新后的查询条件：', JSON.stringify(baseWhere));
    }
    try {
      const [rows, count] = await this.appEntity.findAndCount({
        where: baseWhere,
        skip: (page - 1) * size,
        take: size,
      });
      console.log(`查询返回 ${count} 条结果，显示第 ${page} 页的结果。`);

      rows.forEach((item: any) => {
        delete item.preset; // 假设preset是不需要返回给前端的敏感信息
      });

      console.log('完成查询，准备返回结果');
      return { rows, count };
    } catch (error) {
      console.error('查询数据库时出错：', error);
      throw new Error('Database query failed');
    }
  }

  async createApp(body: CreateAppDto) {
    const { name, catId } = body;
    body.role = 'system';
    const a = await this.appEntity.findOne({ where: { name } });
    if (a) {
      throw new HttpException('该应用名称已存在！', HttpStatus.BAD_REQUEST);
    }
    const c = await this.appCatsEntity.findOne({ where: { id: catId } });
    if (!c) {
      throw new HttpException('该分类不存在！', HttpStatus.BAD_REQUEST);
    }
    return await this.appEntity.save(body);
  }

  async updateApp(body: UpdateAppDto) {
    const { id, name, catId, status } = body;
    const a = await this.appEntity.findOne({ where: { name, id: Not(id) } });
    if (a) {
      throw new HttpException('该应用名称已存在！', HttpStatus.BAD_REQUEST);
    }
    const c = await this.appCatsEntity.findOne({ where: { id: catId } });
    if (!c) {
      throw new HttpException('该分类不存在！', HttpStatus.BAD_REQUEST);
    }
    const curApp = await this.appEntity.findOne({ where: { id } });
    if (curApp.status !== body.status) {
      await this.userAppsEntity.update({ appId: id }, { status });
    }
    const res = await this.appEntity.update({ id }, body);
    if (res.affected > 0) return '修改App信息成功';
    throw new HttpException('修改App信息失败！', HttpStatus.BAD_REQUEST);
  }

  async delApp(body: OperateAppDto) {
    const { id } = body;
    this.logger.log(`开始删除应用，ID: ${id}`);

    // 检查应用是否存在
    const a = await this.appEntity.findOne({ where: { id } });
    if (!a) {
      this.logger.warn(`删除应用失败: 应用ID ${id} 不存在`);
      throw new HttpException('该应用不存在！', HttpStatus.BAD_REQUEST);
    }

    // 查询该应用的收藏记录数量
    const useApp = await this.userAppsEntity.count({ where: { appId: id } });
    this.logger.log(`应用ID ${id} 有 ${useApp} 条收藏记录`);

    // 使用事务确保数据一致性
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 先删除该应用的所有收藏记录
      if (useApp > 0) {
        const deleteResult = await queryRunner.manager.delete(UserAppsEntity, { appId: id });
        this.logger.log(`已删除应用ID ${id} 的 ${deleteResult.affected} 条收藏记录`);
      }

      // 然后删除应用本身
      const res = await queryRunner.manager.delete(AppEntity, id);

      // 提交事务
      await queryRunner.commitTransaction();

      if (res.affected > 0) {
        this.logger.log(`成功删除应用ID ${id}`);
        return '删除App成功';
      }

      this.logger.warn(`删除应用ID ${id} 失败: 影响行数为0`);
      throw new HttpException('删除App失败！', HttpStatus.BAD_REQUEST);
    } catch (error) {
      // 回滚事务
      await queryRunner.rollbackTransaction();
      this.logger.error(`删除应用ID ${id} 时发生错误: ${error.message}`, error.stack);
      throw new HttpException(`删除App失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      // 释放查询运行器
      await queryRunner.release();
    }
  }

  async auditPass(body: OperateAppDto) {
    const { id } = body;
    const a = await this.appEntity.findOne({ where: { id, status: 3 } });
    if (!a) {
      throw new HttpException('该应用不存在！', HttpStatus.BAD_REQUEST);
    }
    await this.appEntity.update({ id }, { status: 4 });
    /* 同步变更useApp status  */
    await this.userAppsEntity.update({ appId: id }, { status: 4 });
    return '应用审核通过';
  }

  async auditFail(body: OperateAppDto) {
    const { id } = body;
    const a = await this.appEntity.findOne({ where: { id, status: 3 } });
    if (!a) {
      throw new HttpException('该应用不存在！', HttpStatus.BAD_REQUEST);
    }
    await this.appEntity.update({ id }, { status: 5 });
    /* 同步变更useApp status  */
    await this.userAppsEntity.update({ appId: id }, { status: 5 });
    return '应用审核拒绝完成';
  }

  async collect(body: CollectAppDto, req: Request) {
    const { appId } = body;
    const { id: userId } = req.user;
    const historyApp = await this.userAppsEntity.findOne({
      where: { appId, userId },
    });
    if (historyApp) {
      const r = await this.userAppsEntity.delete({ appId, userId });
      if (r.affected > 0) {
        return '取消收藏成功!';
      } else {
        throw new HttpException('取消收藏失败！', HttpStatus.BAD_REQUEST);
      }
    }
    const app = await this.appEntity.findOne({ where: { id: appId } });
    const { id, role: appRole, catId } = app;
    const collectInfo = {
      userId,
      appId: id,
      catId,
      appRole,
      public: true,
      status: 1,
    };
    await this.userAppsEntity.save(collectInfo);
    return '已将应用加入到我的收藏！';
  }

  // async mineApps(req: Request, query = { page: 1, size: 30 }) {
  //   const { id } = req.user;
  //   const { page = 1, size = 30 } = query;
  //   const [rows, count] = await this.userAppsEntity.findAndCount({
  //     where: { userId: id, status: In([1, 3, 4, 5]) },
  //     order: { id: 'DESC' },
  //     skip: (page - 1) * size,
  //     take: size,
  //   });

  //   const appIds = rows.map((item) => item.appId);
  //   const appsInfo = await this.appEntity.find({ where: { id: In(appIds) } });
  //   rows.forEach((item: any) => {
  //     const app = appsInfo.find((c) => c.id === item.appId);
  //     item.appName = app ? app.name : '';
  //     item.appRole = app ? app.role : '';
  //     item.appDes = app ? app.des : '';
  //     item.coverImg = app ? app.coverImg : '';
  //     item.demoData = app ? app.demoData : '';
  //     item.preset = app.userId === id ? app.preset : '******';
  //   });
  //   return { rows, count };
  // }

  async mineApps(req: Request, query = { page: 1, size: 30 }) {
    // 记录函数调用时间和查询参数
    // console.log(`mineApps 在 ${new Date().toISOString()} 被调用，查询参数为：`, query);

    const { id } = req.user;
    // console.log(`正在处理用户ID: ${id} 的mineApps请求`); // 记录处理的用户ID

    const { page = 1, size = 30 } = query;
    let rows, count;

    try {
      [rows, count] = await this.userAppsEntity.findAndCount({
        where: { userId: id, status: In([1, 3, 4, 5]) },
        order: { id: 'DESC' },
        skip: (page - 1) * size,
        take: size,
      });

      const appIds = rows.map((item) => item.appId);
      const appsInfo = await this.appEntity.find({ where: { id: In(appIds) } });

      rows.forEach((item: any) => {
        const app = appsInfo.find((c) => c.id === item.appId);
        item.appName = app ? app.name : '未知';
        item.appRole = app ? app.role : '未知';
        item.appDes = app ? app.des : '未知';
        item.coverImg = app ? app.coverImg : '未知';
        item.demoData = app ? app.demoData : '未知';
        item.preset = app && app.userId === id ? app.preset : '******';
        // 标记是否是无效应用（已被删除的应用）
        item.isInvalid = !app;
      });
    } catch (error) {
      console.error(`处理用户ID: ${id} 的mineApps请求时发生错误`, error); // 记录可能的错误
      throw error; // 抛出错误，确保上层调用者知道发生了错误
    }

    return { rows, count };
  }

  /**
   * 清理用户收藏列表中的无效应用（已被删除的应用）
   * @param req 请求对象，包含用户信息
   * @returns 清理结果
   */
  async cleanInvalidCollections(req: Request) {
    const { id: userId } = req.user;
    this.logger.log(`开始清理用户ID ${userId} 的无效收藏应用`);

    try {
      // 1. 获取用户所有收藏的应用ID
      const userApps = await this.userAppsEntity.find({
        where: { userId },
        select: ['appId'],
      });

      if (userApps.length === 0) {
        this.logger.log(`用户ID ${userId} 没有收藏的应用`);
        return { message: '没有收藏的应用', deletedCount: 0 };
      }

      const appIds = userApps.map(item => item.appId);
      this.logger.log(`用户ID ${userId} 收藏了 ${appIds.length} 个应用`);

      // 2. 查询这些应用ID中哪些在app表中不存在
      const existingApps = await this.appEntity.find({
        where: { id: In(appIds) },
        select: ['id'],
      });

      const existingAppIds = existingApps.map(app => app.id);
      this.logger.log(`在这些收藏中，有 ${existingAppIds.length} 个应用仍然存在`);

      // 3. 找出不存在的应用ID
      const invalidAppIds = appIds.filter(id => !existingAppIds.includes(id));
      this.logger.log(`发现 ${invalidAppIds.length} 个无效的收藏应用`);

      if (invalidAppIds.length === 0) {
        return { message: '没有无效的收藏应用', deletedCount: 0 };
      }

      // 4. 删除这些无效的收藏记录
      const deleteResult = await this.userAppsEntity.delete({
        userId,
        appId: In(invalidAppIds),
      });

      this.logger.log(`成功删除 ${deleteResult.affected} 条无效收藏记录`);

      return {
        message: `成功清理 ${deleteResult.affected} 个无效的收藏应用`,
        deletedCount: deleteResult.affected,
      };
    } catch (error) {
      this.logger.error(`清理用户ID ${userId} 的无效收藏应用时发生错误: ${error.message}`, error.stack);
      throw new HttpException(`清理无效收藏失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取应用关联的表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  async getFormsByAppId(appId: number) {
    return this.formService.getFormsByAppId(appId);
  }

  /**
   * 批量导入应用及表单
   * @param body 包含JSON数据和验证标志
   * @returns 导入结果
   */
  async batchImportApps(body: BatchImportAppsDto) {
    const { jsonContent, validateOnly } = body;

    try {
      this.logger.log(`开始批量导入应用，验证模式: ${validateOnly}`);

      // 验证JSON数据
      if (!jsonContent || jsonContent.trim() === '') {
        throw new HttpException('JSON数据不能为空', HttpStatus.BAD_REQUEST);
      }

      // 解析JSON数据
      let appDataList;
      try {
        appDataList = JSON.parse(jsonContent);
        if (!Array.isArray(appDataList)) {
          throw new Error('JSON数据必须是数组格式');
        }
      } catch (error) {
        this.logger.error(`JSON解析失败: ${error.message}`);
        throw new HttpException(`JSON格式错误: ${error.message}`, HttpStatus.BAD_REQUEST);
      }

      // 验证JSON数据结构
      for (let i = 0; i < appDataList.length; i++) {
        const appData = appDataList[i];
        if (!appData.app) {
          throw new HttpException(`第${i+1}项数据缺少app字段`, HttpStatus.BAD_REQUEST);
        }
        if (!appData.forms || !Array.isArray(appData.forms) || appData.forms.length === 0) {
          throw new HttpException(`第${i+1}项数据缺少forms字段或forms不是数组或为空`, HttpStatus.BAD_REQUEST);
        }

        // 验证应用必填字段
        const requiredAppFields = ['name', 'catId', 'des', 'preset'];
        for (const field of requiredAppFields) {
          if (!appData.app[field]) {
            throw new HttpException(`第${i+1}项应用数据缺少必填字段: ${field}`, HttpStatus.BAD_REQUEST);
          }
        }

        // 验证表单必填字段
        for (let j = 0; j < appData.forms.length; j++) {
          const form = appData.forms[j];
          const requiredFormFields = ['name', 'fields'];
          for (const field of requiredFormFields) {
            if (!form[field]) {
              throw new HttpException(`第${i+1}项应用的第${j+1}个表单缺少必填字段: ${field}`, HttpStatus.BAD_REQUEST);
            }
          }

          // 验证表单字段是否为有效的JSON
          try {
            const fieldsObj = JSON.parse(form.fields);
            if (!Array.isArray(fieldsObj)) {
              throw new Error('表单字段必须是数组格式');
            }
          } catch (error) {
            throw new HttpException(`第${i+1}项应用的第${j+1}个表单的fields字段不是有效的JSON: ${error.message}`, HttpStatus.BAD_REQUEST);
          }
        }
      }

      // 如果仅验证，返回成功
      if (validateOnly) {
        this.logger.log('验证模式: JSON数据验证通过');
        return { code: 200, message: 'JSON数据验证通过' };
      }

      // 开始事务
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        let appCount = 0;
        let formCount = 0;
        let skippedApps = []; // 存储被跳过的应用名称及原因

        // 处理每个应用及其表单
        for (const appData of appDataList) {
          // 设置默认值
          const app = appData.app;
          app.role = app.role || 'system';
          app.status = app.status || 1;
          app.order = app.order || 100;
          app.public = app.public || false;
          app.isSystemReserved = app.isSystemReserved || false;
          app.isGPTs = app.isGPTs || 0;
          app.isFixedModel = app.isFixedModel || 0;
          app.appModel = app.appModel || '';
          app.gizmoID = app.gizmoID || '';

          // 检查分类是否存在
          const category = await this.appCatsEntity.findOne({ where: { id: app.catId } });
          if (!category) {
            this.logger.warn(`应用 ${app.name} 的分类ID ${app.catId} 不存在，跳过该应用`);
            skippedApps.push({ name: app.name, reason: `分类ID ${app.catId} 不存在` });
            continue; // 跳过此应用
          }

          // 检查应用名称是否已存在
          const existingApp = await this.appEntity.findOne({ where: { name: app.name } });
          if (existingApp) {
            this.logger.warn(`应用名称 ${app.name} 已存在，跳过该应用`);
            skippedApps.push({ name: app.name, reason: '应用名称已存在' });
            continue; // 跳过此应用
          }

          // 创建应用
          const createdApp = await queryRunner.manager.save(AppEntity, app);
          appCount++;

          // 创建关联的表单
          for (const formData of appData.forms) {
            formData.appId = createdApp.id;
            formData.status = formData.status || 1;
            formData.order = formData.order || 100;

            await queryRunner.manager.save(FormEntity, formData);
            formCount++;
          }
        }

        // 提交事务
        await queryRunner.commitTransaction();

        this.logger.log(`批量导入成功，导入应用数量: ${appCount}, 表单数量: ${formCount}, 跳过应用数量: ${skippedApps.length}`);

        // 构建返回消息
        let message = `成功导入 ${appCount} 个应用和 ${formCount} 个表单。`;
        if (skippedApps.length > 0) {
          message += `\n跳过 ${skippedApps.length} 个应用：`;
          skippedApps.forEach(app => {
            message += `\n- ${app.name}：${app.reason}`;
          });
        }

        return {
          code: 200,
          message: '批量导入应用成功',
          data: {
            appCount,
            formCount,
            skippedApps,
            skippedCount: skippedApps.length
          }
        };
      } catch (error) {
        // 回滚事务
        await queryRunner.rollbackTransaction();
        this.logger.error(`批量导入应用失败: ${error.message}`, error.stack);
        throw new HttpException(`批量导入应用失败: ${error.message}`, HttpStatus.BAD_REQUEST);
      } finally {
        // 释放查询运行器
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`批量导入应用失败: ${error.message}`, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(`批量导入应用失败: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
