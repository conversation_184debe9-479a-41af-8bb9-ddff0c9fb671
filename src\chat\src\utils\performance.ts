/**
 * 性能监控工具
 * 用于监控组件渲染、API调用等性能指标
 */

// 存储性能指标
interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  type: 'render' | 'api' | 'operation';
  metadata?: Record<string, any>;
}

// 性能指标存储
const metrics: PerformanceMetric[] = [];

// 最大存储的指标数量
const MAX_METRICS = 100;

/**
 * 开始计时
 * @param name 操作名称
 * @param type 操作类型
 * @param metadata 附加信息
 * @returns 计时器ID
 */
export function startMeasure(
  name: string,
  type: 'render' | 'api' | 'operation' = 'operation',
  metadata?: Record<string, any>
): string {
  const id = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const startTime = performance.now();
  
  // 存储开始信息
  (window as any).__performanceTimers = (window as any).__performanceTimers || {};
  (window as any).__performanceTimers[id] = {
    name,
    startTime,
    type,
    metadata
  };
  
  return id;
}

/**
 * 结束计时并记录指标
 * @param id 计时器ID
 * @returns 持续时间(ms)
 */
export function endMeasure(id: string): number | null {
  const timers = (window as any).__performanceTimers;
  if (!timers || !timers[id]) {
    console.warn(`Performance timer with id ${id} not found`);
    return null;
  }
  
  const endTime = performance.now();
  const { name, startTime, type, metadata } = timers[id];
  const duration = endTime - startTime;
  
  // 记录指标
  const metric: PerformanceMetric = {
    name,
    startTime,
    endTime,
    duration,
    type,
    metadata
  };
  
  // 添加到指标列表
  metrics.push(metric);
  
  // 限制存储的指标数量
  if (metrics.length > MAX_METRICS) {
    metrics.shift();
  }
  
  // 清理计时器
  delete timers[id];
  
  // 在开发环境下输出性能信息
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
  }
  
  return duration;
}

/**
 * 获取所有记录的性能指标
 */
export function getMetrics(): PerformanceMetric[] {
  return [...metrics];
}

/**
 * 清除所有性能指标
 */
export function clearMetrics(): void {
  metrics.length = 0;
}

/**
 * 性能监控装饰器
 * 用于监控方法执行时间
 * @param type 操作类型
 * @param metadata 附加信息
 */
export function measure(type: 'render' | 'api' | 'operation' = 'operation', metadata?: Record<string, any>) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function(...args: any[]) {
      const id = startMeasure(propertyKey, type, {
        ...metadata,
        args: args.map(arg => typeof arg === 'object' ? '(object)' : arg)
      });
      
      try {
        const result = originalMethod.apply(this, args);
        
        // 处理Promise返回值
        if (result instanceof Promise) {
          return result.finally(() => {
            endMeasure(id);
          });
        }
        
        endMeasure(id);
        return result;
      } catch (error) {
        endMeasure(id);
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * Vue组件性能监控指令
 * 用于监控组件渲染时间
 */
export const vPerformance = {
  mounted(el: HTMLElement, binding: any) {
    const componentName = binding.value || el.tagName.toLowerCase();
    el.__performanceId = startMeasure(`render:${componentName}`, 'render');
  },
  
  updated(el: HTMLElement) {
    if (el.__performanceId) {
      endMeasure(el.__performanceId);
      el.__performanceId = startMeasure(`update:${el.__componentName || el.tagName.toLowerCase()}`, 'render');
    }
  },
  
  unmounted(el: HTMLElement) {
    if (el.__performanceId) {
      endMeasure(el.__performanceId);
      delete el.__performanceId;
    }
  }
};

// 导出默认对象
export default {
  startMeasure,
  endMeasure,
  getMetrics,
  clearMetrics,
  measure,
  vPerformance
};
