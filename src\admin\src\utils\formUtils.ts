/**
 * 表单工具函数
 * 提供表单字段处理、变量提取等功能
 */
import { ElMessage } from 'element-plus';
import formFieldTypes from '@/constants/formFieldTypes';

/**
 * 从预设文本中提取变量
 * @param preset 预设文本
 * @returns 提取的变量集合
 */
export function extractVariablesFromPreset(preset: string): Set<string> {
  if (!preset) {
    return new Set();
  }

  const regex = /\$\{([^\}]+)\}/g;
  const matches = preset.matchAll(regex);
  const variables = new Set<string>();

  for (const match of matches) {
    if (match[1] && match[1].trim()) {
      variables.add(match[1].trim());
    }
  }

  return variables;
}

/**
 * 根据变量名猜测适合的字段类型
 * @param variableName 变量名
 * @returns 猜测的字段类型
 */
export function guessFieldTypeByName(variableName: string): string {
  const lowerName = variableName.toLowerCase();
  
  // 多行文本类型的关键词
  if (
    lowerName.includes('description') ||
    lowerName.includes('content') ||
    lowerName.includes('detail') ||
    lowerName.includes('描述') ||
    lowerName.includes('内容') ||
    lowerName.includes('详情') ||
    lowerName.includes('requirements') ||
    lowerName.includes('需求')
  ) {
    return 'textarea';
  }
  
  // 数字类型的关键词
  if (
    lowerName.includes('number') ||
    lowerName.includes('count') ||
    lowerName.includes('amount') ||
    lowerName.includes('price') ||
    lowerName.includes('数量') ||
    lowerName.includes('价格') ||
    lowerName.includes('金额')
  ) {
    return 'number';
  }
  
  // 日期类型的关键词
  if (
    lowerName.includes('date') ||
    lowerName.includes('time') ||
    lowerName.includes('日期') ||
    lowerName.includes('时间')
  ) {
    if (lowerName.includes('time') && !lowerName.includes('date')) {
      return 'time';
    }
    return 'date';
  }
  
  // 选择类型的关键词
  if (
    lowerName.includes('select') ||
    lowerName.includes('option') ||
    lowerName.includes('choice') ||
    lowerName.includes('选择') ||
    lowerName.includes('选项')
  ) {
    return 'select';
  }
  
  // 默认返回文本输入框
  return 'input';
}

/**
 * 为变量创建表单字段
 * @param variableName 变量名
 * @param fieldType 字段类型
 * @returns 创建的表单字段对象
 */
export function createFieldForVariable(variableName: string, fieldType: string = 'input'): any {
  // 查找对应的字段类型模板
  const fieldTypeObj = formFieldTypes.find(type => type.type === fieldType);
  if (!fieldTypeObj) {
    return {
      type: 'input',
      label: variableName,
      name: variableName,
      required: true,
      placeholder: `请输入${variableName}`
    };
  }
  
  // 基于模板创建新字段
  const field = { ...fieldTypeObj.template };
  field.name = variableName;
  field.label = variableName;
  field.required = true;
  field.placeholder = `请输入${variableName}`;
  
  return field;
}

/**
 * 从预设文本中提取变量并创建表单字段
 * @param preset 预设文本
 * @param existingFields 已存在的字段
 * @returns 创建的字段数组和添加的字段数量
 */
export function createFieldsFromPreset(preset: string, existingFields: any[] = []): { fields: any[], addedCount: number } {
  if (!preset) {
    return { fields: existingFields, addedCount: 0 };
  }
  
  // 提取变量
  const variables = extractVariablesFromPreset(preset);
  if (variables.size === 0) {
    return { fields: existingFields, addedCount: 0 };
  }
  
  // 检查现有字段中的变量名
  const existingVariables = new Set(existingFields.map(field => field.name));
  let addedCount = 0;
  const fields = [...existingFields];
  
  // 为每个新变量创建表单字段
  variables.forEach(variable => {
    if (!existingVariables.has(variable)) {
      // 根据变量名猜测字段类型
      const fieldType = guessFieldTypeByName(variable);
      const newField = createFieldForVariable(variable, fieldType);
      fields.push(newField);
      addedCount++;
    }
  });
  
  return { fields, addedCount };
}

/**
 * 生成唯一的字段名称
 * @param baseName 基础名称
 * @param existingFields 已存在的字段
 * @returns 唯一的字段名称
 */
export function generateUniqueFieldName(baseName: string, existingFields: any[]): string {
  let fieldName = baseName;
  let counter = 1;
  
  // 检查是否已存在相同名称的字段，如果存在则添加数字后缀
  while (existingFields.some(field => field.name === fieldName)) {
    fieldName = `${baseName}${counter}`;
    counter++;
  }
  
  return fieldName;
}

/**
 * 验证表单字段JSON格式
 * @param fieldsJson 表单字段JSON字符串
 * @returns 是否有效
 */
export function validateFormFieldsJson(fieldsJson: string): boolean {
  try {
    const fields = JSON.parse(fieldsJson);
    if (!Array.isArray(fields)) {
      return false;
    }
    
    // 验证每个字段是否有必要的属性
    for (const field of fields) {
      if (!field.type || !field.name || !field.label) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 格式化表单字段JSON
 * @param fieldsJson 表单字段JSON字符串
 * @returns 格式化后的JSON字符串
 */
export function formatFormFieldsJson(fieldsJson: string): string {
  try {
    const fields = JSON.parse(fieldsJson);
    return JSON.stringify(fields, null, 2);
  } catch (error) {
    return fieldsJson;
  }
}

/**
 * 在预设文本中插入变量
 * @param preset 当前预设文本
 * @param variableName 变量名
 * @param position 插入位置，如果未提供则在末尾添加
 * @returns 插入变量后的预设文本
 */
export function insertVariableToPreset(preset: string, variableName: string, position?: { start: number, end: number }): string {
  if (!variableName) return preset;
  
  const variableText = `\${${variableName}}`;
  
  if (position) {
    // 在指定位置插入变量
    return preset.substring(0, position.start) + variableText + preset.substring(position.end);
  } else {
    // 在末尾添加变量
    return preset + variableText;
  }
}

/**
 * 添加表单字段
 * @param fields 当前字段数组
 * @param fieldType 要添加的字段类型
 * @returns 添加字段后的数组
 */
export function addFormField(fields: any[], fieldType: string = 'input'): any[] {
  try {
    // 查找对应的字段类型模板
    const fieldTypeObj = formFieldTypes.find(type => type.type === fieldType);
    if (!fieldTypeObj) {
      ElMessage.error('未知的字段类型');
      return fields;
    }
    
    // 创建基本字段结构
    const newField = { ...fieldTypeObj.template };
    newField.name = generateUniqueFieldName(fieldTypeObj.defaultName, fields);
    
    return [...fields, newField];
  } catch (error) {
    console.error('添加表单字段失败', error);
    ElMessage.error('添加表单字段失败');
    return fields;
  }
}

/**
 * 删除表单字段
 * @param fields 当前字段数组
 * @param index 要删除的字段索引
 * @returns 删除字段后的数组
 */
export function removeFormField(fields: any[], index: number): any[] {
  try {
    const newFields = [...fields];
    newFields.splice(index, 1);
    return newFields;
  } catch (error) {
    console.error('删除表单字段失败', error);
    ElMessage.error('删除表单字段失败');
    return fields;
  }
}
