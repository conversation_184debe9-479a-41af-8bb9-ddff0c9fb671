import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsObject } from 'class-validator';

export class CreatePageDto {
  @ApiProperty({ description: '所属绘本ID' })
  @IsNotEmpty({ message: '绘本ID不能为空' })
  @IsNumber()
  storybookId: number;

  @ApiProperty({ description: '页码' })
  @IsNotEmpty({ message: '页码不能为空' })
  @IsNumber()
  pageNumber: number;

  @ApiProperty({ description: '页面文本内容', required: false })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiProperty({ description: '页面图片URL', required: false })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiProperty({ description: '布局类型', required: false, default: 'vertical' })
  @IsOptional()
  @IsString()
  layout?: string;

  @ApiProperty({ description: '文本样式配置', required: false })
  @IsOptional()
  @IsObject()
  textStyle?: object;

  @ApiProperty({ description: '场景元素', required: false })
  @IsOptional()
  @IsObject()
  sceneElements?: object;

  @ApiProperty({ description: '画面描述', required: false })
  @IsOptional()
  @IsString()
  imageDescription?: string;
}
