<script setup lang="ts">
import { t } from '@/locales';
import { useChatStore } from '@/store';
import { debounce } from '@/utils/functions/debounce';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { CheckIcon, EllipsisHorizontalIcon } from '@heroicons/vue/24/outline';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

interface Props {
  dataSources?: Chat.ChatState['groupList'];
  title?: string;
}
interface Emit {
  (ev: 'update', group: Chat.History, isEdit: boolean): void;
  (ev: 'delete', group: Chat.History): void;
  (ev: 'sticky', group: Chat.History): void;
  (ev: 'select', group: Chat.History): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const dataSources = props.dataSources;

const chatStore = useChatStore();

async function handleSelect(group: Chat.History) {
  emit('select', group);
}

function handleEdit(group: Chat.History, isEdit: boolean, event?: MouseEvent) {
  event?.stopPropagation();
  group.isEdit = isEdit;
}

async function handleSticky(group: Chat.History, event?: MouseEvent) {
  event?.stopPropagation();
  await chatStore.updateGroupInfo({
    isSticky: !group.isSticky,
    groupId: group.uuid,
  });
}

/* 删除对话组 */
async function handleDelete(
  params: Chat.History,
  event?: MouseEvent | TouchEvent
) {
  event?.stopPropagation();
  emit('delete', params);
}

const handleDeleteDebounce = debounce(handleDelete, 600);

/* 修改对话组title */
async function updateGroupTitle(params: Chat.History) {
  const { uuid, title } = params;
  params.isEdit = false;
  await chatStore.updateGroupInfo({ groupId: uuid, title });
}

/* 修改对话组信息 */
async function handleEnter(params: Chat.History, event: KeyboardEvent) {
  event?.stopPropagation();

  if (event.key === 'Enter') updateGroupTitle(params);
}

/* 判断是不是当前选中 */
function isActive(uuid: number) {
  return chatStore.active === uuid;
}
</script>

<template>
  <!-- 豆包风格的分组标题 -->
  <p class="mt-4 mb-2 text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3">
    {{ props.title }} <span class="ml-1">({{ dataSources?.length }})</span>
  </p>

  <!-- 对话列表项 -->
  <div v-for="item of dataSources" :key="`${item.uuid}`" class="mb-1.5">
    <div
      class="relative flex items-center gap-2 px-3 py-2 break-all rounded-md cursor-pointer hover:bg-gray-50 group dark:hover:bg-gray-700 font-medium text-sm transition-all duration-200 border-l-2"
      :class="
        isActive(item.uuid)
          ? [
              'bg-gray-50',
              'text-primary-600',
              'dark:bg-gray-700',
              'dark:text-white',
              'border-l-primary-500',
            ]
          : ['text-gray-700', 'dark:bg-gray-800', 'dark:text-gray-400', 'border-l-transparent']
      "
      @click="handleSelect(item)"
    >
      <!-- 对话图标 -->
      <div class="w-7 h-7 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center shadow-sm border border-gray-200 dark:border-gray-500">
        <span class="text-gray-500 dark:text-gray-400 text-sm font-medium">
          {{ item.title.charAt(0) }}
        </span>
      </div>

      <!-- 对话标题 -->
      <div class="flex items-center w-full">
        <input
          v-if="item.isEdit"
          v-model="item.title"
          v-focus
          type="text"
          class="bg-transparent border border-gray-200 dark:border-gray-400 px-2 py-1 shadow-none flex-1 truncate rounded"
          @keypress="handleEnter(item, $event)"
        />
        <span v-else class="flex-1 truncate max-w-[180px] font-medium">{{ item.title }}</span>
        <CheckIcon
          v-if="item.isEdit"
          class="h-5 w-5 ml-2 text-primary-500 dark:text-primary-400"
          aria-hidden="true"
          @click="updateGroupTitle(item)"
        />
      </div>

      <!-- 操作菜单 -->
      <div v-if="isActive(item.uuid)" class="absolute z-10 right-2">
        <template v-if="!item.isEdit">
          <Menu as="div" class="relative inline-block text-left">
            <MenuButton class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
              <EllipsisHorizontalIcon class="h-5 w-5 text-gray-500 dark:text-gray-400" aria-hidden="true" />
            </MenuButton>
            <transition
              enter-active-class="transition ease-out duration-100"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <MenuItems
                class="absolute right-0 z-10 mt-2 w-36 text-gray-700 dark:text-gray-400 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-gray-200 dark:ring-gray-700 focus:outline-none dark:bg-gray-800 transition-all duration-300"
              >
                <div class="py-1">
                  <MenuItem v-slot="{ active }" as="template">
                    <button
                      :class="[
                        active
                          ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                          : 'text-gray-700 dark:text-gray-300',
                        'flex w-full px-4 py-2 text-left text-sm items-center'
                      ]"
                      @click="handleEdit(item, true, $event)"
                    >
                      <SvgIcon name="ri:edit-line" class="text-base mr-2" />
                      {{ t('chat.rename') }}
                    </button>
                  </MenuItem>
                  <MenuItem v-slot="{ active }" as="template">
                    <button
                      :class="[
                        active
                          ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                          : 'text-gray-700 dark:text-gray-300',
                        'flex w-full px-4 py-2 text-left text-sm items-center'
                      ]"
                      @click="handleSticky(item, $event)"
                    >
                      <SvgIcon :name="item.isSticky ? 'ri:star-line' : 'ri:star-fill'" class="text-base mr-2" />
                      {{
                        item.isSticky
                          ? t('chat.unfavorite')
                          : t('chat.favoriteConversations')
                      }}
                    </button>
                  </MenuItem>
                  <MenuItem v-slot="{ active }" as="template">
                    <button
                      :class="[
                        active
                          ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                          : 'text-gray-700 dark:text-gray-300',
                        'flex w-full px-4 py-2 text-left text-sm items-center text-red-500 dark:text-red-400'
                      ]"
                      @click="handleDeleteDebounce(item, $event)"
                    >
                      <SvgIcon name="ri:delete-bin-line" class="text-base mr-2" />
                      {{ t('chat.deleteConversation') }}
                    </button>
                  </MenuItem>
                </div>
              </MenuItems>
            </transition>
          </Menu>
        </template>
      </div>
    </div>
  </div>
</template>
