<route lang="yaml">
meta:
  title: 应用管理
</route>

<script lang="ts" setup>
import ApiApp from '@/api/modules/app';
import ApiForm from '@/api/modules/form';
import ApiAi from '@/api/modules/ai';
import { sqlSystemPrompt } from './sql-system-prompt';
import { utcToShanghaiTime } from '@/utils/utcformatTime';
import { Plus, Refresh, Document, ArrowDown, Delete, View, Search, MagicStick } from '@element-plus/icons-vue';
import type { FormInstance, FormRules, UploadProps } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive } from 'vue';

import { MODEL_LIST, QUESTION_STATUS_MAP } from '@/constants/index';
import formFieldTypes, { fieldTypeLabels, fieldTypeTagTypes, defaultDateTimeFormats } from '@/constants/formFieldTypes';
import {
  insertVariableToPreset,
  validateFormFieldsJson,
  formatFormFieldsJson,
  addFormField,
  removeFormField
} from '@/utils/formUtils';

// 导入组件
import FormEditor from '@/components/FormEditor/index.vue';
import FormPreview from '@/components/FormPreview/index.vue';
import FormPreviewDrawer from '@/components/FormPreviewDrawer/index.vue';
import AppCard from '@/components/AppCard/index.vue';
import AppList from '@/components/AppList/index.vue';
import AppFilter from '@/components/AppFilter/index.vue';
import AiPromptGenerator from '@/components/AiPromptGenerator/index.vue';

import axios from 'axios';

const formRef = ref<FormInstance>();
const total = ref(0);
const visible = ref(false);
const loading = ref(false);

// 表单相关
const formFields = ref<any[]>([]);
const showFormEditor = ref(false);

// 表单预览相关
const showFormPreview = ref(false);
const showFormPreviewDrawer = ref(false);

// AI生成相关
const showAiGenerator = ref(false);

// 批量导入相关
const showBatchImportDialog = ref(false);
const batchImportLoading = ref(false);
const batchImportMode = ref('manual'); // 添加导入模式：'manual' 或 'ai'
const batchImportForm = reactive({
  jsonContent: '',
  validateOnly: false
});
const batchImportResult = reactive({
  show: false,
  title: '',
  message: '',
  type: 'info' // success, warning, info, error
});

// 可用分类列表
interface CategoryItem {
  id: number;
  name: string;
  status?: number;
}
const availableCategories = ref<CategoryItem[]>([]);
const loadingCategories = ref(false);

// AI生成SQL相关
const showAiGenerateSqlDialog = ref(false);
const loadingModels = ref(false);
interface ModelOption {
  label: string;
  value: string;
  modelAvatar?: string;
}
const modelOptions = ref<ModelOption[]>([]);
const aiGenerateSqlForm = reactive({
  model: '',
  appDescription: '',
  appCount: 1,
  catId: '',
  otherRequirements: '',
  temperature: 0.3 // 默认温度值
});
const aiGenerateSqlResult = reactive({
  show: false,
  loading: false
});

// 视图模式
const viewMode = ref('card'); // 'card' 或 'table'

const formInline = reactive({
  catId: '',
  name: '',
  status: '',
  role: '',
  page: 1,
  size: 10,
  isGPTs: 0,
  gizmoID: '',
  isFixedModel: 0,
  appModel: '',
});

// 上传URL
const uploadUrl = ref(
  `${import.meta.env.VITE_APP_API_BASEURL}/upload/file?dir=${encodeURIComponent(
    'system/app'
  )}`
);

const formPackageRef = ref<FormInstance>();
const activeAppCatId = ref(0);
const isUserApp = ref(false);
const userAppStatus = ref(0);
const formPackage = reactive({
  catId: '',
  name: '',
  preset: '',
  des: '',
  coverImg: '',
  demoData: '',
  order: 100,
  status: 0,
  isGPTs: 0,
  gizmoID: '',
  isFixedModel: 0,
  appModel: '',
  // 表单相关字段
  hasForm: false,
  formName: '',
  formDescription: '',
  formFields: '[]',
});

const rules = reactive<FormRules>({
  catId: [{ required: true, message: '请选择App分类', trigger: 'change' }],
  name: [{ required: true, message: '请填写App名称', trigger: 'blur' }],
  preset: [{ required: false, message: '请填写App预设信息', trigger: 'blur' }],
  des: [{ required: true, message: '请填写App描述', trigger: 'blur' }],
  coverImg: [
    { required: false, message: '请填写App封面图片地址', trigger: 'blur' },
  ],
  demoData: [
    { required: false, message: '请填写App演示数据', trigger: 'blur' },
  ],
  isGPTs: [{ required: true, message: '是否GPTs', trigger: 'blur' }],
  gizmoID: [{ required: false, message: 'GPTs 的ID', trigger: 'blur' }],
  order: [{ required: false, message: '请填写排序ID', trigger: 'blur' }],
  status: [{ required: true, message: '请选择App状态', trigger: 'change' }],
  isFixedModel: [
    { required: true, message: '请选择App是否固定模型', trigger: 'blur' },
  ],
  appModel: [
    { required: false, message: '请选择App使用的模型', trigger: 'change' },
  ],
  // 表单相关验证规则
  formName: [
    { required: false, message: '请填写表单名称', trigger: 'blur' },
  ],
  formFields: [
    { required: false, message: '请填写表单字段定义', trigger: 'blur' },
  ],
});

const tableData = ref([]);

interface CatItem {
  id: number;
  name: string;
}
const catList: Ref<CatItem[]> = ref([]);

const dialogTitle = computed(() => {
  return activeAppCatId.value ? '更新应用' : '新增应用';
});

const dialogButton = computed(() => {
  return activeAppCatId.value ? '确认更新' : '确认新增';
});

/**
 * 查询应用列表
 */
async function queryAppList() {
  try {
    loading.value = true;
    const res = await ApiApp.queryApp(formInline);
    const { rows, count } = res.data;
    total.value = count;
    tableData.value = rows.sort(
      (a: { order: number }, b: { order: number }) => b.order - a.order
    );
  } catch (error) {
    console.error('查询应用列表失败', error);
    ElMessage.error('查询应用列表失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 查询应用分类列表
 */
async function queryCatList() {
  try {
    loading.value = true;
    const res = await ApiApp.queryCats({ size: 100 });
    const { rows } = res.data;
    catList.value = rows;

    // 同时更新可用分类列表
    availableCategories.value = rows.filter((cat: CategoryItem) => cat.status === 1);
  } catch (error) {
    console.error('查询应用分类失败', error);
    ElMessage.error('查询应用分类失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 获取可用的应用分类
 */
async function fetchAvailableCategories() {
  try {
    // 显示加载状态
    loadingCategories.value = true;

    // 获取分类列表
    const res = await ApiApp.queryCats({ status: 1, size: 100 });

    if (res && res.data && res.data.rows) {
      // 保存可用分类
      availableCategories.value = res.data.rows;
      return res.data.rows;
    }
  } catch (error) {
    console.error('获取分类列表失败', error);
    ElMessage.error('获取分类列表失败');
  } finally {
    loadingCategories.value = false;
  }

  return [];
}

async function handleUpdatePackage(row: any) {
  try {
    // 显示加载状态
    loading.value = true;

    // 1. 设置应用基本信息
    activeAppCatId.value = row.id;
    isUserApp.value = row.role === 'user';
    userAppStatus.value = row.status;
    const {
      name,
      status,
      des,
      order,
      coverImg,
      catId,
      preset,
      demoData,
      isGPTs,
      gizmoID,
      isFixedModel,
      appModel,
    } = row;

    // 2. 重置表单相关字段
    formPackage.hasForm = false;
    formPackage.formName = '';
    formPackage.formDescription = '';
    formPackage.formFields = '[]';
    formFields.value = [];

    // 3. 使用服务查询应用关联的表单
    const forms = await AppService.getFormsByAppId(row.id);
    if (forms && forms.length > 0) {
      const form = forms[0]; // 取第一个表单
      formPackage.hasForm = true;
      formPackage.formName = form.name;
      formPackage.formDescription = form.description || '';
      formPackage.formFields = form.fields;
      try {
        formFields.value = JSON.parse(form.fields);
      } catch (error) {
        console.error('解析表单字段失败', error);
        formFields.value = [];
      }
    }

    // 4. 设置应用数据
    nextTick(() => {
      Object.assign(formPackage, {
        name,
        status,
        des,
        order,
        coverImg,
        catId,
        preset,
        demoData,
        isGPTs,
        gizmoID,
        isFixedModel,
        appModel,
      });
    });

    // 5. 显示对话框
    visible.value = true;
  } catch (error) {
    console.error('加载应用数据失败', error);
    ElMessage.error('加载应用数据失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 关闭对话框
 * @param formEl 表单实例
 */
function handlerCloseDialog(formEl: FormInstance | undefined) {
  // 重置应用ID
  activeAppCatId.value = 0;

  // 重置表单
  formEl?.resetFields();

  // 重置表单编辑器状态
  showFormEditor.value = false;
  showFormPreview.value = false;
}

async function handleDeletePackage(row: any) {
  try {
    // 显示加载状态
    loading.value = true;

    // 使用服务删除应用
    await AppService.deleteApp(row.id);

    // 刷新应用列表
    queryAppList();
  } catch (error) {
    console.error('删除应用失败', error);
  } finally {
    loading.value = false;
  }
}

/**
 * 处理过滤器变化
 * @param filter 过滤条件
 */
function handleFilterChange(filter: any) {
  // 更新过滤条件
  Object.assign(formInline, filter);

  // 查询应用列表
  queryAppList();
}

/**
 * 重置搜索表单并查询
 * @param formEl 表单实例
 */
function handlerReset(formEl: FormInstance | undefined) {
  // 重置表单
  formEl?.resetFields();

  // 重置过滤条件
  formInline.catId = '';
  formInline.name = '';
  formInline.status = '';
  formInline.page = 1;

  // 重新查询应用列表
  queryAppList();
}

// 添加表单字段
function handleAddFormField(fieldType = 'input') {
  try {
    const fields = addFormField(formFields.value, fieldType);
    formFields.value = fields;
    formPackage.formFields = JSON.stringify(fields, null, 2);
    ElMessage.success('添加字段成功');
  } catch (error) {
    console.error('添加表单字段失败', error);
    ElMessage.error('添加表单字段失败');
  }
}

// 删除表单字段
function handleRemoveFormField(index: number) {
  try {
    const fields = removeFormField(formFields.value, index);
    formFields.value = fields;
    formPackage.formFields = JSON.stringify(fields, null, 2);
    ElMessage.success('删除字段成功');
  } catch (error) {
    console.error('删除表单字段失败', error);
    ElMessage.error('删除表单字段失败');
  }
}

// 从预设中提取变量并自动创建表单字段
function handleExtractVariablesFromPreset() {
  // 使用表单服务提取变量并创建字段
  const result = FormService.extractVariablesAndCreateFields(
    formPackage.preset,
    formFields.value,
    formPackage.formName
  );

  // 更新表单数据
  formFields.value = result.fields;
  formPackage.formFields = JSON.stringify(result.fields, null, 2);
  formPackage.formName = result.formName;
  formPackage.hasForm = result.hasForm;
}

// 切换表单编辑器显示状态
function toggleFormEditor() {
  showFormEditor.value = !showFormEditor.value;
  showFormPreview.value = false;

  if (showFormEditor.value) {
    try {
      formFields.value = formPackage.formFields ? JSON.parse(formPackage.formFields) : [];
    } catch (error) {
      console.error('解析表单字段失败', error);
      formFields.value = [];
    }
  }
}

// 切换表单预览显示状态
function toggleFormPreview() {
  showFormPreview.value = !showFormPreview.value;
  showFormEditor.value = false;
}

// 处理表单编辑器内容变化
function handleFormEditorChange(newValue: string) {
  formPackage.formFields = newValue;
  try {
    formFields.value = JSON.parse(newValue);
  } catch (error) {
    console.error('解析表单字段失败', error);
  }
}

// 处理表单预览提交
function handleFormPreviewSubmit(formData: Record<string, any>) {
  ElMessage.success('表单预览提交成功，数据: ' + JSON.stringify(formData));
  showFormPreview.value = false;
}

// 编辑表单字段
function editField(index: number) {
  try {
    const field = formFields.value[index];
    // 这里可以实现编辑字段的逻辑，例如打开一个编辑对话框
    // 暂时简单实现为切换到JSON编辑器
    showFormEditor.value = true;
    ElMessage.info('请在JSON编辑器中修改字段');
  } catch (error) {
    console.error('编辑字段失败', error);
    ElMessage.error('编辑字段失败');
  }
}

// 打开AI生成器
function openAiGenerator() {
  showAiGenerator.value = true;
}

// 处理AI生成结果
function handleAiGenerateSuccess(result: any) {
  try {
    // 应用AI生成的提示词
    if (result.prompt) {
      formPackage.preset = result.prompt;
    }

    // 如果有表单字段，启用表单并应用表单字段
    if (result.formFields && result.formFields.length > 0) {
      formPackage.hasForm = true;

      // 如果表单名称为空，自动设置一个默认名称
      if (!formPackage.formName) {
        formPackage.formName = '应用参数表单';
      }

      // 更新表单字段
      formFields.value = result.formFields;
      formPackage.formFields = JSON.stringify(result.formFields, null, 2);
    }

    ElMessage.success('AI生成内容已应用');
  } catch (error) {
    console.error('AI生成内容应用失败', error);
    ElMessage.error('AI生成内容应用失败');
  }
}

/**
 * 从预设提示词中提取变量
 * @param {string} preset - 预设提示词
 * @returns {string[]} - 提取的变量数组
 */
function extractVariablesFromPreset(preset: string): string[] {
  // 如果 preset 为空，返回空数组
  if (!preset) {
    return [];
  }

  const variables: string[] = [];
  const regex = /\${([^}]+)}/g;
  let match;

  while ((match = regex.exec(preset)) !== null) {
    variables.push(match[1]);
  }

  return variables;
}

/**
 * 验证JSON数据
 * @param {any} jsonData - 要验证的JSON数据
 * @returns {boolean} - 验证结果，true表示验证通过，false表示验证失败
 */
function validateJsonData(jsonData: any): boolean {
  try {
    // 检查分类列表是否为空
    if (availableCategories.value.length === 0) {
      throw new Error('分类列表为空，无法验证分类ID，请先获取分类列表');
    }

    // 1. 检查是否为数组
    if (!Array.isArray(jsonData)) {
      throw new Error('JSON数据必须是数组格式');
    }

    // 2. 检查每个应用
    for (let i = 0; i < jsonData.length; i++) {
      const item = jsonData[i];

      // 2.1 检查app字段
      if (!item.app) {
        throw new Error(`第${i+1}项数据缺少app字段`);
      }

      // 2.2 检查必填字段
      const requiredAppFields = ['name', 'catId', 'des', 'preset', 'coverImg'];
      for (const field of requiredAppFields) {
        if (!item.app[field]) {
          throw new Error(`第${i+1}项应用数据缺少必填字段: ${field}`);
        }
      }

      // 2.3 检查分类ID是否存在
      const catId = item.app.catId;
      const categoryExists = availableCategories.value.some(cat => cat.id === catId);
      if (!categoryExists) {
        throw new Error(`第${i+1}项应用的分类ID ${catId} 不存在，请使用有效的分类ID`);
      }

      // 2.4 检查coverImg格式
      if (typeof item.app.coverImg !== 'string' || !item.app.coverImg.startsWith('emoji:')) {
        throw new Error(`第${i+1}项应用的coverImg必须是字符串并使用emoji:前缀`);
      }

      // 2.5 检查forms字段
      if (!item.forms || !Array.isArray(item.forms) || item.forms.length === 0) {
        throw new Error(`第${i+1}项数据缺少forms字段或forms不是数组或为空`);
      }

      // 2.6 检查表单字段
      for (let j = 0; j < item.forms.length; j++) {
        const form = item.forms[j];
        const requiredFormFields = ['name', 'description', 'fields'];

        for (const field of requiredFormFields) {
          if (!form[field]) {
            throw new Error(`第${i+1}项应用的第${j+1}个表单缺少必填字段: ${field}`);
          }
        }

        // 2.7 检查fields格式
        try {
          const fieldsObj = JSON.parse(form.fields);
          if (!Array.isArray(fieldsObj)) {
            throw new Error('表单字段必须是数组格式');
          }

          // 2.8 检查fields中的key是否与preset中的变量匹配
          const variables = extractVariablesFromPreset(item.app.preset);

          // 如果预设提示词中没有变量，则跳过变量检查
          if (variables.length === 0) {
            continue;
          }

          const keys = fieldsObj.map(f => f.key);

          for (const variable of variables) {
            if (!keys.includes(variable)) {
              throw new Error(`第${i+1}项应用的预设提示词中的变量 ${variable} 在表单字段中未定义`);
            }
          }

          for (const key of keys) {
            if (!variables.includes(key)) {
              throw new Error(`第${i+1}项应用的表单字段中的key ${key} 在预设提示词中未使用`);
            }
          }
        } catch (error: any) {
          throw new Error(`第${i+1}项应用的第${j+1}个表单的fields字段格式错误: ${error.message}`);
        }
      }
    }

    return true;
  } catch (error: any) {
    ElMessage.error(error.message);
    return false;
  }
}

/**
 * 处理批量导入应用
 */
async function handleBatchImport() {
  try {
    // 重置结果显示
    batchImportResult.show = false;

    // 检查JSON数据是否为空
    if (!batchImportForm.jsonContent || batchImportForm.jsonContent.trim() === '') {
      ElMessage.error('请输入JSON数据');
      return;
    }

    // 解析JSON数据
    let jsonData;
    try {
      jsonData = JSON.parse(batchImportForm.jsonContent);
    } catch (error) {
      ElMessage.error('JSON格式错误，请检查数据格式');
      return;
    }

    // 确保已获取分类列表
    if (availableCategories.value.length === 0) {
      await fetchAvailableCategories();
    }

    // 验证JSON数据
    if (!validateJsonData(jsonData)) {
      return;
    }

    // 显示加载状态
    batchImportLoading.value = true;

    // 发送请求到后端
    const res = await ApiApp.batchImportApps({
      jsonContent: batchImportForm.jsonContent,
      validateOnly: batchImportForm.validateOnly
    });

    // 显示结果
    batchImportResult.show = true;

    // 判断响应是否成功
    const isSuccess = res && res.data && (res.data.code === 200 || res.status === 200);

    if (isSuccess) {
      batchImportResult.type = 'success';
      batchImportResult.title = batchImportForm.validateOnly ? 'JSON验证成功' : '应用导入成功';

      // 构建成功消息
      let message = '';
      if (batchImportForm.validateOnly) {
        message = 'JSON数据验证通过，可以安全导入。';
      } else {
        const responseData = res.data?.data || res.data;
        const appCount = responseData?.appCount || 0;
        const formCount = responseData?.formCount || 0;
        const skippedApps = responseData?.skippedApps || [];
        const skippedCount = responseData?.skippedCount || 0;

        message = `成功导入 ${appCount} 个应用和 ${formCount} 个表单。`;

        // 添加被跳过的应用信息
        if (skippedCount > 0) {
          message += `\n\n跳过了 ${skippedCount} 个应用：`;
          skippedApps.forEach((app: any) => {
            message += `\n- ${app.name}：${app.reason}`;
          });
        }

        // 如果成功导入，刷新应用列表
        queryAppList();
      }

      batchImportResult.message = message;
    } else {
      batchImportResult.type = 'error';
      batchImportResult.title = batchImportForm.validateOnly ? 'JSON验证失败' : '应用导入失败';

      // 获取错误消息
      const errorMessage = res?.data?.message || res?.data?.msg || (res as any)?.message || '操作失败，请检查JSON数据是否正确';
      batchImportResult.message = errorMessage;
    }
  } catch (error: any) {
    console.error('批量导入应用失败', error);

    // 显示错误信息
    batchImportResult.show = true;
    batchImportResult.type = 'error';
    batchImportResult.title = '操作失败';
    batchImportResult.message = error.message || '未知错误，请检查控制台日志';
  } finally {
    batchImportLoading.value = false;
  }
}

/**
 * 插入示例预设
 */
function insertExamplePreset() {
  // 使用转义的字符串，避免JavaScript尝试解析变量
  const examplePreset = "我是一个专业的\${role}，将帮助你创建一个名为\${projectName}的项目。\n\n项目类型：\${projectType}\n项目描述：\${projectDescription}\n\n我将根据你提供的信息，结合我的专业知识，为你提供最佳的解决方案。\n\n请提供以下详细信息：\n1. \${requirement1}\n2. \${requirement2}\n3. \${additionalInfo}\n";

  // 插入预设的函数
  const insertPreset = () => {
    formPackage.preset = examplePreset;
    ElMessage.success('示例预设已插入');

    // 自动启用表单
    formPackage.hasForm = true;

    // 自动提取变量并创建表单字段
    handleExtractVariablesFromPreset();
  };

  // 如果当前预设为空，直接插入
  if (!formPackage.preset || formPackage.preset.trim() === '') {
    insertPreset();
  } else {
    // 否则询问用户是否覆盖
    ElMessageBox.confirm('当前预设信息不为空，是否覆盖现有内容？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(insertPreset).catch(() => {
      // 用户取消操作
    });
  }
}

/**
 * 在预设文本中插入变量
 * @param {string} variableName - 要插入的变量名
 */
function handleInsertVariableToPreset(variableName: string): void {
  if (!variableName) return;

  const presetInput = document.querySelector('#preset-input') as HTMLTextAreaElement | null;
  let position: { start: number; end: number } | undefined;

  // 如果能获取到输入框元素
  if (presetInput && presetInput instanceof HTMLTextAreaElement) {
    const start = presetInput.selectionStart || 0;
    const end = presetInput.selectionEnd || 0;
    position = { start, end };
  }

  // 使用工具函数插入变量
  formPackage.preset = insertVariableToPreset(formPackage.preset, variableName, position);

  // 如果有光标位置，将光标定位到变量后面
  if (presetInput && position) {
    nextTick(() => {
      presetInput.focus();
      const newPosition = position!.start + `\${${variableName}}`.length;
      presetInput.setSelectionRange(newPosition, newPosition);
    });
  }

  ElMessage.success(`已插入变量: ${variableName}`);
}

/**
 * 插入表单字段模板
 * @param {number} typeIndex - 字段类型索引
 */
function handleInsertFormFieldTemplate(typeIndex: number): void {
  const fieldType = formFieldTypes[typeIndex];
  if (!fieldType) return;

  try {
    // 启用表单
    formPackage.hasForm = true;

    // 如果表单名称为空，自动设置一个默认名称
    if (!formPackage.formName) {
      formPackage.formName = '应用参数表单';
    }

    // 1. 添加表单字段
    const fields = addFormField(formFields.value, fieldType.type);
    const newField = fields[fields.length - 1]; // 获取新添加的字段

    // 更新表单字段
    formFields.value = fields;
    formPackage.formFields = JSON.stringify(fields, null, 2);

    // 2. 在预设文本中插入对应的变量
    handleInsertVariableToPreset(newField.name);

    ElMessage.success(`已添加${fieldType.label}字段，变量名: ${newField.name}`);

    // 切换到表单编辑器，方便用户编辑
    showFormEditor.value = true;

  } catch (error) {
    console.error('插入表单字段失败', error);
    ElMessage.error('插入表单字段失败');
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (
  response,
  uploadFile
) => {
  console.log('response: ', response.data);
  formPackage.coverImg = response.data;
};

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const allowedTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp'];

  if (!allowedTypes.includes(rawFile.type)) {
    ElMessage.error('当前系统仅支持 PNG、JPEG、GIF、和 WebP 格式的图片!');
    return false;
  } else if (rawFile.size / 1024 > 300) {
    ElMessage.error('当前限制文件最大不超过 300KB!');
    return false;
  }
};

async function reuploadAppAvatar() {
  if (formPackage.coverImg) {
    const file = await downloadFile(formPackage.coverImg);
    uploadFile(file, handleAvatarSuccess);
  }
}

function uploadFile(file: any, successHandler: any) {
  const form = new FormData();
  form.append('file', file);

  axios
    .post(uploadUrl.value, form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    .then((response) => {
      successHandler(response.data);
    })
    .catch((error) => {
      console.error('上传失败', error);
    });
}

async function downloadFile(url: string) {
  const response = await axios.get(url, { responseType: 'blob' });
  let fileName = 'downloaded_file';

  const contentDisposition = response.headers['content-disposition'];
  if (contentDisposition) {
    const matches = /filename="([^"]+)"/.exec(contentDisposition);
    if (matches != null && matches[1]) {
      fileName = matches[1];
    }
  } else {
    fileName = getFileNameFromUrl(url);
  }

  return new File([response.data], fileName, { type: response.data.type });
}

function getFileNameFromUrl(url: string | URL) {
  const parsedUrl = new URL(url);
  const pathname = parsedUrl.pathname;
  return pathname.substring(pathname.lastIndexOf('/') + 1);
}

import AppService from '@/services/AppService';
import FormService from '@/services/FormService';
import { validateAppData, validateAppFormData } from '@/utils/formValidator';

async function handlerSubmit(formEl: FormInstance | undefined) {
  formEl?.validate(async (valid) => {
    if (valid) {
      try {
        // 显示加载状态
        loading.value = true;

        // 1. 准备应用数据
        const appData = {
          catId: formPackage.catId,
          name: formPackage.name,
          preset: formPackage.preset,
          des: formPackage.des,
          coverImg: formPackage.coverImg,
          demoData: formPackage.demoData,
          order: formPackage.order,
          status: formPackage.status,
          isGPTs: formPackage.isGPTs,
          gizmoID: formPackage.gizmoID,
          isFixedModel: formPackage.isFixedModel,
          appModel: formPackage.appModel
        };

        // 验证应用数据
        if (!validateAppData(appData)) {
          loading.value = false;
          return;
        }

        // 2. 准备表单数据（如果启用了表单）
        let formData: any = undefined;
        if (formPackage.hasForm) {
          formData = {
            name: formPackage.formName,
            description: formPackage.formDescription,
            fields: formPackage.formFields,
            appId: 0, // 临时值，将在服务中被替换
            status: 1,
            order: 100
          };

          // 验证表单数据
          if (!validateAppFormData(formPackage)) {
            loading.value = false;
            return;
          }
        }

        // 3. 保存应用和表单
        if (activeAppCatId.value) {
          // 更新应用
          await AppService.updateApp(
            { ...appData, id: activeAppCatId.value },
            formData,
            isUserApp.value,
            userAppStatus.value
          );
        } else {
          // 创建应用
          await AppService.createApp(appData, formData);
        }

        // 4. 完成操作
        visible.value = false;
        queryAppList();
      } catch (error) {
        console.error('保存应用失败', error);
        ElMessage({ type: 'error', message: '保存失败！' });
      } finally {
        loading.value = false;
      }
    }
  });
}

/**
 * 获取模型列表
 */
async function fetchModelsList() {
  loadingModels.value = true;
  try {
    const res = await ApiAi.getModelsList();
    if (res.data) {
      const { modelMaps, modelTypeList } = res.data;

      // 处理模型列表，与聊天组件保持一致
      const flatModelArray = Object.values(modelMaps).flat() as any[];
      // 这里可以根据需要筛选特定类型的模型，例如只显示keyType为1的模型
      const filteredModelArray = flatModelArray.filter(
        (model) => model.keyType === 1
      );

      modelOptions.value = filteredModelArray.map((model) => ({
        label: model.modelName,
        value: model.model,
        modelAvatar: model.modelAvatar
      }));

      // 默认选择第一个模型
      if (modelOptions.value.length > 0 && !aiGenerateSqlForm.model) {
        aiGenerateSqlForm.model = modelOptions.value[0].value;
      }
    }
  } catch (error) {
    console.error('获取模型列表失败', error);
    ElMessage.error('获取模型列表失败，将使用默认模型');

    // 如果获取模型列表失败，使用默认模型
    modelOptions.value = [
      { label: 'GPT-3.5', value: 'gpt-3.5-turbo' },
      { label: 'GPT-4', value: 'gpt-4' },
      { label: 'Claude-3', value: 'claude-3-sonnet-20240229' }
    ];
    aiGenerateSqlForm.model = 'gpt-3.5-turbo';
  } finally {
    loadingModels.value = false;
  }
}

/**
 * 处理AI生成JSON
 */
async function handleGenerateSql() {
  try {
    // 显示加载状态
    aiGenerateSqlResult.show = true;
    aiGenerateSqlResult.loading = true;

    // 确保已获取分类列表
    if (availableCategories.value.length === 0) {
      await fetchAvailableCategories();
    }

    // 构建分类ID列表文本
    const categoriesText = availableCategories.value
      .map(cat => `${cat.id}: ${cat.name}`)
      .join('\n     * ');

    // 构建系统提示词
    const systemPrompt = `你是一个专业的JSON生成器，特别擅长生成用于创建应用和表单的JSON数据。请根据以下描述生成符合要求的JSON数据。

数据结构说明：
1. 数据必须是一个数组，每个元素包含一个应用及其表单
2. 每个元素必须包含两个字段：app和forms
3. app字段包含应用信息，必填字段有：
   - name: 应用名称（字符串），确保名称唯一且有意义
   - catId: 分类 ID（数字），必须使用以下实际存在的分类ID之一：
     * ${categoriesText}
   - des: 描述（字符串）
   - preset: 预设提示词（字符串），可以包含变量如\${variable}
   - coverImg: 图标（字符串），必须使用emoji:前缀表示emoji图标，如"emoji:📝"
   - order: 排序（数字），越大越靠前
   - status: 状态（数字），1=启用
   - demoData: 示例数据（字符串），多个示例用\n分隔
   - role: 角色（字符串），通常为'system'
   - isGPTs: 是否GPTs（数字），0=否
   - isFixedModel: 是否固定模型（数字），0=否
   - appModel: 使用模型（字符串），当isFixedModel=1时指定
   - gizmoID: GPTs ID（字符串），当isGPTs=1时指定
   - public: 是否公开（数字），必须设置为0
   - isSystemReserved: 是否系统保留（数字），必须设置为0

4. forms字段是一个数组，包含表单信息，每个表单必填字段有：
   - name: 表单名称（字符串）
   - description: 表单描述（字符串）
   - fields: 表单字段JSON（字符串），必须是一个JSON数组字符串，格式如下：
     "[{\"type\":\"input\",\"label\":\"字段名\",\"required\":true,\"placeholder\":\"请输入\",\"key\":\"variable\"}]"
   - order: 排序（数字）
   - status: 状态（数字），1=启用

注意事项：
1. fields中的key必须与preset中的变量对应
2. 每个应用至少要有一个表单
3. 所有字符串必须正确转义
4. 你必须严格按照示例格式返回JSON数据，不要添加任何额外的解释、说明或标记
5. 只返回有效的JSON数组，不要包含代码块标记或其他格式标记

示例：
[
  {
    "app": {
      "name": "课后辅导助手",
      "catId": 1,
      "des": "提供课后辅导服务",
      "preset": "请帮我解答关于\${subject}的问题",
      "coverImg": "emoji:📝",
      "order": 100,
      "status": 1,
      "demoData": "数学, 英语, 物理",
      "role": "system",
      "isGPTs": 0,
      "isFixedModel": 0,
      "appModel": "",
      "gizmoID": "",
      "public": 0,
      "isSystemReserved": 0
    },
    "forms": [
      {
        "name": "辅导表单",
        "description": "请填写需要辅导的科目",
        "fields": "[{\"type\":\"input\",\"label\":\"科目\",\"required\":true,\"placeholder\":\"请输入科目\",\"key\":\"subject\"}]",
        "order": 100,
        "status": 1
      }
    ]
  }
]`;

    // 构建用户提示词
    let userPrompt = `请生成${aiGenerateSqlForm.appCount}个应用的JSON数据，应用描述如下：${aiGenerateSqlForm.appDescription}`;

    // 添加分类信息（如果有）
    if (aiGenerateSqlForm.catId) {
      // 确保分类列表不为空
      if (availableCategories.value.length === 0) {
        await fetchAvailableCategories();
      }

      const category = availableCategories.value.find(cat => String(cat.id) === String(aiGenerateSqlForm.catId));
      if (category) {
        userPrompt += `\n应用分类：${category.name} (分类ID: ${category.id})`;
      } else {
        // 如果找不到分类，使用默认分类
        userPrompt += `\n应用分类：ID ${aiGenerateSqlForm.catId} (请确保该分类ID存在)`;
      }
    }

    // 添加其他要求（如果有）
    if (aiGenerateSqlForm.otherRequirements) {
      userPrompt += `\n其他要求：${aiGenerateSqlForm.otherRequirements}`;
    }

    // 调用AI生成器
    const response = await ApiAi.generateContent({
      prompt: userPrompt,
      systemMessage: systemPrompt,
      model: aiGenerateSqlForm.model,
      temperature: aiGenerateSqlForm.temperature,
      stream: false
    });

    // 处理响应
    if (response && response.data) {
      try {
        // 直接使用AI返回的内容，不进行额外的提取
        const jsonData = JSON.parse(response.data);

        // 验证生成的JSON
        if (validateJsonData(jsonData)) {
          // 将JSON填充到批量导入表单中
          batchImportForm.jsonContent = response.data;

          // 切换到手动模式，方便用户编辑
          batchImportMode.value = 'manual';

          // 关闭AI生成对话框
          showAiGenerateSqlDialog.value = false;

          // 显示成功消息
          ElMessage.success('JSON生成成功！');
        }
      } catch (error: any) {
        // JSON解析失败
        ElMessage.error(`生成的内容不是有效的JSON: ${error.message}`);
        console.error('JSON解析失败', error);
        console.error('原始响应内容:', response.data);
      }
    } else {
      ElMessage.error('JSON生成失败，请重试');
    }
  } catch (error) {
    console.error('AI生成JSON失败', error);
    ElMessage.error('AI生成JSON失败，请重试');
  } finally {
    aiGenerateSqlResult.loading = false;
  }
}

/**
 * 复制JSON示例到剪贴板
 */
function copyExampleJson() {
  // 获取示例JSON内容
  const exampleJson = document.querySelector('.json-example-code')?.textContent || '';

  // 复制到剪贴板
  navigator.clipboard.writeText(exampleJson)
    .then(() => {
      ElMessage.success('示例JSON已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制失败，请手动复制');
    });
}

onMounted(() => {
  queryAppList();
  queryCatList();
  fetchAvailableCategories();
  fetchModelsList();
});
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">应用配置</div>
      </template>
      <template #content>
        <div class="text-sm/6">
          <div>
            应用一旦创建，可能会被多处使用，请保持规范命名分类，后续尽量变更而不是删除。
          </div>
          <div>
            可自行选择应用是否固定模型。GPTs 需单独在特殊模型中配置 gpts
            模型，并自行搜索填写 gizmoID（例如：g-alKfVrz9K）。
          </div>
        </div>
      </template>
      <div class="flex gap-2">
        <HButton outline @click="visible = true">
          <SvgIcon name="ic:baseline-plus" />
          新增应用
        </HButton>
        <HButton outline @click="showBatchImportDialog = true" class="batch-import-btn">
          <SvgIcon name="ic:baseline-upload" />
          批量导入
        </HButton>
      </div>
    </PageHeader>

    <page-main>
      <!-- 应用过滤器 -->
      <AppFilter
        :categories="catList"
        :initialFilter="formInline"
        @filter="handleFilterChange"
        @reset="handlerReset"
      />
    </page-main>

    <page-main style="width: 100%">
      <!-- 应用列表 -->
      <div class="view-toggle mb-4">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button value="card">卡片视图</el-radio-button>
          <el-radio-button value="table">表格视图</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 卡片视图 -->
      <AppList
        v-if="viewMode === 'card'"
        :apps="tableData"
        :loading="loading"
        :isSystemRole="true"
        @edit="handleUpdatePackage"
        @delete="handleDeletePackage"
      />

      <!-- 表格视图 -->
      <el-table
        v-else
        v-loading="loading"
        border
        :data="tableData"
        style="width: 100%"
        size="large"
      >
        <el-table-column prop="coverImg" label="应用封面" width="100">
          <template #default="scope">
            <div v-if="scope.row.coverImg && scope.row.coverImg.startsWith('emoji:')" class="emoji-table-cell">
              {{ scope.row.coverImg.replace('emoji:', '') }}
            </div>
            <el-image
              v-else
              style="height: 50px"
              :src="scope.row.coverImg"
              fit="fill"
            />
          </template>
        </el-table-column>
        <el-table-column prop="catName" label="应用分类" width="100" />
        <el-table-column prop="name" label="应用名称" width="120" />
        <el-table-column prop="status" label="应用状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ QUESTION_STATUS_MAP[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序ID" width="80" />
        <el-table-column prop="preset" label="预设信息" width="400">
          <template #default="scope">
            <el-tooltip class="box-item" effect="dark" placement="top-start">
              <template #content>
                <div :style="{ maxWidth: '350px' }">
                  {{ scope.row.preset }}
                </div>
              </template>
              <div :style="{ maxHeight: '50px', cursor: 'pointer' }">
                {{ scope.row.preset }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="des" label="描述信息" width="300">
          <template #default="scope">
            <el-tooltip class="box-item" effect="dark" placement="top-start">
              <template #content>
                <div :style="{ maxWidth: '350px' }">
                  {{ scope.row.des }}
                </div>
              </template>
              <div :style="{ maxHeight: '50px', cursor: 'pointer' }">
                {{ scope.row.des }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="120">
          <template #default="scope">
            {{ utcToShanghaiTime(scope.row.createdAt, 'YYYY-MM-DD') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              v-if="scope.row.role === 'system' || scope.row.public"
              link
              type="primary"
              size="small"
              @click="handleUpdatePackage(scope.row)"
            >
              编辑
            </el-button>
            <el-popconfirm
              v-if="scope.row.role === 'system'"
              title="确认删除此应用么?"
              width="200"
              icon-color="red"
              @confirm="handleDeletePackage(scope.row)"
            >
              <template #reference>
                <el-button link type="danger" size="small">
                  删除应用
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <el-row class="mt-5 flex justify-end">
        <el-pagination
          v-model:current-page="formInline.page"
          v-model:page-size="formInline.size"
          class="mr-5"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="queryAppList"
          @current-change="queryAppList"
        />
      </el-row>
    </page-main>
    <el-dialog
      v-model="visible"
      :close-on-click-modal="false"
      :title="dialogTitle"
      width="90%"
      top="5vh"
      fullscreen
      @close="handlerCloseDialog(formPackageRef)"
      class="app-create-dialog"
    >
      <div class="app-create-container">
        <!-- 左侧：应用基本信息 -->
        <div class="app-info-panel">
          <h3 class="panel-title">应用基本信息</h3>
          <el-form
            ref="formPackageRef"
            label-position="right"
            label-width="100px"
            :model="formPackage"
            :rules="rules"
            class="app-info-form"
          >
        <el-form-item label="App分类" prop="catId">
          <el-select
            v-model="formPackage.catId"
            placeholder="请选择App分类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in catList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="App名称" prop="name">
          <el-input v-model="formPackage.name" placeholder="请填写App名称" />
        </el-form-item>
        <el-form-item v-if="!isUserApp" label="App状态" prop="status">
          <el-switch
            v-model="formPackage.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item label="App描述" prop="des">
          <el-input
            v-model="formPackage.des"
            type="textarea"
            placeholder="请填写App介绍信息、用于对外展示..."
            :rows="4"
          />
        </el-form-item>
        <el-form-item v-if="!isUserApp" label="启用GPTs" prop="isGPTs">
          <el-switch
            v-model="formPackage.isGPTs"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item
          v-if="Number(formPackage.isGPTs) === 1"
          label="gizmoID"
          prop="gizmoID"
        >
          <el-input
            v-model="formPackage.gizmoID"
            placeholder="请填写 GPTs 使用的 gizmoID"
          />
        </el-form-item>
        <el-form-item
          v-if="Number(formPackage.isGPTs) !== 1"
          label="App预设"
          prop="preset"
        >
          <div class="mb-2 text-gray-500 text-sm">
            可使用变量占位符，例如：我需要创建一个名为${projectName}的项目，需求描述如下：${requirements}
            <div class="flex gap-2 mt-1">
              <el-button type="primary" size="small" @click="openAiGenerator" class="ai-generate-btn">
                <el-icon><MagicStick /></el-icon> AI生成
              </el-button>
              <el-button plain size="small" @click="insertExamplePreset" class="example-preset-btn">插入示例预设</el-button>

              <!-- 插入已有变量 -->
              <el-dropdown @command="handleInsertVariableToPreset" size="small" trigger="click" v-if="formFields.length > 0">
                <el-button plain size="small" class="insert-variable-btn">
                  插入变量 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="(field, index) in formFields" :key="index" :command="field.name">
                      {{ field.label }} (${{'{'}}{{ field.name }}{{'}'}})
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 插入表单字段模板 -->
              <el-dropdown @command="handleInsertFormFieldTemplate" size="small" trigger="click">
                <el-button plain size="small" class="insert-template-btn">
                  插入表单字段 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="(fieldType, index) in formFieldTypes" :key="index" :command="index">
                      {{ fieldType.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <el-input
            ref="presetInputRef"
            id="preset-input"
            v-model="formPackage.preset"
            type="textarea"
            placeholder="请填写App预设信息，可使用${variableName}格式的变量占位符"
            :rows="6"
          />
        </el-form-item>
        <el-form-item
          v-if="!isUserApp && Number(formPackage.isGPTs) !== 1"
          label="固定模型"
          prop="isFixedModel"
        >
          <el-switch
            v-model="formPackage.isFixedModel"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item
          v-if="
            Number(formPackage.isFixedModel) === 1 &&
            Number(formPackage.isGPTs) !== 1
          "
          label="使用模型"
          prop="appModel"
        >
          <el-select
            v-model="formPackage.appModel"
            filterable
            allow-create
            placeholder="请选择应用使用的模型"
            clearable
          >
            <el-option
              v-for="item in MODEL_LIST"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="示例内容" prop="demoData">
          <el-input
            v-model="formPackage.demoData"
            type="textarea"
            placeholder="请填写App的demo示例数据、每换行一次表示一个新的示例..."
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="应用图标" prop="coverImg">
          <el-input
            v-model="formPackage.coverImg"
            placeholder="请填写或上传应用图标"
            clearable
          >
            <template #append>
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                <span
                  v-if="formPackage.coverImg && formPackage.coverImg.startsWith('emoji:')"
                  style="
                    font-size: 1.5rem;
                    margin: 5px 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  {{ formPackage.coverImg.replace('emoji:', '') }}
                </span>
                <img
                  v-else-if="formPackage.coverImg"
                  :src="formPackage.coverImg"
                  style="
                    max-width: 1.5rem;
                    max-height: 1.5rem;
                    margin: 5px 0;
                    object-fit: contain;
                  "
                />
                <el-icon v-else style="width: 1rem">
                  <Plus />
                </el-icon>
              </el-upload>
              <el-icon
                v-if="formPackage.coverImg"
                @click="reuploadAppAvatar"
                style="margin-left: 35px; width: 1rem"
              >
                <Refresh />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="Logo地址" prop="coverImg">
          <el-input
            v-model="formPackage.coverImg"
            placeholder="请填写应用Logo 或点击 上传按钮/图片预览 上传图片"
          />
        </el-form-item>
        <el-icon>
          <i class="el-icon-refresh" />
        </el-icon>

        <el-form-item label="应用Logo" prop="coverImg">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="formPackage.coverImg"
              :src="formPackage.coverImg"
              style="width: 100px"
              class="avatar"
            />
            <el-icon v-else class="avatar-uploader-icon ml-3">
              <Plus />
            </el-icon>
          </el-upload>
        </el-form-item> -->

        <el-form-item label="排序ID" prop="order">
          <el-input
            v-model.number="formPackage.order"
            placeholder="请填写排序ID[数字越大越靠前]"
          />
        </el-form-item>

        <!-- 表单定义已移动到右侧面板 -->
        <!-- 这里不再显示表单定义相关内容 -->
          </el-form>
        </div>

        <!-- 右侧：表单编辑区域 -->
        <div class="form-edit-panel">
          <h3 class="panel-title">表单编辑</h3>

          <div class="form-edit-content">
            <div class="form-edit-header">
              <el-switch v-model="formPackage.hasForm" active-text="启用表单" inactive-text="禁用表单" />

              <div class="form-edit-actions" v-if="formPackage.hasForm">
                <el-button type="primary" size="small" @click="toggleFormEditor">
                  {{ showFormEditor ? '隐藏编辑器' : '显示编辑器' }}
                </el-button>
                <el-button type="success" size="small" @click="showFormPreviewDrawer = true" v-if="formFields.length > 0">
                  <el-icon><View /></el-icon> 表单预览
                </el-button>
              </div>
            </div>

            <template v-if="formPackage.hasForm">
              <div class="form-basic-info">
                <el-form label-position="top" :model="formPackage">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="表单名称" prop="formName">
                        <el-input v-model="formPackage.formName" placeholder="请填写表单名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="表单描述" prop="formDescription">
                        <el-input v-model="formPackage.formDescription" placeholder="请填写表单描述" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>

              <div class="form-fields-tools">
                <div class="flex gap-2">
                  <el-dropdown @command="handleAddFormField" split-button type="primary" size="small">
                    添加字段
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item v-for="(fieldType, index) in formFieldTypes" :key="index" :command="fieldType.type">
                          {{ fieldType.label }}
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                  <el-button plain size="small" @click="handleExtractVariablesFromPreset" class="extract-variables-btn">从预设提取变量</el-button>
                </div>

                <div class="text-gray-500 text-sm mt-2">
                  表单字段的name属性应与预设信息中的变量名保持一致，例如预设中的${projectName}对应表单字段的name应为projectName
                </div>
              </div>

              <!-- 表单预览抽屉 -->
              <FormPreviewDrawer
                v-model:visible="showFormPreviewDrawer"
                :fields="formPackage.formFields"
                :formName="formPackage.formName"
                :formDescription="formPackage.formDescription"
                :appName="formPackage.name"
                @submit="handleFormPreviewSubmit"
              />

              <!-- 表单内容区域 -->
              <div class="form-content-area">
                <!-- 表单编辑器组件 -->
                <FormEditor
                  v-if="showFormEditor"
                  v-model="formPackage.formFields"
                  v-model:showJsonEditor="showFormEditor"
                  @update:modelValue="handleFormEditorChange"
                />

                <!-- 表单字段预览 -->
                <div v-else class="form-fields-preview">
                  <div v-if="formFields.length === 0" class="text-gray-400 text-center py-4">
                    暂无表单字段，请点击“添加字段”按钮添加
                  </div>
                  <div v-else class="border rounded p-4 bg-gray-50">
                    <div v-for="(field, index) in formFields" :key="index" class="mb-4 p-3 border rounded bg-white">
                      <div class="flex justify-between items-center mb-2">
                        <div class="font-medium">{{ field.label }} <span v-if="field.required" class="text-red-500">*</span></div>
                        <div class="text-gray-500 text-sm">
                          <el-tag size="small" :type="(fieldTypeTagTypes[field.type as keyof typeof fieldTypeTagTypes] || 'info') as any">
                            {{ fieldTypeLabels[field.type as keyof typeof fieldTypeLabels] || field.type }}
                          </el-tag>
                        </div>
                      </div>
                      <div class="text-sm text-gray-500 mb-2" v-if="field.placeholder">占位文本: {{ field.placeholder }}</div>
                      <div class="text-sm text-gray-500 mb-2" v-if="field.name">
                        <span class="font-medium">字段名:</span> {{ field.name }}
                        <span class="text-blue-500">(对应预设变量: ${{'{'}}{{ field.name }}{{'}'}})</span>
                      </div>

                      <!-- 特定字段类型的额外属性 -->
                      <div v-if="['select', 'radio', 'checkbox'].includes(field.type) && field.options" class="text-sm text-gray-500 mb-2">
                        <span class="font-medium">选项:</span>
                        <el-tag
                          v-for="(option, optIndex) in field.options"
                          :key="optIndex"
                          size="small"
                          class="ml-1 mr-1 mb-1"
                        >
                          {{ option.label || option }}
                        </el-tag>
                      </div>

                      <div v-if="['number', 'slider'].includes(field.type)" class="text-sm text-gray-500 mb-2">
                        <span class="font-medium">范围:</span> {{ field.min || 0 }} - {{ field.max || 100 }}, 步长: {{ field.step || 1 }}
                      </div>

                      <div v-if="field.type === 'rate'" class="text-sm text-gray-500 mb-2">
                        <span class="font-medium">最大分值:</span> {{ field.max || 5 }}
                      </div>

                      <div v-if="field.type === 'switch'" class="text-sm text-gray-500 mb-2">
                        <span class="font-medium">文本:</span> {{ field.activeText || '是' }}/{{ field.inactiveText || '否' }}
                      </div>

                      <div v-if="['date', 'time', 'datetime'].includes(field.type)" class="text-sm text-gray-500 mb-2">
                        <span class="font-medium">格式:</span> {{ field.format || (field.type && defaultDateTimeFormats[field.type as keyof typeof defaultDateTimeFormats]) || '' }}
                      </div>

                      <div class="flex justify-end mt-2">
                        <el-button type="primary" size="small" @click="toggleFormEditor" class="mr-2">编辑</el-button>
                        <el-button type="danger" size="small" @click="handleRemoveFormField(index)">删除</el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="text-gray-500 text-sm mt-2">
                表单字段定义使用JSON格式，支持的字段类型有：input、textarea、select、radio、checkbox等
              </div>
            </template>

            <div v-else class="form-disabled-message">
              <el-empty description="表单功能已禁用，请先启用表单" />
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="mr-5 flex justify-end">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handlerSubmit(formPackageRef)">
            {{ dialogButton }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI生成器组件 -->
    <AiPromptGenerator
      v-model:visible="showAiGenerator"
      @generate-success="handleAiGenerateSuccess"
    />

    <!-- AI生成JSON对话框 -->
    <el-dialog
      v-model="showAiGenerateSqlDialog"
      title="AI生成JSON"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="ai-generate-sql-container">
        <el-form :model="aiGenerateSqlForm" label-width="120px">
          <!-- 模型选择器 -->
          <el-form-item label="选择AI模型" required>
            <el-select
              v-model="aiGenerateSqlForm.model"
              placeholder="请选择AI模型"
              :loading="loadingModels"
              style="width: 100%"
            >
              <el-option
                v-for="option in modelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <div class="model-option-content">
                  <span>{{ option.label }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 应用描述 -->
          <el-form-item label="应用描述" required>
            <el-input
              v-model="aiGenerateSqlForm.appDescription"
              type="textarea"
              :rows="3"
              placeholder="请详细描述您想要创建的应用，包括应用名称、功能、用途等"
            />
          </el-form-item>

          <!-- 应用数量 -->
          <el-form-item label="应用数量">
            <el-input-number
              v-model="aiGenerateSqlForm.appCount"
              :min="1"
              :max="5"
              controls-position="right"
            />
            <span class="form-item-tip">建议生成 1-5 个应用</span>
          </el-form-item>

          <!-- 温度设置 -->
          <el-form-item label="温度设置">
            <el-slider
              v-model="aiGenerateSqlForm.temperature"
              :min="0"
              :max="1"
              :step="0.1"
              show-stops
              show-input
              :format-tooltip="(value: number) => value.toFixed(1)"
            />
            <span class="form-item-tip">较低的温度生成更精确的JSON，较高的温度生成更创造性的JSON</span>
          </el-form-item>

          <!-- 应用分类 -->
          <el-form-item label="应用分类">
            <el-select
              v-model="aiGenerateSqlForm.catId"
              placeholder="请选择应用分类"
              style="width: 100%"
            >
              <el-option
                v-for="item in availableCategories"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <span class="form-item-tip">请选择有效的分类ID，AI将使用此分类生成应用</span>
          </el-form-item>

          <!-- 其他要求 -->
          <el-form-item label="其他要求">
            <el-input
              v-model="aiGenerateSqlForm.otherRequirements"
              type="textarea"
              :rows="2"
              placeholder="其他特殊要求，如图标类型、表单字段特点等"
            />
          </el-form-item>
        </el-form>

        <!-- 生成结果区域 -->
        <div v-if="aiGenerateSqlResult.show" class="ai-generate-result mt-4">
          <el-alert
            :title="aiGenerateSqlResult.loading ? '正在生成JSON...' : '生成完成'"
            :type="aiGenerateSqlResult.loading ? 'info' : 'success'"
            :closable="false"
            show-icon
          >
            <div v-if="aiGenerateSqlResult.loading" class="loading-animation">
              <i class="el-icon-loading"></i> 正在生成中，请稍候...
            </div>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAiGenerateSqlDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleGenerateSql"
            :loading="aiGenerateSqlResult.loading"
            :disabled="!aiGenerateSqlForm.appDescription || !aiGenerateSqlForm.model"
          >
            生成JSON
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showBatchImportDialog"
      title="批量导入应用"
      width="70%"
      :close-on-click-modal="false"
      class="batch-import-dialog"
    >
      <div class="batch-import-container">
        <!-- 顶部导航 -->
        <div class="batch-import-tabs">
          <el-radio-group v-model="batchImportMode" size="large">
            <el-radio-button label="manual">
              <i class="el-icon-edit"></i> 手动输入JSON
            </el-radio-button>
            <el-radio-button label="ai">
              <i class="el-icon-magic-stick"></i> AI生成JSON
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 导入说明卡片 -->
        <div class="batch-import-info-card">
          <div class="info-card-header">
            <i class="el-icon-info-filled"></i>
            <span>批量导入应用说明</span>
          </div>
          <div class="info-card-content">
            <div class="info-item">
              <div class="info-icon">1</div>
              <div class="info-text">请在下方输入JSON数据，支持批量创建应用及其关联表单</div>
            </div>
            <div class="info-item">
              <div class="info-icon">2</div>
              <div class="info-text">JSON数据必须是数组格式，每个元素包含应用(app)和表单(forms)字段</div>
            </div>
            <div class="info-item">
              <div class="info-icon">3</div>
              <div class="info-text">请确保JSON数据格式正确，特别是字段名和值的对应关系</div>
            </div>
            <div class="info-item">
              <div class="info-icon">4</div>
              <div class="info-text">应用图标支持emoji格式，使用emoji:前缀，如emoji:📝</div>
            </div>
            <div class="info-item">
              <div class="info-icon">5</div>
              <div class="info-text">为确保应用在前端正确显示，请设置public=0和isSystemReserved=0</div>
            </div>
          </div>
        </div>

        <!-- JSON示例卡片 -->
        <div class="batch-import-example-card">
          <el-collapse>
            <el-collapse-item title="查看JSON示例" name="1">
              <div class="json-example-container">
                <pre class="json-example-code">[
  {
    "app": {
      "name": "教案生成助手",
      "catId": 1,
      "des": "根据教学目标和课程内容自动生成详细教案",
      "preset": "你是一位教育专家，请根据以下信息生成教案：\n课题：${topic}\n年级：${grade}",
      "coverImg": "emoji:📝",
      "order": 180,
      "status": 1,
      "demoData": "帮我设计一节小学三年级数学课\n请为高中语文课设计教案",
      "role": "system",
      "isGPTs": 0,
      "isFixedModel": 0,
      "appModel": "",
      "gizmoID": "",
      "public": 0,
      "isSystemReserved": 0
    },
    "forms": [
      {
        "name": "教案设计表单",
        "description": "请填写教案所需的基本信息",
        "fields": "[{\"type\":\"input\",\"label\":\"课题\",\"required\":true,\"placeholder\":\"请输入课题名称\",\"key\":\"topic\"},{\"type\":\"input\",\"label\":\"年级\",\"required\":true,\"placeholder\":\"请输入年级\",\"key\":\"grade\"}]",
        "order": 100,
        "status": 1
      }
    ]
  }
]</pre>
                <div class="copy-button-container">
                  <el-button size="small" type="primary" @click="copyExampleJson">
                    <i class="el-icon-document-copy"></i> 复制示例
                  </el-button>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 手动输入模式 -->
        <div v-if="batchImportMode === 'manual'" class="batch-import-editor-card">
          <div class="editor-card-header">
            <span>JSON数据</span>
            <div class="editor-card-actions">
              <el-checkbox v-model="batchImportForm.validateOnly">仅验证JSON数据，不执行导入</el-checkbox>
            </div>
          </div>
          <div class="editor-card-content">
            <div class="json-editor-container">
              <el-input
                v-model="batchImportForm.jsonContent"
                type="textarea"
                :rows="15"
                placeholder="请输入JSON数据，支持批量创建应用及其关联表单"
                class="json-editor"
              />
            </div>
          </div>
        </div>

        <!-- AI生成模式 -->
        <div v-else class="batch-import-editor-card">
          <div class="editor-card-header">
            <span>AI生成的JSON数据</span>
            <div class="editor-card-actions">
              <el-checkbox v-model="batchImportForm.validateOnly">仅验证JSON数据，不执行导入</el-checkbox>
            </div>
          </div>
          <div class="editor-card-content">
            <div class="json-editor-container">
              <el-input
                v-model="batchImportForm.jsonContent"
                type="textarea"
                :rows="15"
                placeholder="点击下方按钮使用AI生成JSON数据"
                :disabled="true"
                class="json-editor"
              />
              <div class="ai-generate-buttons">
                <el-button
                  type="primary"
                  @click="showAiGenerateSqlDialog = true"
                  :icon="MagicStick"
                  class="ai-generate-btn"
                >
                  AI生成JSON
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 导入结果 -->
        <div class="batch-import-result-card" v-if="batchImportResult.show">
          <div class="result-card-header" :class="'result-' + batchImportResult.type">
            <i :class="'el-icon-' + (batchImportResult.type === 'success' ? 'circle-check' : 'circle-close')"></i>
            <span>{{ batchImportResult.title }}</span>
          </div>
          <div class="result-card-content">
            <div class="import-result-message" v-html="batchImportResult.message.replace(/\n/g, '<br>')"></div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchImportDialog = false">取消</el-button>
          <el-button type="primary" @click="handleBatchImport" :loading="batchImportLoading">
            {{ batchImportForm.validateOnly ? '验证JSON' : '执行导入' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
/* 两栏布局样式 */
.app-create-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.app-create-container {
  display: flex;
  height: calc(100vh - 150px);
  overflow: hidden;
}

.app-info-panel {
  width: 40%;
  padding: 20px;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;
}

.form-edit-panel {
  width: 60%;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fa;
}

.panel-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
}

.app-info-form {
  padding-right: 20px;
}

/* 表单编辑区域样式 */
.form-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-edit-actions {
  display: flex;
  gap: 10px;
}

.form-basic-info {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-fields-tools {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-disabled-message {
  padding: 40px 0;
  background-color: #fff;
  border-radius: 4px;
  margin-top: 20px;
}

/* 原有样式 */
.form-fields-preview {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-preview-button-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 16px;
}

.example-preset-btn {
  border: none;
  padding: 0;
  margin-left: 5px;
  color: #409eff;
  background: transparent;
}

.example-preset-btn:hover {
  color: #66b1ff;
  background: transparent;
}

.extract-variables-btn {
  color: #67c23a;
  border-color: #67c23a;
}

.extract-variables-btn:hover {
  color: #85ce61;
  border-color: #85ce61;
  background-color: rgba(103, 194, 58, 0.1);
}

.insert-variable-btn {
  color: #409eff;
  border-color: #409eff;
}

.insert-variable-btn:hover {
  color: #66b1ff;
  border-color: #66b1ff;
  background-color: rgba(64, 158, 255, 0.1);
}

.insert-template-btn {
  color: #e6a23c;
  border-color: #e6a23c;
}

.insert-template-btn:hover {
  color: #ebb563;
  border-color: #ebb563;
  background-color: rgba(230, 162, 60, 0.1);
}

.ai-generate-btn {
  background-color: #8e44ad;
  border-color: #8e44ad;
}

.ai-generate-btn:hover {
  background-color: #9b59b6;
  border-color: #9b59b6;
}

.view-toggle {
  display: flex;
  justify-content: flex-end;
}

.form-content-area {
  margin-bottom: 16px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.emoji-table-cell {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background-color: #f5f7fa;
  border-radius: 4px;
}

/* 批量导入样式 */
.batch-import-btn {
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.batch-import-btn:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}

.sql-example-code {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
  max-height: 300px;
  overflow-y: auto;
}

/* SQL生成相关样式 */
.sql-textarea-container {
  position: relative;
}

.ai-generate-buttons {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.ai-generate-sql-container {
  padding: 10px;
}

.form-item-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.model-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.loading-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.mt-4 {
  margin-top: 16px;
}

.import-result-message {
  line-height: 1.6;
  font-size: 14px;
  white-space: normal;
  word-break: break-word;
}

.import-result-message br {
  margin-bottom: 5px;
}

/* 批量导入对话框美化样式 */
.batch-import-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.batch-import-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 500px;
}

/* 顶部导航样式 */
.batch-import-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

/* 导入说明卡片样式 */
.batch-import-info-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.info-card-header {
  background-color: #ecf5ff;
  color: #409eff;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-card-content {
  padding: 16px 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.info-icon {
  width: 24px;
  height: 24px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.info-text {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

/* JSON示例卡片样式 */
.batch-import-example-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.json-example-container {
  position: relative;
}

.json-example-code {
  background-color: #282c34;
  color: #abb2bf;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Fira Code', 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 1.5;
  overflow-y: auto;
  max-height: 300px;
}

.copy-button-container {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 编辑器卡片样式 */
.batch-import-editor-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.editor-card-header {
  background-color: #f5f7fa;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e4e7ed;
}

.editor-card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.editor-card-content {
  padding: 16px;
}

.json-editor-container {
  position: relative;
}

.json-editor {
  font-family: 'Fira Code', 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.json-editor :deep(.el-textarea__inner) {
  background-color: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  color: #303133;
  font-family: 'Fira Code', 'Courier New', Courier, monospace;
}

/* 导入结果卡片样式 */
.batch-import-result-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

.result-card-header {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-success {
  background-color: #f0f9eb;
  color: #67c23a;
}

.result-error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.result-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.result-info {
  background-color: #ecf5ff;
  color: #409eff;
}

.result-card-content {
  padding: 16px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-import-dialog :deep(.el-dialog) {
    width: 95% !important;
  }

  .batch-import-container {
    padding: 12px;
  }

  .info-item {
    flex-direction: column;
    gap: 4px;
  }

  .editor-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .editor-card-actions {
    width: 100%;
  }
}
</style>