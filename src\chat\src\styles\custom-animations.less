/* 自定义动画效果 */

/* 渐入动画 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in-scale {
  animation: fadeInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite cubic-bezier(0.4, 0, 0.6, 1);
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-animation {
  overflow: hidden;
  white-space: nowrap;
  animation: typing 1.5s steps(40, end);
}

/* 浮动效果增强版 */
@keyframes float {
  0% {
    transform: translateY(0px);
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: translateY(-10px);
    box-shadow: 0 15px 25px 0px rgba(0, 0, 0, 0.05);
  }
  100% {
    transform: translateY(0px);
    box-shadow: 0 5px 15px 0px rgba(0, 0, 0, 0.1);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* 闪光效果 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.dark .shimmer-effect {
  background: linear-gradient(
    90deg,
    rgba(50, 50, 50, 0) 0%,
    rgba(50, 50, 50, 0.8) 50%,
    rgba(50, 50, 50, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* 文本阴影效果 */
.text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.text-shadow-md {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .text-shadow-md {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 渐变文本 */
.gradient-text {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.dark .gradient-text {
  background: linear-gradient(90deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

/* 毛玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-effect {
  background: rgba(30, 30, 30, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(50, 50, 50, 0.1);
}

/* 3D按钮效果 */
.button-3d {
  transform: translateY(0);
  box-shadow: 0 4px 0 0 rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.button-3d:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 0 0 rgba(0, 0, 0, 0.1);
}

.button-3d:active {
  transform: translateY(2px);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
}

/* 微妙的背景动画 */
@keyframes subtleBackgroundShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.subtle-bg-animation {
  background: linear-gradient(270deg, #f8fafc, #f1f5f9);
  background-size: 200% 200%;
  animation: subtleBackgroundShift 10s ease infinite;
}

.dark .subtle-bg-animation {
  background: linear-gradient(270deg, #1e293b, #0f172a);
  background-size: 200% 200%;
  animation: subtleBackgroundShift 10s ease infinite;
}
