<?xml version="1.0" encoding="UTF-8"?>
<svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background elements -->
  <circle cx="250" cy="200" r="150" fill="#E0F2FE" fill-opacity="0.5"/>
  <circle cx="250" cy="200" r="100" fill="#BAE6FD" fill-opacity="0.5"/>
  
  <!-- Child figure 1 -->
  <g transform="translate(150, 150)">
    <!-- Head -->
    <circle cx="0" cy="0" r="25" fill="#FDA4AF"/>
    <!-- Face -->
    <circle cx="-10" cy="-5" r="3" fill="#1E293B"/>
    <circle cx="10" cy="-5" r="3" fill="#1E293B"/>
    <path d="M-10 10 Q0 15 10 10" stroke="#1E293B" stroke-width="2" fill="none"/>
    <!-- Body -->
    <path d="M0 25 L0 80" stroke="#FDA4AF" stroke-width="20" stroke-linecap="round"/>
    <!-- Arms -->
    <path d="M0 40 L-30 60" stroke="#FDA4AF" stroke-width="10" stroke-linecap="round"/>
    <path d="M0 40 L30 60" stroke="#FDA4AF" stroke-width="10" stroke-linecap="round"/>
    <!-- Legs -->
    <path d="M0 80 L-20 120" stroke="#FDA4AF" stroke-width="10" stroke-linecap="round"/>
    <path d="M0 80 L20 120" stroke="#FDA4AF" stroke-width="10" stroke-linecap="round"/>
  </g>
  
  <!-- Child figure 2 -->
  <g transform="translate(300, 180)">
    <!-- Head -->
    <circle cx="0" cy="0" r="20" fill="#A5B4FC"/>
    <!-- Face -->
    <circle cx="-8" cy="-4" r="2.5" fill="#1E293B"/>
    <circle cx="8" cy="-4" r="2.5" fill="#1E293B"/>
    <path d="M-8 8 Q0 12 8 8" stroke="#1E293B" stroke-width="2" fill="none"/>
    <!-- Body -->
    <path d="M0 20 L0 65" stroke="#A5B4FC" stroke-width="16" stroke-linecap="round"/>
    <!-- Arms -->
    <path d="M0 35 L-25 50" stroke="#A5B4FC" stroke-width="8" stroke-linecap="round"/>
    <path d="M0 35 L25 50" stroke="#A5B4FC" stroke-width="8" stroke-linecap="round"/>
    <!-- Legs -->
    <path d="M0 65 L-15 100" stroke="#A5B4FC" stroke-width="8" stroke-linecap="round"/>
    <path d="M0 65 L15 100" stroke="#A5B4FC" stroke-width="8" stroke-linecap="round"/>
  </g>
  
  <!-- Book -->
  <g transform="translate(220, 220)">
    <rect x="-40" y="-30" width="80" height="60" rx="5" fill="#60A5FA"/>
    <rect x="-35" y="-25" width="35" height="50" rx="2" fill="#EFF6FF"/>
    <rect x="0" y="-25" width="35" height="50" rx="2" fill="#EFF6FF"/>
    <line x1="-20" y1="-15" x2="-5" y2="-15" stroke="#60A5FA" stroke-width="2"/>
    <line x1="-20" y1="-5" x2="-5" y2="-5" stroke="#60A5FA" stroke-width="2"/>
    <line x1="-20" y1="5" x2="-5" y2="5" stroke="#60A5FA" stroke-width="2"/>
    <line x1="5" y1="-15" x2="20" y2="-15" stroke="#60A5FA" stroke-width="2"/>
    <line x1="5" y1="-5" x2="20" y2="-5" stroke="#60A5FA" stroke-width="2"/>
    <line x1="5" y1="5" x2="20" y2="5" stroke="#60A5FA" stroke-width="2"/>
  </g>
  
  <!-- Computer -->
  <g transform="translate(100, 220)">
    <rect x="-25" y="-20" width="50" height="40" rx="3" fill="#10B981"/>
    <rect x="-20" y="-15" width="40" height="30" rx="2" fill="#ECFDF5"/>
    <rect x="-30" y="20" width="60" height="5" rx="2" fill="#10B981"/>
    <line x1="-10" y1="0" x2="10" y2="0" stroke="#10B981" stroke-width="2"/>
    <line x1="0" y1="-10" x2="0" y2="10" stroke="#10B981" stroke-width="2"/>
  </g>
  
  <!-- Music notes -->
  <g transform="translate(350, 120)">
    <circle cx="0" cy="0" r="10" fill="#EC4899"/>
    <path d="M10 0 L10 -30 L15 -35" stroke="#EC4899" stroke-width="3" fill="none"/>
    <circle cx="25" cy="-10" r="8" fill="#EC4899"/>
    <path d="M33 -10 L33 -35 L38 -40" stroke="#EC4899" stroke-width="3" fill="none"/>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="400" cy="100" r="15" fill="#F472B6" fill-opacity="0.7"/>
  <circle cx="420" cy="150" r="10" fill="#F472B6" fill-opacity="0.5"/>
  <circle cx="380" cy="170" r="8" fill="#F472B6" fill-opacity="0.3"/>
  
  <circle cx="100" cy="100" r="12" fill="#34D399" fill-opacity="0.7"/>
  <circle cx="80" cy="130" r="8" fill="#34D399" fill-opacity="0.5"/>
  <circle cx="120" cy="150" r="6" fill="#34D399" fill-opacity="0.3"/>
  
  <circle cx="200" cy="80" r="10" fill="#60A5FA" fill-opacity="0.7"/>
  <circle cx="230" cy="60" r="7" fill="#60A5FA" fill-opacity="0.5"/>
  <circle cx="180" cy="50" r="5" fill="#60A5FA" fill-opacity="0.3"/>
</svg>
