<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/store';
import { NCard, NButton, NSpace } from 'naive-ui';

const router = useRouter();
const authStore = useAuthStore();

// 进入教师工作台
const enterTeacherSpace = () => {
  // 设置用户角色为教师
  authStore.setUserRole('teacher');
  router.push('/teacher');
};

// 进入学生工作台
const enterStudentSpace = () => {
  // 设置用户角色为学生
  authStore.setUserRole('student');
  router.push('/student');
};
</script>

<template>
  <div class="welcome-page min-h-screen bg-gradient-to-br from-blue-50 to-green-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-6">
    <div class="welcome-container max-w-4xl w-full">
      <!-- 标题区域 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-800 dark:text-gray-100 mb-4">
          🎨 AI创作平台
        </h1>
        <p class="text-lg md:text-xl text-gray-600 dark:text-gray-300">
          为小学师生打造的智能创作助手
        </p>
      </div>

      <!-- 角色选择区域 -->
      <div class="role-selection grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
        <!-- 教师入口 -->
        <NCard class="teacher-card group cursor-pointer hover:shadow-2xl transition-all duration-300" @click="enterTeacherSpace">
          <div class="text-center p-6">
            <div class="role-icon mb-6">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-4xl text-white shadow-lg group-hover:scale-110 transition-transform">
                👩‍🏫
              </div>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-3">
              教师工作台
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              智能教学助手，学生作品管理，教学资源创建
            </p>
            <div class="features space-y-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
              <div>✨ AI教学对话助手</div>
              <div>📊 学生作品统计分析</div>
              <div>🎯 个性化教学工具</div>
            </div>
            <NButton type="primary" size="large" class="w-full teacher-btn">
              进入教师空间
            </NButton>
          </div>
        </NCard>

        <!-- 学生入口 -->
        <NCard class="student-card group cursor-pointer hover:shadow-2xl transition-all duration-300" @click="enterStudentSpace">
          <div class="text-center p-6">
            <div class="role-icon mb-6">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-4xl text-white shadow-lg group-hover:scale-110 transition-transform">
                👨‍🎓
              </div>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-3">
              学生创作空间
            </h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              AI绘本创作，故事编写，作品分享展示
            </p>
            <div class="features space-y-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
              <div>🎨 AI绘本创作工具</div>
              <div>📚 个人作品展示</div>
              <div>🏆 创作成就系统</div>
            </div>
            <NButton type="success" size="large" class="w-full student-btn">
              进入学习空间
            </NButton>
          </div>
        </NCard>
      </div>

      <!-- 底部信息 -->
      <div class="footer-info text-center mt-12 text-gray-500 dark:text-gray-400">
        <p class="text-sm">
          🌟 选择您的身份，开启专属的AI创作之旅
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.welcome-page {
  .teacher-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #3b82f6;
      transform: translateY(-4px);
    }
    
    .teacher-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      }
    }
  }
  
  .student-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #22c55e;
      transform: translateY(-4px);
    }
    
    .student-btn {
      background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
      }
    }
  }
  
  :deep(.n-card) {
    border-radius: 16px;
    overflow: hidden;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .welcome-page {
    .role-selection {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    h1 {
      font-size: 2.5rem;
    }
    
    .role-icon {
      .w-24 {
        width: 5rem;
        height: 5rem;
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.role-icon {
  animation: float 3s ease-in-out infinite;
}
</style> 