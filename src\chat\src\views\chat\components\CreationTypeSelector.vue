<template>
  <div class="creation-type-selector">
    <div class="selector-container" :class="{ 'expanded': isExpanded }">
      <div class="selector-header" @click="toggleExpand">
        <div class="current-type">
          <div class="type-icon" :class="currentType.bgClass">
            <SvgIcon :name="currentType.icon" size="20" />
          </div>
          <span class="type-name">{{ currentType.title }}</span>
        </div>
        <div class="toggle-icon">
          <SvgIcon :name="isExpanded ? 'ri:arrow-up-s-line' : 'ri:arrow-down-s-line'" size="20" />
        </div>
      </div>
      
      <transition name="fade-slide">
        <div v-if="isExpanded" class="type-options">
          <div 
            v-for="type in creationTypes" 
            :key="type.type" 
            class="type-option"
            :class="{ 'active': currentType.type === type.type }"
            @click="selectType(type)"
          >
            <div class="type-icon" :class="type.bgClass">
              <SvgIcon :name="type.icon" size="20" />
            </div>
            <div class="type-info">
              <div class="type-title">{{ type.title }}</div>
              <div class="type-description">{{ type.description }}</div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { SvgIcon } from '@/components/common';

const props = defineProps({
  modelValue: {
    type: String,
    default: 'chat'
  }
});

const emit = defineEmits(['update:modelValue']);

const route = useRoute();
const router = useRouter();
const isExpanded = ref(false);

const creationTypes = [
  {
    type: 'chat',
    title: '通用对话',
    description: '与AI进行自由对话',
    icon: 'ri:chat-3-line',
    path: '/chat',
    bgClass: 'bg-blue-100 text-blue-600 dark:bg-blue-800 dark:text-blue-300'
  },
  {
    type: 'programming',
    title: 'AI编程',
    description: '创建网页、应用和交互式内容',
    icon: 'ri:code-box-line',
    path: '/chat?preset=programming',
    bgClass: 'bg-green-100 text-green-600 dark:bg-green-800 dark:text-green-300'
  },
  {
    type: 'writing',
    title: 'AI写作',
    description: '创作文章、故事和各类文本内容',
    icon: 'ri:file-text-line',
    path: '/chat?preset=writing',
    bgClass: 'bg-purple-100 text-purple-600 dark:bg-purple-800 dark:text-purple-300'
  },
  {
    type: 'picturebook',
    title: 'AI绘本',
    description: '创作图文结合的绘本和故事书',
    icon: 'ri:book-open-line',
    path: '/chat?preset=picturebook',
    bgClass: 'bg-orange-100 text-orange-600 dark:bg-orange-800 dark:text-orange-300'
  },
  {
    type: 'music',
    title: 'AI音乐',
    description: '创作音乐和音频内容',
    icon: 'ri:music-line',
    path: '/chat?preset=music',
    bgClass: 'bg-pink-100 text-pink-600 dark:bg-pink-800 dark:text-pink-300'
  }
];

const currentType = computed(() => {
  const preset = route.query.preset as string || 'chat';
  const found = creationTypes.find(type => type.type === preset);
  return found || creationTypes[0];
});

watch(() => route.query.preset, (newPreset) => {
  emit('update:modelValue', newPreset || 'chat');
});

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const selectType = (type) => {
  router.push(type.path);
  isExpanded.value = false;
  emit('update:modelValue', type.type);
};
</script>

<style scoped>
.creation-type-selector {
  position: relative;
  width: 100%;
  z-index: 10;
}

.selector-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.dark .selector-container {
  background-color: var(--color-gray-800);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.selector-header:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.dark .selector-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.current-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
}

.type-name {
  font-weight: 500;
}

.type-options {
  max-height: 300px;
  overflow-y: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .type-options {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.type-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.type-option:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.dark .type-option:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.type-option.active {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .type-option.active {
  background-color: rgba(255, 255, 255, 0.07);
}

.type-info {
  display: flex;
  flex-direction: column;
}

.type-title {
  font-weight: 500;
}

.type-description {
  font-size: 0.75rem;
  color: var(--color-gray-500);
}

.dark .type-description {
  color: var(--color-gray-400);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
