<template>
  <div class="modal-handler">
    <UserCenterModal v-model:visible="showModal" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import UserCenterModal from '@/components/common/UserCenterModal/index.vue';

const router = useRouter();
const showModal = ref(false);

onMounted(() => {
  // 打开弹窗
  showModal.value = true;

  // 监听弹窗关闭事件，关闭后返回上一页
  const unwatch = watch(showModal, (newVal) => {
    if (!newVal) {
      // 返回上一页
      router.back();
      // 取消监听
      unwatch();
    }
  });
});
</script>

<style scoped>
.modal-handler {
  /* 这个组件不需要显示任何内容，只是用于处理弹窗逻辑 */
  display: none;
}
</style>
