<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NButton, NGrid, NGridItem } from 'naive-ui';
import { useAuthStore } from '@/store';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const router = useRouter();
const authStore = useAuthStore();
const { isMobile } = useBasicLayout();

// 创作工具列表
const creationTools = [
  {
    id: 'storybook',
    title: 'AI绘本创作',
    description: '创作精美的儿童绘本，AI辅助生成故事和插图',
    icon: 'ri:book-open-line',
    path: '/storybook',
    color: 'from-orange-400 to-amber-500',
    textColor: 'text-orange-600',
    bgColor: 'bg-orange-50',
    darkBgColor: 'dark:bg-orange-900/30',
    darkTextColor: 'dark:text-orange-400'
  },
  {
    id: 'programming',
    title: 'AI编程应用',
    description: '创建交互式网页应用，无需编程经验',
    icon: 'ri:code-box-line',
    path: '/aiProgramming',
    color: 'from-emerald-400 to-green-500',
    textColor: 'text-emerald-600',
    bgColor: 'bg-emerald-50',
    darkBgColor: 'dark:bg-emerald-900/30',
    darkTextColor: 'dark:text-emerald-400'
  },
  {
    id: 'music',
    title: 'AI音乐创作',
    description: '创作原创音乐和音频内容，AI辅助作曲',
    icon: 'ri:music-line',
    path: '/chat?preset=music',
    color: 'from-pink-400 to-rose-500',
    textColor: 'text-pink-600',
    bgColor: 'bg-pink-50',
    darkBgColor: 'dark:bg-pink-900/30',
    darkTextColor: 'dark:text-pink-400'
  }
];

// 处理工具点击
const handleToolClick = (path: string) => {
  // 检查用户是否登录
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }
  router.push(path);
};
</script>

<template>
  <section class="creation-tools-section">
    <div class="section-container">
      <div class="section-header">
        <h2 class="section-title">创作工具</h2>
        <p class="section-description">使用我们的AI工具，轻松创作精彩内容</p>
      </div>

      <NGrid :cols="isMobile ? 1 : 3" :x-gap="24" :y-gap="24">
        <NGridItem v-for="tool in creationTools" :key="tool.id">
          <div
            class="tool-card"
            :class="[tool.bgColor, tool.darkBgColor]"
            @click="handleToolClick(tool.path)"
          >
            <div class="tool-icon" :class="[tool.textColor, tool.darkTextColor]">
              <SvgIcon :name="tool.icon" size="32" />
            </div>
            <h3 class="tool-title">{{ tool.title }}</h3>
            <p class="tool-description">{{ tool.description }}</p>
            <div class="tool-button-container">
              <NButton
                class="tool-button"
                :class="`bg-gradient-to-r ${tool.color} text-white border-none`"
              >
                开始创作
              </NButton>
            </div>
            <div class="tool-decoration">
              <SvgIcon :name="tool.icon" size="120" class="decoration-icon" :class="[tool.textColor, tool.darkTextColor, 'opacity-10']" />
            </div>
          </div>
        </NGridItem>
      </NGrid>
    </div>
  </section>
</template>

<style scoped>
.creation-tools-section {
  padding: 5rem 0;
  background-color: var(--color-surface);
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.section-description {
  font-size: 1.125rem;
  color: var(--color-muted);
  max-width: 700px;
  margin: 0 auto;
}

.tool-card {
  position: relative;
  border-radius: 1rem;
  padding: 2rem;
  height: 100%;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.tool-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 1rem;
  background-color: white;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-md);
}

.dark .tool-icon {
  background-color: var(--color-surface);
}

.tool-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
}

.tool-description {
  font-size: 1rem;
  color: var(--color-muted);
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.tool-button-container {
  margin-top: auto;
}

.tool-button {
  font-weight: 600;
}

.tool-decoration {
  position: absolute;
  bottom: -20px;
  right: -20px;
  z-index: 0;
  opacity: 0.1;
}

.decoration-icon {
  opacity: 0.2;
}

/* 暗色模式适配 */
.dark .tool-card {
  background-color: var(--color-surface);
}
</style>
