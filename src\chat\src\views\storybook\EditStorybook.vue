<template>
  <div class="edit-storybook">
    <div class="page-header">
      <h1>编辑绘本</h1>
      <p>编辑您的绘本作品</p>
    </div>

    <div v-if="loading" class="loading-container">
      <NSpin size="large" />
      <p>加载中...</p>
    </div>

    <div v-else class="page-content">
      <NCard>
        <div class="edit-form">
          <NForm ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="80">
            <NFormItem label="标题" path="title">
              <NInput v-model:value="formData.title" placeholder="请输入绘本标题" />
            </NFormItem>

            <NFormItem label="描述" path="description">
              <NInput
                v-model:value="formData.description"
                type="textarea"
                placeholder="请输入绘本描述"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </NFormItem>

            <NFormItem label="封面图">
              <div v-if="formData.coverImg" class="cover-preview">
                <img :src="formData.coverImg" alt="封面预览" />
                <NButton quaternary circle class="remove-btn" @click="removeCover">
                  <SvgIcon name="ri:close-circle-fill" size="20" />
                </NButton>
              </div>

              <NUpload
                v-else
                action="#"
                :default-upload="false"
                :max="1"
                :custom-request="handleUpload"
                list-type="image-card"
              >
                点击上传封面图
              </NUpload>
            </NFormItem>

            <NFormItem>
              <NButton type="primary" @click="handleSubmit">保存修改</NButton>
              <NButton class="ml-4" @click="goBack">返回</NButton>
            </NFormItem>
          </NForm>
        </div>
      </NCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NCard, NForm, NFormItem, NInput, NButton, NUpload, NSpin } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { useWorksStore } from '@/store/modules/works';

const router = useRouter();
const route = useRoute();
const worksStore = useWorksStore();

// 状态
const loading = ref(true);
const formRef = ref(null);
const formData = ref({
  title: '',
  description: '',
  coverImg: ''
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入绘本标题', trigger: 'blur' },
    { max: 50, message: '标题不能超过50个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
};

// 获取绘本详情
const fetchStorybook = async () => {
  const id = route.params.id;
  if (!id) {
    window.$message?.error('绘本ID不存在');
    router.push('/storybook');
    return;
  }

  try {
    // 调用API获取绘本详情
    const storybook = await worksStore.getStorybookDetail(Number(id));

    formData.value = {
      title: storybook.title,
      description: storybook.description,
      coverImg: storybook.coverImg
    };
  } catch (error) {
    console.error('获取绘本详情失败:', error);
    window.$message?.error('获取绘本详情失败');
    router.push('/storybook');
  } finally {
    loading.value = false;
  }
};

// 处理上传
const handleUpload = ({ file }) => {
  // 这里应该实现实际的上传逻辑
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.value.coverImg = e.target.result;
  };
  reader.readAsDataURL(file.file);

  return {
    abort: () => {}
  };
};

// 移除封面
const removeCover = () => {
  formData.value.coverImg = '';
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    try {
      window.$message?.info('正在保存修改...');

      // 更新绘本
      const id = Number(route.params.id);
      await worksStore.updateStorybook(id, {
        title: formData.value.title,
        description: formData.value.description,
        coverImg: formData.value.coverImg
      });

      window.$message?.success('绘本修改成功');
      router.push('/storybook');
    } catch (error) {
      console.error('更新绘本失败:', error);
      window.$message?.error('更新绘本失败');
    }
  });
};

// 返回
const goBack = () => {
  router.push('/storybook');
};

// 组件挂载时获取绘本详情
onMounted(() => {
  fetchStorybook();
});
</script>

<style scoped>
.edit-storybook {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
}

.dark .page-header p {
  color: #aaa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
}

.edit-form {
  max-width: 600px;
}

.cover-preview {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
}

.ml-4 {
  margin-left: 16px;
}
</style>
