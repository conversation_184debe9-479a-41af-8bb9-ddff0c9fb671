<template>
  <div class="story-type-selector">
    <h3 class="step-title">选择你想创作的故事类型</h3>
    <p class="step-description">选择一个故事模板，开始你的创作之旅！</p>

    <div class="story-templates">
      <div
        v-for="template in storyTemplates"
        :key="template.id"
        class="template-card"
        @click="selectTemplate(template)"
      >
        <div class="template-icon">{{ template.icon }}</div>
        <div class="template-title">{{ template.title }}</div>
        <div class="template-desc">{{ template.description }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  projectData: Object
});

const emit = defineEmits(['select-template']);

const storyTemplates = ref([
  {
    id: 'adventure',
    title: '冒险故事',
    icon: '🏝️',
    description: '主角踏上冒险之旅，克服困难',
    structure: {
      theme: '冒险与发现',
      ageGroup: '6-8',
      mainIdea: '勇敢面对挑战，探索未知世界',
      beginning: '主角发现一个神秘地图/物品',
      middle: '主角踏上冒险之旅，遇到困难和帮手',
      ending: '主角克服困难，获得宝藏/知识/友谊'
    }
  },
  {
    id: 'friendship',
    title: '友谊故事',
    icon: '🤝',
    description: '关于交朋友和团队合作的故事',
    structure: {
      theme: '友谊与合作',
      ageGroup: '6-8',
      mainIdea: '真正的友谊需要理解和包容',
      beginning: '主角遇到新朋友/与朋友发生误会',
      middle: '主角学习如何与朋友相处/解决误会',
      ending: '主角和朋友建立更深厚的友谊'
    }
  },
  {
    id: 'learning',
    title: '成长故事',
    icon: '🌱',
    description: '主角学习新技能或重要道理',
    structure: {
      theme: '学习与成长',
      ageGroup: '6-8',
      mainIdea: '通过尝试和坚持，我们能学会新事物',
      beginning: '主角面临需要学习新技能的挑战',
      middle: '主角尝试学习但遇到困难，几乎要放弃',
      ending: '主角坚持下来，最终学会了新技能'
    }
  },
  {
    id: 'fantasy',
    title: '奇幻故事',
    icon: '🧙‍♂️',
    description: '充满魔法和奇妙生物的世界',
    structure: {
      theme: '奇幻与想象',
      ageGroup: '6-8',
      mainIdea: '想象力可以创造奇妙的世界',
      beginning: '主角发现魔法物品或进入奇幻世界',
      middle: '主角在奇幻世界冒险，学习魔法规则',
      ending: '主角用魔法解决问题，回到现实或留在奇幻世界'
    }
  },
  {
    id: 'animals',
    title: '动物故事',
    icon: '🐾',
    description: '以动物为主角的有趣故事',
    structure: {
      theme: '动物世界',
      ageGroup: '3-5',
      mainIdea: '动物也有自己的情感和冒险',
      beginning: '动物主角面临一个问题或挑战',
      middle: '动物主角与朋友一起寻找解决方法',
      ending: '动物主角解决问题，学到关于友谊或自然的道理'
    }
  },
  {
    id: 'family',
    title: '家庭故事',
    icon: '👨‍👩‍👧‍👦',
    description: '关于家人之间爱与理解的故事',
    structure: {
      theme: '家庭与爱',
      ageGroup: '3-5',
      mainIdea: '家人之间的爱和支持很重要',
      beginning: '主角在家庭中遇到问题或误会',
      middle: '主角尝试解决问题，理解家人的感受',
      ending: '主角和家人增进理解，解决问题'
    }
  }
]);

const selectTemplate = (template) => {
  emit('select-template', template);
};
</script>

<style scoped>
.story-type-selector {
  padding: 0.5rem 0;
  position: relative;
}

.story-type-selector::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 5px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  border-radius: 5px;
  opacity: 0.3;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.25rem;
  text-align: center;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.dark .step-title {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 1rem;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dark .step-description {
  color: #94a3b8;
}

.story-templates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  perspective: 1000px;
}

.template-card {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 2px solid transparent;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .template-card {
  background-color: #1e293b;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.05);
}

.template-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  border-color: #3b82f6;
  box-shadow: 0 15px 30px rgba(59, 130, 246, 0.2), 0 10px 10px rgba(59, 130, 246, 0.1);
}

.template-card:hover::before {
  opacity: 1;
}

.dark .template-card:hover {
  border-color: #60a5fa;
  box-shadow: 0 15px 30px rgba(96, 165, 250, 0.2), 0 10px 10px rgba(96, 165, 250, 0.1);
}

.template-icon {
  font-size: 3rem;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.template-card:hover .template-icon {
  transform: scale(1.1) translateZ(10px);
  animation-play-state: paused;
}

.template-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.dark .template-title {
  color: #e2e8f0;
}

.template-card:hover .template-title {
  color: #3b82f6;
  transform: translateZ(10px);
}

.dark .template-card:hover .template-title {
  color: #60a5fa;
}

.template-desc {
  font-size: 0.9rem;
  color: #64748b;
  text-align: center;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.dark .template-desc {
  color: #94a3b8;
}

.template-card:hover .template-desc {
  transform: translateZ(10px);
}

@media (max-width: 768px) {
  .story-templates {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .step-title {
    font-size: 1.75rem;
  }

  .template-card {
    padding: 1.5rem;
  }

  .template-icon {
    font-size: 3.5rem;
  }
}
</style>
