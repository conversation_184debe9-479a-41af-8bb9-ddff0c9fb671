const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:9520/api';
let token = '';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Website-Domain': 'http://localhost:9002'
  }
});

// 添加请求拦截器
api.interceptors.request.use(function (config) {
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  console.log(`请求: ${config.method.toUpperCase()} ${config.url}`);
  return config;
}, function (error) {
  console.error('请求错误:', error);
  return Promise.reject(error);
});

// 添加响应拦截器
api.interceptors.response.use(function (response) {
  console.log(`响应状态: ${response.status}`);
  return response;
}, function (error) {
  console.error('响应错误:', error.message);
  if (error.response) {
    console.error('错误状态:', error.response.status);
    console.error('错误数据:', error.response.data);
  }
  return Promise.reject(error);
});

// 登录
async function login() {
  try {
    console.log('尝试登录...');
    const response = await api.post('/auth/login', {
      username: 'super',
      password: '123456'
    });

    // 检查响应格式
    console.log('登录响应:', response.data);

    if (response.data && response.data.success) {
      // 直接使用data字段作为token
      if (typeof response.data.data === 'string') {
        token = response.data.data;
      }
      // 或者使用data.token字段
      else if (response.data.data && response.data.data.token) {
        token = response.data.data.token;
      }
      // 如果都不存在，登录失败
      else {
        console.error('登录成功但未找到token');
        return false;
      }

      console.log('登录成功，获取到Token');
      return true;
    } else {
      console.error('登录失败:', response.data);
      return false;
    }
  } catch (error) {
    console.error('登录请求失败:', error.message);
    return false;
  }
}

// 测试获取角色模板
async function testGetCharacterTemplates() {
  try {
    console.log('\n测试获取角色模板...');
    const response = await api.get('/storybook/character/templates');

    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('获取角色模板失败');
    return null;
  }
}

// 测试获取用户角色库
async function testGetUserCharacters() {
  try {
    console.log('\n测试获取用户角色库...');
    const response = await api.get('/storybook/character/mine');

    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('获取用户角色库失败');
    return null;
  }
}

// 测试创建角色
async function testCreateCharacter() {
  try {
    console.log('\n测试创建角色...');
    const response = await api.post('/storybook/character', {
      name: '测试角色',
      characterType: 'boy',
      appearance: '一个可爱的小男孩',
      personalityTraits: { traits: ['活泼', '开朗', '勇敢'] },
      imageUrl: 'https://example.com/image.jpg'
    });

    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('创建角色失败');
    return null;
  }
}

// 测试更新角色
async function testUpdateCharacter(characterId) {
  try {
    console.log(`\n测试更新角色 ID:${characterId}...`);
    const response = await api.put(`/storybook/character/${characterId}`, {
      name: '更新后的角色名称',
      appearance: '更新后的外观描述',
      isFavorite: 1
    });

    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error(`更新角色失败: ${error.message}`);
    return null;
  }
}

// 测试删除角色
async function testDeleteCharacter(characterId) {
  try {
    console.log(`\n测试删除角色 ID:${characterId}...`);
    const response = await api.delete(`/storybook/character/${characterId}`);

    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.error(`删除角色失败: ${error.message}`);
    return false;
  }
}

// 运行所有测试
async function runTests() {
  console.log('开始测试角色库API...');

  // 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.error('登录失败，无法继续测试');
    return;
  }

  // 测试获取角色模板
  await testGetCharacterTemplates();

  // 测试获取用户角色库
  const characters = await testGetUserCharacters();

  // 测试创建角色
  const createdCharacter = await testCreateCharacter();

  // 如果创建成功，测试更新和删除
  if (createdCharacter && createdCharacter.data && createdCharacter.data.id) {
    const characterId = createdCharacter.data.id;

    // 测试更新角色
    await testUpdateCharacter(characterId);

    // 再次获取用户角色库，确认更新成功
    await testGetUserCharacters();

    // 测试删除角色
    await testDeleteCharacter(characterId);

    // 再次获取用户角色库，确认删除成功
    await testGetUserCharacters();
  }

  console.log('\n角色库API测试完成');
}

// 执行测试
runTests();
