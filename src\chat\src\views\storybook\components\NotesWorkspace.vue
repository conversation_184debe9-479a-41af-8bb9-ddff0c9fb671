<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { NCard, NInput, NButton, NSpace, NTag, NPopover, NTabs, NTabPane } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 笔记状态
const notes = ref(props.projectData.notes || '');
const activeTab = ref('notes');
const inspirations = ref<string[]>(props.projectData.inspirations || []);
const newInspiration = ref('');
const autoSaveTimeout = ref(null);

// 笔记类型
const noteTypes = [
  { id: 'notes', label: '笔记本', icon: 'ri:sticky-note-line' },
  { id: 'inspirations', label: '灵感收集', icon: 'ri:lightbulb-line' }
];

// 自动保存笔记
const autoSaveNotes = () => {
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }

  autoSaveTimeout.value = setTimeout(() => {
    props.projectData.notes = notes.value;
    props.projectData.inspirations = inspirations.value;
    window.$message?.success('笔记已自动保存');
  }, 2000);
};

// 添加灵感
const addInspiration = () => {
  if (newInspiration.value.trim()) {
    inspirations.value.push(newInspiration.value.trim());
    newInspiration.value = '';
    autoSaveNotes();
  }
};

// 删除灵感
const removeInspiration = (index: number) => {
  inspirations.value.splice(index, 1);
  autoSaveNotes();
};

// 监听笔记变化
watch(notes, () => {
  autoSaveNotes();
});

// 手动保存笔记
const saveNotes = () => {
  props.projectData.notes = notes.value;
  props.projectData.inspirations = inspirations.value;
  window.$message?.success('笔记已保存');
};

// 清空笔记
const clearNotes = () => {
  if (confirm('确定要清空笔记吗？此操作不可撤销。')) {
    notes.value = '';
    props.projectData.notes = '';
    window.$message?.info('笔记已清空');
  }
};

// 清空灵感
const clearInspirations = () => {
  if (confirm('确定要清空所有灵感吗？此操作不可撤销。')) {
    inspirations.value = [];
    props.projectData.inspirations = [];
    window.$message?.info('灵感已清空');
  }
};

// 生成笔记摘要
const generateSummary = () => {
  if (!notes.value.trim()) {
    window.$message?.warning('请先添加一些笔记内容');
    return;
  }

  window.$message?.info('正在生成笔记摘要...');
  // 这里可以添加AI生成摘要的逻辑
  setTimeout(() => {
    window.$message?.success('笔记摘要已生成');
  }, 1500);
};
</script>

<template>
  <div class="notes-workspace">
    <div class="workspace-header">
      <h2 class="section-title">创作笔记本</h2>
      <p class="section-description">记录你的创作灵感和想法</p>

      <!-- 笔记类型切换 -->
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane
          v-for="type in noteTypes"
          :key="type.id"
          :name="type.id"
        >
          <template #tab>
            <div class="tab-label">
              <SvgIcon :name="type.icon" size="16" class="mr-1" />
              <span>{{ type.label }}</span>
            </div>
          </template>
        </NTabPane>
      </NTabs>
    </div>

    <div class="workspace-content">
      <!-- 笔记本 -->
      <div v-if="activeTab === 'notes'" class="notes-container">
        <div class="notes-paper">
          <div class="paper-header">
            <div class="paper-title">创作笔记</div>
            <div class="paper-actions">
              <NButton size="small" @click="saveNotes" class="action-button">
                <SvgIcon name="ri:save-line" size="14" class="mr-1" />
                保存
              </NButton>
              <NButton size="small" @click="clearNotes" class="action-button">
                <SvgIcon name="ri:delete-bin-line" size="14" class="mr-1" />
                清空
              </NButton>
              <NButton size="small" @click="generateSummary" class="action-button">
                <SvgIcon name="ri:magic-line" size="14" class="mr-1" />
                生成摘要
              </NButton>
            </div>
          </div>
          <textarea
            v-model="notes"
            class="paper-content"
            placeholder="在这里记录你的创作笔记、想法和灵感..."
          ></textarea>
        </div>

        <div class="notes-tips">
          <h3 class="tips-title">创作提示</h3>
          <ul class="tips-list">
            <li>记录你对故事的初步构思和想法</li>
            <li>写下你想要表达的主题和情感</li>
            <li>记录你喜欢的其他绘本中的元素</li>
            <li>描述你想要创造的角色特点</li>
            <li>记录适合你故事的场景和环境</li>
          </ul>
        </div>
      </div>

      <!-- 灵感收集 -->
      <div v-else-if="activeTab === 'inspirations'" class="inspirations-container">
        <div class="add-inspiration">
          <div class="inspiration-header">
            <NInput
              v-model:value="newInspiration"
              type="text"
              placeholder="添加一个新的灵感..."
              @keyup.enter="addInspiration"
            >
              <template #suffix>
                <NButton type="primary" ghost @click="addInspiration">
                  <SvgIcon name="ri:add-line" size="16" />
                </NButton>
              </template>
            </NInput>
            <NButton size="small" @click="clearInspirations" class="clear-button">
              <SvgIcon name="ri:delete-bin-line" size="14" class="mr-1" />
              清空灵感
            </NButton>
          </div>
        </div>

        <div class="inspirations-board">
          <div v-if="inspirations.length === 0" class="empty-inspirations">
            <SvgIcon name="ri:lightbulb-line" size="48" class="empty-icon" />
            <p>还没有添加灵感，开始记录你的创意吧！</p>
          </div>

          <div v-else class="inspiration-notes">
            <div
              v-for="(inspiration, index) in inspirations"
              :key="index"
              class="inspiration-note"
            >
              <div class="note-content">{{ inspiration }}</div>
              <NButton
                size="tiny"
                quaternary
                class="remove-button"
                @click="removeInspiration(index)"
              >
                <SvgIcon name="ri:close-line" size="12" />
              </NButton>
            </div>
          </div>
        </div>

        <div class="inspiration-prompts">
          <h3 class="prompts-title">灵感提示</h3>
          <div class="prompts-list">
            <NTag
              v-for="(prompt, index) in [
                '如果主角是一只会飞的猫...',
                '故事发生在一个永远不会下雨的世界...',
                '主角发现了一个神奇的门...',
                '一个失去颜色的王国...',
                '一个能听懂动物语言的孩子...',
                '一棵会走路的树...',
                '一个永远向前走的小女孩...',
                '一个装满星星的口袋...'
              ]"
              :key="index"
              class="prompt-tag"
              type="info"
              size="medium"
              @click="newInspiration = prompt"
            >
              {{ prompt }}
            </NTag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.notes-workspace {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  overflow-y: auto;
}

.workspace-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.dark .section-title {
  color: #e2e8f0;
}

.section-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.dark .section-description {
  color: #94a3b8;
}

.tab-label {
  display: flex;
  align-items: center;
}

.workspace-content {
  flex: 1;
  overflow-y: auto;
}

/* 笔记本样式 */
.notes-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.notes-paper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff9c4;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  min-height: 400px;
}

.dark .notes-paper {
  background-color: #2d3748;
}

.paper-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #fff176;
  border-bottom: 1px solid #ffd54f;
}

.dark .paper-header {
  background-color: #1a202c;
  border-bottom: 1px solid #2d3748;
}

.paper-title {
  font-weight: 600;
  color: #5d4037;
}

.dark .paper-title {
  color: #e2e8f0;
}

.paper-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background-color: rgba(255, 255, 255, 0.7);
}

.dark .action-button {
  background-color: rgba(45, 55, 72, 0.7);
}

.paper-content {
  flex: 1;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  background-color: transparent;
  border: none;
  resize: none;
  min-height: 350px;
  color: #5d4037;
  background-image: linear-gradient(#ffefd5 1px, transparent 1px);
  background-size: 100% 1.6rem;
  line-height: 1.6rem;
}

.dark .paper-content {
  color: #e2e8f0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

.paper-content:focus {
  outline: none;
}

.notes-tips {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .notes-tips {
  background-color: #1e293b;
}

.tips-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .tips-title {
  color: #e2e8f0;
}

.tips-list {
  padding-left: 1.5rem;
  font-size: 0.875rem;
  color: #475569;
}

.dark .tips-list {
  color: #cbd5e1;
}

.tips-list li {
  margin-bottom: 0.5rem;
}

/* 灵感收集样式 */
.inspirations-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.add-inspiration {
  margin-bottom: 1rem;
}

.inspiration-header {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.inspiration-header .n-input {
  flex: 1;
}

.clear-button {
  white-space: nowrap;
  background-color: rgba(255, 255, 255, 0.7);
}

.dark .clear-button {
  background-color: rgba(45, 55, 72, 0.7);
}

.inspirations-board {
  flex: 1;
  min-height: 300px;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.dark .inspirations-board {
  background-color: #1e293b;
}

.empty-inspirations {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.inspiration-notes {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.inspiration-note {
  position: relative;
  width: 200px;
  min-height: 100px;
  padding: 1rem;
  background-color: #fbec8f;
  border-radius: 0.25rem;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  transform: rotate(var(--rotation));
  --rotation: calc(var(--index) * 2deg - 3deg);
}

.dark .inspiration-note {
  background-color: #4a5568;
}

.note-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #5d4037;
}

.dark .note-content {
  color: #e2e8f0;
}

.remove-button {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.inspiration-note:hover .remove-button {
  opacity: 1;
}

.inspiration-prompts {
  margin-top: 1.5rem;
}

.prompts-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .prompts-title {
  color: #e2e8f0;
}

.prompts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.prompt-tag {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.prompt-tag:hover {
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .notes-workspace {
    padding: 1rem;
  }

  .notes-container {
    gap: 1rem;
  }

  .paper-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .paper-actions {
    width: 100%;
    justify-content: space-between;
  }

  .inspiration-note {
    width: 100%;
  }
}
</style>
