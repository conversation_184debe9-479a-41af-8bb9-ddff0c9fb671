<template>
  <div class="character-builder">



    <div class="builder-layout">
      <!-- 左侧：角色预览区 -->
      <div class="character-preview-panel">
        <div class="preview-header">
          <h4 class="preview-title">角色预览</h4>
          <div class="preview-actions">
            <button class="library-button small" @click="openCharacterLibrary" title="打开角色库">
              <span class="library-icon">📚</span>
              <span class="library-text">角色库</span>
            </button>
            <button class="add-character-button small" @click="addNewCharacter" title="添加新角色">
              <span class="add-icon">➕</span>
              <span class="add-text">添加新角色</span>
            </button>
          </div>
        </div>

        <!-- 角色选择器 -->
        <div class="character-selector" v-if="props.characters.length > 0">
          <div
            v-for="(character, index) in props.characters"
            :key="character.id || index"
            class="character-tab"
            :class="{ 'active': props.activeCharacterIndex === index }"
            @click="selectCharacter(index)"
          >
            <div class="character-tab-icon">
              {{ getCharacterEmoji(character) }}
            </div>
            <div class="character-tab-name">
              {{ character.name || `角色 ${index + 1}` }}
            </div>
            <button
              v-if="props.characters.length > 1"
              class="delete-character-btn"
              @click.stop="deleteCharacter(index)"
              title="删除角色"
            >
              ✖
            </button>
          </div>
        </div>
        <div class="preview-content">


          <div class="preview-image-container">
            <div class="preview-image-header">
              <div class="image-title">角色形象</div>
              <div class="image-hint" v-if="!characterImageUrl">点击下方"魔法生成角色"按钮创建形象</div>
            </div>
            <div class="preview-image">
              <!-- 使用计算属性获取角色图像URL -->
              <img v-if="characterImageUrl" :src="characterImageUrl" :alt="localCharacter.name" />
              <div v-else class="image-placeholder">
                <div class="placeholder-content">
                  <span class="emoji-icon">{{ getCharacterEmoji() }}</span>
                </div>
              </div>
              <!-- 生成中遮罩 -->
              <div v-if="isGeneratingImage" class="generating-overlay">
                <div class="generating-spinner">
                  <div class="spinner"></div>
                </div>
                <div class="generating-text">AI正在绘制角色...</div>
                <!-- 图像生成进度显示 -->
                <ImageGenerationProgress
                  :status="generationStatus"
                  :progress="generationProgress"
                  class="mt-4"
                />
              </div>

              <!-- 图像编辑按钮 -->
              <div v-if="characterImageUrl && !isGeneratingImage" class="image-actions">
                <button class="edit-image-btn" @click="showImageEditor = true" title="编辑角色图像">
                  <span class="edit-icon">✏️</span>
                  <span class="edit-label">魔法修改</span>
                </button>
                <button class="regenerate-image-btn" @click="regenerateCharacterImage" title="重新生成角色图像">
                  <span class="regenerate-icon">🔄</span>
                  <span class="regenerate-label">重新生成</span>
                </button>
              </div>

              <!-- 编辑提示 -->
              <div v-if="characterImageUrl && !isGeneratingImage && showEditHint" class="edit-hint">
                <div class="hint-content">
                  <span class="hint-icon">✨</span>
                  <p>太棒了！你的角色已经创建好了！</p>
                  <p class="hint-subtitle">你可以用魔法修改按钮来改变角色的外观</p>
                  <button class="hint-close-btn" @click="closeEditHint">我知道了！</button>
                </div>
              </div>
            </div>

            <!-- 生成角色按钮 -->
            <div class="generate-btn-container">
              <button class="generate-btn" @click="generateCharacter" :disabled="isGeneratingImage">
                <span class="generate-icon">{{ isGeneratingImage ? '⏳' : '✨' }}</span>
                <span class="generate-text">{{ isGeneratingImage ? '生成中...' : '魔法生成角色' }}</span>
              </button>
            </div>

            <!-- 图像编辑器组件 -->
            <ImageEditor
              v-model:visible="showImageEditor"
              :imageUrl="characterImageUrl"
              :onConversation="onConversation"
              :onSuccess="handleEditSuccess"
              :onCancel="handleEditCancel"
              @edit-complete="handleEditComplete"
            />

            <!-- 角色库组件 -->
            <CharacterLibrary
              :visible="showCharacterLibrary"
              @close="showCharacterLibrary = false"
              @select-character="importCharacterFromLibrary"
            />
          </div>

          <div class="preview-info" v-if="localCharacter.name">
            <div class="preview-info-header">
              <div class="character-name-container">
                <h3 class="preview-name">{{ localCharacter.name }}</h3>
              </div>
            </div>

            <div class="preview-traits">
              <div class="traits-section" v-if="localCharacter.personalityTraits.length > 0">
                <div class="traits-title">
                  <span class="traits-icon">✨</span>
                  <span>性格特点</span>
                </div>
                <div class="traits-badges">
                  <span v-for="trait in localCharacter.personalityTraits" :key="trait" class="trait-badge personality-badge">
                    {{ getTraitIcon(trait) }} {{ getTraitLabel(trait) }}
                  </span>
                </div>
              </div>

              <div class="traits-section" v-if="localCharacter.appearanceTraits.length > 0">
                <div class="traits-title">
                  <span class="traits-icon">👁️</span>
                  <span>外表特点</span>
                </div>
                <div class="traits-badges">
                  <span v-for="trait in localCharacter.appearanceTraits" :key="trait" class="trait-badge appearance-badge">
                    {{ getTraitIcon(trait) }} {{ getTraitLabel(trait) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="preview-details" v-if="localCharacter.appearance">
              <div class="preview-detail-item">
                <span class="detail-icon">👁️</span>
                <span class="detail-label">外表描述：</span>
                <span class="detail-value">{{ localCharacter.appearance }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：角色信息编辑区 -->
      <div class="character-options-panel">
        <div class="options-header">
          <div class="options-header-content">
            <div class="options-icon">✏️</div>
            <h4 class="options-title">角色信息</h4>
          </div>
          <div class="options-subtitle">填写角色的基本信息和特点</div>
        </div>
        <div class="options-content">
          <div class="tab-content">
            <!-- 角色名称 -->
            <div class="option-group">
              <h4 class="option-title">角色叫什么名字？</h4>
              <input
                v-model="localCharacter.name"
                placeholder="输入角色名字"
                class="character-name-input"
              />
            </div>

            <!-- 外表描述 -->
            <div class="option-group">
              <div class="option-title-container">
                <h4 class="option-title">外表描述</h4>
                <span class="option-hint">此描述将直接用于AI生成角色图像</span>
              </div>
              <textarea
                v-model="localCharacter.appearance"
                placeholder="详细描述角色的外表、服装、颜色等，此描述将直接用于AI生成图像"
                class="character-textarea"
                ref="appearanceTextarea"
              ></textarea>

              <!-- 特点选择面板 -->
              <div class="appearance-traits-fixed">
                <div class="traits-header">
                  <h4 class="traits-subtitle">选择角色特点<span class="hint">(点击添加到描述)</span></h4>
                </div>

                <!-- 分类标签页 -->
                <div class="traits-tabs">
                  <button
                    class="trait-tab-btn trait-character"
                    :class="{ active: activeTraitCategory === 'character' }"
                    @click="activeTraitCategory = 'character'"
                  >
                    👤 角色类型
                  </button>
                  <button
                    class="trait-tab-btn trait-personality"
                    :class="{ active: activeTraitCategory === 'personality' }"
                    @click="activeTraitCategory = 'personality'"
                  >
                    ✨ 性格特点
                  </button>
                  <button
                    v-for="category in appearanceCategories"
                    :key="category.id"
                    class="trait-tab-btn"
                    :class="{
                      active: activeTraitCategory === category.id,
                      [`trait-${category.id}`]: true
                    }"
                    @click="activeTraitCategory = category.id"
                  >
                    {{ category.icon }} {{ category.label }}
                  </button>
                </div>

                <!-- 分类内容 -->
                <div class="traits-content">
                  <!-- 角色类型 -->
                  <div v-show="activeTraitCategory === 'character'" class="trait-category-content">
                    <div class="option-buttons">
                      <div
                        v-for="type in characterTypes"
                        :key="type.value"
                        class="trait-btn-container"
                      >
                        <button
                          class="option-btn type-btn trait-btn trait-character"
                          :class="{
                            active: localCharacter.characterType === type.value,
                            'custom-trait': type.isCustom
                          }"
                          @click="localCharacter.characterType = type.value"
                        >
                          {{ type.icon }} {{ type.label }}
                        </button>
                        <button
                          v-if="type.isCustom"
                          class="delete-trait-btn"
                          @click="deleteCustomCharacterType(type.value)"
                          title="删除自定义角色类型"
                        >
                          ×
                        </button>
                      </div>
                      <!-- 自定义角色类型 -->
                      <div class="custom-option-container">
                        <input
                          v-model="customCharacterType"
                          placeholder="自定义角色类型"
                          class="custom-option-input"
                          @keyup.enter="addCustomCharacterType"
                        />
                        <button class="custom-option-btn" @click="addCustomCharacterType">添加</button>
                      </div>
                    </div>
                  </div>

                  <!-- 性格特点 -->
                  <div v-show="activeTraitCategory === 'personality'" class="trait-category-content">
                    <div class="option-buttons">
                      <div
                        v-for="trait in personalityTraits"
                        :key="trait.value"
                        class="trait-btn-container"
                      >
                        <button
                          class="option-btn trait-btn trait-personality"
                          :class="{
                            active: localCharacter.personalityTraits.includes(trait.value),
                            'custom-trait': trait.isCustom
                          }"
                          @click="togglePersonalityTrait(trait.value)"
                        >
                          {{ trait.icon }} {{ trait.label }}
                        </button>
                        <button
                          v-if="trait.isCustom"
                          class="delete-trait-btn"
                          @click="deleteCustomTrait(trait.value, 'personality')"
                          title="删除自定义特点"
                        >
                          ×
                        </button>
                      </div>
                      <!-- 自定义性格特点 -->
                      <div class="custom-option-container">
                        <input
                          v-model="customPersonalityTrait"
                          placeholder="自定义性格特点"
                          class="custom-option-input"
                          @keyup.enter="addCustomPersonalityTrait"
                        />
                        <button class="custom-option-btn" @click="addCustomPersonalityTrait">添加</button>
                      </div>
                    </div>
                  </div>

                  <!-- 外表特点分类 -->
                  <template v-for="category in appearanceCategories" :key="category.id">
                    <div v-show="activeTraitCategory === category.id" class="trait-category-content">
                      <div class="option-buttons">
                        <div
                          v-for="trait in getTraitsByCategory(category.id)"
                          :key="trait.value"
                          class="trait-btn-container"
                        >
                          <button
                            class="option-btn trait-btn"
                            :class="{
                              active: localCharacter.appearanceTraits.includes(trait.value),
                              [`trait-${category.id}`]: true,
                              'custom-trait': trait.isCustom
                            }"
                            @click="selectAppearanceTrait(trait.value)"
                          >
                            {{ trait.icon }} {{ trait.label }}
                          </button>
                          <button
                            v-if="trait.isCustom"
                            class="delete-trait-btn"
                            @click="deleteCustomTrait(trait.value, 'appearance')"
                            title="删除自定义特点"
                          >
                            ×
                          </button>
                        </div>
                        <!-- 自定义特点输入 -->
                        <div class="custom-option-container">
                          <input
                            v-model="customAppearanceTraits[category.id]"
                            :placeholder="`自定义${category.label}`"
                            class="custom-option-input"
                            @keyup.enter="addCustomAppearanceTrait(category.id)"
                          />
                          <button class="custom-option-btn" @click="addCustomAppearanceTrait(category.id)">添加</button>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, inject, computed } from 'vue';
import ImageGenerationProgress from './ImageGenerationProgress.vue';
import ImageEditor from './ImageEditor.vue';
import CharacterLibrary from './CharacterLibrary.vue';
import { useChatStore } from '@/store';
import { addCharacterToLibrary, synchronizeCharacterLibrary } from '@/utils/characterLibrary';
import StorybookApi from '@/api/modules/storybook';
import { saveStorybookProject } from '@/utils/storybookStorage';

// 注入onConversation函数，用于与聊天界面一致的图像生成
const onConversation = inject('onConversation');
const chatStore = useChatStore();

const props = defineProps({
  character: Object,
  characters: {
    type: Array,
    default: () => []
  },
  activeCharacterIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:character', 'add-character', 'select-character', 'delete-character']);

// 自定义输入字段
const customCharacterType = ref('');
const customPersonalityTrait = ref('');
// 为每个外表特点类别创建独立的输入框
const customAppearanceTraits = reactive({
  basic: '',
  face: '',
  hair: '',
  clothing: '',
  accessories: '',
  pose: '',
  style: ''
});

// 控制特点面板的分类标签
const appearanceTextarea = ref(null);
const activeTraitCategory = ref('character'); // 默认显示角色类型标签页

// 打开角色库
const openCharacterLibrary = () => {
  showCharacterLibrary.value = true;
};

// 选择角色
const selectCharacter = (index) => {
  // 通知父组件选择角色
  emit('select-character', index);
};

// 添加新角色
const addNewCharacter = () => {
  // 通知父组件添加新角色
  emit('add-character');
};

// 删除角色
const deleteCharacter = (index) => {
  // 通知父组件删除角色
  emit('delete-character', index);
};

// 首先确定最终要使用的图像URL
const getImageUrl = () => {
  return props.character?.imageUrl || props.character?.image || props.character?._image || '';
};

// 创建本地副本以便编辑，确保所有数组属性都已初始化
const localCharacter = reactive({
  // 基本属性
  id: props.character?.id || Date.now() + Math.floor(Math.random() * 1000),
  name: props.character?.name || '',
  characterType: props.character?.characterType || '',

  // 性格特点
  personalityTraits: props.character?.personalityTraits || [],

  // 外表特点
  appearance: props.character?.appearance || '',
  appearanceTraits: props.character?.appearanceTraits || [],

  // 视觉元素 - 统一设置所有图像相关字段
  imageUrl: getImageUrl(),
  _image: getImageUrl(),
  image: getImageUrl(),

  // 其他属性
  tags: props.character?.tags || [],
  isFavorite: props.character?.isFavorite || 0,
  isTemplate: props.character?.isTemplate || 0,

  // 时间戳
  createdAt: props.character?.createdAt || new Date().toISOString(),
  updatedAt: props.character?.updatedAt || new Date().toISOString(),

  // 兼容旧版本
  traits: props.character?.traits || []
});

// 当本地数据变化时，通知父组件并保存到localStorage
watch(localCharacter, () => {
  // 确保所有图像字段都一致
  const imageUrl = localCharacter.imageUrl || localCharacter._image || localCharacter.image || '';
  if (imageUrl) {
    localCharacter.imageUrl = imageUrl;
    localCharacter._image = imageUrl;
    localCharacter.image = imageUrl;
  }

  // 通知父组件更新角色数据
  emit('update:character', {...localCharacter});

  // 通知父组件保存数据
  try {
    // 获取父组件的projectData
    const parentComponent = inject('projectData', null);
    if (parentComponent) {
      // 保存到localStorage
      saveStorybookProject(parentComponent);
      console.log('[AI绘本创作-角色创建] 角色数据已自动保存到localStorage');
    }
  } catch (error) {
    console.error('[AI绘本创作-角色创建] 自动保存角色数据失败:', error);
  }
}, { deep: true });

// 监听角色类型变化，更新外表描述
watch(() => localCharacter.characterType, () => {
  if (localCharacter.appearanceTraits.length > 0) {
    updateAppearanceDescription();
  }
});

const characterTypes = [
  { label: '小男孩', value: 'boy', icon: '👦' },
  { label: '小女孩', value: 'girl', icon: '👧' },
  { label: '小动物', value: 'animal', icon: '🐶' },
  { label: '魔法生物', value: 'magical', icon: '🧚' },
  { label: '机器人', value: 'robot', icon: '🤖' },
  { label: '外星人', value: 'alien', icon: '👽' },
  { label: '老爷爷', value: 'oldman', icon: '👴' },
  { label: '老奶奶', value: 'oldwoman', icon: '👵' },
  { label: '青少年', value: 'teen', icon: '🧑' },
  { label: '婴儿', value: 'baby', icon: '👶' },
  { label: '超级英雄', value: 'superhero', icon: '🦸' },
  { label: '公主', value: 'princess', icon: '👸' },
  { label: '王子', value: 'prince', icon: '🤴' },
  { label: '精灵', value: 'elf', icon: '🧝' },
  { label: '巫师', value: 'wizard', icon: '🧙' }
];

// 性格特点
const personalityTraits = [
  { label: '勇敢', value: 'brave', icon: '🦁', category: 'personality' },
  { label: '聪明', value: 'smart', icon: '🦉', category: 'personality' },
  { label: '友善', value: 'kind', icon: '🐻', category: 'personality' },
  { label: '好奇', value: 'curious', icon: '🐒', category: 'personality' },
  { label: '害羞', value: 'shy', icon: '🐰', category: 'personality' },
  { label: '调皮', value: 'mischievous', icon: '🐱', category: 'personality' },
  { label: '创意', value: 'creative', icon: '🎨', category: 'personality' },
  { label: '幽默', value: 'funny', icon: '🤡', category: 'personality' },
  { label: '固执', value: 'stubborn', icon: '🐂', category: 'personality' },
  { label: '乐观', value: 'optimistic', icon: '🌞', category: 'personality' },
  { label: '谨慎', value: 'cautious', icon: '🐢', category: 'personality' },
  { label: '热心', value: 'helpful', icon: '🤲', category: 'personality' },
  { label: '善良', value: 'goodhearted', icon: '💖', category: 'personality' },
  { label: '诚实', value: 'honest', icon: '🤝', category: 'personality' },
  { label: '坚持', value: 'persistent', icon: '🧗', category: 'personality' }
];

// 外表特点分类
const appearanceCategories = [
  { id: 'basic', label: '基本外观', icon: '👤', description: '角色的基本外观特征，如年龄、体型等' },
  { id: 'face', label: '面部特征', icon: '😊', description: '角色的脸型、眼睛、鼻子、嘴巴等特征' },
  { id: 'hair', label: '发型发色', icon: '💇', description: '角色的发型、发色、发长等特征' },
  { id: 'clothing', label: '服装风格', icon: '👕', description: '角色的服装类型、颜色、风格等' },
  { id: 'accessories', label: '配饰装扮', icon: '👒', description: '角色的帽子、眼镜、首饰等配饰' },
  { id: 'pose', label: '姿势表情', icon: '🧍', description: '角色的表情、姿势、动作等' },
  { id: 'style', label: '画面风格', icon: '🎨', description: '绘画的艺术风格、光影效果等' }
];

// 外表特点
const appearanceTraits = [
  // 基本外观
  { label: '小孩', value: 'child', icon: '👶', category: 'basic' },
  { label: '少年', value: 'teenager', icon: '🧒', category: 'basic' },
  { label: '高个子', value: 'tall', icon: '📏', category: 'basic' },
  { label: '矮个子', value: 'short', icon: '🔍', category: 'basic' },
  { label: '苗条', value: 'slim', icon: '🪶', category: 'basic' },
  { label: '圆润', value: 'chubby', icon: '⭕', category: 'basic' },
  { label: '强壮', value: 'strong', icon: '💪', category: 'basic' },
  { label: '瘦小', value: 'skinny', icon: '🥢', category: 'basic' },
  { label: '健壮', value: 'muscular', icon: '🏋️', category: 'basic' },
  { label: '年轻', value: 'young', icon: '🌱', category: 'basic' },
  { label: '年长', value: 'old', icon: '🧓', category: 'basic' },
  { label: '优雅', value: 'elegant', icon: '💫', category: 'basic' },
  { label: '活泼', value: 'lively', icon: '⚡', category: 'basic' },
  { label: '敏捷', value: 'agile', icon: '🏃', category: 'basic' },
  { label: '笨拙', value: 'clumsy', icon: '🤪', category: 'basic' },

  // 面部特征
  { label: '圆脸', value: 'roundFace', icon: '⭕', category: 'face' },
  { label: '瓜子脸', value: 'ovalFace', icon: '🥚', category: 'face' },
  { label: '大眼睛', value: 'bigEyes', icon: '👁️', category: 'face' },
  { label: '小眼睛', value: 'smallEyes', icon: '👀', category: 'face' },
  { label: '大鼻子', value: 'bigNose', icon: '👃', category: 'face' },
  { label: '小鼻子', value: 'smallNose', icon: '👃', category: 'face' },
  { label: '厚嘴唇', value: 'thickLips', icon: '👄', category: 'face' },
  { label: '薄嘴唇', value: 'thinLips', icon: '👄', category: 'face' },
  { label: '雀斑', value: 'freckles', icon: '✨', category: 'face' },
  { label: '酒窝', value: 'dimples', icon: '😊', category: 'face' },
  { label: '浓眉', value: 'thickEyebrows', icon: '🧿', category: 'face' },
  { label: '长睫毛', value: 'longEyelashes', icon: '👁️‍🗨️', category: 'face' },
  { label: '高颧骨', value: 'highCheekbones', icon: '🦴', category: 'face' },
  { label: '尖下巴', value: 'pointedChin', icon: '🔽', category: 'face' },
  { label: '方下巴', value: 'squareChin', icon: '🟥', category: 'face' },

  // 发型发色
  { label: '短发', value: 'shortHair', icon: '✂️', category: 'hair' },
  { label: '长发', value: 'longHair', icon: '💁', category: 'hair' },
  { label: '卷发', value: 'curlyHair', icon: '➰', category: 'hair' },
  { label: '直发', value: 'straightHair', icon: '➖', category: 'hair' },
  { label: '马尾辫', value: 'ponytail', icon: '🐴', category: 'hair' },
  { label: '辫子', value: 'braids', icon: '🧶', category: 'hair' },
  { label: '黑发', value: 'blackHair', icon: '⚫', category: 'hair' },
  { label: '棕发', value: 'brownHair', icon: '🟤', category: 'hair' },
  { label: '金发', value: 'blondeHair', icon: '🟡', category: 'hair' },
  { label: '红发', value: 'redHair', icon: '🔴', category: 'hair' },
  { label: '彩色发', value: 'colorfulHair', icon: '🌈', category: 'hair' },
  { label: '蓬松发', value: 'fluffyHair', icon: '☁️', category: 'hair' },
  { label: '光头', value: 'bald', icon: '🥚', category: 'hair' },
  { label: '刘海', value: 'bangs', icon: '↘️', category: 'hair' },
  { label: '双马尾', value: 'twintails', icon: '🎀', category: 'hair' },

  // 服装风格
  { label: '休闲装', value: 'casualWear', icon: '👕', category: 'clothing' },
  { label: '正式装', value: 'formalWear', icon: '👔', category: 'clothing' },
  { label: '运动装', value: 'sportswear', icon: '🏃', category: 'clothing' },
  { label: '校服', value: 'schoolUniform', icon: '🏫', category: 'clothing' },
  { label: '连衣裙', value: 'dress', icon: '👗', category: 'clothing' },
  { label: '牛仔裤', value: 'jeans', icon: '👖', category: 'clothing' },
  { label: 'T恤', value: 'tshirt', icon: '👕', category: 'clothing' },
  { label: '毛衣', value: 'sweater', icon: '🧶', category: 'clothing' },
  { label: '外套', value: 'jacket', icon: '🧥', category: 'clothing' },
  { label: '红色服装', value: 'redClothes', icon: '🔴', category: 'clothing' },
  { label: '蓝色服装', value: 'blueClothes', icon: '🔵', category: 'clothing' },
  { label: '绿色服装', value: 'greenClothes', icon: '🟢', category: 'clothing' },
  { label: '黄色服装', value: 'yellowClothes', icon: '🟡', category: 'clothing' },
  { label: '睡衣', value: 'pajamas', icon: '🛌', category: 'clothing' },
  { label: '制服', value: 'uniform', icon: '👮', category: 'clothing' },

  // 配饰装扮
  { label: '戴眼镜', value: 'glasses', icon: '👓', category: 'accessories' },
  { label: '太阳镜', value: 'sunglasses', icon: '🕶️', category: 'accessories' },
  { label: '帽子', value: 'hat', icon: '🧢', category: 'accessories' },
  { label: '围巾', value: 'scarf', icon: '🧣', category: 'accessories' },
  { label: '手套', value: 'gloves', icon: '🧤', category: 'accessories' },
  { label: '背包', value: 'backpack', icon: '🎒', category: 'accessories' },
  { label: '项链', value: 'necklace', icon: '📿', category: 'accessories' },
  { label: '耳环', value: 'earrings', icon: '👂', category: 'accessories' },
  { label: '手表', value: 'watch', icon: '⌚', category: 'accessories' },
  { label: '手链', value: 'bracelet', icon: '💎', category: 'accessories' },
  { label: '发饰', value: 'hairAccessory', icon: '🎀', category: 'accessories' },
  { label: '领结', value: 'bowtie', icon: '🎀', category: 'accessories' },
  { label: '领带', value: 'tie', icon: '👔', category: 'accessories' },
  { label: '皇冠', value: 'crown', icon: '👑', category: 'accessories' },
  { label: '魔法棒', value: 'magicWand', icon: '✨', category: 'accessories' },

  // 姿势表情
  { label: '微笑', value: 'smile', icon: '😊', category: 'pose' },
  { label: '大笑', value: 'laugh', icon: '😄', category: 'pose' },
  { label: '严肃', value: 'serious', icon: '😐', category: 'pose' },
  { label: '惊讶', value: 'surprised', icon: '😲', category: 'pose' },
  { label: '站立', value: 'standing', icon: '🧍', category: 'pose' },
  { label: '坐着', value: 'sitting', icon: '🪑', category: 'pose' },
  { label: '跑步', value: 'running', icon: '🏃', category: 'pose' },
  { label: '跳跃', value: 'jumping', icon: '🦘', category: 'pose' },
  { label: '舞蹈', value: 'dancing', icon: '💃', category: 'pose' },
  { label: '思考', value: 'thinking', icon: '🤔', category: 'pose' },
  { label: '害怕', value: 'scared', icon: '😱', category: 'pose' },
  { label: '生气', value: 'angry', icon: '😠', category: 'pose' },
  { label: '哭泣', value: 'crying', icon: '😢', category: 'pose' },
  { label: '睡觉', value: 'sleeping', icon: '😴', category: 'pose' },
  { label: '挥手', value: 'waving', icon: '👋', category: 'pose' },

  // 画面风格
  { label: '卡通风格', value: 'cartoonStyle', icon: '🎨', category: 'style' },
  { label: '水彩风格', value: 'watercolorStyle', icon: '🖌️', category: 'style' },
  { label: '铅笔素描', value: 'pencilSketch', icon: '✏️', category: 'style' },
  { label: '动漫风格', value: 'animeStyle', icon: '🇯🇵', category: 'style' },
  { label: '3D风格', value: '3dStyle', icon: '🧊', category: 'style' },
  { label: '明亮色彩', value: 'brightColors', icon: '🌈', category: 'style' },
  { label: '柔和色调', value: 'softColors', icon: '🌸', category: 'style' },
  { label: '复古风格', value: 'vintageStyle', icon: '📷', category: 'style' },
  { label: '未来风格', value: 'futuristicStyle', icon: '🚀', category: 'style' },
  { label: '油画风格', value: 'oilPainting', icon: '🖼️', category: 'style' },
  { label: '像素风格', value: 'pixelArt', icon: '👾', category: 'style' },
  { label: '剪纸风格', value: 'papercut', icon: '✂️', category: 'style' },
  { label: '黑白风格', value: 'blackAndWhite', icon: '⚪', category: 'style' },
  { label: '梦幻风格', value: 'dreamy', icon: '💫', category: 'style' },
  { label: '写实风格', value: 'realistic', icon: '📸', category: 'style' }
];

// 合并所有特点
const characterTraits = [...personalityTraits, ...appearanceTraits];

// 切换性格特点
const togglePersonalityTrait = (trait) => {
  if (localCharacter.personalityTraits.includes(trait)) {
    localCharacter.personalityTraits = localCharacter.personalityTraits.filter(t => t !== trait);
  } else {
    // 不再限制特点数量
    localCharacter.personalityTraits.push(trait);
  }

  // 兼容旧版本
  localCharacter.traits = [...localCharacter.personalityTraits];
};

// 切换外表特点
const toggleAppearanceTrait = (trait) => {
  if (localCharacter.appearanceTraits.includes(trait)) {
    localCharacter.appearanceTraits = localCharacter.appearanceTraits.filter(t => t !== trait);
  } else {
    // 不再限制特点数量
    localCharacter.appearanceTraits.push(trait);
  }
};

// 选择外表特点并填充到描述中
const selectAppearanceTrait = (trait) => {
  // 先切换特点选择状态
  toggleAppearanceTrait(trait);

  // 获取特点标签
  const traitLabel = getTraitLabel(trait);

  // 更新外表描述
  updateAppearanceDescription();
};

// 更新外表描述
const updateAppearanceDescription = () => {
  // 获取所有选中的外表特点，并按分类整理
  const traitsByCategory = {};

  // 初始化分类
  appearanceCategories.forEach(category => {
    traitsByCategory[category.id] = [];
  });

  // 按分类整理特点
  localCharacter.appearanceTraits.forEach(traitValue => {
    const trait = appearanceTraits.find(t => t.value === traitValue);
    if (trait && trait.category) {
      traitsByCategory[trait.category].push(getTraitLabel(traitValue));
    }
  });

  // 如果没有选择任何特点，直接返回
  if (localCharacter.appearanceTraits.length === 0) return;

  // 生成描述文本
  const characterType = characterTypes.find(t => t.value === localCharacter.characterType)?.label || '角色';
  let description = `${localCharacter.name || '这个角色'}是一个`;

  // 添加基本外观描述
  if (traitsByCategory.basic.length > 0) {
    description += `${traitsByCategory.basic.join('、')}的`;
  }

  // 添加面部特征描述
  if (traitsByCategory.face.length > 0) {
    description += `${traitsByCategory.face.join('、')}的`;
  }

  // 添加发型发色描述
  if (traitsByCategory.hair.length > 0) {
    description += `${traitsByCategory.hair.join('、')}的`;
  }

  // 添加角色类型
  description += `${characterType}`;

  // 添加服装风格
  if (traitsByCategory.clothing.length > 0) {
    description += `，穿着${traitsByCategory.clothing.join('、')}`;
  }

  // 添加配饰装扮
  if (traitsByCategory.accessories.length > 0) {
    // 根据不同配饰类型使用不同的动词
    const accessoryVerbs = {
      'glasses': '戴着',
      'sunglasses': '戴着',
      'hat': '戴着',
      'scarf': '围着',
      'gloves': '戴着',
      'backpack': '背着',
      'necklace': '戴着',
      'earrings': '戴着',
      'watch': '戴着',
      'bracelet': '戴着',
      'hairAccessory': '戴着',
      'bowtie': '系着',
      'tie': '系着',
      'crown': '戴着',
      'magicWand': '拿着'
    };

    // 按动词分组配饰
    const accessoriesByVerb = {};

    traitsByCategory.accessories.forEach(acc => {
      // 查找特点对应的值
      const trait = appearanceTraits.find(t => t.label === acc);
      const verb = trait ? (accessoryVerbs[trait.value] || '戴着') : '戴着';

      if (!accessoriesByVerb[verb]) {
        accessoriesByVerb[verb] = [];
      }

      // 移除"戴"前缀，避免"戴着戴眼镜"的重复
      let accLabel = acc;
      if (acc.startsWith('戴') && verb === '戴着') {
        accLabel = acc.substring(1);
      }

      accessoriesByVerb[verb].push(accLabel);
    });

    // 拼接不同动词的配饰
    let isFirst = true;
    Object.entries(accessoriesByVerb).forEach(([verb, accs]) => {
      if (accs.length > 0) {
        description += `${isFirst ? '，' : '，'}${verb}${accs.join('、')}`;
        isFirst = false;
      }
    });
  }

  // 添加姿势表情
  if (traitsByCategory.pose.length > 0) {
    // 分类表情和姿势
    const expressions = [];
    const poses = [];
    const actions = [];

    traitsByCategory.pose.forEach(pose => {
      // 表情类
      if (['微笑', '大笑', '严肃', '惊讶', '害怕', '生气', '哭泣'].includes(pose)) {
        expressions.push(pose);
      }
      // 动作类
      else if (['跑步', '跳跃', '舞蹈', '挥手'].includes(pose)) {
        actions.push(pose);
      }
      // 姿势类
      else {
        poses.push(pose);
      }
    });

    // 先添加表情
    if (expressions.length > 0) {
      description += `，脸上露出${expressions.join('、')}的表情`;
    }

    // 再添加姿势
    if (poses.length > 0) {
      description += `，${poses.length > 1 ? '保持着' : ''}${poses.join('、')}${poses.length > 1 ? '的姿势' : ''}`;
    }

    // 最后添加动作
    if (actions.length > 0) {
      description += `，正在${actions.join('、')}`;
    }
  }

  // 添加画面风格
  if (traitsByCategory.style.length > 0) {
    description += `。画面风格为${traitsByCategory.style.join('、')}`;
  }

  // 确保句子结尾有句号
  if (!description.endsWith('。')) {
    description += '。';
  }

  // 更新外表描述
  localCharacter.appearance = description;

  // 聚焦文本框并将光标移到末尾
  if (appearanceTextarea.value) {
    setTimeout(() => {
      appearanceTextarea.value.focus();
      appearanceTextarea.value.setSelectionRange(description.length, description.length);
    }, 50);
  }
};



// 通用切换特点函数
const toggleTrait = (trait, category) => {
  if (category === 'personality') {
    togglePersonalityTrait(trait);
  } else if (category === 'appearance') {
    toggleAppearanceTrait(trait);
  }
};

// 添加自定义角色类型
const addCustomCharacterType = () => {
  if (!customCharacterType.value.trim()) return;

  // 创建新的角色类型
  const newType = {
    label: `[自定义] ${customCharacterType.value}`,
    value: 'custom_' + Date.now(),
    icon: '✏️', // 使用特殊图标标识自定义类型
    isCustom: true // 标记为自定义类型
  };

  // 添加到角色类型列表
  characterTypes.push(newType);

  // 选择新添加的类型
  localCharacter.characterType = newType.value;

  // 清空输入框
  customCharacterType.value = '';

  window.$message?.success('已添加自定义角色类型');
};

// 添加自定义性格特点
const addCustomPersonalityTrait = () => {
  if (!customPersonalityTrait.value.trim()) return;

  // 创建新的性格特点
  const newTrait = {
    label: `[自定义] ${customPersonalityTrait.value}`,
    value: 'custom_personality_' + Date.now(),
    icon: '✏️', // 使用特殊图标标识自定义特点
    category: 'personality',
    isCustom: true // 标记为自定义特点
  };

  // 添加到性格特点列表
  personalityTraits.push(newTrait);

  // 选择新添加的特点
  togglePersonalityTrait(newTrait.value);

  // 清空输入框
  customPersonalityTrait.value = '';

  window.$message?.success('已添加自定义性格特点');
};

// 按分类获取特点
const getTraitsByCategory = (categoryId) => {
  return appearanceTraits.filter(trait => trait.category === categoryId);
};

// 删除自定义特点
const deleteCustomTrait = (traitValue, type) => {
  if (type === 'personality') {
    // 从性格特点列表中移除
    const index = personalityTraits.findIndex(t => t.value === traitValue);
    if (index !== -1) {
      personalityTraits.splice(index, 1);
    }

    // 如果该特点已被选中，也从角色的性格特点中移除
    if (localCharacter.personalityTraits.includes(traitValue)) {
      localCharacter.personalityTraits = localCharacter.personalityTraits.filter(t => t !== traitValue);
      // 兼容旧版本
      localCharacter.traits = [...localCharacter.personalityTraits];
    }

    window.$message?.success('已删除自定义性格特点');
  } else if (type === 'appearance') {
    // 从外表特点列表中移除
    const index = appearanceTraits.findIndex(t => t.value === traitValue);
    if (index !== -1) {
      appearanceTraits.splice(index, 1);
    }

    // 如果该特点已被选中，也从角色的外表特点中移除
    if (localCharacter.appearanceTraits.includes(traitValue)) {
      localCharacter.appearanceTraits = localCharacter.appearanceTraits.filter(t => t !== traitValue);
      // 更新外表描述
      updateAppearanceDescription();
    }

    window.$message?.success('已删除自定义外表特点');
  }
};

// 删除自定义角色类型
const deleteCustomCharacterType = (typeValue) => {
  // 从角色类型列表中移除
  const index = characterTypes.findIndex(t => t.value === typeValue);
  if (index !== -1) {
    characterTypes.splice(index, 1);
  }

  // 如果当前角色使用的是这个类型，重置为默认类型
  if (localCharacter.characterType === typeValue) {
    localCharacter.characterType = 'boy'; // 默认为小男孩
  }

  window.$message?.success('已删除自定义角色类型');
};

// 添加自定义外表特点
const addCustomAppearanceTrait = (categoryId) => {
  if (!customAppearanceTraits[categoryId].trim()) return;

  // 创建新的外表特点
  const newTrait = {
    label: `[自定义] ${customAppearanceTraits[categoryId]}`,
    value: 'custom_' + categoryId + '_' + Date.now(),
    icon: '✏️', // 使用特殊图标标识自定义特点
    category: categoryId,
    isCustom: true // 标记为自定义特点
  };

  // 添加到外表特点列表
  appearanceTraits.push(newTrait);

  // 选择新添加的特点并更新描述
  toggleAppearanceTrait(newTrait.value);
  updateAppearanceDescription();

  // 清空输入框
  customAppearanceTraits[categoryId] = '';

  window.$message?.success(`已添加自定义${appearanceCategories.find(c => c.id === categoryId)?.label || '外表特点'}`);
};

// 获取角色类型名称
const getRoleName = (role) => {
  const roleNames = {
    'protagonist': '主角',
    'friend': '朋友',
    'helper': '帮手',
    'opponent': '对手'
  };
  return roleNames[role] || '未知';
};

const getCharacterEmoji = (character = null) => {
  const char = character || localCharacter;
  const typeEmojis = {
    boy: '👦',
    girl: '👧',
    animal: '🐶',
    magical: '🧚',
    robot: '🤖',
    alien: '👽',
    oldman: '👴',
    oldwoman: '👵',
    teen: '🧑',
    baby: '👶',
    superhero: '🦸',
    princess: '👸',
    prince: '🤴',
    elf: '🧝',
    wizard: '🧙',
    protagonist: '👑',
    friend: '💛',
    helper: '🤝',
    opponent: '⚡'
  };

  // 如果有角色类型，使用角色类型的表情
  if (char.characterType && typeEmojis[char.characterType]) {
    return typeEmojis[char.characterType];
  }

  // 否则使用角色在故事中的角色类型的表情
  if (char.role && typeEmojis[char.role]) {
    return typeEmojis[char.role];
  }

  // 默认返回通用角色表情
  return '👤';
};

const getTraitIcon = (traitValue) => {
  const trait = characterTraits.find(t => t.value === traitValue);
  return trait ? trait.icon : '';
};

const getTraitLabel = (traitValue) => {
  const trait = characterTraits.find(t => t.value === traitValue);
  return trait ? trait.label : '';
};

const getTraitCategory = (traitValue) => {
  const trait = characterTraits.find(t => t.value === traitValue);
  return trait ? trait.category : '';
};

import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const authStore = useAuthStore();
const isGeneratingImage = ref(false);
const generationStatus = ref('queuing'); // queuing, generating, progress, completed, failed
const generationProgress = ref(0);
const showImageEditor = ref(false); // 控制图像编辑器的显示
const showEditHint = ref(true); // 控制编辑提示的显示
const showCharacterLibrary = ref(false); // 控制角色库的显示

// 图像生成配置
const imageConfig = ref({
  model: 'gpt-image-1',
  size: '1024x1024',
  quality: 'hd',
  style: 'natural',
  fallbackService: 'https://image.pollinations.ai/prompt/%s',
  enabled: true
});

// 计算属性：获取角色图像URL
const characterImageUrl = computed(() => {
  // 尝试从所有可能的字段中获取图像URL
  const imageUrl = localCharacter.imageUrl || localCharacter._image || localCharacter.image || '';

  // 如果URL存在且有效，返回它
  if (imageUrl && (imageUrl.startsWith('http') || imageUrl.startsWith('data:'))) {
    console.log('[AI绘本创作-角色创建] 使用角色图像URL:', imageUrl.substring(0, 50) + '...');
    return imageUrl;
  }

  // 如果没有有效的URL，返回空字符串
  return '';
});

// 加载图像生成配置
const loadImageConfig = async () => {
  try {
    const res = await StorybookApi.getImageGenerationConfig();
    if (res && res.data) {
      imageConfig.value = {
        model: res.data.model || 'gpt-image-1',
        size: res.data.size || '1024x1024',
        quality: res.data.quality || 'hd',
        style: res.data.style || 'natural',
        fallbackService: res.data.fallbackService || 'https://image.pollinations.ai/prompt/%s',
        enabled: res.data.enabled !== false
      };
      console.log('[AI绘本创作-角色生成] 已加载图像生成配置:', imageConfig.value);
    }
  } catch (error) {
    console.error('[AI绘本创作-角色生成] 加载图像生成配置失败:', error);
    console.log('[AI绘本创作-角色生成] 使用默认配置');

    // 使用默认配置，确保即使API调用失败也能继续工作
    imageConfig.value = {
      model: 'gpt-image-1',
      size: '1024x1024',
      quality: 'hd',
      style: 'natural',
      fallbackService: 'https://image.pollinations.ai/prompt/%s',
      enabled: true
    };
  }
};

// 关闭编辑提示
const closeEditHint = () => {
  showEditHint.value = false;
  // 将提示状态保存到localStorage，避免重复显示
  try {
    localStorage.setItem('characterEditHintShown', 'true');
  } catch (e) {
    console.error('无法保存提示状态到localStorage', e);
  }
};

// 处理图像编辑成功
const handleEditSuccess = (newImageInfo) => {
  try {
    console.log('[AI绘本创作-角色编辑] 图像编辑成功，收到的数据类型:', typeof newImageInfo);

    // 确保我们有有效的图像URL
    let newImageUrl;
    if (typeof newImageInfo === 'string') {
      newImageUrl = newImageInfo;
    } else if (newImageInfo && newImageInfo.url) {
      newImageUrl = newImageInfo.url;
    } else if (newImageInfo && typeof newImageInfo === 'object') {
      console.log('[AI绘本创作-角色编辑] 图像编辑成功，但需要从对象中提取URL，对象结构:',
        Object.keys(newImageInfo).join(', '));
      // 尝试从对象中找到URL字段
      newImageUrl = newImageInfo.fileUrl || newImageInfo.url || newImageInfo.imageUrl ||
                   (newImageInfo.fileInfo && typeof newImageInfo.fileInfo === 'string' ?
                    newImageInfo.fileInfo : null);
    }

    if (!newImageUrl) {
      console.error('[AI绘本创作-角色编辑] 无法从响应中提取图像URL:', newImageInfo);
      window.$message?.error('图像编辑成功，但无法获取新图像URL');
      return;
    }

    console.log('[AI绘本创作-角色编辑] 图像编辑成功，新图像URL:', newImageUrl.substring(0, 50) + '...');

    // 检查URL是否有效
    if (!newImageUrl.startsWith('http')) {
      console.warn('[AI绘本创作-角色编辑] 图像URL格式可能不正确:', newImageUrl.substring(0, 50) + '...');
    }

    // 更新角色图像，同时更新新旧字段以保持一致性
    localCharacter.imageUrl = newImageUrl;
    localCharacter._image = newImageUrl;
    localCharacter.image = newImageUrl; // 兼容旧版本

    window.$message?.success('角色图像编辑成功！');

    // 重置生成状态
    isGeneratingImage.value = false;
    generationStatus.value = 'completed';
    generationProgress.value = 100;

    // 延迟后完全隐藏生成状态
    setTimeout(() => {
      generationStatus.value = '';
    }, 1000);

    // 保存到角色库
    saveCharacterToLibrary();
  } catch (error) {
    console.error('[AI绘本创作-角色编辑] 处理编辑成功回调时出错:', error);
    window.$message?.error('处理图像编辑结果时出错');
  }
};

// 处理图像编辑取消
const handleEditCancel = () => {
  console.log('[AI绘本创作-角色编辑] 图像编辑已取消');

  // 确保生成状态被重置
  isGeneratingImage.value = false;
  generationStatus.value = '';
  generationProgress.value = 0;
};

// 处理图像编辑完成
const handleEditComplete = (newImageInfo) => {
  try {
    console.log('[AI绘本创作-角色编辑] 图像编辑完成，收到的数据类型:', typeof newImageInfo);

    // 确保我们有有效的图像URL
    let newImageUrl;
    if (typeof newImageInfo === 'string') {
      newImageUrl = newImageInfo;
    } else if (newImageInfo && newImageInfo.url) {
      newImageUrl = newImageInfo.url;
    } else if (newImageInfo && typeof newImageInfo === 'object') {
      console.log('[AI绘本创作-角色编辑] 图像编辑完成，但需要从对象中提取URL，对象结构:',
        Object.keys(newImageInfo).join(', '));
      // 尝试从对象中找到URL字段
      newImageUrl = newImageInfo.fileUrl || newImageInfo.url || newImageInfo.imageUrl ||
                   (newImageInfo.fileInfo && typeof newImageInfo.fileInfo === 'string' ?
                    newImageInfo.fileInfo : null);
    }

    if (!newImageUrl) {
      console.error('[AI绘本创作-角色编辑] 无法从响应中提取图像URL:', newImageInfo);
      return;
    }

    console.log('[AI绘本创作-角色编辑] 图像编辑完成，新图像URL:', newImageUrl.substring(0, 50) + '...');

    // 检查URL是否有效
    if (!newImageUrl.startsWith('http')) {
      console.warn('[AI绘本创作-角色编辑] 图像URL格式可能不正确:', newImageUrl.substring(0, 50) + '...');
    }

    // 更新角色图像，同时更新新旧字段以保持一致性
    if (newImageUrl && (newImageUrl !== localCharacter.imageUrl || newImageUrl !== localCharacter._image)) {
      localCharacter.imageUrl = newImageUrl;
      localCharacter._image = newImageUrl;
      localCharacter.image = newImageUrl; // 兼容旧版本

      // 保存到角色库
      saveCharacterToLibrary();
    }
  } catch (error) {
    console.error('[AI绘本创作-角色编辑] 处理编辑完成回调时出错:', error);
  }
};

// 重新生成角色图像
const regenerateCharacterImage = () => {
  console.log('[AI绘本创作-角色生成] 重新生成角色图像');
  generateCharacter(true);
};

// 从角色库导入角色
const importCharacterFromLibrary = (character) => {
  console.log('[AI绘本创作-角色库] 导入角色:', character.name);

  // 创建一个深拷贝，避免引用问题
  const characterCopy = JSON.parse(JSON.stringify(character));

  // 确保所有必要的字段都存在
  // 基本属性
  localCharacter.id = characterCopy.id || Date.now() + Math.floor(Math.random() * 1000);
  localCharacter.name = characterCopy.name || '';
  localCharacter.characterType = characterCopy.characterType || '';

  // 性格特点
  localCharacter.personalityTraits = characterCopy.personalityTraits || [];

  // 外表特点
  localCharacter.appearance = characterCopy.appearance || '';
  localCharacter.appearanceTraits = characterCopy.appearanceTraits || [];

  // 视觉元素 - 确保所有图像字段都正确设置
  // 首先确定最终要使用的图像URL
  const imageUrl = characterCopy.imageUrl || characterCopy.image || characterCopy._image || '';

  // 然后统一设置所有图像相关字段
  localCharacter.imageUrl = imageUrl;
  localCharacter._image = imageUrl;
  localCharacter.image = imageUrl; // 兼容旧版本

  // 其他属性
  localCharacter.tags = characterCopy.tags || [];
  localCharacter.isFavorite = characterCopy.isFavorite || 0;
  localCharacter.isTemplate = characterCopy.isTemplate || 0;

  // 兼容旧版本
  if (characterCopy.traits && characterCopy.traits.length > 0) {
    localCharacter.traits = [...characterCopy.traits];
    // 如果没有性格特点但有traits，使用traits作为性格特点
    if (!localCharacter.personalityTraits || localCharacter.personalityTraits.length === 0) {
      localCharacter.personalityTraits = [...characterCopy.traits];
    }
  }

  // 更新时间戳
  localCharacter.createdAt = characterCopy.createdAt || new Date().toISOString();
  localCharacter.updatedAt = new Date().toISOString();

  // 立即保存到localStorage
  try {
    // 获取父组件的projectData
    const parentComponent = inject('projectData', null);
    if (parentComponent) {
      // 确保角色数据已更新到父组件
      emit('update:character', {...localCharacter});

      // 保存到localStorage
      saveStorybookProject(parentComponent);
      console.log('[AI绘本创作-角色创建] 从角色库导入的角色数据已保存到localStorage');
    }
  } catch (error) {
    console.error('[AI绘本创作-角色创建] 保存从角色库导入的角色数据失败:', error);
  }

  // 显示成功消息
  window.$message?.success(`已导入角色"${character.name}"`);

  // 打印导入后的角色数据，用于调试
  console.log('[AI绘本创作-角色库] 导入后的角色数据:', JSON.stringify(localCharacter));
};

// 保存角色到角色库
const saveCharacterToLibrary = async () => {
  // 确保角色有名称和图像（支持新旧两种字段）
  if (!localCharacter.name || (!localCharacter.imageUrl && !localCharacter._image && !localCharacter.image)) return;

  // 创建角色对象，确保字段一致性
  const character = {
    // 基本属性
    id: localCharacter.id || Date.now() + Math.floor(Math.random() * 1000),
    name: localCharacter.name,
    characterType: localCharacter.characterType || '',

    // 性格特点
    personalityTraits: localCharacter.personalityTraits || [],

    // 外表特点
    appearance: localCharacter.appearance || '',
    appearanceTraits: localCharacter.appearanceTraits || [],

    // 视觉元素 - 优先使用新字段
    imageUrl: localCharacter.imageUrl || localCharacter.image || localCharacter._image || '',

    // 其他属性
    tags: localCharacter.tags || [],
    isFavorite: localCharacter.isFavorite || 0,
    isTemplate: localCharacter.isTemplate || 0,

    // 时间戳
    createdAt: localCharacter.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),

    // 兼容旧版本
    _image: localCharacter.imageUrl || localCharacter.image || localCharacter._image || '',
    traits: localCharacter.personalityTraits || []
  };

  try {
    // 添加到角色库
    const savedCharacter = await addCharacterToLibrary(character);
    console.log('[AI绘本创作-角色库] 角色已保存到角色库:', savedCharacter.name);

    // 更新本地角色的ID，确保与服务器一致
    if (savedCharacter.id && savedCharacter.id !== localCharacter.id) {
      localCharacter.id = savedCharacter.id;
      console.log('[AI绘本创作-角色库] 更新本地角色ID:', savedCharacter.id);
    }

    // 如果用户已登录，尝试同步角色库
    try {
      const authStore = useAuthStore();
      if (authStore.token) {
        // 异步同步，不阻塞UI
        synchronizeCharacterLibrary().catch(error => {
          console.error('[AI绘本创作-角色库] 同步角色库失败:', error);
        });
      }
    } catch (error) {
      console.error('[AI绘本创作-角色库] 获取认证状态失败:', error);
      // 即使获取认证状态失败，也不影响本地保存
    }

    return savedCharacter;
  } catch (error) {
    console.error('[AI绘本创作-角色库] 保存角色失败:', error);
    return null;
  }
};

const generateCharacter = async (isRegenerate = false) => {
  console.log('[AI绘本创作-角色生成] 开始生成角色图像');

  // 防止重复点击
  if (isGeneratingImage.value && !isRegenerate) {
    window.$message?.warning('正在生成角色图像，请稍候...');
    return;
  }

  // 随机选择角色类型
  if (!localCharacter.characterType) {
    const types = characterTypes.map(t => t.value);
    localCharacter.characterType = types[Math.floor(Math.random() * types.length)];
    console.log('[AI绘本创作-角色生成] 随机选择角色类型:', localCharacter.characterType);
  }

  // 如果没有名字，随机选择一个
  if (!localCharacter.name) {
    const randomNames = {
      boy: ['小明', '小强', '小航', '小宇', '小天', '小杰', '小亮', '小峰'],
      girl: ['小红', '小丽', '小美', '小雪', '小芳', '小婷', '小月', '小琳'],
      animal: ['毛毛', '点点', '花花', '跳跳', '球球', '贝贝', '多多', '乐乐'],
      magical: ['星星', '云云', '光光', '彩彩', '风风', '雨雨', '雪雪', '霜霜'],
      robot: ['机器人小白', '小铁', '小钢', '小智', '小电', '小芯', '小齿', '小机'],
      alien: ['外星人小绿', '小紫', '小蓝', '小星', '小宇', '小银', '小太', '小空'],
      oldman: ['王爷爷', '李爷爷', '张爷爷', '刘爷爷', '陈爷爷', '老智', '老明', '老林'],
      oldwoman: ['王奶奶', '李奶奶', '张奶奶', '刘奶奶', '陈奶奶', '老花', '老兰', '老红'],
      teen: ['小青', '小年', '小少', '小志', '小华', '小梦', '小青', '小岚'],
      baby: ['小宝', '小豆', '小丸', '小团', '小圆', '小包', '小粒', '小珠'],
      superhero: ['超人小侠', '飞侠', '闪电侠', '力量侠', '隐形侠', '火焰侠', '冰冻侠', '风暴侠'],
      princess: ['小公主', '花公主', '雪公主', '星公主', '月公主', '梦公主', '云公主', '彩公主'],
      prince: ['小王子', '勇王子', '智王子', '星王子', '月王子', '梦王子', '云王子', '光王子'],
      elf: ['小精灵', '花精灵', '树精灵', '风精灵', '水精灵', '火精灵', '光精灵', '影精灵'],
      wizard: ['小巫师', '魔法师', '法师', '咒语师', '魔杖师', '星辰师', '元素师', '奇迹师']
    };
    const type = localCharacter.characterType;
    const names = randomNames[type] || randomNames.boy;
    localCharacter.name = names[Math.floor(Math.random() * names.length)];
    console.log('[AI绘本创作-角色生成] 随机选择角色名称:', localCharacter.name);
  }

  // 如果没有性格特点，随机选择3-5个
  if (localCharacter.personalityTraits.length === 0) {
    const personalityValues = personalityTraits.map(t => t.value);
    const personalityCount = Math.floor(Math.random() * 3) + 3; // 3-5个特点
    console.log('[AI绘本创作-角色生成] 将随机选择性格特点数量:', personalityCount);

    while (localCharacter.personalityTraits.length < personalityCount) {
      const randomTrait = personalityValues[Math.floor(Math.random() * personalityValues.length)];
      if (!localCharacter.personalityTraits.includes(randomTrait)) {
        localCharacter.personalityTraits.push(randomTrait);
      }
    }
    console.log('[AI绘本创作-角色生成] 随机选择的性格特点:', localCharacter.personalityTraits);
  }

  // 如果没有外表特点，随机选择3-5个
  if (localCharacter.appearanceTraits.length === 0) {
    const appearanceValues = appearanceTraits.map(t => t.value);
    const appearanceCount = Math.floor(Math.random() * 3) + 3; // 3-5个特点
    console.log('[AI绘本创作-角色生成] 将随机选择外表特点数量:', appearanceCount);

    while (localCharacter.appearanceTraits.length < appearanceCount) {
      const randomTrait = appearanceValues[Math.floor(Math.random() * appearanceValues.length)];
      if (!localCharacter.appearanceTraits.includes(randomTrait)) {
        localCharacter.appearanceTraits.push(randomTrait);
      }
    }
    console.log('[AI绘本创作-角色生成] 随机选择的外表特点:', localCharacter.appearanceTraits);
  }

  // 生成外表描述
  updateAppearanceDescription();
  console.log('[AI绘本创作-角色生成] 生成的外表描述:', localCharacter.appearance);

  // 使用外表描述作为提示词
  let prompt = localCharacter.appearance;

  // 添加一些额外的描述，以提高生成质量
  prompt += ' 高质量插图，儿童绘本风格，白色背景，细节丰富。';
  console.log('[AI绘本创作-角色生成] 最终提示词:', prompt);

  // 创建一个可以取消的控制器
  const abortController = new AbortController();

  // 声明进度更新定时器变量
  const progressInterval = ref(null);

  // 设置超时ID
  let timeoutId = null;

  try {
    // 设置生成状态
    isGeneratingImage.value = true;
    generationStatus.value = 'queuing';
    generationProgress.value = 0;
    window.$message?.info(isRegenerate ? '正在重新生成角色图像，请稍候...' : '正在使用AI生成角色图像，请稍候...');
    console.log('[AI绘本创作-角色生成] 状态已设置为queuing，开始生成图像');

    // 先保存角色基本信息到库中，即使图像生成失败也能保留角色信息
    if (!isRegenerate && localCharacter.name) {
      try {
        await saveCharacterToLibrary();
      } catch (error) {
        console.error('[AI绘本创作-角色生成] 保存角色到库中失败:', error);
        // 继续生成图像，不中断流程
      }
    }

    // 创建一个响应处理函数
    const handleImageResponse = (response) => {
      console.log('[AI绘本创作-角色生成] 收到图像生成响应:', JSON.stringify(response));

      // 检查是否是进度更新
      if (response && response.status === 2) {
        // 处理中状态，更新进度
        console.log('[AI绘本创作-角色生成] 图像生成处理中，状态码:', response.status);
        generationStatus.value = 'generating';

        // 如果有进度百分比，更新进度条
        if (response.progress) {
          const progressValue = parseInt(response.progress);
          if (!isNaN(progressValue)) {
            generationProgress.value = Math.min(90, progressValue);
            console.log('[AI绘本创作-角色生成] 更新进度:', generationProgress.value);
          }
        }

        // 如果没有进度更新定时器，创建一个
        if (!progressInterval.value) {
          progressInterval.value = setInterval(() => {
            if (generationStatus.value === 'completed' || generationStatus.value === 'failed') {
              console.log('[AI绘本创作-角色生成] 进度更新结束，最终状态:', generationStatus.value);
              clearInterval(progressInterval.value);
              progressInterval.value = null;
            } else {
              generationProgress.value = Math.min(90, generationProgress.value + 2);
              console.log('[AI绘本创作-角色生成] 更新进度:', generationProgress.value);
            }
          }, 3000);
        }
        return null; // 继续等待
      } else if (response && response.status === 5) {
        // 图像生成失败
        console.error('[AI绘本创作-角色生成] 图像生成失败，状态码:', response.status, '错误信息:', response.error || '未知错误');
        handleGenerationFailure(response.error || '未知错误');
        return false;
      }

      // 尝试从不同格式的响应中提取图像URL
      let imageUrl = null;

      if (response && response.fileInfo) {
        // 旧格式
        imageUrl = response.fileInfo;
      } else if (response && response.data && response.data.length > 0) {
        // OpenAI API格式
        if (response.data[0].b64_json) {
          // 如果是base64格式，转换为数据URL
          imageUrl = 'data:image/png;base64,' + response.data[0].b64_json;
        } else if (response.data[0].url) {
          imageUrl = response.data[0].url;
        }
      } else if (response && response.url) {
        // 简化格式
        imageUrl = response.url;
      } else if (typeof response === 'string' && (response.startsWith('http') || response.startsWith('data:'))) {
        // 直接返回URL字符串
        imageUrl = response;
      }

      if (imageUrl) {
        // 图像生成成功
        console.log('[AI绘本创作-角色生成] 图像生成成功，文件URL:', imageUrl.substring(0, 50) + '...');
        generationStatus.value = 'completed';
        generationProgress.value = 100;

        // 更新角色图像，同时更新新旧字段以保持一致性
        localCharacter.imageUrl = imageUrl;
        localCharacter._image = imageUrl;
        localCharacter.image = imageUrl; // 兼容旧版本

        window.$message?.success('角色图像生成成功！');

        // 保存到角色库
        saveCharacterToLibrary().catch(error => {
          console.error('[AI绘本创作-角色生成] 图像生成成功后保存角色失败:', error);
          // 不影响用户体验，继续流程
        });

        // 保存到localStorage
        try {
          // 获取父组件的projectData
          const parentComponent = inject('projectData', null);
          if (parentComponent) {
            // 保存到localStorage
            saveStorybookProject(parentComponent);
            console.log('[AI绘本创作-角色生成] 角色图像已自动保存到localStorage');
          }
        } catch (error) {
          console.error('[AI绘本创作-角色生成] 自动保存角色图像失败:', error);
        }

        // 清除可能存在的进度更新定时器
        if (progressInterval.value) {
          clearInterval(progressInterval.value);
          progressInterval.value = null;
        }

        // 清除超时定时器
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        // 延迟后重置生成状态
        setTimeout(() => {
          isGeneratingImage.value = false;
        }, 1000);

        return true;
      } else {
        console.error('[AI绘本创作-角色生成] 无法从响应中提取图像URL:', JSON.stringify(response));
        handleGenerationFailure('无法从响应中提取图像URL');
        return false;
      }
    };

    // 设置超时处理
    const timeoutDuration = 120000; // 2分钟超时
    timeoutId = setTimeout(() => {
      if (isGeneratingImage.value && generationStatus.value !== 'completed') {
        console.warn('[AI绘本创作-角色生成] 图像生成超时');
        handleGenerationFailure('生成超时，请稍后重试');
        abortController.abort();

        // 尝试使用备用方案
        tryFallbackImageGeneration(prompt);
      }
    }, timeoutDuration);

    // 设置进度更新定时器
    progressInterval.value = setInterval(() => {
      if (generationStatus.value === 'generating' && generationProgress.value < 90) {
        generationProgress.value += 5;
        console.log('[AI绘本创作-角色生成] 更新进度:', generationProgress.value);
      }
    }, 3000);

    // 使用onConversation函数发送请求（与聊天界面一致）
    console.log('[AI绘本创作-角色生成] 发送图像生成请求，参数:', {
      model: imageConfig.value.model,
      modelName: 'GPT Image',
      modelType: 2,
      promptLength: prompt.length,
      extraParam: {
        size: imageConfig.value.size,
        quality: imageConfig.value.quality,
        style: imageConfig.value.style
      }
    });

    // 设置初始进度状态
    generationStatus.value = 'generating';
    generationProgress.value = 30;

    const response = await onConversation({
      msg: prompt,
      model: imageConfig.value.model,
      modelName: 'GPT Image',
      modelType: 2,
      extraParam: {
        size: imageConfig.value.size,
        quality: imageConfig.value.quality,
        style: imageConfig.value.style
      },
      onSuccess: handleImageResponse,
      signal: abortController.signal
    });

    // 如果onConversation直接返回了结果（而不是通过回调）
    if (response) {
      const result = handleImageResponse(response);
      if (result === false) {
        // 如果处理失败，尝试使用备用方案
        await tryFallbackImageGeneration(prompt);
      }
    }

  } catch (error) {
    console.error('[AI绘本创作-角色生成] 生成角色图像失败:', error);
    handleGenerationFailure(error.message || '未知错误');

    // 尝试使用备用方案
    await tryFallbackImageGeneration(prompt);
  } finally {
    // 清除超时定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    // 兼容旧版本
    localCharacter.traits = [...localCharacter.personalityTraits];
  }
};

// 处理生成失败的统一函数
const handleGenerationFailure = (errorMessage) => {
  generationStatus.value = 'failed';
  generationProgress.value = 0;

  // 显示错误消息
  let displayError = '图像生成失败';
  if (errorMessage) {
    displayError += ': ' + errorMessage;
  }
  window.$message?.error(displayError);

  // 清除进度更新定时器
  if (progressInterval.value) {
    clearInterval(progressInterval.value);
    progressInterval.value = null;
  }

  // 重置生成状态
  isGeneratingImage.value = false;

  // 提示用户重试
  if (window.$dialog) {
    window.$dialog.warning({
      title: '图像生成失败',
      content: '生成角色图像失败，您可以稍后重试或修改角色描述后再次尝试。也可以使用备用方案生成图像。',
      positiveText: '使用备用方案',
      negativeText: '取消',
      onPositiveClick: () => {
        tryFallbackImageGeneration(localCharacter.appearance + ' 高质量插图，儿童绘本风格，白色背景，细节丰富。');
      }
    });
  }
};

// 尝试使用备用方案生成图像
const tryFallbackImageGeneration = async (prompt) => {
  try {
    console.log('[AI绘本创作-角色生成] 尝试使用备用方案生成图像');
    window.$message?.info('正在使用备用方案生成角色图像...');

    // 设置生成状态
    isGeneratingImage.value = true;
    generationStatus.value = 'generating';
    generationProgress.value = 30;

    // 使用配置的备用服务
    const fallbackService = imageConfig.value.fallbackService || 'https://image.pollinations.ai/prompt/%s';
    const fallbackUrl = fallbackService.replace('%s', encodeURIComponent(prompt));
    console.log('[AI绘本创作-角色生成] 使用备用方案生成图像:', fallbackUrl.substring(0, 50) + '...');

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 更新角色图像
    localCharacter.image = fallbackUrl;
    localCharacter.imageUrl = fallbackUrl;
    localCharacter._image = fallbackUrl;
    generationStatus.value = 'completed';
    generationProgress.value = 100;

    window.$message?.success('已使用备用方案生成角色图像');

    // 保存到角色库
    saveCharacterToLibrary();

    // 保存到localStorage
    try {
      // 获取父组件的projectData
      const parentComponent = inject('projectData', null);
      if (parentComponent) {
        // 保存到localStorage
        saveStorybookProject(parentComponent);
        console.log('[AI绘本创作-角色生成] 备用方案生成的角色图像已自动保存到localStorage');
      }
    } catch (error) {
      console.error('[AI绘本创作-角色生成] 自动保存备用方案生成的角色图像失败:', error);
    }

    // 重置生成状态
    setTimeout(() => {
      isGeneratingImage.value = false;
    }, 1000);

    return true;
  } catch (fallbackError) {
    console.error('[AI绘本创作-角色生成] 备用方案也失败:', fallbackError);
    window.$message?.error('备用图像生成方案也失败，请稍后重试');
    isGeneratingImage.value = false;
    generationStatus.value = 'failed';
    return false;
  }
};

// 组件挂载后的初始化
onMounted(async () => {
  // 加载图像生成配置
  await loadImageConfig();

  // 检查是否已经显示过编辑提示
  try {
    const hintShown = localStorage.getItem('characterEditHintShown');
    if (hintShown === 'true') {
      showEditHint.value = false;
    }
  } catch (e) {
    console.error('[AI绘本创作-角色编辑] 无法从localStorage读取提示状态', e);
  }

  // 确保所有图像字段都一致
  const imageUrl = localCharacter.imageUrl || localCharacter._image || localCharacter.image || '';
  if (imageUrl) {
    console.log('[AI绘本创作-角色编辑] 组件挂载时统一设置图像URL:', imageUrl.substring(0, 50) + '...');
    localCharacter.imageUrl = imageUrl;
    localCharacter._image = imageUrl;
    localCharacter.image = imageUrl;

    // 通知父组件更新角色数据
    emit('update:character', {...localCharacter});
  }

  // 检查角色图像是否存在
  console.log('[AI绘本创作-角色编辑] 组件挂载时的角色图像URL:', characterImageUrl.value ? characterImageUrl.value.substring(0, 50) + '...' : 'null');

  // 如果有图像但没有显示过提示，则显示编辑提示
  if (characterImageUrl.value && showEditHint.value) {
    // 5秒后自动隐藏提示
    setTimeout(() => {
      showEditHint.value = false;
    }, 5000);
  }

  // 如果角色有外表描述但没有图像，尝试使用备用图像生成服务
  if (!characterImageUrl.value && localCharacter.appearance) {
    console.warn('[AI绘本创作-角色编辑] 角色没有图像但有外表描述，尝试使用备用图像服务');

    const fallbackUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(localCharacter.appearance)}`;
    console.log('[AI绘本创作-角色编辑] 使用备用图像URL:', fallbackUrl.substring(0, 50) + '...');

    // 更新所有图像字段
    localCharacter.imageUrl = fallbackUrl;
    localCharacter._image = fallbackUrl;
    localCharacter.image = fallbackUrl;

    // 通知父组件更新角色数据
    emit('update:character', {...localCharacter});
  }
});
</script>

<style scoped>
.character-builder {
  padding: 0;
  background-color: transparent;
  border-radius: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.dark .character-builder {
  background-color: transparent;
}

.character-creation-hint {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  background-color: #f0f9ff;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.character-creation-hint:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .character-creation-hint {
  background-color: #0c4a6e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark .character-creation-hint:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hint-icon {
  font-size: 1.5rem;
  color: #3b82f6;
}

.dark .hint-icon {
  color: #60a5fa;
}

.hint-text {
  margin: 0;
  font-size: 1rem;
  color: #334155;
  line-height: 1.4;
}

.dark .hint-text {
  color: #e2e8f0;
}



.character-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.dark .character-selector {
  background-color: #1e293b;
  border-color: #334155;
}

.character-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: white;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.dark .character-tab {
  background-color: #334155;
  border-color: #475569;
}

.character-tab:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.dark .character-tab:hover {
  background-color: #475569;
}

.character-tab.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.dark .character-tab.active {
  background-color: #1e40af;
  border-color: #60a5fa;
}

.character-tab-icon {
  font-size: 1.25rem;
}

.character-tab-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .character-tab-name {
  color: #e2e8f0;
}

.delete-character-btn {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #fee2e2;
  color: #ef4444;
  border: none;
  font-size: 10px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  position: absolute;
  top: -5px;
  right: -5px;
}

.dark .delete-character-btn {
  background-color: #991b1b;
  color: #fca5a5;
}

.character-tab:hover .delete-character-btn {
  opacity: 1;
}



.add-character-button,
.library-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.add-character-button {
  background-color: #f0fdf4;
  color: #16a34a;
  border: 1px solid #dcfce7;
}

.library-button {
  background-color: #eff6ff;
  color: #1e40af;
  border: 1px solid #dbeafe;
}

.add-character-button.small,
.library-button.small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.character-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.character-tab:hover {
  background-color: #e2e8f0;
}

.character-tab.active {
  background-color: #dbeafe;
  border-color: #3b82f6;
}

.dark .character-tab {
  background-color: #1e293b;
  border-color: #334155;
}

.dark .character-tab:hover {
  background-color: #334155;
}

.dark .character-tab.active {
  background-color: #1e40af;
  border-color: #3b82f6;
}

.character-tab-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-character-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #dcfce7;
}

.add-icon,
.library-icon {
  font-size: 1rem;
}

.dark .add-character-button {
  background-color: #14532d;
  color: #86efac;
  border-color: #166534;
}

.dark .library-button {
  background-color: #1e3a8a;
  color: #93c5fd;
  border-color: #1e40af;
}

.add-character-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #dcfce7;
}

.library-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #dbeafe;
}

.dark .add-character-button:hover {
  background-color: #166534;
}

.dark .library-button:hover {
  background-color: #1e40af;
}


/* 引导提示 */
.guide-tip-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: #f0fdf4;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #d1fae5;
}

.dark .guide-tip-container {
  background-color: #064e3b;
  border-color: #065f46;
}

.guide-character-small {
  flex-shrink: 0;
}

.guide-character-small img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 50%;
  background-color: white;
  padding: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #a7f3d0;
}

.dark .guide-character-small img {
  background-color: #065f46;
  border-color: #10b981;
}

.guide-bubble {
  position: relative;
  background-color: white;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #d1fae5;
  flex: 1;
}

.dark .guide-bubble {
  background-color: #065f46;
  border-color: #10b981;
}

.guide-bubble:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent white transparent transparent;
}

.dark .guide-bubble:before {
  border-color: transparent #065f46 transparent transparent;
}

.guide-bubble p {
  margin: 0;
  font-size: 0.95rem;
  color: #065f46;
  line-height: 1.5;
}

.dark .guide-bubble p {
  color: #a7f3d0;
}

.builder-layout {
  display: flex;
  gap: 1.5rem;
  height: 100%;
}

/* 左侧预览面板 */
.character-preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark .character-preview-panel {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.dark .preview-header {
  background-color: #0f172a;
  border-color: #334155;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.character-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.dark .character-selector {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  overflow-y: auto;
  gap: 1rem;
}

/* 角色类型选择器 */
.preview-image-container {
  width: 100%;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  background-color: white;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.dark .preview-image-container {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.image-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .image-title {
  color: #e2e8f0;
}

.image-hint {
  font-size: 0.8rem;
  color: #64748b;
}

.dark .image-hint {
  color: #94a3b8;
}

.appearance-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.dark .appearance-section {
  border-top-color: #334155;
}

.appearance-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.dark .appearance-title {
  color: #e2e8f0;
}

.appearance-description {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.dark .appearance-description {
  border-top-color: #334155;
}

.appearance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.appearance-traits-fixed {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  max-height: 300px;
  overflow-y: auto;
}

.dark .appearance-traits-fixed {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.option-group .character-textarea {
  min-height: 80px;
  margin-bottom: 0.5rem;
  position: relative;
  resize: none;
}

.traits-header {
  margin-bottom: 0.75rem;
}

.traits-subtitle {
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
}

.dark .traits-subtitle {
  color: #94a3b8;
}

.traits-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.3rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

.dark .traits-tabs {
  border-bottom-color: #334155;
}

.trait-tab-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.trait-tab-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: transparent;
  transition: all 0.2s ease;
}

.trait-tab-btn[class*="character"]::after {
  background-color: #3b82f6;
}

.trait-tab-btn[class*="personality"]::after {
  background-color: #8b5cf6;
}

.trait-tab-btn[class*="basic"]::after {
  background-color: #3b82f6;
}

.trait-tab-btn[class*="face"]::after {
  background-color: #ec4899;
}

.trait-tab-btn[class*="hair"]::after {
  background-color: #8b5cf6;
}

.trait-tab-btn[class*="clothing"]::after {
  background-color: #10b981;
}

.trait-tab-btn[class*="accessories"]::after {
  background-color: #f59e0b;
}

.trait-tab-btn[class*="pose"]::after {
  background-color: #ef4444;
}

.trait-tab-btn[class*="style"]::after {
  background-color: #6366f1;
}

.dark .trait-tab-btn {
  background-color: #1e293b;
  border-color: #334155;
  color: #94a3b8;
}

.trait-tab-btn:hover {
  background-color: #e2e8f0;
  color: #475569;
}

.dark .trait-tab-btn:hover {
  background-color: #334155;
  color: #e2e8f0;
}

.trait-tab-btn.active {
  background-color: #f8fafc;
  border-color: #e2e8f0;
  color: #1e293b;
  font-weight: 600;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trait-tab-btn.active::after {
  height: 3px;
}

.dark .trait-tab-btn.active {
  background-color: #334155;
  border-color: #475569;
  color: #f8fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.traits-content {
  max-height: 200px;
  overflow-y: auto;
}

.trait-category-content {
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}





.role-selector-options {
  display: flex;
  justify-content: space-between;
  gap: 0.5rem;
}

.role-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.75rem;
  border-radius: 0.75rem;
  background-color: #f8fafc;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.dark .role-option {
  background-color: #1e293b;
  border-color: #334155;
}

.role-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.dark .role-option:hover {
  border-color: #475569;
}

.role-option.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.2);
}

.dark .role-option.active {
  border-color: #3b82f6;
  background-color: #1e40af;
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
}

.role-option.active:before {
  content: '✓';
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #3b82f6;
  color: white;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.dark .role-option.active:before {
  background-color: #60a5fa;
  color: #1e293b;
}

.role-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.role-label {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.dark .role-label {
  color: #e2e8f0;
}

.role-description {
  font-size: 0.8rem;
  color: #64748b;
  text-align: center;
}

.dark .role-description {
  color: #94a3b8;
}

.protagonist-icon {
  color: #3b82f6;
  background-color: #dbeafe;
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.friend-icon {
  color: #f59e0b;
  background-color: #fef3c7;
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.helper-icon {
  color: #10b981;
  background-color: #d1fae5;
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.opponent-icon {
  color: #ef4444;
  background-color: #fee2e2;
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.dark .protagonist-icon {
  background-color: #1e40af;
}

.dark .friend-icon {
  background-color: #92400e;
}

.dark .helper-icon {
  background-color: #065f46;
}

.dark .opponent-icon {
  background-color: #991b1b;
}

.role-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .role-label {
  color: #e2e8f0;
}

.preview-image {
  width: 100%;
  height: 300px;
  border-radius: 1rem;
  overflow: hidden;
  background-color: #f8fafc;
  margin-bottom: 1rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
}

.dark .preview-image {
  background-color: #1e293b;
  border-color: #334155;
}

/* 图像操作按钮 */
.image-actions {
  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 1rem;
  opacity: 1;
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 5;
}

.preview-image:not(:hover) .image-actions {
  transform: translateY(1rem);
  opacity: 0.8;
}

.preview-image:hover .image-actions {
  transform: translateY(0);
  opacity: 1;
}

.edit-image-btn,
.regenerate-image-btn {
  padding: 0.75rem 1.25rem;
  border-radius: 1rem;
  background-color: rgba(255, 255, 255, 0.95);
  border: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: #1e293b;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.edit-image-btn {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
}

.regenerate-image-btn {
  background-color: #f0fdf4;
  border-color: #10b981;
  color: #065f46;
}

.edit-icon,
.regenerate-icon {
  font-size: 1.25rem;
}

.edit-label,
.regenerate-label {
  display: inline-block;
}

.dark .edit-image-btn {
  background-color: rgba(30, 64, 175, 0.9);
  border-color: #3b82f6;
  color: #eff6ff;
}

.dark .regenerate-image-btn {
  background-color: rgba(6, 95, 70, 0.9);
  border-color: #10b981;
  color: #f0fdf4;
}

.edit-image-btn:hover,
.regenerate-image-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.dark .edit-image-btn:hover,
.dark .regenerate-image-btn:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.preview-image img:hover {
  transform: scale(1.05);
}

/* 编辑提示样式 */
.edit-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  pointer-events: none;
}

.hint-content {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 280px;
  pointer-events: auto;
  animation: float 3s ease-in-out infinite;
  border: 2px solid #e2e8f0;
}

.dark .hint-content {
  background-color: rgba(30, 41, 59, 0.95);
  color: #e2e8f0;
  border-color: #334155;
}

.hint-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  display: block;
  animation: sparkle 2s infinite;
}

.hint-content p {
  margin: 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e40af;
}

.hint-subtitle {
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  color: #4b5563 !important;
  margin-bottom: 1rem !important;
}

.dark .hint-content p {
  color: #60a5fa;
}

.dark .hint-subtitle {
  color: #94a3b8 !important;
}

.hint-close-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.dark .hint-close-btn {
  background-color: #60a5fa;
  color: #1e293b;
  box-shadow: 0 4px 6px rgba(96, 165, 250, 0.4);
}

.hint-close-btn:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(59, 130, 246, 0.4);
}

.dark .hint-close-btn:hover {
  background-color: #93c5fd;
  box-shadow: 0 6px 8px rgba(96, 165, 250, 0.5);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 生成中遮罩样式 */
.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 1rem;
}

.generating-spinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #60a5fa;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.generating-text {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  background-color: #f1f5f9;
  background-image:
    radial-gradient(circle at 25px 25px, #e2e8f0 2px, transparent 0),
    radial-gradient(circle at 75px 75px, #e2e8f0 2px, transparent 0),
    radial-gradient(circle at 125px 125px, #e2e8f0 2px, transparent 0);
  background-size: 150px 150px;
}

.dark .image-placeholder {
  background-color: #0f172a;
  background-image:
    radial-gradient(circle at 25px 25px, #334155 2px, transparent 0),
    radial-gradient(circle at 75px 75px, #334155 2px, transparent 0),
    radial-gradient(circle at 125px 125px, #334155 2px, transparent 0);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.dark .placeholder-content {
  background-color: rgba(15, 23, 42, 0.8);
}



.emoji-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.preview-info {
  width: 100%;
  padding: 1.5rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  max-height: 400px;
  border: 1px solid #e2e8f0;
}

.dark .preview-info {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-info-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .preview-info-header {
  border-bottom-color: #334155;
}

.character-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.preview-name {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.dark .preview-name {
  color: #e2e8f0;
}

.character-role-badge {
  padding: 0.35rem 0.75rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
}

.role-protagonist {
  background-color: #3b82f6;
}

.role-friend {
  background-color: #f59e0b;
}

.role-helper {
  background-color: #10b981;
}

.role-opponent {
  background-color: #ef4444;
}

.preview-nickname {
  font-size: 1rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.dark .preview-nickname {
  color: #94a3b8;
}

.preview-traits {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .preview-traits {
  border-bottom-color: #334155;
}

.traits-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.traits-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.traits-icon {
  font-size: 1.25rem;
}

.dark .traits-title {
  color: #94a3b8;
}

.traits-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.trait-badge {
  padding: 0.5rem 0.75rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.35rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.trait-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.personality-badge {
  background-color: #eff6ff;
  color: #3b82f6;
  border: 1px solid #bfdbfe;
}

.appearance-badge {
  background-color: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.dark .personality-badge {
  background-color: #1e40af;
  color: #93c5fd;
  border-color: #3b82f6;
}

.dark .appearance-badge {
  background-color: #14532d;
  color: #86efac;
  border-color: #16a34a;
}

.preview-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.preview-detail-item {
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
}

.preview-detail-item:hover {
  background-color: #f1f5f9;
}

.dark .preview-detail-item {
  background-color: #0f172a;
}

.dark .preview-detail-item:hover {
  background-color: #1e293b;
}

.detail-icon {
  font-size: 1.25rem;
  background-color: white;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .detail-icon {
  background-color: #334155;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.detail-label {
  font-weight: 600;
  color: #64748b;
  min-width: 80px;
}

.dark .detail-label {
  color: #94a3b8;
}

.detail-value {
  color: #1e293b;
  flex: 1;
  font-weight: 500;
}

.dark .detail-value {
  color: #e2e8f0;
}

/* 右侧选项面板 */
.character-options-panel {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  border-radius: 1rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark .character-options-panel {
  background-color: #1e293b;
  border-color: #334155;
}

.options-header {
  padding: 1.5rem;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.dark .options-header {
  background-color: #0f172a;
  border-color: #334155;
}

.options-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.options-icon {
  font-size: 1.5rem;
  color: #3b82f6;
  background-color: #dbeafe;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.dark .options-icon {
  background-color: #1e40af;
  color: #93c5fd;
}

.options-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.dark .options-title {
  color: #e2e8f0;
}

.options-subtitle {
  font-size: 0.95rem;
  color: #64748b;
}

.dark .options-subtitle {
  color: #94a3b8;
}

.options-content {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
}

.option-group {
  margin-bottom: 1rem;
}

.option-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.option-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0;
}

.option-hint {
  font-size: 0.8rem;
  color: #64748b;
  font-style: italic;
}

.dark .option-title {
  color: #e2e8f0;
}

.dark .option-hint {
  color: #94a3b8;
}

.hint {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: normal;
}

.dark .hint {
  color: #94a3b8;
}

.character-name-input,
.character-input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background-color: white;
  color: #1e293b;
  transition: all 0.3s ease;
}

.character-name-input:focus,
.character-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.character-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  background-color: white;
  color: #1e293b;
  transition: all 0.3s ease;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.character-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark .character-name-input,
.dark .character-input,
.dark .character-textarea {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .character-name-input:focus,
.dark .character-input:focus,
.dark .character-textarea:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

/* 选项卡样式 */
.tabs-nav {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  padding: 0 0.5rem;
}

.tabs-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.dark .tabs-nav {
  border-bottom-color: #334155;
}

.tab-item {
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab-item:hover {
  color: #3b82f6;
}

.tab-item.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  font-weight: 600;
}

.tab-icon {
  font-size: 1.25rem;
}

.dark .tab-item {
  color: #94a3b8;
}

.dark .tab-item:hover {
  color: #60a5fa;
}

.dark .tab-item.active {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

.tab-content {
  padding: 0.5rem 1rem 1.5rem 1rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.dark .tab-content {
  background-color: #1e293b;
  border-color: #334155;
}

.option-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.35rem;
  margin-bottom: 0.5rem;
}

.option-btn {
  padding: 0.35rem 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #64748b;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .option-btn {
  background-color: #334155;
  border-color: #475569;
  color: #94a3b8;
}

.option-btn:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.dark .option-btn:hover {
  background-color: #475569;
  color: #e2e8f0;
}

.option-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.dark .option-btn.active {
  background-color: #60a5fa;
  color: #1e293b;
  border-color: #60a5fa;
}

.trait-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* 为不同类别的特点按钮添加不同的样式 */
.trait-btn[class*="basic"] {
  border-left: 3px solid #3b82f6;
}

.trait-btn[class*="face"] {
  border-left: 3px solid #ec4899;
}

.trait-btn[class*="hair"] {
  border-left: 3px solid #8b5cf6;
}

.trait-btn[class*="clothing"] {
  border-left: 3px solid #10b981;
}

.trait-btn[class*="accessories"] {
  border-left: 3px solid #f59e0b;
}

.trait-btn[class*="pose"] {
  border-left: 3px solid #ef4444;
}

.trait-btn[class*="style"] {
  border-left: 3px solid #6366f1;
}

.trait-btn.trait-personality {
  border-left: 3px solid #8b5cf6;
}

.trait-btn.trait-character {
  border-left: 3px solid #3b82f6;
}

.dark .trait-btn[class*="basic"] {
  border-left: 3px solid #60a5fa;
}

.dark .trait-btn[class*="face"] {
  border-left: 3px solid #f472b6;
}

.dark .trait-btn[class*="hair"] {
  border-left: 3px solid #a78bfa;
}

.dark .trait-btn[class*="clothing"] {
  border-left: 3px solid #34d399;
}

.dark .trait-btn[class*="accessories"] {
  border-left: 3px solid #fbbf24;
}

.dark .trait-btn[class*="pose"] {
  border-left: 3px solid #f87171;
}

.dark .trait-btn[class*="style"] {
  border-left: 3px solid #818cf8;
}

.dark .trait-btn.trait-personality {
  border-left: 3px solid #a78bfa;
}

.dark .trait-btn.trait-character {
  border-left: 3px solid #60a5fa;
}

/* 自定义特点样式 */
.trait-btn.custom-trait {
  background-color: #f0f9ff;
  border: 1px dashed #3b82f6;
}

.dark .trait-btn.custom-trait {
  background-color: #0c4a6e;
  border: 1px dashed #60a5fa;
}

/* 特点按钮容器 */
.trait-btn-container {
  position: relative;
  display: inline-block;
}

/* 删除特点按钮 */
.delete-trait-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
  font-size: 12px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.dark .delete-trait-btn {
  background-color: #7f1d1d;
  color: #fca5a5;
  border-color: #b91c1c;
}

.trait-btn-container:hover .delete-trait-btn {
  opacity: 1;
}

.custom-option-container {
  display: flex;
  align-items: center;
  gap: 0.35rem;
  margin-top: 0.25rem;
  width: 100%;
}

.custom-option-input {
  flex: 1;
  padding: 0.35rem 0.5rem;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
  font-size: 0.8rem;
  background-color: white;
  color: #1e293b;
}

.dark .custom-option-input {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.custom-option-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark .custom-option-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

.custom-option-btn {
  padding: 0.35rem 0.5rem;
  border-radius: 0.375rem;
  background-color: #3b82f6;
  color: white;
  font-size: 0.8rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .custom-option-btn {
  background-color: #60a5fa;
  color: #1e293b;
}

.custom-option-btn:hover {
  background-color: #2563eb;
}

.dark .custom-option-btn:hover {
  background-color: #93c5fd;
}

.generate-btn-container {
  position: relative;
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
}

.dark .generate-btn-container {
  border-top-color: #334155;
}

.generate-btn {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.75rem;
  background-color: #8b5cf6;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  z-index: 0;
}

.dark .generate-btn {
  background-color: #a78bfa;
  color: #1e293b;
  box-shadow: 0 8px 16px rgba(167, 139, 250, 0.3);
}

.generate-btn:hover {
  background-color: #7c3aed;
  transform: translateY(-2px);
  box-shadow: 0 12px 20px rgba(139, 92, 246, 0.4);
}

.dark .generate-btn:hover {
  background-color: #c4b5fd;
  box-shadow: 0 12px 20px rgba(167, 139, 250, 0.4);
}

.generate-icon {
  font-size: 1.25rem;
}

.generate-text {
  font-weight: 600;
}

@media (max-width: 768px) {
  .builder-layout {
    flex-direction: column;
    gap: 1rem;
  }

  .character-preview-panel,
  .character-options-panel {
    width: 100%;
  }

  .preview-image {
    height: 200px;
  }

  .preview-content,
  .options-content {
    padding: 0.75rem;
  }

  .option-group {
    margin-bottom: 1rem;
  }

  .option-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .option-buttons {
    gap: 0.35rem;
  }

  .option-btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }

  .generate-btn {
    padding: 0.6rem;
    font-size: 0.9rem;
  }

  .role-selector-options {
    flex-wrap: wrap;
  }

  .role-option {
    flex: 1 0 40%;
    margin-bottom: 0.5rem;
  }

  .role-icon {
    font-size: 1.25rem;
  }

  .role-label {
    font-size: 0.8rem;
  }

  .character-role-selector {
    padding: 0.75rem;
  }

  /* 移动端选项卡样式 */
  .tabs-nav {
    margin-bottom: 1rem;
  }

  .tab-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .preview-info {
    max-height: 300px;
  }

  .traits-section {
    gap: 0.25rem;
  }

  .traits-badges {
    gap: 0.35rem;
  }

  .trait-badge {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
  }

  .preview-detail-item {
    font-size: 0.8rem;
  }

  .detail-label {
    min-width: 70px;
  }

  .character-textarea {
    min-height: 80px;
  }
}
</style>
