<script setup lang="ts">
import { ref, watch } from 'vue';
import { NInput, NButton } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  activeCategory: {
    type: String,
    default: 'all'
  }
});

const emit = defineEmits(['category-change', 'search']);

const { isMobile } = useBasicLayout();
const searchQuery = ref('');

// 处理分类点击
const handleCategoryClick = (value: string) => {
  emit('category-change', value);
};

// 处理搜索
const handleSearch = () => {
  emit('search', searchQuery.value);
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  emit('search', '');
};

// 监听搜索框变化，当清空时自动触发搜索
watch(searchQuery, (newValue) => {
  if (newValue === '') {
    emit('search', '');
  }
});
</script>

<template>
  <div class="kid-category-filter">
    <div class="filter-container">
      <!-- 分类选择 -->
      <div class="categories-container">
        <div 
          v-for="category in categories" 
          :key="category.value"
          class="category-item"
          :class="{ 'active': activeCategory === category.value }"
          @click="handleCategoryClick(category.value)"
        >
          <div class="category-icon" v-if="category.value === 'all'">🌈</div>
          <div class="category-icon" v-else-if="category.label.includes('绘本')">📚</div>
          <div class="category-icon" v-else-if="category.label.includes('编程')">🎮</div>
          <div class="category-icon" v-else-if="category.label.includes('音乐')">🎵</div>
          <div class="category-icon" v-else>🎨</div>
          <span class="category-label">{{ category.label }}</span>
        </div>
      </div>
      
      <!-- 搜索框 -->
      <div class="search-container">
        <NInput
          v-model:value="searchQuery"
          placeholder="搜索你喜欢的作品..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <div class="search-icon">🔍</div>
          </template>
          <template #suffix>
            <div v-if="searchQuery" class="clear-icon" @click="clearSearch">❌</div>
          </template>
        </NInput>
        <NButton type="primary" class="search-button" @click="handleSearch">
          <span class="button-text">搜索</span>
        </NButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.kid-category-filter {
  margin-bottom: 2rem;
}

.filter-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.categories-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  background-color: white;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
}

.category-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.category-item.active {
  background: linear-gradient(90deg, #FF9A9E, #FFAFBD);
  color: white;
  border-color: white;
  box-shadow: 0 6px 12px rgba(255, 154, 158, 0.3);
}

.category-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.category-label {
  font-weight: 700;
  font-size: 1rem;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.search-container {
  display: flex;
  gap: 1rem;
}

.search-input {
  flex: 1;
  border-radius: 50px;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.search-icon {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

.clear-icon {
  font-size: 1rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.clear-icon:hover {
  opacity: 1;
}

.search-button {
  border-radius: 50px;
  padding: 0 1.5rem;
  background: linear-gradient(90deg, #FF9A9E, #FFAFBD);
  border: none;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  font-weight: 700;
  box-shadow: 0 4px 8px rgba(255, 154, 158, 0.3);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.search-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(255, 154, 158, 0.4);
}

.button-text {
  font-size: 1rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-container {
    gap: 1rem;
  }
  
  .categories-container {
    overflow-x: auto;
    padding-bottom: 0.5rem;
    flex-wrap: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  .categories-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  
  .category-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
  }
  
  .category-icon {
    font-size: 1.25rem;
    margin-right: 0.5rem;
  }
  
  .category-label {
    font-size: 0.9rem;
  }
  
  .search-container {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .search-button {
    width: 100%;
  }
}
</style>
