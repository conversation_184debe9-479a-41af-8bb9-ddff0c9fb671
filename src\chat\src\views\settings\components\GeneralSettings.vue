<template>
  <div class="general-settings">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">通用设置</h1>
      <p class="page-description">管理您的基本偏好设置</p>
    </div>

    <!-- 主题设置 -->
    <NCard class="setting-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:palette-line" class="card-icon" />
          <span>主题设置</span>
        </div>
      </template>
      
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">深色模式</div>
          <div class="setting-desc">切换到深色主题以减少眼部疲劳</div>
        </div>
        <div class="setting-control">
          <NSwitch
            v-model:value="isDarkMode"
            @update:value="toggleTheme"
            size="large"
          >
            <template #checked-icon>
              <SvgIcon icon="ri:moon-line" />
            </template>
            <template #unchecked-icon>
              <SvgIcon icon="ri:sun-line" />
            </template>
          </NSwitch>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">主题色彩</div>
          <div class="setting-desc">选择您喜欢的主题色彩</div>
        </div>
        <div class="setting-control">
          <div class="color-picker">
            <button
              v-for="color in themeColors"
              :key="color.name"
              :class="[
                'color-option',
                { 'active': currentThemeColor === color.name }
              ]"
              :style="{ backgroundColor: color.value }"
              @click="changeThemeColor(color.name)"
              :title="color.label"
            />
          </div>
        </div>
      </div>
    </NCard>

    <!-- 语言设置 -->
    <NCard class="setting-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:translate-2" class="card-icon" />
          <span>语言设置</span>
        </div>
      </template>
      
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">界面语言</div>
          <div class="setting-desc">选择您的首选语言</div>
        </div>
        <div class="setting-control">
          <NSelect
            v-model:value="currentLanguage"
            :options="languageOptions"
            @update:value="changeLanguage"
            style="width: 200px"
          />
        </div>
      </div>
    </NCard>

    <!-- 界面设置 -->
    <NCard class="setting-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:layout-line" class="card-icon" />
          <span>界面设置</span>
        </div>
      </template>
      
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">侧边栏自动收起</div>
          <div class="setting-desc">在移动设备上自动收起侧边栏</div>
        </div>
        <div class="setting-control">
          <NSwitch
            v-model:value="autoCollapseSidebar"
            @update:value="updateAutoCollapse"
          />
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">动画效果</div>
          <div class="setting-desc">启用界面过渡动画效果</div>
        </div>
        <div class="setting-control">
          <NSwitch
            v-model:value="enableAnimations"
            @update:value="updateAnimations"
          />
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">紧凑模式</div>
          <div class="setting-desc">使用更紧凑的界面布局</div>
        </div>
        <div class="setting-control">
          <NSwitch
            v-model:value="compactMode"
            @update:value="updateCompactMode"
          />
        </div>
      </div>
    </NCard>

    <!-- 学生模式快捷切换 -->
    <NCard class="setting-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:user-settings-line" class="card-icon" />
          <span>模式切换</span>
        </div>
      </template>
      
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-label">当前模式</div>
          <div class="setting-desc">{{ userRole === 'teacher' ? '教师模式 - 适合教学和管理' : '学生模式 - 适合学习和创作' }}</div>
        </div>
        <div class="setting-control">
          <NButton
            type="primary"
            @click="switchRole"
            :loading="switchingRole"
          >
            <template #icon>
              <SvgIcon icon="ri:refresh-line" />
            </template>
            切换到{{ userRole === 'teacher' ? '学生' : '教师' }}模式
          </NButton>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NSwitch, NSelect, NButton, useMessage } from 'naive-ui';
import { useAppStore, useAuthStore } from '@/store';
import { SvgIcon } from '@/components/common';

const router = useRouter();
const appStore = useAppStore();
const authStore = useAuthStore();
const message = useMessage();

// 主题相关
const isDarkMode = computed({
  get: () => appStore.theme === 'dark',
  set: (value: boolean) => {
    appStore.setTheme(value ? 'dark' : 'light');
  }
});

const toggleTheme = (value: boolean) => {
  const mode = value ? 'dark' : 'light';
  appStore.setTheme(mode);
  message.success(`已切换到${mode === 'dark' ? '深色' : '浅色'}模式`);
};

// 主题色彩
const themeColors = [
  { name: 'blue', label: '蓝色', value: '#2080f0' },
  { name: 'green', label: '绿色', value: '#18a058' },
  { name: 'purple', label: '紫色', value: '#722ed1' },
  { name: 'orange', label: '橙色', value: '#fa8c16' },
  { name: 'red', label: '红色', value: '#f5222d' }
];

const currentThemeColor = ref('blue');

const changeThemeColor = (color: string) => {
  currentThemeColor.value = color;
  message.success(`已切换到${themeColors.find(c => c.name === color)?.label}主题`);
};

// 语言设置
const languageOptions = [
  { label: '简体中文', value: 'zh-CN' },
  { label: '繁體中文', value: 'zh-TW' },
  { label: 'English', value: 'en-US' }
];

const currentLanguage = ref('zh-CN');

const changeLanguage = (lang: string) => {
  currentLanguage.value = lang;
  message.success('语言设置已更新');
};

// 界面设置
const autoCollapseSidebar = ref(true);
const enableAnimations = ref(true);
const compactMode = ref(false);

const updateAutoCollapse = (value: boolean) => {
  autoCollapseSidebar.value = value;
  message.success(`侧边栏自动收起已${value ? '启用' : '禁用'}`);
};

const updateAnimations = (value: boolean) => {
  enableAnimations.value = value;
  message.success(`动画效果已${value ? '启用' : '禁用'}`);
};

const updateCompactMode = (value: boolean) => {
  compactMode.value = value;
  message.success(`紧凑模式已${value ? '启用' : '禁用'}`);
};

// 角色切换
const userRole = computed(() => authStore.userInfo?.role || 'student');
const switchingRole = ref(false);

const switchRole = async () => {
  switchingRole.value = true;
  try {
    const newRole = userRole.value === 'teacher' ? 'student' : 'teacher';
    const targetPath = newRole === 'teacher' ? '/teacher' : '/student';
    
    authStore.setUserRole(newRole);
    message.success(`已切换为${newRole === 'teacher' ? '教师' : '学生'}模式`);
    
    setTimeout(() => {
      router.push(targetPath);
    }, 1000);
  } finally {
    switchingRole.value = false;
  }
};
</script>

<style scoped>
.general-settings {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.setting-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply flex items-center space-x-2 text-gray-900 dark:text-gray-100;
}

.card-icon {
  @apply text-lg text-primary-600;
}

.setting-item {
  @apply flex items-center justify-between py-4;
}

.setting-item:not(:last-child) {
  @apply border-b border-gray-100 dark:border-gray-700;
}

.setting-info {
  @apply flex-1;
}

.setting-label {
  @apply text-base font-medium text-gray-900 dark:text-gray-100 mb-1;
}

.setting-desc {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.setting-control {
  @apply flex-shrink-0;
}

.color-picker {
  @apply flex space-x-2;
}

.color-option {
  @apply w-8 h-8 rounded-full border-2 border-gray-200 dark:border-gray-600 transition-all duration-200;
  @apply hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.color-option.active {
  @apply border-white shadow-lg ring-2 ring-primary-500;
}
</style>
