<template>
  <div class="student-mode">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">学生模式</h1>
      <p class="page-description">在教师模式和学生模式之间切换</p>
    </div>

    <!-- 当前模式状态 -->
    <NCard class="current-mode-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:user-settings-line" class="card-icon" />
          <span>当前模式</span>
        </div>
      </template>
      
      <div class="mode-status">
        <div class="mode-info">
          <div class="mode-icon-wrapper" :class="modeConfig.bgClass">
            <SvgIcon :icon="modeConfig.icon" class="mode-icon" :class="modeConfig.iconClass" />
          </div>
          <div class="mode-details">
            <div class="mode-name">{{ modeConfig.name }}</div>
            <div class="mode-description">{{ modeConfig.description }}</div>
          </div>
        </div>
        
        <NButton
          type="primary"
          size="large"
          @click="switchRole"
          :loading="switching"
        >
          <template #icon>
            <SvgIcon icon="ri:refresh-line" />
          </template>
          切换到{{ targetMode.name }}
        </NButton>
      </div>
    </NCard>

    <!-- 模式对比 -->
    <div class="mode-comparison">
      <div class="comparison-header">
        <h2 class="comparison-title">模式对比</h2>
        <p class="comparison-desc">了解不同模式的特点和功能</p>
      </div>
      
      <div class="comparison-grid">
        <!-- 教师模式 -->
        <NCard class="mode-card teacher-mode">
          <template #header>
            <div class="mode-card-header">
              <SvgIcon icon="ri:graduation-cap-line" class="mode-card-icon teacher" />
              <span>教师模式</span>
            </div>
          </template>
          
          <div class="mode-features">
            <div class="feature-item">
              <SvgIcon icon="ri:chat-3-line" class="feature-icon" />
              <span>AI聊天助手</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:tools-line" class="feature-icon" />
              <span>教学工具</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:group-line" class="feature-icon" />
              <span>班级管理</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:bar-chart-line" class="feature-icon" />
              <span>数据分析</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:settings-3-line" class="feature-icon" />
              <span>高级设置</span>
            </div>
          </div>
          
          <div class="mode-target">
            <strong>适合：</strong>教师、教育工作者、管理员
          </div>
        </NCard>

        <!-- 学生模式 -->
        <NCard class="mode-card student-mode">
          <template #header>
            <div class="mode-card-header">
              <SvgIcon icon="ri:user-smile-line" class="mode-card-icon student" />
              <span>学生模式</span>
            </div>
          </template>
          
          <div class="mode-features">
            <div class="feature-item">
              <SvgIcon icon="ri:book-open-line" class="feature-icon" />
              <span>AI绘本创作</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:palette-line" class="feature-icon" />
              <span>创意工具</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:gamepad-line" class="feature-icon" />
              <span>趣味学习</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:heart-line" class="feature-icon" />
              <span>简化界面</span>
            </div>
            <div class="feature-item">
              <SvgIcon icon="ri:shield-check-line" class="feature-icon" />
              <span>安全保护</span>
            </div>
          </div>
          
          <div class="mode-target">
            <strong>适合：</strong>学生、儿童、创作爱好者
          </div>
        </NCard>
      </div>
    </div>

    <!-- 切换提示 -->
    <NCard class="tips-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:lightbulb-line" class="card-icon" />
          <span>切换提示</span>
        </div>
      </template>
      
      <div class="tips-content">
        <div class="tip-item">
          <SvgIcon icon="ri:information-line" class="tip-icon info" />
          <span>切换模式后，界面布局和可用功能会相应调整</span>
        </div>
        <div class="tip-item">
          <SvgIcon icon="ri:save-line" class="tip-icon success" />
          <span>您的数据和设置在模式切换后会自动保存</span>
        </div>
        <div class="tip-item">
          <SvgIcon icon="ri:time-line" class="tip-icon warning" />
          <span>模式切换可能需要几秒钟时间，请耐心等待</span>
        </div>
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NButton, useMessage } from 'naive-ui';
import { useAuthStore } from '@/store';
import { SvgIcon } from '@/components/common';

const router = useRouter();
const authStore = useAuthStore();
const message = useMessage();

// 状态管理
const switching = ref(false);

// 计算属性
const currentRole = computed(() => authStore.userInfo?.role || 'student');

const modeConfig = computed(() => {
  const configs = {
    teacher: {
      name: '教师模式',
      description: '专为教育工作者设计，提供丰富的教学工具和管理功能',
      icon: 'ri:graduation-cap-line',
      bgClass: 'bg-blue-50 dark:bg-blue-900/20',
      iconClass: 'text-blue-600'
    },
    student: {
      name: '学生模式',
      description: '专为学生设计，提供简洁友好的学习和创作环境',
      icon: 'ri:user-smile-line',
      bgClass: 'bg-green-50 dark:bg-green-900/20',
      iconClass: 'text-green-600'
    }
  };
  return configs[currentRole.value] || configs.student;
});

const targetMode = computed(() => {
  const configs = {
    teacher: {
      name: '学生模式',
      path: '/student'
    },
    student: {
      name: '教师模式',
      path: '/teacher'
    }
  };
  return configs[currentRole.value] || configs.student;
});

// 方法
const switchRole = async () => {
  switching.value = true;
  try {
    const newRole = currentRole.value === 'teacher' ? 'student' : 'teacher';
    
    // 更新用户角色
    authStore.setUserRole(newRole);
    
    message.success(`正在切换到${newRole === 'teacher' ? '教师' : '学生'}模式...`);
    
    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push(targetMode.value.path);
    }, 1500);
  } catch (error) {
    message.error('切换失败，请重试');
  } finally {
    setTimeout(() => {
      switching.value = false;
    }, 1500);
  }
};
</script>

<style scoped>
.student-mode {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.current-mode-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply flex items-center space-x-2 text-gray-900 dark:text-gray-100;
}

.card-icon {
  @apply text-lg text-primary-600;
}

.mode-status {
  @apply flex items-center justify-between;
}

.mode-info {
  @apply flex items-center space-x-4;
}

.mode-icon-wrapper {
  @apply w-16 h-16 rounded-xl flex items-center justify-center;
}

.mode-icon {
  @apply text-2xl;
}

.mode-details {
  @apply flex-1;
}

.mode-name {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100 mb-1;
}

.mode-description {
  @apply text-gray-600 dark:text-gray-400;
}

.mode-comparison {
  @apply space-y-6;
}

.comparison-header {
  @apply text-center;
}

.comparison-title {
  @apply text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2;
}

.comparison-desc {
  @apply text-gray-600 dark:text-gray-400;
}

.comparison-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.mode-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.mode-card.teacher-mode {
  @apply border-blue-200 dark:border-blue-800;
}

.mode-card.student-mode {
  @apply border-green-200 dark:border-green-800;
}

.mode-card-header {
  @apply flex items-center space-x-2;
}

.mode-card-icon {
  @apply text-lg;
}

.mode-card-icon.teacher {
  @apply text-blue-600;
}

.mode-card-icon.student {
  @apply text-green-600;
}

.mode-features {
  @apply space-y-3 mb-4;
}

.feature-item {
  @apply flex items-center space-x-3 text-gray-700 dark:text-gray-300;
}

.feature-icon {
  @apply text-base text-gray-500 dark:text-gray-400;
}

.mode-target {
  @apply text-sm text-gray-600 dark:text-gray-400 pt-4 border-t border-gray-100 dark:border-gray-700;
}

.tips-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.tips-content {
  @apply space-y-3;
}

.tip-item {
  @apply flex items-center space-x-3;
}

.tip-icon {
  @apply text-lg flex-shrink-0;
}

.tip-icon.info {
  @apply text-blue-500;
}

.tip-icon.success {
  @apply text-green-500;
}

.tip-icon.warning {
  @apply text-yellow-500;
}
</style>
