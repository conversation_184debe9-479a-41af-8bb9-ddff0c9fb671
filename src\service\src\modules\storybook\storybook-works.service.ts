import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Between, MoreThanOrEqual, <PERSON>Than<PERSON>rEqual, <PERSON>Null, In } from 'typeorm';
import { StorybookEntity, StorybookFolderEntity } from './entities';
import {
  CreateStorybookDto,
  UpdateStorybookDto,
  QueryStorybookDto,
  BatchOperationDto,
  CreateFolderDto,
  UpdateFolderDto,
  QueryFolderDto,
  MoveToFolderDto
} from './dto';

/**
 * 绘本作品管理服务
 */
@Injectable()
export class StorybookWorksService {
  private readonly logger = new Logger(StorybookWorksService.name);

  constructor(
    @InjectRepository(StorybookEntity)
    private readonly storybookRepository: Repository<StorybookEntity>,

    @InjectRepository(StorybookFolderEntity)
    private readonly folderRepository: Repository<StorybookFolderEntity>,
  ) {}

  // ==================== 文件夹管理 ====================

  /**
   * 创建文件夹
   * @param userId 用户ID
   * @param createFolderDto 创建文件夹DTO
   * @returns 创建的文件夹
   */
  async createFolder(userId: number, createFolderDto: CreateFolderDto): Promise<StorybookFolderEntity> {
    // 创建文件夹
    const folder = this.folderRepository.create({
      ...createFolderDto,
      userId
    });

    return this.folderRepository.save(folder);
  }

  /**
   * 获取用户文件夹列表
   * @param userId 用户ID
   * @param queryFolderDto 查询参数
   * @returns 文件夹列表和总数
   */
  async getUserFolders(userId: number, queryFolderDto: QueryFolderDto = {}): Promise<{ items: StorybookFolderEntity[], total: number }> {
    const { page = 1, size = 10, keyword } = queryFolderDto;

    const queryBuilder = this.folderRepository.createQueryBuilder('folder')
      .where('folder.userId = :userId', { userId });

    if (keyword) {
      queryBuilder.andWhere('(folder.name LIKE :keyword OR folder.description LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy('folder.order', 'ASC')
      .addOrderBy('folder.updatedAt', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getMany();

    return { items, total };
  }

  /**
   * 获取文件夹详情
   * @param id 文件夹ID
   * @param userId 用户ID
   * @returns 文件夹详情
   */
  async getFolderDetail(id: number, userId: number): Promise<StorybookFolderEntity> {
    const folder = await this.folderRepository.findOne({
      where: { id }
    });

    if (!folder) {
      throw new NotFoundException('文件夹不存在');
    }

    // 检查权限
    if (folder.userId !== userId) {
      throw new BadRequestException('无权访问此文件夹');
    }

    return folder;
  }

  /**
   * 更新文件夹
   * @param id 文件夹ID
   * @param userId 用户ID
   * @param updateFolderDto 更新文件夹DTO
   * @returns 更新后的文件夹
   */
  async updateFolder(id: number, userId: number, updateFolderDto: UpdateFolderDto): Promise<StorybookFolderEntity> {
    const folder = await this.folderRepository.findOne({
      where: { id }
    });

    if (!folder) {
      throw new NotFoundException('文件夹不存在');
    }

    // 检查权限
    if (folder.userId !== userId) {
      throw new BadRequestException('无权修改此文件夹');
    }

    // 更新文件夹
    Object.assign(folder, updateFolderDto);
    return this.folderRepository.save(folder);
  }

  /**
   * 删除文件夹
   * @param id 文件夹ID
   * @param userId 用户ID
   */
  async deleteFolder(id: number, userId: number): Promise<void> {
    const folder = await this.folderRepository.findOne({
      where: { id }
    });

    if (!folder) {
      throw new NotFoundException('文件夹不存在');
    }

    // 检查权限
    if (folder.userId !== userId) {
      throw new BadRequestException('无权删除此文件夹');
    }

    // 检查是否为系统文件夹
    if (folder.isSystem === 1) {
      throw new BadRequestException('系统文件夹不能删除');
    }

    // 将该文件夹下的作品移到默认文件夹
    await this.storybookRepository.update(
      { folderId: id, userId },
      { folderId: null }
    );

    // 删除文件夹
    await this.folderRepository.remove(folder);
  }

  // ==================== 作品管理 ====================

  /**
   * 获取用户作品列表
   * @param userId 用户ID
   * @param queryStorybookDto 查询参数
   * @returns 作品列表和总数
   */
  async getUserStorybooks(userId: number, queryStorybookDto: QueryStorybookDto = {}): Promise<{ items: StorybookEntity[], total: number }> {
    const {
      page = 1,
      size = 10,
      keyword,
      status,
      folderId,
      isDeleted = 0,
      sortField = 'updatedAt',
      sortOrder = 'DESC',
      recentOnly = false
    } = queryStorybookDto;

    const queryBuilder = this.storybookRepository.createQueryBuilder('storybook')
      .where('storybook.userId = :userId', { userId })
      .andWhere('storybook.isDeleted = :isDeleted', { isDeleted });

    if (status !== undefined) {
      queryBuilder.andWhere('storybook.status = :status', { status });
    }

    if (folderId !== undefined) {
      if (folderId === 0) {
        // 查询未分类的作品
        queryBuilder.andWhere('storybook.folderId IS NULL');
      } else {
        queryBuilder.andWhere('storybook.folderId = :folderId', { folderId });
      }
    }

    if (keyword) {
      queryBuilder.andWhere('(storybook.title LIKE :keyword OR storybook.description LIKE :keyword)',
        { keyword: `%${keyword}%` });
    }

    if (recentOnly) {
      // 获取最近7天编辑的作品
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      queryBuilder.andWhere('storybook.lastEditedAt >= :sevenDaysAgo', { sevenDaysAgo });
    }

    const total = await queryBuilder.getCount();

    const items = await queryBuilder
      .orderBy(`storybook.${sortField}`, sortOrder)
      .skip((page - 1) * size)
      .take(size)
      .getMany();

    return { items, total };
  }

  /**
   * 批量操作作品
   * @param userId 用户ID
   * @param batchOperationDto 批量操作DTO
   */
  async batchOperation(userId: number, batchOperationDto: BatchOperationDto): Promise<void> {
    const { storybookIds, operation, targetFolderId } = batchOperationDto;

    if (storybookIds.length === 0) {
      throw new BadRequestException('作品ID列表不能为空');
    }

    // 检查作品是否存在且属于当前用户
    const storybooks = await this.storybookRepository.find({
      where: {
        id: In(storybookIds),
        userId
      }
    });

    if (storybooks.length !== storybookIds.length) {
      throw new BadRequestException('部分作品不存在或无权操作');
    }

    switch (operation) {
      case 'delete':
        // 移到回收站
        await this.storybookRepository.update(
          { id: In(storybookIds), userId },
          {
            isDeleted: 1,
            deletedAt: new Date()
          }
        );
        break;

      case 'restore':
        // 从回收站恢复
        await this.storybookRepository.update(
          { id: In(storybookIds), userId },
          {
            isDeleted: 0,
            deletedAt: null
          }
        );
        break;

      case 'move':
        // 移动到指定文件夹
        if (targetFolderId !== undefined && targetFolderId !== 0) {
          // 检查目标文件夹是否存在
          const targetFolder = await this.folderRepository.findOne({
            where: { id: targetFolderId, userId }
          });

          if (!targetFolder) {
            throw new BadRequestException('目标文件夹不存在');
          }
        }

        await this.storybookRepository.update(
          { id: In(storybookIds), userId },
          {
            folderId: targetFolderId === 0 ? null : targetFolderId
          }
        );
        break;

      default:
        throw new BadRequestException('不支持的操作类型');
    }
  }

  /**
   * 永久删除作品
   * @param id 作品ID
   * @param userId 用户ID
   */
  async permanentlyDeleteStorybook(id: number, userId: number): Promise<void> {
    const storybook = await this.storybookRepository.findOne({
      where: { id, userId, isDeleted: 1 }
    });

    if (!storybook) {
      throw new NotFoundException('作品不存在或不在回收站中');
    }

    // 永久删除作品
    await this.storybookRepository.remove(storybook);
  }

  /**
   * 清空回收站
   * @param userId 用户ID
   */
  async emptyTrash(userId: number): Promise<void> {
    const storybooks = await this.storybookRepository.find({
      where: { userId, isDeleted: 1 }
    });

    if (storybooks.length > 0) {
      await this.storybookRepository.remove(storybooks);
    }
  }

  /**
   * 创建绘本
   * @param userId 用户ID
   * @param createStorybookDto 创建绘本DTO
   * @returns 创建的绘本
   */
  async createStorybook(userId: number, createStorybookDto: CreateStorybookDto): Promise<StorybookEntity> {
    this.logger.log(`开始创建绘本: userId=${userId}, title="${createStorybookDto.title}"`);

    // 设置默认值
    if (createStorybookDto.status === undefined) {
      createStorybookDto.status = 0; // 默认为草稿状态
    }

    if (createStorybookDto.isPublic === undefined) {
      createStorybookDto.isPublic = 0; // 默认为私有
    }

    if (createStorybookDto.source === undefined) {
      createStorybookDto.source = 'storybook'; // 默认来源
    }

    // 创建新绘本
    const storybook = this.storybookRepository.create({
      ...createStorybookDto,
      userId
    });

    try {
      const savedStorybook = await this.storybookRepository.save(storybook);
      this.logger.log(`创建绘本成功: ID=${savedStorybook.id}, 标题="${savedStorybook.title}"`);
      return savedStorybook;
    } catch (error) {
      this.logger.error(`创建绘本失败: ${error.message}`, error.stack);
      throw new BadRequestException('创建绘本失败，请稍后重试');
    }
  }

  /**
   * 获取绘本详情
   * @param id 绘本ID
   * @param userId 用户ID
   * @returns 绘本详情
   */
  async getStorybookDetail(id: number, userId: number): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查权限（如果不是公开的绘本，则只有作者可以查看）
    if (storybook.userId !== userId && storybook.isPublic !== 1) {
      throw new BadRequestException('无权查看此绘本');
    }

    return storybook;
  }

  /**
   * 更新绘本
   * @param id 绘本ID
   * @param userId 用户ID
   * @param updateStorybookDto 更新绘本DTO
   * @returns 更新后的绘本
   */
  async updateStorybook(id: number, userId: number, updateStorybookDto: UpdateStorybookDto): Promise<StorybookEntity> {
    const storybook = await this.storybookRepository.findOne({
      where: { id }
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 检查权限
    if (storybook.userId !== userId) {
      throw new BadRequestException('无权修改此绘本');
    }

    // 更新绘本
    Object.assign(storybook, updateStorybookDto);

    // 更新最后编辑时间
    storybook.lastEditedAt = new Date();

    try {
      const updatedStorybook = await this.storybookRepository.save(storybook);
      this.logger.log(`更新绘本成功: ID=${updatedStorybook.id}, 标题="${updatedStorybook.title}"`);
      return updatedStorybook;
    } catch (error) {
      this.logger.error(`更新绘本失败: ${error.message}`, error.stack);
      throw new BadRequestException('更新绘本失败，请稍后重试');
    }
  }
}
