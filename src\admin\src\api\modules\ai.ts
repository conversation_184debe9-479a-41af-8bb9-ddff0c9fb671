import api from '../index';

/**
 * AI服务API
 */
export default {
  /**
   * 获取模型列表
   * @returns 模型列表
   */
  getModelsList() {
    return api.get('models/list');
  },

  /**
   * 生成内容
   * @param data 请求数据
   * @returns 生成的内容
   */
  generateContent(data: {
    prompt: string;
    systemMessage?: string;
    model?: string;
    temperature?: number;
    stream?: boolean;
  }) {
    // 构建消息历史
    const messages = [];

    // 如果有系统消息，添加系统消息
    if (data.systemMessage) {
      messages.push({
        role: 'system',
        content: data.systemMessage
      });
    }

    // 添加用户消息
    messages.push({
      role: 'user',
      content: data.prompt
    });

    // 使用现有的聊天API
    // 添加日志输出，查看请求参数
    console.log('发送的请求参数：', {
      messages,
      model: data.model,
      temperature: data.temperature || 0.3,
      stream: data.stream || false
    });

    // 直接使用messages数组，确保系统提示词被正确传递
    return api.post('chatgpt/direct-completion', {
      messages,
      model: data.model,
      temperature: data.temperature || 0.3,
      stream: data.stream || false
    });
  }
};
