<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { NSpace } from 'naive-ui';

// 导入拆分后的组件
import StoryOutlineHeader from './outline/StoryOutlineHeader.vue';
import StoryBasicInfo from './outline/StoryBasicInfo.vue';
import StoryThemeSelector from './outline/StoryThemeSelector.vue';
import StorySettingSelector from './outline/StorySettingSelector.vue';
import StoryStructureEditor from './outline/StoryStructureEditor.vue';
import StoryNotesEditor from './outline/StoryNotesEditor.vue';
import StorySaveActions from './outline/StorySaveActions.vue';
import StoryExampleLoader from './outline/StoryExampleLoader.vue';

const props = defineProps({
  projectData: {
    type: Object,
    required: true
  }
});

// 故事大纲数据
const outlineData = reactive({
  title: '',
  mainIdea: '',
  theme: '',
  ageGroup: '',
  // 故事设定
  setting: {
    place: '',
    time: '',
    background: ''
  },
  // 角色设定
  characters: [
    { name: '', traits: '', role: '主角' }
  ],
  // 故事情节
  beginning: '',
  middle: '',
  climax: '',
  ending: '',
  notes: ''
});

// 标记是否正在从项目数据同步，避免循环同步
let isSyncingFromProject = false;

// 监听outlineData变化，自动同步到项目数据
watch(outlineData, () => {
  // 如果正在从项目数据同步，则不触发同步
  if (isSyncingFromProject) {
    console.log('Skipping sync because data is being synced from project');
    return;
  }

  console.log('Local outline data changed, syncing to project');

  // 更新项目数据
  props.projectData.title = outlineData.title;

  // 确保outline对象存在
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }

  // 同步基本字段到项目数据
  props.projectData.outline.mainIdea = outlineData.mainIdea;
  props.projectData.outline.theme = outlineData.theme;
  props.projectData.outline.ageGroup = outlineData.ageGroup;

  // 同步故事情节到项目数据
  props.projectData.outline.beginning = outlineData.beginning;
  props.projectData.outline.middle = outlineData.middle;
  props.projectData.outline.climax = outlineData.climax;
  props.projectData.outline.ending = outlineData.ending;

  // 同步故事设定到项目数据
  props.projectData.outline.setting = JSON.parse(JSON.stringify(outlineData.setting));

  // 同步角色设定到项目数据
  // 确保角色数据是数组格式
  if (Array.isArray(outlineData.characters)) {
    props.projectData.outline.characters = JSON.parse(JSON.stringify(outlineData.characters));
  } else {
    // 如果不是数组，创建一个空数组
    props.projectData.outline.characters = [];
    console.warn('角色数据不是数组格式，已重置为空数组');
  }

  // 同步备注到项目数据
  props.projectData.notes = outlineData.notes;
}, { deep: true });

// 监听项目outline变化，自动同步到本地数据
watch(() => props.projectData.outline, (newOutline) => {
  if (newOutline && !isSyncingFromProject) {
    console.log('Project outline changed, syncing to local data');

    // 标记正在从项目数据同步，避免循环同步
    isSyncingFromProject = true;

    try {
      // 同步数据到本地
      outlineData.mainIdea = newOutline.mainIdea || outlineData.mainIdea;
      outlineData.theme = newOutline.theme || outlineData.theme;
      outlineData.ageGroup = newOutline.ageGroup || outlineData.ageGroup;

      // 同步故事情节
      outlineData.beginning = newOutline.beginning || outlineData.beginning;
      outlineData.middle = newOutline.middle || outlineData.middle;
      outlineData.climax = newOutline.climax || outlineData.climax;
      outlineData.ending = newOutline.ending || outlineData.ending;

      // 同步故事设定
      if (newOutline.setting) {
        // 深拷贝以避免引用问题
        outlineData.setting = JSON.parse(JSON.stringify(newOutline.setting));
      }

      // 同步角色设定
      if (newOutline.characters) {
        if (Array.isArray(newOutline.characters) && newOutline.characters.length > 0) {
          // 深拷贝以避免引用问题
          outlineData.characters = JSON.parse(JSON.stringify(newOutline.characters));
          console.log('从项目数据同步角色数组:', outlineData.characters);
        } else if (typeof newOutline.characters === 'object' && !Array.isArray(newOutline.characters)) {
          // 如果是对象，尝试转换为数组
          try {
            const charactersArray = Object.values(newOutline.characters);
            if (charactersArray.length > 0) {
              outlineData.characters = charactersArray;
              console.log('从项目数据同步角色对象转换为数组:', charactersArray);
            }
          } catch (error) {
            console.error('转换角色对象时出错:', error);
          }
        }
      }

      console.log('Successfully synced project outline to local data');
    } finally {
      // 确保无论如何都会重置标记
      setTimeout(() => {
        isSyncingFromProject = false;
        console.log('Reset syncing flag');
      }, 100);
    }
  }
}, { deep: true });

// 监听项目标题变化
watch(() => props.projectData.title, (newTitle) => {
  if (newTitle && newTitle !== outlineData.title) {
    outlineData.title = newTitle;
  }
});

// 监听项目笔记变化
watch(() => props.projectData.notes, (newNotes) => {
  if (newNotes && newNotes !== outlineData.notes) {
    outlineData.notes = newNotes;
  }
});

// 初始化大纲数据
const initializeOutlineData = () => {
  // 如果项目已有数据，使用它
  if (props.projectData.title) {
    outlineData.title = props.projectData.title;
  }

  if (props.projectData.outline) {
    // 加载基本字段
    outlineData.mainIdea = props.projectData.outline.mainIdea || '';
    outlineData.theme = props.projectData.outline.theme || '';
    outlineData.ageGroup = props.projectData.outline.ageGroup || '';

    // 加载故事情节
    outlineData.beginning = props.projectData.outline.beginning || '';
    outlineData.middle = props.projectData.outline.middle || '';
    outlineData.climax = props.projectData.outline.climax || '';
    outlineData.ending = props.projectData.outline.ending || '';

    // 加载故事设定
    if (props.projectData.outline.setting) {
      outlineData.setting.place = props.projectData.outline.setting.place || '';
      outlineData.setting.time = props.projectData.outline.setting.time || '';
      outlineData.setting.background = props.projectData.outline.setting.background || '';
    }

    // 加载角色设定
    if (props.projectData.outline.characters) {
      if (Array.isArray(props.projectData.outline.characters) && props.projectData.outline.characters.length > 0) {
        // 如果是数组，直接使用
        outlineData.characters = JSON.parse(JSON.stringify(props.projectData.outline.characters));
        console.log('初始化时加载角色数组:', outlineData.characters);
      } else if (typeof props.projectData.outline.characters === 'object') {
        // 如果是对象，尝试转换为数组
        try {
          const charactersArray = Object.values(props.projectData.outline.characters);
          if (charactersArray.length > 0) {
            outlineData.characters = charactersArray;
            console.log('初始化时将角色对象转换为数组:', charactersArray);
          }
        } catch (error) {
          console.error('初始化时转换角色对象出错:', error);
          // 保持默认的角色数组
        }
      }
    }
  }

  if (props.projectData.notes) {
    outlineData.notes = props.projectData.notes;
  }
};

// 保存大纲数据到项目
const saveOutlineData = () => {
  if (!outlineData.title.trim()) {
    window.$message?.warning('请输入故事标题');
    return;
  }

  // 更新项目数据（这些操作现在由watch函数自动完成）
  // 但我们仍然保留这些代码，以确保数据一致性
  props.projectData.title = outlineData.title;

  // 确保outline对象存在
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }

  // 保存基本字段
  props.projectData.outline.mainIdea = outlineData.mainIdea;
  props.projectData.outline.theme = outlineData.theme;
  props.projectData.outline.ageGroup = outlineData.ageGroup;

  // 保存故事情节
  props.projectData.outline.beginning = outlineData.beginning;
  props.projectData.outline.middle = outlineData.middle;
  props.projectData.outline.climax = outlineData.climax;
  props.projectData.outline.ending = outlineData.ending;

  // 保存故事设定
  props.projectData.outline.setting = JSON.parse(JSON.stringify(outlineData.setting));

  // 保存角色设定
  // 确保角色数据是数组格式
  if (Array.isArray(outlineData.characters)) {
    props.projectData.outline.characters = JSON.parse(JSON.stringify(outlineData.characters));
    console.log('保存角色数据:', props.projectData.outline.characters);
  } else {
    // 如果不是数组，创建一个空数组
    props.projectData.outline.characters = [];
    console.warn('保存时角色数据不是数组格式，已重置为空数组');
  }

  // 保存备注
  props.projectData.notes = outlineData.notes;

  // 保存到localStorage
  try {
    localStorage.setItem('project-data', JSON.stringify(props.projectData));
    console.log('Saved project data to localStorage');

    // 更新时间戳
    props.projectData.updatedAt = new Date().toISOString();

    // 显示保存成功提示
    window.$message?.success('故事大纲已保存');
  } catch (e) {
    console.warn('Failed to save project data to localStorage', e);
    window.$message?.error('保存失败，请重试');
  }
};



// 加载示例故事
const loadExampleStory = (storyData) => {
  // 将示例故事数据应用到当前故事大纲
  Object.assign(outlineData, storyData);

  // 保存到项目数据
  saveOutlineData();
};

// 组件挂载时初始化数据
onMounted(() => {
  initializeOutlineData();
});
</script>

<template>
  <div class="step-page-container">
    <!-- 页面标题和描述 -->
    <div class="step-header">
      <div class="header-left">
        <h2 class="step-title">想一个故事</h2>
        <p class="step-description">创造一个有趣的故事大纲，包括主题、角色和情节</p>
      </div>
      <div class="header-right">
        <StoryExampleLoader :outline-data="outlineData" @load-example="loadExampleStory" />
      </div>
    </div>

    <!-- 故事大纲表单 -->
    <div class="outline-form">
      <NSpace vertical size="large">
        <!-- 基本信息 -->
        <div class="content-card">
          <div class="card-title">
            <span class="card-title-icon">📝</span>
            基本信息
          </div>
          <StoryBasicInfo
            v-model:title="outlineData.title"
            v-model:mainIdea="outlineData.mainIdea"
          />
        </div>

        <!-- 故事主题 -->
        <div class="content-card">
          <div class="card-title">
            <span class="card-title-icon">🎭</span>
            故事主题
          </div>
          <StoryThemeSelector
            v-model:theme="outlineData.theme"
          />
        </div>

        <!-- 故事设定 -->
        <div class="content-card">
          <div class="card-title">
            <span class="card-title-icon">🏞️</span>
            故事设定
          </div>
          <StorySettingSelector
            v-model:setting="outlineData.setting"
          />
        </div>

        <!-- 故事结构 -->
        <div class="content-card">
          <div class="card-title">
            <span class="card-title-icon">📚</span>
            故事结构
          </div>
          <StoryStructureEditor
            v-model:beginning="outlineData.beginning"
            v-model:middle="outlineData.middle"
            v-model:climax="outlineData.climax"
            v-model:ending="outlineData.ending"
          />
        </div>

        <!-- 备注 -->
        <div class="content-card">
          <div class="card-title">
            <span class="card-title-icon">📌</span>
            备注
          </div>
          <StoryNotesEditor
            v-model:notes="outlineData.notes"
          />
        </div>
      </NSpace>

      <!-- 保存按钮 -->
      <div class="actions-container">
        <button class="primary-btn" @click="saveOutlineData">
          <span>💾</span> 保存故事大纲
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 使用全局统一样式，这里只添加特定于此组件的样式 */
.outline-form {
  margin-top: 1.5rem;
  padding: 1rem 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-start;
    margin-top: 0.5rem;
  }
}
</style>
