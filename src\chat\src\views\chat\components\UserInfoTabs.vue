<script setup lang="ts">
import logo from '@/assets/logo.png';
import { SvgIcon } from '@/components/common';
import SettingsModal from '@/components/common/SettingsModal/index.vue';
import { t } from '@/locales';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import {
  Announcement,
  Calendar,
  Commodity,
  DeleteThemes,
  Logout,
  SunOne,
  User,
} from '@icon-park/vue-next';
import type { NumberAnimationInst } from 'naive-ui';
import { NButton, NIcon, useDialog } from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { useBasicLayout } from '@/hooks/useBasicLayout';
import {
  useAppStore,
  useAuthStore,
  useChatStore,
  useGlobalStoreWithOut,
} from '@/store';

const useGlobalStore = useGlobalStoreWithOut();

const router = useRouter();
const appStore = useAppStore();
const chatStore = useChatStore();
const authStore = useAuthStore();
const model3AnimationInstRef = ref<NumberAnimationInst | null>(null);
const model4AnimationInstRef = ref<NumberAnimationInst | null>(null);
const modelMjAnimationInstRef = ref<NumberAnimationInst | null>(null);

const signInStatus = computed(
  () => Number(authStore.globalConfig?.signInStatus) === 1
);
const darkMode = computed(() => appStore.theme === 'dark');
const userBalance = computed(() => authStore.userBalance);
const usingPlugin = computed(() => chatStore.currentPlugin);
const dialog = useDialog();
const { isMobile } = useBasicLayout();
const isLogin = computed(() => authStore.isLogin);
const logoPath = computed(() => authStore.globalConfig.clientLogoPath || logo);
const homePage = computed(() => authStore.globalConfig.clientHomePath || '/');
const siteName = authStore.globalConfig?.siteName || 'AIWeb';
const showSettings = ref(false);
const model3Name = computed(
  () => authStore.globalConfig.model3Name || t('chat.ordinaryPoints')
);
const model4Name =
  computed(() => authStore.globalConfig.model4Name) || t('chat.advancedPoints');
const drawMjName =
  computed(() => authStore.globalConfig.drawMjName) || t('chat.drawingPoints');

const avatar = computed(() => authStore.userInfo.avatar);
const globaelConfig = computed(() => authStore.globalConfig);

const activeGroupInfo = computed(() => {
  const info = chatStore.groupList.find(
    (item: any) => item.uuid === chatStore.active
  );
  return info;
});

const configObj = computed(() => {
  try {
    return JSON.parse(activeGroupInfo.value?.config || '{}');
  } catch (e) {
    return {};
  }
});

const activeModelDeductType = computed(() => chatStore?.activeModelDeductType);

const displayInfo = computed(() => {
  const deductType =
    usingPlugin?.value?.deductType ||
    configObj.value.deductType ||
    activeModelDeductType.value;

  let remainingPoints;
  let buttonText;

  switch (deductType) {
    case 1:
      remainingPoints = userBalance.value.sumModel3Count || 0;
      buttonText = model3Name.value;
      break;
    case 2:
      remainingPoints = userBalance.value.sumModel4Count || 0;
      buttonText = model4Name.value;
      break;
    case 3:
      remainingPoints = userBalance.value.sumDrawMjCount || 0;
      buttonText = drawMjName.value;
      break;
    default:
      remainingPoints = 0;
      buttonText = t('chat.points');
  }

  if (remainingPoints > 99999) {
    remainingPoints = '不限';
    buttonText = '次数';
  }

  return { remainingPoints, buttonText };
});

function openSettings() {
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }
  showSettings.value = true;
}

function checkMode() {
  const mode = darkMode.value ? 'light' : 'dark';
  appStore.setTheme(mode);
}

function toggleLogin() {
  if (isLogin.value) authStore.logOut();
  else authStore.setLoginDialog(true);
}

function handleSignIn() {
  if (!isLogin.value) {
    authStore.setLoginDialog(true);
    return;
  }
  useGlobalStore.updateSignInDialog(true);
}

function logOut() {
  authStore.logOut();
  router.replace('/');
}

function goToUserCenter() {
  useGlobalStore.updateUserCenterDialog(true);
}
</script>

<template>
  <div class="user-info-tabs h-full flex flex-col">
    <!-- 标签页头部 -->
    <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">用户中心</h3>
    </div>

    <!-- 用户信息内容 -->
    <div class="flex-1 overflow-hidden">
      <div class="p-4 space-y-4">
        <!-- 用户头像和基本信息 -->
        <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="w-12 h-12 rounded-full bg-primary-600 flex items-center justify-center overflow-hidden shadow-sm border border-gray-200 dark:border-gray-600">
            <img
              v-if="avatar"
              :src="avatar"
              class="w-full h-full object-cover"
              alt="用户头像"
            />
            <User
              v-if="!avatar"
              theme="outline"
              size="24"
              class="text-white"
            />
          </div>
          
          <div class="flex-1">
            <div class="font-medium text-gray-800 dark:text-gray-200">
              {{ authStore.userInfo.username || '未登录用户' }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ siteName }}
            </div>
          </div>
        </div>

        <!-- 余额信息 -->
        <div v-if="isLogin" class="space-y-2">
          <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            账户余额
          </div>
          
          <button
            @click="useGlobalStore.updateGoodsDialog(true)"
            class="w-full p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
          >
            <div class="flex items-center justify-center space-x-2">
              <SvgIcon icon="ri:coin-line" class="text-yellow-500" />
              <span class="text-gray-600 dark:text-gray-300">
                {{ t('chat.remaining') }}{{ displayInfo.remainingPoints }}{{ displayInfo.buttonText }}
              </span>
            </div>
          </button>
        </div>

        <!-- 功能按钮组 -->
        <div class="space-y-2">
          <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            快捷操作
          </div>
          
          <!-- 主题切换 -->
          <button
            @click="checkMode"
            class="w-full flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
          >
            <SunOne 
              v-if="!darkMode" 
              theme="outline" 
              size="18" 
              class="text-gray-600 dark:text-gray-300" 
            />
            <SunOne 
              v-else 
              theme="filled" 
              size="18" 
              class="text-gray-600 dark:text-gray-300" 
            />
            <span class="text-gray-700 dark:text-gray-300">
              {{ darkMode ? '切换到浅色模式' : '切换到深色模式' }}
            </span>
          </button>

          <!-- 个人中心 -->
          <button
            v-if="isLogin"
            @click="goToUserCenter"
            class="w-full flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
          >
            <SvgIcon icon="ri:user-3-line" class="text-gray-600 dark:text-gray-300" />
            <span class="text-gray-700 dark:text-gray-300">个人中心</span>
          </button>

          <!-- 设置 -->
          <button
            @click="openSettings"
            class="w-full flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
          >
            <SvgIcon icon="ri:settings-3-line" class="text-gray-600 dark:text-gray-300" />
            <span class="text-gray-700 dark:text-gray-300">设置</span>
          </button>

          <!-- 签到按钮 -->
          <button
            v-if="signInStatus && isLogin"
            @click="handleSignIn"
            class="w-full flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-300"
          >
            <Calendar theme="outline" size="18" class="text-green-500" />
            <span class="text-gray-700 dark:text-gray-300">每日签到</span>
          </button>

          <!-- 登录/登出 -->
          <button
            @click="toggleLogin"
            class="w-full flex items-center space-x-3 p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/40 transition-all duration-300"
          >
            <SvgIcon 
              :icon="isLogin ? 'ri:logout-circle-line' : 'ri:login-circle-line'" 
              class="text-primary-600 dark:text-primary-400" 
            />
            <span class="text-primary-700 dark:text-primary-300">
              {{ isLogin ? '退出登录' : '登录 / 注册' }}
            </span>
          </button>
        </div>

        <!-- 系统信息 -->
        <div v-if="globaelConfig?.companyName && globaelConfig?.filingNumber" 
             class="text-xs text-gray-400 dark:text-gray-500 text-center border-t border-gray-200 dark:border-gray-700 pt-4">
          <div>版权所有 © {{ globaelConfig.companyName }}</div>
          <a
            class="hover:text-gray-600 dark:hover:text-gray-400 transition-colors"
            href="https://beian.miit.gov.cn"
            target="_blank"
          >
            {{ globaelConfig.filingNumber }}
          </a>
        </div>
      </div>
    </div>

    <!-- 设置弹窗 -->
    <SettingsModal v-model:visible="showSettings" />
  </div>
</template>

<style scoped lang="scss">
.user-info-tabs {
  background: white;
  
  @media (prefers-color-scheme: dark) {
    background: #1f2937;
  }
}
</style> 