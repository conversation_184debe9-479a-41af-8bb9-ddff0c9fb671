<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, inject } from 'vue';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAppStore, useChatStore } from '@/store';
import { NIcon, useMessage } from 'naive-ui';
import {
  ChatbubbleEllipsesOutline,
  ExtensionPuzzleOutline,
  ChevronDownOutline,
  ChevronUpOutline
} from '@vicons/ionicons5';
import { EditTwo, CheckOne, Right } from '@icon-park/vue-next';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { fetchUpdateGroupAPI } from '@/api/group';
import { fetchQueryModelsListAPI } from '@/api/models';
import ChatHistoryTabs from './ChatHistoryTabs.vue';
import PluginTabs from './PluginTabs.vue';

const { isMobile } = useBasicLayout();
const appStore = useAppStore();
const chatStore = useChatStore();
const ms = useMessage();

// 悬浮下拉菜单状态管理
const activeDropdown = ref('');
const dropdownStates = ref({
  history: false,
  plugins: false
});

// 模型相关状态
const modelOptions = ref([]);
const isHovering = ref(false);

// 计算属性
const activeGroupInfo = computed(() => chatStore.getChatByGroupInfo());
const dataSources = computed(() => chatStore.groupList);
const listSources = computed(() => chatStore.chatList);
const chatGroupId = computed(() => chatStore.active);
const usingPlugin = computed(() => chatStore.currentPlugin);
const activeAppId = computed(() => activeGroupInfo?.value?.appId || 0);

const configObj = computed(() => {
  const configString = activeGroupInfo.value?.config;
  if (!configString) {
    return {};
  }
  try {
    return JSON.parse(configString);
  } catch (e) {
    return {};
  }
});

const activeModel = computed(() =>
  String(configObj?.value?.modelInfo?.model ?? '')
);

const notSwitchModel = computed(() => {
  return (
    (activeGroupInfo?.value?.appId &&
      (configObj.value.modelInfo?.isFixedModel === 1 ||
        configObj.value.modelInfo?.isGPTs === 1)) ||
    (usingPlugin.value && usingPlugin?.value?.deductType !== 0)
  );
});

// 悬浮下拉菜单配置
const dropdownConfigs = [
  {
    key: 'history',
    label: '聊天历史',
    icon: ChatbubbleEllipsesOutline,
    component: ChatHistoryTabs,
    description: '查看和管理聊天记录'
  },
  {
    key: 'plugins',
    label: '插件管理',
    icon: ExtensionPuzzleOutline,
    component: PluginTabs,
    description: '管理和配置插件'
  }
];

// 下拉菜单内容高度
const dropdownContentHeight = computed(() => {
  if (isMobile.value) {
    return '35vh';
  }
  return '25vh';
});

// 创建新对话函数
const createNewChatGroup = inject('createNewChatGroup', () =>
  Promise.resolve()
) as () => Promise<void>;

// 新对话处理
async function handleNewChat() {
  if (chatStore.isStreamIn) {
    ms.info('AI回复中，请稍后再试');
    return;
  }
  await createNewChatGroup();
}

// 模型切换功能
async function switchModel(option: any) {
  const { modelInfo, fileInfo } = chatStore.activeConfig;
  const { isGPTs, isFixedModel, modelName } = modelInfo;

  const config = {
    modelInfo: {
      keyType: option.keyType,
      modelName: (activeGroupInfo?.value?.appId ? modelName : option.label) || '',
      model: option.value,
      deductType: option.deductType,
      deduct: option.deduct,
      isFileUpload: option.isFileUpload,
      modelAvatar: option.modelAvatar || '',
      isGPTs,
      isFixedModel,
    },
    fileInfo: fileInfo || {},
  };

  const params = {
    groupId: chatGroupId.value,
    config: JSON.stringify(config),
  };
  await fetchUpdateGroupAPI(params);
  await chatStore.queryMyGroup();
}

// 查询模型列表
async function queryModelsList() {
  try {
    const res: any = await fetchQueryModelsListAPI();
    if (!res.success) return;
    const { modelMaps } = res.data;

    const flatModelArray = Object.values(modelMaps).flat() as any[];
    const filteredModelArray = flatModelArray.filter(
      (model) => model.keyType === 1
    );
    modelOptions.value = filteredModelArray.map((model) => ({
      label: model.modelName,
      value: model.model,
      deductType: model.deductType,
      keyType: model.keyType,
      deduct: model.deduct,
      isFileUpload: model.isFileUpload,
      modelAvatar: model.modelAvatar,
      modelDescription: model.modelDescription,
    }));
  } catch (error) {
    console.error('加载模型列表失败:', error);
  }
}

// 切换下拉菜单显示状态
const toggleDropdown = (key: string) => {
  Object.keys(dropdownStates.value).forEach(k => {
    if (k !== key) {
      dropdownStates.value[k] = false;
    }
  });

  dropdownStates.value[key] = !dropdownStates.value[key];
  activeDropdown.value = dropdownStates.value[key] ? key : '';
};

// 关闭所有下拉菜单
const closeAllDropdowns = () => {
  Object.keys(dropdownStates.value).forEach(key => {
    dropdownStates.value[key] = false;
  });
  activeDropdown.value = '';
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as Element;
  if (!target.closest('.dropdown-container')) {
    closeAllDropdowns();
  }
};

// 初始化状态
onMounted(() => {
  queryModelsList();
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="chat-toolbar bg-gradient-to-r from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 border-b border-blue-100/60 dark:border-blue-900/30 backdrop-blur-xl shadow-md">
    <!-- 美化的现代工具栏 -->
    <div class="flex items-center justify-between px-6 py-4">
      <!-- 左侧：美化的功能按钮组 -->
      <div class="flex items-center space-x-5 dropdown-container">
        <!-- 模型选择按钮（移到第一个位置） -->
        <Menu v-if="!notSwitchModel" as="div" class="relative">
          <MenuButton
            class="group inline-flex items-center gap-x-3 rounded-xl px-6 py-3 text-sm font-medium bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl dark:shadow-blue-900/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-300/50 transform hover:-translate-y-1 hover:scale-105 active:scale-95 backdrop-blur-sm"
            @mouseover="isHovering = true"
            @mouseleave="isHovering = false"
          >
            <div class="flex items-center gap-x-2">
              <div class="w-2 h-2 rounded-full bg-white/80 group-hover:bg-white transition-colors duration-300 animate-pulse"></div>
              <span class="truncate max-w-[150px] font-semibold">{{ configObj?.modelInfo?.modelName || '选择模型' }}</span>
            </div>
            <Right
              size="14"
              class="transition-all duration-300 text-white/80 group-hover:text-white"
              :class="{ 'transform rotate-90': isHovering }"
            />
          </MenuButton>

          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <MenuItems
              class="absolute top-full left-0 z-20 origin-top divide-y divide-gray-100/60 dark:divide-gray-700/60 rounded-xl bg-white/95 dark:bg-gray-800/95 shadow-xl border border-blue-100/60 dark:border-blue-900/30 backdrop-blur-xl overflow-y-auto mt-2 max-h-60 min-w-[280px]"
            >
              <div class="py-2">
                <MenuItem v-for="(option, index) in modelOptions" :key="index">
                  <div
                    class="flex items-center space-x-3 mx-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 p-3 rounded-lg cursor-pointer transition-all duration-300 group"
                    @click="switchModel(option)"
                  >
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-700 flex items-center justify-center overflow-hidden shadow-sm group-hover:shadow-md transition-shadow duration-300">
                      <img
                        v-if="option.modelAvatar"
                        :src="option.modelAvatar"
                        alt="model avatar"
                        class="w-full h-full object-cover"
                      />
                      <span v-else class="text-xs font-semibold text-blue-600 dark:text-blue-300">
                        {{ option.label.charAt(0) }}
                      </span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="text-sm font-semibold truncate text-gray-900 dark:text-gray-100 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300">{{ option.label }}</div>
                      <div
                        v-if="option.modelDescription"
                        class="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5"
                      >
                        {{ option.modelDescription }}
                      </div>
                    </div>
                    <CheckOne
                      v-if="activeModel === option.value"
                      theme="filled"
                      size="18"
                      class="text-blue-500 dark:text-blue-400 animate-pulse"
                    />
                  </div>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>

        <!-- 美化的固定模型显示 -->
        <div
          v-else
          class="inline-flex items-center gap-x-3 rounded-xl px-6 py-3 text-sm font-medium bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 text-blue-800 dark:text-blue-300 shadow-md border border-blue-200/60 dark:border-blue-700/40 transform hover:-translate-y-1 hover:scale-105 transition-all duration-300"
        >
          <div class="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse"></div>
          <span class="truncate max-w-[150px] font-semibold">
            {{ usingPlugin?.pluginName || activeGroupInfo?.title || '当前模型' }}
          </span>
        </div>

        <!-- 聊天历史和插件管理下拉菜单 -->
        <Menu v-for="dropdown in dropdownConfigs" :key="dropdown.key" as="div" class="relative">
          <MenuButton
            :class="[
              'group flex items-center space-x-2 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300',
              'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-300',
              'bg-gradient-to-r from-blue-50/80 to-blue-100/80 dark:from-blue-900/30 dark:to-blue-800/30',
              'border border-blue-200/60 dark:border-blue-700/40 hover:border-blue-300/70 dark:hover:border-blue-600/70',
              'shadow-md hover:shadow-lg dark:shadow-gray-900/20',
              'focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 dark:focus:ring-offset-gray-800',
              'transform hover:-translate-y-1 hover:scale-105 active:scale-95'
            ]"
          >
            <NIcon :component="dropdown.icon" :size="18" class="text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300" />
            <span class="hidden sm:inline font-medium">{{ dropdown.label }}</span>
            <ChevronDownOutline class="w-4 h-4 transition-all duration-300 ui-open:rotate-180 group-hover:text-blue-600 dark:group-hover:text-blue-400" />
          </MenuButton>

          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="transform opacity-0 scale-95 translate-y-1"
            enter-to-class="transform opacity-100 scale-100 translate-y-0"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100 translate-y-0"
            leave-to-class="transform opacity-0 scale-95 translate-y-1"
          >
            <MenuItems
              :class="[
                'absolute top-full left-0 z-50 mt-2 origin-top-left',
                'bg-white/95 dark:bg-gray-800/95 rounded-xl shadow-xl border border-blue-100/60 dark:border-blue-700/40',
                'backdrop-blur-xl',
                'min-w-[320px] max-w-[400px]',
                isMobile ? 'w-screen max-w-[calc(100vw-2rem)]' : 'w-80'
              ]"
              :style="{ maxHeight: dropdownContentHeight }"
            >
              <div class="p-4 border-b border-gray-100/60 dark:border-gray-700/60 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-t-xl">
                <div class="flex items-center space-x-3">
                  <div class="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/50 shadow-inner">
                    <NIcon :component="dropdown.icon" :size="20" class="text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 dark:text-gray-100">{{ dropdown.label }}</h3>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{{ dropdown.description }}</p>
                  </div>
                </div>
              </div>

              <div class="overflow-y-auto" :style="{ maxHeight: 'calc(' + dropdownContentHeight + ' - 80px)' }">
                <component :is="dropdown.component" class="p-0" />
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>

      <!-- 中间：占位区域 -->
      <div class="flex-1 flex justify-center">
        <!-- 空白占位 -->
      </div>

      <!-- 右侧：美化的新对话按钮 -->
      <div class="flex items-center">
        <button
          @click="handleNewChat"
          :disabled="listSources.length === 0 && !activeAppId && dataSources.length !== 0"
          class="group relative flex items-center space-x-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl dark:shadow-green-900/30 border border-green-400/30 hover:border-green-300/50 transform hover:-translate-y-1 hover:scale-105 active:scale-95 backdrop-blur-sm overflow-hidden"
        >
          <span class="hidden sm:inline font-semibold relative z-10">新对话</span>
          <EditTwo size="18" class="group-hover:rotate-12 transition-transform duration-300 relative z-10" />
          <div class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 group-hover:animate-pulse"></div>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.chat-toolbar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 30;
  box-shadow:
    0 4px 12px -2px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.1);

  @media (prefers-color-scheme: dark) {
    box-shadow:
      0 4px 12px -2px rgba(0, 0, 0, 0.2),
      0 2px 4px -1px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  // 添加微妙的动画效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::before {
    opacity: 1;
  }
  
  // 添加底部光晕效果
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    opacity: 1;
  }
}

// 下拉菜单样式优化
.dropdown-container {
  :deep(.relative) {
    z-index: 50;
  }

  :deep(.overflow-y-auto) {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgb(156, 163, 175);
      border-radius: 2px;

      &:hover {
        background: rgb(107, 114, 128);
      }
    }

    @media (prefers-color-scheme: dark) {
      &::-webkit-scrollbar-thumb {
        background: rgb(75, 85, 99);

        &:hover {
          background: rgb(107, 114, 128);
        }
      }
    }
  }
}

// 下拉菜单动画优化
:deep(.ui-open) {
  .w-3 {
    transform: rotate(180deg);
  }
}

// 增强的悬浮效果和动画
button, .inline-flex {
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 10px 30px -5px rgba(0, 0, 0, 0.15),
      0 5px 15px -3px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  &:active {
    transform: translateY(-1px) scale(0.98);
    transition-duration: 0.1s;
  }

  // 添加统一的光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s ease;
    z-index: 1;
  }

  &:hover::before {
    left: 100%;
  }
}

// 响应式优化
@media (max-width: 640px) {
  .chat-toolbar {
    padding: 0.75rem 1rem;

    .flex {
      flex-wrap: wrap;
      gap: 0.75rem;
    }

    button, .inline-flex {
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      min-height: 44px;
      border-radius: 0.75rem;
      touch-action: manipulation;

      &:active {
        transform: scale(0.95);
        transition-duration: 0.1s;
      }

      &:hover {
        transform: none; // 移动端禁用悬浮效果
        box-shadow: none;
      }

      // 移动端禁用光泽效果
      &::before,
      &::after {
        display: none;
      }

      span {
        font-size: 0.875rem;
        font-weight: 600;
      }
    }
  }

  .dropdown-container {
    :deep(.absolute) {
      position: fixed !important;
      left: 1rem !important;
      right: 1rem !important;
      width: auto !important;
      max-width: none !important;
      z-index: 60 !important;
    }
  }
}

// 平板端优化
@media (min-width: 641px) and (max-width: 1024px) {
  .chat-toolbar {
    button, .inline-flex {
      span {
        font-size: 0.875rem;
      }
    }
  }
}

// 添加按钮焦点状态样式
button:focus-visible, .inline-flex:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  z-index: 10;
}

// 添加键盘导航支持
button:focus, .inline-flex:focus {
  z-index: 10;
}

// 优化动画性能
@media (prefers-reduced-motion: reduce) {
  button, .inline-flex {
    transition: none;

    &:hover {
      transform: none;
    }

    &::before,
    &::after {
      display: none;
    }
  }
}

// 添加新的动效
@keyframes soft-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}
</style>