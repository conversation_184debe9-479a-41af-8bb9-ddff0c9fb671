import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class MessageDto {
  @ApiProperty({ example: 'system', description: '消息角色' })
  @IsString()
  role: string;

  @ApiProperty({ example: '你是一个专业的SQL生成器', description: '消息内容' })
  @IsString()
  content: string;
}

export class DirectCompletionDto {
  @ApiProperty({ 
    example: [
      { role: 'system', content: '你是一个专业的SQL生成器' },
      { role: 'user', content: '请生成一个创建用户表的SQL' }
    ], 
    description: '消息数组' 
  })
  @IsArray()
  @IsNotEmpty({ message: '消息数组不能为空' })
  messages: MessageDto[];

  @ApiProperty({ example: 'gpt-3.5-turbo', description: '使用的模型', required: false })
  @IsOptional()
  @IsString()
  model?: string;

  @ApiProperty({ example: 0.3, description: '温度值', required: false })
  @IsOptional()
  @IsNumber()
  temperature?: number;

  @ApiProperty({ example: false, description: '是否使用流式响应', required: false })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;
}
