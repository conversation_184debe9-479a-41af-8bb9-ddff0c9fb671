import { Modu<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppCatsEntity } from './appCats.entity';
import { AppEntity } from './app.entity';
import { UserAppsEntity } from './userApps.entity';
import { FormEntity } from './form.entity';
import { FormController } from './form.controller';
import { FormService } from './form.service';

@Module({
	imports: [TypeOrmModule.forFeature([AppCatsEntity, AppEntity, UserAppsEntity, FormEntity])],
	controllers: [AppController, FormController],
	providers: [AppService, FormService],
	exports: [AppService, FormService]
})
export class AppModule {}
