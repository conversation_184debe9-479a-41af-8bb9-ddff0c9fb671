<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { NAvatar, NButton } from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useAuthStore } from '@/store';
import { t } from '@/locales';
import defaultAvatar from '@/assets/avatar.png';

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  },
  activeTab: {
    type: String,
    default: 'personal-info'
  }
});

const emit = defineEmits(['update-collapsed', 'switch-tab']);

const router = useRouter();
const authStore = useAuthStore();

// 用户信息
const avatar = computed(() => authStore.userInfo.avatar ?? defaultAvatar);
const username = computed(() => authStore.userInfo.username ?? t('usercenter.notLoggedIn'));
const userBalance = computed(() => authStore.userBalance);

// 侧边栏菜单项
const menuItems = [
  {
    key: 'personal-info',
    label: '个人信息',
    icon: 'ri:user-3-line'
  },
  {
    key: 'points',
    label: '积分/订阅',
    icon: 'ri:coin-line'
  },
  {
    key: 'notifications',
    label: '通知与消息',
    icon: 'ri:notification-3-line'
  }
];

// 返回聊天页面
function goToChat() {
  router.push('/chat');
}

// 退出登录
function logOut() {
  authStore.logOut();
  router.replace('/');
}

// 切换标签页
function handleSwitchTab(key: string) {
  emit('switch-tab', key);
}

// 更新侧边栏折叠状态
function handleUpdateCollapsed() {
  emit('update-collapsed', !props.collapsed);
}
</script>

<template>
  <div class="h-full transition-all duration-300 animate__animated animate__fadeIn">
    <div
      class="flex flex-col h-full w-full bg-white dark:bg-gray-800 select-none transition-all duration-300 ds-shadow-md rounded-lg overflow-hidden border border-gray-100 dark:border-gray-700 backdrop-blur-sm mr-0"
    >
      <main class="flex flex-col h-full flex-1 overflow-hidden flex-shrink-0">
        <!-- 顶部区域 -->
        <div
          class="flex bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 w-full justify-between items-center px-4 py-3 border-b border-gray-100 dark:border-gray-700 rounded-t-lg shadow-sm"
        >
          <div class="flex items-center gap-3">
            <!-- 返回按钮改为用户中心按钮 -->
            <button
              @click="goToChat"
              class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
              title="返回聊天"
            >
              <SvgIcon icon="ri:arrow-left-line" class="text-gray-600 dark:text-gray-300" />
            </button>

            <!-- 标题 -->
            <div class="font-medium text-gray-800 dark:text-gray-200 text-shadow-sm">{{ t('usercenter.personalCenter') }}</div>
          </div>
        </div>

        <!-- 用户信息区域 -->
        <div class="flex flex-col items-center px-4 py-6 border-b border-gray-100 dark:border-gray-700">
          <NAvatar :size="80" :src="avatar" :fallback-src="defaultAvatar" class="border-2 border-white dark:border-gray-700 shadow-md hover-float transition-all duration-300" />
          <div class="mt-3 text-lg font-medium text-gray-800 dark:text-gray-200">{{ username }}</div>
        </div>

        <!-- 菜单区域 -->
        <div class="flex-1 overflow-y-auto px-4 py-4">
          <div class="space-y-2">
            <div
              v-for="item in menuItems"
              :key="item.key"
              class="flex items-center p-3 rounded-lg cursor-pointer transition-all duration-300 hover-float"
              :class="[
                activeTab === item.key
                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                  : 'bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              ]"
              @click="handleSwitchTab(item.key)"
            >
              <SvgIcon :icon="item.icon" class="mr-2" />
              <span>{{ item.label }}</span>
            </div>
          </div>
        </div>

        <!-- 底部区域 -->
        <div class="p-4 border-t border-gray-100 dark:border-gray-700">
          <NButton
            block
            type="error"
            @click="logOut"
            class="hover-float transition-all duration-300"
          >
            <template #icon>
              <SvgIcon icon="ri:logout-box-line" />
            </template>
            {{ t('usercenter.logOut') }}
          </NButton>
        </div>

        <!-- 会员过期提示 -->
        <div
          v-if="userBalance.expirationTime"
          class="px-4 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm font-medium"
        >
          {{ t('usercenter.membershipExpiration') }}: {{ userBalance.expirationTime }}
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
}
</style>
