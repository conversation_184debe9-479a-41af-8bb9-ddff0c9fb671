import { defineStore } from 'pinia';
import { ref } from 'vue';
import { get, post } from '@/utils/request';

export interface WorkFolder {
  id: number;
  name: string;
  description: string;
  coverImg: string;
  userId: number;
  order: number;
  color: string;
  icon: string;
  emoji?: string; // 添加emoji字段
  isSystem: number;
  createdAt: string;
  updatedAt: string;
}

export interface Work {
  id: number;
  title: string;
  description: string;
  coverImg: string;
  userId: number;
  status: number;
  isPublic: number;
  isRecommended: number;
  folderId: number | null;
  pageCount: number;
  wordCount: number;
  createdAt: string;
  updatedAt: string;
  lastEditedAt: string;
  isDeleted: number;
  deletedAt: string | null;
}

export const useWorksStore = defineStore('works', () => {
  // 作品列表
  const myWorks = ref<Work[]>([]);
  // 文件夹列表
  const folders = ref<WorkFolder[]>([]);
  // 回收站作品
  const trashWorks = ref<Work[]>([]);
  // 加载状态
  const loading = ref(false);

  /**
   * 获取我的作品列表
   */
  const fetchMyWorks = async (params: {
    page?: number;
    size?: number;
    keyword?: string;
    status?: number;
    folderId?: number;
    isDeleted?: number;
    sortField?: string;
    sortOrder?: 'ASC' | 'DESC';
    recentOnly?: boolean;
  } = {}) => {
    loading.value = true;
    try {
      // 确保URL路径正确，移除重复的/api前缀
      const url = 'storybook/works';

      const { data } = await get<{
        items: Work[];
        total: number;
      }>({
        url,
        data: params
      });


      if (params.isDeleted === 1) {
        trashWorks.value = data.items;
      } else {
        myWorks.value = data.items;
      }

      return data;
    } catch (error) {
      console.error('获取作品列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取文件夹列表
   */
  const fetchFolders = async (params: {
    page?: number;
    size?: number;
    keyword?: string;
  } = {}) => {
    loading.value = true;
    try {
      // 确保URL路径正确，移除重复的/api前缀
      const url = 'storybook/works/folder';

      const { data } = await get<{
        items: WorkFolder[];
        total: number;
      }>({
        url,
        data: params
      });

      folders.value = data.items;
      return data;
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建文件夹
   */
  const createFolder = async (folder: {
    name: string;
    description?: string;
    coverImg?: string;
    color?: string;
    icon?: string;
    emoji?: string; // 添加emoji字段
    order?: number;
  }) => {
    try {
      const { data } = await post<WorkFolder>({
        url: 'storybook/works/folder',
        data: folder
      });

      folders.value.push(data);
      return data;
    } catch (error) {
      console.error('创建文件夹失败:', error);
      throw error;
    }
  };

  /**
   * 更新文件夹
   */
  const updateFolder = async (id: number, folder: {
    name?: string;
    description?: string;
    coverImg?: string;
    color?: string;
    icon?: string;
    emoji?: string; // 添加emoji字段
    order?: number;
  }) => {
    try {
      const { data } = await post<WorkFolder>({
        url: `storybook/works/folder/${id}`,
        method: 'PUT',
        data: folder
      });

      const index = folders.value.findIndex(f => f.id === id);
      if (index !== -1) {
        folders.value[index] = data;
      }

      return data;
    } catch (error) {
      console.error('更新文件夹失败:', error);
      throw error;
    }
  };

  /**
   * 删除文件夹
   */
  const deleteFolder = async (id: number) => {
    try {
      await post({
        url: `storybook/works/folder/${id}`,
        method: 'DELETE'
      });

      const index = folders.value.findIndex(f => f.id === id);
      if (index !== -1) {
        folders.value.splice(index, 1);
      }
    } catch (error) {
      console.error('删除文件夹失败:', error);
      throw error;
    }
  };

  /**
   * 批量操作作品
   */
  const batchOperation = async (operation: {
    storybookIds: number[];
    operation: 'delete' | 'restore' | 'move';
    targetFolderId?: number;
  }) => {
    try {
      await post({
        url: 'storybook/works/batch',
        data: operation
      });

      // 更新本地状态
      if (operation.operation === 'delete') {
        myWorks.value = myWorks.value.filter(work => !operation.storybookIds.includes(work.id));
      } else if (operation.operation === 'restore') {
        trashWorks.value = trashWorks.value.filter(work => !operation.storybookIds.includes(work.id));
      } else if (operation.operation === 'move') {
        myWorks.value.forEach(work => {
          if (operation.storybookIds.includes(work.id)) {
            work.folderId = operation.targetFolderId || null;
          }
        });
      }
    } catch (error) {
      console.error('批量操作作品失败:', error);
      throw error;
    }
  };

  /**
   * 删除作品（移到回收站）
   */
  const deleteWork = async (id: number) => {
    return batchOperation({
      storybookIds: [id],
      operation: 'delete'
    });
  };

  /**
   * 恢复作品（从回收站恢复）
   */
  const restoreWork = async (id: number) => {
    return batchOperation({
      storybookIds: [id],
      operation: 'restore'
    });
  };

  /**
   * 移动作品到文件夹
   */
  const moveWorkToFolder = async (id: number, folderId?: number) => {
    return batchOperation({
      storybookIds: [id],
      operation: 'move',
      targetFolderId: folderId
    });
  };

  /**
   * 永久删除作品
   */
  const permanentlyDeleteWork = async (id: number) => {
    try {
      await post({
        url: `storybook/works/permanent/${id}`,
        method: 'DELETE'
      });

      trashWorks.value = trashWorks.value.filter(work => work.id !== id);
    } catch (error) {
      console.error('永久删除作品失败:', error);
      throw error;
    }
  };

  /**
   * 清空回收站
   */
  const emptyTrash = async () => {
    try {
      await post({
        url: 'storybook/works/trash',
        method: 'DELETE'
      });

      trashWorks.value = [];
    } catch (error) {
      console.error('清空回收站失败:', error);
      throw error;
    }
  };

  /**
   * 导出作品为PDF
   */
  const exportWorkToPdf = async (id: number, options: {
    range?: 'all' | 'custom';
    startPage?: number;
    endPage?: number;
  } = {}) => {
    try {
      const { data } = await post<{
        url: string;
        filename: string;
      }>({
        url: `storybook/works/${id}/export/pdf`,
        data: options
      });

      return data;
    } catch (error) {
      console.error('导出PDF失败:', error);
      throw error;
    }
  };

  /**
   * 导出作品为图片集
   */
  const exportWorkToImages = async (id: number, options: {
    range?: 'all' | 'custom';
    startPage?: number;
    endPage?: number;
    quality?: 'high' | 'medium' | 'low';
  } = {}) => {
    try {
      const { data } = await post<{
        url: string;
        filename: string;
      }>({
        url: `storybook/works/${id}/export/images`,
        data: options
      });

      return data;
    } catch (error) {
      console.error('导出图片集失败:', error);
      throw error;
    }
  };

  /**
   * 创建绘本
   */
  const createStorybook = async (storybook: {
    title: string;
    description?: string;
    coverImg?: string;
    isPublic?: number;
    content?: string;
    source?: string;
    tags?: string[];
    status?: number;
    folderId?: number;
    pageCount?: number;
    wordCount?: number;
  }) => {
    try {
      console.log('开始创建绘本:', storybook.title);
      const { data } = await post<Work>({
        url: 'storybook/works',
        data: storybook
      });

      console.log('绘本创建成功，ID:', data.id);

      // 更新本地状态
      myWorks.value.unshift(data);

      // 设置一个标记，表示已经创建了新故事
      localStorage.setItem('storybook-created-new', JSON.stringify({
        id: data.id,
        title: data.title,
        timestamp: new Date().toISOString()
      }));

      return data;
    } catch (error) {
      console.error('创建绘本失败:', error);
      throw error;
    }
  };

  /**
   * 获取绘本详情
   */
  const getStorybookDetail = async (id: number) => {
    try {
      const { data } = await get<Work>({
        url: `storybook/works/${id}`
      });

      return data;
    } catch (error) {
      console.error('获取绘本详情失败:', error);
      throw error;
    }
  };

  /**
   * 更新绘本
   */
  const updateStorybook = async (id: number, storybook: {
    title?: string;
    description?: string;
    coverImg?: string;
    isPublic?: number;
    content?: string;
    source?: string;
    tags?: string[];
    status?: number;
  }) => {
    try {
      const { data } = await post<Work>({
        url: `storybook/works/${id}`,
        method: 'PUT',
        data: storybook
      });

      // 更新本地状态
      const index = myWorks.value.findIndex(w => w.id === id);
      if (index !== -1) {
        myWorks.value[index] = data;
      }

      return data;
    } catch (error) {
      console.error('更新绘本失败:', error);
      throw error;
    }
  };

  return {
    myWorks,
    folders,
    trashWorks,
    loading,
    fetchMyWorks,
    fetchFolders,
    createFolder,
    updateFolder,
    deleteFolder,
    batchOperation,
    deleteWork,
    restoreWork,
    moveWorkToFolder,
    permanentlyDeleteWork,
    emptyTrash,
    exportWorkToPdf,
    exportWorkToImages,
    createStorybook,
    getStorybookDetail,
    updateStorybook
  };
});
