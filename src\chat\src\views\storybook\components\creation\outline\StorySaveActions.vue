<template>
  <div class="form-actions">
    <NButton type="primary" size="large" @click="$emit('save')" class="save-button">
      <span class="save-icon">🎉</span>
      <span>保存我的绘本</span>
    </NButton>
  </div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui';

defineEmits(['save']);
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 2px dashed #e2e8f0;
  position: relative;
}

.dark .form-actions {
  border-top-color: #334155;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 2.5rem;
  height: 4rem;
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
  border: none;
  border-radius: 1.5rem;
  color: white;
  box-shadow: 0 8px 16px rgba(79, 70, 229, 0.3), 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.save-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4f46e5, #2563eb);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.save-button:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 20px rgba(79, 70, 229, 0.4), 0 6px 8px rgba(0, 0, 0, 0.1);
}

.save-button:hover::before {
  opacity: 1;
}

.save-button:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(79, 70, 229, 0.3);
}

.dark .save-button {
  background: linear-gradient(135deg, #8b5cf6, #3b82f6);
  box-shadow: 0 8px 16px rgba(79, 70, 229, 0.5), 0 4px 6px rgba(0, 0, 0, 0.2);
}

.dark .save-button::before {
  background: linear-gradient(135deg, #4f46e5, #2563eb);
}

.dark .save-button:hover {
  box-shadow: 0 12px 20px rgba(79, 70, 229, 0.6), 0 6px 8px rgba(0, 0, 0, 0.2);
}

.save-icon {
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}
</style>
