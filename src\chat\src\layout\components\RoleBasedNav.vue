<script setup lang="ts">
import { computed, h, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore, useAppStore } from '@/store';
import { NMenu, NIcon, NButton } from 'naive-ui';


import {
  ChatbubbleEllipsesOutline,
  BookOutline,
  PeopleOutline,
  CreateOutline,
  SchoolOutline,
  LibraryOutline,
  SettingsOutline,
  HomeOutline,
  CloseOutline
} from '@vicons/ionicons5';


interface NavItem {
  key: string;
  label: string;
  icon: any;
  path: string;
  role?: 'teacher' | 'student' | 'both';
}

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const appStore = useAppStore();

// 状态管理

// 用户角色检测 - 与UserRoleIndicator保持一致
const userRole = computed(() => {
  const userInfo = authStore.userInfo;
  // 优先使用store中的角色信息
  if (userInfo?.role && (userInfo.role === 'teacher' || userInfo.role === 'student')) {
    return userInfo.role;
  }
  // 根据当前路径推断角色
  if (route.path.includes('/chat') || route.path.includes('/teacher')) {
    return 'teacher';
  } else if (route.path.includes('/storybook') || route.path.includes('/student')) {
    return 'student';
  }
  return 'student'; // 默认为学生角色
});

// 导航菜单项配置
const navItems: NavItem[] = [
  {
    key: 'teacher-dashboard',
    label: '教师工作台',
    icon: HomeOutline,
    path: '/teacher/dashboard',
    role: 'teacher'
  },
  {
    key: 'teacher-chat',
    label: 'AI教学助手',
    icon: ChatbubbleEllipsesOutline,
    path: '/teacher/chat',
    role: 'teacher'
  },
  {
    key: 'student-works',
    label: '学生作品',
    icon: PeopleOutline,
    path: '/teacher/works',
    role: 'teacher'
  },
  {
    key: 'teaching-tools',
    label: '教学工具',
    icon: SchoolOutline,
    path: '/teacher/tools',
    role: 'teacher'
  },
  {
    key: 'student-dashboard',
    label: '学习空间',
    icon: HomeOutline,
    path: '/student/dashboard',
    role: 'student'
  },
  {
    key: 'create-storybook',
    label: 'AI绘本创作',
    icon: CreateOutline,
    path: '/student/storybook',
    role: 'student'
  },
  {
    key: 'my-works',
    label: '我的作品',
    icon: BookOutline,
    path: '/student/works',
    role: 'student'
  },
  {
    key: 'library',
    label: '作品广场',
    icon: LibraryOutline,
    path: '/showcase',
    role: 'both'
  },
  {
    key: 'settings',
    label: '设置',
    icon: SettingsOutline,
    path: '/settings',
    role: 'both'
  }
];

// 根据用户角色过滤菜单项
const filteredNavItems = computed(() => {
  return navItems.filter(item =>
    item.role === 'both' || item.role === userRole.value
  );
});

// 当前选中的菜单项
const selectedKey = computed(() => {
  const currentPath = route.path;
  const matchedItem = navItems.find(item =>
    currentPath.startsWith(item.path) || currentPath === item.path
  );
  return matchedItem?.key || '';
});

// 导航点击处理
const handleNavClick = (key: string) => {
  const item = navItems.find(item => item.key === key);
  if (item) {
    // 特殊处理设置页面，根据当前角色跳转
    if (key === 'settings') {
      const settingsPath = userRole.value === 'teacher' ? '/teacher/settings' : '/student/settings';
      router.push(settingsPath);
    } else {
      router.push(item.path);
    }
  }
};

// 生成菜单选项
const menuOptions = computed(() => {
  return filteredNavItems.value.map(item => ({
    key: item.key,
    label: item.label,
    icon: () => h(NIcon, { size: 20 }, { default: () => h(item.icon) })
  }));
});

// 获取角色显示名称
const roleDisplayName = computed(() => {
  return userRole.value === 'teacher' ? '老师' : '学生';
});

// 获取角色主色调
const roleThemeColor = computed(() => {
  return userRole.value === 'teacher' ? '#2080f0' : '#18a058';
});





// 菜单切换函数
const handleToggleSider = () => {
  appStore.setSiderCollapsed(!appStore.siderCollapsed);
};
</script>

<template>
  <div class="role-based-nav w-full h-full">
    <!-- 角色指示器 -->
    <div class="role-indicator p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div
            class="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold"
            :style="{ backgroundColor: roleThemeColor }"
          >
            {{ roleDisplayName.charAt(0) }}
          </div>
          <div>
            <div class="font-medium text-gray-900 dark:text-gray-100">
              {{ roleDisplayName }}工作台
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ userRole === 'teacher' ? 'AI教学助手' : 'AI创作伙伴' }}
            </div>
          </div>
        </div>

        <!-- 菜单切换按钮 -->
        <NButton
          quaternary
          circle
          size="small"
          @click="handleToggleSider"
          class="menu-toggle-btn"
          title="收起侧边栏"
        >
          <template #icon>
            <NIcon :component="CloseOutline" />
          </template>
        </NButton>
      </div>
    </div>

    <!-- 导航菜单 -->
    <div class="nav-menu flex-1 p-2">
      <NMenu
        :value="selectedKey"
        :options="menuOptions"
        @update:value="handleNavClick"
        :indent="24"
        class="role-nav-menu"
      />
    </div>




  </div>
</template>

<style scoped lang="scss">
.role-based-nav {
  @apply flex flex-col bg-white dark:bg-gray-900;
  height: 100%;
}

.role-indicator {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  @apply dark:bg-gray-800;

  .menu-toggle-btn {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      background-color: rgba(255, 255, 255, 0.8);
      @apply dark:bg-gray-700;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.nav-menu {
  :deep(.n-menu-item) {
    @apply my-1 rounded-lg;
  }

  :deep(.n-menu-item--selected) {
    @apply font-medium;
  }
}


</style>