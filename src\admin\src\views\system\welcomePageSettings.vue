<route lang="yaml">
meta:
  title: 首页设置
</route>

<script lang="ts" setup>
import apiConfig from '@/api/modules/config';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const formInline = reactive({
  clientHomePath: '',
  homeHtml: '',
});
const rules = ref<FormRules>({
  siteName: [{ required: true, trigger: 'blur', message: '请填写网站名称' }],
});
const formRef = ref<FormInstance>();

async function queryAllconfig() {
  const res = await apiConfig.queryConfig({
    keys: ['clientHomePath', 'homeHtml'],
  });
  Object.assign(formInline, res.data);
}

function handlerUpdateConfig() {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await apiConfig.setConfig({ settings: fotmatSetting(formInline) });
        ElMessage.success('变更欢迎页设置成功');
      } catch (error) {}
      queryAllconfig();
    } else {
      ElMessage.error('请填写完整信息');
    }
  });
}

function fotmatSetting(settings: any) {
  return Object.keys(settings).map((key) => {
    return {
      configKey: key,
      configVal: settings[key],
    };
  });
}

onMounted(() => {
  queryAllconfig();
});
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">首页设置</div>
      </template>
      <template #content>
        <div class="text-sm/6">
          <div>
            首页设置支持配置用户访问系统时的默认页面。可以选择欢迎页、聊天页、AI编程页或AI绘本创作页作为首页。
          </div>
          <div>若选择欢迎页，可以在此处自定义欢迎页面内容。选择AI编程页可以提供给用户一个代码编辑器和实时预览功能。选择AI绘本创作页可以为儿童用户提供直观的绘本创作体验。</div>
          <div class="mt-2 text-gray-500">
            <strong>推荐：</strong> 您可以在其他专业的 HTML 编辑器（如 VS
            Code、Sublime
            Text）中编辑欢迎页面内容并复制粘贴到此处，以获得更好的编辑体验。
          </div>
        </div>
      </template>
      <HButton outline @click="handlerUpdateConfig">
        <SvgIcon name="i-ri:file-text-line" />
        保存设置
      </HButton>
    </PageHeader>

    <el-card style="margin: 20px">
      <el-form
        ref="formRef"
        :rules="rules"
        :model="formInline"
        label-width="150px"
      >
        <el-row>
          <el-col :xs="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="首页设置" prop="clientHomePath">
              <el-select v-model="formInline.clientHomePath" placeholder="请选择首页">
                <el-option label="欢迎页" value="/home" />
                <el-option label="聊天页" value="/chat" />
                <el-option label="AI编程页" value="/aiProgramming" />
                <el-option label="AI绘本创作页" value="/storybook" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="formInline.clientHomePath === '/home'">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="欢迎页（HTML）" prop="homeHtml">
              <el-input
                v-model="formInline.homeHtml"
                placeholder="请输入自定义欢迎页内容"
                type="textarea"
                :rows="10"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="formInline.clientHomePath === '/chat'">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-alert
              title="聊天页面已设置为首页"
              type="success"
              description="用户访问系统时将直接进入聊天页面，该页面提供了AI对话和交互功能。"
              :closable="false"
            />
          </el-col>
        </el-row>

        <el-row v-if="formInline.clientHomePath === '/aiProgramming'">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-alert
              title="AI编程页面已设置为首页"
              type="success"
              description="用户访问系统时将直接进入AI编程页面，该页面提供了代码编辑器和实时预览功能。"
              :closable="false"
            />
          </el-col>
        </el-row>

        <el-row v-if="formInline.clientHomePath === '/storybook'">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-alert
              title="AI绘本创作页面已设置为首页"
              type="success"
              description="用户访问系统时将直接进入AI绘本创作页面，该页面提供了儿童友好的绘本创作功能，适合小学生使用。"
              :closable="false"
            />
          </el-col>
        </el-row>

        <el-col :xs="28" :md="24" :lg="20" :xl="12" style="margin-top: 20px">
          <el-form-item label="预览">
            <iframe
              class="w-full h-100 border border-gray-200 rounded-md bg-gray-100"
              :srcdoc="formInline.homeHtml"
              sandbox="allow-same-origin allow-scripts"
            ></iframe>
          </el-form-item>
        </el-col>
      </el-form>
    </el-card>
  </div>
</template>
