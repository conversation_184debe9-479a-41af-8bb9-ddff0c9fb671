<template>
  <div class="work-toolbar">
    <div class="toolbar-container">
      <div class="toolbar-section">
        <!-- 作品管理功能已移除 -->
      </div>

      <div class="toolbar-section">
        <n-tooltip placement="bottom" trigger="hover">
          <template #trigger>
            <button class="toolbar-button" @click="createNewChat">
              <SvgIcon name="ri:add-line" size="18" />
              <span class="button-text">新建</span>
            </button>
          </template>
          创建新的对话
        </n-tooltip>

        <n-tooltip placement="bottom" trigger="hover">
          <template #trigger>
            <button class="toolbar-button" @click="clearChat">
              <SvgIcon name="ri:delete-bin-line" size="18" />
              <span class="button-text">清空</span>
            </button>
          </template>
          清空当前对话
        </n-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SvgIcon } from '@/components/common';
import { NTooltip } from 'naive-ui';

const props = defineProps({
  creationType: {
    type: String,
    default: 'chat'
  },
  content: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['new-chat', 'clear-chat']);

// 作品管理功能已移除

const createNewChat = () => {
  emit('new-chat');
};

const clearChat = () => {
  emit('clear-chat');
};
</script>

<style scoped>
.work-toolbar {
  width: 100%;
  margin-bottom: 0.5rem;
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .toolbar-container {
  background-color: var(--color-gray-800);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.toolbar-section {
  display: flex;
  gap: 0.5rem;
}

.toolbar-button {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-gray-700);
}

.dark .toolbar-button {
  color: var(--color-gray-300);
}

.toolbar-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--color-gray-900);
}

.dark .toolbar-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.button-text {
  font-size: 0.875rem;
}

@media (max-width: 640px) {
  .button-text {
    display: none;
  }

  .toolbar-button {
    padding: 0.5rem;
  }
}
</style>
