{"name": "DeepCreate", "version": "1.0.0", "description": "使用 Nestjs 和 Vue3 搭建的 AIGC 生态社区 持续集成AI能力到社区之中", "main": "index.js", "author": "deepcreate", "keywords": ["ChatGpt", "AIGC", "AI"], "dependencies": {"jsonc-eslint-parser": "^2.4.0", "prettier-linter-helpers": "^1.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^5.62.0", "prettier": "^2.8.8", "typescript": "^5.5.3"}, "repository": {"type": "git", "url": "git+https://github.com/DeepCreate-Team/DeepCreate.git"}, "license": "MIT", "bugs": {"url": "https://github.com/DeepCreate-Team/DeepCreate/issues"}, "homepage": "https://github.com/DeepCreate-Team/DeepCreate/blob/main/README.md"}