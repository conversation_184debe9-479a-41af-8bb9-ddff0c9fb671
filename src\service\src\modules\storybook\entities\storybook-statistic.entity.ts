import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'storybook_statistic' })
export class StorybookStatisticEntity extends BaseEntity {
  @Column({ comment: '统计日期', type: 'date' })
  date: Date;

  @Column({ comment: '新增绘本数', default: 0 })
  newStorybookCount: number;

  @Column({ comment: '总浏览量', default: 0 })
  totalViewCount: number;

  @Column({ comment: '总点赞量', default: 0 })
  totalLikeCount: number;

  @Column({ comment: '活跃用户数', default: 0 })
  activeUserCount: number;
}
