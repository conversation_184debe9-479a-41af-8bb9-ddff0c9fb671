<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NCard, NInput, NButton, NSpace, NTabs, NTabPane, NCollapse, NCollapseItem, NForm, NFormItem, NSelect, NModal, NDivider, NSpin, NGrid, NGridItem, NRadio, NRadioGroup } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 大纲状态
const activeTab = ref('structure');
const showAIAssistModal = ref(false);
const aiPrompt = ref('');
const isGenerating = ref(false);
const aiResponse = ref('');

// AI助手状态
const authStore = useAuthStore();
const controller = ref(null);

// 故事结构
const storyStructure = reactive({
  title: props.projectData.title || '',
  theme: props.projectData.outline?.theme || '',
  targetAge: props.projectData.outline?.targetAge || '',
  mainIdea: props.projectData.outline?.mainIdea || '',
  beginning: props.projectData.outline?.beginning || '',
  middle: props.projectData.outline?.middle || '',
  end: props.projectData.outline?.end || '',
  moralLesson: props.projectData.outline?.moralLesson || ''
});

// 场景列表
const scenes = ref(props.projectData.outline?.scenes || []);

// 年龄段选项
const ageOptions = [
  { label: '3-5岁', value: '3-5' },
  { label: '6-8岁', value: '6-8' },
  { label: '9-12岁', value: '9-12' }
];

// 添加新场景
const addNewScene = () => {
  scenes.value.push({
    id: Date.now(),
    title: `场景 ${scenes.value.length + 1}`,
    description: '',
    visualNotes: '',
    order: scenes.value.length + 1
  });
};

// 删除场景
const removeScene = (index: number) => {
  if (confirm('确定要删除这个场景吗？')) {
    scenes.value.splice(index, 1);
    // 重新排序
    scenes.value.forEach((scene, idx) => {
      scene.order = idx + 1;
      scene.title = `场景 ${idx + 1}`;
    });
  }
};

// 移动场景顺序
const moveScene = (index: number, direction: 'up' | 'down') => {
  if (direction === 'up' && index > 0) {
    [scenes.value[index], scenes.value[index - 1]] = [scenes.value[index - 1], scenes.value[index]];
  } else if (direction === 'down' && index < scenes.value.length - 1) {
    [scenes.value[index], scenes.value[index + 1]] = [scenes.value[index + 1], scenes.value[index]];
  }

  // 重新排序
  scenes.value.forEach((scene, idx) => {
    scene.order = idx + 1;
    scene.title = `场景 ${idx + 1}`;
  });
};



// 保存大纲
const saveOutline = () => {
  props.projectData.title = storyStructure.title;
  props.projectData.outline = {
    ...props.projectData.outline,
    ...storyStructure,
    scenes: scenes.value
  };
  window.$message?.success('故事大纲已保存');
};

// 打开AI助手
const openAIAssist = (type: string) => {
  aiPrompt.value = '';
  aiResponse.value = '';

  switch (type) {
    case 'structure':
      aiPrompt.value = '请帮我构思一个儿童绘本的故事结构，主题是关于友谊和分享。';
      break;
    case 'scene':
      aiPrompt.value = '请帮我设计一个儿童绘本的场景，这个场景发生在一个魔法森林里。';
      break;
    default:
      aiPrompt.value = '请帮我完善我的儿童绘本故事。';
  }

  showAIAssistModal.value = true;
};

// 生成AI回答
const generateAIResponse = async () => {
  if (!aiPrompt.value.trim()) {
    window.$message?.warning('请输入提示内容');
    return;
  }

  try {
    isGenerating.value = true;
    aiResponse.value = '';

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建提示词
    let prompt = aiPrompt.value;

    // 根据不同类型的提示，增强提示词
    if (prompt.includes('故事结构')) {
      prompt = `
请为一本儿童绘本设计故事结构。

${prompt}

请提供以下内容：
1. 开头：介绍主角和背景设定
2. 中间：主要冲突或挑战
3. 转折点：故事的关键转变
4. 结尾：问题解决和结局
5. 寓意：故事想要传达的价值观或教育意义

请使用适合儿童的语言和主题，确保故事积极向上，有教育意义。
`;
    } else if (prompt.includes('场景')) {
      prompt = `
请为儿童绘本设计一个详细的场景。

${prompt}

请提供以下内容：
1. 场景描述：详细描述场景的整体氛围和环境
2. 视觉元素：列出5-6个关键的视觉元素，包括颜色、形状、特点等
3. 情绪氛围：这个场景想要传达的情绪和感受
4. 互动元素：可以加入的互动或动态元素

请确保场景描述生动形象，适合儿童阅读，并且有助于故事情节发展。
`;
    } else {
      prompt = `
请为儿童绘本创作提供专业建议。

${prompt}

请从以下几个方面提供具体的建议：
1. 角色塑造
2. 故事结构
3. 语言风格
4. 视觉设计
5. 教育价值
6. 读者互动

请确保建议具体实用，适合儿童绘本创作，并考虑到不同年龄段儿童的认知特点。
`;
    }

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        // 提取最新的响应内容
        responseText = text;
        aiResponse.value = responseText;
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

  } catch (error) {
    console.error('AI生成失败:', error);
    window.$message?.error('AI生成失败，请稍后重试');
  } finally {
    isGenerating.value = false;
    controller.value = null;
  }
};

// 应用AI建议
const applyAISuggestion = () => {
  if (!aiResponse.value) {
    window.$message?.warning('没有可应用的AI建议');
    return;
  }

  // 根据当前的提示类型，将AI建议应用到不同的字段
  if (aiPrompt.value.includes('故事结构')) {
    // 尝试从AI回答中提取故事结构信息
    const response = aiResponse.value;

    // 提取标题（如果有）
    const titleMatch = response.match(/标题[：:]\s*(.+)/i);
    if (titleMatch && titleMatch[1]) {
      storyStructure.title = titleMatch[1].trim();
    }

    // 提取主题（如果有）
    const themeMatch = response.match(/主题[：:]\s*(.+)/i);
    if (themeMatch && themeMatch[1]) {
      storyStructure.theme = themeMatch[1].trim();
    }

    // 提取开头
    const beginningMatch = response.match(/开头[：:]([\s\S]*?)(?=中间[：:]|转折点[：:]|结尾[：:]|寓意[：:]|$)/i);
    if (beginningMatch && beginningMatch[1]) {
      storyStructure.beginning = beginningMatch[1].trim();
    }

    // 提取中间
    const middleMatch = response.match(/中间[：:]([\s\S]*?)(?=转折点[：:]|结尾[：:]|寓意[：:]|$)/i);
    if (middleMatch && middleMatch[1]) {
      storyStructure.middle = middleMatch[1].trim();
    }

    // 提取结尾
    const endMatch = response.match(/结尾[：:]([\s\S]*?)(?=寓意[：:]|$)/i);
    if (endMatch && endMatch[1]) {
      storyStructure.end = endMatch[1].trim();
    }

    // 提取寓意
    const moralMatch = response.match(/寓意[：:]([\s\S]*?)(?=$)/i);
    if (moralMatch && moralMatch[1]) {
      storyStructure.moralLesson = moralMatch[1].trim();
    }
  } else if (aiPrompt.value.includes('场景')) {
    // 创建新场景并添加到场景列表
    const newScene = {
      id: Date.now(),
      title: `场景 ${scenes.value.length + 1}`,
      description: aiResponse.value,
      visualNotes: '',
      order: scenes.value.length + 1
    };

    scenes.value.push(newScene);
  }

  // 保存更改
  saveOutline();

  window.$message?.success('AI建议已应用到故事大纲');
  showAIAssistModal.value = false;
};

// 保存到项目
onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="story-outline">
    <div class="outline-header">
      <h2 class="section-title">绘本创作</h2>
      <p class="section-description">设计你的故事、角色和场景</p>

      <!-- 大纲类型切换 -->
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane name="structure" tab="故事结构">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:file-list-line" size="16" class="mr-1" />
              <span>故事结构</span>
            </div>
          </template>
        </NTabPane>

        <NTabPane name="scenes" tab="场景分镜">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:layout-line" size="16" class="mr-1" />
              <span>场景分镜</span>
            </div>
          </template>
        </NTabPane>
      </NTabs>
    </div>

    <div class="outline-content">
      <!-- 故事结构 -->
      <div v-if="activeTab === 'structure'" class="structure-container">
        <NCard class="structure-card">
          <div class="card-header">
            <h3 class="card-title">故事基本结构</h3>
            <NButton size="small" @click="openAIAssist('structure')" class="ai-assist-btn">
              <SvgIcon name="ri:robot-line" size="14" class="mr-1" />
              AI助手
            </NButton>
          </div>

          <NForm label-placement="left" label-width="auto" :model="storyStructure">
            <NFormItem label="故事标题">
              <NInput v-model:value="storyStructure.title" placeholder="输入故事标题" />
            </NFormItem>

            <NFormItem label="主题">
              <NInput v-model:value="storyStructure.theme" placeholder="故事的主题是什么？例如：友谊、勇气、分享..." />
            </NFormItem>

            <NFormItem label="目标年龄">
              <NSelect v-model:value="storyStructure.targetAge" :options="ageOptions" placeholder="选择目标读者年龄段" />
            </NFormItem>

            <NFormItem label="核心理念">
              <NInput v-model:value="storyStructure.mainIdea" type="textarea" placeholder="这个故事想要传达什么核心理念？" />
            </NFormItem>

            <NDivider>故事三段式结构</NDivider>

            <NFormItem label="开始">
              <NInput v-model:value="storyStructure.beginning" type="textarea" placeholder="故事是如何开始的？介绍主角和背景..." />
            </NFormItem>

            <NFormItem label="中间">
              <NInput v-model:value="storyStructure.middle" type="textarea" placeholder="主角面临什么挑战或问题？发生了什么冲突？" />
            </NFormItem>

            <NFormItem label="结尾">
              <NInput v-model:value="storyStructure.end" type="textarea" placeholder="问题如何解决？主角学到了什么？" />
            </NFormItem>

            <NFormItem label="寓意">
              <NInput v-model:value="storyStructure.moralLesson" type="textarea" placeholder="这个故事的寓意或教育意义是什么？" />
            </NFormItem>
          </NForm>

          <div class="form-actions">
            <NButton type="primary" @click="saveOutline">
              <SvgIcon name="ri:save-line" size="16" class="mr-1" />
              保存大纲
            </NButton>
          </div>
        </NCard>

        <div class="structure-tips">
          <h3 class="tips-title">故事结构提示</h3>
          <div class="tips-content">
            <p><strong>开始：</strong> 介绍主角、设定和初始情况。让读者了解故事发生的背景。</p>
            <p><strong>中间：</strong> 引入问题或挑战，创造冲突。主角尝试解决问题但可能遇到困难。</p>
            <p><strong>结尾：</strong> 解决问题，展示主角的成长或变化。提供一个满意的结局和可能的教训。</p>
            <p><strong>提示：</strong> 儿童绘本通常有简单明了的情节，强调积极的价值观和情感连接。</p>
          </div>
        </div>
      </div>

      <!-- 场景分镜 -->
      <div v-else-if="activeTab === 'scenes'" class="scenes-container">
        <div class="scenes-header">
          <h3 class="scenes-title">场景列表</h3>
          <div class="scenes-actions">
            <NButton @click="addNewScene">
              <SvgIcon name="ri:add-line" size="16" class="mr-1" />
              添加场景
            </NButton>
            <NButton @click="openAIAssist('scene')">
              <SvgIcon name="ri:robot-line" size="16" class="mr-1" />
              AI助手
            </NButton>
          </div>
        </div>

        <div v-if="scenes.length === 0" class="empty-scenes">
          <SvgIcon name="ri:layout-line" size="48" class="empty-icon" />
          <p>还没有添加场景，点击"添加场景"开始创建</p>
        </div>

        <NCollapse v-else>
          <NCollapseItem
            v-for="(scene, index) in scenes"
            :key="scene.id"
            :title="scene.title"
          >
            <template #header-extra>
              <div class="scene-actions">
                <NButton size="tiny" @click.stop="moveScene(index, 'up')" :disabled="index === 0">
                  <SvgIcon name="ri:arrow-up-s-line" size="14" />
                </NButton>
                <NButton size="tiny" @click.stop="moveScene(index, 'down')" :disabled="index === scenes.length - 1">
                  <SvgIcon name="ri:arrow-down-s-line" size="14" />
                </NButton>
                <NButton size="tiny" @click.stop="removeScene(index)">
                  <SvgIcon name="ri:delete-bin-line" size="14" />
                </NButton>
              </div>
            </template>

            <div class="scene-form">
              <NForm label-placement="left" label-width="auto">
                <NFormItem label="场景描述">
                  <NInput
                    v-model:value="scene.description"
                    type="textarea"
                    placeholder="描述这个场景中发生了什么？角色做了什么？"
                  />
                </NFormItem>

                <NFormItem label="视觉笔记">
                  <NInput
                    v-model:value="scene.visualNotes"
                    type="textarea"
                    placeholder="描述这个场景的视觉元素：背景、颜色、氛围、重要物品等"
                  />
                </NFormItem>
              </NForm>
            </div>
          </NCollapseItem>
        </NCollapse>

        <div class="form-actions">
          <NButton type="primary" @click="saveOutline">
            <SvgIcon name="ri:save-line" size="16" class="mr-1" />
            保存场景
          </NButton>
        </div>

        <div class="scenes-tips">
          <h3 class="tips-title">分镜提示</h3>
          <div class="tips-content">
            <p><strong>场景数量：</strong> 儿童绘本通常有10-12个场景（双页跨页），每个场景应该推动故事发展。</p>
            <p><strong>视觉连贯性：</strong> 确保场景之间有视觉连贯性，使用相似的色彩方案和风格。</p>
            <p><strong>页面转换：</strong> 考虑页面之间的转换，创造期待感和惊喜。</p>
            <p><strong>视觉焦点：</strong> 每个场景应有明确的视觉焦点，引导读者的注意力。</p>
          </div>
        </div>
      </div>


    </div>

    <!-- AI助手弹窗 -->
    <NModal
      v-model:show="showAIAssistModal"
      preset="card"
      style="width: 90%; max-width: 700px;"
      title="AI创作助手"
      :bordered="false"
      size="huge"
    >
      <div class="ai-assist-modal">
        <div class="ai-prompt-section">
          <h3 class="ai-section-title">你的提示</h3>
          <NInput
            v-model:value="aiPrompt"
            type="textarea"
            placeholder="描述你需要AI帮助的内容..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
          <NButton
            type="primary"
            @click="generateAIResponse"
            :loading="isGenerating"
            :disabled="!aiPrompt.trim()"
            class="generate-btn"
          >
            生成建议
          </NButton>
        </div>

        <!-- 加载中 -->
        <div v-if="isGenerating" class="ai-loading-section">
          <NSpin size="large" />
          <p class="loading-text">AI正在生成建议...</p>
        </div>

        <!-- AI回答 -->
        <div v-else-if="aiResponse" class="ai-response-section">
          <h3 class="ai-section-title">AI建议</h3>
          <div class="ai-response">
            <div v-html="aiResponse.replace(/\n/g, '<br>')"></div>
          </div>
          <div class="response-actions">
            <NButton type="primary" @click="applyAISuggestion">应用建议</NButton>
            <NButton @click="aiResponse = ''">清除</NButton>
          </div>
        </div>
      </div>
    </NModal>


  </div>
</template>

<style scoped>
.story-outline {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  overflow-y: auto;
}

.outline-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.dark .section-title {
  color: #e2e8f0;
}

.section-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.dark .section-description {
  color: #94a3b8;
}

.tab-label {
  display: flex;
  align-items: center;
}

.outline-content {
  flex: 1;
  overflow-y: auto;
}

/* 故事结构样式 */
.structure-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.structure-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .structure-card {
  background-color: #1e293b;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .card-title {
  color: #e2e8f0;
}

.ai-assist-btn {
  background-color: #f0f9ff;
  color: #0ea5e9;
}

.dark .ai-assist-btn {
  background-color: #0c4a6e;
  color: #7dd3fc;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.structure-tips {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid #3b82f6;
}

.dark .structure-tips {
  background-color: #0f172a;
  border-left: 4px solid #3b82f6;
}

.tips-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .tips-title {
  color: #e2e8f0;
}

.tips-content {
  font-size: 0.875rem;
  color: #475569;
}

.dark .tips-content {
  color: #cbd5e1;
}

.tips-content p {
  margin-bottom: 0.5rem;
}

/* 场景分镜样式 */
.scenes-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.scenes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.scenes-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .scenes-title {
  color: #e2e8f0;
}

.scenes-actions {
  display: flex;
  gap: 0.5rem;
}

.empty-scenes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  color: #94a3b8;
}

.dark .empty-scenes {
  background-color: #1e293b;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.scene-actions {
  display: flex;
  gap: 0.25rem;
}

.scene-form {
  padding: 0.5rem 0;
}

.scenes-tips {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid #f59e0b;
  margin-top: 1rem;
}

.dark .scenes-tips {
  background-color: #0f172a;
  border-left: 4px solid #f59e0b;
}

/* AI助手弹窗样式 */
.ai-assist-modal {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.ai-section-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .ai-section-title {
  color: #e2e8f0;
}

.generate-btn {
  margin-top: 0.75rem;
}

.ai-loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  gap: 1rem;
}

.loading-text {
  color: #64748b;
  font-size: 0.875rem;
}

.dark .loading-text {
  color: #94a3b8;
}

.ai-response {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.6;
}

.dark .ai-response {
  background-color: #1e293b;
}

.response-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* 角色设计样式 */
.characters-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.characters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.characters-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .characters-title {
  color: #e2e8f0;
}

.characters-actions {
  display: flex;
  gap: 0.5rem;
}

.add-character-btn {
  background-color: #ecfdf5;
  color: #10b981;
}

.dark .add-character-btn {
  background-color: #064e3b;
  color: #6ee7b7;
}

.random-btn {
  background-color: #f0f9ff;
  color: #0ea5e9;
}

.dark .random-btn {
  background-color: #0c4a6e;
  color: #7dd3fc;
}

.empty-characters {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  color: #94a3b8;
}

.dark .empty-characters {
  background-color: #1e293b;
  color: #94a3b8;
}

.characters-grid {
  margin-bottom: 1.5rem;
}

.character-card {
  height: 100%;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.character-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.character-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  background-color: #f1f5f9;
}

.dark .character-image {
  background-color: #334155;
}

.character-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.image-placeholder.large {
  gap: 1rem;
}

.image-placeholder p {
  font-size: 0.875rem;
  text-align: center;
  max-width: 80%;
}

.character-role {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.role-protagonist {
  background-color: #3b82f6;
}

.role-antagonist {
  background-color: #ef4444;
}

.role-supporting {
  background-color: #f59e0b;
}

.character-info {
  padding: 0 0.5rem;
}

.character-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #1e293b;
}

.dark .character-name {
  color: #e2e8f0;
}

.character-age {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.dark .character-age {
  color: #94a3b8;
}

.character-description {
  font-size: 0.875rem;
  color: #475569;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .character-description {
  color: #cbd5e1;
}

.character-actions {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.character-card:hover .character-actions {
  opacity: 1;
}

.delete-btn {
  background-color: rgba(255, 255, 255, 0.8);
  color: #ef4444;
}

.dark .delete-btn {
  background-color: rgba(30, 41, 59, 0.8);
  color: #f87171;
}

.design-tips {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid #8b5cf6;
}

.dark .design-tips {
  background-color: #0f172a;
  border-left: 4px solid #8b5cf6;
}

/* 角色编辑器样式 */
.character-editor {
  display: flex;
  flex-direction: column;
}

.editor-layout {
  display: flex;
  gap: 1.5rem;
}

.editor-form {
  flex: 3;
}

.editor-preview {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-image {
  position: relative;
  height: 300px;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: #f1f5f9;
}

.dark .preview-image {
  background-color: #334155;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.dark .generating-overlay {
  background-color: rgba(30, 41, 59, 0.8);
}

.preview-info {
  padding: 0.5rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
}

.dark .preview-info {
  background-color: #1e293b;
}

.preview-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.dark .preview-name {
  color: #e2e8f0;
}

.preview-role {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.preview-description {
  font-size: 0.875rem;
  color: #475569;
  line-height: 1.5;
}

.dark .preview-description {
  color: #cbd5e1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .story-outline {
    padding: 1rem;
  }

  .scenes-header, .characters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .scenes-actions, .characters-actions {
    width: 100%;
    justify-content: space-between;
  }

  .editor-layout {
    flex-direction: column;
  }

  .preview-image {
    height: 250px;
  }
}
</style>
