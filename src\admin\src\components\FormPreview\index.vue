<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { fieldTypeLabels } from '@/constants/formFieldTypes';
import { FormField } from '@/services/FormService';

const props = defineProps({
  fields: {
    type: [String, Array],
    default: '[]'
  },
  formName: {
    type: String,
    default: ''
  },
  formDescription: {
    type: String,
    default: ''
  },
  initialValues: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 表单字段
const formFields = ref<FormField[]>([]);

// 表单数据
const formData = ref<Record<string, any>>({});

// 表单引用
const formRef = ref();

// 监听fields变化
watch(() => props.fields, (newVal) => {
  try {
    if (typeof newVal === 'string') {
      formFields.value = JSON.parse(newVal);
    } else {
      formFields.value = [...newVal];
    }

    // 初始化表单数据
    initFormData();
  } catch (error) {
    console.error('解析表单字段失败', error);
    formFields.value = [];
    formData.value = {};
  }
}, { immediate: true, deep: true });

// 监听initialValues变化
watch(() => props.initialValues, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 合并初始值到表单数据
    formData.value = { ...formData.value, ...newVal };
  }
}, { immediate: true, deep: true });

// 初始化表单数据
function initFormData() {
  const data: Record<string, any> = {};

  formFields.value.forEach(field => {
    const fieldName = field.name || field.label;

    // 根据字段类型设置初始值
    switch (field.type) {
      case 'checkbox':
        data[fieldName] = [];
        break;
      case 'switch':
        data[fieldName] = false;
        break;
      case 'slider':
      case 'rate':
      case 'number':
        data[fieldName] = field.min || 0;
        break;
      default:
        data[fieldName] = '';
    }
  });

  formData.value = data;
}

// 提交表单
function handleSubmit() {
  if (formRef.value) {
    formRef.value.validate((valid: boolean) => {
      if (valid) {
        emit('submit', formData.value);
      } else {
        ElMessage.error('表单验证失败，请检查输入');
      }
    });
  } else {
    emit('submit', formData.value);
  }
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  // 重新初始化表单数据
  initFormData();

  ElMessage.info('表单已重置');
}

// 获取字段类型标签
function getFieldTypeLabel(type: string) {
  return fieldTypeLabels[type] || type;
}
</script>

<template>
  <div class="form-preview">
    <div class="form-preview-header">
      <h2 v-if="props.formName" class="form-preview-title">{{ props.formName }}</h2>
      <p v-if="props.formDescription" class="form-preview-description">{{ props.formDescription }}</p>
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      label-position="top"
      class="form-preview-form"
    >
      <template v-if="formFields.length > 0">
        <el-form-item
          v-for="(field, index) in formFields"
          :key="index"
          :label="field.label"
          :prop="field.name || field.label"
          :required="field.required"
        >
          <!-- 输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="formData[field.name || field.label]"
            :placeholder="field.placeholder || `请输入${field.label}`"
          />

          <!-- 文本域 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.name || field.label]"
            type="textarea"
            :rows="field.rows || 4"
            :placeholder="field.placeholder || `请输入${field.label}`"
          />

          <!-- 数字输入框 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="formData[field.name || field.label]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            style="width: 100%"
          />

          <!-- 选择器 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="formData[field.name || field.label]"
            :placeholder="field.placeholder || `请选择${field.label}`"
            style="width: 100%"
          >
            <el-option
              v-for="(option, optIndex) in field.options"
              :key="optIndex"
              :label="typeof option === 'object' && option !== null && 'label' in option ? option.label : option"
              :value="typeof option === 'object' && option !== null && 'value' in option ? option.value : option"
            />
          </el-select>

          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="formData[field.name || field.label]"
          >
            <el-radio
              v-for="(option, optIndex) in field.options"
              :key="optIndex"
              :value="typeof option === 'object' && option !== null && 'value' in option ? option.value : option"
            >
              {{ typeof option === 'object' && option !== null && 'label' in option ? option.label : option }}
            </el-radio>
          </el-radio-group>

          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.name || field.label]"
          >
            <el-checkbox
              v-for="(option, optIndex) in field.options"
              :key="optIndex"
              :label="typeof option === 'object' && option !== null && 'value' in option ? option.value : option"
            >
              {{ typeof option === 'object' && option !== null && 'label' in option ? option.label : option }}
            </el-checkbox>
          </el-checkbox-group>

          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.name || field.label]"
            :active-text="field.activeText || '是'"
            :inactive-text="field.inactiveText || '否'"
          />

          <!-- 滑块 -->
          <el-slider
            v-else-if="field.type === 'slider'"
            v-model="formData[field.name || field.label]"
            :min="field.min || 0"
            :max="field.max || 100"
            :step="field.step || 1"
          />

          <!-- 日期选择器 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="formData[field.name || field.label]"
            type="date"
            :placeholder="field.placeholder || `请选择${field.label}`"
            style="width: 100%"
          />

          <!-- 时间选择器 -->
          <el-time-picker
            v-else-if="field.type === 'time'"
            v-model="formData[field.name || field.label]"
            :placeholder="field.placeholder || `请选择${field.label}`"
            style="width: 100%"
          />

          <!-- 日期时间选择器 -->
          <el-date-picker
            v-else-if="field.type === 'datetime'"
            v-model="formData[field.name || field.label]"
            type="datetime"
            :placeholder="field.placeholder || `请选择${field.label}`"
            style="width: 100%"
          />

          <!-- 评分 -->
          <el-rate
            v-else-if="field.type === 'rate'"
            v-model="formData[field.name || field.label]"
            :max="field.max || 5"
          />

          <!-- 颜色选择器 -->
          <el-color-picker
            v-else-if="field.type === 'color'"
            v-model="formData[field.name || field.label]"
          />

          <!-- 默认输入框 -->
          <el-input
            v-else
            v-model="formData[field.name || field.label]"
            :placeholder="field.placeholder || `请输入${field.label}`"
          />
        </el-form-item>
      </template>

      <el-empty v-else description="表单未定义任何字段" />

      <div class="form-preview-actions">
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped>
.form-preview {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-preview-header {
  margin-bottom: 24px;
  text-align: center;
}

.form-preview-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.form-preview-description {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.form-preview-form {
  margin-top: 24px;
}

.form-preview-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 12px;
}
</style>
