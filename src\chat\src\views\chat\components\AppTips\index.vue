<script lang="ts" setup>
import { fetchQueryOneCatAPI } from '@/api/appStore';
import { fetchFormsByAppIdAPI } from '@/api/form';
import { SvgIcon } from '@/components/common';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { inject, onMounted, ref, watch } from 'vue';
import FormMessage from '../FormMessage/index.vue';
const { isMobile } = useBasicLayout();

const appDetail: any = ref(null);
const hasForms = ref(false);

const props = defineProps<{
  appId: number;
}>();

const onConversation = inject<any>('onConversation');

const queryAppInfo = async (appId: number) => {
  try {
    const res: any = await fetchQueryOneCatAPI({ id: appId });
    appDetail.value = res.data;

    // 检查是否有表单
    if (res.data.forms && res.data.forms.length > 0) {
      hasForms.value = true;
    } else {
      // 如果应用详情中没有表单信息，尝试直接获取
      try {
        const formsRes = await fetchFormsByAppIdAPI(appId);
        if (formsRes.data && formsRes.data.length > 0) {
          hasForms.value = true;
        }
      } catch (error) {
        console.error('获取表单失败', error);
      }
    }
  } catch (error) {}
};

function useDemo(item: string) {
  onConversation({
    msg: item,
    model: appDetail?.value?.model,
    modelAvatar: appDetail.value.modelAvatar,
  });
}

function bgRandomColor() {
  const hues = [
    'bg-blue-300',
    'bg-red-300',
    'bg-green-300',
    'bg-yellow-300',
    'bg-purple-300',
    'bg-pink-300',
  ];
  return hues[Math.floor(Math.random() * hues.length)];
}

onMounted(() => {
  if (props.appId) {
    queryAppInfo(props.appId);
  }
});

watch(
  () => props.appId,
  (newVal) => {
    if (newVal) {
      queryAppInfo(newVal);
    }
  }
);
</script>

<template>
  <div
    v-if="appDetail"
    :class="[isMobile ? 'mb-16' : 'mt-8']"
    class="px-4 select-none w-full flex flex-col items-center justify-start"
  >
    <!-- 头像和名称 -->
    <div class="w-full md:max-w-[40rem] mb-6 transition-all duration-500 transform">
      <div class="flex flex-col items-center justify-center">
        <div
          v-if="appDetail.coverImg && appDetail.coverImg.startsWith('emoji:')"
          class="flex-shrink-0 dark:ring-gray-400 rounded-lg w-16 h-16 mb-4 flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 rounded-xl shadow-md"
        >
          <span class="text-4xl">{{ appDetail.coverImg.replace('emoji:', '') }}</span>
        </div>
        <div
          v-else-if="appDetail.coverImg"
          class="flex-shrink-0 dark:ring-gray-400 rounded-xl mb-4 shadow-md overflow-hidden"
        >
          <img
            :src="appDetail.coverImg"
            class="w-16 h-16 object-cover"
            alt="app-image"
          />
        </div>
        <div
          v-else
          :class="[
            bgRandomColor(),
            'flex-shrink-0 dark:ring-gray-400 rounded-xl w-16 h-16 flex items-center justify-center mb-4 shadow-md',
          ]"
        >
          <span class="text-white text-xl font-bold">{{
            appDetail.name.slice(0, 2)
          }}</span>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
          {{ appDetail?.name }}
        </h1>
      </div>
    </div>

    <!-- 描述 -->
    <div class="w-full md:max-w-[40rem] mb-6 transition-all duration-500 transform" style="transition-delay: 0.2s">
      <div class="flex items-center justify-center">
        <p class="text-center text-gray-600 dark:text-gray-400 max-w-md">{{ appDetail?.des }}</p>
      </div>
    </div>

    <!-- 表单部分 -->
    <div v-if="hasForms" class="w-full md:max-w-[40rem] mt-4 mb-4 transition-all duration-500 transform" style="transition-delay: 0.4s">
      <FormMessage :appId="props.appId" />
    </div>

    <!-- 示例按钮部分已屏蔽 -->
    <!-- <div v-if="appDetail?.demoData && appDetail.demoData.length > 0" class="w-full md:max-w-[40rem] mt-2">
      <h2 class="text-lg font-medium mb-3 text-center">示例提示</h2>
      <div
        class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 justify-center"
      >
        <div
          v-for="(item, index) in appDetail?.demoData"
          :key="index"
          class="space-y-4"
        >
          <button
            @click="useDemo(item)"
            class="relative flex flex-col gap-2 rounded-xl border border-gray-200 dark:border-gray-800 px-3 pb-4 pt-3 text-start align-top text-sm shadow-sm transition dark:bg-gray-800 dark:hover:bg-gray-700 hover:bg-gray-50 w-full h-full min-h-[4rem] min-w-[8rem] flex-grow"
          >
            <SvgIcon
              class="mb-3 inline-block text-base absolute top-3 left-3"
              icon="material-symbols:tips-and-updates-outline"
            />
            <div
              class="mt-8 line-clamp-2 break-all overflow-hidden text-gray-600 dark:text-gray-500 flex-grow text-sm"
            >
              {{ item }}
            </div>
          </button>
        </div>
      </div>
    </div> -->
  </div>
</template>
