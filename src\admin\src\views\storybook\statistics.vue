<template>
  <div>
    <page-header title="绘本统计分析" />
    <page-main>
      <!-- 时间范围选择 -->
      <el-form :inline="true" :model="searchForm" class="mb-4">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-content">
              <div class="stat-card-icon" style="background-color: #409EFF;">
                <i class="el-icon-picture-outline"></i>
              </div>
              <div class="stat-card-info">
                <div class="stat-card-title">绘本总数</div>
                <div class="stat-card-value">{{ overview.totalStorybooks }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-content">
              <div class="stat-card-icon" style="background-color: #67C23A;">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-card-info">
                <div class="stat-card-title">创作用户数</div>
                <div class="stat-card-value">{{ overview.totalUsers }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-content">
              <div class="stat-card-icon" style="background-color: #E6A23C;">
                <i class="el-icon-view"></i>
              </div>
              <div class="stat-card-info">
                <div class="stat-card-title">总浏览量</div>
                <div class="stat-card-value">{{ overview.totalViews }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-card-content">
              <div class="stat-card-icon" style="background-color: #F56C6C;">
                <i class="el-icon-star-on"></i>
              </div>
              <div class="stat-card-info">
                <div class="stat-card-title">总点赞量</div>
                <div class="stat-card-value">{{ overview.totalLikes }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 趋势图表 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>绘本创作趋势</span>
                <el-radio-group v-model="trendTimeUnit" size="small" @change="loadTrendData">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <div ref="creationTrendChart" style="width: 100%; height: 350px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 用户活跃度和内容分析 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>用户活跃度分析</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="userActivityChart" style="width: 100%; height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>内容热度分析</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="contentHeatChart" style="width: 100%; height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 资源使用统计 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>AI资源使用统计</span>
              </div>
            </template>
            <div class="chart-container">
              <div ref="resourceUsageChart" style="width: 100%; height: 350px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </page-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import ApiStorybook from '@/api/modules/storybook';
import * as echarts from 'echarts';

// 图表实例
let creationTrendChartInstance: echarts.ECharts | null = null;
let userActivityChartInstance: echarts.ECharts | null = null;
let contentHeatChartInstance: echarts.ECharts | null = null;
let resourceUsageChartInstance: echarts.ECharts | null = null;

// 图表DOM引用
const creationTrendChart = ref(null);
const userActivityChart = ref(null);
const contentHeatChart = ref(null);
const resourceUsageChart = ref(null);

// 搜索表单
const searchForm = reactive({
  startDate: '',
  endDate: '',
});

// 日期范围
const dateRange = ref([]);

// 趋势时间单位
const trendTimeUnit = ref('day');

// 数据概览
const overview = reactive({
  totalStorybooks: 0,
  totalUsers: 0,
  totalViews: 0,
  totalLikes: 0,
});

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 搜索
const handleSearch = () => {
  loadData();
};

// 重置搜索
const resetSearch = () => {
  dateRange.value = [];
  searchForm.startDate = '';
  searchForm.endDate = '';
  loadData();
};

// 加载数据
const loadData = async () => {
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    searchForm.startDate = dateRange.value[0];
    searchForm.endDate = dateRange.value[1];
  } else {
    searchForm.startDate = '';
    searchForm.endDate = '';
  }

  try {
    // 加载概览数据
    const overviewRes = await ApiStorybook.getStorybookStatistics({
      type: 'overview',
      ...searchForm,
    });
    
    overview.totalStorybooks = overviewRes.data.totalStorybooks || 0;
    overview.totalUsers = overviewRes.data.totalUsers || 0;
    overview.totalViews = overviewRes.data.totalViews || 0;
    overview.totalLikes = overviewRes.data.totalLikes || 0;

    // 加载各种图表数据
    loadTrendData();
    loadUserActivityData();
    loadContentHeatData();
    loadResourceUsageData();
  } catch (error) {
    console.error('加载统计数据失败', error);
    ElMessage.error('加载统计数据失败');
  }
};

// 加载趋势数据
const loadTrendData = async () => {
  try {
    const trendRes = await ApiStorybook.getStorybookStatistics({
      type: 'trend',
      timeUnit: trendTimeUnit.value,
      ...searchForm,
    });
    
    renderCreationTrendChart(trendRes.data);
  } catch (error) {
    console.error('加载趋势数据失败', error);
    ElMessage.error('加载趋势数据失败');
  }
};

// 加载用户活跃度数据
const loadUserActivityData = async () => {
  try {
    const userActivityRes = await ApiStorybook.getStorybookStatistics({
      type: 'user',
      ...searchForm,
    });
    
    renderUserActivityChart(userActivityRes.data);
  } catch (error) {
    console.error('加载用户活跃度数据失败', error);
    ElMessage.error('加载用户活跃度数据失败');
  }
};

// 加载内容热度数据
const loadContentHeatData = async () => {
  try {
    const contentHeatRes = await ApiStorybook.getStorybookStatistics({
      type: 'content',
      ...searchForm,
    });
    
    renderContentHeatChart(contentHeatRes.data);
  } catch (error) {
    console.error('加载内容热度数据失败', error);
    ElMessage.error('加载内容热度数据失败');
  }
};

// 加载资源使用数据
const loadResourceUsageData = async () => {
  try {
    const resourceUsageRes = await ApiStorybook.getStorybookStatistics({
      type: 'resource',
      ...searchForm,
    });
    
    renderResourceUsageChart(resourceUsageRes.data);
  } catch (error) {
    console.error('加载资源使用数据失败', error);
    ElMessage.error('加载资源使用数据失败');
  }
};

// 渲染创作趋势图表
const renderCreationTrendChart = (data) => {
  if (!creationTrendChart.value) return;
  
  if (!creationTrendChartInstance) {
    creationTrendChartInstance = echarts.init(creationTrendChart.value);
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['新增绘本', '浏览量', '点赞量'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.dates || [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '新增绘本',
        type: 'line',
        data: data.newStorybooks || [],
        smooth: true,
        lineStyle: {
          width: 3,
        },
      },
      {
        name: '浏览量',
        type: 'line',
        data: data.views || [],
        smooth: true,
        lineStyle: {
          width: 3,
        },
      },
      {
        name: '点赞量',
        type: 'line',
        data: data.likes || [],
        smooth: true,
        lineStyle: {
          width: 3,
        },
      },
    ],
  };
  
  creationTrendChartInstance.setOption(option);
};

// 渲染用户活跃度图表
const renderUserActivityChart = (data) => {
  if (!userActivityChart.value) return;
  
  if (!userActivityChartInstance) {
    userActivityChartInstance = echarts.init(userActivityChart.value);
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '用户活跃度',
        type: 'pie',
        radius: '50%',
        data: [
          { value: data.activeUsers || 0, name: '活跃用户' },
          { value: data.occasionalUsers || 0, name: '偶尔使用' },
          { value: data.inactiveUsers || 0, name: '不活跃用户' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
  
  userActivityChartInstance.setOption(option);
};

// 渲染内容热度图表
const renderContentHeatChart = (data) => {
  if (!contentHeatChart.value) return;
  
  if (!contentHeatChartInstance) {
    contentHeatChartInstance = echarts.init(contentHeatChart.value);
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: data.categories || [],
    },
    series: [
      {
        name: '绘本数量',
        type: 'bar',
        data: data.counts || [],
      },
    ],
  };
  
  contentHeatChartInstance.setOption(option);
};

// 渲染资源使用图表
const renderResourceUsageChart = (data) => {
  if (!resourceUsageChart.value) return;
  
  if (!resourceUsageChartInstance) {
    resourceUsageChartInstance = echarts.init(resourceUsageChart.value);
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['图像生成次数', '文本生成次数'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.dates || [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '图像生成次数',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        data: data.imageGenerations || [],
      },
      {
        name: '文本生成次数',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        data: data.textGenerations || [],
      },
    ],
  };
  
  resourceUsageChartInstance.setOption(option);
};

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
  creationTrendChartInstance?.resize();
  userActivityChartInstance?.resize();
  contentHeatChartInstance?.resize();
  resourceUsageChartInstance?.resize();
};

onMounted(() => {
  loadData();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  creationTrendChartInstance?.dispose();
  userActivityChartInstance?.dispose();
  contentHeatChartInstance?.dispose();
  resourceUsageChartInstance?.dispose();
});
</script>

<style scoped>
.stat-card {
  height: 100%;
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
  font-size: 24px;
}

.stat-card-info {
  flex: 1;
}

.stat-card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-card-value {
  font-size: 24px;
  font-weight: bold;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  position: relative;
}
</style>
