<template>
  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">📚</span>
      故事是关于什么的？
    </div>
    <div class="theme-buttons">
      <button
        v-for="option in themeOptions"
        :key="option.value"
        type="button"
        :class="['theme-button', theme === option.value ? 'active' : '']"
        @click="theme = option.value"
      >
        {{ option.label }}
      </button>
      <div class="custom-theme">
        <NInput
          v-model:value="customTheme"
          placeholder="自定义主题..."
          class="custom-theme-input"
          @update:value="updateTheme"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NInput } from 'naive-ui';

// 主题选项
const themeOptions = [
  { label: '友谊', value: 'friendship' },
  { label: '勇气', value: 'courage' },
  { label: '诚实', value: 'honesty' },
  { label: '合作', value: 'cooperation' },
  { label: '创造力', value: 'creativity' },
  { label: '尊重', value: 'respect' },
  { label: '责任', value: 'responsibility' },
  { label: '坚持', value: 'perseverance' },
  { label: '环保', value: 'environment' },
  { label: '多样性', value: 'diversity' },
  { label: '冒险', value: 'adventure' },
  { label: '家庭', value: 'family' },
  { label: '科学', value: 'science' }
];

// 使用defineModel实现双向绑定
const theme = defineModel('theme');

// 自定义主题输入
const customTheme = ref('');

// 监听主题变化
watch(() => theme.value, (newTheme) => {
  // 如果当前主题不在预设选项中，则更新自定义主题输入框
  if (!themeOptions.some(option => option.value === newTheme)) {
    customTheme.value = newTheme;
  }
});

// 更新主题
const updateTheme = (value: string) => {
  if (value.trim()) {
    theme.value = value;
  }
};
</script>

<style scoped>
.form-group {
  margin-bottom: 2.5rem;
  width: 100%;
  position: relative;
}

.form-group.animated-form-group {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-label {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.dark .form-label {
  color: #60a5fa;
}

.form-icon {
  font-size: 1.5rem;
}

.theme-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.theme-button {
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  color: #334155;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-button:hover {
  background-color: #f8fafc;
  border-color: #93c5fd;
  transform: translateY(-2px);
}

.theme-button.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1e40af;
}

.dark .theme-button {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .theme-button:hover {
  background-color: #1e293b;
  border-color: #60a5fa;
}

.dark .theme-button.active {
  border-color: #3b82f6;
  background-color: #1e3a8a;
  color: #e2e8f0;
}

.custom-theme {
  margin-top: 0.5rem;
  width: 100%;
  max-width: 300px;
}

.custom-theme-input {
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.custom-theme-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .custom-theme-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .custom-theme-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}


</style>
