<template>
  <div class="magic-helper-tab">
    <div class="helper-header">
      <h3 class="helper-title">小狐狸创作助手</h3>
      <p class="helper-subtitle">需要帮助？选择一个创意提示或直接提问！</p>
    </div>

    <!-- 聊天历史 -->
    <div class="chat-history">
      <div v-if="chatHistory.length === 0" class="empty-chat">
        <div class="helper-prompts">
          <div
            v-for="(prompt, index) in prompts"
            :key="index"
            class="prompt-card"
            @click="selectPrompt(prompt)"
          >
            <div class="prompt-content">
              <div class="prompt-icon">{{ prompt.icon }}</div>
              <div class="prompt-text">{{ prompt.text }}</div>
            </div>
            <div class="prompt-arrow">
              <span>→</span>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="chat-messages">
        <div
          v-for="message in chatHistory"
          :key="message.id"
          :class="['chat-message', message.role === 'user' ? 'user-message' : 'ai-message']"
        >
          <div class="message-avatar">
            <span v-if="message.role === 'user'" class="emoji-icon">👤</span>
            <span v-else class="emoji-icon">🦊</span>
          </div>
          <div class="message-content">
            <div v-html="message.content.replace(/\n/g, '<br>')"></div>
          </div>
        </div>

        <div v-if="isLoading" class="ai-typing">
          <div class="message-avatar">
            <span class="emoji-icon">🦊</span>
          </div>
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <NInput
        v-model:value="customPrompt"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        placeholder="在这里输入你的创作问题，小狐狸助手将为你解答..."
        @keydown.enter.ctrl="askCustomPrompt"
      />
      <div class="input-actions">
        <NButton size="small" @click="clearChatHistory" v-if="chatHistory.length > 0">
          清空历史
        </NButton>
        <NButton
          type="primary"
          @click="askCustomPrompt"
          :loading="isLoading"
          :disabled="isLoading || !customPrompt.trim()"
        >
          <span class="ask-icon">🔍</span>
          <span>向小狐狸提问</span>
        </NButton>
      </div>
      <div class="input-tips">
        提示：按Ctrl+Enter快速发送
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import { NInput, NButton, NSpace, NTag } from 'naive-ui';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';
import StorybookApi from '@/api/modules/storybook';

const props = defineProps<{
  projectData: any;
}>();

const customPrompt = ref('');
const isLoading = ref(false);
const controller = ref(null);
const authStore = useAuthStore();
const chatHistory = ref([]);

// 小狐狸助手AI配置
const foxAssistantConfig = ref({
  model: '',
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。'
});

// 加载小狐狸助手配置
const loadFoxAssistantConfig = async () => {
  try {
    const res = await StorybookApi.getFoxAssistantConfig();
    if (res.data) {
      const config = res.data;
      // 优先使用专用配置，如果没有则使用通用配置
      foxAssistantConfig.value = {
        model: config.foxAssistantModel || config.aiModel || 'gpt-3.5-turbo',
        temperature: config.foxAssistantTemperature || config.temperature || 0.7,
        maxTokens: config.foxAssistantMaxTokens || config.maxTokens || 2000,
        systemPrompt: config.foxAssistantSystemPrompt || config.systemPrompt ||
          '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。'
      };
      console.log('已加载小狐狸助手配置:', foxAssistantConfig.value);
    }
  } catch (error) {
    console.error('加载小狐狸助手配置失败:', error);
    // 使用默认配置
  }
};

// 组件挂载时加载配置
onMounted(() => {
  loadFoxAssistantConfig();
  // 尝试从本地存储加载聊天历史
  try {
    const savedHistory = localStorage.getItem('fox-assistant-chat-history');
    if (savedHistory) {
      chatHistory.value = JSON.parse(savedHistory);
    }
  } catch (e) {
    console.warn('Failed to load chat history from localStorage', e);
  }
});

// 精简后的提示列表
const prompts = computed(() => {
  return [
    { icon: '📝', text: '给我一些有趣的故事主题创意', action: 'generateStoryIdeas' },
    { icon: '👤', text: '如何创造有趣的角色？', action: 'createCharacterTips' },
    { icon: '🏞️', text: '如何描述一个生动的场景？', action: 'describeSceneTips' },
    { icon: '🎭', text: '如何在场景中展现角色情感？', action: 'emotionInScenesTips' }
  ];
});

// 保存聊天历史到本地存储
const saveChatHistory = () => {
  try {
    localStorage.setItem('fox-assistant-chat-history', JSON.stringify(chatHistory.value));
  } catch (e) {
    console.warn('Failed to save chat history to localStorage', e);
  }
};

// 保存问答记录到项目数据
const saveQuestionToProject = (question, answer) => {
  // 确保项目数据中有aiMagicPen.questions数组
  if (!props.projectData.aiMagicPen) {
    props.projectData.aiMagicPen = { inspirations: [], outlines: [], questions: [] };
  }
  if (!props.projectData.aiMagicPen.questions) {
    props.projectData.aiMagicPen.questions = [];
  }

  // 添加新问答记录
  props.projectData.aiMagicPen.questions.push({
    id: Date.now(),
    question,
    answer,
    timestamp: new Date().toISOString(),
    source: 'reading' // 标记来源为阅读
  });

  // 更新本地存储
  try {
    localStorage.setItem('project-data', JSON.stringify(props.projectData));
  } catch (e) {
    console.warn('Failed to save project data to localStorage', e);
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  const chatHistoryEl = document.querySelector('.chat-history');
  if (chatHistoryEl) {
    chatHistoryEl.scrollTop = chatHistoryEl.scrollHeight;
  }
};

const selectPrompt = async (prompt) => {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建提示词
    let promptText = '';

    switch (prompt.action) {
      case 'generateStoryIdeas':
        promptText = '请给我一些有趣的儿童绘本故事主题创意，提供5-6个不同类型的创意，每个都简短描述。';
        break;
      case 'explainStoryStructure':
        promptText = '请详细解释如何为儿童绘本构建一个好的故事结构，包括开始、中间和结尾应该包含哪些元素。';
        break;
      case 'createCharacterTips':
        promptText = '请提供一些创造有趣的儿童绘本角色的技巧和方法，包括如何设计角色特点、性格和外观。';
        break;
      case 'describeSceneTips':
        promptText = '请提供一些在儿童绘本中描述生动场景的技巧和方法，包括如何使用语言创造出丰富的视觉效果。';
        break;
      case 'emotionInScenesTips':
        promptText = '请提供一些在儿童绘本场景中展现角色情感的技巧和方法，包括如何通过描述和对话表达情感。';
        break;
      default:
        promptText = prompt.text;
    }

    // 添加用户消息到聊天历史
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: promptText,
      timestamp: new Date().toISOString()
    };
    chatHistory.value.push(userMessage);
    await scrollToBottom();

    // 构建请求参数，使用小狐狸助手专用配置
    const params = {
      model: foxAssistantConfig.value.model || authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: '小狐狸助手', // 使用固定名称
      modelType: 1,
      modelAvatar: '🦊',
      prompt: `${foxAssistantConfig.value.systemPrompt}\n\n${promptText}`,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    let lastProcessedIndex = 0;

    params.onDownloadProgress = (progressEvent) => {
      try {
        // 兼容不同版本的axios事件结构
        const xhr = progressEvent.event?.target || progressEvent.target;
        if (!xhr || !xhr.responseText) return;

        const responseText = xhr.responseText;
        const newResponsePart = responseText.substring(lastProcessedIndex);
        lastProcessedIndex = responseText.length; // 更新处理位置

        if (!newResponsePart.trim()) return;

        const responseParts = newResponsePart.trim().split('\n');

        responseParts.forEach((part) => {
          try {
            // 尝试将字符串解析为 JSON 对象
            if (!part.trim()) return;

            // 有些响应可能不是JSON格式，直接使用文本
            let jsonObj;
            try {
              jsonObj = JSON.parse(part);
            } catch (e) {
              // 如果解析失败，直接使用文本
              jsonObj = { text: part };
            }

            // 检查 jsonObj 是否具有有效的 text 字段
            if (jsonObj.text) {
              // 如果已经有AI回复，则更新最后一条消息
              if (chatHistory.value.length > 0 && chatHistory.value[chatHistory.value.length - 1].role === 'assistant') {
                // 累加文本内容
                chatHistory.value[chatHistory.value.length - 1].content += jsonObj.text;
              } else {
                // 否则添加新的AI回复
                chatHistory.value.push({
                  id: Date.now(),
                  role: 'assistant',
                  content: jsonObj.text,
                  timestamp: new Date().toISOString()
                });
              }

              // 更新响应文本（用于保存到项目数据）
              responseText += jsonObj.text;

              // 滚动到底部
              scrollToBottom();
            }
          } catch (error) {
            console.error('处理响应部分时出错:', error, 'part:', part);
          }
        });
      } catch (error) {
        console.error('处理流式响应时出错:', error);
        // 不在这里添加错误消息，让外层的catch处理
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

    // 只有在成功获取到响应时才保存
    if (responseText && responseText.trim()) {
      // 保存问答记录到项目数据
      saveQuestionToProject(promptText, responseText);

      // 保存聊天历史
      saveChatHistory();
    } else {
      // 检查是否已经添加了AI回复
      const hasAiReply = chatHistory.value.some(msg =>
        msg.role === 'assistant' && msg.content && msg.content.trim()
      );

      // 如果没有AI回复，添加一个提示
      if (!hasAiReply) {
        chatHistory.value.push({
          id: Date.now(),
          role: 'assistant',
          content: '抱歉，我没能理解您的问题，请尝试用不同的方式提问。',
          timestamp: new Date().toISOString()
        });

        // 保存聊天历史
        saveChatHistory();
      }
    }

  } catch (error) {
    console.error('小狐狸助手查询失败:', error);

    // 显示友好的错误提示
    let errorMessage = '抱歉，我遇到了一些问题，请稍后再试。';

    // 根据错误类型提供更具体的错误信息
    if (error.message) {
      if (error.message.includes('500')) {
        errorMessage = '抱歉，服务器出现了问题，请稍后再试。';
      } else if (error.message.includes('timeout')) {
        errorMessage = '抱歉，请求超时，请检查网络连接后再试。';
      } else if (error.message.includes('Network Error')) {
        errorMessage = '抱歉，网络连接出现问题，请检查网络后再试。';
      } else if (error.message.includes('canceled')) {
        errorMessage = '请求已取消。';
      } else if (error.message.includes('余额不足')) {
        errorMessage = '抱歉，账户余额不足，请充值后再试。';
      } else if (error.message.includes('违规')) {
        errorMessage = '抱歉，您的提问包含敏感内容，请调整后重试。';
      }
    }

    // 显示错误消息
    window.$message?.error('小狐狸助手查询失败：' + errorMessage);

    // 检查是否已经添加了AI回复
    const lastMessage = chatHistory.value[chatHistory.value.length - 1];
    if (!lastMessage || lastMessage.role !== 'assistant') {
      // 添加错误消息到聊天历史
      chatHistory.value.push({
        id: Date.now(),
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date().toISOString()
      });
    } else {
      // 如果最后一条消息是AI回复但内容为空，更新它
      if (!lastMessage.content || !lastMessage.content.trim()) {
        lastMessage.content = errorMessage;
      }
    }

    // 保存聊天历史（即使是错误消息也保存）
    saveChatHistory();

    scrollToBottom();
  } finally {
    isLoading.value = false;
    controller.value = null;
  }
};

const askCustomPrompt = async () => {
  if (!customPrompt.value.trim() || isLoading.value) return;

  const userPromptText = customPrompt.value;
  isLoading.value = true;

  // 添加用户消息到聊天历史
  const userMessage = {
    id: Date.now(),
    role: 'user',
    content: userPromptText,
    timestamp: new Date().toISOString()
  };
  chatHistory.value.push(userMessage);

  // 清空输入框
  customPrompt.value = '';

  await scrollToBottom();

  try {
    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建请求参数，使用小狐狸助手专用配置
    const params = {
      model: foxAssistantConfig.value.model || authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: '小狐狸助手', // 使用固定名称
      modelType: 1,
      modelAvatar: '🦊',
      prompt: `${foxAssistantConfig.value.systemPrompt}\n\n请回答以下问题：${userPromptText}`,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    let lastProcessedIndex = 0;

    params.onDownloadProgress = (progressEvent) => {
      try {
        // 兼容不同版本的axios事件结构
        const xhr = progressEvent.event?.target || progressEvent.target;
        if (!xhr || !xhr.responseText) return;

        const responseText = xhr.responseText;
        const newResponsePart = responseText.substring(lastProcessedIndex);
        lastProcessedIndex = responseText.length; // 更新处理位置

        if (!newResponsePart.trim()) return;

        const responseParts = newResponsePart.trim().split('\n');

        responseParts.forEach((part) => {
          try {
            // 尝试将字符串解析为 JSON 对象
            if (!part.trim()) return;

            // 有些响应可能不是JSON格式，直接使用文本
            let jsonObj;
            try {
              jsonObj = JSON.parse(part);
            } catch (e) {
              // 如果解析失败，直接使用文本
              jsonObj = { text: part };
            }

            // 检查 jsonObj 是否具有有效的 text 字段
            if (jsonObj.text) {
              // 如果已经有AI回复，则更新最后一条消息
              if (chatHistory.value.length > 0 && chatHistory.value[chatHistory.value.length - 1].role === 'assistant') {
                // 累加文本内容
                chatHistory.value[chatHistory.value.length - 1].content += jsonObj.text;
              } else {
                // 否则添加新的AI回复
                chatHistory.value.push({
                  id: Date.now(),
                  role: 'assistant',
                  content: jsonObj.text,
                  timestamp: new Date().toISOString()
                });
              }

              // 更新响应文本（用于保存到项目数据）
              responseText += jsonObj.text;

              // 滚动到底部
              scrollToBottom();
            }
          } catch (error) {
            console.error('处理响应部分时出错:', error, 'part:', part);
          }
        });
      } catch (error) {
        console.error('处理流式响应时出错:', error);
        // 不在这里添加错误消息，让外层的catch处理
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

    // 只有在成功获取到响应时才保存
    if (responseText && responseText.trim()) {
      // 保存问答记录到项目数据
      saveQuestionToProject(userPromptText, responseText);

      // 保存聊天历史
      saveChatHistory();
    } else {
      // 检查是否已经添加了AI回复
      const hasAiReply = chatHistory.value.some(msg =>
        msg.role === 'assistant' && msg.content && msg.content.trim()
      );

      // 如果没有AI回复，添加一个提示
      if (!hasAiReply) {
        chatHistory.value.push({
          id: Date.now(),
          role: 'assistant',
          content: '抱歉，我没能理解您的问题，请尝试用不同的方式提问。',
          timestamp: new Date().toISOString()
        });

        // 保存聊天历史
        saveChatHistory();
      }
    }

  } catch (error) {
    console.error('小狐狸助手查询失败:', error);

    // 显示友好的错误提示
    let errorMessage = '抱歉，我遇到了一些问题，请稍后再试。';

    // 根据错误类型提供更具体的错误信息
    if (error.message) {
      if (error.message.includes('500')) {
        errorMessage = '抱歉，服务器出现了问题，请稍后再试。';
      } else if (error.message.includes('timeout')) {
        errorMessage = '抱歉，请求超时，请检查网络连接后再试。';
      } else if (error.message.includes('Network Error')) {
        errorMessage = '抱歉，网络连接出现问题，请检查网络后再试。';
      } else if (error.message.includes('canceled')) {
        errorMessage = '请求已取消。';
      } else if (error.message.includes('余额不足')) {
        errorMessage = '抱歉，账户余额不足，请充值后再试。';
      } else if (error.message.includes('违规')) {
        errorMessage = '抱歉，您的提问包含敏感内容，请调整后重试。';
      }
    }

    // 显示错误消息
    window.$message?.error('小狐狸助手查询失败：' + errorMessage);

    // 检查是否已经添加了AI回复
    const lastMessage = chatHistory.value[chatHistory.value.length - 1];
    if (!lastMessage || lastMessage.role !== 'assistant') {
      // 添加错误消息到聊天历史
      chatHistory.value.push({
        id: Date.now(),
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date().toISOString()
      });
    } else {
      // 如果最后一条消息是AI回复但内容为空，更新它
      if (!lastMessage.content || !lastMessage.content.trim()) {
        lastMessage.content = errorMessage;
      }
    }

    // 保存聊天历史（即使是错误消息也保存）
    saveChatHistory();

    scrollToBottom();
  } finally {
    isLoading.value = false;
    controller.value = null;
  }
};

// 清空聊天历史
const clearChatHistory = () => {
  if (confirm('确定要清空聊天历史吗？')) {
    chatHistory.value = [];
    localStorage.removeItem('fox-assistant-chat-history');
  }
};
</script>

<style scoped>
.magic-helper-tab {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.helper-header {
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.helper-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.25rem;
  background: linear-gradient(90deg, #8b5cf6, #6366f1);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .helper-title {
  color: #e2e8f0;
}

.helper-subtitle {
  font-size: 0.875rem;
  color: #64748b;
}

.dark .helper-subtitle {
  color: #94a3b8;
}

.helper-prompts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

.prompt-card {
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  height: 100%;
}

.dark .prompt-card {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.prompt-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.4);
  background-color: #faf5ff;
}

.dark .prompt-card:hover {
  background-color: #2e1065;
  border-color: #7c3aed;
}

.prompt-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.prompt-icon {
  font-size: 1.75rem;
  margin-right: 1rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3e8ff;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.15);
}

.dark .prompt-icon {
  background-color: #4c1d95;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
}

.prompt-card:hover .prompt-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 0 4px 10px rgba(139, 92, 246, 0.25);
}

.prompt-text {
  font-size: 0.95rem;
  font-weight: 500;
  color: #334155;
  line-height: 1.4;
}

.dark .prompt-text {
  color: #e2e8f0;
}

.prompt-arrow {
  color: #a855f7;
  font-size: 1.4rem;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
}

.prompt-card:hover .prompt-arrow {
  opacity: 1;
  transform: translateX(0);
}

@media (max-width: 500px) {
  .helper-prompts {
    grid-template-columns: 1fr;
  }
}

.helper-custom {
  margin-bottom: 1.5rem;
  padding: 0 0.5rem;
}

.custom-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #334155;
  display: flex;
  align-items: center;
}

.custom-title::before {
  content: '✨';
  margin-right: 0.5rem;
}

.dark .custom-title {
  color: #e2e8f0;
}

.custom-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.custom-input-group :deep(.n-input) {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.custom-input-group :deep(.n-input:hover),
.custom-input-group :deep(.n-input--focus) {
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.3);
}

.dark .custom-input-group :deep(.n-input) {
  background-color: #1e293b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dark .custom-input-group :deep(.n-input:hover),
.dark .custom-input-group :deep(.n-input--focus) {
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
  border-color: rgba(139, 92, 246, 0.5);
}

.custom-input-group :deep(.n-input__textarea-el) {
  font-size: 0.95rem;
  padding: 0.75rem;
  line-height: 1.5;
}

.input-tips {
  font-size: 0.75rem;
  color: #64748b;
  text-align: right;
  padding-right: 0.5rem;
}

.dark .input-tips {
  color: #94a3b8;
}

.ask-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
  transition: all 0.3s ease;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 0.75rem;
}

.ask-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.ask-button:active {
  transform: translateY(0);
}

.ask-icon {
  font-size: 1.1rem;
}

/* 聊天历史样式 */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  min-height: 300px;
  max-height: calc(100vh - 300px);
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.05);
}

.dark .chat-history {
  background-color: #1f2937;
  box-shadow: inset 0 2px 6px rgba(0, 0, 0, 0.2);
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #6b7280;
  padding: 1rem;
}

.dark .empty-chat {
  color: #9ca3af;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-message {
  display: flex;
  gap: 0.75rem;
  max-width: 90%;
  animation: fadeIn 0.5s ease;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e7eb;
  flex-shrink: 0;
}

.dark .message-avatar {
  background-color: #374151;
}

.user-message .message-avatar {
  background-color: #dbeafe;
}

.dark .user-message .message-avatar {
  background-color: #1e40af;
}

.ai-message .message-avatar {
  background-color: #f3e8ff;
}

.dark .ai-message .message-avatar {
  background-color: #5b21b6;
}

.emoji-icon {
  font-size: 1.25rem;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  line-height: 1.5;
}

.dark .message-content {
  background-color: #374151;
  color: #e5e7eb;
}

.user-message .message-content {
  background-color: #3b82f6;
  color: white;
  border-bottom-right-radius: 0;
}

.dark .user-message .message-content {
  background-color: #2563eb;
}

.ai-message .message-content {
  background-color: white;
  border-bottom-left-radius: 0;
}

.dark .ai-message .message-content {
  background-color: #1f2937;
}

.ai-typing {
  display: flex;
  gap: 0.75rem;
  align-self: flex-start;
}

.typing-indicator {
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  border-bottom-left-radius: 0;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dark .typing-indicator {
  background-color: #1f2937;
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #d1d5db;
  animation: typing 1.4s infinite both;
}

.dark .typing-indicator span {
  background-color: #6b7280;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

/* 输入区域样式 */
.chat-input {
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .chat-input {
  background-color: #1f2937;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 0.75rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
