<template>
  <div class="share-storybook">
    <div class="share-container">
      <div v-if="loading" class="loading-container">
        <NSpin size="large" />
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="error-container">
        <NResult status="error" title="加载失败" :description="errorMessage">
          <template #footer>
            <NButton @click="fetchStorybook">重试</NButton>
          </template>
        </NResult>
      </div>
      
      <template v-else>
        <div class="share-header">
          <h1>{{ storybook.title || '未命名绘本' }}</h1>
          <p class="subtitle">{{ storybook.description || '暂无描述' }}</p>
          <div class="author-info">
            <span>作者：{{ storybook.authorName || '匿名用户' }}</span>
            <span class="dot-divider">•</span>
            <span>{{ formatDate(storybook.createdAt) }}</span>
          </div>
        </div>
        
        <div class="share-preview">
          <div class="cover-container">
            <img
              v-if="storybook.coverImg"
              :src="storybook.coverImg"
              :alt="storybook.title"
              class="cover-image"
            />
            <div v-else class="cover-placeholder">
              <SvgIcon name="ri:book-open-line" size="64" />
            </div>
          </div>
          
          <div class="preview-info">
            <div class="info-item">
              <SvgIcon name="ri:file-list-line" size="16" />
              <span>{{ storybook.pageCount || 0 }} 页</span>
            </div>
            
            <div class="info-item">
              <SvgIcon name="ri:text" size="16" />
              <span>{{ storybook.wordCount || 0 }} 字</span>
            </div>
            
            <div class="info-item">
              <SvgIcon name="ri:eye-line" size="16" />
              <span>{{ storybook.viewCount || 0 }} 次阅读</span>
            </div>
          </div>
          
          <div class="action-buttons">
            <NButton type="primary" @click="readStorybook">
              <template #icon>
                <SvgIcon name="ri:book-read-line" size="18" />
              </template>
              阅读绘本
            </NButton>
            
            <NButton v-if="allowDownload" @click="downloadStorybook">
              <template #icon>
                <SvgIcon name="ri:download-line" size="18" />
              </template>
              下载绘本
            </NButton>
          </div>
        </div>
        
        <div class="share-footer">
          <p>此绘本由 DeepCreate AI绘本创作平台 提供技术支持</p>
          <div class="share-links">
            <NButton text @click="copyShareLink">
              <template #icon>
                <SvgIcon name="ri:link" size="16" />
              </template>
              复制链接
            </NButton>
            
            <NButton text @click="showQRCode = true">
              <template #icon>
                <SvgIcon name="ri:qr-code-line" size="16" />
              </template>
              二维码
            </NButton>
          </div>
        </div>
      </template>
    </div>
    
    <!-- 二维码弹窗 -->
    <NModal
      v-model:show="showQRCode"
      preset="card"
      title="扫码阅读绘本"
      style="width: 300px"
    >
      <div class="qrcode-container">
        <div ref="qrcodeRef" class="qrcode"></div>
        <p>使用手机扫描二维码阅读绘本</p>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NButton, NSpin, NResult, NModal } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import QRCode from 'qrcode';

const router = useRouter();
const route = useRoute();

// 状态
const loading = ref(true);
const error = ref(false);
const errorMessage = ref('');
const showQRCode = ref(false);
const qrcodeRef = ref(null);
const allowDownload = ref(true);

// 绘本数据
const storybook = ref({
  id: '',
  title: '',
  description: '',
  coverImg: '',
  authorName: '',
  pageCount: 0,
  wordCount: 0,
  viewCount: 0,
  createdAt: '',
  updatedAt: ''
});

// 获取绘本详情
const fetchStorybook = async () => {
  const id = route.params.id;
  if (!id) {
    error.value = true;
    errorMessage.value = '绘本ID不存在';
    loading.value = false;
    return;
  }
  
  loading.value = true;
  error.value = false;
  
  try {
    // 这里应该调用实际的API获取绘本详情
    // const result = await api.getSharedStorybook(id);
    
    // 模拟数据
    setTimeout(() => {
      storybook.value = {
        id: id.toString(),
        title: '分享的示例绘本',
        description: '这是一个通过链接分享的示例绘本',
        coverImg: '',
        authorName: '示例作者',
        pageCount: 10,
        wordCount: 1000,
        viewCount: 25,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      loading.value = false;
    }, 1000);
  } catch (err) {
    error.value = true;
    errorMessage.value = '加载绘本失败，请稍后重试';
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit' 
  });
};

// 阅读绘本
const readStorybook = () => {
  router.push(`/storybook/reader/${storybook.value.id}`);
};

// 下载绘本
const downloadStorybook = () => {
  // 实现下载功能
  window.$message?.info('绘本下载功能开发中...');
};

// 复制分享链接
const copyShareLink = () => {
  const shareUrl = window.location.href;
  navigator.clipboard.writeText(shareUrl)
    .then(() => {
      window.$message?.success('链接已复制到剪贴板');
    })
    .catch(() => {
      window.$message?.error('复制链接失败');
    });
};

// 生成二维码
const generateQRCode = () => {
  if (qrcodeRef.value) {
    const shareUrl = window.location.href;
    QRCode.toCanvas(qrcodeRef.value, shareUrl, {
      width: 200,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    }).catch(err => {
      console.error('生成二维码失败:', err);
    });
  }
};

// 监听二维码弹窗显示状态
watch(showQRCode, (visible) => {
  if (visible) {
    setTimeout(generateQRCode, 100);
  }
});

// 组件挂载时获取绘本详情
onMounted(() => {
  fetchStorybook();
});
</script>

<style scoped>
.share-storybook {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
}

.dark .share-storybook {
  background-color: #1a1a1a;
}

.share-container {
  max-width: 800px;
  width: 100%;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.dark .share-container {
  background-color: #242424;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

.share-header {
  text-align: center;
  margin-bottom: 40px;
}

.share-header h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  margin: 12px 0 0 0;
  color: #666;
  font-size: 16px;
}

.dark .subtitle {
  color: #aaa;
}

.author-info {
  margin-top: 16px;
  font-size: 14px;
  color: #888;
}

.dot-divider {
  margin: 0 8px;
}

.share-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.cover-container {
  width: 240px;
  height: 320px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  margin-bottom: 24px;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #aaa;
}

.dark .cover-placeholder {
  background-color: #333;
  color: #666;
}

.preview-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.dark .info-item {
  color: #aaa;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.share-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #eee;
  padding-top: 24px;
  color: #888;
  font-size: 14px;
}

.dark .share-footer {
  border-top-color: #333;
}

.share-links {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.qrcode {
  padding: 16px;
  background-color: white;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .share-container {
    padding: 20px;
  }
  
  .share-header h1 {
    font-size: 24px;
  }
  
  .cover-container {
    width: 200px;
    height: 280px;
  }
  
  .preview-info {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
}
</style>
