<template>
  <div>
    <page-header title="绘本详情" :back="true" />
    <page-main v-loading="loading">
    <template v-if="storybook">
      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="font-bold">基本信息</span>
            <div>
              <el-button
                v-if="storybook.status === 2"
                type="success"
                @click="handleAudit(1)"
              >通过审核</el-button>
              <el-button
                v-if="storybook.status === 2"
                type="danger"
                @click="handleAudit(3)"
              >拒绝审核</el-button>
              <el-button
                v-if="storybook.isRecommended === 0 && storybook.status === 1"
                type="warning"
                @click="handleRecommend(1)"
              >设为推荐</el-button>
              <el-button
                v-if="storybook.isRecommended === 1"
                @click="handleRecommend(0)"
              >取消推荐</el-button>
              <el-button
                type="danger"
                @click="handleDelete"
              >删除绘本</el-button>
            </div>
          </div>
        </template>
        <div class="flex">
          <div class="mr-6">
            <el-image
              v-if="storybook.coverImg"
              :src="storybook.coverImg"
              fit="cover"
              style="width: 200px; height: 200px; border-radius: 8px"
            />
            <el-empty v-else description="无封面" :image-size="100" style="width: 200px; height: 200px" />
          </div>
          <div class="flex-1">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="ID">{{ storybook.id }}</el-descriptions-item>
              <el-descriptions-item label="标题">{{ storybook.title }}</el-descriptions-item>
              <el-descriptions-item label="创建用户">{{ storybook.userId }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(storybook.status)">
                  {{ getStatusText(storybook.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="公开状态">
                <el-tag :type="storybook.isPublic === 1 ? 'success' : 'info'">
                  {{ storybook.isPublic === 1 ? '公开' : '私有' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="推荐状态">
                <el-tag :type="storybook.isRecommended === 1 ? 'warning' : 'info'">
                  {{ storybook.isRecommended === 1 ? '推荐' : '普通' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="浏览量">{{ storybook.viewCount }}</el-descriptions-item>
              <el-descriptions-item label="点赞量">{{ storybook.likeCount }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ storybook.createdAt }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ storybook.updatedAt }}</el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ storybook.description || '无描述' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>

      <!-- 角色信息 -->
      <el-card class="mb-4" v-if="storybook.characters && storybook.characters.length > 0">
        <template #header>
          <div class="font-bold">角色信息</div>
        </template>
        <el-table :data="storybook.characters" border style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column label="角色图片" width="120">
            <template #default="{ row }">
              <el-image
                v-if="row.imageUrl"
                :src="row.imageUrl"
                fit="cover"
                style="width: 80px; height: 80px; border-radius: 4px"
              />
              <el-empty v-else description="无图片" :image-size="40" />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="角色名称" width="120" />
          <el-table-column prop="characterType" label="角色类型" width="120" />
          <el-table-column prop="appearance" label="外观描述" show-overflow-tooltip />
          <el-table-column label="性格特点" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.personalityTraits">{{ formatPersonality(row.personalityTraits) }}</span>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180" />
        </el-table>
      </el-card>
      <el-empty v-else description="暂无角色信息" />

      <!-- 页面内容 -->
      <el-card v-if="storybook.pages && storybook.pages.length > 0">
        <template #header>
          <div class="font-bold">页面内容</div>
        </template>
        <el-collapse accordion>
          <el-collapse-item v-for="page in sortedPages" :key="page.id" :title="`第${page.pageNumber}页`">
            <div class="flex">
              <div class="mr-4">
                <el-image
                  v-if="page.imageUrl"
                  :src="page.imageUrl"
                  fit="cover"
                  style="width: 200px; height: 200px; border-radius: 8px"
                />
                <el-empty v-else description="无图片" :image-size="100" style="width: 200px; height: 200px" />
              </div>
              <div class="flex-1">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="页码">{{ page.pageNumber }}</el-descriptions-item>
                  <el-descriptions-item label="布局">{{ page.layout || '默认' }}</el-descriptions-item>
                  <el-descriptions-item label="文本内容">
                    <div style="white-space: pre-wrap;">{{ page.text || '无文本' }}</div>
                  </el-descriptions-item>
                  <el-descriptions-item label="画面描述">
                    <div style="white-space: pre-wrap;">{{ page.imageDescription || '无描述' }}</div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-empty v-else description="暂无页面内容" />
    </template>
    <el-empty v-else description="未找到绘本信息" />

    <!-- 拒绝对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="拒绝审核" width="500px">
      <el-form :model="rejectForm">
        <el-form-item label="拒绝原因" :label-width="'80px'">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReject">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </page-main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import ApiStorybook from '@/api/modules/storybook';

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const storybook = ref(null);
const storybookId = computed(() => Number(route.params.id));

// 拒绝对话框
const rejectDialogVisible = ref(false);
const rejectForm = reactive({
  reason: '',
});

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '草稿',
    1: '已发布',
    2: '审核中',
    3: '已拒绝',
  };
  return statusMap[status] || '未知';
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
  };
  return typeMap[status] || 'info';
};

// 格式化性格特点
const formatPersonality = (personality) => {
  if (typeof personality === 'string') {
    try {
      personality = JSON.parse(personality);
    } catch (e) {
      return personality;
    }
  }

  if (typeof personality === 'object') {
    return Object.entries(personality)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
  }

  return String(personality);
};

// 排序后的页面
const sortedPages = computed(() => {
  if (!storybook.value || !storybook.value.pages) return [];
  return [...storybook.value.pages].sort((a, b) => a.pageNumber - b.pageNumber);
});

// 加载绘本详情
const loadStorybookDetail = async () => {
  loading.value = true;
  try {
    const res = await ApiStorybook.getStorybookDetail(storybookId.value);
    storybook.value = res.data;
  } catch (error) {
    console.error('加载绘本详情失败', error);
    ElMessage.error('加载绘本详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理审核
const handleAudit = async (status) => {
  if (status === 3) {
    // 拒绝需要填写原因
    rejectForm.reason = '';
    rejectDialogVisible.value = true;
    return;
  }

  try {
    await ApiStorybook.updateStorybookStatus(storybookId.value, { status });
    ElMessage.success('审核操作成功');
    loadStorybookDetail();
  } catch (error) {
    console.error('审核操作失败', error);
    ElMessage.error('审核操作失败');
  }
};

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.reason) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }

  try {
    await ApiStorybook.updateStorybookStatus(storybookId.value, {
      status: 3,
      rejectReason: rejectForm.reason
    });
    ElMessage.success('拒绝操作成功');
    rejectDialogVisible.value = false;
    loadStorybookDetail();
  } catch (error) {
    console.error('拒绝操作失败', error);
    ElMessage.error('拒绝操作失败');
  }
};

// 处理推荐
const handleRecommend = async (isRecommended) => {
  try {
    await ApiStorybook.setStorybookRecommend(storybookId.value, { isRecommended });
    ElMessage.success(isRecommended === 1 ? '设置推荐成功' : '取消推荐成功');
    loadStorybookDetail();
  } catch (error) {
    console.error('推荐操作失败', error);
    ElMessage.error('推荐操作失败');
  }
};

// 处理删除
const handleDelete = () => {
  ElMessageBox.confirm(
    `确定要删除绘本"${storybook.value.title}"吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await ApiStorybook.deleteStorybook(storybookId.value);
        ElMessage.success('删除成功');
        router.push('/storybook/list');
      } catch (error) {
        console.error('删除失败', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

onMounted(() => {
  loadStorybookDetail();
});
</script>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}
</style>
