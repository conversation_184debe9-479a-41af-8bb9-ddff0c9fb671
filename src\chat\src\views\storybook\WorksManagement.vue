<template>
  <StoryCreationLayout v-model:active-tab="activeTab" :current-step="0">
    <div class="works-management">
      <div class="works-container">
        <!-- 顶部标题栏 - 儿童友好设计 -->
        <div class="works-header">
          <div class="header-title">
            <div class="title-icon-wrapper">
              <span class="title-icon">📚</span>
            </div>
            <div class="title-text">
              <h1>我的绘本作品</h1>
              <p class="subtitle">这里收藏了你创作的所有精彩故事！</p>
            </div>
          </div>

          <div class="header-actions">
            <NButton type="primary" class="create-button" @click="createNewWork">
              <template #icon>
                <SvgIcon name="ri:add-line" size="20" />
              </template>
              创作新绘本
            </NButton>
          </div>
        </div>

        <!-- 主内容区 - 儿童友好设计（无侧边栏） -->
        <div class="works-main">
          <div class="works-content">
            <!-- 文件夹选择器 -->
            <div class="folder-selector">
              <div class="folder-tabs">
                <div
                  v-for="option in folderOptions.filter(opt => opt.type !== 'divider')"
                  :key="option.key"
                  class="folder-tab"
                  :class="{ 'active': activeFolder === option.key }"
                  @click="activeFolder = option.key; handleFolderChange(option.key)"
                >
                  <span class="folder-emoji-icon">{{ option.icon ? renderFolderIcon(option) : '📁' }}</span>
                  <span class="folder-name">{{ option.label }}</span>
                </div>

                <NButton class="new-folder-button" @click="showFolderModal = true">
                  <template #icon>
                    <span class="folder-emoji-icon">✨</span>
                  </template>
                  新收藏夹
                </NButton>
              </div>
            </div>

            <NCard class="works-card">
              <div class="card-decoration top-left"></div>
              <div class="card-decoration top-right"></div>
              <div class="card-decoration bottom-left"></div>
              <div class="card-decoration bottom-right"></div>

              <WorksList
                :folder-id="getFolderIdFromActive"
                :is-trash="activeFolder === 'trash'"
                @create="createNewWork"
              />
            </NCard>
          </div>
        </div>
      </div>

      <!-- 新建文件夹对话框 - 儿童友好设计 -->
      <NModal
        v-model:show="showFolderModal"
        preset="dialog"
        title="创建新收藏夹"
        positive-text="创建"
        negative-text="取消"
        class="folder-modal"
        @positive-click="createFolder"
      >
        <div class="modal-header">
          <div class="modal-icon">📁✨</div>
          <h3 class="modal-title">给你的收藏夹起个好听的名字吧！</h3>
        </div>

        <NForm ref="folderFormRef" :model="folderForm" :rules="folderRules" class="folder-form">
          <NFormItem label="收藏夹名称" path="name">
            <NInput v-model:value="folderForm.name" placeholder="例如：我喜欢的动物故事" class="folder-input" />
          </NFormItem>
          <NFormItem label="简短介绍" path="description">
            <NInput
              v-model:value="folderForm.description"
              type="textarea"
              placeholder="告诉我这个收藏夹里会放什么样的故事呢？（可以不填）"
              class="folder-textarea"
            />
          </NFormItem>
        </NForm>
      </NModal>
    </div>
  </StoryCreationLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, h } from 'vue';
import { useRouter } from 'vue-router';
import {
  NButton, NCard, NMenu, NModal, NForm, NFormItem,
  NInput, NRadioGroup, NRadio, NInputGroup, NInputNumber,
  NInputGroupLabel, NCheckbox
} from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import WorksList from './components/works/WorksList.vue';
import StoryCreationLayout from './components/creation/StoryCreationLayout.vue';
import { useAuthStore } from '@/store';
import { useWorksStore } from '@/store/modules/works';

// 布局响应式
const { isMobile } = useBasicLayout();
const router = useRouter();
const authStore = useAuthStore();
const worksStore = useWorksStore();

// 定义emit
const emit = defineEmits(['update:active-tab']);

// 活动标签页 - 设置为'my-works'以匹配侧边栏选项
const activeTab = ref('my-works');

// 活动文件夹
const activeFolder = ref('all');

// 模态框状态
const showFolderModal = ref(false);

// 表单引用
const folderFormRef = ref(null);

// 表单数据
const folderForm = ref({
  name: '',
  description: ''
});

// 文件夹验证规则
const folderRules = {
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { max: 20, message: '文件夹名称不能超过20个字符', trigger: 'blur' }
  ]
};

// 文件夹选项 - 儿童友好设计
const folderOptions = computed(() => {
  const options = [
    {
      label: '全部故事',
      key: 'all',
      icon: () => h('span', { class: 'folder-emoji-icon' }, '📚')
    },
    {
      label: '最近编辑',
      key: 'recent',
      icon: () => h('span', { class: 'folder-emoji-icon' }, '⏰')
    },
    {
      label: '未分类',
      key: 'uncategorized',
      icon: () => h('span', { class: 'folder-emoji-icon' }, '📋')
    },
    {
      label: '回收站',
      key: 'trash',
      icon: () => h('span', { class: 'folder-emoji-icon' }, '🗑️')
    },
    {
      type: 'divider',
      key: 'd1'
    }
  ];

  // 添加用户创建的文件夹，使用随机可爱的emoji
  const folderEmojis = ['🦊', '🐼', '🐯', '🦁', '🐶', '🐱', '🐰', '🐻', '🐨', '🐮', '🐷', '🦄', '🐢', '🐬', '🐙'];
  const userFolders = worksStore.folders.map((folder, index) => ({
    label: folder.name,
    key: folder.id.toString(),
    icon: () => h('span', { class: 'folder-emoji-icon' }, folderEmojis[index % folderEmojis.length])
  }));

  return [...options, ...userFolders];
});

// 渲染文件夹图标
const renderFolderIcon = (option) => {
  if (typeof option.icon === 'function') {
    // 对于emoji图标，我们需要提取文本内容
    const vnode = option.icon();
    if (vnode && vnode.children) {
      return vnode.children;
    }
  }
  return '📁'; // 默认图标
};

// 处理文件夹切换
const handleFolderChange = (key) => {
  activeFolder.value = key;
};

// 创建新绘本
const createNewWork = () => {
  // 切换到绘本创作模式，使用内部标签页切换而不是路由跳转
  activeTab.value = 'outline';
  // 通知父组件更新标签页
  emit('update:active-tab', 'outline');

  // 设置一个标记，表示正在创建新故事
  localStorage.setItem('storybook-creating-new', 'true');
  console.log('设置创建新故事标记');
};

// 从激活的文件夹选项获取文件夹ID
const getFolderIdFromActive = computed(() => {
  if (activeFolder.value === 'all' || activeFolder.value === 'trash') {
    return null;
  } else if (activeFolder.value === 'recent') {
    return null; // 最近编辑的特殊处理在WorksList组件中
  } else if (activeFolder.value === 'uncategorized') {
    return 0; // 0表示未分类（folderId为null的作品）
  } else {
    return parseInt(activeFolder.value);
  }
});

// 创建文件夹
const createFolder = () => {
  folderFormRef.value?.validate(async (errors) => {
    if (errors) return;

    try {
      await worksStore.createFolder({
        name: folderForm.value.name,
        description: folderForm.value.description
      });

      window.$message?.success('文件夹创建成功');
      folderForm.value = { name: '', description: '' };
      showFolderModal.value = false;
    } catch (error) {
      console.error('创建文件夹失败:', error);
      window.$message?.error('创建文件夹失败');
    }
  });
};

// 监听移动设备状态变化
watch(isMobile, (newValue) => {
  // 移动设备相关逻辑
});

// 组件挂载时获取文件夹列表
onMounted(async () => {
  if (authStore.isLogin) {
    try {
      await worksStore.fetchFolders();
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    }
  }
});
</script>

<style scoped>
/* 儿童友好的设计样式 */
.works-management {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Comic Sans MS', 'Marker Felt', 'Arial Rounded MT Bold', sans-serif;
}

.works-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0.5rem;
}

/* 顶部标题栏样式 */
.works-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(240, 249, 255, 0.8));
  border-radius: 1.5rem;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.1),
              0 2px 4px rgba(59, 130, 246, 0.05);
  border: 2px solid rgba(186, 230, 253, 0.8);
  transition: all 0.3s ease;
  animation: float 6s ease-in-out infinite;
}

.dark .works-header {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.8));
  border: 2px solid rgba(51, 65, 85, 0.8);
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon-wrapper {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 1.2rem;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.2);
  transform: rotate(-5deg);
  transition: all 0.3s ease;
  animation: wiggle 3s ease-in-out infinite;
}

@keyframes wiggle {
  0% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
  100% { transform: rotate(-5deg); }
}

.title-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.title-text {
  display: flex;
  flex-direction: column;
}

.header-title h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 800;
  color: #1e293b;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .header-title h1 {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  margin: 0.25rem 0 0 0;
  color: #64748b;
  font-size: 1rem;
  font-weight: 500;
}

.dark .subtitle {
  color: #94a3b8;
}

.create-button {
  font-size: 1.1rem !important;
  padding: 0.6rem 1.2rem !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.2) !important;
  transition: all 0.3s ease !important;
  transform-origin: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.create-button:hover {
  transform: translateY(-3px) scale(1.05) !important;
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3) !important;
}

.create-button:active {
  transform: translateY(0) scale(0.98) !important;
}

/* 主内容区样式 */
.works-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 1rem;
}

/* 文件夹选择器样式 */
.folder-selector {
  margin-bottom: 1rem;
}

.folder-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7), rgba(240, 249, 255, 0.7));
  border-radius: 1rem;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08);
  border: 2px solid rgba(186, 230, 253, 0.6);
  backdrop-filter: blur(8px);
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
}

.folder-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.dark .folder-tabs {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.7), rgba(30, 41, 59, 0.7));
  border: 2px solid rgba(51, 65, 85, 0.6);
}

.folder-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border-radius: 0.8rem;
  background: rgba(255, 255, 255, 0.5);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.folder-tab:hover {
  background: rgba(224, 242, 254, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
}

.folder-tab.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.2));
  border: 2px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

.dark .folder-tab {
  background: rgba(30, 41, 59, 0.5);
}

.dark .folder-tab:hover {
  background: rgba(51, 65, 85, 0.8);
}

.dark .folder-tab.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.3));
  border: 2px solid rgba(96, 165, 250, 0.4);
}

.folder-emoji-icon {
  font-size: 1.3rem;
  display: inline-block;
  transition: all 0.3s ease;
}

.folder-tab:hover .folder-emoji-icon {
  transform: scale(1.2) rotate(10deg);
}

.folder-name {
  font-weight: 600;
  font-size: 0.95rem;
  color: #1e293b;
}

.dark .folder-name {
  color: #e2e8f0;
}

.new-folder-button {
  padding: 0.6rem 1rem !important;
  border-radius: 0.8rem !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border: 1px dashed rgba(59, 130, 246, 0.5) !important;
  transition: all 0.3s ease !important;
  white-space: nowrap;
}

.new-folder-button:hover {
  background: rgba(224, 242, 254, 0.8) !important;
  border: 1px dashed rgba(59, 130, 246, 0.8) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1) !important;
}

.dark .new-folder-button {
  background: rgba(30, 41, 59, 0.5) !important;
  border: 1px dashed rgba(96, 165, 250, 0.5) !important;
}

.dark .new-folder-button:hover {
  background: rgba(51, 65, 85, 0.8) !important;
  border: 1px dashed rgba(96, 165, 250, 0.8) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 主内容卡片样式 */
.works-content {
  flex: 1;
  position: relative;
}

.works-card {
  height: 100%;
  background: linear-gradient(135deg, #ffffff, #f8fafc) !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.1),
              0 3px 6px rgba(59, 130, 246, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  border-radius: 1.5rem !important;
  padding: 1.5rem !important;
  position: relative;
  overflow: hidden;
}

.dark .works-card {
  background: linear-gradient(135deg, #1e293b, #0f172a) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
              0 3px 6px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
}

/* 卡片装饰元素 */
.card-decoration {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  opacity: 0.1;
  z-index: 0;
}

.top-left {
  top: -30px;
  left: -30px;
  background: radial-gradient(circle, #3b82f6, transparent 70%);
  animation: pulse-decoration 8s infinite alternate;
}

.top-right {
  top: -30px;
  right: -30px;
  background: radial-gradient(circle, #f97316, transparent 70%);
  animation: pulse-decoration 7s infinite alternate-reverse;
}

.bottom-left {
  bottom: -30px;
  left: -30px;
  background: radial-gradient(circle, #10b981, transparent 70%);
  animation: pulse-decoration 9s infinite alternate;
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  background: radial-gradient(circle, #8b5cf6, transparent 70%);
  animation: pulse-decoration 6s infinite alternate-reverse;
}

@keyframes pulse-decoration {
  0% { transform: scale(1); opacity: 0.1; }
  100% { transform: scale(1.5); opacity: 0.15; }
}

/* 文件夹模态框样式 */
.folder-modal {
  border-radius: 1.5rem !important;
  overflow: hidden !important;
}

.folder-modal :deep(.n-card) {
  background: linear-gradient(135deg, #ffffff, #f8fafc) !important;
  border: none !important;
}

.dark .folder-modal :deep(.n-card) {
  background: linear-gradient(135deg, #1e293b, #0f172a) !important;
}

.modal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: tada 2s infinite;
}

@keyframes tada {
  0% { transform: scale(1) rotate(0deg); }
  10%, 20% { transform: scale(0.9) rotate(-3deg); }
  30%, 50%, 70%, 90% { transform: scale(1.1) rotate(3deg); }
  40%, 60%, 80% { transform: scale(1.1) rotate(-3deg); }
  100% { transform: scale(1) rotate(0deg); }
}

.modal-title {
  font-size: 1.2rem;
  color: #3b82f6;
  text-align: center;
  margin: 0;
}

.dark .modal-title {
  color: #60a5fa;
}

.folder-form {
  margin-top: 1rem;
}

.folder-input, .folder-textarea {
  border-radius: 0.8rem !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  background: rgba(255, 255, 255, 0.8) !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
}

.folder-input:focus, .folder-textarea:focus {
  border: 2px solid rgba(59, 130, 246, 0.8) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  transform: translateY(-2px);
}

.dark .folder-input, .dark .folder-textarea {
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
  background: rgba(30, 41, 59, 0.8) !important;
}

.dark .folder-input:focus, .dark .folder-textarea:focus {
  border: 2px solid rgba(96, 165, 250, 0.8) !important;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2) !important;
}

.folder-modal :deep(.n-button) {
  border-radius: 0.8rem !important;
  font-size: 1rem !important;
  padding: 0.5rem 1.2rem !important;
  transition: all 0.3s ease !important;
}

.folder-modal :deep(.n-button--primary-type) {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
}

.folder-modal :deep(.n-button--primary-type:hover) {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3) !important;
}

.folder-modal :deep(.n-button--default-type) {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
}

.dark .folder-modal :deep(.n-button--default-type) {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(51, 65, 85, 0.8) !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .works-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .header-title h1 {
    font-size: 1.5rem;
  }

  .title-icon-wrapper {
    width: 3rem;
    height: 3rem;
  }

  .title-icon {
    font-size: 1.5rem;
  }

  .create-button {
    width: 100%;
    font-size: 0.9rem !important;
    padding: 0.5rem 1rem !important;
  }

  .folder-tabs {
    padding: 0.5rem;
    overflow-x: auto;
  }

  .folder-tab {
    padding: 0.5rem 0.8rem;
  }

  .folder-name {
    font-size: 0.85rem;
  }
}
</style>
