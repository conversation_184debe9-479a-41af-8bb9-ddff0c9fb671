import type { AxiosProgressEvent, AxiosResponse, GenericAbortSignal } from 'axios'
import axios from 'axios'
import request from './axios'
import { useAuthStore } from '@/store'

export interface HttpOption {
  url: string
  data?: any
  method?: string
  headers?: any
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  signal?: GenericAbortSignal
  beforeRequest?: () => void
  afterRequest?: () => void
}

export interface Response<T = any> {
  data: T
  message: string | null
  status: string
}

let last401ErrorTimestamp = 0
const homePagePath = ['/chatlog/chatList', '/group/query']

function hasWhitePath(path: string) {
  if (!path)
    return false
  return homePagePath.some(item => path.includes(item))
}

function http<T = any>(
  { url, data, method, headers, onDownloadProgress, signal, beforeRequest, afterRequest }: HttpOption,
) {
  console.log('[HTTP REQUEST] 开始处理请求');
  console.log('[HTTP REQUEST] 原始URL:', url);
  console.log('[HTTP REQUEST] 请求方法:', method);
  console.log('[HTTP REQUEST] 请求头:', headers);
  console.log('[HTTP REQUEST] baseURL:', import.meta.env.VITE_GLOB_API_URL);

  const successHandler = (res: AxiosResponse<Response<T>>) => {
    const authStore = useAuthStore()

    const { code } = res.data

    if ((code >= 200 && code < 300) || !code)
      return res.data

    if (code === 401) {
      authStore.removeToken()
      window.location.reload()
    }

    return Promise.reject(res.data)
  }

  const failHandler = (error: Response<Error>) => {
    const authStore = useAuthStore()
    let data = ''
    error.response?.data && (data = error.response.data)
    afterRequest?.()
    const status = error?.response?.status

    if (status === 401) {
      authStore.removeToken()
      if (!hasWhitePath(error?.request?.responseURL)) {
        authStore.loadInit && authStore.setLoginDialog(true)
        const message = error.response.data?.message || '请先登录后再进行使用！'
        Date.now() - last401ErrorTimestamp > 3000 && window.$message.error(message)
      }
      last401ErrorTimestamp = Date.now()
    }
    else {
      if (data && !data?.success)
        window.$message.error(data?.message || '请求接口错误！')
    }
    throw new Error(error.response?.data?.message || error || 'Error')
  }

  beforeRequest?.()

  method = method || 'GET'

  // 处理参数，确保数字类型的参数被正确传递
  let params = {};

  // 如果URL中已经包含了查询参数，则不处理data
  if (url.includes('?')) {
    console.log('在http函数中检测到URL已包含查询参数:', url);
  } else if (data) {
    const rawParams = typeof data === 'function' ? data() : data;

    // 安全地复制参数，避免原型链污染
    try {
      // 对于包含htmlContent的请求，特殊处理
      if (rawParams.htmlContent) {
        console.log('[REQUEST] 检测到 htmlContent 参数');
        console.log('[REQUEST] htmlContent 类型:', typeof rawParams.htmlContent);
        console.log('[REQUEST] htmlContent 长度:', rawParams.htmlContent?.length || 0);

        // 安全地复制对象，确保 htmlContent 是字符串
        params = {};
        Object.keys(rawParams).forEach(key => {
          if (key === 'htmlContent') {
            const content = rawParams[key];
            params[key] = typeof content === 'string' ? content : String(content);
            console.log('[REQUEST] 处理后 htmlContent 类型:', typeof params[key]);
            console.log('[REQUEST] 处理后 htmlContent 长度:', params[key].length);
          } else {
            params[key] = rawParams[key];
          }
        });
      } else {
        console.log('原始参数:', rawParams);

        // 遍历参数，将字符串数字转换为数字类型
        Object.keys(rawParams).forEach(key => {
          const value = rawParams[key];
          if (key === 'page' || key === 'size') {
            // 将page和size参数转换为数字，并确保是整数
            const numValue = typeof value === 'number' ? value : Number(value);
            params[key] = isNaN(numValue) ? (key === 'page' ? 1 : 10) : Math.floor(numValue);
            console.log(`转换参数 ${key}:`, value, '->', params[key], '类型:', typeof params[key]);
          } else {
            params[key] = value;
          }
        });

        console.log('处理后的参数:', params);
      }
    } catch (error) {
      console.error('[REQUEST] 处理参数时出错:', error);
      // 出错时回退到简单的浅复制
      params = {};
      try {
        // 尝试安全地复制每个属性
        Object.keys(rawParams).forEach(key => {
          try {
            const value = rawParams[key];
            if (key === 'htmlContent' && typeof value !== 'string') {
              params[key] = String(value);
              console.log('[REQUEST] 强制转换 htmlContent 为字符串');
            } else {
              params[key] = value;
            }
          } catch (err) {
            console.error(`[REQUEST] 复制属性 ${key} 失败:`, err);
            // 如果单个属性复制失败，跳过该属性
          }
        });
      } catch (e) {
        console.error('[REQUEST] 安全复制失败，使用空对象:', e);
        // 如果还是失败，使用空对象
        params = {};
      }
    }
  }

  console.log('[HTTP REQUEST] 最终请求方法:', method);
  console.log('[HTTP REQUEST] 最终请求URL:', url);
  console.log('[HTTP REQUEST] 最终请求参数:', params);

  // 确保URL路径正确
  // 记录完整的请求URL（包括baseURL）
  const baseURL = import.meta.env.VITE_GLOB_API_URL || '/api';

  // 处理URL格式，确保不会有重复的/api前缀
  // 如果URL以/api开头，则移除这个前缀，因为baseURL已经包含了/api
  if (url && url.startsWith('/api/')) {
    url = url.substring(5); // 移除'/api/'
    console.log('[HTTP REQUEST] 移除重复的/api前缀:', url);
  } else if (url && url.startsWith('api/')) {
    url = url.substring(4); // 移除'api/'
    console.log('[HTTP REQUEST] 移除重复的api/前缀:', url);
  }

  // 确保URL有正确的斜杠
  if (url && !url.startsWith('/') && baseURL && !baseURL.endsWith('/')) {
    url = '/' + url;
    console.log('[HTTP REQUEST] URL路径已添加前导斜杠:', url);
  }

  const fullUrl = baseURL + url;
  console.log('[HTTP REQUEST] 完整请求URL:', fullUrl);

  return method === 'GET'
    ? request.get(url, { params, signal, onDownloadProgress })
        .then(res => {
          console.log('[HTTP REQUEST] GET请求成功:', url);
          return successHandler(res);
        }, err => {
          console.error('[HTTP REQUEST] GET请求失败:', url, err);
          return failHandler(err);
        })
    : request.post(url, params, { headers, signal, onDownloadProgress })
        .then(res => {
          console.log('[HTTP REQUEST] POST请求成功:', url);
          return successHandler(res);
        }, err => {
          console.error('[HTTP REQUEST] POST请求失败:', url, err);
          return failHandler(err);
        })
}

export function get<T = any>(
  { url, data, method = 'GET', onDownloadProgress, signal, beforeRequest, afterRequest }: HttpOption,
): Promise<Response<T>> {

  // 如果URL中已经包含了查询参数，则不传递data
  // 添加空值检查，避免调用undefined的includes方法
  if (url && url.includes('?')) {
    return http<T>({
      url,
      method,
      // 不传递data
      onDownloadProgress,
      signal,
      beforeRequest,
      afterRequest,
    });
  }

  // 否则，正常传递data
  return http<T>({
    url,
    method,
    data,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
  });
}

export function post<T = any>(
  { url, data, method = 'POST', headers, onDownloadProgress, signal, beforeRequest, afterRequest }: HttpOption,
): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    headers,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
  })
}

// 创建一个不添加token的请求函数，用于公开API
export function publicGet<T = any>(
  { url, data, method = 'GET', onDownloadProgress, signal, beforeRequest, afterRequest }: HttpOption,
): Promise<Response<T>> {
  console.log('[PUBLIC_REQUEST] 使用公开请求函数，不添加token');
  console.log('[PUBLIC_REQUEST] 原始URL:', url);
  console.log('[PUBLIC_REQUEST] 原始数据:', data);

  // 确保基础URL正确
  const baseURL = import.meta.env.VITE_GLOB_API_URL || '';
  console.log('[PUBLIC_REQUEST] 基础URL:', baseURL);

  // 创建一个新的axios实例，不添加token
  const publicRequest = axios.create({
    baseURL,
    timeout: 60000,
  });

  // 添加请求拦截器
  publicRequest.interceptors.request.use(
    (config) => {
      console.log('[PUBLIC_REQUEST] 请求配置:', config);
      // 添加域名头，但不添加token
      const currentDomain = window.location.origin;
      config.headers['X-Website-Domain'] = currentDomain;
      console.log('[PUBLIC_REQUEST] 请求头:', config.headers);
      return config;
    },
    (error) => {
      console.error('[PUBLIC_REQUEST] 请求拦截器错误:', error);
      return Promise.reject(error);
    }
  );

  // 添加响应拦截器
  publicRequest.interceptors.response.use(
    (response) => {
      console.log('[PUBLIC_REQUEST] 响应成功:', response.status);
      console.log('[PUBLIC_REQUEST] 响应头:', response.headers);
      console.log('[PUBLIC_REQUEST] 响应数据:', response.data);
      return response;
    },
    (error) => {
      console.error('[PUBLIC_REQUEST] 响应错误:', error);
      if (error.response) {
        console.error('[PUBLIC_REQUEST] 错误状态:', error.response.status);
        console.error('[PUBLIC_REQUEST] 错误数据:', error.response.data);
      }
      return Promise.reject(error);
    }
  );

  // 处理参数
  let params = {};
  if (data) {
    params = typeof data === 'function' ? data() : data;
  }

  console.log('[PUBLIC_REQUEST] 处理后请求URL:', url);
  console.log('[PUBLIC_REQUEST] 处理后请求参数:', params);

  // 构建完整URL，确保不会有双斜杠
  let fullUrl = url;
  if (baseURL && !url.startsWith('http')) {
    fullUrl = baseURL.endsWith('/') && url.startsWith('/')
      ? baseURL + url.substring(1)
      : (!baseURL.endsWith('/') && !url.startsWith('/') ? baseURL + '/' + url : baseURL + url);
  }
  console.log('[PUBLIC_REQUEST] 完整URL:', fullUrl);

  return publicRequest.get(url, { params })
    .then(res => {
      console.log('[PUBLIC_REQUEST] 原始响应:', res);
      return res.data;
    })
    .catch(error => {
      console.error('[PUBLIC_REQUEST] 请求失败:', error);
      throw error;
    });
}

export default post
