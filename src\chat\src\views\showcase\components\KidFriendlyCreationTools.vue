<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { NGrid, NGridItem, NButton } from 'naive-ui';
import { useAuthStore } from '@/store';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const router = useRouter();
const authStore = useAuthStore();
const { isMobile } = useBasicLayout();

// 创作工具列表
const creationTools = [
  {
    id: 'storybook',
    title: '绘本创作',
    description: '画出你想象中的故事',
    icon: '📚',
    path: '/storybook',
    color: '#FF9A9E',
    bgColor: '#FFF3E0'
  },
  {
    id: 'programming',
    title: '编程小游戏',
    description: '创建你自己的小游戏',
    icon: '🎮',
    path: '/aiProgramming',
    color: '#81C784',
    bgColor: '#E8F5E9'
  },
  {
    id: 'music',
    title: '音乐创作',
    description: '创作你的音乐',
    icon: '🎵',
    path: '/chat?preset=music',
    color: '#9575CD',
    bgColor: '#EDE7F6'
  }
];

// 处理工具点击
const handleToolClick = (path: string) => {
  // 检查用户是否登录
  if (!authStore.isLogin) {
    authStore.setLoginDialog(true);
    return;
  }
  router.push(path);
};

// 工具卡片悬停状态
const hoveredTool = ref('');

const setHoveredTool = (id: string) => {
  hoveredTool.value = id;
};

const clearHoveredTool = () => {
  hoveredTool.value = '';
};

// 滚动到作品展示区域
const scrollToWorks = () => {
  const worksSection = document.querySelector('.works-section');
  if (worksSection) {
    worksSection.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>

<template>
  <section class="kid-creation-tools-section">
    <div class="section-container">
      <div class="section-header">
        <div class="section-title-wrapper">
          <span class="section-emoji">🎨</span>
          <h2 class="section-title">创作工具箱</h2>
        </div>
        <p class="section-description">选择一个工具，开始你的创作冒险！</p>
      </div>

      <NGrid :cols="isMobile ? 1 : 3" :x-gap="24" :y-gap="24">
        <NGridItem v-for="tool in creationTools" :key="tool.id">
          <div
            class="tool-card"
            :style="{ backgroundColor: tool.bgColor }"
            @click="handleToolClick(tool.path)"
            @mouseenter="setHoveredTool(tool.id)"
            @mouseleave="clearHoveredTool()"
            :class="{ 'hovered': hoveredTool === tool.id }"
          >
            <div class="tool-content">
              <div class="tool-icon" :style="{ backgroundColor: tool.color }">
                <span class="tool-emoji">{{ tool.icon }}</span>
              </div>
              <h3 class="tool-title">{{ tool.title }}</h3>
              <p class="tool-description">{{ tool.description }}</p>
              <div class="tool-button">
                <span class="button-text">开始创作</span>
                <span class="button-arrow">→</span>
              </div>
            </div>
            <div class="tool-decoration" :class="{ 'active': hoveredTool === tool.id }">
              <div class="decoration-circle" :style="{ backgroundColor: tool.color }"></div>
              <div class="decoration-circle" :style="{ backgroundColor: tool.color }"></div>
              <div class="decoration-circle" :style="{ backgroundColor: tool.color }"></div>
            </div>
          </div>
        </NGridItem>
      </NGrid>

      <div class="navigation-buttons">
        <NButton size="large" class="nav-button" @click="scrollToWorks">
          <span class="btn-icon">🔍</span>
          看看大家的作品
        </NButton>
      </div>
    </div>
  </section>
</template>

<style scoped>
.kid-creation-tools-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #FAFAFA;
  position: relative;
  overflow: hidden;
}

.kid-creation-tools-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(90deg, #81C784, #64B5F6, #9575CD);
  z-index: 1;
}

.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  position: relative;
  z-index: 2;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.section-emoji {
  font-size: 2.5rem;
  margin-right: 1rem;
  animation: spin 10s linear infinite;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #5D4037;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.section-description {
  font-size: 1.25rem;
  color: #795548;
  max-width: 700px;
  margin: 0 auto;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.tool-card {
  border-radius: 24px;
  padding: 2rem;
  height: 100%;
  min-height: 300px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.tool-card.hovered {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.tool-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tool-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.tool-emoji {
  font-size: 3rem;
}

.tool-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #5D4037;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.tool-description {
  font-size: 1.1rem;
  color: #795548;
  margin-bottom: 2rem;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.tool-button {
  margin-top: auto;
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #5D4037;
  font-size: 1.1rem;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.button-arrow {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.tool-card:hover .button-arrow {
  transform: translateX(5px);
}

.tool-decoration {
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  z-index: 1;
  opacity: 0.1;
  transition: all 0.5s ease;
}

.tool-decoration.active {
  opacity: 0.2;
  transform: scale(1.2);
}

.navigation-buttons {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.nav-button {
  background: white;
  color: #81C784;
  border: 2px solid #81C784;
  font-size: 1.25rem;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.nav-button:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  background: #E8F5E9;
}

.btn-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
  vertical-align: middle;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
}

.decoration-circle:nth-child(1) {
  width: 100px;
  height: 100px;
  top: 0;
  left: 0;
}

.decoration-circle:nth-child(2) {
  width: 70px;
  height: 70px;
  top: 60px;
  left: 80px;
}

.decoration-circle:nth-child(3) {
  width: 50px;
  height: 50px;
  top: 20px;
  left: 120px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1.1rem;
  }

  .tool-card {
    padding: 1.5rem;
    min-height: 250px;
  }

  .tool-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
  }

  .tool-emoji {
    font-size: 2.5rem;
  }

  .tool-title {
    font-size: 1.5rem;
  }

  .tool-description {
    font-size: 1rem;
  }
}
</style>
