<template>
  <div class="chat-history">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">聊天历史</h1>
      <p class="page-description">管理您的对话记录和聊天设置</p>
    </div>

    <!-- 搜索和筛选 -->
    <NCard class="search-card">
      <div class="search-controls">
        <div class="search-input-wrapper">
          <NInput
            v-model:value="searchKeyword"
            placeholder="搜索对话标题..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <SvgIcon icon="ri:search-line" />
            </template>
          </NInput>
        </div>
        
        <div class="filter-controls">
          <NSelect
            v-model:value="filterType"
            :options="filterOptions"
            placeholder="筛选类型"
            style="width: 150px"
            @update:value="handleFilter"
          />
          
          <NButton
            @click="clearAllNonSticky"
            type="error"
            :loading="clearing"
          >
            <template #icon>
              <SvgIcon icon="ri:delete-bin-line" />
            </template>
            清空历史
          </NButton>
        </div>
      </div>
    </NCard>

    <!-- 对话列表 -->
    <NCard class="history-list-card">
      <template #header>
        <div class="card-header">
          <SvgIcon icon="ri:chat-history-line" class="card-icon" />
          <span>对话记录 ({{ filteredChatList.length }})</span>
        </div>
      </template>

      <div v-if="loading" class="loading-state">
        <NSpin size="large" />
        <div class="loading-text">加载中...</div>
      </div>

      <div v-else-if="filteredChatList.length === 0" class="empty-state">
        <SvgIcon icon="ri:chat-3-line" class="empty-icon" />
        <div class="empty-title">暂无对话记录</div>
        <div class="empty-desc">开始一个新的对话吧</div>
      </div>

      <div v-else class="chat-list">
        <div
          v-for="chat in paginatedChatList"
          :key="chat.uuid"
          class="chat-item"
          :class="{ 'sticky': chat.isSticky }"
        >
          <div class="chat-content">
            <div class="chat-header">
              <div class="chat-title">{{ chat.title || '未命名对话' }}</div>
              <div class="chat-badges">
                <NBadge
                  v-if="chat.isSticky"
                  type="warning"
                  :show-zero="false"
                  dot
                >
                  置顶
                </NBadge>
                <NBadge
                  v-if="chat.appId"
                  type="info"
                  :show-zero="false"
                  dot
                >
                  应用
                </NBadge>
              </div>
            </div>
            
            <div class="chat-meta">
              <span class="chat-time">{{ formatTime(chat.updatedAt) }}</span>
              <span class="chat-count">{{ getChatCount(chat.uuid) }} 条消息</span>
            </div>
          </div>

          <div class="chat-actions">
            <NButton
              size="small"
              quaternary
              @click="toggleSticky(chat)"
              :title="chat.isSticky ? '取消置顶' : '置顶对话'"
            >
              <template #icon>
                <SvgIcon :icon="chat.isSticky ? 'ri:pushpin-fill' : 'ri:pushpin-line'" />
              </template>
            </NButton>
            
            <NButton
              size="small"
              quaternary
              @click="openChat(chat.uuid)"
              title="打开对话"
            >
              <template #icon>
                <SvgIcon icon="ri:external-link-line" />
              </template>
            </NButton>
            
            <NButton
              size="small"
              quaternary
              type="error"
              @click="deleteChat(chat)"
              title="删除对话"
            >
              <template #icon>
                <SvgIcon icon="ri:delete-bin-line" />
              </template>
            </NButton>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredChatList.length > pageSize" class="pagination-wrapper">
        <NPagination
          v-model:page="currentPage"
          :page-count="Math.ceil(filteredChatList.length / pageSize)"
          :page-size="pageSize"
          show-size-picker
          :page-sizes="[10, 20, 50]"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  NCard,
  NInput,
  NSelect,
  NButton,
  NSpin,
  NBadge,
  NPagination,
  useMessage,
  useDialog
} from 'naive-ui';
import { useChatStore } from '@/store';
import { SvgIcon } from '@/components/common';

const router = useRouter();
const chatStore = useChatStore();
const message = useMessage();
const dialog = useDialog();

// 状态管理
const loading = ref(false);
const clearing = ref(false);
const searchKeyword = ref('');
const filterType = ref('all');
const currentPage = ref(1);
const pageSize = ref(20);

// 筛选选项
const filterOptions = [
  { label: '全部对话', value: 'all' },
  { label: '置顶对话', value: 'sticky' },
  { label: '普通对话', value: 'normal' },
  { label: '应用对话', value: 'app' }
];

// 计算属性
const chatList = computed(() => chatStore.groupList || []);

const filteredChatList = computed(() => {
  let list = chatList.value;

  // 搜索过滤
  if (searchKeyword.value) {
    list = list.filter(chat =>
      chat.title?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 类型过滤
  switch (filterType.value) {
    case 'sticky':
      list = list.filter(chat => chat.isSticky);
      break;
    case 'normal':
      list = list.filter(chat => !chat.isSticky && !chat.appId);
      break;
    case 'app':
      list = list.filter(chat => chat.appId);
      break;
  }

  // 按时间排序
  return list.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
});

const paginatedChatList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredChatList.value.slice(start, end);
});

// 方法
const handleSearch = () => {
  currentPage.value = 1;
};

const handleFilter = () => {
  currentPage.value = 1;
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN');
};

const getChatCount = (uuid: string) => {
  // 这里可以根据实际情况获取对话消息数量
  return Math.floor(Math.random() * 50) + 1;
};

const toggleSticky = async (chat: any) => {
  try {
    await chatStore.updateGroupInfo(chat.uuid, { isSticky: !chat.isSticky });
    message.success(chat.isSticky ? '已取消置顶' : '已置顶对话');
  } catch (error) {
    message.error('操作失败');
  }
};

const openChat = (uuid: string) => {
  chatStore.setActiveGroup(uuid);
  router.push('/chat');
};

const deleteChat = (chat: any) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除对话"${chat.title || '未命名对话'}"吗？此操作不可恢复。`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await chatStore.deleteGroup(chat.uuid);
        message.success('对话已删除');
      } catch (error) {
        message.error('删除失败');
      }
    }
  });
};

const clearAllNonSticky = () => {
  dialog.warning({
    title: '清空历史对话',
    content: '确定要清空所有非置顶的历史对话吗？此操作不可恢复。',
    positiveText: '清空',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        clearing.value = true;
        await chatStore.delAllGroup();
        message.success('历史对话已清空');
      } catch (error) {
        message.error('清空失败');
      } finally {
        clearing.value = false;
      }
    }
  });
};

// 生命周期
onMounted(async () => {
  loading.value = true;
  try {
    await chatStore.queryMyGroup();
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.chat-history {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.search-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.search-controls {
  @apply flex flex-col md:flex-row gap-4 items-start md:items-center;
}

.search-input-wrapper {
  @apply flex-1;
}

.filter-controls {
  @apply flex gap-3;
}

.history-list-card {
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply flex items-center space-x-2 text-gray-900 dark:text-gray-100;
}

.card-icon {
  @apply text-lg text-primary-600;
}

.loading-state {
  @apply flex flex-col items-center justify-center py-12;
}

.loading-text {
  @apply mt-4 text-gray-500 dark:text-gray-400;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500;
}

.empty-icon {
  @apply text-4xl mb-3;
}

.empty-title {
  @apply text-lg font-medium mb-2;
}

.empty-desc {
  @apply text-sm;
}

.chat-list {
  @apply space-y-3;
}

.chat-item {
  @apply flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors;
}

.chat-item.sticky {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800;
}

.chat-content {
  @apply flex-1 min-w-0;
}

.chat-header {
  @apply flex items-center justify-between mb-2;
}

.chat-title {
  @apply font-medium text-gray-900 dark:text-gray-100 truncate;
}

.chat-badges {
  @apply flex gap-2;
}

.chat-meta {
  @apply flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400;
}

.chat-actions {
  @apply flex items-center gap-2 ml-4;
}

.pagination-wrapper {
  @apply flex justify-center mt-6 pt-6 border-t border-gray-200 dark:border-gray-700;
}
</style>
