<template>
  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">🏷️</span>
      我的故事叫什么名字？
    </div>
    <NInput v-model:value="title" placeholder="例如：小兔子的冒险、魔法森林的秘密..." class="child-input" />
  </div>

  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">💭</span>
      这个故事想告诉大家什么？
    </div>
    <NInput v-model:value="mainIdea" placeholder="例如：朋友之间要互相帮助、勇敢面对困难..." class="child-input" />
  </div>
</template>

<script setup lang="ts">
import { NInput } from 'naive-ui';

const props = defineProps<{
  title: string;
  mainIdea: string;
}>();

const emit = defineEmits<{
  (e: 'update:title', value: string): void;
  (e: 'update:mainIdea', value: string): void;
}>();

// 使用计算属性实现双向绑定
const title = defineModel('title');
const mainIdea = defineModel('mainIdea');
</script>

<style scoped>
.form-group {
  margin-bottom: 2.5rem;
  width: 100%;
  position: relative;
}

.form-group.animated-form-group {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-label {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.dark .form-label {
  color: #60a5fa;
}

.form-icon {
  font-size: 1.5rem;
}

.child-input {
  font-size: 1.1rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.child-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .child-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .child-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}
</style>
