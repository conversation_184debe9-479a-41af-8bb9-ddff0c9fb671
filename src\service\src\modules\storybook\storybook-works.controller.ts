import { Controller, Get, Post, Put, Delete, Body, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt-auth.guard';
import { Request } from 'express';
import { StorybookWorksService } from './storybook-works.service';
import { StorybookExportService } from './storybook-export.service';
import {
  CreateStorybookDto,
  UpdateStorybookDto,
  QueryStorybookDto,
  BatchOperationDto,
  CreateFolderDto,
  UpdateFolderDto,
  QueryFolderDto,
  MoveToFolderDto
} from './dto';

@Controller('storybook/works')
@ApiTags('绘本作品管理')
export class StorybookWorksController {
  constructor(
    private readonly storybookWorksService: StorybookWorksService,
    private readonly storybookExportService: StorybookExportService
  ) {}

  // ==================== 文件夹管理 ====================

  @Post('folder')
  @ApiOperation({ summary: '创建文件夹' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createFolder(@Body() createFolderDto: CreateFolderDto, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.createFolder(userId, createFolderDto);
  }

  @Get('folder')
  @ApiOperation({ summary: '获取文件夹列表' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserFolders(@Query() queryFolderDto: QueryFolderDto, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.getUserFolders(userId, queryFolderDto);
  }

  @Get('folder/:id')
  @ApiOperation({ summary: '获取文件夹详情' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getFolderDetail(@Param('id') id: number, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.getFolderDetail(id, userId);
  }

  @Put('folder/:id')
  @ApiOperation({ summary: '更新文件夹' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async updateFolder(
    @Param('id') id: number,
    @Body() updateFolderDto: UpdateFolderDto,
    @Req() req: Request
  ) {
    const userId = req.user['id'];
    return this.storybookWorksService.updateFolder(id, userId, updateFolderDto);
  }

  @Delete('folder/:id')
  @ApiOperation({ summary: '删除文件夹' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async deleteFolder(@Param('id') id: number, @Req() req: Request) {
    const userId = req.user['id'];
    await this.storybookWorksService.deleteFolder(id, userId);
    return { success: true, message: '文件夹删除成功' };
  }

  // ==================== 作品管理 ====================

  @Post()
  @ApiOperation({ summary: '创建绘本' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createStorybook(@Body() createStorybookDto: CreateStorybookDto, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.createStorybook(userId, createStorybookDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取绘本详情' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getStorybookDetail(@Param('id') id: number, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.getStorybookDetail(id, userId);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新绘本' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async updateStorybook(
    @Param('id') id: number,
    @Body() updateStorybookDto: UpdateStorybookDto,
    @Req() req: Request
  ) {
    const userId = req.user['id'];
    return this.storybookWorksService.updateStorybook(id, userId, updateStorybookDto);
  }

  @Get()
  @ApiOperation({ summary: '获取作品列表' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserStorybooks(@Query() queryStorybookDto: QueryStorybookDto, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookWorksService.getUserStorybooks(userId, queryStorybookDto);
  }

  @Post('batch')
  @ApiOperation({ summary: '批量操作作品' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async batchOperation(@Body() batchOperationDto: BatchOperationDto, @Req() req: Request) {
    const userId = req.user['id'];
    await this.storybookWorksService.batchOperation(userId, batchOperationDto);

    const operationMessages = {
      'delete': '作品已移至回收站',
      'restore': '作品已从回收站恢复',
      'move': '作品已移动到指定文件夹'
    };

    return {
      success: true,
      message: operationMessages[batchOperationDto.operation] || '操作成功'
    };
  }

  @Delete('permanent/:id')
  @ApiOperation({ summary: '永久删除作品' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async permanentlyDeleteStorybook(@Param('id') id: number, @Req() req: Request) {
    const userId = req.user['id'];
    await this.storybookWorksService.permanentlyDeleteStorybook(id, userId);
    return { success: true, message: '作品已永久删除' };
  }

  @Delete('trash')
  @ApiOperation({ summary: '清空回收站' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async emptyTrash(@Req() req: Request) {
    const userId = req.user['id'];
    await this.storybookWorksService.emptyTrash(userId);
    return { success: true, message: '回收站已清空' };
  }

  // ==================== 导出功能 ====================

  @Post(':id/export/pdf')
  @ApiOperation({ summary: '导出绘本为PDF' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async exportToPdf(
    @Param('id') id: number,
    @Body() options: { range?: 'all' | 'custom', startPage?: number, endPage?: number },
    @Req() req: Request
  ) {
    const userId = req.user['id'];
    const result = await this.storybookExportService.exportToPdf(id, userId, options);
    return {
      success: true,
      message: '绘本导出成功',
      data: result
    };
  }

  @Post(':id/export/images')
  @ApiOperation({ summary: '导出绘本为图片集' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async exportToImages(
    @Param('id') id: number,
    @Body() options: {
      range?: 'all' | 'custom',
      startPage?: number,
      endPage?: number,
      quality?: 'high' | 'medium' | 'low'
    },
    @Req() req: Request
  ) {
    const userId = req.user['id'];
    const result = await this.storybookExportService.exportToImages(id, userId, options);
    return {
      success: true,
      message: '绘本导出成功',
      data: result
    };
  }
}
