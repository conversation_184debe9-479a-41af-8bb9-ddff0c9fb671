import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '../../common/auth/jwtAuth.guard';
import { CreateShareDto } from './dto/create-share.dto';
import { DeleteShareDto } from './dto/delete-share.dto';
import { GetShareContentDto } from './dto/get-share-content.dto';
import { GetSharedHtmlDto } from './dto/get-shared-html.dto';
import { GetUserSharesDto } from './dto/get-user-shares.dto';
import { ShareService } from './share.service';

@ApiTags('share')
@Controller('share')
export class ShareController {
  constructor(private readonly shareService: ShareService) {}

  @Post('createHtmlShare')
  @ApiOperation({ summary: '创建HTML分享' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createHtmlShare(
    @Body() createShareDto: CreateShareDto,
    @Req() req: Request,
  ) {
    // 确保req.user存在并且有id属性
    const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;
    console.log('[ShareController] 创建HTML分享请求');
    console.log('[ShareController] 用户ID:', userId);
    console.log('[ShareController] 请求用户对象:', req.user);
    console.log('[ShareController] 请求体类型:', typeof createShareDto);

    if (createShareDto.htmlContent) {
      console.log('[ShareController] htmlContent 类型:', typeof createShareDto.htmlContent);
      console.log('[ShareController] htmlContent 长度:', createShareDto.htmlContent.length);
      console.log('[ShareController] htmlContent 预览:', createShareDto.htmlContent.substring(0, 100) + '...');
    } else {
      console.warn('[ShareController] 请求中缺少 htmlContent');
    }

    try {
      const shareCode = await this.shareService.createHtmlShare(createShareDto, userId);
      console.log('[ShareController] 生成分享代码:', shareCode);
      const responseData = { shareCode };
      console.log('[ShareController] 返回的数据:', responseData);
      return { data: responseData };
    } catch (error) {
      console.error('[ShareController] 创建HTML分享失败:', error);
      throw error;
    }
  }

  @Get('getSharedHtml')
  @ApiOperation({ summary: '获取分享的HTML内容' })
  async getSharedHtml(
    @Query() getSharedHtmlDto: GetSharedHtmlDto,
    @Req() req: Request,
  ) {
    console.log('[ShareController] 获取分享的HTML内容请求');
    console.log('[ShareController] 分享代码:', getSharedHtmlDto.shareCode);
    console.log('[ShareController] 请求头:', req.headers);

    try {
      const htmlContent = await this.shareService.getSharedHtml(getSharedHtmlDto, req);
      console.log('[ShareController] 获取分享内容成功');
      console.log('[ShareController] 返回的HTML内容长度:', htmlContent.htmlContent.length);
      return { success: true, data: htmlContent };
    } catch (error) {
      console.error('[ShareController] 获取分享内容失败:', error);
      throw error;
    }
  }

  @Get('getUserShares')
  @ApiOperation({ summary: '获取用户的HTML分享列表' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserShares(
    @Query() getUserSharesDto: GetUserSharesDto,
    @Req() req: Request,
  ) {
    console.log('[ShareController] 获取用户分享列表请求');

    // 确保req.user存在并且有id属性
    const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;

    if (!userId) {
      console.error('[ShareController] 用户未登录或ID无效');
      throw new Error('用户未登录或ID无效');
    }

    console.log('[ShareController] 用户ID:', userId);
    console.log('[ShareController] 查询参数:', getUserSharesDto);

    try {
      const result = await this.shareService.getUserShares(userId, getUserSharesDto);
      console.log('[ShareController] 获取用户分享列表成功, 数量:', result.count);
      return { success: true, data: result };
    } catch (error) {
      console.error('[ShareController] 获取用户分享列表失败:', error);
      throw error;
    }
  }

  @Post('deleteShare')
  @ApiOperation({ summary: '删除HTML分享' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async deleteShare(
    @Body() deleteShareDto: DeleteShareDto,
    @Req() req: Request,
  ) {
    console.log('[ShareController] 删除分享请求');

    // 确保req.user存在并且有id属性
    const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;

    if (!userId) {
      console.error('[ShareController] 用户未登录或ID无效');
      throw new Error('用户未登录或ID无效');
    }

    console.log('[ShareController] 用户ID:', userId);
    console.log('[ShareController] 分享ID:', deleteShareDto.shareId);

    try {
      const result = await this.shareService.deleteShare(userId, deleteShareDto.shareId);
      console.log('[ShareController] 删除分享成功');
      return { success: true, message: '删除分享成功' };
    } catch (error) {
      console.error('[ShareController] 删除分享失败:', error);
      throw error;
    }
  }

  @Get('getShareContent')
  @ApiOperation({ summary: '获取分享内容' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getShareContent(
    @Query() getShareContentDto: GetShareContentDto,
    @Req() req: Request,
  ) {
    console.log('[ShareController] 获取分享内容请求');

    // 确保req.user存在并且有id属性
    const userId = req.user && typeof req.user === 'object' && 'id' in req.user ? req.user.id : null;

    if (!userId) {
      console.error('[ShareController] 用户未登录或ID无效');
      throw new Error('用户未登录或ID无效');
    }

    console.log('[ShareController] 用户ID:', userId);
    console.log('[ShareController] 分享ID:', getShareContentDto.shareId);

    try {
      const result = await this.shareService.getShareContent(userId, getShareContentDto.shareId);
      console.log('[ShareController] 获取分享内容成功');
      return { success: true, data: result };
    } catch (error) {
      console.error('[ShareController] 获取分享内容失败:', error);
      throw error;
    }
  }
}
