import api from '../index';

/**
 * 表单API模块
 */
export default {
  /**
   * 查询表单列表
   * @param params 查询参数
   * @returns 表单列表
   */
  queryForms: (params: any) => api.get('form/query', { params }),

  /**
   * 创建表单
   * @param data 表单数据
   * @returns 创建结果
   */
  createForm: (data: any) => api.post('form/create', data),

  /**
   * 更新表单
   * @param data 表单数据
   * @returns 更新结果
   */
  updateForm: (data: any) => api.post('form/update', data),

  /**
   * 删除表单
   * @param data 表单ID
   * @returns 删除结果
   */
  deleteForm: (data: { id: number }) => api.post('form/delete', data),

  /**
   * 根据ID获取表单详情
   * @param id 表单ID
   * @returns 表单详情
   */
  getFormById: (id: number) => api.get(`form/getById/${id}`),

  /**
   * 获取应用关联的表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  getFormsByAppId: (appId: number) => api.get(`form/getByAppId/${appId}`),

  /**
   * 兼容旧版API，使用应用接口获取表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  getFormsByAppIdLegacy: (appId: number) => api.get(`app/getFormsByAppId/${appId}`),
};
