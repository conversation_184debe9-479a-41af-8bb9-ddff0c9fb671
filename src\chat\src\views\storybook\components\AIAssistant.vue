<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { N<PERSON><PERSON>er, NDrawerContent, NButton, NInput, NTabs, NTabPane, NSpin, NList, NListItem, NCard, NEmpty, NSpace, NTag } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

// 拖拽相关状态
const buttonPosition = ref({ x: 0, y: 0 });
const isDragging = ref(false);
const startPosition = ref({ x: 0, y: 0 });

// 初始化按钮位置
onMounted(() => {
  // 从本地存储中获取保存的位置，如果没有则使用默认位置
  const savedPosition = localStorage.getItem('aiAssistantButtonPosition');
  if (savedPosition) {
    buttonPosition.value = JSON.parse(savedPosition);
  } else {
    // 默认位置：右下角
    buttonPosition.value = { x: window.innerWidth - 150, y: window.innerHeight - 100 };
  }
});

// 定义属性
const props = defineProps<{
  projectData: any;
}>();

// 抽屉状态
const showDrawer = ref(false);
const activeTab = ref('assistant');
const authStore = useAuthStore();
const isExpanded = ref(false);
const botMessage = ref('AI创作助手为您服务！');

// AI助手状态
const query = ref('');
const isProcessing = ref(false);
const aiResponse = ref('');
const controller = ref(null);
const chatHistory = ref([]);

// 笔记状态
const inspirations = ref<string[]>(props.projectData.inspirations || []);
const newInspiration = ref('');
const autoSaveTimeout = ref(null);

// 常用提示
const commonPrompts = [
  { label: '如何设计有教育意义的绘本故事？', value: '如何设计有教育意义的绘本故事？' },
  { label: '儿童绘本的色彩搭配建议', value: '请给我一些儿童绘本的色彩搭配建议' },
  { label: '如何创作适合3-6岁儿童的角色？', value: '如何创作适合3-6岁儿童的绘本角色？' },
  { label: '绘本故事结构模板', value: '请提供几种常用的儿童绘本故事结构模板' },
  { label: '如何处理绘本中的冲突？', value: '如何在儿童绘本中恰当地处理冲突？' }
];

// 笔记类型
const noteTypes = [
  { id: 'assistant', label: 'AI助手', emoji: '🤖' },
  { id: 'inspirations', label: '灵感收集', emoji: '💡' }
];

// 灵感提示列表
const inspirationPrompts = [
  '如果主角是一只会飞的猫...',
  '故事发生在一个永远不会下雨的世界...',
  '主角发现了一个神奇的门...',
  '一个失去颜色的王国...',
  '一个能听懂动物语言的孩子...',
  '一棵会走路的树...',
  '一个永远向前走的小女孩...',
  '一个装满星星的口袋...'
];

// 切换机器人展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;

  if (isExpanded.value) {
    const messages = [
      'AI创作助手为您服务！',
      '需要创作灵感吗？',
      '有什么问题需要解答？',
      '记录灵感和创意！',
      '让我帮助您的创作！'
    ];
    botMessage.value = messages[Math.floor(Math.random() * messages.length)];
  }
};

// 打开抽屉
const openAssistant = () => {
  showDrawer.value = true;
  isExpanded.value = false;
};

// 关闭抽屉
const closeAssistant = () => {
  showDrawer.value = false;
};

// 自动保存笔记
const autoSaveNotes = () => {
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }

  autoSaveTimeout.value = setTimeout(() => {
    props.projectData.inspirations = inspirations.value;
  }, 2000);
};

// 添加灵感
const addInspiration = () => {
  if (newInspiration.value.trim()) {
    inspirations.value.push(newInspiration.value.trim());
    newInspiration.value = '';
    autoSaveNotes();
  }
};

// 删除灵感
const removeInspiration = (index: number) => {
  inspirations.value.splice(index, 1);
  autoSaveNotes();
};

// 使用提示作为灵感
const usePromptAsInspiration = (prompt: string) => {
  newInspiration.value = prompt;
};

// 监听抽屉状态
watch(showDrawer, (newVal) => {
  if (newVal) {
    // 打开抽屉时，更新灵感内容
    inspirations.value = props.projectData.inspirations || [];
  } else {
    // 关闭抽屉时，保存灵感内容
    props.projectData.inspirations = inspirations.value;
  }
});

// 发送查询
const sendQuery = async () => {
  if (!query.value.trim()) {
    window.$message?.warning('请输入问题');
    return;
  }

  try {
    isProcessing.value = true;
    aiResponse.value = '';

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 添加到聊天历史
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: query.value,
      timestamp: new Date().toISOString()
    };
    chatHistory.value.push(userMessage);

    // 构建提示词
    let prompt = `
作为儿童绘本创作助手，请回答以下关于绘本创作的问题：

${query.value}

请提供专业、具体且适合教育工作者使用的建议。回答应该简洁明了，重点突出，便于实际应用。
`;

    // 如果项目数据中有相关信息，添加到提示中
    if (props.projectData) {
      if (props.projectData.outline?.title) {
        prompt += `\n当前绘本标题：${props.projectData.outline.title}`;
      }

      if (props.projectData.outline?.theme) {
        prompt += `\n当前绘本主题：${props.projectData.outline.theme}`;
      }

      if (props.projectData.outline?.characters && props.projectData.outline.characters.length > 0) {
        prompt += `\n当前绘本角色：${props.projectData.outline.characters.map(c => c.name).join('、')}`;
      }
    }

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        // 提取最新的响应内容
        responseText = text;
        aiResponse.value = responseText;
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

    // 添加AI回复到聊天历史
    const aiMessage = {
      id: Date.now(),
      role: 'assistant',
      content: responseText,
      timestamp: new Date().toISOString()
    };
    chatHistory.value.push(aiMessage);

    // 清空查询
    query.value = '';

  } catch (error) {
    console.error('AI助手查询失败:', error);
    window.$message?.error('AI助手查询失败，请稍后重试');
  } finally {
    isProcessing.value = false;
    controller.value = null;
  }
};

// 使用常用提示
const usePrompt = (promptText) => {
  query.value = promptText;
  sendQuery();
};

// 清空聊天历史
const clearChatHistory = () => {
  if (confirm('确定要清空聊天历史吗？')) {
    chatHistory.value = [];
    aiResponse.value = '';
  }
};

// 拖拽相关方法
const startDrag = (event) => {
  // 只处理鼠标左键
  if (event.button !== 0) return;

  // 记录开始拖拽时的位置
  isDragging.value = true;
  startPosition.value = {
    x: event.clientX - buttonPosition.value.x,
    y: event.clientY - buttonPosition.value.y
  };

  // 添加鼠标移动和松开事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);

  // 阻止默认行为和冒泡
  event.preventDefault();
  event.stopPropagation();
};

const onDrag = (event) => {
  if (!isDragging.value) return;

  // 计算新位置
  buttonPosition.value = {
    x: event.clientX - startPosition.value.x,
    y: event.clientY - startPosition.value.y
  };

  // 限制按钮不超出屏幕边界
  const buttonWidth = 150; // 估计按钮宽度
  const buttonHeight = 50; // 估计按钮高度

  if (buttonPosition.value.x < 0) buttonPosition.value.x = 0;
  if (buttonPosition.value.y < 0) buttonPosition.value.y = 0;
  if (buttonPosition.value.x > window.innerWidth - buttonWidth)
    buttonPosition.value.x = window.innerWidth - buttonWidth;
  if (buttonPosition.value.y > window.innerHeight - buttonHeight)
    buttonPosition.value.y = window.innerHeight - buttonHeight;

  event.preventDefault();
};

const stopDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;

  // 保存位置到本地存储
  localStorage.setItem('aiAssistantButtonPosition', JSON.stringify(buttonPosition.value));

  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 处理按钮点击，只有在非拖拽状态下才打开抽屉
const handleButtonClick = (event) => {
  if (!isDragging.value) {
    openAssistant();
  }
  event.stopPropagation();
};

</script>

<template>
  <div class="ai-assistant">
    <!-- 可拖动的悬浮按钮 -->
    <div
      class="assistant-button"
      :style="{
        left: `${buttonPosition.x}px`,
        top: `${buttonPosition.y}px`,
        cursor: isDragging ? 'grabbing' : 'grab'
      }"
      @mousedown="startDrag"
      @click="handleButtonClick"
    >
      <span class="emoji-icon">🤖</span>
      <span class="button-text">AI助手</span>
      <span class="drag-hint">拖动</span>
    </div>

    <!-- 抽屉 -->
    <NDrawer v-model:show="showDrawer" :width="500" placement="right">
      <NDrawerContent title="AI创作助手" closable @close="closeAssistant">
        <NTabs v-model:value="activeTab" type="segment" animated>
          <NTabPane
            v-for="type in noteTypes"
            :key="type.id"
            :name="type.id"
          >
            <template #tab>
              <div class="tab-label">
                <span class="emoji-icon">{{ type.emoji }}</span>
                <span>{{ type.label }}</span>
              </div>
            </template>
          </NTabPane>
        </NTabs>

        <div class="drawer-content">
          <!-- AI助手 -->
          <div v-if="activeTab === 'assistant'" class="assistant-container">
            <!-- 聊天历史 -->
            <div class="chat-history">
              <div v-if="chatHistory.length === 0" class="empty-chat">
                <span class="emoji-icon large-emoji">🤖</span>
                <p>你好！我是AI创作助手，可以帮助你解决绘本创作中的问题。</p>
                <div class="common-prompts">
                  <p>你可以问我：</p>
                  <NSpace vertical>
                    <NTag
                      v-for="prompt in commonPrompts"
                      :key="prompt.value"
                      type="info"
                      size="medium"
                      @click="usePrompt(prompt.value)"
                    >
                      {{ prompt.label }}
                    </NTag>
                  </NSpace>
                </div>
              </div>

              <div v-else class="chat-messages">
                <div
                  v-for="message in chatHistory"
                  :key="message.id"
                  :class="['chat-message', message.role === 'user' ? 'user-message' : 'ai-message']"
                >
                  <div class="message-avatar">
                    <span v-if="message.role === 'user'" class="emoji-icon">👤</span>
                    <span v-else class="emoji-icon">🤖</span>
                  </div>
                  <div class="message-content">
                    <div v-html="message.content.replace(/\n/g, '<br>')"></div>
                  </div>
                </div>

                <div v-if="isProcessing" class="ai-typing">
                  <div class="message-avatar">
                    <span class="emoji-icon">🤖</span>
                  </div>
                  <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input">
              <NInput
                v-model:value="query"
                type="textarea"
                placeholder="输入你的问题..."
                :autosize="{ minRows: 2, maxRows: 4 }"
                @keydown.enter.ctrl="sendQuery"
              />
              <div class="input-actions">
                <NButton size="small" @click="clearChatHistory" v-if="chatHistory.length > 0">
                  清空历史
                </NButton>
                <NButton
                  type="primary"
                  @click="sendQuery"
                  :loading="isProcessing"
                  :disabled="isProcessing || !query.trim()"
                >
                  发送
                </NButton>
              </div>
              <div class="input-tips">
                提示：按Ctrl+Enter快速发送
              </div>
            </div>
          </div>

          <!-- 灵感收集 -->
          <div v-else-if="activeTab === 'inspirations'" class="inspirations-container">
            <div class="add-inspiration">
              <NInput
                v-model:value="newInspiration"
                type="text"
                placeholder="添加一个新的灵感..."
                @keyup.enter="addInspiration"
              >
                <template #suffix>
                  <NButton type="primary" ghost @click="addInspiration">
                    <span class="emoji-icon">➕</span>
                  </NButton>
                </template>
              </NInput>
            </div>

            <div class="inspirations-board">
              <div v-if="inspirations.length === 0" class="empty-inspirations">
                <span class="emoji-icon large-emoji">💡</span>
                <p>还没有添加灵感，开始记录你的创意吧！</p>
              </div>

              <div v-else class="inspiration-notes">
                <div
                  v-for="(inspiration, index) in inspirations"
                  :key="index"
                  class="inspiration-note"
                  :style="{ '--index': index }"
                >
                  <div class="note-content">{{ inspiration }}</div>
                  <NButton
                    size="tiny"
                    quaternary
                    class="remove-button"
                    @click="removeInspiration(index)"
                  >
                    <span class="emoji-icon small-emoji">❌</span>
                  </NButton>
                </div>
              </div>
            </div>

            <div class="inspiration-prompts">
              <h3 class="prompts-title">灵感提示</h3>
              <div class="prompts-list">
                <NTag
                  v-for="(prompt, index) in inspirationPrompts"
                  :key="index"
                  class="prompt-tag"
                  type="info"
                  size="medium"
                  @click="usePromptAsInspiration(prompt)"
                >
                  {{ prompt }}
                </NTag>
              </div>
            </div>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.ai-assistant {
  position: relative;
  z-index: 10000;
}

.assistant-button {
  position: fixed;
  background-color: #8b5cf6;
  color: white;
  border-radius: 2rem;
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease;
  z-index: 10000;
  user-select: none;
  touch-action: none;
}

.assistant-button:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.assistant-button .drag-hint {
  font-size: 0.7rem;
  opacity: 0;
  margin-left: 0.5rem;
  transition: opacity 0.2s ease;
}

.assistant-button:hover .drag-hint {
  opacity: 0.7;
}

.emoji-icon {
  font-size: 1.25rem;
}

.large-emoji {
  font-size: 3rem;
}

.small-emoji {
  font-size: 12px;
}

.button-text {
  font-weight: 600;
}

.drawer-content {
  height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 16px 0;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* AI助手样式 */
.assistant-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
}

.empty-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #64748b;
  padding: 2rem 0;
}

.common-prompts {
  margin-top: 1.5rem;
  width: 100%;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-message {
  display: flex;
  gap: 0.75rem;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dark .message-avatar {
  background-color: #334155;
}

.message-content {
  background-color: #f1f5f9;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.dark .message-content {
  background-color: #334155;
  color: #e2e8f0;
}

.user-message .message-content {
  background-color: #8b5cf6;
  color: white;
  border-top-right-radius: 0;
}

.ai-message .message-content {
  border-top-left-radius: 0;
}

.ai-typing {
  display: flex;
  gap: 0.75rem;
  align-self: flex-start;
}

.typing-indicator {
  background-color: #f1f5f9;
  padding: 1rem;
  border-radius: 1rem;
  border-top-left-radius: 0;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dark .typing-indicator {
  background-color: #334155;
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #94a3b8;
  animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    opacity: 0.4;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-4px);
  }
  100% {
    opacity: 0.4;
    transform: translateY(0);
  }
}

.chat-input {
  margin-top: 1rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.dark .chat-input {
  border-top: 1px solid #334155;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 0.75rem;
}

.input-tips {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.5rem;
  text-align: right;
}



/* 灵感收集样式 */
.inspirations-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.add-inspiration {
  margin-bottom: 1rem;
}

.inspirations-board {
  flex: 1;
  min-height: 200px;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.dark .inspirations-board {
  background-color: #1e293b;
}

.empty-inspirations {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.inspiration-notes {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.inspiration-note {
  position: relative;
  width: 100%;
  min-height: 60px;
  padding: 0.75rem;
  background-color: #fbec8f;
  border-radius: 0.25rem;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  transform: rotate(var(--rotation));
  --rotation: calc(var(--index) * 1deg - 1.5deg);
}

.dark .inspiration-note {
  background-color: #4a5568;
}

.note-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #5d4037;
}

.dark .note-content {
  color: #e2e8f0;
}

.remove-button {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.inspiration-note:hover .remove-button {
  opacity: 1;
}

.inspiration-prompts {
  margin-top: 0.5rem;
}

.prompts-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .prompts-title {
  color: #e2e8f0;
}

.prompts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.prompt-tag {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.prompt-tag:hover {
  transform: translateY(-2px);
}


</style>
