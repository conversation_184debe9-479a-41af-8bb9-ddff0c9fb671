<template>
  <div class="image-generation-progress">
    <!-- 排队中 -->
    <div v-if="status === 'queuing'" class="progress-item">
      <div class="progress-icon queuing">
        <div class="icon-circle">
          <span class="icon">⏱️</span>
        </div>
      </div>
      <div class="progress-text">排队中...</div>
    </div>
    
    <!-- 生成中 -->
    <div v-if="status === 'generating'" class="progress-item">
      <div class="progress-icon generating">
        <div class="icon-circle">
          <span class="icon">⚡</span>
        </div>
      </div>
      <div class="progress-text">生成中...</div>
    </div>
    
    <!-- 进度百分比 -->
    <div v-if="status === 'progress'" class="progress-item">
      <div class="progress-icon progress">
        <div class="icon-circle">
          <span class="icon">🏃</span>
        </div>
      </div>
      <div class="progress-text">
        进度 {{ progressText }}
      </div>
    </div>
    
    <!-- 生成完成 -->
    <div v-if="status === 'completed'" class="progress-item">
      <div class="progress-icon completed">
        <div class="icon-circle">
          <span class="icon">✅</span>
        </div>
      </div>
      <div class="progress-text">生成完成</div>
    </div>
    
    <!-- 生成失败 -->
    <div v-if="status === 'failed'" class="progress-item">
      <div class="progress-icon failed">
        <div class="icon-circle">
          <span class="icon">❌</span>
        </div>
      </div>
      <div class="progress-text">生成失败</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  status: {
    type: String,
    default: 'queuing', // queuing, generating, progress, completed, failed
    validator: (value) => ['queuing', 'generating', 'progress', 'completed', 'failed'].includes(value)
  },
  progress: {
    type: Number,
    default: 0
  }
});

// 生成进度文本，模拟类似 8...20...31...40...50...62...72...81...100 的效果
const progressText = computed(() => {
  if (props.progress <= 0) return '0%';
  
  // 生成一系列递增的数字
  const steps = [];
  const maxSteps = 8; // 最多显示8个进度点
  const currentProgress = Math.min(100, Math.max(0, props.progress));
  
  // 如果进度是100%，直接返回
  if (currentProgress >= 100) return '100%';
  
  // 计算每个步骤的增量
  const increment = currentProgress / (maxSteps - 1);
  
  // 生成步骤数组
  for (let i = 0; i < maxSteps - 1; i++) {
    const step = Math.round(i * increment);
    if (step <= currentProgress) {
      steps.push(step);
    }
  }
  
  // 确保最后一个数字是当前进度
  if (steps[steps.length - 1] !== currentProgress) {
    steps.push(currentProgress);
  }
  
  // 如果进度接近100但不是100，添加一个接近但不等于100的数字
  if (currentProgress > 90 && currentProgress < 100) {
    steps.push(Math.floor(currentProgress));
  }
  
  // 去重并排序
  const uniqueSteps = [...new Set(steps)].sort((a, b) => a - b);
  
  // 格式化为文本
  return uniqueSteps.join('...') + '%';
});
</script>

<style scoped>
.image-generation-progress {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.dark .image-generation-progress {
  background-color: #1e293b;
  border-color: #334155;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  background-color: white;
  border: 1px solid #f1f5f9;
}

.dark .progress-item {
  background-color: #0f172a;
  border-color: #1e293b;
}

.progress-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-circle {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
}

.dark .icon-circle {
  background-color: #1e293b;
}

.queuing .icon-circle {
  background-color: #f1f5f9;
  color: #64748b;
}

.generating .icon-circle {
  background-color: #fef3c7;
  color: #d97706;
}

.progress .icon-circle {
  background-color: #e0f2fe;
  color: #0284c7;
}

.completed .icon-circle {
  background-color: #dcfce7;
  color: #16a34a;
}

.failed .icon-circle {
  background-color: #fee2e2;
  color: #dc2626;
}

.progress-text {
  font-size: 0.875rem;
  color: #334155;
}

.dark .progress-text {
  color: #e2e8f0;
}

.icon {
  font-size: 1.25rem;
}
</style>
