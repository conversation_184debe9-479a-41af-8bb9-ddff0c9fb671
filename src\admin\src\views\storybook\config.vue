<template>
  <div>
    <page-header title="绘本配置" />
    <page-main v-loading="loading">
    <el-card class="mb-4">
      <el-tabs type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置">
          <el-form
            ref="basicFormRef"
            :model="basicConfig"
            label-width="180px"
            label-position="left"
          >
            <el-form-item label="每日创建绘本限制">
              <el-input-number
                v-model="basicConfig.dailyCreateLimit"
                :min="0"
                :max="100"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                设置为0表示不限制，普通用户每日可创建的绘本数量
              </div>
            </el-form-item>
            <el-form-item label="绘本页面数量限制">
              <el-input-number
                v-model="basicConfig.maxPageCount"
                :min="1"
                :max="50"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                每本绘本最多可以创建的页面数量
              </div>
            </el-form-item>
            <el-form-item label="角色数量限制">
              <el-input-number
                v-model="basicConfig.maxCharacterCount"
                :min="1"
                :max="20"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                每本绘本最多可以创建的角色数量
              </div>
            </el-form-item>
            <el-form-item label="默认页面布局">
              <el-select
                v-model="basicConfig.defaultLayout"
                class="w-200px"
              >
                <el-option
                  v-for="option in layoutOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                新创建绘本的默认页面布局
              </div>
            </el-form-item>
            <el-form-item label="默认页面方向">
              <el-select
                v-model="basicConfig.defaultOrientation"
                class="w-200px"
              >
                <el-option
                  v-for="option in orientationOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                新创建绘本的默认页面方向
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig" :loading="submitting">
                保存配置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 小狐狸助手AI配置 -->
        <el-tab-pane label="小狐狸助手配置">
          <el-form
            ref="aiFormRef"
            :model="aiConfig"
            label-width="180px"
            label-position="left"
          >
            <div class="mb-4 p-3 bg-blue-50 text-blue-700 rounded-md">
              <p class="font-medium">小狐狸助手专用AI配置</p>
              <p class="text-sm mt-1">这里的设置将专门用于小狐狸助手的AI回答，与绘本创作的其他AI功能分开设置。</p>
            </div>

            <el-form-item label="AI模型">
              <el-select
                v-model="aiConfig.model"
                class="w-200px"
                :loading="loadingModels"
              >
                <el-option
                  v-for="option in modelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                小狐狸助手使用的AI模型，从模型管理中加载基础对话模型
              </div>
              <div class="mt-2">
                <el-button size="small" @click="loadChatModels" :loading="loadingModels">
                  刷新模型列表
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="温度">
              <el-slider
                v-model="aiConfig.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                class="w-300px"
              />
              <div class="text-gray-500 text-sm mt-1">
                控制生成内容的随机性，值越高随机性越大
              </div>
            </el-form-item>
            <el-form-item label="最大输出长度">
              <el-input-number
                v-model="aiConfig.maxTokens"
                :min="100"
                :max="4000"
                :step="100"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                单次生成内容的最大长度
              </div>
            </el-form-item>
            <el-form-item label="系统提示词">
              <el-input
                v-model="aiConfig.systemPrompt"
                type="textarea"
                placeholder="请输入系统提示词"
                :rows="5"
              />
              <div class="text-gray-500 text-sm mt-1">
                用于指导小狐狸助手生成内容的系统提示词
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveAiConfig" :loading="submitting">
                保存配置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 显示配置 -->
        <el-tab-pane label="显示配置">
          <el-form
            ref="displayFormRef"
            :model="displayConfig"
            label-width="180px"
            label-position="left"
          >
            <el-form-item label="首页推荐数量">
              <el-input-number
                v-model="displayConfig.homeRecommendCount"
                :min="0"
                :max="20"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                首页展示的推荐绘本数量
              </div>
            </el-form-item>
            <el-form-item label="列表每页数量">
              <el-input-number
                v-model="displayConfig.listPageSize"
                :min="10"
                :max="50"
                :step="5"
                class="w-200px"
              />
              <div class="text-gray-500 text-sm mt-1">
                绘本列表页每页显示的数量
              </div>
            </el-form-item>
            <el-form-item label="默认排序方式">
              <el-select
                v-model="displayConfig.defaultSort"
                class="w-200px"
              >
                <el-option
                  v-for="option in sortOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                绘本列表的默认排序方式
              </div>
            </el-form-item>
            <el-form-item label="显示创作者信息">
              <el-switch
                v-model="displayConfig.showCreator"
                active-text="显示"
                inactive-text="隐藏"
              />
              <div class="text-gray-500 text-sm mt-1">
                是否在绘本详情页显示创作者信息
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveDisplayConfig" :loading="submitting">
                保存配置
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 绘图模型配置 -->
        <el-tab-pane label="绘图模型配置">
          <el-form
            ref="imageModelFormRef"
            :model="imageModelConfig"
            label-width="180px"
            label-position="left"
          >
            <el-form-item label="默认绘图模型">
              <el-select
                v-model="imageModelConfig.defaultModel"
                class="w-300px"
                filterable
              >
                <el-option
                  v-for="model in availableImageModels"
                  :key="model.id"
                  :label="model.modelName"
                  :value="model.model"
                >
                  <div class="flex items-center">
                    <img v-if="model.modelAvatar" :src="model.modelAvatar" class="w-6 h-6 mr-2 rounded-full" />
                    <span>{{ model.modelName }}</span>
                    <span class="text-gray-400 text-xs ml-2">({{ model.model }})</span>
                  </div>
                </el-option>
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                绘本创作中默认使用的图像生成模型
              </div>
            </el-form-item>

            <el-form-item label="默认图像尺寸">
              <el-select
                v-model="imageModelConfig.defaultSize"
                class="w-200px"
              >
                <el-option
                  v-for="option in imageSizeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                生成图像的默认尺寸
              </div>
            </el-form-item>

            <el-form-item label="默认图像质量">
              <el-select
                v-model="imageModelConfig.defaultQuality"
                class="w-200px"
              >
                <el-option
                  v-for="option in imageQualityOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                生成图像的默认质量
              </div>
            </el-form-item>

            <el-form-item label="默认图像风格">
              <el-select
                v-model="imageModelConfig.defaultStyle"
                class="w-200px"
              >
                <el-option
                  v-for="option in imageStyleOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div class="text-gray-500 text-sm mt-1">
                生成图像的默认风格
              </div>
            </el-form-item>

            <el-form-item label="备用图像服务">
              <el-input
                v-model="imageModelConfig.fallbackService"
                placeholder="例如: https://image.pollinations.ai/prompt/%s"
                class="w-300px"
              />
              <div class="text-gray-500 text-sm mt-1">
                当主要图像生成服务不可用时的备用服务，使用 %s 作为提示词占位符
              </div>
            </el-form-item>

            <el-form-item label="启用图像生成">
              <el-switch
                v-model="imageModelConfig.enabled"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="text-gray-500 text-sm mt-1">
                是否启用绘本创作中的图像生成功能
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveImageModelConfig" :loading="submitting">
                保存配置
              </el-button>
              <el-button @click="refreshImageModels" :loading="loadingModels">
                刷新可用模型
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </page-main>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import ApiStorybook from '@/api/modules/storybook';
import ApiModels from '@/api/modules/models';
import ApiAi from '@/api/modules/ai';

// 加载状态
const loading = ref(false);
const submitting = ref(false);
const loadingModels = ref(false);

// 表单引用
const basicFormRef = ref(null);
const aiFormRef = ref(null);
const displayFormRef = ref(null);
const imageModelFormRef = ref(null);

// 选项
const layoutOptions = [
  { label: '图片在上，文字在下', value: 'image-top' },
  { label: '图片在左，文字在右', value: 'image-left' },
  { label: '图片在右，文字在左', value: 'image-right' },
  { label: '全屏图片，文字覆盖', value: 'fullscreen' },
];

const orientationOptions = [
  { label: '横向', value: 'landscape' },
  { label: '纵向', value: 'portrait' },
];

// 模型选项，将从API动态加载
const modelOptions = ref([
  { label: 'DeepSeek-V3', value: 'deepseek-v3' },
  { label: 'GPT-4', value: 'gpt-4' },
  { label: 'GPT-3.5', value: 'gpt-3.5-turbo' },
]);

const sortOptions = [
  { label: '最新创建', value: 'created_desc' },
  { label: '最多浏览', value: 'views_desc' },
  { label: '最多点赞', value: 'likes_desc' },
];

// 图像尺寸选项
const imageSizeOptions = [
  { label: '1024 x 1024', value: '1024x1024' },
  { label: '1024 x 1792', value: '1024x1792' },
  { label: '1792 x 1024', value: '1792x1024' },
];

// 图像质量选项
const imageQualityOptions = [
  { label: '标准质量', value: 'standard' },
  { label: '高清质量', value: 'hd' },
];

// 图像风格选项
const imageStyleOptions = [
  { label: '自然风格', value: 'natural' },
  { label: '生动风格', value: 'vivid' },
];

// 可用的图像模型列表
const availableImageModels = ref([]);

// 配置数据
const basicConfig = reactive({
  dailyCreateLimit: 5,
  maxPageCount: 20,
  maxCharacterCount: 5,
  defaultLayout: 'image-top',
  defaultOrientation: 'landscape',
});

const aiConfig = reactive({
  model: 'deepseek-v3',
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事。',
});

const displayConfig = reactive({
  homeRecommendCount: 6,
  listPageSize: 20,
  defaultSort: 'created_desc',
  showCreator: true,
});

// 图像模型配置
const imageModelConfig = reactive({
  defaultModel: 'gpt-image-1',
  defaultSize: '1024x1024',
  defaultQuality: 'hd',
  defaultStyle: 'natural',
  fallbackService: 'https://image.pollinations.ai/prompt/%s',
  enabled: true,
});

// 从配置列表中获取指定键的值
const getConfigValue = (configs, key) => {
  const config = configs.find(c => c.configKey === key);
  return config ? config.configVal : null;
};

// 更新本地配置
const updateLocalConfig = (configs) => {
  if (!configs || !Array.isArray(configs)) {
    console.error('无效的配置数据:', configs);
    return;
  }

  console.log('正在更新本地配置，配置数量:', configs.length);

  try {
    // 基础配置
    basicConfig.dailyCreateLimit = parseInt(getConfigValue(configs, 'dailyCreateLimit')) || 5;
    basicConfig.maxPageCount = parseInt(getConfigValue(configs, 'maxPageCount')) || 20;
    basicConfig.maxCharacterCount = parseInt(getConfigValue(configs, 'maxCharacterCount')) || 5;
    basicConfig.defaultLayout = getConfigValue(configs, 'defaultLayout') || 'image-top';
    basicConfig.defaultOrientation = getConfigValue(configs, 'defaultOrientation') || 'landscape';

    // 小狐狸助手AI配置（优先使用专用配置，如果没有则使用通用配置）
    aiConfig.model = getConfigValue(configs, 'foxAssistantModel') || getConfigValue(configs, 'aiModel') || 'deepseek-v3';
    aiConfig.temperature = parseFloat(getConfigValue(configs, 'foxAssistantTemperature')) || parseFloat(getConfigValue(configs, 'temperature')) || 0.7;
    aiConfig.maxTokens = parseInt(getConfigValue(configs, 'foxAssistantMaxTokens')) || parseInt(getConfigValue(configs, 'maxTokens')) || 2000;
    aiConfig.systemPrompt = getConfigValue(configs, 'foxAssistantSystemPrompt') || getConfigValue(configs, 'systemPrompt') || '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。';

    // 显示配置
    displayConfig.homeRecommendCount = parseInt(getConfigValue(configs, 'homeRecommendCount')) || 6;
    displayConfig.listPageSize = parseInt(getConfigValue(configs, 'listPageSize')) || 20;
    displayConfig.defaultSort = getConfigValue(configs, 'defaultSort') || 'created_desc';
    displayConfig.showCreator = getConfigValue(configs, 'showCreator') !== 'false';

    console.log('本地配置更新成功');
  } catch (error) {
    console.error('更新本地配置失败:', error);
  }
};

// 加载配置
const loadConfig = async () => {
  loading.value = true;
  try {
    // 加载基础配置
    const configRes = await ApiStorybook.getStorybookConfig();
    console.log('从服务器获取的配置:', configRes);

    if (configRes && Array.isArray(configRes.data)) {
      // 如果返回的是配置数组，直接使用
      updateLocalConfig(configRes.data);
    } else if (configRes && typeof configRes.data === 'object') {
      // 如果返回的是配置对象，按旧方式处理
      const config = configRes.data;

      // 基础配置
      basicConfig.dailyCreateLimit = config.dailyCreateLimit || 5;
      basicConfig.maxPageCount = config.maxPageCount || 20;
      basicConfig.maxCharacterCount = config.maxCharacterCount || 5;
      basicConfig.defaultLayout = config.defaultLayout || 'image-top';
      basicConfig.defaultOrientation = config.defaultOrientation || 'landscape';

      // 小狐狸助手AI配置（优先使用专用配置，如果没有则使用通用配置）
      aiConfig.model = config.foxAssistantModel || config.aiModel || 'deepseek-v3';
      aiConfig.temperature = config.foxAssistantTemperature || config.temperature || 0.7;
      aiConfig.maxTokens = config.foxAssistantMaxTokens || config.maxTokens || 2000;
      aiConfig.systemPrompt = config.foxAssistantSystemPrompt || config.systemPrompt || '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。';

      // 显示配置
      displayConfig.homeRecommendCount = config.homeRecommendCount || 6;
      displayConfig.listPageSize = config.listPageSize || 20;
      displayConfig.defaultSort = config.defaultSort || 'created_desc';
      displayConfig.showCreator = config.showCreator !== false;
    }

    // 加载图像生成配置
    const imageConfigRes = await ApiStorybook.getImageGenerationConfig();
    const imageConfig = imageConfigRes.data;

    // 图像模型配置
    imageModelConfig.defaultModel = imageConfig.model || 'gpt-image-1';
    imageModelConfig.defaultSize = imageConfig.size || '1024x1024';
    imageModelConfig.defaultQuality = imageConfig.quality || 'hd';
    imageModelConfig.defaultStyle = imageConfig.style || 'natural';
    imageModelConfig.fallbackService = imageConfig.fallbackService || 'https://image.pollinations.ai/prompt/%s';
    imageModelConfig.enabled = imageConfig.enabled !== false;

    // 加载可用的图像模型
    await loadImageModels();
  } catch (error) {
    console.error('加载配置失败', error);
    ElMessage.error('加载配置失败');
  } finally {
    loading.value = false;
  }
};

// 加载基础对话模型（用于小狐狸助手）
const loadChatModels = async () => {
  loadingModels.value = true;
  try {
    const res = await ApiAi.getModelsList();
    if (res.data) {
      const { modelMaps, modelTypeList } = res.data;

      // 处理模型列表，与聊天组件保持一致
      const flatModelArray = Object.values(modelMaps).flat() as any[];
      // 筛选出基础对话模型（keyType为1）
      const filteredModelArray = flatModelArray.filter(
        (model) => model.keyType === 1
      );

      modelOptions.value = filteredModelArray.map((model) => ({
        label: model.modelName,
        value: model.model,
      }));

      // 如果当前选择的模型不在可用列表中，选择第一个可用模型
      if (modelOptions.value.length > 0) {
        const modelExists = modelOptions.value.some(option => option.value === aiConfig.model);
        if (!modelExists) {
          aiConfig.model = modelOptions.value[0].value;
        }
      }

      console.log('已加载小狐狸助手可用模型:', modelOptions.value);
    }
  } catch (error) {
    console.error('加载对话模型失败', error);
    ElMessage.error('加载对话模型失败，将使用默认模型');
  } finally {
    loadingModels.value = false;
  }
};

// 加载可用的图像模型
const loadImageModels = async () => {
  loadingModels.value = true;
  try {
    const res = await ApiModels.getModelList({ modelType: 2 }); // 图像模型类型为2
    if (res.data && Array.isArray(res.data.list)) {
      availableImageModels.value = res.data.list.filter(model => model.status === 1);

      // 如果当前选择的模型不在可用列表中，选择第一个可用模型
      if (availableImageModels.value.length > 0) {
        const modelExists = availableImageModels.value.some(model => model.model === imageModelConfig.defaultModel);
        if (!modelExists) {
          imageModelConfig.defaultModel = availableImageModels.value[0].model;
        }
      }
    }
  } catch (error) {
    console.error('加载图像模型失败', error);
    ElMessage.error('加载图像模型失败');
  } finally {
    loadingModels.value = false;
  }
};

// 保存基础配置
const saveBasicConfig = async () => {
  submitting.value = true;
  try {
    await ApiStorybook.updateStorybookConfig({
      dailyCreateLimit: basicConfig.dailyCreateLimit,
      maxPageCount: basicConfig.maxPageCount,
      maxCharacterCount: basicConfig.maxCharacterCount,
      defaultLayout: basicConfig.defaultLayout,
      defaultOrientation: basicConfig.defaultOrientation,
    });
    ElMessage.success('保存基础配置成功');
  } catch (error) {
    console.error('保存基础配置失败', error);
    ElMessage.error('保存基础配置失败');
  } finally {
    submitting.value = false;
  }
};

// 保存小狐狸助手AI配置
const saveAiConfig = async () => {
  submitting.value = true;
  try {
    console.log('正在保存小狐狸助手配置:', {
      foxAssistantModel: aiConfig.model,
      foxAssistantTemperature: aiConfig.temperature,
      foxAssistantMaxTokens: aiConfig.maxTokens,
      foxAssistantSystemPrompt: aiConfig.systemPrompt,
    });

    const response = await ApiStorybook.updateStorybookConfig({
      // 使用特殊前缀标记这是小狐狸助手的配置
      foxAssistantModel: aiConfig.model,
      foxAssistantTemperature: aiConfig.temperature,
      foxAssistantMaxTokens: aiConfig.maxTokens,
      foxAssistantSystemPrompt: aiConfig.systemPrompt,
      // 同时保持向后兼容
      aiModel: aiConfig.model,
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens,
      systemPrompt: aiConfig.systemPrompt,
    });

    console.log('保存小狐狸助手配置成功，服务器响应:', response);

    // 如果服务器返回了最新配置，直接使用
    if (response && response.configs) {
      console.log('服务器返回了最新配置，正在更新本地配置');
      updateLocalConfig(response.configs);
    } else {
      // 否则重新加载配置
      console.log('重新加载配置');
      await loadConfig();
    }

    ElMessage.success('保存小狐狸助手配置成功');
  } catch (error) {
    console.error('保存小狐狸助手配置失败', error);
    ElMessage.error('保存小狐狸助手配置失败');
  } finally {
    submitting.value = false;
  }
};

// 保存显示配置
const saveDisplayConfig = async () => {
  submitting.value = true;
  try {
    await ApiStorybook.updateStorybookConfig({
      homeRecommendCount: displayConfig.homeRecommendCount,
      listPageSize: displayConfig.listPageSize,
      defaultSort: displayConfig.defaultSort,
      showCreator: displayConfig.showCreator,
    });
    ElMessage.success('保存显示配置成功');
  } catch (error) {
    console.error('保存显示配置失败', error);
    ElMessage.error('保存显示配置失败');
  } finally {
    submitting.value = false;
  }
};

// 保存图像模型配置
const saveImageModelConfig = async () => {
  submitting.value = true;
  try {
    // 使用图像生成配置API
    await ApiStorybook.updateImageGenerationConfig({
      model: imageModelConfig.defaultModel,
      size: imageModelConfig.defaultSize,
      quality: imageModelConfig.defaultQuality,
      style: imageModelConfig.defaultStyle,
      fallbackService: imageModelConfig.fallbackService,
      enabled: imageModelConfig.enabled,
    });
    ElMessage.success('保存图像模型配置成功');
  } catch (error) {
    console.error('保存图像模型配置失败', error);
    ElMessage.error('保存图像模型配置失败');
  } finally {
    submitting.value = false;
  }
};

// 刷新可用的图像模型
const refreshImageModels = async () => {
  await loadImageModels();
  ElMessage.success('刷新图像模型列表成功');
};

onMounted(() => {
  loadConfig();
  // 加载基础对话模型
  loadChatModels();
});
</script>

<style scoped>
.w-200px {
  width: 200px;
}
.w-300px {
  width: 300px;
}
</style>
