<template>
  <div class="user-profile">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">个人资料</h1>
      <p class="page-description">管理您的个人信息和账户设置</p>
    </div>

    <!-- 复用现有的 Profile 组件 -->
    <div class="profile-wrapper">
      <Profile />
    </div>
  </div>
</template>

<script setup lang="ts">
import Profile from '@/components/common/UserCenter/Profile.vue';
</script>

<style scoped>
.user-profile {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.profile-wrapper {
  /* 确保内部组件样式正常显示 */
  @apply w-full;
}

/* 覆盖内部组件的一些样式以适应新的布局 */
.profile-wrapper :deep(.profile-container) {
  @apply space-y-6;
}

.profile-wrapper :deep(.user-info-card) {
  @apply mb-6;
}
</style>
