import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseGuards, Req, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/auth/jwtAuth.guard';
import { StorybookService } from './storybook.service';
import {
  CreateStorybookDto,
  UpdateStorybookDto,
  CreatePageDto,
  CreateCharacterDto
} from './dto';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';
import { Request } from 'express';

@ApiTags('Storybook')
@Controller('storybook')
export class StorybookController {
  constructor(private readonly storybookService: StorybookService) {}

  // ==================== 绘本作品管理 ====================
  // 作品管理功能已移除

  // ==================== 绘本页面管理 ====================

  @Post('page')
  @ApiOperation({ summary: '创建绘本页面' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createPage(@Body() createPageDto: CreatePageDto) {
    return this.storybookService.createPage(createPageDto);
  }

  @Put('page/:id')
  @ApiOperation({ summary: '更新绘本页面' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '页面ID' })
  async updatePage(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: Partial<CreatePageDto>,
  ) {
    return this.storybookService.updatePage(id, updateData);
  }

  @Delete('page/:id')
  @ApiOperation({ summary: '删除绘本页面' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '页面ID' })
  async deletePage(@Param('id', ParseIntPipe) id: number) {
    await this.storybookService.deletePage(id);
    return { success: true, message: '删除成功' };
  }

  @Get(':id/pages')
  @ApiOperation({ summary: '获取绘本的所有页面' })
  @ApiParam({ name: 'id', description: '绘本ID' })
  async getStorybookPages(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.getStorybookPages(id);
  }

  // ==================== 绘本角色管理 ====================

  @Post('character')
  @ApiOperation({ summary: '创建角色' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createCharacter(@Body() createCharacterDto: CreateCharacterDto) {
    return this.storybookService.createCharacter(createCharacterDto);
  }

  @Put('character/:id')
  @ApiOperation({ summary: '更新角色' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '角色ID' })
  async updateCharacter(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: Partial<CreateCharacterDto>,
  ) {
    return this.storybookService.updateCharacter(id, updateData);
  }

  @Delete('character/:id')
  @ApiOperation({ summary: '删除角色' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '角色ID' })
  async deleteCharacter(@Param('id', ParseIntPipe) id: number) {
    await this.storybookService.deleteCharacter(id);
    return { success: true, message: '删除成功' };
  }

  @Get(':id/characters')
  @ApiOperation({ summary: '获取绘本的所有角色' })
  @ApiParam({ name: 'id', description: '绘本ID' })
  async getStorybookCharacters(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.getStorybookCharacters(id);
  }

  @Get('character/templates')
  @ApiOperation({ summary: '获取角色模板' })
  async getCharacterTemplates() {
    return this.storybookService.getCharacterTemplates();
  }

  @Get('character/mine')
  @ApiOperation({ summary: '获取我的角色库' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getUserCharacters(@Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookService.getUserCharacters(userId);
  }

  // ==================== 配置管理 ====================

  @Get('config/:key')
  @ApiOperation({ summary: '获取配置' })
  @ApiParam({ name: 'key', description: '配置键' })
  async getConfig(@Param('key') key: string) {
    const config = await this.storybookService.getConfig(key);
    if (!config || config.public !== 1) {
      return { configKey: key, configVal: '', public: 0 };
    }
    return config;
  }

  @Get('fox-assistant-config')
  @ApiOperation({ summary: '获取小狐狸助手配置' })
  async getFoxAssistantConfig() {
    // 获取所有配置
    const configs = await this.storybookService.getAllConfigs();

    // 提取小狐狸助手专用配置
    const foxAssistantConfig = {
      // 优先使用专用配置，如果没有则使用通用配置
      foxAssistantModel: this.getConfigValue(configs, 'foxAssistantModel') || this.getConfigValue(configs, 'aiModel') || 'deepseek-v3',
      foxAssistantTemperature: parseFloat(this.getConfigValue(configs, 'foxAssistantTemperature') || this.getConfigValue(configs, 'temperature') || '0.7'),
      foxAssistantMaxTokens: parseInt(this.getConfigValue(configs, 'foxAssistantMaxTokens') || this.getConfigValue(configs, 'maxTokens') || '2000'),
      foxAssistantSystemPrompt: this.getConfigValue(configs, 'foxAssistantSystemPrompt') || this.getConfigValue(configs, 'systemPrompt') ||
        '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事，并能以简单易懂的方式回答儿童关于创作的问题。',

      // 同时保留通用配置以保持兼容性
      aiModel: this.getConfigValue(configs, 'aiModel') || 'deepseek-v3',
      temperature: parseFloat(this.getConfigValue(configs, 'temperature') || '0.7'),
      maxTokens: parseInt(this.getConfigValue(configs, 'maxTokens') || '2000'),
      systemPrompt: this.getConfigValue(configs, 'systemPrompt') ||
        '你是一个专业的儿童绘本创作助手，擅长创作有教育意义、富有想象力的儿童故事。',
    };

    return foxAssistantConfig;
  }

  // 辅助方法：从配置列表中获取指定键的值
  private getConfigValue(configs: any[], key: string): string | null {
    const config = configs.find(c => c.configKey === key);
    return config ? config.configVal : null;
  }

  // ==================== AI绘图资源管理 ====================

  @Post('image')
  @ApiOperation({ summary: '创建图像记录' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async createImage(@Body() createImageDto: CreateImageDto, @Req() req: Request) {
    const userId = req.user['id'];
    return this.storybookService.createImage(userId, createImageDto);
  }

  @Get('image')
  @ApiOperation({ summary: '获取图像列表' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'imageType', required: false, description: '图像类型' })
  @ApiQuery({ name: 'storybookId', required: false, description: '绘本ID' })
  @ApiQuery({ name: 'pageId', required: false, description: '页面ID' })
  @ApiQuery({ name: 'characterId', required: false, description: '角色ID' })
  @ApiQuery({ name: 'keyword', required: false, description: '关键词' })
  async getImages(
    @Req() req: Request,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('imageType') imageType?: number,
    @Query('storybookId') storybookId?: number,
    @Query('pageId') pageId?: number,
    @Query('characterId') characterId?: number,
    @Query('keyword') keyword?: string,
  ) {
    const userId = req.user['id'];
    return this.storybookService.getImages({
      page,
      limit,
      userId,
      imageType,
      storybookId,
      pageId,
      characterId,
      keyword
    });
  }

  @Get('image/:id')
  @ApiOperation({ summary: '获取图像详情' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async getImageDetail(@Param('id', ParseIntPipe) id: number) {
    return this.storybookService.getImageDetail(id);
  }

  @Put('image/:id')
  @ApiOperation({ summary: '更新图像' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async updateImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateImageDto: UpdateImageDto,
  ) {
    return this.storybookService.updateImage(id, updateImageDto);
  }

  @Delete('image/:id')
  @ApiOperation({ summary: '删除图像' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiParam({ name: 'id', description: '图像ID' })
  async deleteImage(@Param('id', ParseIntPipe) id: number) {
    await this.storybookService.deleteImage(id);
    return { success: true, message: '删除成功' };
  }

  @Get('image-config')
  @ApiOperation({ summary: '获取图像生成配置' })
  async getImageGenerationConfig() {
    return this.storybookService.getImageGenerationConfig();
  }

  // 添加一个带ID参数的备用路由
  @Get('image-config/:id')
  @ApiOperation({ summary: '获取图像生成配置（带ID参数）' })
  async getImageGenerationConfigWithId(@Param('id') id: string) {
    return this.storybookService.getImageGenerationConfig();
  }

  // ==================== 内容安全管理 ====================

  @Post('filter-content')
  @ApiOperation({ summary: '过滤内容中的敏感词' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async filterSensitiveContent(
    @Body('content') content: string,
    @Req() req: Request,
  ) {
    const userId = req.user['id'];
    const filteredContent = await this.storybookService.filterSensitiveContent(content, userId);
    return { original: content, filtered: filteredContent };
  }

  @Post('check-content')
  @ApiOperation({ summary: '检查内容是否包含敏感词' })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async checkContent(
    @Body('content') content: string,
    @Req() req: Request,
  ) {
    const userId = req.user['id'];
    return this.storybookService.checkContent(content, userId);
  }
}
