<script lang="ts" setup>
import { useChatStore } from '@/store';
import { Copy, Delete } from '@icon-park/vue-next';
import mdKatex from '@traptitech/markdown-it-katex';
import hljs from 'highlight.js';
import MarkdownIt from 'markdown-it';
import mila from 'markdown-it-link-attributes';
import { NImage, NProgress, useMessage } from 'naive-ui';
import { computed, inject, onMounted, onUnmounted, ref, watch } from 'vue';

import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';

interface Props {
  inversion?: boolean;
  text?: string;
  modelType?: number;
  status?: number;
  loading?: boolean;
  asRawText?: boolean;
  fileInfo?: string;
  model?: string;
  drawId?: string;
  customId?: string;
  modelName?: string;
}

interface Emit {
  (ev: 'regenerate'): void;
  (ev: 'delete'): void;
  (ev: 'copy'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const { isMobile } = useBasicLayout();
const ms = useMessage();

const onConversation = inject('onConversation');

const textRef = ref<HTMLElement>();
const chatStore = useChatStore();
let intervalId: number | undefined;
const progressValue = ref(10); // 默认进度值
const progressStatus = ref('generating'); // 进度状态

// 监听状态变化
watch(
  () => props.status,
  (currentStatus) => {
    // 清除可能已经存在的定时器
    if (intervalId !== undefined) {
      clearInterval(intervalId);
      intervalId = undefined;
    }

    // 当status为2时，启动定时器
    if (currentStatus === 2) {
      progressStatus.value = 'generating';
      progressValue.value = 10;

      // 启动定时器，每5秒查询一次最新状态
      intervalId = window.setInterval(async () => {
        await chatStore.queryActiveChatLogList();
      }, 5000);

      // 启动进度模拟
      simulateProgress();
    } else if (currentStatus === 3) {
      // 成功完成
      progressStatus.value = 'completed';
      progressValue.value = 100;
    } else if (currentStatus === 5) {
      // 生成失败
      progressStatus.value = 'failed';
    }
  },
  { immediate: true }
);

// 模拟进度增长
function simulateProgress() {
  // 创建一个定时器，模拟进度增长
  const simulateInterval = setInterval(() => {
    // 如果状态不再是生成中，则停止模拟
    if (props.status !== 2) {
      clearInterval(simulateInterval);
      return;
    }

    // 根据当前进度值确定增长速度
    let increment = 1;
    if (progressValue.value < 30) {
      increment = 2; // 初始阶段快速增长
    } else if (progressValue.value < 70) {
      increment = 1; // 中间阶段正常增长
    } else {
      increment = 0.5; // 后期缓慢增长
    }

    // 增加进度值，但不超过95%
    progressValue.value = Math.min(95, progressValue.value + increment);

    // 更新状态
    if (progressValue.value >= 10) {
      progressStatus.value = 'progress';
    }
  }, 1000);

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(simulateInterval);
  });
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (intervalId !== undefined) {
    clearInterval(intervalId);
    intervalId = undefined;
  }
});

// 格式化进度文本
const progressText = computed(() => {
  if (progressValue.value <= 0) return '0%';

  // 生成一系列递增的数字
  const steps = [];
  const maxSteps = 8; // 最多显示8个进度点
  const currentProgress = Math.min(100, Math.max(0, progressValue.value));

  // 如果进度是100%，直接返回
  if (currentProgress >= 100) return '100%';

  // 计算每个步骤的增量
  const increment = currentProgress / (maxSteps - 1);

  // 生成步骤数组
  for (let i = 0; i < maxSteps - 1; i++) {
    const step = Math.round(i * increment);
    if (step <= currentProgress) {
      steps.push(step);
    }
  }

  // 确保最后一个数字是当前进度
  if (steps[steps.length - 1] !== currentProgress) {
    steps.push(currentProgress);
  }

  // 如果进度接近100但不是100，添加一个接近但不等于100的数字
  if (currentProgress > 90 && currentProgress < 100) {
    steps.push(Math.floor(currentProgress));
  }

  // 去重并排序
  const uniqueSteps = [...new Set(steps)].sort((a, b) => a - b);

  // 格式化为文本
  return uniqueSteps.join('...') + '%';
});

const mdi = new MarkdownIt({
  linkify: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language));
    if (validLang) {
      const lang = language ?? '';
      return highlightBlock(
        hljs.highlight(code, { language: lang }).value,
        lang
      );
    }
    return highlightBlock(hljs.highlightAuto(code).value, '');
  },
});

const fileInfo = computed(() => props.fileInfo);
const fileInfoArray = computed(() => {
  if (!props.fileInfo) return [];
  return [props.fileInfo];
});

// 如果customId存在，解析为JSON数组，这是多张图片的情况
const customImageUrls = computed(() => {
  if (!props.customId) return [];
  try {
    return JSON.parse(props.customId);
  } catch (e) {
    return [];
  }
});

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } });
mdi.use(mdKatex, {
  blockClass: 'katexmath-block rounded-md p-[10px]',
  errorColor: ' #cc0000',
});

const text = computed(() => {
  const value = props.text ?? '';
  if (!props.asRawText) return mdi.render(value);
  return value;
});

function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy" onclick="copyToClipboard(this)">复制</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;
}

function handleCopy() {
  emit('copy');
}

function handleDelete() {
  emit('delete');
}

function handleRegenerate() {
  emit('regenerate');
}

// 预览图片
function previewImage(index = 0) {
  if (customImageUrls.value.length > 0) {
    // 使用naive-ui的NImage组件预览
    const images = customImageUrls.value.map(url => url);
    // 这里可以实现预览逻辑，例如使用第三方库或自定义预览组件
    console.log('预览图片', images[index]);
  }
}

// 预览图片（在新窗口中打开）
function viewInSidebar(imageUrl) {
  window.open(imageUrl, '_blank');
}

defineExpose({ textRef });
</script>

<template>
  <div class="flex flex-col group w-full">
    <div ref="textRef" class="leading-relaxed break-words w-full">
      <div class="flex flex-col items-start w-full">
        <div class="w-full">
          <span v-if="status === 2 && !text" class="loading-anchor"></span>
          <div class="flex flex-col items-start">
            <div class="w-full">
              <!-- 进度显示 -->
              <div v-if="status === 2" class="image-generation-progress mb-4">
                <!-- 生成中 -->
                <div v-if="progressStatus === 'generating'" class="progress-item">
                  <div class="progress-icon generating">
                    <div class="icon-circle">
                      <span class="icon">⚡</span>
                    </div>
                  </div>
                  <div class="progress-text">生成中...</div>
                </div>

                <!-- 进度百分比 -->
                <div v-if="progressStatus === 'progress'" class="progress-item">
                  <div class="progress-icon progress">
                    <div class="icon-circle">
                      <span class="icon">🏃</span>
                    </div>
                  </div>
                  <div class="progress-text">
                    进度 {{ progressText }}
                  </div>
                </div>

                <!-- 进度条 -->
                <div class="mt-2">
                  <NProgress
                    type="line"
                    :percentage="progressValue"
                    :height="8"
                    :border-radius="4"
                    :color="progressValue < 30 ? '#f59e0b' : progressValue < 70 ? '#3b82f6' : '#10b981'"
                    :rail-color="'rgba(0, 0, 0, 0.04)'"
                    :processing="status === 2"
                  />
                </div>
              </div>

              <div
                :class="[
                  'w-full markdown-body text-gray-800 dark:text-gray-400 ',
                  {
                    'markdown-body-generate':
                      status === 1 || status === 2 || !text,
                  },
                ]"
                v-html="text"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="text-wrap rounded-lg min-w-12 text-gray-800 dark:text-gray-400">
      <div>
        <div>
          <div>
            <!-- 单张图片显示 -->
            <div
              v-if="fileInfo && !customId"
              class="my-1 flex w-auto"
              :style="{
                maxWidth: isMobile ? '100%' : '',
                maxHeight: isMobile ? '' : '30vh',
                objectFit: 'contain',
              }"
            >
              <div class="relative group">
                <NImage
                  :src="fileInfo"
                  :preview-src="fileInfo"
                  alt="图片"
                  class="rounded-md flex mb-1 mr-4"
                  :style="{
                    maxWidth: '100%',
                    maxHeight: '100%',
                    objectFit: 'contain',
                  }"
                />
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                  <button
                    @click.stop="viewInSidebar(fileInfo)"
                    class="p-1.5 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-700"
                    title="在新窗口查看"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-700 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- 多张图片显示 -->
            <div
              v-if="customId && customImageUrls.length > 0"
              class="my-1 grid grid-cols-2 md:grid-cols-2 gap-2"
              :style="{
                maxWidth: isMobile ? '100%' : '',
                maxHeight: isMobile ? '' : '60vh',
              }"
            >
              <div
                v-for="(image, index) in customImageUrls"
                :key="index"
                class="relative"
              >
                <div class="relative group">
                  <NImage
                    :src="image"
                    :preview-src="image"
                    alt="生成的图片"
                    class="rounded-md w-full h-auto"
                    :style="{
                      maxHeight: '100%',
                      objectFit: 'contain',
                    }"
                    @click="previewImage(index)"
                  />
                  <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                    <button
                      @click.stop="viewInSidebar(image)"
                      class="p-1.5 bg-white dark:bg-gray-800 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-700"
                      title="在新窗口查看"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-700 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.loading-anchor {
  @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite];
}

.markdown-body-generate {
  @apply animate-pulse;
}

.markdown-body {
  @apply text-gray-800 dark:text-gray-400;
}

.markdown-body pre {
  @apply bg-gray-100 dark:bg-gray-800;
}

.markdown-body code {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-400;
}

.markdown-body pre code {
  @apply bg-transparent;
}

.markdown-body a {
  @apply text-blue-600 dark:text-blue-400;
}

.markdown-body table {
  @apply border-collapse border border-gray-300 dark:border-gray-600;
}

.markdown-body th,
.markdown-body td {
  @apply border border-gray-300 dark:border-gray-600 px-3 py-2;
}

.markdown-body .katexmath-block {
  @apply bg-gray-100 dark:bg-gray-800;
}

.code-block-wrapper {
  @apply relative;
}

.code-block-header {
  @apply flex items-center justify-between rounded-t-md bg-gray-200 dark:bg-gray-700 px-4 py-2;
}

.code-block-header__lang {
  @apply text-xs font-bold uppercase text-gray-700 dark:text-gray-300;
}

.code-block-header__copy {
  @apply cursor-pointer text-xs text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100;
}

.code-block-body {
  @apply rounded-b-md p-4;
}

/* 进度显示样式 */
.image-generation-progress {
  @apply flex flex-col gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700;
}

.progress-item {
  @apply flex items-center gap-3 p-2 rounded-md bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800;
}

.progress-icon {
  @apply flex items-center justify-center;
}

.icon-circle {
  @apply w-8 h-8 rounded-full flex items-center justify-center;
}

.generating .icon-circle {
  @apply bg-amber-100 dark:bg-amber-900 text-amber-600 dark:text-amber-400;
}

.progress .icon-circle {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400;
}

.completed .icon-circle {
  @apply bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400;
}

.failed .icon-circle {
  @apply bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400;
}

.progress-text {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.icon {
  @apply text-xl;
}
</style>
