import { type App } from 'vue';
import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHashHistory } from 'vue-router';
import { setupPageGuard } from './permission';

const routes: RouteRecordRaw[] = [
  // 老师角色路由 - 使用TeacherLayout
  {
    path: '/teacher',
    name: 'TeacherWorkspace',
    component: () => import('@/layout/TeacherLayout.vue'),
    redirect: '/teacher/dashboard',
    meta: {
      title: '教师工作台',
      role: 'teacher'
    },
    children: [
      {
        path: '/teacher/dashboard',
        name: 'TeacherDashboard',
        component: () => import('@/views/dashboard/TeacherDashboard.vue'),
        meta: {
          title: '教师工作台',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/chat',
        name: 'TeacherChat',
        component: () => import('@/views/chat/chat.vue'),
        meta: {
          title: 'AI教学助手',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/works',
        name: 'StudentWorks',
        component: () => import('@/views/storybook/WorksManagement.vue'),
        meta: {
          title: '学生作品管理',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/tools',
        name: 'TeachingTools',
        component: () => import('@/views/teacher/TeachingTools.vue'),
        meta: {
          title: '教学工具',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/class',
        name: 'ClassManagement',
        component: () => import('@/views/teacher/ClassManagement.vue'),
        meta: {
          title: '班级管理',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/class/student/:id',
        name: 'StudentProfile',
        component: () => import('@/views/teacher/StudentProfile.vue'),
        meta: {
          title: '学生档案',
          role: 'teacher',
          requiresAuth: true
        }
      },
      {
        path: '/teacher/settings',
        name: 'TeacherSettings',
        component: () => import('@/views/settings/index.vue'),
        meta: {
          title: '设置中心',
          role: 'teacher',
          requiresAuth: true
        }
      }
    ]
  },

  // 学生角色路由 - 使用StudentLayout
  {
    path: '/student',
    name: 'StudentWorkspace',
    component: () => import('@/layout/StudentLayout.vue'),
    redirect: '/student/dashboard',
    meta: {
      title: '学生创作空间',
      role: 'student'
    },
    children: [
      {
        path: '/student/dashboard',
        name: 'StudentDashboard',
        component: () => import('@/views/dashboard/StudentDashboard.vue'),
        meta: {
          title: '学生创作空间',
          role: 'student',
          requiresAuth: true
        }
      },
      {
        path: '/student/storybook',
        name: 'StudentStorybook',
        component: () => import('@/views/storybook/StoryBookStudio.vue'),
        meta: {
          title: 'AI绘本创作',
          role: 'student',
          requiresAuth: true
        }
      },
      {
        path: '/student/storybook/edit/:id',
        name: 'EditStorybook',
        component: () => import('@/views/storybook/EditStorybook.vue'),
        meta: {
          title: '编辑绘本',
          role: 'student',
          requiresAuth: true
        }
      },
      {
        path: '/student/storybook/view/:id',
        name: 'ViewStorybook',
        component: () => import('@/views/storybook/ViewStorybook.vue'),
        meta: {
          title: '查看绘本',
          role: 'student',
          requiresAuth: true
        }
      },
      {
        path: '/student/works',
        name: 'MyWorks',
        component: () => import('@/views/storybook/MyWorks.vue'),
        meta: {
          title: '我的作品',
          role: 'student',
          requiresAuth: true
        }
      },
      {
        path: '/student/settings',
        name: 'StudentSettings',
        component: () => import('@/views/settings/index.vue'),
        meta: {
          title: '设置中心',
          role: 'student',
          requiresAuth: true
        }
      }
    ]
  },

  // 通用路由 - 只包含共享功能，不包含角色特定功能
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/welcome',
    children: [
      {
        path: '/welcome',
        name: 'Welcome',
        component: () => import('@/views/welcome/index.vue'),
        meta: {
          title: '欢迎页面',
          requiresAuth: false
        }
      },
      // 共享功能路由
      {
        path: '/showcase',
        name: 'Showcase',
        component: () => import('@/views/showcase/index.vue'),
        meta: {
          title: '作品广场',
          requiresAuth: false
        }
      },
      {
        path: '/user-center',
        name: 'UserCenter',
        component: () => import('@/views/userCenter/UserCenterModalHandler.vue'),
        meta: {
          title: '个人中心',
          requiresAuth: true
        }
      },
      {
        path: '/aiProgramming',
        name: 'AIProgramming',
        component: () => import('@/views/aiProgramming/index.vue'),
        meta: {
          title: 'AI编程助手',
          requiresAuth: true
        }
      },
      // 公共访问路由
      {
        path: '/storybook/reader/:id',
        name: 'BookReader',
        component: () => import('@/views/storybook/BookReader.vue'),
        meta: {
          title: '绘本阅读',
          requiresAuth: false
        }
      },
      {
        path: '/storybook/share/:id',
        name: 'ShareStorybook',
        component: () => import('@/views/storybook/ShareStorybook.vue'),
        meta: {
          title: '分享绘本',
          requiresAuth: false
        }
      }
    ]
  },

  {
    path: '/share/:shareCode',
    name: 'ShareView',
    component: () => import('@/views/share/view.vue'),
    meta: {
      title: '查看分享内容',
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404/index.vue'),
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/views/exception/500/index.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/404',
  },
];

export const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

setupPageGuard(router);

export async function setupRouter(app: App) {
  app.use(router);
  await router.isReady();
}
