<template>
  <div class="share-view">
    <div v-if="loading" class="loading">
      <NSpin size="large" />
      <p>加载中...</p>
    </div>
    <div v-else-if="error" class="error">
      <div class="error-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="#f5222d">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
        </svg>
      </div>
      <h2>{{ error }}</h2>
      <NButton @click="goHome">返回首页</NButton>
    </div>
    <iframe v-else ref="contentFrame" class="content-frame" :srcdoc="htmlContent"></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NSpin, NButton, useMessage } from 'naive-ui';
import axios from 'axios';
import { useGlobalStoreWithOut } from '@/store';

const route = useRoute();
const router = useRouter();
const message = useMessage();
const globalStore = useGlobalStoreWithOut();
const shareCode = ref(route.params.shareCode as string);
const htmlContent = ref('');
const loading = ref(true);
const error = ref('');
const contentFrame = ref<HTMLIFrameElement | null>(null);

// 递归查找对象中的 htmlContent 属性
const findHtmlContent = (obj) => {
  if (!obj || typeof obj !== 'object') return null;

  // 直接检查当前对象是否有 htmlContent 属性
  if ('htmlContent' in obj && typeof obj.htmlContent === 'string') {
    return obj.htmlContent;
  }

  // 递归检查所有属性
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      const result = findHtmlContent(obj[key]);
      if (result) return result;
    }
  }

  return null;
};

const fetchSharedHtml = async () => {
  try {
    loading.value = true;
    // 添加指纹信息到请求头
    const fingerprint = globalStore.fingerprint;
    const headers = {
      'Fingerprint': fingerprint
    };
    console.log('[ShareView] 发送请求获取分享内容，指纹:', fingerprint);
    console.log('[ShareView] 分享代码:', shareCode.value);

    const response = await axios.get(`/api/share/getSharedHtml?shareCode=${shareCode.value}`, { headers });
    console.log('[ShareView] 响应状态:', response.status);
    console.log('[ShareView] 响应数据:', response.data);
    console.log('[ShareView] 响应数据结构:', {
      hasData: !!response.data.data,
      dataType: typeof response.data.data,
      dataKeys: response.data.data ? Object.keys(response.data.data) : [],
      htmlContentExists: response.data.data && response.data.data.htmlContent !== undefined,
      nestedDataExists: response.data.data && response.data.data.data !== undefined,
      nestedDataType: response.data.data && response.data.data.data ? typeof response.data.data.data : 'undefined',
      nestedDataKeys: response.data.data && response.data.data.data ? Object.keys(response.data.data.data) : []
    });

    // 打印完整的响应数据结构
    console.log('[ShareView] 完整响应数据 JSON:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      // 使用递归函数查找 HTML 内容
      const foundHtmlContent = findHtmlContent(response.data);

      if (foundHtmlContent) {
        console.log('[ShareView] 使用递归查找找到 HTML 内容');
        htmlContent.value = foundHtmlContent;
      } else if (response.data.data && typeof response.data.data === 'string') {
        // 可能直接返回 HTML 字符串
        console.log('[ShareView] 使用 data.data 字符串结构');
        htmlContent.value = response.data.data;
      } else {
        // 找不到 HTML 内容
        console.error('[ShareView] 响应数据中找不到 HTML 内容:', response.data);
        throw new Error('响应数据中找不到 HTML 内容');
      }

      console.log('[ShareView] 获取分享内容成功，长度:', htmlContent.value.length);
    } else {
      error.value = response.data.message || '获取分享内容失败';
      console.error('[ShareView] 获取分享内容失败，原因:', response.data.message);
    }
  } catch (err: any) {
    console.error('[ShareView] 获取分享内容失败:', err);
    console.error('[ShareView] 错误详情:', err.response?.data);
    error.value = err.response?.data?.message || '获取分享内容失败';
  } finally {
    loading.value = false;
  }
};

const goHome = () => {
  router.push('/chat');
};

onMounted(() => {
  console.log('[ShareView] 组件挂载，当前路径:', route.path);
  console.log('[ShareView] 分享代码参数:', route.params);
  console.log('[ShareView] 当前指纹:', globalStore.fingerprint);
  console.log('[ShareView] 完整URL:', window.location.href);

  // 如果使用哈希模式，从 URL 中提取分享代码
  if (!shareCode.value) {
    const hashParts = window.location.hash.split('/');
    if (hashParts.length > 2) {
      shareCode.value = hashParts[hashParts.length - 1];
      console.log('[ShareView] 从哈希中提取分享代码:', shareCode.value);
    }
  }

  if (!shareCode.value) {
    console.error('[ShareView] 分享代码无效');
    error.value = '分享代码无效';
    loading.value = false;
    return;
  }

  fetchSharedHtml();
});
</script>

<style scoped>
.share-view {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  text-align: center;
  max-width: 400px;
}

.error-icon {
  font-size: 48px;
  color: #f5222d;
}

.content-frame {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
