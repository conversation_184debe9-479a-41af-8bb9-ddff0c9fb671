.markdown-body {
	background-color: transparent;
	font-size: 1rem;
  max-width: min(80vw, 58rem);

	p {
		white-space: pre-wrap;
    // margin-right: 5rem;
	}

	ol {
		list-style-type: decimal;
	}

	ul {
		list-style-type: disc;
	}

	pre code,
	pre tt {
		line-height: 1.65;
	}

	.highlight pre,
	pre {
		background-color:#1f2937 ;
	}

	code.hljs {
		padding: 0;
	}

	.code-block {

		&-wrapper {
			position: relative;
			padding-top: 2rem;
      // margin-right: 1rem
        max-width: min(80vw, 58rem);
      // max-width: 80vw;
      /* 隐藏滚动条的样式 */
        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* Internet Explorer 10+ */

        &::-webkit-scrollbar {
          display: none;
          /* Safari 和 Chrome */
        }
		}

		&-header {
			position: absolute;
			top: 5px;
			right: 0;
			width: 100%;
			padding: 0 1rem;
			display: flex;
			// justify-content: flex-end;
      justify-content: space-between;
			align-items: center;
			color: #b3b3b3;

			&__copy {
				cursor: pointer;
				margin-left: 0.5rem;
				user-select: none;

				&:hover {
					color: #65a665;
				}
			}
		}
	}

}

.whitespace-pre-wrap{
  // background-color: transparent;
  font-size: 1rem;
}
// html.dark {

// 	.message-reply {
// 		.whitespace-pre-wrap {
// 			white-space: pre-wrap;
// 			color: var(--n-text-color);
// 		}
// 	}

// 	.highlight pre,
// 	pre {
// 		background-color: #282c34;
// 	}
// }
