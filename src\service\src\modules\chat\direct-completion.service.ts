import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import axios, { AxiosRequestConfig } from 'axios';
import { formatUrl } from '@/common/utils';
import { DirectCompletionDto } from './dto/directCompletion.dto';
import { ModelsService } from '../models/models.service';
import { UserService } from '../user/user.service';
import { UserBalanceService } from '../userBalance/userBalance.service';
import { GlobalConfigService } from '../globalConfig/globalConfig.service';

@Injectable()
export class DirectCompletionService {
  constructor(
    private readonly modelsService: ModelsService,
    private readonly userService: UserService,
    private readonly userBalanceService: UserBalanceService,
    private readonly globalConfigService: GlobalConfigService
  ) {}

  /**
   * 直接使用消息数组进行对话生成
   * @param body 请求体，包含消息数组、模型和温度等参数
   * @param req 请求对象
   * @returns 生成的内容
   */
  async directCompletion(body: DirectCompletionDto, req: Request) {
    try {
      // 检查用户状态
      await this.userService.checkUserStatus(req.user);
      await this.userBalanceService.checkUserCertification(req.user.id);

      const { messages, model, temperature = 0.3, stream = false } = body;

      // 获取模型配置
      const currentRequestModelKey = await this.modelsService.getCurrentModelKeyInfo(
        model || 'gpt-3.5-turbo'
      );

      if (!currentRequestModelKey) {
        throw new HttpException('未找到指定的模型', HttpStatus.BAD_REQUEST);
      }

      // 获取模型配置
      const {
        key,
        proxyUrl,
        deduct,
        deductType,
        timeout,
        model: useModel,
      } = currentRequestModelKey;

      // 获取全局配置
      const { openaiBaseUrl, openaiBaseKey, openaiTimeout } =
        await this.globalConfigService.getConfigs([
          'openaiBaseUrl',
          'openaiBaseKey',
          'openaiTimeout',
        ]);

      // 检测用户余额
      await this.userBalanceService.validateBalance(req, deductType, deduct);

      // 准备请求参数
      const modelKey = key || openaiBaseKey;
      const proxyResUrl = formatUrl(
        proxyUrl || openaiBaseUrl || 'https://api.openai.com'
      );
      const modelTimeout = (timeout || openaiTimeout || 300) * 1000;

      // 记录请求日志
      Logger.log(
        `直接调用模型API，模型: ${useModel}, 温度: ${temperature}`,
        'DirectCompletionService'
      );

      // 构建请求选项
      const options: AxiosRequestConfig = {
        method: 'POST',
        url: `${proxyResUrl}/v1/chat/completions`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${modelKey}`,
        },
        timeout: modelTimeout,
        data: {
          model: useModel,
          messages,
          temperature,
          stream,
        },
      };

      // 发送请求
      const response = await axios(options);

      // 扣除用户余额
      await this.userBalanceService.deductFromBalance(
        req.user.id,
        deductType,
        deduct
      );

      // 返回结果
      if (stream) {
        return response.data;
      } else {
        return response.data.choices[0].message.content;
      }
    } catch (error) {
      Logger.error(
        `直接调用模型API失败: ${error.message}`,
        error.stack,
        'DirectCompletionService'
      );
      throw new HttpException(
        `调用模型API失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
