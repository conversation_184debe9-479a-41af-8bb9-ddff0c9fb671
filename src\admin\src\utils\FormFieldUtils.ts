/**
 * 表单字段工具类
 * 用于处理表单字段的创建和验证
 */
import { FormField } from '@/services/FormService';

/**
 * 创建输入框字段
 * @param name 字段名称
 * @param label 字段标签
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 输入框字段
 */
export function createInputField(
  name: string,
  label: string,
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'input',
    name,
    label,
    placeholder: placeholder || `请输入${label}`,
    required,
  };
}

/**
 * 创建文本域字段
 * @param name 字段名称
 * @param label 字段标签
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 文本域字段
 */
export function createTextareaField(
  name: string,
  label: string,
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'textarea',
    name,
    label,
    placeholder: placeholder || `请输入${label}`,
    required,
    rows: 4,
  };
}

/**
 * 创建选择器字段
 * @param name 字段名称
 * @param label 字段标签
 * @param options 选项
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 选择器字段
 */
export function createSelectField(
  name: string,
  label: string,
  options: Array<{ label: string; value: any }> | Array<string>,
  placeholder: string = '',
  required: boolean = false
): FormField {
  // 如果选项是字符串数组，转换为对象数组
  const formattedOptions = options.map(option => {
    if (typeof option === 'string') {
      return { label: option, value: option };
    }
    return option;
  });

  return {
    type: 'select',
    name,
    label,
    placeholder: placeholder || `请选择${label}`,
    required,
    options: formattedOptions,
  };
}

/**
 * 创建单选框字段
 * @param name 字段名称
 * @param label 字段标签
 * @param options 选项
 * @param required 是否必填
 * @returns 单选框字段
 */
export function createRadioField(
  name: string,
  label: string,
  options: Array<{ label: string; value: any }> | Array<string>,
  required: boolean = false
): FormField {
  // 如果选项是字符串数组，转换为对象数组
  const formattedOptions = options.map(option => {
    if (typeof option === 'string') {
      return { label: option, value: option };
    }
    return option;
  });

  return {
    type: 'radio',
    name,
    label,
    required,
    options: formattedOptions,
  };
}

/**
 * 创建复选框字段
 * @param name 字段名称
 * @param label 字段标签
 * @param options 选项
 * @param required 是否必填
 * @returns 复选框字段
 */
export function createCheckboxField(
  name: string,
  label: string,
  options: Array<{ label: string; value: any }> | Array<string>,
  required: boolean = false
): FormField {
  // 如果选项是字符串数组，转换为对象数组
  const formattedOptions = options.map(option => {
    if (typeof option === 'string') {
      return { label: option, value: option };
    }
    return option;
  });

  return {
    type: 'checkbox',
    name,
    label,
    required,
    options: formattedOptions,
  };
}

/**
 * 创建开关字段
 * @param name 字段名称
 * @param label 字段标签
 * @param activeText 开启文本
 * @param inactiveText 关闭文本
 * @returns 开关字段
 */
export function createSwitchField(
  name: string,
  label: string,
  activeText: string = '是',
  inactiveText: string = '否'
): FormField {
  return {
    type: 'switch',
    name,
    label,
    activeText,
    inactiveText,
  };
}

/**
 * 创建滑块字段
 * @param name 字段名称
 * @param label 字段标签
 * @param min 最小值
 * @param max 最大值
 * @param step 步长
 * @returns 滑块字段
 */
export function createSliderField(
  name: string,
  label: string,
  min: number = 0,
  max: number = 100,
  step: number = 1
): FormField {
  return {
    type: 'slider',
    name,
    label,
    min,
    max,
    step,
  };
}

/**
 * 创建日期字段
 * @param name 字段名称
 * @param label 字段标签
 * @param format 日期格式
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 日期字段
 */
export function createDateField(
  name: string,
  label: string,
  format: string = 'YYYY-MM-DD',
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'date',
    name,
    label,
    format,
    placeholder: placeholder || `请选择${label}`,
    required,
  };
}

/**
 * 创建时间字段
 * @param name 字段名称
 * @param label 字段标签
 * @param format 时间格式
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 时间字段
 */
export function createTimeField(
  name: string,
  label: string,
  format: string = 'HH:mm:ss',
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'time',
    name,
    label,
    format,
    placeholder: placeholder || `请选择${label}`,
    required,
  };
}

/**
 * 创建日期时间字段
 * @param name 字段名称
 * @param label 字段标签
 * @param format 日期时间格式
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 日期时间字段
 */
export function createDatetimeField(
  name: string,
  label: string,
  format: string = 'YYYY-MM-DD HH:mm:ss',
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'datetime',
    name,
    label,
    format,
    placeholder: placeholder || `请选择${label}`,
    required,
  };
}

/**
 * 创建评分字段
 * @param name 字段名称
 * @param label 字段标签
 * @param max 最大值
 * @returns 评分字段
 */
export function createRateField(
  name: string,
  label: string,
  max: number = 5
): FormField {
  return {
    type: 'rate',
    name,
    label,
    max,
  };
}

/**
 * 创建颜色选择器字段
 * @param name 字段名称
 * @param label 字段标签
 * @returns 颜色选择器字段
 */
export function createColorField(
  name: string,
  label: string
): FormField {
  return {
    type: 'color',
    name,
    label,
  };
}

/**
 * 创建数字输入框字段
 * @param name 字段名称
 * @param label 字段标签
 * @param min 最小值
 * @param max 最大值
 * @param step 步长
 * @param placeholder 占位文本
 * @param required 是否必填
 * @returns 数字输入框字段
 */
export function createNumberField(
  name: string,
  label: string,
  min?: number,
  max?: number,
  step: number = 1,
  placeholder: string = '',
  required: boolean = false
): FormField {
  return {
    type: 'number',
    name,
    label,
    min,
    max,
    step,
    placeholder: placeholder || `请输入${label}`,
    required,
  };
}

/**
 * 根据字段类型创建字段
 * @param type 字段类型
 * @param name 字段名称
 * @param label 字段标签
 * @returns 字段
 */
export function createFieldByType(
  type: string,
  name: string,
  label: string
): FormField {
  switch (type) {
    case 'input':
      return createInputField(name, label);
    case 'textarea':
      return createTextareaField(name, label);
    case 'select':
      return createSelectField(name, label, []);
    case 'radio':
      return createRadioField(name, label, []);
    case 'checkbox':
      return createCheckboxField(name, label, []);
    case 'switch':
      return createSwitchField(name, label);
    case 'slider':
      return createSliderField(name, label);
    case 'date':
      return createDateField(name, label);
    case 'time':
      return createTimeField(name, label);
    case 'datetime':
      return createDatetimeField(name, label);
    case 'rate':
      return createRateField(name, label);
    case 'color':
      return createColorField(name, label);
    case 'number':
      return createNumberField(name, label);
    default:
      return createInputField(name, label);
  }
}

/**
 * 获取字段类型列表
 * @returns 字段类型列表
 */
export function getFieldTypes(): Array<{ label: string; value: string }> {
  return [
    { label: '输入框', value: 'input' },
    { label: '文本域', value: 'textarea' },
    { label: '选择器', value: 'select' },
    { label: '单选框', value: 'radio' },
    { label: '复选框', value: 'checkbox' },
    { label: '开关', value: 'switch' },
    { label: '滑块', value: 'slider' },
    { label: '日期', value: 'date' },
    { label: '时间', value: 'time' },
    { label: '日期时间', value: 'datetime' },
    { label: '评分', value: 'rate' },
    { label: '颜色选择器', value: 'color' },
    { label: '数字输入框', value: 'number' },
  ];
}

/**
 * 获取字段类型标签
 * @param type 字段类型
 * @returns 字段类型标签
 */
export function getFieldTypeLabel(type: string): string {
  const fieldType = getFieldTypes().find(item => item.value === type);
  return fieldType ? fieldType.label : '未知';
}

/**
 * 获取字段类型标签类型
 * @param type 字段类型
 * @returns 字段类型标签类型
 */
export function getFieldTypeTagType(type: string): string {
  switch (type) {
    case 'input':
      return 'primary';
    case 'textarea':
      return 'primary';
    case 'select':
      return 'success';
    case 'radio':
      return 'success';
    case 'checkbox':
      return 'success';
    case 'switch':
      return 'warning';
    case 'slider':
      return 'warning';
    case 'date':
      return 'info';
    case 'time':
      return 'info';
    case 'datetime':
      return 'info';
    case 'rate':
      return 'danger';
    case 'color':
      return 'danger';
    case 'number':
      return 'primary';
    default:
      return 'info';
  }
}

export default {
  createInputField,
  createTextareaField,
  createSelectField,
  createRadioField,
  createCheckboxField,
  createSwitchField,
  createSliderField,
  createDateField,
  createTimeField,
  createDatetimeField,
  createRateField,
  createColorField,
  createNumberField,
  createFieldByType,
  getFieldTypes,
  getFieldTypeLabel,
  getFieldTypeTagType,
};
