<script setup lang="ts">
import { NButton, NIcon } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const { isMobile } = useBasicLayout();
const router = useRouter();

const emit = defineEmits(['create']);

const handleCreateClick = () => {
  emit('create');
};

const scrollToWorks = () => {
  const worksSection = document.querySelector('.works-section');
  if (worksSection) {
    worksSection.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>

<template>
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="text-gradient">AI创造力</span>
          <br />激发孩子的想象
        </h1>
        <p class="hero-description">
          DeepCreate为中小学生提供AI绘本创作和编程学习平台，
          <br />让孩子在数字世界中探索无限可能
        </p>
        <div class="hero-buttons">
          <NButton type="primary" size="large" class="get-started-btn" @click="handleCreateClick">
            开始创作
          </NButton>
          <NButton size="large" class="learn-more-btn" @click="scrollToWorks">
            探索作品
          </NButton>
        </div>
      </div>
      <div class="hero-image">
        <img src="@/assets/images/hero-illustration.svg" alt="孩子们在创作" class="main-illustration" />
        <div class="floating-element element-1">
          <SvgIcon name="ri:book-open-line" size="48" color="#6366F1" />
        </div>
        <div class="floating-element element-2">
          <SvgIcon name="ri:music-line" size="48" color="#EC4899" />
        </div>
        <div class="floating-element element-3">
          <SvgIcon name="ri:code-box-line" size="48" color="#10B981" />
        </div>
      </div>
    </div>
    <div class="scroll-indicator" @click="scrollToWorks">
      <SvgIcon name="ri:arrow-down-line" size="24" class="bounce" />
    </div>
  </section>
</template>

<style scoped>
.hero-section {
  position: relative;
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4rem 1.5rem;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  overflow: hidden;
}

.dark .hero-section {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.hero-content {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  align-items: center;
  gap: 2rem;
}

.hero-text {
  flex: 1;
  z-index: 10;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.text-gradient {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--color-muted);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.get-started-btn {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border: none;
  font-weight: 600;
}

.learn-more-btn {
  background-color: white;
  color: #3b82f6;
  border: 1px solid #e5e7eb;
  font-weight: 600;
}

.dark .learn-more-btn {
  background-color: #1e293b;
  color: #93c5fd;
  border: 1px solid #374151;
}

.hero-image {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-illustration {
  width: 100%;
  max-width: 500px;
  z-index: 1;
}

.floating-element {
  position: absolute;
  background-color: white;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  padding: 1rem;
  z-index: 2;
  animation: float 6s ease-in-out infinite;
}

.dark .floating-element {
  background-color: #1e293b;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

.element-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.element-3 {
  bottom: 15%;
  left: 20%;
  animation-delay: 4s;
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 10;
}

.bounce {
  animation: bounce 2s infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .floating-element {
    display: none;
  }
}
</style>
