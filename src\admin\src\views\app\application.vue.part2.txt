
要求：
1. 生成的SQL必须包含应用和对应的表单
2. 应用图标必须使用emoji格式，如'emoji:📝'
3. 表单字段必须与预设提示词中的变量对应
4. 必须设置public=0和isSystemReserved=0确保应用在前端正确显示
5. 必须使用LAST_INSERT_ID()获取应用ID并关联表单
6. 生成的SQL必须可以直接执行，不需要修改
7. 每个语句结尾必须有分号
8. 必须使用USE chatgpt;指定数据库

正确的SQL输出示例：
```sql
USE chatgpt;

-- 确保应用分类存在
INSERT INTO app_cats (name, `order`, status)
SELECT '教育辅助', 180, 1
FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM app_cats WHERE name = '教育辅助');

-- 创建应用
INSERT INTO app (name, catId, des, preset, coverImg, `order`, status, demoData, role, isGPTs, isFixedModel, appModel, gizmoID, public, isSystemReserved)
VALUES (
  '教案生成助手',
  (SELECT id FROM app_cats WHERE name = '教育辅助' LIMIT 1),
  '根据教学目标和课程内容自动生成详细教案',
  '你是一位教育专家，请根据以下信息生成教案：\\n课题：${topic}\\n年级：${grade}',
  'emoji:📝',
  180,
  1,
  '帮我设计一节小学三年级数学课\\n请为高中语文课设计教案',
  'system',
  0,
  0,
  '',
  '',
  0,
  0
);

-- 获取刚插入的应用ID
SET @app_id = LAST_INSERT_ID();

-- 为应用创建表单
INSERT INTO app_form (name, description, appId, fields, `order`, status)
VALUES (
  '教案设计表单',
  '请填写教案所需的基本信息',
  @app_id,
  '[{"type":"input","label":"课题","required":true,"placeholder":"请输入课题名称","key":"topic"},{"type":"input","label":"年级","required":true,"placeholder":"请输入年级","key":"grade"}]',
  100,
  1
);
```

请生成完整的SQL语句，包含创建应用分类、应用和表单的所有必要语句。只返回SQL语句，不要有其他解释或标记。确保生成的SQL与示例格式一致，包含所有必要的字段和正确的语法。`;
