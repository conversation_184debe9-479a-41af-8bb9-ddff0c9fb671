<script setup lang="ts">
import { fetchFormsByAppIdAPI } from '@/api/form';
import { fetchQueryOneCatAPI } from '@/api/appStore';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { ref, onMounted, inject, watch } from 'vue';
import { useRoute } from 'vue-router';

const { isMobile } = useBasicLayout();
const route = useRoute();
const forms = ref([]);
const activeForm = ref(null);
const formData = ref({});
const loading = ref(false);
const formSubmitted = ref(false);
const appPreset = ref('');
const appInfo = ref({
  name: '',
  des: '',
  coverImg: ''
});

const props = defineProps<{
  appId?: number;
}>();

const onConversation = inject<any>('onConversation');

// 监听appId变化
watch(() => props.appId, (newAppId) => {
  if (newAppId) {
    fetchForms(newAppId);
  }
}, { immediate: true });

async function fetchForms(appId: number) {
  if (!appId) return;

  try {
    loading.value = true;

    // 获取应用信息，包括预设
    const appRes = await fetchQueryOneCatAPI({ id: appId });
    if (appRes.data) {
      // 保存应用预设
      if (appRes.data.preset) {
        appPreset.value = appRes.data.preset;
      }

      // 保存应用信息
      appInfo.value = {
        name: appRes.data.name || '',
        des: appRes.data.des || '',
        coverImg: appRes.data.coverImg || ''
      };
    }

    // 获取表单
    const res = await fetchFormsByAppIdAPI(appId);
    forms.value = res.data || [];

    // 如果有表单，默认选择第一个
    if (forms.value.length > 0) {
      activeForm.value = forms.value[0];
      // 初始化表单数据
      initFormData(activeForm.value);
    }
    loading.value = false;
  } catch (error) {
    console.error('获取表单失败', error);
    loading.value = false;
  }
}

function initFormData(form) {
  if (!form || !form.fields) return;

  try {
    const fields = JSON.parse(form.fields);
    const data = {};

    fields.forEach(field => {
      data[field.name || field.label] = '';
    });

    formData.value = data;
  } catch (error) {
    console.error('解析表单字段失败', error);
  }
}

function selectForm(form) {
  activeForm.value = form;
  initFormData(form);
  formSubmitted.value = false;
}

function handleSubmit() {
  if (!activeForm.value) return;

  try {
    // 验证必填字段
    const fields = JSON.parse(activeForm.value.fields);
    let isValid = true;
    let missingFields = [];

    fields.forEach(field => {
      if (field.required && !formData.value[field.name || field.label]) {
        isValid = false;
        missingFields.push(field.label);
      }
    });

    if (!isValid) {
      alert(`请填写必填字段: ${missingFields.join(', ')}`);
      return;
    }

    // 构建提示词
    let prompt = '';

    // 如果有预设，使用预设替换变量
    if (appPreset.value) {
      prompt = appPreset.value;

      // 替换预设中的变量
      Object.entries(formData.value).forEach(([key, value]) => {
        // 获取字段定义
        const fields = JSON.parse(activeForm.value.fields);
        const field = fields.find(f => (f.name || f.label) === key);

        // 如果是单选或多选字段，需要将值转换为标签
        let displayValue = value;
        if (field && ['radio', 'select', 'checkbox'].includes(field.type) && field.options && value) {
          // 单个值的情况
          if (typeof value === 'string') {
            const option = field.options.find(opt =>
              (opt.value || opt) === value
            );
            if (option) {
              displayValue = option.label || option;
            }
          }
          // 多个值的情况（复选框）
          else if (Array.isArray(value)) {
            displayValue = value.map(v => {
              const option = field.options.find(opt =>
                (opt.value || opt) === v
              );
              return option ? (option.label || option) : v;
            }).join(', ');
          }
        }

        // 使用正则表达式替换所有匹配的变量
        const regex = new RegExp(`\\$\\{${key}\\}`, 'g');
        prompt = prompt.replace(regex, displayValue as string);
      });

      // 检查是否还有未替换的变量
      const remainingVars = prompt.match(/\$\{[^\}]+\}/g);
      if (remainingVars) {
        // 对于未替换的变量，添加到提示词末尾
        prompt += '\n\n其他信息:\n';
        remainingVars.forEach(variable => {
          const varName = variable.replace(/\$\{|\}/g, '');
          prompt += `${varName}: 未提供\n`;
        });
      }
    } else {
      // 如果没有预设，使用默认格式
      prompt = `基于以下表单信息，请帮我完成任务：\n\n`;
      prompt += `表单名称: ${activeForm.value.name}\n`;

      // 获取字段定义
      const fields = JSON.parse(activeForm.value.fields);

      Object.entries(formData.value).forEach(([key, value]) => {
        // 获取字段定义
        const field = fields.find(f => (f.name || f.label) === key);

        // 如果是单选或多选字段，需要将值转换为标签
        let displayValue = value;
        if (field && ['radio', 'select', 'checkbox'].includes(field.type) && field.options && value) {
          // 单个值的情况
          if (typeof value === 'string') {
            const option = field.options.find(opt =>
              (opt.value || opt) === value
            );
            if (option) {
              displayValue = option.label || option;
            }
          }
          // 多个值的情况（复选框）
          else if (Array.isArray(value)) {
            displayValue = value.map(v => {
              const option = field.options.find(opt =>
                (opt.value || opt) === v
              );
              return option ? (option.label || option) : v;
            }).join(', ');
          }
        }

        // 使用字段标签而不是字段名
        const fieldLabel = field ? field.label : key;
        prompt += `${fieldLabel}: ${displayValue}\n`;
      });
    }

    // 发送到对话
    onConversation({
      msg: prompt,
      appId: props.appId
    });

    formSubmitted.value = true;
  } catch (error) {
    console.error('提交表单失败', error);
  }
}

onMounted(() => {
  const appId = props.appId || Number(route.query.appId);
  if (appId) {
    fetchForms(appId);
  }
});
</script>

<template>
  <div class="form-message-container">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载表单中...</p>
    </div>

    <div v-else-if="forms.length === 0" class="empty-container">
      <p>该应用没有预定义表单</p>
    </div>

    <div v-else class="form-content">
      <!-- 移除应用标题和描述，避免重复显示 -->

      <div v-if="forms.length > 1" class="form-tabs">
        <div
          v-for="form in forms"
          :key="form.id"
          class="form-tab"
          :class="{ active: activeForm && activeForm.id === form.id }"
          @click="selectForm(form)"
        >
          {{ form.name }}
        </div>
      </div>

      <div v-if="activeForm && !formSubmitted" class="form-body form-content-wrapper">
        <p v-if="activeForm.description" class="form-description">{{ activeForm.description }}</p>

        <div class="form-fields">
          <template v-if="activeForm.fields">
            <div
              v-for="(field, index) in JSON.parse(activeForm.fields)"
              :key="index"
              class="form-field"
            >
              <label :class="{ required: field.required }">{{ field.label }}</label>

              <template v-if="field.type === 'input'">
                <input
                  v-model="formData[field.name || field.label]"
                  type="text"
                  :placeholder="field.placeholder || ''"
                />
              </template>

              <template v-else-if="field.type === 'textarea'">
                <textarea
                  v-model="formData[field.name || field.label]"
                  :placeholder="field.placeholder || ''"
                  rows="4"
                ></textarea>
              </template>

              <template v-else-if="field.type === 'select'">
                <select v-model="formData[field.name || field.label]">
                  <option value="" disabled>请选择</option>
                  <option
                    v-for="(option, optIndex) in field.options"
                    :key="optIndex"
                    :value="option.value || option"
                  >
                    {{ option.label || option }}
                  </option>
                </select>
              </template>

              <template v-else-if="field.type === 'radio'">
                <div class="radio-group">
                  <div
                    v-for="(option, optIndex) in field.options"
                    :key="optIndex"
                    class="radio-option"
                  >
                    <input
                      type="radio"
                      :id="`radio-${index}-${optIndex}`"
                      :name="`radio-${index}`"
                      :value="option.value || option"
                      v-model="formData[field.name || field.label]"
                    />
                    <label :for="`radio-${index}-${optIndex}`">{{ option.label || option }}</label>
                  </div>
                </div>
              </template>

              <template v-else-if="field.type === 'checkbox'">
                <div class="checkbox-group">
                  <div
                    v-for="(option, optIndex) in field.options"
                    :key="optIndex"
                    class="checkbox-option"
                  >
                    <input
                      type="checkbox"
                      :id="`checkbox-${index}-${optIndex}`"
                      :value="option.value || option"
                      v-model="formData[field.name || field.label]"
                    />
                    <label :for="`checkbox-${index}-${optIndex}`">{{ option.label || option }}</label>
                  </div>
                </div>
              </template>

              <template v-else>
                <input
                  v-model="formData[field.name || field.label]"
                  type="text"
                  :placeholder="field.placeholder || ''"
                />
              </template>
            </div>
          </template>
        </div>

        <div class="form-actions">
          <button class="submit-button" @click="handleSubmit">提交表单</button>
        </div>
      </div>

      <div v-else-if="formSubmitted" class="form-submitted">
        <!-- 移除应用标题和描述，避免重复显示 -->
        <div class="success-icon">✓</div>
        <h3>表单已提交</h3>
        <p>AI正在根据您提供的信息生成回复...</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.form-message-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto; /* 移除上下边距，避免与AppTips组件产生过大间距 */
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10; /* 确保表单内容不被遗漏 */
  /* 移除overflow属性，让内容自然流动 */
  /* 确保表单容器不会被上下元素遮挡 */
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
  overflow-x: auto;
}

.form-tab {
  padding: 10px 15px;
  cursor: pointer;
  white-space: nowrap;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.form-tab.active {
  border-bottom-color: #3498db;
  color: #3498db;
  font-weight: 500;
}

.form-title {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #333;
}

.form-description {
  margin-bottom: 20px;
  color: #666;
}

.form-content-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 5px 0;
}

/* 应用信息样式 */
.app-info {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.5s ease-out;
}

.app-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.app-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.app-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.app-avatar-placeholder {
  background-image: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.app-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: #333;
  margin: 0;
  letter-spacing: -0.01em;
}

.app-description {
  color: #666;
  line-height: 1.6;
  margin-top: 8px;
  font-size: 1.05rem;
}



.form-fields {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  /* 移除最大高度和滚动限制，让表单完全展开 */
  padding-right: 5px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-field label.required::after {
  content: '*';
  color: #e74c3c;
  margin-left: 4px;
}

.form-field input,
.form-field textarea,
.form-field select {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.form-field input:focus,
.form-field textarea:focus,
.form-field select:focus {
  border-color: #3498db;
  outline: none;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-option,
.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.submit-button {
  padding: 12px 24px;
  background-color: #3498db;
  background-image: linear-gradient(to right, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
  position: relative;
  overflow: hidden;
  width: auto;
  min-width: 150px;
}

.submit-button:hover {
  background-image: linear-gradient(to right, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.4);
}

.submit-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
}

.form-submitted {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  padding: 20px;
  transition: all 0.5s ease-out;
}

.app-info-submitted {
  width: 100%;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  text-align: left;
}

.success-icon {
  width: 70px;
  height: 70px;
  background-image: linear-gradient(135deg, #2ecc71, #27ae60);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-bottom: 24px;
  box-shadow: 0 6px 16px rgba(46, 204, 113, 0.3);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: scale(1);
}

.form-submitted h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 12px 0;
}

.form-submitted p {
  font-size: 1.1rem;
  color: #666;
  max-width: 80%;
  line-height: 1.6;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .form-message-container {
    padding: 16px;
    margin: 0 auto; /* 移除上边距，避免重复 */
    border-radius: 10px;
  }

  .form-title {
    font-size: 1.3rem;
  }

  .app-title {
    font-size: 1.4rem;
  }

  .app-avatar {
    width: 42px;
    height: 42px;
  }

  .app-description {
    font-size: 0.95rem;
  }

  .form-tab {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .submit-button {
    width: 100%;
    padding: 12px 16px;
  }

  .form-fields {
    /* 移除高度限制，让表单完全展开 */
    gap: 12px;
  }

  .form-field label {
    font-size: 0.95rem;
    margin-bottom: 4px;
  }

  .form-field input,
  .form-field textarea,
  .form-field select {
    padding: 10px;
    font-size: 0.95rem;
  }

  .success-icon {
    width: 60px;
    height: 60px;
    font-size: 28px;
    margin-bottom: 16px;
  }

  .form-submitted h3 {
    font-size: 1.3rem;
  }

  .form-submitted p {
    font-size: 1rem;
    max-width: 90%;
  }
}

/* 深色模式 */
:deep(.dark) .form-message-container {
  background-color: #1f1f1f;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.05);
  margin: 0 auto; /* 确保深色模式下也没有上边距 */
}

:deep(.dark) .form-title,
:deep(.dark) .app-title {
  color: #e0e0e0;
}

:deep(.dark) .form-description,
:deep(.dark) .app-description,
:deep(.dark) .empty-container,
:deep(.dark) .loading-container {
  color: #aaa;
}

:deep(.dark) .app-info,
:deep(.dark) .app-info-submitted {
  border-bottom-color: #333;
}

:deep(.dark) .form-field label {
  color: #e0e0e0;
}

:deep(.dark) .form-field input,
:deep(.dark) .form-field textarea,
:deep(.dark) .form-field select {
  background-color: #2d2d2d;
  border-color: #444;
  color: #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

:deep(.dark) .form-field input:focus,
:deep(.dark) .form-field textarea:focus,
:deep(.dark) .form-field select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

:deep(.dark) .form-tab.active {
  color: #5dade2;
}

:deep(.dark) .submit-button {
  background-image: linear-gradient(to right, #3498db, #2574a9);
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
}

:deep(.dark) .submit-button:hover {
  background-image: linear-gradient(to right, #2574a9, #3498db);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
}

:deep(.dark) .success-icon {
  background-image: linear-gradient(135deg, #27ae60, #219955);
  box-shadow: 0 6px 16px rgba(39, 174, 96, 0.2);
}

:deep(.dark) .form-submitted h3 {
  color: #e0e0e0;
}

:deep(.dark) .form-submitted p {
  color: #aaa;
}

/* 移除深色模式下的滚动条样式，因为我们已经移除了表单字段的滚动条 */
</style>
