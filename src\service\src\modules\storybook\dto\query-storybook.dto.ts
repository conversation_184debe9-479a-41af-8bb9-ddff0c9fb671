import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsArray, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * 查询绘本DTO
 */
export class QueryStorybookDto {
  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  size?: number;

  @ApiProperty({ description: '关键词搜索', required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: '状态(0:草稿,1:已发布,2:审核中,3:已拒绝)', required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  status?: number;

  @ApiProperty({ description: '是否公开(0:私有,1:公开)', required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  isPublic?: number;

  @ApiProperty({ description: '是否推荐(0:否,1:是)', required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  isRecommended?: number;

  @ApiProperty({ description: '所属文件夹ID', required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  folderId?: number;

  @ApiProperty({ description: '是否在回收站', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  isDeleted?: number;

  @ApiProperty({ description: '排序字段', required: false, default: 'updatedAt' })
  @IsOptional()
  @IsString()
  sortField?: string;

  @ApiProperty({ description: '排序方向', required: false, default: 'DESC' })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';

  @ApiProperty({ description: '是否只查询最近编辑的作品', required: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  recentOnly?: boolean;
}

/**
 * 批量操作绘本DTO
 */
export class BatchOperationDto {
  @ApiProperty({ description: '绘本ID数组' })
  @IsNotEmpty({ message: '绘本ID数组不能为空' })
  @IsArray()
  storybookIds: number[];

  @ApiProperty({ description: '操作类型(delete:删除,restore:恢复,move:移动)', required: true })
  @IsNotEmpty({ message: '操作类型不能为空' })
  @IsString()
  operation: 'delete' | 'restore' | 'move';

  @ApiProperty({ description: '目标文件夹ID(移动操作时必填)', required: false })
  @IsOptional()
  @IsNumber()
  targetFolderId?: number;
}
