# 用户认证接口文档

## 认证方式

系统使用JWT (JSON Web Token) 进行用户认证。所有需要认证的API都需要在请求头中包含`Authorization`字段，格式为`Bearer {token}`。

### 获取Token

通过登录接口获取token后，需要在后续请求中添加到请求头：

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 接口列表

### 1. 用户登录

**接口地址**：`/auth/login`

**请求方式**：POST

**请求参数**：

```json
{
  "username": "用户名",
  "password": "密码"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| username | string | 是 | 用户名，长度2-30个字符 |
| password | string | 是 | 密码，长度6-30个字符 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "message": "请求成功"
}
```

### 2. 验证码登录

**接口地址**：`/auth/loginWithCaptcha`

**请求方式**：POST

**请求参数**：

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

或

```json
{
  "phone": "13800138000",
  "code": "123456"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| email | string | 二选一 | 用户邮箱 |
| phone | string | 二选一 | 用户手机号 |
| code | string | 是 | 验证码 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "message": "请求成功"
}
```

### 3. 用户注册

**接口地址**：`/auth/register`

**请求方式**：POST

**请求参数**：

```json
{
  "username": "用户名",
  "password": "密码",
  "email": "<EMAIL>"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| username | string | 是 | 用户名，长度2-30个字符 |
| password | string | 是 | 密码，长度6-30个字符 |
| email | string | 是 | 邮箱地址 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "注册成功",
  "message": "请求成功"
}
```

### 4. 获取用户信息

**接口地址**：`/auth/info`

**请求方式**：GET

**请求头**：

```
Authorization: Bearer {token}
```

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": {
    "userInfo": {
      "username": "用户名",
      "avatar": "头像URL",
      "role": "用户角色",
      "email": "用户邮箱",
      "sign": "个性签名",
      "isBindWx": true,
      "consecutiveDays": 5,
      "id": "ABC123"
    },
    "userBalance": {
      "id": 1,
      "userId": 100,
      "model3Count": 100,
      "model4Count": 50,
      "drawMjCount": 20
    }
  },
  "message": "请求成功"
}
```

## 错误处理

当认证失败时，服务器会返回401状态码，响应内容如下：

```json
{
  "code": 401,
  "success": false,
  "message": "亲爱的用户,请登录后继续操作,我们正在等您的到来！"
}
```

其他可能的错误：

| 错误码 | 说明 |
| ------ | ---- |
| 400 | 请求参数错误 |
| 401 | 未授权或token已过期 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |
