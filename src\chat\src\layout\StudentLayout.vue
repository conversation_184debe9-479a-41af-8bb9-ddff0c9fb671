<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAppStore } from '@/store';
import { 
  NLayout, 
  NLayoutSider, 
  NLayoutHeader, 
  NLayoutContent,
  NButton,
  NIcon,
  NDrawer,
  NDrawerContent,
  NBadge
} from 'naive-ui';
import { MenuOutline, CreateOutline, BookOutline } from '@vicons/ionicons5';
import RoleBasedNav from './components/RoleBasedNav.vue';
import UserRoleIndicator from './components/UserRoleIndicator.vue';

const { isMobile } = useBasicLayout();
const appStore = useAppStore();

// 侧边栏状态管理
const siderCollapsed = computed({
  get: () => appStore.siderCollapsed,
  set: (value: boolean) => appStore.setSiderCollapsed(value)
});

// 移动端侧边栏显示控制
const mobileDrawerVisible = ref(false);

// 侧边栏宽度配置（学生版更窄，更适合触摸）
const siderWidth = computed(() => {
  return isMobile.value ? '100vw' : 240;
});

// 响应式类名计算
const getLayoutClass = computed(() => {
  if (isMobile.value) {
    return ['h-full', 'overflow-hidden'];
  }
  return ['h-full', 'student-layout'];
});

const getHeaderClass = computed(() => {
  return ['border-b', 'border-green-200', 'dark:border-green-700', 'bg-white', 'dark:bg-gray-900'];
});

const getContentClass = computed(() => {
  return ['bg-green-50', 'dark:bg-gray-800'];
});

// 切换侧边栏
const toggleSider = () => {
  if (isMobile.value) {
    mobileDrawerVisible.value = !mobileDrawerVisible.value;
  } else {
    siderCollapsed.value = !siderCollapsed.value;
  }
};

// 关闭移动端侧边栏
const closeMobileDrawer = () => {
  mobileDrawerVisible.value = false;
};

// 监听窗口大小变化
const handleResize = () => {
  if (!isMobile.value && mobileDrawerVisible.value) {
    mobileDrawerVisible.value = false;
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="student-layout-container h-full">
    <NLayout :class="getLayoutClass" has-sider position="absolute">
      
      <!-- 桌面端侧边栏 -->
      <NLayoutSider
        v-if="!isMobile"
        :collapsed="siderCollapsed"
        :collapsed-width="0"
        :width="siderWidth"
        :native-scrollbar="false"
        bordered
        class="student-sider"
        collapse-mode="width"
      >
        <RoleBasedNav />
      </NLayoutSider>

      <!-- 主内容区域 -->
      <NLayout>
        <!-- 顶部导航栏 - 学生版更友好 -->
        <NLayoutHeader :class="getHeaderClass" bordered>
          <div class="flex items-center justify-between h-16 px-4">
            <!-- 左侧：菜单按钮和可爱的品牌标识 -->
            <div class="flex items-center space-x-4">
              <NButton
                quaternary
                circle
                @click="toggleSider"
                class="flex-shrink-0 student-menu-btn"
                size="large"
              >
                <template #icon>
                  <NIcon :component="MenuOutline" size="20" />
                </template>
              </NButton>
              
              <div class="hidden sm:flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg">
                  <span class="text-white font-bold text-lg">✨</span>
                </div>
                <div>
                  <div class="font-bold text-gray-900 dark:text-gray-100 text-lg">
                    AI创作乐园
                  </div>
                  <div class="text-sm text-green-600 dark:text-green-400 font-medium">
                    🎨 我的创作空间
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间：快捷操作按钮（学生专用） -->
            <div class="hidden md:flex items-center space-x-2">
              <NButton
                type="success"
                size="medium"
                class="student-quick-btn"
                @click="$router.push('/storybook')"
              >
                <template #icon>
                  <NIcon :component="CreateOutline" />
                </template>
                开始创作
              </NButton>
              
              <NBadge :value="3" type="info">
                <NButton
                  quaternary
                  size="medium"
                  class="student-quick-btn"
                  @click="$router.push('/storybook/my-works')"
                >
                  <template #icon>
                    <NIcon :component="BookOutline" />
                  </template>
                  我的作品
                </NButton>
              </NBadge>
            </div>

            <!-- 右侧：用户信息 -->
            <UserRoleIndicator />
          </div>
        </NLayoutHeader>

        <!-- 主内容区 -->
        <NLayoutContent :class="getContentClass" content-style="height: 100%;">
          <div class="h-full relative">
            <RouterView v-slot="{ Component, route }">
              <component :is="Component" :key="route.fullPath" />
            </RouterView>
          </div>
        </NLayoutContent>
      </NLayout>
    </NLayout>

    <!-- 移动端侧边栏抽屉 - 学生版更可爱 -->
    <NDrawer
      v-model:show="mobileDrawerVisible"
      :width="280"
      placement="left"
      class="student-mobile-drawer"
    >
      <NDrawerContent closable @close="closeMobileDrawer">
        <template #header>
          <div class="flex items-center space-x-2">
            <span class="text-2xl">🎨</span>
            <span class="font-bold text-green-700 dark:text-green-300">我的创作空间</span>
          </div>
        </template>
        <RoleBasedNav />
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped lang="scss">
.student-layout-container {
  background-color: #f0fdf4;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.05) 0%, transparent 25%);
  
  .student-sider {
    box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    border-color: #bbf7d0;
    background: linear-gradient(180deg, #ffffff 0%, #f0fdf4 100%);
    
    :deep(.n-layout-sider-scroll-container) {
      display: flex;
      flex-direction: column;
    }
  }
}

// 深色模式基础
@media (prefers-color-scheme: dark) {
  .student-layout-container {
    background-color: #111827;
    
    .student-sider {
      background-color: #111827;
      border-color: #059669;
    }
  }
}

// 学生模式的主题样式 - 更活泼友好
.student-layout {
  --primary-color: #22c55e;
  --primary-hover: #16a34a;
  --primary-pressed: #15803d;
  
  :deep(.n-layout-header) {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  }
  
  :deep(.n-button--success-type) {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(34, 197, 94, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// 深色模式下的学生布局
@media (prefers-color-scheme: dark) {
  .student-layout {
    :deep(.n-layout-header) {
      background-color: #111827;
    }
  }
}

// 学生专用按钮样式
.student-menu-btn {
  background-color: #dcfce7;
  color: #15803d;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #bbf7d0;
    transform: scale(1.05);
  }
}

@media (prefers-color-scheme: dark) {
  .student-menu-btn {
    background-color: rgba(34, 197, 94, 0.3);
    color: #86efac;
    
    &:hover {
      background-color: rgba(34, 197, 94, 0.5);
    }
  }
}

.student-quick-btn {
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}

// 移动端优化 - 更大的触摸目标
@media (max-width: 768px) {
  .student-layout-container {
    :deep(.n-layout-header) {
      height: 64px;
      
      .flex {
        height: 64px;
      }
    }
    
    .student-menu-btn {
      width: 3rem;
      height: 3rem;
    }
  }
}

// 小屏幕优化
@media (max-width: 640px) {
  .student-layout-container {
    :deep(.n-layout-header) {
      padding-left: 1rem;
      padding-right: 1rem;
      
      .flex {
        gap: 0.75rem;
      }
    }
  }
}

// 深色模式优化
@media (prefers-color-scheme: dark) {
  .student-layout-container {
    .student-sider {
      background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
      border-color: #374151;
    }
  }
}

// 滚动条样式 - 更可爱的设计
.student-layout-container {
  :deep(.n-scrollbar-content) {
    height: 100%;
  }
  
  :deep(.n-scrollbar) {
    height: 100%;
  }
  
  :deep(.n-scrollbar-rail) {
    background-color: rgba(34, 197, 94, 0.1);
  }
  
  :deep(.n-scrollbar-handle) {
    background-color: rgba(34, 197, 94, 0.3);
    border-radius: 10px;
    
    &:hover {
      background-color: rgba(34, 197, 94, 0.5);
    }
  }
}

// 抽屉样式 - 学生版
.student-mobile-drawer {
  :deep(.n-drawer-body) {
    padding: 0;
    background: linear-gradient(180deg, #f0fdf4 0%, #ffffff 100%);
  }
  
  :deep(.n-drawer-header) {
    background-color: #dcfce7;
    border-bottom: 2px solid rgb(34, 197, 94, 0.2);
    
    .n-drawer-header__main {
      font-weight: 700;
    }
  }
  
  :deep(.n-drawer-header__close) {
    color: #059669;
    
    &:hover {
      color: #047857;
    }
  }
}

// 深色模式抽屉
@media (prefers-color-scheme: dark) {
  .student-mobile-drawer {
    :deep(.n-drawer-body) {
      background-color: #111827;
    }
    
    :deep(.n-drawer-header) {
      background-color: rgba(34, 197, 94, 0.3);
    }
    
    :deep(.n-drawer-header__close) {
      color: #86efac;
      
      &:hover {
        color: #bbf7d0;
      }
    }
  }
}

// 动画效果
@keyframes bounce-gentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.student-layout-container {
  .student-quick-btn {
    &:hover {
      animation: bounce-gentle 0.6s ease-in-out;
    }
  }
}

// 响应式字体大小
@media (min-width: 1024px) {
  .student-layout-container {
    :deep(.n-layout-header) {
      .font-bold {
        font-size: 1.25rem;
      }
    }
  }
}
</style> 