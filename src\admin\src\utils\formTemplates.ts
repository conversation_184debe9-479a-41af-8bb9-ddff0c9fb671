/**
 * 表单模板工具类
 * 提供常用的表单模板
 */
import { FormField } from '@/services/FormService';
import formFieldUtils from '@/utils/FormFieldUtils';

/**
 * 表单模板接口
 */
export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  fields: FormField[];
  category: string;
}

/**
 * 联系表单模板
 */
const contactFormTemplate: FormTemplate = {
  id: 'contact-form',
  name: '联系表单',
  description: '基础联系表单，包含姓名、邮箱、电话和留言',
  category: '基础',
  fields: [
    formFieldUtils.createInputField('name', '姓名', '请输入您的姓名', true),
    formFieldUtils.createInputField('email', '邮箱', '请输入您的邮箱地址', true),
    formFieldUtils.createInputField('phone', '电话', '请输入您的联系电话'),
    formFieldUtils.createTextareaField('message', '留言内容', '请输入您的留言内容', true)
  ]
};

/**
 * 用户注册表单模板
 */
const registrationFormTemplate: FormTemplate = {
  id: 'registration-form',
  name: '用户注册表单',
  description: '用户注册表单，包含用户名、密码、确认密码、邮箱等字段',
  category: '用户',
  fields: [
    formFieldUtils.createInputField('username', '用户名', '请输入用户名', true),
    {
      type: 'input',
      name: 'password',
      label: '密码',
      placeholder: '请输入密码',
      required: true,
      showPassword: true
    },
    {
      type: 'input',
      name: 'confirmPassword',
      label: '确认密码',
      placeholder: '请再次输入密码',
      required: true,
      showPassword: true
    },
    formFieldUtils.createInputField('email', '邮箱', '请输入邮箱地址', true),
    formFieldUtils.createRadioField('gender', '性别', [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' },
      { label: '其他', value: 'other' }
    ])
  ]
};

/**
 * 调查问卷表单模板
 */
const surveyFormTemplate: FormTemplate = {
  id: 'survey-form',
  name: '调查问卷表单',
  description: '基础调查问卷表单，包含单选、多选、评分等字段',
  category: '调查',
  fields: [
    formFieldUtils.createInputField('name', '姓名', '请输入您的姓名', true),
    formFieldUtils.createRadioField('age', '年龄段', [
      { label: '18岁以下', value: 'under18' },
      { label: '18-25岁', value: '18-25' },
      { label: '26-35岁', value: '26-35' },
      { label: '36-45岁', value: '36-45' },
      { label: '46岁以上', value: 'above46' }
    ], true),
    formFieldUtils.createCheckboxField('interests', '兴趣爱好', [
      { label: '阅读', value: 'reading' },
      { label: '音乐', value: 'music' },
      { label: '电影', value: 'movie' },
      { label: '运动', value: 'sports' },
      { label: '旅行', value: 'travel' },
      { label: '美食', value: 'food' }
    ]),
    formFieldUtils.createRateField('satisfaction', '满意度评分', 5),
    formFieldUtils.createTextareaField('suggestion', '建议', '请输入您的建议或意见')
  ]
};

/**
 * 产品反馈表单模板
 */
const productFeedbackFormTemplate: FormTemplate = {
  id: 'product-feedback-form',
  name: '产品反馈表单',
  description: '产品反馈表单，用于收集用户对产品的反馈',
  category: '反馈',
  fields: [
    formFieldUtils.createInputField('name', '姓名', '请输入您的姓名'),
    formFieldUtils.createInputField('email', '邮箱', '请输入您的邮箱地址', true),
    formFieldUtils.createSelectField('productType', '产品类型', [
      { label: '软件', value: 'software' },
      { label: '硬件', value: 'hardware' },
      { label: '服务', value: 'service' }
    ], '请选择产品类型', true),
    formFieldUtils.createRateField('usability', '易用性评分', 5),
    formFieldUtils.createRateField('performance', '性能评分', 5),
    formFieldUtils.createRateField('reliability', '可靠性评分', 5),
    formFieldUtils.createTextareaField('feedback', '反馈内容', '请详细描述您的反馈', true)
  ]
};

/**
 * AI提示词表单模板
 */
const aiPromptFormTemplate: FormTemplate = {
  id: 'ai-prompt-form',
  name: 'AI提示词表单',
  description: 'AI提示词表单，用于构建AI生成内容的提示词',
  category: 'AI',
  fields: [
    formFieldUtils.createSelectField('role', 'AI角色', [
      { label: '助手', value: 'assistant' },
      { label: '专家', value: 'expert' },
      { label: '教师', value: 'teacher' },
      { label: '作家', value: 'writer' },
      { label: '程序员', value: 'programmer' }
    ], '请选择AI角色', true),
    formFieldUtils.createInputField('topic', '主题', '请输入主题', true),
    formFieldUtils.createTextareaField('context', '上下文', '请提供相关上下文信息'),
    formFieldUtils.createSelectField('tone', '语气', [
      { label: '正式', value: 'formal' },
      { label: '友好', value: 'friendly' },
      { label: '专业', value: 'professional' },
      { label: '幽默', value: 'humorous' }
    ], '请选择语气'),
    formFieldUtils.createSelectField('format', '输出格式', [
      { label: '文本', value: 'text' },
      { label: 'Markdown', value: 'markdown' },
      { label: 'HTML', value: 'html' },
      { label: 'JSON', value: 'json' }
    ], '请选择输出格式'),
    formFieldUtils.createSliderField('length', '输出长度', 100, 1000, 100)
  ]
};

/**
 * 编程项目表单模板
 */
const programmingProjectFormTemplate: FormTemplate = {
  id: 'programming-project-form',
  name: '编程项目表单',
  description: '编程项目表单，用于收集编程项目的需求',
  category: '编程',
  fields: [
    formFieldUtils.createInputField('projectName', '项目名称', '请输入项目名称', true),
    formFieldUtils.createSelectField('projectType', '项目类型', [
      { label: 'Web应用', value: 'web' },
      { label: '移动应用', value: 'mobile' },
      { label: '桌面应用', value: 'desktop' },
      { label: 'API服务', value: 'api' },
      { label: '其他', value: 'other' }
    ], '请选择项目类型', true),
    formFieldUtils.createTextareaField('projectDescription', '项目描述', '请详细描述项目需求', true),
    formFieldUtils.createCheckboxField('technologies', '技术栈', [
      { label: 'JavaScript', value: 'javascript' },
      { label: 'TypeScript', value: 'typescript' },
      { label: 'React', value: 'react' },
      { label: 'Vue', value: 'vue' },
      { label: 'Angular', value: 'angular' },
      { label: 'Node.js', value: 'nodejs' },
      { label: 'Python', value: 'python' },
      { label: 'Java', value: 'java' },
      { label: 'C#', value: 'csharp' },
      { label: 'PHP', value: 'php' }
    ]),
    formFieldUtils.createTextareaField('features', '功能列表', '请列出主要功能点', true),
    formFieldUtils.createTextareaField('additionalInfo', '其他信息', '请提供其他相关信息')
  ]
};

/**
 * 所有表单模板
 */
export const formTemplates: FormTemplate[] = [
  contactFormTemplate,
  registrationFormTemplate,
  surveyFormTemplate,
  productFeedbackFormTemplate,
  aiPromptFormTemplate,
  programmingProjectFormTemplate
];

/**
 * 获取所有表单模板
 * @returns 所有表单模板
 */
export function getAllTemplates(): FormTemplate[] {
  return formTemplates;
}

/**
 * 根据ID获取表单模板
 * @param id 模板ID
 * @returns 表单模板
 */
export function getTemplateById(id: string): FormTemplate | undefined {
  return formTemplates.find(template => template.id === id);
}

/**
 * 根据分类获取表单模板
 * @param category 分类
 * @returns 表单模板列表
 */
export function getTemplatesByCategory(category: string): FormTemplate[] {
  return formTemplates.filter(template => template.category === category);
}

/**
 * 获取所有分类
 * @returns 分类列表
 */
export function getAllCategories(): string[] {
  const categories = new Set<string>();
  formTemplates.forEach(template => categories.add(template.category));
  return Array.from(categories);
}

export default {
  getAllTemplates,
  getTemplateById,
  getTemplatesByCategory,
  getAllCategories
};
