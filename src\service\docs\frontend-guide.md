# 前端对接指南

本文档提供了前端开发人员对接AI聊天系统API的指南和最佳实践。

## 目录

- [开始使用](#开始使用)
- [认证流程](#认证流程)
- [聊天功能实现](#聊天功能实现)
- [绘画功能实现](#绘画功能实现)
- [错误处理](#错误处理)
- [最佳实践](#最佳实践)

## 开始使用

### 基础设置

1. 创建API请求客户端

```javascript
// 使用axios创建API客户端
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://your-api-base-url',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 添加请求拦截器，自动添加token
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器，处理常见错误
apiClient.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

## 认证流程

### 登录实现

```javascript
async function login(username, password) {
  try {
    const response = await apiClient.post('/auth/login', {
      username,
      password
    });
    
    // 保存token
    localStorage.setItem('token', response.data);
    
    // 获取用户信息
    await getUserInfo();
    
    return true;
  } catch (error) {
    console.error('登录失败:', error);
    return false;
  }
}

async function getUserInfo() {
  try {
    const response = await apiClient.get('/auth/info');
    // 保存用户信息
    localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo));
    localStorage.setItem('userBalance', JSON.stringify(response.data.userBalance));
    return response.data;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}
```

## 聊天功能实现

### 常规聊天（同步方式）

```javascript
async function sendChatMessage(message, options = {}) {
  try {
    const response = await apiClient.post('/chatgpt/chat-sync', {
      prompt: message,
      options: {
        parentMessageId: options.parentMessageId || '',
        model: options.model || 'gpt-3.5-turbo',
        temperature: options.temperature || 0.7,
        groupId: options.groupId
      },
      systemMessage: options.systemMessage
    });
    
    return response.data;
  } catch (error) {
    console.error('发送消息失败:', error);
    throw error;
  }
}
```

### 流式聊天（实时响应）

```javascript
async function sendStreamChatMessage(message, options = {}, onProgress) {
  try {
    const response = await fetch(`${apiClient.defaults.baseURL}/chatgpt/chat-process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        prompt: message,
        options: {
          parentMessageId: options.parentMessageId || '',
          model: options.model || 'gpt-3.5-turbo',
          temperature: options.temperature || 0.7,
          groupId: options.groupId
        },
        systemMessage: options.systemMessage
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let result = { text: '', done: false };

    while (!result.done) {
      const { value, done } = await reader.read();
      result.done = done;
      
      if (done) break;
      
      const text = decoder.decode(value);
      buffer += text;
      
      // 处理接收到的数据块
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 保留最后一个不完整的行
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            result.text = data.text || data.answer || '';
            onProgress && onProgress(data);
          } catch (e) {
            console.error('解析JSON失败:', e, line);
          }
        }
      }
    }
    
    return result.text;
  } catch (error) {
    console.error('流式聊天请求失败:', error);
    throw error;
  }
}
```

### 创建对话组

```javascript
async function createChatGroup(title) {
  try {
    const response = await apiClient.post('/group/create', {
      title
    });
    
    return response.data;
  } catch (error) {
    console.error('创建对话组失败:', error);
    throw error;
  }
}
```

### 获取对话组列表

```javascript
async function getChatGroups() {
  try {
    const response = await apiClient.get('/group/query');
    return response.data;
  } catch (error) {
    console.error('获取对话组列表失败:', error);
    throw error;
  }
}
```

## 绘画功能实现

### 生成图片

```javascript
async function generateImage(prompt, options = {}) {
  try {
    const response = await apiClient.post('/chatgpt/chat-draw', {
      prompt,
      n: options.n || 1,
      size: options.size || '1024x1024',
      quality: options.quality || 'standard',
      extraParam: options.extraParam,
      action: 'IMAGINE'
    });
    
    return response.data;
  } catch (error) {
    console.error('生成图片失败:', error);
    throw error;
  }
}
```

### 获取我的绘画记录

```javascript
async function getMyDrawings(page = 1, size = 10) {
  try {
    const response = await apiClient.get('/chatLog/draw', {
      params: { page, size }
    });
    
    return response.data;
  } catch (error) {
    console.error('获取绘画记录失败:', error);
    throw error;
  }
}
```

## 最佳实践

### 1. 处理长时间运行的请求

对于可能需要较长时间的请求（如复杂的AI生成或绘画），建议：

- 使用流式API而不是同步API
- 实现请求超时和重试机制
- 提供用户反馈，如加载指示器或进度条

### 2. 优化用户体验

- 实现打字机效果显示AI回复
- 在等待响应时显示思考动画
- 允许用户在等待响应时取消请求

### 3. 错误处理和恢复

- 实现全局错误处理
- 在UI中友好地显示错误信息
- 提供重试选项
- 在网络错误时保存用户输入

### 4. 性能优化

- 实现消息缓存
- 使用虚拟滚动处理长对话历史
- 延迟加载图片和其他资源

### 5. 安全最佳实践

- 不要在客户端存储敏感信息
- 实现token刷新机制
- 使用HTTPS进行所有API通信
- 实现适当的CORS策略
