<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import formFieldTypes, { fieldTypeLabels, fieldTypeTagTypes, defaultDateTimeFormats } from '@/constants/formFieldTypes';
import { addFormField, removeFormField, formatFormFieldsJson } from '@/utils/formUtils';
import FormFieldEditor from '@/components/FormFieldEditor/index.vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '[]'
  },
  showJsonEditor: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'update:showJsonEditor']);

// 表单字段数组
const formFields = ref<any[]>([]);

// JSON编辑器内容
const jsonEditorContent = ref(props.modelValue);

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  try {
    formFields.value = JSON.parse(newValue);
    // 仅当当前不在编辑状态时才更新jsonEditorContent
    if (!props.showJsonEditor) {
      jsonEditorContent.value = formatFormFieldsJson(newValue);
    }
  } catch (error) {
    console.error('解析表单字段失败', error);
    formFields.value = [];
    if (!props.showJsonEditor) {
      jsonEditorContent.value = '[]';
    }
  }
}, { immediate: true });

// 监听showJsonEditor变化
watch(() => props.showJsonEditor, (newValue) => {
  if (newValue) {
    // 切换到JSON编辑器时，更新JSON内容
    jsonEditorContent.value = formatFormFieldsJson(props.modelValue);
  } else {
    // 从JSON编辑器切换回来时，尝试解析JSON
    try {
      const fields = JSON.parse(jsonEditorContent.value);
      formFields.value = fields;
      emit('update:modelValue', JSON.stringify(fields, null, 2));
    } catch (error) {
      ElMessage.error('JSON格式错误，请检查');
    }
  }
}, { immediate: true });

// 监听jsonEditorContent变化
watch(jsonEditorContent, (newValue) => {
  // 仅当在JSON编辑器模式下才处理
  if (props.showJsonEditor) {
    try {
      const fields = JSON.parse(newValue);
      emit('update:modelValue', JSON.stringify(fields, null, 2));
    } catch (error) {
      // JSON解析错误时不更新modelValue
    }
  }
});

// 处理表单字段变化
function handleFormFieldsChange(fields: any[]) {
  try {
    formFields.value = fields;
    emit('update:modelValue', JSON.stringify(fields, null, 2));
  } catch (error) {
    console.error('更新表单字段失败', error);
  }
}

// 更新JSON编辑器内容
function updateJsonContent(event: Event | string) {
  // 如果传入的是事件对象
  if (event instanceof Event && event.target) {
    const target = event.target as HTMLTextAreaElement;
    jsonEditorContent.value = target.value;
  }
  // 如果传入的是字符串
  else if (typeof event === 'string') {
    jsonEditorContent.value = event;
  }
  // 如果不是事件对象也不是字符串，则不做任何处理
  else {
    return;
  }

  try {
    const fields = JSON.parse(jsonEditorContent.value);
    emit('update:modelValue', JSON.stringify(fields, null, 2));
  } catch (error) {
    // JSON解析错误时不更新modelValue
    console.error('JSON解析错误', error);
  }
}

// 切换JSON编辑器显示状态
function toggleJsonEditor() {
  emit('update:showJsonEditor', !props.showJsonEditor);
}
</script>

<template>
  <div class="form-editor">
    <!-- 工具栏 -->
    <div class="form-editor-toolbar">
      <div class="flex justify-between mb-2">
        <div class="flex gap-2">
          <el-button type="primary" size="small" @click="toggleJsonEditor">
            {{ props.showJsonEditor ? '可视化编辑' : 'JSON编辑' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- JSON编辑器 -->
    <div v-if="props.showJsonEditor" class="json-editor">
      <el-input
        v-model="jsonEditorContent"
        type="textarea"
        :rows="10"
        placeholder="请使用JSON格式定义表单字段"
        @update:modelValue="updateJsonContent"
      />
      <div class="text-gray-500 text-sm mt-2">
        表单字段定义使用JSON格式，支持的字段类型有：input、textarea、select、radio、checkbox等
      </div>
    </div>

    <!-- 可视化编辑器 -->
    <div v-else class="visual-editor">
      <!-- 使用表单字段编辑器组件 -->
      <FormFieldEditor
        v-model="formFields"
        :showJsonEditor="false"
        @update:modelValue="handleFormFieldsChange"
      />
    </div>
  </div>
</template>

<style scoped>
.form-editor {
  width: 100%;
}

.form-editor-toolbar {
  margin-bottom: 10px;
}

.json-editor, .visual-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.visual-editor {
  max-height: 600px;
  overflow-y: auto;
}
</style>
