# AI绘画接口文档

## 接口规范

所有API请求和响应格式与聊天接口一致，请参考[聊天接口文档](./chat-api.md)中的接口规范部分。

## 绘画相关接口

### 1. AI绘画

**接口地址**：`/chatgpt/chat-draw`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "prompt": "Draw a cute little dog",
  "n": 1,
  "size": "1024x1024",
  "quality": "standard",
  "extraParam": "--ar 16:9 --c 0",
  "action": "IMAGINE"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| prompt | string | 是 | 绘画描述信息 |
| n | number | 是 | 绘画张数 |
| size | string | 是 | 图片尺寸，如"1024x1024" |
| quality | string | 是 | 图片质量，如"standard" |
| extraParam | string | 否 | 除了prompt的额外参数 |
| imgUrl | string | 否 | 垫图图片地址 |
| action | string | 否 | 任务类型，可用值:IMAGINE,UPSCALE,VARIATION,ZOOM,PAN,DESCRIBE,BLEND,SHORTEN,SWAP_FACE |
| orderId | number | 否 | 变体或者放大的序号 |
| drawId | number | 否 | 绘画的DBID |
| customId | string | 否 | 自定义ID |
| base64 | string | 否 | 图片base64数据 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": {
    "id": 12345,
    "status": 2,
    "progress": "100%",
    "imageUrl": "https://example.com/images/generated.png",
    "prompt": "Draw a cute little dog",
    "model": "midjourney"
  },
  "message": "请求成功"
}
```

### 2. 查询我的绘制记录

**接口地址**：`/chatLog/draw`

**请求方式**：GET

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```
/chatLog/draw?page=1&size=10
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | number | 否 | 页码，默认1 |
| size | number | 否 | 每页数量，默认10 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": {
    "rows": [
      {
        "id": 12345,
        "userId": 100,
        "model": "midjourney",
        "prompt": "Draw a cute little dog",
        "answer": "https://example.com/images/generated.png",
        "status": 2,
        "createdAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "count": 1
  },
  "message": "请求成功"
}
```

### 3. 查询所有绘制记录

**接口地址**：`/chatLog/drawAll`

**请求方式**：GET

**请求参数**：

```
/chatLog/drawAll?page=1&size=10&rec=1&model=midjourney
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | number | 否 | 页码，默认1 |
| size | number | 否 | 每页数量，默认10 |
| rec | number | 否 | 是否推荐，0: 默认 1: 推荐 |
| userId | number | 否 | 生成图片的用户id |
| model | string | 否 | 生成图片使用的模型 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": {
    "rows": [
      {
        "id": 12345,
        "userId": 100,
        "model": "midjourney",
        "prompt": "Draw a cute little dog",
        "answer": "https://example.com/images/generated.png",
        "status": 2,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "rec": 1
      }
    ],
    "count": 1
  },
  "message": "请求成功"
}
```

### 4. 推荐图片对外展示

**接口地址**：`/chatLog/recDrawImg`

**请求方式**：POST

**请求头**：

```
Authorization: Bearer {token}
```

**请求参数**：

```json
{
  "id": 12345,
  "rec": 1
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| id | number | 是 | 图片ID |
| rec | number | 是 | 是否推荐，0: 不推荐 1: 推荐 |

**响应结果**：

```json
{
  "code": 200,
  "success": true,
  "data": "操作成功",
  "message": "请求成功"
}
```

## 错误处理

绘画接口可能返回的错误：

| 错误码 | 说明 |
| ------ | ---- |
| 400 | 请求参数错误 |
| 401 | 未授权或token已过期 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

特定错误信息：

- "当前请求已过载，请稍后再试！" - 服务器负载过高
- "当前模型key已被封禁、已冻结当前调用Key、尝试重新对话试试吧！" - API密钥问题
- "生成图像失败，请检查你的提示词是否有非法描述！" - 提示词可能包含不适当内容
