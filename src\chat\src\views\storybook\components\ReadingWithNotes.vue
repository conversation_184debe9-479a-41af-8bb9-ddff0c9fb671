<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { NTabs, NTabPane, NDrawer, NDrawerContent, NButton } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import ReadingPanel from './reading-notes/ReadingPanel.vue';
import NotesPanel from './reading-notes/NotesPanel.vue';

// 响应式布局
const { isMobile } = useBasicLayout();

// 状态
const isLandscape = ref(false);
const activeTab = ref('reading');
const showNoteDrawer = ref(false);
const currentPage = ref(0);
const pageNotes = reactive<Record<number, string>>({});

// 示例绘本数据
const bookData = reactive({
  title: '小兔子的冒险',
  author: '童话作家',
  coverImg: 'https://image.pollinations.ai/prompt/cute%20rabbit%20adventure%20children%20book%20cover',
  pages: [
    {
      id: 1,
      type: 'hard',
      image: 'https://image.pollinations.ai/prompt/cute%20rabbit%20adventure%20children%20book%20cover',
      text: '小兔子的冒险'
    },
    {
      id: 2,
      image: 'https://image.pollinations.ai/prompt/cute%20white%20rabbit%20in%20forest%20children%20illustration',
      text: '从前，有一只名叫小白的小兔子，它住在一片美丽的森林里。每天，小白都会在森林里蹦蹦跳跳，寻找新鲜的胡萝卜和嫩草。'
    },
    {
      id: 3,
      image: 'https://image.pollinations.ai/prompt/rabbit%20looking%20at%20mysterious%20door%20in%20tree%20children%20illustration',
      text: '一天，小白在森林深处发现了一扇神秘的门。这扇门藏在一棵巨大的老树里，门上刻着奇怪的符号。小白从来没有见过这样的门。'
    },
    {
      id: 4,
      image: 'https://image.pollinations.ai/prompt/rabbit%20opening%20magical%20door%20adventure%20children%20illustration',
      text: '小白好奇地推开了门，一道耀眼的光芒闪过，小白发现自己来到了一个全新的世界。这里的天空是紫色的，树叶是蓝色的，花朵会唱歌。'
    },
    {
      id: 5,
      image: 'https://image.pollinations.ai/prompt/rabbit%20meeting%20friendly%20creatures%20magical%20world%20children%20illustration',
      text: '在这个神奇的世界里，小白遇到了许多友好的生物。有会飞的小松鼠、会说话的蘑菇，还有一只戴着眼镜的智慧猫头鹰。'
    },
    {
      id: 6,
      image: 'https://image.pollinations.ai/prompt/rabbit%20and%20owl%20looking%20at%20map%20adventure%20children%20illustration',
      text: '猫头鹰告诉小白，这个世界正面临危险。一个邪恶的巫师偷走了魔法水晶，导致这个世界的魔法正在消失。如果不找回水晶，这个美丽的世界将会永远消失。'
    },
    {
      id: 7,
      image: 'https://image.pollinations.ai/prompt/brave%20rabbit%20on%20adventure%20journey%20children%20illustration',
      text: '虽然小白只是一只普通的兔子，但它决定帮助这个世界。于是，小白和新朋友们踏上了寻找魔法水晶的冒险之旅。'
    },
    {
      id: 8,
      image: 'https://image.pollinations.ai/prompt/rabbit%20crossing%20dangerous%20bridge%20adventure%20children%20illustration',
      text: '他们穿过了危险的悬崖，越过了湍急的河流，穿越了黑暗的森林。一路上，小白学会了勇敢和坚持。'
    },
    {
      id: 9,
      image: 'https://image.pollinations.ai/prompt/rabbit%20confronting%20wizard%20castle%20adventure%20children%20illustration',
      text: '最终，他们来到了巫师的城堡。巫师非常强大，但小白用智慧和朋友们的帮助，成功地拿回了魔法水晶。'
    },
    {
      id: 10,
      image: 'https://image.pollinations.ai/prompt/rabbit%20returning%20crystal%20celebration%20magical%20world%20children%20illustration',
      text: '当魔法水晶被放回原位，整个世界又恢复了生机。所有的生物都感谢小白的勇敢和善良。'
    },
    {
      id: 11,
      image: 'https://image.pollinations.ai/prompt/rabbit%20saying%20goodbye%20to%20magical%20friends%20children%20illustration',
      text: '是时候回家了。小白依依不舍地告别了新朋友，答应有一天会再回来看望他们。'
    },
    {
      id: 12,
      image: 'https://image.pollinations.ai/prompt/rabbit%20back%20home%20with%20memories%20adventure%20children%20illustration',
      text: '回到自己的森林，小白躺在温暖的兔子窝里，回想着这次奇妙的冒险。它知道，勇气和友谊可以创造奇迹。从此，小白不再只是一只普通的兔子，它是一只经历过冒险的勇敢兔子。'
    },
    {
      id: 13,
      type: 'hard',
      image: 'https://image.pollinations.ai/prompt/the%20end%20page%20children%20book%20illustration',
      text: '故事的结束，也是新冒险的开始。'
    }
  ]
});

// 处理页面翻转
const handlePageFlip = (pageNumber) => {
  currentPage.value = pageNumber;
};

// 切换横竖版模式
const toggleOrientation = () => {
  isLandscape.value = !isLandscape.value;
};

// 更新页面笔记
const updatePageNotes = (notes) => {
  Object.assign(pageNotes, notes);
};

// 移动端切换笔记抽屉
const toggleNoteDrawer = () => {
  showNoteDrawer.value = !showNoteDrawer.value;
};

// 获取当前页面内容
const currentPageContent = computed(() => {
  return bookData.pages[currentPage.value]?.text || '';
});
</script>

<template>
  <div class="reading-with-notes">
    <!-- 桌面布局 -->
    <div v-if="!isMobile" class="desktop-layout">
      <ReadingPanel
        :book-data="bookData"
        :landscape="isLandscape"
        @page-flip="handlePageFlip"
        @toggle-orientation="toggleOrientation"
      />

      <NotesPanel
        :current-page="currentPage"
        :page-content="currentPageContent"
        :page-notes="pageNotes"
        @update:page-notes="updatePageNotes"
      />
    </div>

    <!-- 移动端布局 -->
    <div v-else class="mobile-layout">
      <NTabs v-model:value="activeTab" type="line" animated>
        <NTabPane name="reading" tab="绘本阅读">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:book-open-line" size="16" class="mr-1" />
              <span>绘本阅读</span>
            </div>
          </template>

          <div class="mobile-reading-container">
            <div class="section-controls">
              <NButton size="small" @click="toggleOrientation" class="control-button">
                <SvgIcon :name="isLandscape ? 'ri:smartphone-line' : 'ri:tablet-line'" size="16" />
                {{ isLandscape ? '竖版' : '横版' }}
              </NButton>
              <NButton size="small" @click="toggleNoteDrawer" class="control-button">
                <SvgIcon name="ri:sticky-note-line" size="16" />
                笔记
              </NButton>
            </div>

            <ReadingPanel
              :book-data="bookData"
              :landscape="isLandscape"
              @page-flip="handlePageFlip"
              @toggle-orientation="toggleOrientation"
            />
          </div>
        </NTabPane>

        <NTabPane name="notes" tab="阅读笔记">
          <template #tab>
            <div class="tab-label">
              <SvgIcon name="ri:sticky-note-line" size="16" class="mr-1" />
              <span>阅读笔记</span>
            </div>
          </template>

          <div class="mobile-notes-container">
            <NotesPanel
              :current-page="currentPage"
              :page-content="currentPageContent"
              :page-notes="pageNotes"
              @update:page-notes="updatePageNotes"
            />
          </div>
        </NTabPane>
      </NTabs>
    </div>

    <!-- 移动端笔记抽屉 -->
    <NDrawer v-if="isMobile" v-model:show="showNoteDrawer" placement="right" :width="300">
      <NDrawerContent title="阅读笔记" closable>
        <div class="drawer-content">
          <NotesPanel
            :current-page="currentPage"
            :page-content="currentPageContent"
            :page-notes="pageNotes"
            @update:page-notes="updatePageNotes"
          />
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.reading-with-notes {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 桌面布局 */
.desktop-layout {
  display: flex;
  height: 100%;
  gap: 1.5rem;
}

.desktop-layout > :first-child {
  flex: 3;
}

.desktop-layout > :last-child {
  flex: 2;
}

/* 移动端布局 */
.mobile-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mobile-reading-container,
.mobile-notes-container {
  height: calc(100vh - 120px);
  padding: 1rem;
}

.section-controls {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tab-label {
  display: flex;
  align-items: center;
}

.drawer-content {
  height: calc(100vh - 60px);
}
</style>
