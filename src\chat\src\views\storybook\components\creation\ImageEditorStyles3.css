/* 进度条样式 */
.progress-container {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border: 2px solid #e2e8f0;
  margin-top: 1rem;
}

.dark .progress-container {
  background-color: #1e293b;
  border-color: #475569;
}

.progress-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.status-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.magic-spinner {
  width: 2rem;
  height: 2rem;
  border: 4px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.status-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
}

.dark .status-text {
  color: #e2e8f0;
}

.progress-bar {
  width: 100%;
  height: 1rem;
  background-color: #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 0.5rem;
  transition: width 0.5s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 20px 20px;
  animation: progress-animation 1s linear infinite;
}

@keyframes progress-animation {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

.progress-percentage {
  font-size: 1rem;
  color: #475569;
  text-align: right;
  font-weight: 600;
}

.dark .progress-bar {
  background-color: #334155;
}

.dark .progress-fill {
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.dark .progress-percentage {
  color: #94a3b8;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  background-color: white;
  color: #64748b;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.button-icon {
  font-size: 1.25rem;
}

.dark .cancel-button {
  background-color: #1e293b;
  border-color: #334155;
  color: #94a3b8;
}

.cancel-button:hover {
  background-color: #f8fafc;
  color: #334155;
  transform: translateY(-2px);
}

.dark .cancel-button:hover {
  background-color: #334155;
  color: #e2e8f0;
}

.apply-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.75rem;
  background-color: #3b82f6;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.apply-button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(59, 130, 246, 0.4);
}

.apply-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.dark .apply-button {
  background-color: #3b82f6;
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.5);
}

.dark .apply-button:hover {
  background-color: #2563eb;
  box-shadow: 0 6px 8px rgba(59, 130, 246, 0.6);
}

.dark .apply-button:disabled {
  background-color: #1e40af;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .editor-main {
    flex-direction: column;
  }
  
  .editor-left-panel {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .dark .editor-left-panel {
    border-color: #334155;
  }
  
  .canvas-container {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .editor-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .editor-toolbar {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .tool-group {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .edit-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .cancel-button, .apply-button {
    width: 100%;
    justify-content: center;
  }
}
