import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { CreateShareDto } from './dto/create-share.dto';
import { GetSharedHtmlDto } from './dto/get-shared-html.dto';
import { GetUserSharesDto } from './dto/get-user-shares.dto';
import { ShareEntity } from './entities/share.entity';

@Injectable()
export class ShareService {
  constructor(
    @InjectRepository(ShareEntity)
    private readonly shareEntity: Repository<ShareEntity>,
  ) {}

  async createHtmlShare(createShareDto: CreateShareDto, userId?: number) {
    console.log('[ShareService] 开始创建HTML分享');
    try {
      let { htmlContent } = createShareDto;
      console.log('[ShareService] 原始 htmlContent 类型:', typeof htmlContent);

      if (!htmlContent) {
        console.error('[ShareService] HTML内容为空');
        throw new HttpException('HTML内容不能为空', HttpStatus.BAD_REQUEST);
      }

      if (typeof htmlContent !== 'string') {
        console.warn('[ShareService] 收到非字符串HTML内容，尝试转换:', typeof htmlContent);
        try {
          try {
            htmlContent = JSON.stringify(htmlContent);
            console.log('[ShareService] JSON.stringify 转换成功');
          } catch (jsonError) {
            console.warn('[ShareService] JSON.stringify 转换失败，尝试 String():', jsonError);
            htmlContent = String(htmlContent);
          }

          console.log('[ShareService] 转换后 htmlContent 类型:', typeof htmlContent);
          console.log('[ShareService] 转换后 htmlContent 长度:', htmlContent.length);
          console.log('[ShareService] 转换后 htmlContent 预览:', htmlContent.substring(0, 100) + '...');

          if (htmlContent.includes('[[Prototype]]') || htmlContent.includes('__proto__')) {
            console.error('[ShareService] 转换后的内容包含原型信息，尝试清理');
            htmlContent = htmlContent.replace(/\[\[Prototype\]\].*?\}\)/gs, '');
            htmlContent = htmlContent.replace(/__proto__.*?\}/gs, '');
          }
        } catch (conversionError) {
          console.error('[ShareService] 转换HTML内容失败:', conversionError);
          throw new HttpException('HTML内容格式错误', HttpStatus.BAD_REQUEST);
        }
      } else {
        console.log('[ShareService] htmlContent 是字符串，长度:', htmlContent.length);
        if (htmlContent.includes('[[Prototype]]') || htmlContent.includes('__proto__')) {
          console.warn('[ShareService] 字符串内容包含原型信息，尝试清理');
          htmlContent = htmlContent.replace(/\[\[Prototype\]\].*?\}\)/gs, '');
          htmlContent = htmlContent.replace(/__proto__.*?\}/gs, '');
        }
      }

      const shareCode = this.generateShareCode();
      console.log('[ShareService] 生成分享代码:', shareCode);

      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);
      console.log('[ShareService] 设置过期时间:', expiresAt);

      // 已移除操作跟踪功能

      console.log('[ShareService] 准备创建分享记录');
      console.log('[ShareService] 用户ID:', userId);

      const share = this.shareEntity.create({
        shareCode,
        htmlContent,
        expiresAt,
        creatorId: userId
      });

      console.log('[ShareService] 创建的分享记录:', {
        shareCode: share.shareCode,
        expiresAt: share.expiresAt,
        creatorId: share.creatorId
      });

      console.log('[ShareService] 开始保存分享记录');
      try {
        const savedShare = await this.shareEntity.save(share);
        console.log('[ShareService] 分享记录保存成功, ID:', savedShare.id);
        console.log('[ShareService] 保存的分享记录:', {
          id: savedShare.id,
          shareCode: savedShare.shareCode,
          creatorId: savedShare.creatorId,
          expiresAt: savedShare.expiresAt
        });
      } catch (dbError) {
        console.error('[ShareService] 分享记录保存失败:', dbError);
        throw new HttpException('保存分享记录失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      return shareCode;
    } catch (error) {
      console.error('[ShareService] 创建HTML分享失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('创建HTML分享失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getSharedHtml(getSharedHtmlDto: GetSharedHtmlDto, req?: any) {
    console.log('[ShareService] 开始获取分享的HTML内容');
    const { shareCode } = getSharedHtmlDto;
    console.log('[ShareService] 请求的分享代码:', shareCode);

    try {
      console.log('[ShareService] 查询数据库中的分享记录');
      const share = await this.shareEntity.findOne({
        where: { shareCode },
      });

      if (!share) {
        console.error('[ShareService] 分享记录不存在:', shareCode);
        throw new HttpException('分享内容不存在', HttpStatus.NOT_FOUND);
      }

      console.log('[ShareService] 找到分享记录, ID:', share.id);
      console.log('[ShareService] 分享创建时间:', share.createdAt);
      console.log('[ShareService] 分享过期时间:', share.expiresAt);
      console.log('[ShareService] HTML内容长度:', share.htmlContent.length);

      const now = new Date();
      if (now > share.expiresAt) {
        console.error('[ShareService] 分享内容已过期, 当前时间:', now, ', 过期时间:', share.expiresAt);
        throw new HttpException('分享内容已过期', HttpStatus.GONE);
      }

      console.log('[ShareService] 分享内容有效，准备返回');
      return { htmlContent: share.htmlContent };
    } catch (error) {
      console.error('[ShareService] 获取分享内容失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('获取分享内容失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }



  generateShareCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  }

  async getUserShares(userId: number, getUserSharesDto: GetUserSharesDto) {
    console.log('[ShareService] 获取用户分享列表, 用户ID:', userId);
    const { page = 1, size = 10, keyword } = getUserSharesDto;

    try {
      const where: any = { creatorId: userId };

      // 如果有关键词，搜索HTML内容
      if (keyword) {
        where.htmlContent = Like(`%${keyword}%`);
      }

      console.log('[ShareService] 查询条件:', where);

      // 查询总数
      const count = await this.shareEntity.count({ where });
      console.log('[ShareService] 查询到的记录总数:', count);

      if (count === 0) {
        console.log('[ShareService] 没有找到记录，检查数据库中是否有任何分享记录');
        const allCount = await this.shareEntity.count();
        console.log('[ShareService] 数据库中的总记录数:', allCount);

        if (allCount > 0) {
          console.log('[ShareService] 数据库中有记录，但没有与当前用户关联的记录');
          // 查询所有记录的creatorId
          const allShares = await this.shareEntity.find({
            select: ['id', 'creatorId']
          });
          console.log('[ShareService] 所有记录的creatorId:', allShares.map(s => ({ id: s.id, creatorId: s.creatorId })));
        }
      }

      // 查询分页数据
      const shares = await this.shareEntity.find({
        where,
        order: { createdAt: 'DESC' },
        skip: (page - 1) * size,
        take: size,
        select: ['id', 'shareCode', 'createdAt', 'expiresAt']
      });

      console.log('[ShareService] 查询到的分页数据数量:', shares.length);

      if (shares.length > 0) {
        console.log('[ShareService] 查询到的第一条记录:', shares[0]);
      }

      // 处理分享链接
      const result = shares.map(share => {
        const isExpired = new Date() > share.expiresAt;
        return {
          ...share,
          isExpired,
          shareUrl: `${process.env.CLIENT_URL || 'http://localhost:9002'}/#/share/${share.shareCode}`
        };
      });

      return { rows: result, count };
    } catch (error) {
      console.error('[ShareService] 获取用户分享列表失败:', error);
      throw new HttpException('获取分享列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async deleteShare(userId: number, shareId: number) {
    console.log('[ShareService] 删除分享, 用户ID:', userId, '分享ID:', shareId);

    try {
      // 查找分享记录
      const share = await this.shareEntity.findOne({
        where: { id: shareId, creatorId: userId }
      });

      if (!share) {
        console.error('[ShareService] 分享记录不存在或不属于当前用户');
        throw new HttpException('分享记录不存在或不属于当前用户', HttpStatus.NOT_FOUND);
      }

      // 删除分享记录
      const result = await this.shareEntity.delete(shareId);

      if (result.affected > 0) {
        console.log('[ShareService] 删除分享成功');
        return { success: true, message: '删除分享成功' };
      } else {
        console.error('[ShareService] 删除分享失败');
        throw new HttpException('删除分享失败', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (error) {
      console.error('[ShareService] 删除分享失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('删除分享失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getShareContent(userId: number, shareId: number) {
    console.log('[ShareService] 获取分享内容, 用户ID:', userId, '分享ID:', shareId);

    try {
      // 查找分享记录
      const share = await this.shareEntity.findOne({
        where: { id: shareId, creatorId: userId }
      });

      if (!share) {
        console.error('[ShareService] 分享记录不存在或不属于当前用户');
        throw new HttpException('分享记录不存在或不属于当前用户', HttpStatus.NOT_FOUND);
      }

      return { htmlContent: share.htmlContent, shareCode: share.shareCode };
    } catch (error) {
      console.error('[ShareService] 获取分享内容失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('获取分享内容失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
