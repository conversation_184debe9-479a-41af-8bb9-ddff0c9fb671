import { Test, TestingModule } from '@nestjs/testing';
import { StorybookAdminController } from './storybook-admin.controller';
import { StorybookService } from './storybook.service';
import { AdminAuthGuard } from 'src/common/auth/adminAuth.guard';
import { SuperAuthGuard } from 'src/common/auth/superAuth.guard';
import { StorybookPromptEntity, StorybookTemplateEntity, StorybookConfigEntity } from './entities';
import { NotFoundException, BadRequestException } from '@nestjs/common';

// 添加Jest类型声明
import 'jest';

// 创建模拟StorybookService
const mockStorybookService = () => ({
  getAllStorybooks: jest.fn(),
  reviewStorybook: jest.fn(),
  setRecommendStatus: jest.fn(),
  getAllConfigs: jest.fn(),
  setConfig: jest.fn(),
  getAllPrompts: jest.fn(),
  createPrompt: jest.fn(),
  updatePrompt: jest.fn(),
  deletePrompt: jest.fn(),
  getAllTemplates: jest.fn(),
  createTemplate: jest.fn(),
  updateTemplate: jest.fn(),
  deleteTemplate: jest.fn(),
  getStatistics: jest.fn(),
  getImages: jest.fn(),
  getImageDetail: jest.fn(),
  auditImage: jest.fn(),
  setImageQuality: jest.fn(),
  deleteImage: jest.fn(),
  getImageGenerationConfig: jest.fn(),
  updateImageGenerationConfig: jest.fn(),
  getImageUsageStats: jest.fn(),
  getContentSafetyConfig: jest.fn(),
  updateContentSafetyConfig: jest.fn(),
  auditStorybookContent: jest.fn(),
  autoAuditStorybook: jest.fn(),
  getViolationStats: jest.fn(),
});

// 创建模拟请求对象
const mockRequest = () => ({
  user: {
    id: 1,
    username: 'admin',
    role: 'admin',
  },
});

describe('StorybookAdminController', () => {
  let controller: StorybookAdminController;
  let service: StorybookService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StorybookAdminController],
      providers: [
        {
          provide: StorybookService,
          useFactory: mockStorybookService,
        },
      ],
    })
      .overrideGuard(AdminAuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(SuperAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<StorybookAdminController>(StorybookAdminController);
    service = module.get<StorybookService>(StorybookService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  // 测试获取所有绘本 - ST-002
  describe('getAllStorybooks', () => {
    it('应该返回所有绘本', async () => {
      const mockResult = {
        items: [{ id: 1, title: '测试绘本' }],
        total: 1,
        page: 1,
        limit: 10,
      };

      jest.spyOn(service, 'getAllStorybooks').mockResolvedValue(mockResult as any);

      const result = await controller.getAllStorybooks(
        1,
        10,
        1,
        1,
        '测试',
        new Date(),
        new Date()
      );

      expect(result).toEqual(mockResult);
      expect(service.getAllStorybooks).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        status: 1,
        userId: 1,
        keyword: '测试',
        startDate: expect.any(Date),
        endDate: expect.any(Date),
      });
    });
  });

  // 测试审核绘本 - ST-006
  describe('reviewStorybook', () => {
    it('应该成功审核绘本', async () => {
      const mockResult = { id: 1, status: 1 };

      jest.spyOn(service, 'reviewStorybook').mockResolvedValue(mockResult as any);

      const result = await controller.reviewStorybook(1, 1);

      expect(result).toEqual(mockResult);
      expect(service.reviewStorybook).toHaveBeenCalledWith(1, 1);
    });

    it('当绘本不存在时应该抛出异常', async () => {
      jest.spyOn(service, 'reviewStorybook').mockRejectedValue(new NotFoundException('绘本不存在'));

      await expect(controller.reviewStorybook(999, 1)).rejects.toThrow(NotFoundException);
    });
  });

  // 测试设置推荐状态 - ST-007
  describe('setRecommendStatus', () => {
    it('应该成功设置绘本推荐状态', async () => {
      const mockResult = { id: 1, isRecommended: 1 };

      jest.spyOn(service, 'setRecommendStatus').mockResolvedValue(mockResult as any);

      const result = await controller.setRecommendStatus(1, 1);

      expect(result).toEqual(mockResult);
      expect(service.setRecommendStatus).toHaveBeenCalledWith(1, 1);
    });
  });

  // 测试获取所有配置 - ST-037
  describe('getAllConfigs', () => {
    it('应该返回所有配置', async () => {
      const mockConfigs = [
        { configKey: 'key1', configVal: 'value1', public: 1 },
        { configKey: 'key2', configVal: 'value2', public: 0 },
      ];

      jest.spyOn(service, 'getAllConfigs').mockResolvedValue(mockConfigs as StorybookConfigEntity[]);

      const result = await controller.getAllConfigs();

      expect(result).toEqual(mockConfigs);
      expect(service.getAllConfigs).toHaveBeenCalled();
    });
  });

  // 测试更新配置 - ST-038
  describe('updateConfig', () => {
    it('应该成功更新配置', async () => {
      const mockConfig = {
        configKey: 'test_key',
        configVal: 'updated_value',
        description: '测试配置',
        public: 1
      };

      jest.spyOn(service, 'setConfig').mockResolvedValue(mockConfig as StorybookConfigEntity);

      const result = await controller.updateConfig('test_key', {
        configVal: 'updated_value',
        description: '测试配置',
        public: 1
      });

      expect(result).toEqual(mockConfig);
      expect(service.setConfig).toHaveBeenCalledWith('test_key', 'updated_value', '测试配置');
    });
  });

  // 测试获取所有提示词 - ST-033
  describe('getAllPrompts', () => {
    it('应该返回所有提示词', async () => {
      const mockPrompts = [
        {
          id: 1,
          title: '提示词1',
          content: '内容1',
          type: '类型1',
          status: 1,
          order: 100,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        },
        {
          id: 2,
          title: '提示词2',
          content: '内容2',
          type: '类型2',
          status: 1,
          order: 100,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        },
      ];

      jest.spyOn(service, 'getAllPrompts').mockResolvedValue(mockPrompts as StorybookPromptEntity[]);

      const result = await controller.getAllPrompts();

      expect(result).toEqual(mockPrompts);
      expect(service.getAllPrompts).toHaveBeenCalled();
    });
  });

  // 测试创建提示词 - ST-032
  describe('createPrompt', () => {
    it('应该成功创建提示词', async () => {
      const promptData = {
        title: '新提示词',
        content: '新内容',
        type: '新类型',
        status: 1,
        order: 100
      };

      const mockPrompt = {
        id: 1,
        ...promptData,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
      };

      jest.spyOn(service, 'createPrompt').mockResolvedValue(mockPrompt as StorybookPromptEntity);

      const result = await controller.createPrompt(promptData as Partial<StorybookPromptEntity>);

      expect(result).toEqual(mockPrompt);
      expect(service.createPrompt).toHaveBeenCalledWith(promptData);
    });
  });

  // 测试获取所有模板 - ST-015
  describe('getAllTemplates', () => {
    it('应该返回所有模板', async () => {
      const mockTemplates = [
        { id: 1, title: '模板1', category: '分类1' },
        { id: 2, title: '模板2', category: '分类2' },
      ];

      jest.spyOn(service, 'getAllTemplates').mockResolvedValue(mockTemplates as StorybookTemplateEntity[]);

      const result = await controller.getAllTemplates();

      expect(result).toEqual(mockTemplates);
      expect(service.getAllTemplates).toHaveBeenCalled();
    });
  });

  // 测试创建模板 - ST-014
  describe('createTemplate', () => {
    it('应该成功创建模板', async () => {
      const templateData = {
        title: '新模板',
        description: '模板描述',
        category: '模板分类',
        content: { key: 'value' },
      };

      const mockTemplate = { id: 1, ...templateData, status: 1 };

      jest.spyOn(service, 'createTemplate').mockResolvedValue(mockTemplate as StorybookTemplateEntity);

      const result = await controller.createTemplate(templateData);

      expect(result).toEqual(mockTemplate);
      expect(service.createTemplate).toHaveBeenCalledWith(templateData);
    });
  });

  // 测试获取统计数据 - ST-040
  describe('getStatistics', () => {
    it('应该返回统计数据', async () => {
      const mockStats = {
        totalStorybooks: 100,
        totalViews: 1000,
        totalLikes: 500,
        dailyStats: [
          { date: '2023-07-01', newStorybookCount: 10, totalViewCount: 100 }
        ]
      };

      jest.spyOn(service, 'getStatistics').mockResolvedValue(mockStats);

      // 使用字符串格式的日期
      const startDate = '2023-07-01';
      const endDate = '2023-07-31';
      const result = await controller.getStatistics(startDate, endDate);

      expect(result).toEqual(mockStats);
      expect(service.getStatistics).toHaveBeenCalledWith(startDate, endDate);
    });
  });

  // 测试获取图像列表 - ST-023
  describe('getImages', () => {
    it('应该返回图像列表', async () => {
      const mockResult = {
        items: [{ id: 1, imageUrl: 'https://example.com/image.jpg' }],
        total: 1,
        page: 1,
        limit: 10,
      };

      jest.spyOn(service, 'getImages').mockResolvedValue(mockResult as any);

      const result = await controller.getImages(
        1,
        10,
        1,
        1,
        0,
        1,
        '测试',
        new Date(),
        new Date()
      );

      expect(result).toEqual(mockResult);
      expect(service.getImages).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        userId: 1,
        imageType: 1,
        auditStatus: 0,
        storybookId: 1,
        keyword: '测试',
        startDate: expect.any(Date),
        endDate: expect.any(Date)
      });
    });
  });

  // 测试审核图像 - ST-027
  describe('auditImage', () => {
    it('应该成功审核图像', async () => {
      const mockResult = { id: 1, auditStatus: 1, auditRemark: '通过审核' };

      jest.spyOn(service, 'auditImage').mockResolvedValue(mockResult as any);

      const result = await controller.auditImage(1, 1, '通过审核');

      expect(result).toEqual(mockResult);
      expect(service.auditImage).toHaveBeenCalledWith(1, 1, '通过审核');
    });
  });

  // 测试内容安全配置 - ST-046
  describe('getContentSafetyConfig', () => {
    it('应该返回内容安全配置', async () => {
      const mockResult = { autoAudit: true, sensitiveFilter: true };

      jest.spyOn(service, 'getContentSafetyConfig').mockResolvedValue(mockResult);

      const result = await controller.getContentSafetyConfig();

      expect(result).toEqual(mockResult);
      expect(service.getContentSafetyConfig).toHaveBeenCalled();
    });
  });

  // 测试更新内容安全配置 - ST-047
  describe('updateContentSafetyConfig', () => {
    it('应该成功更新内容安全配置', async () => {
      const config = { autoAudit: true, sensitiveFilter: true };
      const mockResult = { autoAudit: true, sensitiveFilter: true };

      jest.spyOn(service, 'updateContentSafetyConfig').mockResolvedValue(mockResult);

      const result = await controller.updateContentSafetyConfig(config);

      expect(result).toEqual(mockResult);
      expect(service.updateContentSafetyConfig).toHaveBeenCalledWith(config);
    });
  });

  // 测试绘本内容审核 - ST-044
  describe('auditStorybookContent', () => {
    it('应该返回绘本内容审核结果', async () => {
      const mockResult = {
        safe: true,
        issues: []
      };

      jest.spyOn(service, 'auditStorybookContent').mockResolvedValue(mockResult);

      // 提供请求对象
      const mockReq = { user: { id: 1 } };
      const result = await controller.auditStorybookContent(1, mockReq as any);

      expect(result).toEqual(mockResult);
      expect(service.auditStorybookContent).toHaveBeenCalledWith(1, 1);
    });

    it('当内容不安全时应该返回问题列表', async () => {
      const mockResult = {
        safe: false,
        issues: [
          { type: 'title', content: '不安全标题', triggeredWords: ['不安全'] }
        ]
      };

      jest.spyOn(service, 'auditStorybookContent').mockResolvedValue(mockResult);

      // 提供请求对象
      const mockReq = { user: { id: 1 } };
      const result = await controller.auditStorybookContent(1, mockReq as any);

      expect(result).toEqual(mockResult);
      expect(service.auditStorybookContent).toHaveBeenCalledWith(1, 1);
    });
  });
});
