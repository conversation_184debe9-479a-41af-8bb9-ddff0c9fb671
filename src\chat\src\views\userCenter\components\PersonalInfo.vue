<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
  NCard, 
  NAvatar, 
  NInput, 
  NButton, 
  NForm, 
  NFormItem, 
  NSelect,
  NSpace,
  useMessage
} from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import { t } from '@/locales';
import defaultAvatar from '@/assets/avatar.png';
import { fetchUpdateInfoAPI } from '@/api';
import type { ResData } from '@/api/types';

const authStore = useAuthStore();
const useGlobalStore = useGlobalStoreWithOut();
const ms = useMessage();

// 用户信息
const avatar = computed(() => authStore.userInfo.avatar ?? defaultAvatar);
const username = ref(authStore.userInfo.username || '');
const email = computed(() => authStore.userInfo.email || '');
const bio = ref(authStore.userInfo.sign || '');
const school = ref(authStore.userInfo.school || '');
const classInfo = ref(authStore.userInfo.class || '');

// 微信绑定相关
const isUseWxLogin = computed(() => authStore.globalConfig?.isUseWxLogin);
const wechatRegisterStatus = computed(() => Number(authStore.globalConfig.wechatRegisterStatus) === 1);
const isBindWx = computed(() => authStore.userInfo.isBindWx);

// 表单状态
const loading = ref(false);
const formRef = ref(null);

// 更新用户信息
async function updateUserInfo() {
  try {
    loading.value = true;
    const res: ResData = await fetchUpdateInfoAPI({
      username: username.value,
      sign: bio.value,
      school: school.value,
      class: classInfo.value
    });
    
    if (!res.success) {
      ms.error(res.message || '更新失败');
      loading.value = false;
      return;
    }
    
    ms.success('个人信息更新成功');
    authStore.getUserInfo();
  } catch (error) {
    ms.error('更新失败，请稍后重试');
    console.error('更新用户信息失败:', error);
  } finally {
    loading.value = false;
  }
}

// 修改密码
function changePassword() {
  // 打开修改密码弹窗
  useGlobalStore.updatePasswordDialog(true);
}

onMounted(() => {
  // 初始化表单数据
  username.value = authStore.userInfo.username || '';
  bio.value = authStore.userInfo.sign || '';
  school.value = authStore.userInfo.school || '';
  classInfo.value = authStore.userInfo.class || '';
});
</script>

<template>
  <div class="personal-info-container animate__animated animate__fadeIn">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">个人信息管理</h2>
    
    <!-- 个人资料卡片 -->
    <NCard class="mb-6">
      <template #header>
        <div class="text-lg font-medium">基本资料</div>
      </template>
      
      <div class="flex flex-col md:flex-row gap-8">
        <!-- 头像区域 -->
        <div class="flex flex-col items-center">
          <NAvatar
            :size="120"
            :src="avatar"
            :fallback-src="defaultAvatar"
            class="border-4 border-white dark:border-gray-700 shadow-lg hover-float transition-all duration-300"
          />
          <div class="mt-4 text-center">
            <div class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ username }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ email || '未设置邮箱' }}</div>
          </div>
        </div>
        
        <!-- 表单区域 -->
        <div class="flex-1">
          <NForm ref="formRef" label-placement="left" label-width="100px">
            <NFormItem label="用户昵称">
              <NInput v-model:value="username" placeholder="请输入昵称" maxlength="20" show-count />
            </NFormItem>
            
            <NFormItem label="个人简介">
              <NInput
                v-model:value="bio"
                type="textarea"
                placeholder="介绍一下自己吧"
                maxlength="200"
                show-count
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </NFormItem>
            
            <NFormItem label="学校">
              <NInput v-model:value="school" placeholder="请输入学校名称" />
            </NFormItem>
            
            <NFormItem label="班级">
              <NInput v-model:value="classInfo" placeholder="请输入班级信息" />
            </NFormItem>
            
            <NFormItem>
              <NButton
                type="primary"
                :loading="loading"
                @click="updateUserInfo"
                class="hover-float transition-all duration-300"
              >
                保存修改
              </NButton>
            </NFormItem>
          </NForm>
        </div>
      </div>
    </NCard>
    
    <!-- 账号安全卡片 -->
    <NCard>
      <template #header>
        <div class="text-lg font-medium">账号安全</div>
      </template>
      
      <NSpace vertical>
        <!-- 密码修改 -->
        <div class="flex items-center justify-between p-4 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300">
          <div class="flex items-center">
            <SvgIcon icon="ri:lock-password-line" class="text-lg mr-3 text-gray-600 dark:text-gray-400" />
            <div>
              <div class="text-gray-800 dark:text-gray-200">密码修改</div>
              <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">定期修改密码可以保护账号安全</div>
            </div>
          </div>
          <NButton
            size="small"
            type="primary"
            @click="changePassword"
            class="hover-float transition-all duration-300"
          >
            修改密码
          </NButton>
        </div>
        
        <!-- 微信绑定 -->
        <div
          v-if="isUseWxLogin && wechatRegisterStatus"
          class="flex items-center justify-between p-4 rounded-lg bg-gray-50 dark:bg-gray-750 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300"
        >
          <div class="flex items-center">
            <SvgIcon icon="ri:wechat-line" class="text-lg mr-3 text-green-600 dark:text-green-500" />
            <div>
              <div class="text-gray-800 dark:text-gray-200">微信绑定</div>
              <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">绑定微信账号，可以使用微信快速登录</div>
            </div>
          </div>
          <div>
            <NButton
              v-if="!isBindWx"
              size="small"
              type="primary"
              @click="useGlobalStore.updateBindwxDialog(true)"
              class="hover-float transition-all duration-300"
            >
              绑定微信
            </NButton>
            <span v-else class="text-sm text-green-500">已绑定</span>
          </div>
        </div>
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped>
.personal-info-container {
  max-width: 900px;
  margin: 0 auto;
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
}
</style>
