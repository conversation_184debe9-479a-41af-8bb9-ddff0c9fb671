<script setup lang="ts">
import { ref, computed } from 'vue';
import AppCard from '@/components/AppCard/index.vue';

const props = defineProps({
  apps: {
    type: Array as () => Array<any>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  isSystemRole: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['edit', 'delete']);

// 处理编辑应用
function handleEditApp(app: any) {
  emit('edit', app);
}

// 处理删除应用
function handleDeleteApp(app: any) {
  emit('delete', app);
}

// 计算列表视图类
const listViewClass = computed(() => {
  return {
    'app-list-grid': true,
    'app-list-loading': props.loading
  };
});
</script>

<template>
  <div class="app-list-container">
    <el-empty v-if="!loading && (!apps || apps.length === 0)" description="暂无应用数据" />

    <div v-else :class="listViewClass">
      <el-skeleton v-if="loading" :rows="3" animated />

      <template v-else>
        <div v-for="app in apps" :key="app.id" class="app-list-item">
          <AppCard
            :app="app"
            :isSystemRole="isSystemRole"
            @edit="handleEditApp"
            @delete="handleDeleteApp"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.app-list-container {
  width: 100%;
}

.app-list-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.app-list-loading {
  opacity: 0.7;
}

.app-list-item {
  transition: all 0.3s;
}

@media (max-width: 768px) {
  .app-list-grid {
    grid-template-columns: 1fr;
  }
}
</style>
