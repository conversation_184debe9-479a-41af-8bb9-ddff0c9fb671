<script setup lang="ts">
import { computed, ref, markRaw } from 'vue';
import { NModal, NTabs, NTabPane, useMessage } from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import Profile from './Profile.vue';
import Wallet from './Wallet.vue';
import Password from './Password.vue';
import HtmlManager from './HtmlManager.vue';

interface Props {
  visible: boolean;
}

interface Emit {
  (e: 'update:visible', visible: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emit>();

const authStore = useAuthStore();
const useGlobalStore = useGlobalStoreWithOut();
const { isMobile } = useBasicLayout();
const ms = useMessage();

const activeTab = ref('profile');

// 使用计算属性来处理双向绑定
const show = computed({
  get() {
    return props.visible;
  },
  set(visible: boolean) {
    emit('update:visible', visible);
  },
});

// 标签页配置
const tabs = [
  {
    key: 'profile',
    label: '基本信息',
    icon: 'ri:user-3-line',
    component: markRaw(Profile),
  },
  {
    key: 'wallet',
    label: '我的钱包',
    icon: 'ri:wallet-3-line',
    component: markRaw(Wallet),
  },
  {
    key: 'password',
    label: '密码管理',
    icon: 'ri:lock-password-line',
    component: markRaw(Password),
  },
  {
    key: 'html',
    label: 'HTML管理',
    icon: 'ri:html5-line',
    component: markRaw(HtmlManager),
  },
];

// 关闭弹窗
function handleClose() {
  show.value = false;
}
</script>

<template>
  <NModal
    v-model:show="show"
    title="个人中心"
    preset="card"
    :style="{ width: isMobile ? '95%' : '800px', maxWidth: '95vw' }"
    :auto-focus="false"
    class="user-center-modal"
  >
    <div class="user-center-container">
      <NTabs
        v-model:value="activeTab"
        type="line"
        animated
        class="user-center-tabs"
        :class="{ 'mobile-tabs': isMobile }"
      >
        <NTabPane
          v-for="tab in tabs"
          :key="tab.key"
          :name="tab.key"
          :tab="tab.label"
        >
          <template #tab>
            <div class="flex items-center">
              <SvgIcon class="text-lg mr-1" :icon="tab.icon" />
              <span>{{ tab.label }}</span>
            </div>
          </template>

          <!-- 动态组件 -->
          <component :is="tab.component" />
        </NTabPane>
      </NTabs>
    </div>
  </NModal>
</template>

<style scoped>
.user-center-container {
  min-height: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.user-center-tabs {
  height: 100%;
}

.mobile-tabs :deep(.n-tabs-nav) {
  padding: 0 8px;
}

.user-center-modal :deep(.n-card-header) {
  padding-bottom: 8px;
  border-bottom: 1px solid var(--n-border-color);
}

.user-center-modal :deep(.n-card-header__main) {
  font-size: 18px;
  font-weight: 600;
}

/* 自定义滚动条 */
.user-center-container::-webkit-scrollbar {
  width: 6px;
}

.user-center-container::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.user-center-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(144, 147, 153, 0.5);
}

.user-center-container::-webkit-scrollbar-track {
  background-color: transparent;
}
</style>
