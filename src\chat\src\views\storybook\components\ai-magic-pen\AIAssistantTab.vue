<script setup lang="ts">
import { ref } from 'vue';
import { NInput, NButton, NSpace, NTag } from 'naive-ui';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  projectData: any;
}>();

// AI助手状态
const query = ref('');
const isProcessing = ref(false);
const aiResponse = ref('');
const controller = ref(null);
const chatHistory = ref([]);
const authStore = useAuthStore();

// 常用提示
const commonPrompts = [
  { label: '如何设计有教育意义的绘本故事？', value: '如何设计有教育意义的绘本故事？' },
  { label: '儿童绘本的色彩搭配建议', value: '请给我一些儿童绘本的色彩搭配建议' },
  { label: '如何创作适合3-6岁儿童的角色？', value: '如何创作适合3-6岁儿童的绘本角色？' },
  { label: '绘本故事结构模板', value: '请提供几种常用的儿童绘本故事结构模板' },
  { label: '如何处理绘本中的冲突？', value: '如何在儿童绘本中恰当地处理冲突？' }
];

// 发送查询
const sendQuery = async () => {
  if (!query.value.trim()) {
    window.$message?.warning('请输入问题');
    return;
  }

  try {
    isProcessing.value = true;
    aiResponse.value = '';

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 添加到聊天历史
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: query.value,
      timestamp: new Date().toISOString()
    };
    chatHistory.value.push(userMessage);

    // 构建提示词
    let prompt = `
作为儿童绘本创作助手，请回答以下关于绘本创作的问题：

${query.value}

请提供专业、具体且适合教育工作者使用的建议。回答应该简洁明了，重点突出，便于实际应用。
`;

    // 如果项目数据中有相关信息，添加到提示中
    if (props.projectData) {
      if (props.projectData.title) {
        prompt += `\n当前绘本标题：${props.projectData.title}`;
      }

      if (props.projectData.outline?.theme) {
        prompt += `\n当前绘本主题：${props.projectData.outline.theme}`;
      }

      if (props.projectData.outline?.characters) {
        const characters = Object.values(props.projectData.outline.characters)
          .filter(c => c && c.name)
          .map(c => c.name);
        
        if (characters.length > 0) {
          prompt += `\n当前绘本角色：${characters.join('、')}`;
        }
      }
    }

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      signal: controller.value.signal,
      options: {
        groupId: 0
      }
    };

    let responseText = '';

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        // 提取最新的响应内容
        responseText = text;
        aiResponse.value = responseText;
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

    // 添加AI回复到聊天历史
    const aiMessage = {
      id: Date.now(),
      role: 'assistant',
      content: responseText,
      timestamp: new Date().toISOString()
    };
    chatHistory.value.push(aiMessage);

    // 清空查询
    query.value = '';

  } catch (error) {
    console.error('AI助手查询失败:', error);
    window.$message?.error('AI助手查询失败，请稍后重试');
  } finally {
    isProcessing.value = false;
    controller.value = null;
  }
};

// 使用常用提示
const usePrompt = (promptText) => {
  query.value = promptText;
  sendQuery();
};

// 清空聊天历史
const clearChatHistory = () => {
  if (confirm('确定要清空聊天历史吗？')) {
    chatHistory.value = [];
    aiResponse.value = '';
  }
};
</script>

<template>
  <div class="assistant-container">
    <!-- 聊天历史 -->
    <div class="chat-history">
      <div v-if="chatHistory.length === 0" class="empty-chat">
        <span class="emoji-icon large-emoji">🤖</span>
        <p>你好！我是AI创作助手，可以帮助你解决绘本创作中的问题。</p>
        <div class="common-prompts">
          <p>你可以问我：</p>
          <NSpace vertical>
            <NTag
              v-for="prompt in commonPrompts"
              :key="prompt.value"
              type="info"
              size="medium"
              @click="usePrompt(prompt.value)"
            >
              {{ prompt.label }}
            </NTag>
          </NSpace>
        </div>
      </div>

      <div v-else class="chat-messages">
        <div
          v-for="message in chatHistory"
          :key="message.id"
          :class="['chat-message', message.role === 'user' ? 'user-message' : 'ai-message']"
        >
          <div class="message-avatar">
            <span v-if="message.role === 'user'" class="emoji-icon">👤</span>
            <span v-else class="emoji-icon">🤖</span>
          </div>
          <div class="message-content">
            <div v-html="message.content.replace(/\n/g, '<br>')"></div>
          </div>
        </div>

        <div v-if="isProcessing" class="ai-typing">
          <div class="message-avatar">
            <span class="emoji-icon">🤖</span>
          </div>
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <NInput
        v-model:value="query"
        type="textarea"
        placeholder="输入你的问题..."
        :autosize="{ minRows: 2, maxRows: 4 }"
        @keydown.enter.ctrl="sendQuery"
      />
      <div class="input-actions">
        <NButton size="small" @click="clearChatHistory" v-if="chatHistory.length > 0">
          清空历史
        </NButton>
        <NButton
          type="primary"
          @click="sendQuery"
          :loading="isProcessing"
          :disabled="isProcessing || !query.trim()"
        >
          发送
        </NButton>
      </div>
      <div class="input-tips">
        提示：按Ctrl+Enter快速发送
      </div>
    </div>
  </div>
</template>

<style scoped>
.assistant-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  min-height: 300px;
  max-height: calc(100vh - 300px);
}

.dark .chat-history {
  background-color: #1f2937;
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #6b7280;
  padding: 2rem;
}

.dark .empty-chat {
  color: #9ca3af;
}

.large-emoji {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.common-prompts {
  margin-top: 1.5rem;
  width: 100%;
}

.common-prompts p {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-message {
  display: flex;
  gap: 0.75rem;
  max-width: 90%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e7eb;
  flex-shrink: 0;
}

.dark .message-avatar {
  background-color: #374151;
}

.user-message .message-avatar {
  background-color: #dbeafe;
}

.dark .user-message .message-avatar {
  background-color: #1e40af;
}

.ai-message .message-avatar {
  background-color: #f3e8ff;
}

.dark .ai-message .message-avatar {
  background-color: #5b21b6;
}

.emoji-icon {
  font-size: 1.25rem;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  line-height: 1.5;
}

.dark .message-content {
  background-color: #374151;
  color: #e5e7eb;
}

.user-message .message-content {
  background-color: #3b82f6;
  color: white;
  border-bottom-right-radius: 0;
}

.dark .user-message .message-content {
  background-color: #2563eb;
}

.ai-message .message-content {
  background-color: white;
  border-bottom-left-radius: 0;
}

.dark .ai-message .message-content {
  background-color: #1f2937;
}

.ai-typing {
  display: flex;
  gap: 0.75rem;
  align-self: flex-start;
}

.typing-indicator {
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  border-bottom-left-radius: 0;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dark .typing-indicator {
  background-color: #1f2937;
}

.typing-indicator span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #d1d5db;
  animation: typing 1.4s infinite both;
}

.dark .typing-indicator span {
  background-color: #6b7280;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

.chat-input {
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .chat-input {
  background-color: #1f2937;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 0.75rem;
}

.input-tips {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  text-align: right;
}

.dark .input-tips {
  color: #6b7280;
}
</style>
