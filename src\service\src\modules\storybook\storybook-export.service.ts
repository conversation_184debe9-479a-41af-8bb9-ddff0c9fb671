import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as puppeteer from 'puppeteer';
import * as fs from 'fs';
import * as path from 'path';
import * as archiver from 'archiver';
import { StorybookEntity, StorybookPageEntity } from './entities';
import { ConfigService } from '@nestjs/config';
import { UploadService } from 'src/modules/upload/upload.service';

/**
 * 绘本导出服务
 */
@Injectable()
export class StorybookExportService {
  private readonly logger = new Logger(StorybookExportService.name);

  constructor(
    @InjectRepository(StorybookEntity)
    private readonly storybookRepository: Repository<StorybookEntity>,

    @InjectRepository(StorybookPageEntity)
    private readonly pageRepository: Repository<StorybookPageEntity>,

    private readonly configService: ConfigService,
    private readonly uploadService: UploadService,
  ) {}

  /**
   * 导出绘本为PDF
   * @param id 绘本ID
   * @param userId 用户ID
   * @param options 导出选项
   * @returns PDF文件URL
   */
  async exportToPdf(
    id: number,
    userId: number,
    options: {
      range?: 'all' | 'custom',
      startPage?: number,
      endPage?: number
    } = {}
  ): Promise<{ url: string, filename: string }> {
    const { range = 'all', startPage = 1, endPage } = options;

    // 获取绘本详情
    const storybook = await this.storybookRepository.findOne({
      where: { id, userId },
      relations: ['pages']
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 确保页面按页码排序
    storybook.pages.sort((a, b) => a.pageNumber - b.pageNumber);

    // 根据范围筛选页面
    let pagesToExport = storybook.pages;
    if (range === 'custom' && startPage && endPage) {
      pagesToExport = storybook.pages.filter(
        page => page.pageNumber >= startPage && page.pageNumber <= endPage
      );
    }

    if (pagesToExport.length === 0) {
      throw new BadRequestException('没有可导出的页面');
    }

    try {
      // 创建临时目录
      const tempDir = path.join(process.cwd(), 'temp', `export_${id}_${Date.now()}`);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 创建HTML文件
      const htmlPath = path.join(tempDir, 'storybook.html');
      const htmlContent = this.generateHtml(storybook, pagesToExport);
      fs.writeFileSync(htmlPath, htmlContent);

      // 使用Puppeteer生成PDF
      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      const page = await browser.newPage();
      await page.goto(`file://${htmlPath}`, { waitUntil: 'networkidle0' });

      const pdfPath = path.join(tempDir, 'storybook.pdf');
      await page.pdf({
        path: pdfPath,
        format: 'A4',
        printBackground: true,
        margin: {
          top: '1cm',
          right: '1cm',
          bottom: '1cm',
          left: '1cm'
        }
      });

      await browser.close();

      // 上传PDF文件
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const currentDate = `${year}${month}/${day}`;

      const pdfBuffer = fs.readFileSync(pdfPath);
      const filename = `${storybook.title}_${now.getTime()}.pdf`;

      const fileUrl = await this.uploadService.uploadBuffer({
        buffer: pdfBuffer,
        filename,
        dir: `exports/storybooks/${currentDate}`
      });

      // 清理临时文件
      fs.unlinkSync(htmlPath);
      fs.unlinkSync(pdfPath);
      fs.rmdirSync(tempDir);

      return {
        url: fileUrl,
        filename
      };
    } catch (error) {
      this.logger.error(`导出PDF失败: ${error.message}`, error.stack);
      throw new BadRequestException('导出PDF失败，请稍后重试');
    }
  }

  /**
   * 导出绘本为图片集
   * @param id 绘本ID
   * @param userId 用户ID
   * @param options 导出选项
   * @returns 图片集ZIP文件URL
   */
  async exportToImages(
    id: number,
    userId: number,
    options: {
      range?: 'all' | 'custom',
      startPage?: number,
      endPage?: number,
      quality?: 'high' | 'medium' | 'low'
    } = {}
  ): Promise<{ url: string, filename: string }> {
    const { range = 'all', startPage = 1, endPage, quality = 'high' } = options;

    // 获取绘本详情
    const storybook = await this.storybookRepository.findOne({
      where: { id, userId },
      relations: ['pages']
    });

    if (!storybook) {
      throw new NotFoundException('绘本不存在');
    }

    // 确保页面按页码排序
    storybook.pages.sort((a, b) => a.pageNumber - b.pageNumber);

    // 根据范围筛选页面
    let pagesToExport = storybook.pages;
    if (range === 'custom' && startPage && endPage) {
      pagesToExport = storybook.pages.filter(
        page => page.pageNumber >= startPage && page.pageNumber <= endPage
      );
    }

    if (pagesToExport.length === 0) {
      throw new BadRequestException('没有可导出的页面');
    }

    try {
      // 创建临时目录
      const tempDir = path.join(process.cwd(), 'temp', `export_${id}_${Date.now()}`);
      const imagesDir = path.join(tempDir, 'images');
      if (!fs.existsSync(imagesDir)) {
        fs.mkdirSync(imagesDir, { recursive: true });
      }

      // 下载所有图片
      for (let i = 0; i < pagesToExport.length; i++) {
        const page = pagesToExport[i];
        if (page.imageUrl) {
          // 下载图片
          const response = await fetch(page.imageUrl);
          const buffer = await response.arrayBuffer();
          const imagePath = path.join(imagesDir, `page_${page.pageNumber}.jpg`);
          fs.writeFileSync(imagePath, Buffer.from(buffer));
        }
      }

      // 创建ZIP文件
      const zipPath = path.join(tempDir, 'storybook_images.zip');
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', {
        zlib: { level: 9 }
      });

      archive.pipe(output);
      archive.directory(imagesDir, false);
      await archive.finalize();

      // 等待ZIP文件写入完成
      await new Promise<void>((resolve) => {
        output.on('close', () => resolve());
      });

      // 上传ZIP文件
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const currentDate = `${year}${month}/${day}`;

      const zipBuffer = fs.readFileSync(zipPath);
      const filename = `${storybook.title}_images_${now.getTime()}.zip`;

      const fileUrl = await this.uploadService.uploadBuffer({
        buffer: zipBuffer,
        filename,
        dir: `exports/storybooks/${currentDate}`
      });

      // 清理临时文件
      fs.rmSync(tempDir, { recursive: true, force: true });

      return {
        url: fileUrl,
        filename
      };
    } catch (error) {
      this.logger.error(`导出图片集失败: ${error.message}`, error.stack);
      throw new BadRequestException('导出图片集失败，请稍后重试');
    }
  }

  /**
   * 生成HTML内容
   * @param storybook 绘本数据
   * @param pages 页面数据
   * @returns HTML内容
   */
  private generateHtml(storybook: StorybookEntity, pages: StorybookPageEntity[]): string {
    let pagesHtml = '';

    for (const page of pages) {
      pagesHtml += `
        <div class="page">
          <div class="page-number">${page.pageNumber}</div>
          ${page.imageUrl ? `<div class="page-image"><img src="${page.imageUrl}" alt="Page ${page.pageNumber}"></div>` : ''}
          <div class="page-text">${page.text || ''}</div>
        </div>
      `;
    }

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${storybook.title}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
          }
          .cover {
            text-align: center;
            padding: 40px;
            page-break-after: always;
          }
          .cover h1 {
            font-size: 24px;
            margin-bottom: 20px;
          }
          .cover-image {
            max-width: 80%;
            max-height: 400px;
            margin-bottom: 20px;
          }
          .page {
            page-break-after: always;
            padding: 20px;
            position: relative;
          }
          .page-number {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 12px;
            color: #999;
          }
          .page-image {
            text-align: center;
            margin-bottom: 20px;
          }
          .page-image img {
            max-width: 100%;
            max-height: 400px;
          }
          .page-text {
            font-size: 14px;
            line-height: 1.5;
          }
        </style>
      </head>
      <body>
        <div class="cover">
          <h1>${storybook.title}</h1>
          ${storybook.coverImg ? `<img class="cover-image" src="${storybook.coverImg}" alt="Cover">` : ''}
          <p>${storybook.description || ''}</p>
        </div>
        ${pagesHtml}
      </body>
      </html>
    `;
  }
}
