<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="settings-header">
      <!-- 移动端顶部栏 -->
      <div v-if="isMobile" class="mobile-header">
        <button
          @click="goBack"
          class="mobile-back-btn"
        >
          <SvgIcon icon="ri:arrow-left-line" class="text-xl" />
        </button>
        <h1 class="mobile-title">设置中心</h1>
        <div class="mobile-placeholder"></div>
      </div>

      <!-- 桌面端标题 -->
      <div v-else class="desktop-header">
        <div class="header-content">
          <div class="header-icon">
            <SvgIcon icon="ri:settings-3-line" class="text-2xl text-primary-600" />
          </div>
          <div class="header-info">
            <h1 class="header-title">设置中心</h1>
            <p class="header-subtitle">管理您的偏好设置和账户信息</p>
          </div>
        </div>
        <button
          @click="goBack"
          class="back-btn"
          title="返回"
        >
          <SvgIcon icon="ri:close-line" class="text-xl" />
        </button>
      </div>
    </div>

    <!-- 标签页导航和内容 -->
    <div class="settings-content">
      <NTabs
        v-model:value="activeTab"
        type="line"
        animated
        :tabs-padding="20"
        class="settings-tabs"
        @update:value="switchTab"
      >
        <NTabPane name="general" tab="通用设置">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:settings-4-line" class="tab-icon" />
              <span class="tab-label">通用设置</span>
            </div>
          </template>
          <GeneralSettings />
        </NTabPane>

        <NTabPane name="profile" tab="个人资料">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:user-3-line" class="tab-icon" />
              <span class="tab-label">个人资料</span>
            </div>
          </template>
          <UserProfile />
        </NTabPane>

        <NTabPane name="wallet" tab="我的钱包">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:wallet-3-line" class="tab-icon" />
              <span class="tab-label">我的钱包</span>
            </div>
          </template>
          <WalletManagement />
        </NTabPane>

        <NTabPane name="history" tab="聊天历史">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:chat-history-line" class="tab-icon" />
              <span class="tab-label">聊天历史</span>
            </div>
          </template>
          <ChatHistory />
        </NTabPane>

        <NTabPane name="plugins" tab="插件管理">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:plug-line" class="tab-icon" />
              <span class="tab-label">插件管理</span>
            </div>
          </template>
          <PluginManager />
        </NTabPane>

        <NTabPane name="student-mode" tab="学生模式">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:user-settings-line" class="tab-icon" />
              <span class="tab-label">学生模式</span>
            </div>
          </template>
          <StudentMode />
        </NTabPane>

        <NTabPane name="notice" tab="网站公告">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:notification-3-line" class="tab-icon" />
              <span class="tab-label">网站公告</span>
            </div>
          </template>
          <NoticeContent />
        </NTabPane>

        <NTabPane
          v-if="showSignInTab"
          name="signin"
          tab="签到奖励"
        >
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:calendar-check-line" class="tab-icon" />
              <span class="tab-label">签到奖励</span>
            </div>
          </template>
          <SignInRewards />
        </NTabPane>

        <NTabPane name="password" tab="密码管理">
          <template #tab>
            <div class="tab-item">
              <SvgIcon icon="ri:lock-password-line" class="tab-icon" />
              <span class="tab-label">密码管理</span>
            </div>
          </template>
          <PasswordManagement />
        </NTabPane>
      </NTabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { NTabs, NTabPane } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAuthStore } from '@/store';
import { SvgIcon } from '@/components/common';

// 导入子组件
import GeneralSettings from './components/GeneralSettings.vue';
import UserProfile from './components/UserProfile.vue';
import WalletManagement from './components/WalletManagement.vue';
import ChatHistory from './components/ChatHistory.vue';
import PluginManager from './components/PluginManager.vue';
import StudentMode from './components/StudentMode.vue';
import NoticeContent from './components/NoticeContent.vue';
import SignInRewards from './components/SignInRewards.vue';
import PasswordManagement from './components/PasswordManagement.vue';

const router = useRouter();
const { isMobile } = useBasicLayout();
const authStore = useAuthStore();

// 状态管理
const activeTab = ref('general');

// 计算属性
const showSignInTab = computed(() => {
  return authStore.globalConfig?.signInStatus === '1';
});

// 方法
const switchTab = (tab: string) => {
  activeTab.value = tab;
};

const goBack = () => {
  router.back();
};
</script>

<style scoped>
.settings-page {
  @apply h-full flex flex-col;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* 页面头部样式 */
.settings-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .settings-header {
  background: rgba(17, 24, 39, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.mobile-header {
  @apply flex items-center justify-between p-4;
}

.mobile-back-btn {
  @apply p-3 rounded-xl transition-all duration-300;
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.mobile-back-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.mobile-title {
  @apply text-xl font-bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.mobile-placeholder {
  @apply w-12 h-12;
}

.desktop-header {
  @apply flex items-center justify-between p-8;
}

.header-content {
  @apply flex items-center space-x-6;
}

.header-icon {
  @apply w-16 h-16 rounded-2xl flex items-center justify-center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.header-info {
  @apply flex-1;
}

.header-title {
  @apply text-3xl font-bold mb-2;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .header-title {
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  @apply text-lg text-gray-600 dark:text-gray-300;
  font-weight: 500;
}

.back-btn {
  @apply p-3 rounded-xl transition-all duration-300;
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.back-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* 内容区域样式 */
.settings-content {
  @apply flex-1 overflow-hidden;
  margin: 0 20px 20px 20px;
}

.settings-tabs {
  @apply h-full;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dark .settings-tabs {
  background: rgba(17, 24, 39, 0.9);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 标签页样式 */
.tab-item {
  @apply flex items-center space-x-3 px-4 py-2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-icon {
  @apply text-lg;
  transition: all 0.3s ease;
}

.tab-label {
  @apply font-semibold text-sm;
  transition: all 0.3s ease;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .desktop-header {
    @apply hidden;
  }

  .settings-content {
    margin: 0 12px 12px 12px;
  }

  .settings-tabs {
    border-radius: 16px;
  }

  .tab-label {
    @apply hidden;
  }

  .tab-item {
    @apply justify-center px-3 py-3;
  }

  .tab-icon {
    @apply text-xl;
  }
}

@media (min-width: 769px) {
  .mobile-header {
    @apply hidden;
  }
}

/* 标签页内容区域 */
:deep(.n-tabs-content) {
  @apply h-full overflow-hidden;
  background: transparent;
}

:deep(.n-tab-pane) {
  @apply h-full overflow-y-auto;
  padding: 32px;
  background: transparent;
}

/* 移动端标签页内容间距调整 */
@media (max-width: 768px) {
  :deep(.n-tab-pane) {
    padding: 20px;
  }
}

/* 标签页导航栏样式优化 */
:deep(.n-tabs-nav) {
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  padding: 8px 0;
}

.dark :deep(.n-tabs-nav) {
  background: rgba(30, 41, 59, 0.8);
  border-bottom: 1px solid rgba(71, 85, 105, 0.5);
}

:deep(.n-tabs-nav-scroll-content) {
  padding: 0 20px;
}

:deep(.n-tabs-tab) {
  @apply transition-all duration-300;
  border-radius: 16px;
  margin: 0 4px;
  padding: 8px 0;
  position: relative;
  overflow: hidden;
}

:deep(.n-tabs-tab::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16px;
}

:deep(.n-tabs-tab:hover::before) {
  opacity: 1;
}

:deep(.n-tabs-tab:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

:deep(.n-tabs-tab:hover .tab-icon) {
  transform: scale(1.1);
  color: #6366f1;
}

:deep(.n-tabs-tab:hover .tab-label) {
  color: #6366f1;
}

:deep(.n-tabs-tab--active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

:deep(.n-tabs-tab--active .tab-icon),
:deep(.n-tabs-tab--active .tab-label) {
  color: white;
}

:deep(.n-tabs-tab--active::before) {
  opacity: 0;
}

/* 标签页指示器样式 */
:deep(.n-tabs-tab-pad) {
  display: none;
}
</style>
