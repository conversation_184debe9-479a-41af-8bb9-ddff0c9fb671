<template>
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
  >
    <Close
      size="18"
      class="absolute top-3 right-3 cursor-pointer z-30"
      @click="closeModal"
    />

    <div
      class="bg-white dark:bg-gray-900 w-full h-full p-4"
      :class="[isMobile ? 'flex-col' : 'flex']"
    >
      <div v-if="isMobile" class="p-2 w-full h-1/2" :class="{ 'preview-mode': isPreviewMode }">
        <iframe
          :srcDoc="localEditableText"
          class="box-border w-full h-full border rounded-md"
        ></iframe>
        <div v-if="isPreviewMode && historyVersions.length > 1" class="preview-controls">
          <div class="preview-nav">
            <button
              class="nav-button"
              :disabled="currentVersionIndex.value <= 0"
              @click="() => {
                if (currentVersionIndex > 0) {
                  currentVersionIndex--;
                  localEditableText.value = historyVersions.value[currentVersionIndex.value];
                }
              }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
            <span class="version-info">{{ currentVersionIndex + 1 }} / {{ historyVersions.length }}</span>
            <button
              class="nav-button"
              :disabled="currentVersionIndex.value >= historyVersions.value.length - 1"
              @click="() => {
                if (currentVersionIndex < historyVersions.length - 1) {
                  currentVersionIndex++;
                  localEditableText.value = historyVersions.value[currentVersionIndex.value];
                }
              }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <div
        class="p-2 flex flex-col"
        :class="[isMobile ? 'w-full h-1/2' : 'w-1/4']"
      >
        <textarea
          v-model="localEditableText"
          class="w-full h-full p-2 border rounded-md resize-none text-base dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
        ></textarea>
        <div class="mt-2 flex justify-between items-center">
          <div class="flex items-center">
            <button
              @click="handlePreview"
              class="px-4 py-2 shadow-sm bg-primary-600 hover:bg-primary-500 text-white rounded-md mr-2 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
              </svg>
              预览
            </button>
            <button
              @click="handleShare"
              :disabled="isSharing"
              class="px-4 py-2 shadow-sm bg-primary-600 hover:bg-primary-500 text-white rounded-md mr-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
              </svg>
              {{ isSharing ? '分享中...' : '分享' }}
            </button>
          </div>
          <div class="flex">
            <button
              @click="closeModal"
              class="px-4 py-2 shadow-sm ring-1 ring-inset bg-white ring-gray-300 hover:bg-gray-50 text-gray-900 rounded-md mr-4 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:ring-gray-700 dark:hover:ring-gray-600 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
              取消
            </button>
            <button
              @click="handleCopy"
              class="px-4 py-2 shadow-sm bg-primary-600 hover:bg-primary-500 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
              </svg>
              复制
            </button>
          </div>
        </div>
      </div>
      <div v-if="!isMobile" class="w-3/4 p-2" :class="{ 'preview-mode': isPreviewMode }">
        <iframe
          :srcDoc="localEditableText"
          class="box-border w-full h-full border rounded-md"
        ></iframe>
        <div v-if="isPreviewMode && historyVersions.length > 1" class="preview-controls">
          <div class="preview-nav">
            <button
              class="nav-button"
              :disabled="currentVersionIndex.value <= 0"
              @click="() => {
                if (currentVersionIndex > 0) {
                  currentVersionIndex--;
                  localEditableText.value = historyVersions.value[currentVersionIndex.value];
                }
              }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
            <span class="version-info">{{ currentVersionIndex + 1 }} / {{ historyVersions.length }}</span>
            <button
              class="nav-button"
              :disabled="currentVersionIndex.value >= historyVersions.value.length - 1"
              @click="() => {
                if (currentVersionIndex < historyVersions.length - 1) {
                  currentVersionIndex++;
                  localEditableText.value = historyVersions.value[currentVersionIndex.value];
                }
              }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分享对话框 -->
  <ShareDialog
    v-if="showShareDialog"
    :share-url="shareUrl"
    :on-close="() => showShareDialog = false"
    @close="showShareDialog = false"
  />
</template>

<script lang="ts" setup>
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { Close } from '@icon-park/vue-next';
import { ref, watch, watchEffect, onMounted, onUnmounted } from 'vue';
import { fetchCreateHtmlShare } from '@/api';
import ShareDialog from '@/components/ShareDialog.vue';

interface Props {
  text: string;
  close: () => void;
}

const props = defineProps<Props>();
const htmlPreviewRef = ref<HTMLIFrameElement | null>(null);
const localEditableText = ref(props.text);
const { isMobile } = useBasicLayout();

const isSharing = ref(false);
const shareCode = ref('');
const shareUrl = ref('');
const showShareDialog = ref(false);
const historyVersions = ref<string[]>([]);
const currentVersionIndex = ref(0);
const isPreviewMode = ref(false);

watch(localEditableText, () => {
  updatePreview();
});

watchEffect(() => {
  if (!isPreviewMode.value) {
    localEditableText.value = props.text;
  }
});

// 监听HTML编辑器更新事件
const handleHtmlEditorUpdate = (event: CustomEvent) => {
  if (!event.detail || !event.detail.code) return;

  const newCode = event.detail.code;
  console.log('HTML模态框收到HTML编辑器更新事件，代码长度:', newCode.length);

  // 更新编辑器内容
  localEditableText.value = newCode;

  // 添加到历史版本
  if (!historyVersions.value.includes(newCode)) {
    historyVersions.value.push(newCode);
    currentVersionIndex.value = historyVersions.value.length - 1;

    // 限制历史记录数量，最多保存10个版本
    if (historyVersions.value.length > 10) {
      historyVersions.value = historyVersions.value.slice(-10);
      currentVersionIndex.value = historyVersions.value.length - 1;
    }

    // 保存到本地存储
    try {
      localStorage.setItem('html-history-versions', JSON.stringify(historyVersions.value));
    } catch (storageError) {
      console.error('保存HTML历史版本失败:', storageError);
    }
  }

  window.$message?.success('HTML内容已同步更新');
};

// 初始化历史版本记录
onMounted(() => {
  // 将当前版本添加到历史记录
  historyVersions.value = [props.text];
  currentVersionIndex.value = 0;

  // 尝试从本地存储加载历史版本
  try {
    const savedVersions = localStorage.getItem('html-history-versions');
    if (savedVersions) {
      const parsedVersions = JSON.parse(savedVersions);
      if (Array.isArray(parsedVersions) && parsedVersions.length > 0) {
        // 确保当前版本也在历史记录中
        if (!parsedVersions.includes(props.text)) {
          parsedVersions.push(props.text);
        }
        historyVersions.value = parsedVersions;
        currentVersionIndex.value = historyVersions.value.indexOf(props.text);
      }
    }
  } catch (error) {
    console.error('加载HTML历史版本失败:', error);
  }

  // 添加HTML编辑器更新事件监听
  window.addEventListener('update-html-editor', handleHtmlEditorUpdate as EventListener);
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('update-html-editor', handleHtmlEditorUpdate as EventListener);
  document.removeEventListener('keydown', handleKeyNavigation);
});

const updatePreview = () => {
  if (htmlPreviewRef.value) {
    const iframeDocument = htmlPreviewRef.value.contentDocument;
    if (iframeDocument) {
      iframeDocument.body.innerHTML = localEditableText.value;
    }
  }
};

const closeModal = () => {
  props.close();
};

const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(localEditableText.value);
    window.$message?.success('内容已复制到剪贴板');
  } catch (err) {
    window.$message?.error('复制失败');
  }
};

// 预览历史版本
const handlePreview = () => {
  isPreviewMode.value = !isPreviewMode.value;

  if (isPreviewMode.value) {
    // 进入预览模式，显示历史版本选择界面
    window.$message?.info('预览模式：使用左右箭头键浏览历史版本');

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyNavigation);
  } else {
    // 退出预览模式，恢复当前编辑内容
    document.removeEventListener('keydown', handleKeyNavigation);
    localEditableText.value = props.text;
  }
};

// 处理键盘导航
const handleKeyNavigation = (event: KeyboardEvent) => {
  if (!isPreviewMode.value || historyVersions.value.length <= 1) return;

  if (event.key === 'ArrowLeft') {
    // 上一个版本
    if (currentVersionIndex.value > 0) {
      currentVersionIndex.value--;
      localEditableText.value = historyVersions.value[currentVersionIndex.value];
      window.$message?.info(`查看历史版本: ${currentVersionIndex.value + 1}/${historyVersions.value.length}`);
    }
  } else if (event.key === 'ArrowRight') {
    // 下一个版本
    if (currentVersionIndex.value < historyVersions.value.length - 1) {
      currentVersionIndex.value++;
      localEditableText.value = historyVersions.value[currentVersionIndex.value];
      window.$message?.info(`查看历史版本: ${currentVersionIndex.value + 1}/${historyVersions.value.length}`);
    }
  }
};

const handleShare = async () => {
  if (!localEditableText.value) {
    window.$message?.error('HTML内容不能为空');
    return;
  }

  try {
    isSharing.value = true;
    const response = await fetchCreateHtmlShare({
      htmlContent: localEditableText.value
    });

    console.log('分享响应数据:', response);
    // 处理不同的响应数据结构
    const shareCodeValue = response.data?.data?.data?.shareCode || response.data?.data?.shareCode || response.data?.shareCode;

    if (shareCodeValue) {
      shareCode.value = shareCodeValue;
      // 判断是否使用哈希模式
      const isHashMode = window.location.href.includes('#/');
      shareUrl.value = isHashMode
        ? `${window.location.origin}/#/share/${shareCode.value}`
        : `${window.location.origin}/share/${shareCode.value}`;

      // 显示分享对话框，而不是直接复制到剪贴板
      showShareDialog.value = true;
      window.$message?.success('分享链接已生成');

      // 保存当前版本到历史记录
      if (!historyVersions.value.includes(localEditableText.value)) {
        historyVersions.value.push(localEditableText.value);
        currentVersionIndex.value = historyVersions.value.length - 1;

        // 限制历史记录数量，最多保存10个版本
        if (historyVersions.value.length > 10) {
          historyVersions.value = historyVersions.value.slice(-10);
          currentVersionIndex.value = historyVersions.value.length - 1;
        }

        // 保存到本地存储
        try {
          localStorage.setItem('html-history-versions', JSON.stringify(historyVersions.value));
        } catch (storageError) {
          console.error('保存HTML历史版本失败:', storageError);
        }
      }
    } else {
      window.$message?.error('创建分享失败');
    }
  } catch (err) {
    console.error('分享失败:', err);
    window.$message?.error('分享失败');
  } finally {
    isSharing.value = false;
  }
};
</script>

<style lang="less" scoped>
.preview-mode {
  position: relative;

  &::after {
    content: '预览模式';
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
  }
}

.preview-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;

  .preview-nav {
    display: flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 4px 8px;
    border-radius: 4px;

    .nav-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px 8px;
      display: flex;
      align-items: center;

      &:hover {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .version-info {
      margin: 0 8px;
      font-size: 12px;
    }
  }
}
</style>
