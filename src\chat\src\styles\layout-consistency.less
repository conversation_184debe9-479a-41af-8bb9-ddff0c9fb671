/* 布局一致性样式 */

/* 确保三栏布局的一致性 */
.three-column-layout {
  display: flex;
  height: 100%;
  gap: 12px; /* 统一的间距 */
}

/* 三栏布局的间距一致性 */
.column-gap {
  gap: 12px; /* 与 gap-3 保持一致，3 * 4px = 12px */
}

/* 三栏布局的左侧间距 */
.column-gap-left {
  padding-left: 12px; /* 与 gap-3 保持一致，3 * 4px = 12px */
}

/* 侧边栏收起时的布局样式 */
.sidebar-collapsed {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* 确保侧边栏收起时聊天区域完全覆盖 */
.sidebar-collapsed .chat-area {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* 三栏布局的右侧间距 */
.column-gap-right {
  padding-right: 12px; /* 与 gap-3 保持一致，3 * 4px = 12px */
}

/* 确保所有卡片具有一致的圆角和阴影 */
.consistent-card {
  border-radius: 0.5rem;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 确保所有卡片头部具有一致的样式 */
.consistent-card-header {
  height: 4rem;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(to right, #ffffff, #f9fafb);
}

.dark .consistent-card-header {
  background: linear-gradient(to right, #1f2937, #111827);
  border-bottom: 1px solid var(--dark-border-color);
}

/* 确保所有卡片内容区域具有一致的内边距 */
.consistent-card-content {
  flex: 1;
  overflow: auto;
  padding: 1rem;
}

/* 确保所有滚动条具有一致的样式 */
.consistent-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.consistent-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.consistent-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.consistent-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

.dark .consistent-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 统一的CSS变量 */
:root {
  --border-color: rgba(229, 231, 235, 1);
  --dark-border-color: rgba(55, 65, 81, 1);
  --card-padding: 1rem;
  --column-gap: 0.75rem;
  --row-gap: 0.75rem;
}

/* 确保所有卡片底部具有一致的样式 */
.consistent-card-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--border-color);
  background-color: #f9fafb;
}

.dark .consistent-card-footer {
  background-color: #1f2937;
  border-top: 1px solid var(--dark-border-color);
}

/* 确保所有输入框具有一致的样式 */
.consistent-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  background-color: white;
  transition: all 0.3s ease;
}

.dark .consistent-input {
  background-color: #374151;
  border-color: var(--dark-border-color);
  color: #e5e7eb;
}

.consistent-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* 确保所有按钮具有一致的样式 */
.consistent-button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 确保所有图标具有一致的大小 */
.consistent-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* 确保所有标题具有一致的样式 */
.consistent-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.dark .consistent-title {
  color: #f9fafb;
}

/* 确保所有文本具有一致的样式 */
.consistent-text {
  font-size: 0.875rem;
  color: #4b5563;
}

.dark .consistent-text {
  color: #d1d5db;
}

/* 确保所有分割线具有一致的样式 */
.consistent-divider {
  height: 1px;
  width: 100%;
  background-color: var(--border-color);
  margin: 0.75rem 0;
}

.dark .consistent-divider {
  background-color: var(--dark-border-color);
}

/* 确保所有标签具有一致的样式 */
.consistent-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #4b5563;
}

.dark .consistent-tag {
  background-color: #374151;
  color: #d1d5db;
}

/* 确保所有徽章具有一致的样式 */
.consistent-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  background-color: #ef4444;
  color: white;
}

/* 确保所有提示具有一致的样式 */
.consistent-tooltip {
  position: absolute;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  background-color: #1f2937;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 50;
  max-width: 16rem;
}

/* 确保所有菜单具有一致的样式 */
.consistent-menu {
  position: absolute;
  padding: 0.5rem 0;
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  min-width: 10rem;
}

.dark .consistent-menu {
  background-color: #1f2937;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

.consistent-menu-item {
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.consistent-menu-item:hover {
  background-color: #f3f4f6;
}

.dark .consistent-menu-item:hover {
  background-color: #374151;
}

/* 确保所有表单具有一致的样式 */
.consistent-form-group {
  margin-bottom: 1rem;
}

.consistent-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.dark .consistent-form-label {
  color: #d1d5db;
}

.consistent-form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid var(--border-color);
  background-color: white;
  transition: all 0.3s ease;
}

.dark .consistent-form-input {
  background-color: #374151;
  border-color: var(--dark-border-color);
  color: #e5e7eb;
}

.consistent-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.consistent-form-helper {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.dark .consistent-form-helper {
  color: #9ca3af;
}

.consistent-form-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
}

/* 确保所有加载状态具有一致的样式 */
.consistent-loading {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.consistent-loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  border: 2px solid rgba(156, 163, 175, 0.3);
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 确保所有空状态具有一致的样式 */
.consistent-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.consistent-empty-icon {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.consistent-empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.dark .consistent-empty-title {
  color: #d1d5db;
}

.consistent-empty-description {
  font-size: 0.875rem;
  color: #6b7280;
  max-width: 20rem;
}

.dark .consistent-empty-description {
  color: #9ca3af;
}
