<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';
import { NInput, NButton, NSpace, NDivider, NCollapse, NCollapseItem, NForm, NFormItem, NSelect, NTabs, NTabPane } from 'naive-ui';

const props = defineProps<{
  projectData: any;
}>();

// 故事大纲结构
const outlineStructure = reactive({
  title: '',
  setting: {
    place: '',
    time: '',
    background: ''
  },
  characters: [
    { name: '', traits: '', role: '主角' }
  ],
  plot: {
    beginning: '',
    middle: '',
    climax: '',
    ending: ''
  },
  theme: '',
  notes: ''
});

// 当前编辑的角色索引
const currentCharacterIndex = ref(0);

// 大纲模板
const outlineTemplates = [
  {
    label: '基础三幕结构',
    description: '包括开端（介绍主角和背景）、中段（主角面临挑战）和结尾（解决问题和学到的教训）'
  },
  {
    label: '英雄冒险',
    description: '包括主角、召唤冒险、遇到帮手、面临挑战、克服困难和凯旋归来'
  },
  {
    label: '友谊故事',
    description: '包括角色相遇、建立友谊、友谊受到考验、解决误会和友谊加深'
  },
  {
    label: '成长故事',
    description: '包括主角的初始状态、面临挑战、学习新技能、克服困难和成长蜕变'
  },
  {
    label: '解决问题',
    description: '包括发现问题、尝试解决、失败尝试、获得启示和最终解决'
  }
];

// 角色类型选项
const characterRoles = [
  { label: '主角', value: '主角' },
  { label: '配角', value: '配角' },
  { label: '帮手', value: '帮手' },
  { label: '对手', value: '对手' },
  { label: '导师', value: '导师' }
];

// 添加新角色
const addCharacter = () => {
  outlineStructure.characters.push({
    name: '',
    traits: '',
    role: '配角'
  });
  currentCharacterIndex.value = outlineStructure.characters.length - 1;
};

// 删除角色
const removeCharacter = (index) => {
  if (outlineStructure.characters.length > 1) {
    outlineStructure.characters.splice(index, 1);
    if (currentCharacterIndex.value >= outlineStructure.characters.length) {
      currentCharacterIndex.value = outlineStructure.characters.length - 1;
    }
  } else {
    window.$message?.warning('至少需要保留一个角色');
  }
};

// 保存大纲到项目
const saveOutlineToProject = () => {
  if (!outlineStructure.title.trim()) {
    window.$message?.warning('请输入故事标题');
    return;
  }

  // 格式化大纲内容
  const formattedOutline = formatOutline();

  // 创建新大纲对象
  const newOutline = {
    id: Date.now(),
    title: outlineStructure.title,
    structure: JSON.parse(JSON.stringify(outlineStructure)), // 深拷贝
    content: formattedOutline,
    timestamp: new Date().toISOString(),
    source: 'magic-pen' // 标记来源为AI魔笔
  };

  // 确保项目数据中有aiMagicPen.outlines数组
  if (!props.projectData.aiMagicPen) {
    props.projectData.aiMagicPen = { inspirations: [], outlines: [], questions: [] };
  }
  if (!props.projectData.aiMagicPen.outlines) {
    props.projectData.aiMagicPen.outlines = [];
  }

  // 添加到新数据结构
  props.projectData.aiMagicPen.outlines.push(newOutline);

  // 为了向后兼容，也添加到旧的位置
  if (!props.projectData.outlines) {
    props.projectData.outlines = [];
  }
  props.projectData.outlines.push(newOutline);

  // 同步到项目的outline属性
  syncToProjectOutline();

  // 更新本地存储
  try {
    localStorage.setItem('project-data', JSON.stringify(props.projectData));

    // 清除临时大纲数据，因为已经正式保存了
    localStorage.removeItem('temp-outline-data');
    console.log('Temporary outline data cleared after saving');

    // 清除自动保存计时器
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      autoSaveTimer = null;
    }
  } catch (e) {
    console.warn('Failed to save project data to localStorage', e);
  }

  window.$message?.success('大纲已保存到项目中');
};

// 格式化大纲为文本
const formatOutline = () => {
  let outline = `# ${outlineStructure.title}\n\n`;

  outline += `## 故事设定\n`;
  outline += `- 地点：${outlineStructure.setting.place || '未设定'}\n`;
  outline += `- 时间：${outlineStructure.setting.time || '未设定'}\n`;
  outline += `- 背景：${outlineStructure.setting.background || '未设定'}\n\n`;

  outline += `## 角色\n`;
  outlineStructure.characters.forEach(char => {
    outline += `- ${char.name || '未命名角色'} (${char.role})：${char.traits || '未描述特点'}\n`;
  });
  outline += '\n';

  outline += `## 故事情节\n`;
  outline += `### 开端\n${outlineStructure.plot.beginning || '未设定'}\n\n`;
  outline += `### 发展\n${outlineStructure.plot.middle || '未设定'}\n\n`;
  outline += `### 高潮\n${outlineStructure.plot.climax || '未设定'}\n\n`;
  outline += `### 结局\n${outlineStructure.plot.ending || '未设定'}\n\n`;

  outline += `## 主题\n${outlineStructure.theme || '未设定'}\n\n`;

  if (outlineStructure.notes) {
    outline += `## 备注\n${outlineStructure.notes}\n`;
  }

  return outline;
};

// 重置大纲
const resetOutline = () => {
  Object.assign(outlineStructure, {
    title: '',
    setting: {
      place: '',
      time: '',
      background: ''
    },
    characters: [
      { name: '', traits: '', role: '主角' }
    ],
    plot: {
      beginning: '',
      middle: '',
      climax: '',
      ending: ''
    },
    theme: '',
    notes: ''
  });
  currentCharacterIndex.value = 0;
};

// 计算已保存的大纲列表
const savedOutlines = computed(() => {
  // 优先使用新数据结构
  if (props.projectData.aiMagicPen && props.projectData.aiMagicPen.outlines) {
    return props.projectData.aiMagicPen.outlines;
  }
  // 向后兼容，使用旧数据结构
  return props.projectData.outlines || [];
});

// 自动保存计时器
let autoSaveTimer = null;

// 标记是否正在从创作步骤同步数据，避免循环同步
let isSyncingFromCreator = false;

// 自动保存大纲数据
const autoSaveOutline = () => {
  // 如果正在从创作步骤同步数据，则不触发自动保存
  if (isSyncingFromCreator) {
    console.log('Skipping auto-save because data is being synced from creator');
    return;
  }

  // 清除之前的计时器
  if (autoSaveTimer) {
    clearTimeout(autoSaveTimer);
  }

  // 设置新的计时器，延迟2秒保存
  autoSaveTimer = setTimeout(() => {
    // 只有当标题不为空时才保存
    if (outlineStructure.title.trim()) {
      // 创建临时大纲对象
      const tempOutline = {
        id: 'temp-outline',
        title: outlineStructure.title,
        structure: JSON.parse(JSON.stringify(outlineStructure)), // 深拷贝
        content: formatOutline(),
        timestamp: new Date().toISOString(),
        source: 'magic-pen',
        isTemp: true // 标记为临时数据
      };

      // 保存到localStorage
      try {
        localStorage.setItem('temp-outline-data', JSON.stringify(tempOutline));
        console.log('Auto-saved outline data to localStorage');

        // 同步到项目的outline属性中
        syncToProjectOutline();
      } catch (e) {
        console.warn('Failed to auto-save outline data to localStorage', e);
      }
    }
  }, 2000);
};

// 同步大纲数据到项目的outline属性
const syncToProjectOutline = () => {
  // 如果正在从创作步骤同步数据，则不触发同步到项目
  if (isSyncingFromCreator) {
    console.log('Skipping sync to project because data is being synced from creator');
    return;
  }

  console.log('Syncing outline data to project outline');

  // 确保项目数据中有outline对象
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }

  // 同步标题
  props.projectData.title = outlineStructure.title;

  // 同步主题和主要理念
  // 保留原有的mainIdea，如果没有则使用theme
  if (!props.projectData.outline.mainIdea) {
    props.projectData.outline.mainIdea = outlineStructure.theme;
  }
  props.projectData.outline.theme = outlineStructure.theme;

  // 保留原有的ageGroup
  // 不覆盖已有的ageGroup值
  if (!props.projectData.outline.ageGroup) {
    props.projectData.outline.ageGroup = '';
  }

  // 同步情节
  props.projectData.outline.beginning = outlineStructure.plot.beginning;
  props.projectData.outline.middle = outlineStructure.plot.middle;
  props.projectData.outline.climax = outlineStructure.plot.climax;
  props.projectData.outline.ending = outlineStructure.plot.ending;

  // 同步故事设定
  props.projectData.outline.setting = JSON.parse(JSON.stringify(outlineStructure.setting));

  // 同步角色设定
  props.projectData.outline.characters = JSON.parse(JSON.stringify(outlineStructure.characters));

  // 同步备注
  props.projectData.notes = outlineStructure.notes;

  // 同时更新localStorage中的项目数据，确保数据一致性
  try {
    localStorage.setItem('project-data', JSON.stringify(props.projectData));
    console.log('Project data also updated in localStorage');
  } catch (e) {
    console.warn('Failed to update project data in localStorage', e);
  }

  console.log('Synced outline data to project outline');
};

// 监听大纲结构变化，自动保存
watch(outlineStructure, () => {
  // 如果正在从创作步骤同步数据，则不触发自动保存
  if (!isSyncingFromCreator) {
    autoSaveOutline();
  }
}, { deep: true });

// 从项目outline属性加载数据
const loadFromProjectOutline = () => {
  // 检查项目是否有outline数据
  if (props.projectData.outline) {
    console.log('Loading data from project outline');

    // 加载标题
    if (props.projectData.title) {
      outlineStructure.title = props.projectData.title;
    }

    // 加载主题
    if (props.projectData.outline.theme) {
      outlineStructure.theme = props.projectData.outline.theme;
    } else if (props.projectData.outline.mainIdea) {
      outlineStructure.theme = props.projectData.outline.mainIdea;
    }

    // 加载情节
    if (props.projectData.outline.beginning) {
      outlineStructure.plot.beginning = props.projectData.outline.beginning;
    }
    if (props.projectData.outline.middle) {
      outlineStructure.plot.middle = props.projectData.outline.middle;
    }
    if (props.projectData.outline.climax) {
      outlineStructure.plot.climax = props.projectData.outline.climax;
    }
    if (props.projectData.outline.ending) {
      outlineStructure.plot.ending = props.projectData.outline.ending;
    }

    // 加载故事设定
    if (props.projectData.outline.setting) {
      outlineStructure.setting.place = props.projectData.outline.setting.place || '';
      outlineStructure.setting.time = props.projectData.outline.setting.time || '';
      outlineStructure.setting.background = props.projectData.outline.setting.background || '';
    }

    // 加载角色设定
    if (props.projectData.outline.characters && props.projectData.outline.characters.length > 0) {
      outlineStructure.characters = JSON.parse(JSON.stringify(props.projectData.outline.characters));
    }

    // 加载备注
    if (props.projectData.notes) {
      outlineStructure.notes = props.projectData.notes;
    }

    console.log('Project outline data loaded successfully');
    return true;
  }
  return false;
};

// 组件挂载时加载数据
onMounted(() => {
  // 尝试从localStorage加载项目数据
  try {
    // 先尝试加载临时大纲数据
    const tempOutlineData = localStorage.getItem('temp-outline-data');
    if (tempOutlineData) {
      const tempOutline = JSON.parse(tempOutlineData);

      // 如果有临时数据，恢复到当前大纲结构
      if (tempOutline && tempOutline.structure) {
        console.log('Found temporary outline data, restoring...');

        // 恢复标题
        outlineStructure.title = tempOutline.structure.title || '';

        // 恢复设定
        if (tempOutline.structure.setting) {
          outlineStructure.setting.place = tempOutline.structure.setting.place || '';
          outlineStructure.setting.time = tempOutline.structure.setting.time || '';
          outlineStructure.setting.background = tempOutline.structure.setting.background || '';
        }

        // 恢复角色
        if (tempOutline.structure.characters && tempOutline.structure.characters.length > 0) {
          outlineStructure.characters = JSON.parse(JSON.stringify(tempOutline.structure.characters));
        }

        // 恢复情节
        if (tempOutline.structure.plot) {
          outlineStructure.plot.beginning = tempOutline.structure.plot.beginning || '';
          outlineStructure.plot.middle = tempOutline.structure.plot.middle || '';
          outlineStructure.plot.climax = tempOutline.structure.plot.climax || '';
          outlineStructure.plot.ending = tempOutline.structure.plot.ending || '';
        }

        // 恢复主题和备注
        outlineStructure.theme = tempOutline.structure.theme || '';
        outlineStructure.notes = tempOutline.structure.notes || '';

        console.log('Temporary outline data restored successfully');
      }
    } else {
      // 如果没有临时数据，尝试从项目outline属性中加载
      const outlineLoaded = loadFromProjectOutline();

      // 如果项目outline也没有数据，尝试从localStorage加载
      if (!outlineLoaded) {
        const savedData = localStorage.getItem('project-data');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          if (parsedData && parsedData.id === props.projectData.id) {
            console.log('Found saved project data in localStorage');
          }
        }
      }
    }
  } catch (e) {
    console.warn('Failed to load outline data from localStorage', e);
  }
});

// 监听项目outline变化
watch(() => props.projectData.outline, (newOutline) => {
  if (newOutline) {
    // 只有当AI魔笔大纲为空时，才从项目outline加载数据
    if (!outlineStructure.title && !outlineStructure.theme && !outlineStructure.plot.beginning) {
      loadFromProjectOutline();
    }
  }
}, { deep: true });

// 添加对localStorage中临时大纲数据的监听
// 使用定时器定期检查localStorage中的临时大纲数据
let tempOutlineCheckTimer = null;

// 开始监听临时大纲数据
const startTempOutlineCheck = () => {
  // 清除之前的计时器
  if (tempOutlineCheckTimer) {
    clearInterval(tempOutlineCheckTimer);
  }

  // 设置新的计时器，每2秒检查一次
  tempOutlineCheckTimer = setInterval(() => {
    try {
      const tempOutlineData = localStorage.getItem('temp-outline-data');
      if (tempOutlineData) {
        const tempOutline = JSON.parse(tempOutlineData);

        // 检查是否是来自创作步骤的数据
        if (tempOutline && tempOutline.source === 'creator') {
          console.log('Found temporary outline data from creator, updating...');

          // 设置标记，表示正在从创作步骤同步数据
          isSyncingFromCreator = true;

          try {
            // 更新大纲结构
            if (tempOutline.structure) {
              // 更新标题
              outlineStructure.title = tempOutline.structure.title || '';

              // 更新主题
              outlineStructure.theme = tempOutline.structure.theme || tempOutline.structure.mainIdea || '';

              // 更新情节
              if (tempOutline.structure.plot) {
                outlineStructure.plot.beginning = tempOutline.structure.plot.beginning || '';
                outlineStructure.plot.middle = tempOutline.structure.plot.middle || '';
                outlineStructure.plot.climax = tempOutline.structure.plot.climax || '';
                outlineStructure.plot.ending = tempOutline.structure.plot.ending || '';
              } else {
                // 兼容旧格式，直接从outline对象读取
                outlineStructure.plot.beginning = tempOutline.structure.beginning || '';
                outlineStructure.plot.middle = tempOutline.structure.middle || '';
                outlineStructure.plot.climax = tempOutline.structure.climax || '';
                outlineStructure.plot.ending = tempOutline.structure.ending || '';
              }

              // 更新设定
              if (tempOutline.structure.setting) {
                outlineStructure.setting = JSON.parse(JSON.stringify(tempOutline.structure.setting));
              }

              // 更新角色
              if (tempOutline.structure.characters && tempOutline.structure.characters.length > 0) {
                outlineStructure.characters = JSON.parse(JSON.stringify(tempOutline.structure.characters));
              }

              // 更新备注
              outlineStructure.notes = tempOutline.structure.notes || '';

              console.log('Updated outline structure from creator data');

              // 同步到项目的outline属性
              syncToProjectOutline();

              console.log('Data synced from creator to AI Magic Pen');
            }
          } finally {
            // 确保无论如何都会重置标记
            setTimeout(() => {
              isSyncingFromCreator = false;
              console.log('Reset syncing from creator flag');
            }, 100);
          }
        }
      }
    } catch (e) {
      console.warn('Failed to check temporary outline data', e);
    }
  }, 2000);
};

// 组件挂载时开始监听
onMounted(() => {
  startTempOutlineCheck();
});

// 组件卸载时停止监听
onUnmounted(() => {
  if (tempOutlineCheckTimer) {
    clearInterval(tempOutlineCheckTimer);
  }
});
</script>

<template>
  <div class="story-outline-tab">
    <div class="outline-header">
      <h3 class="outline-title">故事大纲</h3>
      <p class="outline-subtitle">帮助小朋友梳理故事结构和创作思路</p>
      <div class="auto-save-hint">
        <span class="auto-save-icon">🔄</span>
        <span class="auto-save-text">内容会实时双向同步，与创作步骤中的大纲保持一致</span>
      </div>
    </div>

    <NCollapse class="structure-collapse">
      <NCollapseItem title="故事结构参考" name="templates">
        <div class="outline-templates">
          <div
            v-for="(template, index) in outlineTemplates"
            :key="template.label"
            class="template-card"
          >
            <div class="template-icon">{{ index === 0 ? '📝' : index === 1 ? '🦸‍♂️' : index === 2 ? '🤝' : index === 3 ? '🌱' : '🔍' }}</div>
            <div class="template-content">
              <div class="template-title">{{ template.label }}</div>
              <div class="template-description">{{ template.description }}</div>
            </div>
          </div>
        </div>
      </NCollapseItem>
    </NCollapse>

    <NDivider class="section-divider" />

    <div class="outline-form-container">
      <!-- 故事标题 -->
      <div class="form-section title-section">
        <div class="section-header">
          <div class="section-icon">✨</div>
          <h4 class="section-title">故事标题</h4>
        </div>
        <NInput
          v-model:value="outlineStructure.title"
          placeholder="给你的故事起个名字"
          class="title-input"
        />
      </div>

      <!-- 故事设定 -->
      <div class="form-section setting-section">
        <div class="section-header">
          <div class="section-icon">🏞️</div>
          <h4 class="section-title">故事设定</h4>
        </div>
        <div class="setting-inputs">
          <div class="setting-input-group">
            <div class="input-label">故事发生的地点</div>
            <NInput
              v-model:value="outlineStructure.setting.place"
              placeholder="例如：魔法森林、太空站、海底城市"
              class="setting-input"
            />
          </div>

          <div class="setting-input-group">
            <div class="input-label">故事发生的时间</div>
            <NInput
              v-model:value="outlineStructure.setting.time"
              placeholder="例如：很久以前、未来世界、四季变换"
              class="setting-input"
            />
          </div>

          <div class="setting-input-group">
            <div class="input-label">故事的背景情况</div>
            <NInput
              v-model:value="outlineStructure.setting.background"
              type="textarea"
              placeholder="例如：这是一个充满魔法的世界，动物们可以说话..."
              :autosize="{ minRows: 2, maxRows: 4 }"
              class="setting-input"
            />
          </div>
        </div>
      </div>

      <!-- 角色设定 -->
      <div class="form-section character-section">
        <div class="section-header">
          <div class="section-icon">👤</div>
          <h4 class="section-title">角色设定</h4>
        </div>

        <div class="character-tabs-container">
          <div class="character-tabs">
            <div
              v-for="(char, index) in outlineStructure.characters"
              :key="index"
              class="character-tab"
              :class="{ active: currentCharacterIndex === index }"
              @click="currentCharacterIndex = index"
            >
              <div class="character-tab-content">
                <span class="character-emoji">{{ char.role === '主角' ? '🌟' : char.role === '对手' ? '😈' : char.role === '帮手' ? '🤝' : char.role === '导师' ? '🧙' : '👤' }}</span>
                <span>{{ char.name || `角色 ${index + 1}` }}</span>
              </div>
            </div>
            <div class="character-tab add-tab" @click="addCharacter">
              <span>+</span>
            </div>
          </div>
        </div>

        <div class="character-form">
          <div class="character-form-header">
            <div class="character-role-badge" :class="outlineStructure.characters[currentCharacterIndex].role">
              {{ outlineStructure.characters[currentCharacterIndex].role }}
            </div>
          </div>

          <div class="character-inputs">
            <div class="character-input-group">
              <div class="input-label">角色名字</div>
              <NInput
                v-model:value="outlineStructure.characters[currentCharacterIndex].name"
                placeholder="给你的角色起个名字"
                class="character-input"
              />
            </div>

            <div class="character-input-group">
              <div class="input-label">角色类型</div>
              <NSelect
                v-model:value="outlineStructure.characters[currentCharacterIndex].role"
                :options="characterRoles"
                placeholder="选择角色类型"
                class="character-input"
              />
            </div>

            <div class="character-input-group">
              <div class="input-label">角色特点</div>
              <NInput
                v-model:value="outlineStructure.characters[currentCharacterIndex].traits"
                type="textarea"
                placeholder="描述角色的特点（例如：勇敢、聪明、害羞、好奇心强...）"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="character-input"
              />
            </div>

            <div class="character-actions" v-if="outlineStructure.characters.length > 1">
              <NButton
                @click="removeCharacter(currentCharacterIndex)"
                size="small"
                type="error"
                class="remove-character-btn"
              >
                <span class="button-icon">🗑️</span>
                <span>删除角色</span>
              </NButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 故事情节 -->
      <div class="form-section plot-section">
        <div class="section-header">
          <div class="section-icon">📚</div>
          <h4 class="section-title">故事情节</h4>
        </div>

        <div class="plot-timeline">
          <div class="plot-timeline-item">
            <div class="plot-timeline-icon">1</div>
            <div class="plot-timeline-content">
              <div class="plot-timeline-header">
                <div class="plot-timeline-title">开端</div>
                <div class="plot-timeline-subtitle">引入角色和背景</div>
              </div>
              <NInput
                v-model:value="outlineStructure.plot.beginning"
                type="textarea"
                placeholder="故事是如何开始的？介绍主角和故事背景"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="plot-input"
              />
            </div>
          </div>

          <div class="plot-timeline-connector"></div>

          <div class="plot-timeline-item">
            <div class="plot-timeline-icon">2</div>
            <div class="plot-timeline-content">
              <div class="plot-timeline-header">
                <div class="plot-timeline-title">发展</div>
                <div class="plot-timeline-subtitle">主角面临的挑战</div>
              </div>
              <NInput
                v-model:value="outlineStructure.plot.middle"
                type="textarea"
                placeholder="主角遇到了什么问题或挑战？发生了什么事情？"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="plot-input"
              />
            </div>
          </div>

          <div class="plot-timeline-connector"></div>

          <div class="plot-timeline-item">
            <div class="plot-timeline-icon">3</div>
            <div class="plot-timeline-content">
              <div class="plot-timeline-header">
                <div class="plot-timeline-title">高潮</div>
                <div class="plot-timeline-subtitle">故事的转折点</div>
              </div>
              <NInput
                v-model:value="outlineStructure.plot.climax"
                type="textarea"
                placeholder="故事中最紧张或最重要的时刻是什么？"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="plot-input"
              />
            </div>
          </div>

          <div class="plot-timeline-connector"></div>

          <div class="plot-timeline-item">
            <div class="plot-timeline-icon">4</div>
            <div class="plot-timeline-content">
              <div class="plot-timeline-header">
                <div class="plot-timeline-title">结局</div>
                <div class="plot-timeline-subtitle">问题如何解决</div>
              </div>
              <NInput
                v-model:value="outlineStructure.plot.ending"
                type="textarea"
                placeholder="故事如何结束？主角学到了什么？"
                :autosize="{ minRows: 2, maxRows: 4 }"
                class="plot-input"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 故事主题 -->
      <div class="form-section theme-section">
        <div class="section-header">
          <div class="section-icon">💫</div>
          <h4 class="section-title">故事主题</h4>
        </div>

        <div class="theme-input-wrapper">
          <NInput
            v-model:value="outlineStructure.theme"
            placeholder="这个故事想要表达什么？（例如：友谊的重要性、勇气、诚实...）"
            class="theme-input"
          />
        </div>
      </div>

      <!-- 备注 -->
      <div class="form-section notes-section">
        <div class="section-header">
          <div class="section-icon">📌</div>
          <h4 class="section-title">备注</h4>
        </div>

        <div class="notes-input-wrapper">
          <NInput
            v-model:value="outlineStructure.notes"
            type="textarea"
            placeholder="记录你的其他想法、创意或备注..."
            :autosize="{ minRows: 2, maxRows: 4 }"
            class="notes-input"
          />
        </div>
      </div>
    </div>

    <div class="form-actions">
      <div class="form-tips">
        <div class="tip-icon">💡</div>
        <div class="tip-text">提示：保存后的大纲可以在下方查看，也可以继续编辑完善</div>
      </div>
      <div class="action-buttons">
        <NButton @click="resetOutline" size="small" class="reset-button">
          <span class="button-icon">🔄</span>
          <span>重置大纲</span>
        </NButton>
        <NButton type="primary" @click="saveOutlineToProject" class="save-button">
          <span class="button-icon">💾</span>
          <span>保存大纲</span>
        </NButton>
      </div>
    </div>

    <div v-if="savedOutlines.length > 0" class="saved-outlines">
      <div class="saved-outlines-header">
        <div class="saved-outlines-icon">📚</div>
        <h4>已保存的故事大纲</h4>
      </div>

      <NCollapse class="saved-outlines-collapse">
        <NCollapseItem
          v-for="(outline, index) in savedOutlines"
          :key="outline.id"
          :name="outline.id"
        >
          <template #header>
            <div class="saved-outline-header">
              <div class="saved-outline-number">{{ index + 1 }}</div>
              <div class="saved-outline-title">{{ outline.title || `未命名大纲` }}</div>
              <div class="saved-outline-date">{{ new Date(outline.timestamp).toLocaleString() }}</div>
            </div>
          </template>

          <div class="saved-outline-content" v-html="outline.content.replace(/\n/g, '<br>')"></div>
        </NCollapseItem>
      </NCollapse>
    </div>
  </div>
</template>

<style scoped>
.story-outline-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding-bottom: 1rem;
}

/* 标题部分 */
.outline-header {
  margin-bottom: 1.5rem;
}

.outline-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #8b5cf6, #6366f1);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.outline-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

.dark .outline-subtitle {
  color: #94a3b8;
}

.auto-save-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #f0f9ff;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  color: #0369a1;
  border-left: 3px solid #0ea5e9;
  max-width: fit-content;
}

.dark .auto-save-hint {
  background-color: #0c4a6e;
  color: #7dd3fc;
  border-left-color: #0ea5e9;
}

.auto-save-icon {
  font-size: 1rem;
}

/* 分隔线 */
.section-divider {
  margin: 1rem 0;
}

/* 故事结构参考 */
.structure-collapse {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.outline-templates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin: 0.75rem 0;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.dark .template-card {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.template-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.4);
  background-color: #faf5ff;
}

.dark .template-card:hover {
  background-color: #2e1065;
  border-color: #7c3aed;
}

.template-icon {
  font-size: 1.75rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3e8ff;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.15);
  flex-shrink: 0;
}

.dark .template-icon {
  background-color: #4c1d95;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
}

.template-card:hover .template-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 0 4px 10px rgba(139, 92, 246, 0.25);
}

.template-content {
  flex: 1;
}

.template-title {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #334155;
}

.dark .template-title {
  color: #e2e8f0;
}

.template-description {
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.5;
}

.dark .template-description {
  color: #94a3b8;
}

/* 表单容器 */
.outline-form-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 表单部分通用样式 */
.form-section {
  background-color: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.dark .form-section {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.section-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: #f5f3ff;
  border-radius: 0.75rem;
  color: #8b5cf6;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.15);
}

.dark .section-icon {
  background-color: #4c1d95;
  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);
}

.section-title {
  font-weight: 600;
  color: #334155;
  font-size: 1.1rem;
  margin: 0;
}

.dark .section-title {
  color: #e2e8f0;
}

/* 输入标签 */
.input-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: #4f46e5;
  margin-bottom: 0.5rem;
}

.dark .input-label {
  color: #818cf8;
}

/* 标题部分 */
.title-input {
  font-size: 1rem;
}

/* 设定部分 */
.setting-inputs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 角色部分 */
.character-tabs-container {
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.character-tabs {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  min-width: max-content;
}

.character-tab {
  padding: 0.5rem 1rem;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

.dark .character-tab {
  background-color: #1e293b;
  border-color: #334155;
}

.character-tab-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.character-emoji {
  font-size: 1.1rem;
}

.character-tab.active {
  background-color: #4f46e5;
  color: white;
  font-weight: 600;
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.2);
}

.dark .character-tab.active {
  background-color: #6366f1;
  border-color: #6366f1;
}

.character-tab.add-tab {
  background-color: #f8fafc;
  color: #64748b;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  border: 1px dashed #cbd5e1;
}

.dark .character-tab.add-tab {
  background-color: #0f172a;
  color: #94a3b8;
  border-color: #475569;
}

.character-tab.add-tab:hover {
  background-color: #f1f5f9;
  color: #4f46e5;
}

.dark .character-tab.add-tab:hover {
  background-color: #1e293b;
  color: #818cf8;
}

.character-form {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.dark .character-form {
  background-color: #0f172a;
  border-color: #334155;
}

.character-form-header {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
}

.character-role-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.character-role-badge.主角 {
  background-color: #c7d2fe;
  color: #4338ca;
}

.character-role-badge.配角 {
  background-color: #bfdbfe;
  color: #1d4ed8;
}

.character-role-badge.帮手 {
  background-color: #bbf7d0;
  color: #15803d;
}

.character-role-badge.对手 {
  background-color: #fecaca;
  color: #b91c1c;
}

.character-role-badge.导师 {
  background-color: #fed7aa;
  color: #c2410c;
}

.dark .character-role-badge.主角 {
  background-color: #4338ca;
  color: #c7d2fe;
}

.dark .character-role-badge.配角 {
  background-color: #1d4ed8;
  color: #bfdbfe;
}

.dark .character-role-badge.帮手 {
  background-color: #15803d;
  color: #bbf7d0;
}

.dark .character-role-badge.对手 {
  background-color: #b91c1c;
  color: #fecaca;
}

.dark .character-role-badge.导师 {
  background-color: #c2410c;
  color: #fed7aa;
}

.character-inputs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.character-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.character-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

.remove-character-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* 故事情节部分 */
.plot-timeline {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.plot-timeline-item {
  display: flex;
  gap: 1rem;
  position: relative;
}

.plot-timeline-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: #4f46e5;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  flex-shrink: 0;
  z-index: 2;
}

.dark .plot-timeline-icon {
  background-color: #6366f1;
}

.plot-timeline-connector {
  width: 2px;
  height: 2rem;
  background-color: #e2e8f0;
  margin-left: 1rem;
  z-index: 1;
}

.dark .plot-timeline-connector {
  background-color: #334155;
}

.plot-timeline-content {
  flex: 1;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
}

.dark .plot-timeline-content {
  background-color: #0f172a;
  border-color: #334155;
}

.plot-timeline-header {
  margin-bottom: 0.75rem;
}

.plot-timeline-title {
  font-weight: 600;
  color: #4f46e5;
  font-size: 1rem;
}

.dark .plot-timeline-title {
  color: #818cf8;
}

.plot-timeline-subtitle {
  font-size: 0.8rem;
  color: #64748b;
}

.dark .plot-timeline-subtitle {
  color: #94a3b8;
}

/* 主题和备注部分 */
.theme-input-wrapper, .notes-input-wrapper {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
}

.dark .theme-input-wrapper, .dark .notes-input-wrapper {
  background-color: #0f172a;
  border-color: #334155;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: #f0f9ff;
  border-radius: 0.75rem;
  border-left: 4px solid #0ea5e9;
}

.dark .form-tips {
  background-color: #0c4a6e;
  border-left-color: #0ea5e9;
}

.tip-icon {
  font-size: 1.25rem;
}

.tip-text {
  font-size: 0.875rem;
  color: #0369a1;
}

.dark .tip-text {
  color: #7dd3fc;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.reset-button, .save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.save-button {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
  padding: 0.5rem 1.25rem;
  font-weight: 600;
  border-radius: 0.75rem;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.button-icon {
  font-size: 1.1rem;
}

/* 已保存的大纲 */
.saved-outlines {
  margin-top: 2rem;
  background-color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
}

.dark .saved-outlines {
  background-color: #1e293b;
  border-color: #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.saved-outlines-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.saved-outlines-icon {
  font-size: 1.5rem;
  color: #4f46e5;
}

.dark .saved-outlines-icon {
  color: #818cf8;
}

.saved-outlines h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #334155;
}

.dark .saved-outlines h4 {
  color: #e2e8f0;
}

.saved-outlines-collapse {
  border-radius: 0.75rem;
  overflow: hidden;
}

.saved-outline-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.saved-outline-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background-color: #4f46e5;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
}

.dark .saved-outline-number {
  background-color: #6366f1;
}

.saved-outline-title {
  flex: 1;
  font-weight: 600;
  color: #334155;
}

.dark .saved-outline-title {
  color: #e2e8f0;
}

.saved-outline-date {
  font-size: 0.8rem;
  color: #64748b;
}

.dark .saved-outline-date {
  color: #94a3b8;
}

.saved-outline-content {
  white-space: pre-line;
  line-height: 1.6;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  margin-top: 0.5rem;
  border: 1px solid #e2e8f0;
}

.dark .saved-outline-content {
  background-color: #0f172a;
  border-color: #334155;
  color: #e2e8f0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .outline-templates {
    grid-template-columns: 1fr;
  }
}
</style>
