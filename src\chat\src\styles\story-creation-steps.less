/* 绘本创作步骤统一样式 */

/* 步骤页面容器 */
.step-page-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8fafc;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
  height: 100%;
  overflow-y: auto;
}

.dark .step-page-container {
  background-color: #1e293b;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 步骤标题和描述 */
.step-header {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.dark .step-title {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  font-size: 1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.dark .step-description {
  color: #94a3b8;
}

/* 内容卡片统一样式 */
.content-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.dark .content-card {
  background-color: #1e293b;
  border-color: #334155;
}

.dark .content-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 卡片标题 */
.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.card-title-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.dark .card-title {
  color: #e2e8f0;
  border-bottom-color: #334155;
}

/* 表单元素统一样式 */
.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 0.5rem;
}

.dark .form-label {
  color: #cbd5e1;
}

/* 按钮统一样式 */
.primary-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.primary-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.dark .primary-btn {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

.dark .primary-btn:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.secondary-btn {
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.secondary-btn:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

.dark .secondary-btn {
  background-color: #334155;
  color: #e2e8f0;
  border-color: #475569;
}

.dark .secondary-btn:hover {
  background-color: #475569;
}

/* 操作按钮容器 */
.actions-container {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* 提示框统一样式 */
.tip-box {
  background-color: #f0f9ff;
  border-left: 4px solid #0ea5e9;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.tip-icon {
  font-size: 1.25rem;
  color: #0ea5e9;
  flex-shrink: 0;
}

.tip-content {
  font-size: 0.9rem;
  color: #0369a1;
  line-height: 1.5;
}

.dark .tip-box {
  background-color: #0c4a6e;
  border-left-color: #0ea5e9;
}

.dark .tip-icon {
  color: #38bdf8;
}

.dark .tip-content {
  color: #7dd3fc;
}

/* 网格布局 */
.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* 两列布局 */
.two-column-layout {
  display: flex;
  gap: 1.5rem;
}

.column-left {
  flex: 1;
}

.column-right {
  flex: 1;
}

@media (max-width: 768px) {
  .two-column-layout {
    flex-direction: column;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 图片预览区域 */
.image-preview {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16 / 9;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
}

.dark .image-preview {
  background-color: #334155;
  border-color: #475569;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #94a3b8;
}

.placeholder-icon {
  font-size: 2.5rem;
}

.placeholder-text {
  font-size: 0.9rem;
  text-align: center;
}

.dark .image-placeholder {
  color: #cbd5e1;
}

/* 预览图片 */
.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 魔法生成按钮 */
.magic-generate-btn {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.magic-generate-btn:hover {
  background: linear-gradient(135deg, #7c3aed, #4f46e5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.dark .magic-generate-btn {
  background: linear-gradient(135deg, #a78bfa, #818cf8);
}

.dark .magic-generate-btn:hover {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.4);
}
