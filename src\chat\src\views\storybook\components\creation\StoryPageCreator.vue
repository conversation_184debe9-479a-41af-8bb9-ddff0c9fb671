<template>
  <div class="step-page-container">
    <div class="step-header">
      <h2 class="step-title">画出故事</h2>
      <p class="step-description">为你的故事添加精美的插图和文字</p>
    </div>

    <!-- 更多选项对话框 -->
    <NModal v-model:show="showMoreOptionsDialog" preset="card" title="更多选项" style="width: 600px">
      <template #header>
        <div class="modal-header">
          <span class="modal-icon">{{ getModalIcon() }}</span>
          <span class="modal-title">{{ moreOptionsTitle }}</span>
        </div>
      </template>

      <div class="more-options-grid">
        <div
          v-for="option in getMoreOptions()"
          :key="option.value"
          :class="['more-option-item', isOptionSelected(option.value) ? 'active' : '']"
          @click="selectMoreOption(option.value)"
        >
          <div class="option-icon">
            <template v-if="currentOptionType === 'characters'">
              {{ getCharacterEmoji(option) }}
            </template>
            <template v-else>
              {{ option.icon }}
            </template>
          </div>
          <div class="option-label">{{ option.label }}</div>
        </div>
      </div>
    </NModal>

    <div class="content-layout">
      <!-- 左侧：页面导航 -->
      <div class="page-navigation">
        <div class="book-guide">
          <div class="book-icon">📖</div>
          <div class="book-text">我的绘本</div>
        </div>

        <div class="page-list">
          <div
            v-for="(page, index) in storyContent.pages"
            :key="page.id"
            class="page-item"
            :class="{ active: index === currentPageIndex }"
            @click="currentPageIndex = index"
          >
            <div class="page-preview">
              <img v-if="page.image" :src="page.image" :alt="`页面 ${page.pageNumber}`" class="page-image" />
              <div v-else class="page-placeholder">
                <span class="placeholder-icon">🖼️</span>
              </div>
            </div>
            <div class="page-number">第 {{ page.pageNumber }} 页</div>
          </div>

          <div class="page-item add-page" @click="addNewPage">
            <div class="add-icon">+</div>
            <div class="add-label">添加新页面</div>
          </div>
        </div>


      </div>

      <!-- 右侧：页面编辑 -->
      <div class="page-editor" v-if="storyContent.pages.length > 0">
        <div class="editor-header">
          <div class="editor-title-wrapper">
            <div class="page-emoji">📄</div>
            <h3 class="editor-title">第 {{ currentPageIndex + 1 }} 页</h3>
          </div>

          <div class="editor-actions">
            <NButton size="small" @click="deleteCurrentPage" class="delete-button">
              <span class="delete-icon">🗑️</span> 删除这一页
            </NButton>
          </div>
        </div>

        <div class="editor-content">
          <!-- 页面预览 -->
          <div class="page-preview-wrapper">
            <div class="page-preview-large" :class="[`paper-size-${currentPage.paperSize || 'size24'}`]">
              <div :class="['page-layout-container', `layout-${currentPage.layout}`]">
                <!-- 图片区域 -->
                <div class="page-image-container" :style="getImageContainerStyle()">
                  <img
                    v-if="currentPage.image"
                    :src="currentPage.image"
                    :alt="`页面 ${currentPage.pageNumber}`"
                    class="preview-image"
                  />
                  <div v-else class="preview-placeholder">
                    <div class="placeholder-animation">
                      <span class="placeholder-icon">🎨</span>
                      <div class="placeholder-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                      </div>
                    </div>
                    <p class="placeholder-text">点击下面的按钮，让AI帮你画出这一页的图画！</p>
                  </div>
                  <!-- 生成中遮罩 -->
                  <div v-if="isGeneratingImage" class="generating-overlay">
                    <div class="generating-spinner">
                      <div class="spinner"></div>
                    </div>
                    <div class="generating-text">AI正在绘制故事图像...</div>
                    <!-- 图像生成进度显示 -->
                    <ImageGenerationProgress
                      :status="generationStatus"
                      :progress="generationProgress"
                      class="mt-4"
                    />
                  </div>
                </div>

                <!-- 文字区域 -->
                <div class="page-text-container" :style="getTextContainerStyle()">
                  <div class="page-text-content" :style="getTextContentStyle()">
                    {{ currentPage.text || '这一页的故事内容...' }}
                  </div>
                </div>
              </div>

              <!-- 纸张尺寸标记已移除，默认使用横版比例 -->
            </div>

            <NButton class="generate-image-btn" @click="generatePageImage" :loading="isGeneratingImage" :disabled="isGeneratingImage">
              <span class="btn-icon">{{ isGeneratingImage ? '⏳' : '✨' }}</span> {{ isGeneratingImage ? '生成中...' : '画出这一页' }}
            </NButton>
          </div>

          <!-- 主标签页 -->
          <div class="main-tabs-container">
            <NTabs type="line" animated class="main-tabs">
              <!-- 画面内容标签页 -->
              <NTabPane name="content" tab="画面内容">
                <div class="tab-content">
                  <!-- 紧凑型布局：绘本文字和画面描述 -->
                  <div class="compact-editor-layout">
                    <!-- 左侧：绘本文字 -->
                    <div class="page-text-editor compact">
                      <div class="settings-title-wrapper">
                        <span class="settings-icon">📖</span>
                        <h4 class="settings-title">绘本上的文字</h4>
                      </div>
                      <NInput
                        v-model:value="currentPage.text"
                        type="textarea"
                        placeholder="写下绘本上显示的文字..."
                        :autosize="{ minRows: 2, maxRows: 3 }"
                        @update:value="updatePageContent"
                        class="child-input"
                      />
                    </div>

                    <!-- 右侧：画面描述 -->
                    <div class="scene-description-editor compact">
                      <div class="settings-title-wrapper">
                        <span class="settings-icon">🎨</span>
                        <h4 class="settings-title">画面描述</h4>
                        <NButton size="small" type="primary" class="generate-preview-btn" @click="generatePageImage" :loading="isGeneratingImage" :disabled="isGeneratingImage">
                          <span class="btn-icon">{{ isGeneratingImage ? '⏳' : '✨' }}</span> {{ isGeneratingImage ? '生成中...' : '画出这一页' }}
                        </NButton>
                      </div>

                      <!-- 场景元素选择器 -->
                      <div class="scene-elements-selector">
                        <!-- 场景类型选择器 -->
                        <div class="element-selector">
                          <div class="selector-label">
                            <span class="selector-icon">🏞️</span>
                            <span>场景</span>
                          </div>
                          <div class="selector-options">
                            <div
                              v-for="option in sceneTypeOptions.slice(0, 6)"
                              :key="option.value"
                              :class="['selector-option', currentPage.sceneElements?.type === option.value ? 'active' : '']"
                              @click="updateSceneElement('type', option.value)"
                            >
                              {{ option.icon }}
                            </div>
                            <div class="selector-more" @click="showMoreOptions('type')">
                              <span>更多</span>
                            </div>
                          </div>
                        </div>

                        <!-- 时间选择器 -->
                        <div class="element-selector">
                          <div class="selector-label">
                            <span class="selector-icon">🕒</span>
                            <span>时间</span>
                          </div>
                          <div class="selector-options">
                            <div
                              v-for="option in timeOptions"
                              :key="option.value"
                              :class="['selector-option', currentPage.sceneElements?.time === option.value ? 'active' : '']"
                              @click="updateSceneElement('time', option.value)"
                            >
                              {{ option.icon }}
                            </div>
                          </div>
                        </div>

                        <!-- 天气选择器 -->
                        <div class="element-selector">
                          <div class="selector-label">
                            <span class="selector-icon">☁️</span>
                            <span>天气</span>
                          </div>
                          <div class="selector-options">
                            <div
                              v-for="option in weatherOptions.slice(0, 6)"
                              :key="option.value"
                              :class="['selector-option', currentPage.sceneElements?.weather === option.value ? 'active' : '']"
                              @click="updateSceneElement('weather', option.value)"
                            >
                              {{ option.icon }}
                            </div>
                            <div class="selector-more" @click="showMoreOptions('weather')">
                              <span>更多</span>
                            </div>
                          </div>
                        </div>

                        <!-- 氛围选择器 -->
                        <div class="element-selector">
                          <div class="selector-label">
                            <span class="selector-icon">✨</span>
                            <span>氛围</span>
                          </div>
                          <div class="selector-options">
                            <div
                              v-for="option in moodOptions.slice(0, 6)"
                              :key="option.value"
                              :class="['selector-option', currentPage.sceneElements?.mood === option.value ? 'active' : '']"
                              @click="updateSceneElement('mood', option.value)"
                            >
                              {{ option.icon }}
                            </div>
                            <div class="selector-more" @click="showMoreOptions('mood')">
                              <span>更多</span>
                            </div>
                          </div>
                        </div>

                        <!-- 角色选择器 -->
                        <div class="element-selector">
                          <div class="selector-label">
                            <span class="selector-icon">👪</span>
                            <span>角色</span>
                          </div>
                          <div class="selector-options">
                            <div
                              v-for="option in characterOptions.slice(0, 6)"
                              :key="option.value"
                              :class="['selector-option', currentPage.characters && currentPage.characters.includes(option.value) ? 'active' : '']"
                              @click="toggleCharacter(option.value)"
                            >
                              {{ getCharacterEmoji(option) }}
                            </div>
                            <div class="selector-more" @click="showMoreOptions('characters')">
                              <span>更多</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 额外描述输入框 -->
                      <div class="extra-description">
                        <NInput
                          v-model:value="currentPage.imageDescription"
                          type="textarea"
                          placeholder="添加更多细节描述..."
                          :autosize="{ minRows: 1, maxRows: 2 }"
                          @update:value="updateImageDescription"
                          class="child-input"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 描述预览 -->
                  <div class="description-preview-bar">
                    <div class="preview-label">画面描述预览：</div>
                    <div class="preview-content">{{ getFullImageDescription() }}</div>
                    <div class="preview-hint">此描述将直接用于AI生成图像</div>
                  </div>
                </div>
              </NTabPane>

              <!-- 文字设置标签页 -->
              <NTabPane name="settings" tab="文字设置">
                <div class="tab-content">
                  <!-- 文字设置 -->
                  <div class="text-style-editor full-width">
                    <div class="settings-title-wrapper">
                      <span class="settings-icon">🔤</span>
                      <h4 class="settings-title">文字设置</h4>
                    </div>

                    <!-- 文字设置选择器 -->
                    <div class="text-style-selectors">
                      <!-- 文字颜色设置 -->
                      <div class="element-selector">
                        <div class="selector-label">
                          <span class="selector-icon">🎨</span>
                          <span>颜色</span>
                        </div>
                        <div class="selector-options color-options">
                          <div
                            v-for="color in textColorPresets"
                            :key="color.value"
                            :class="['selector-option color-option', currentPage.textStyle.color === color.value ? 'active' : '']"
                            :style="{ backgroundColor: color.value }"
                            @click="updateTextColor(color.value)"
                          >
                            <span class="color-tooltip">{{ color.name }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 文字大小设置 -->
                      <div class="element-selector">
                        <div class="selector-label">
                          <span class="selector-icon">🔠</span>
                          <span>大小</span>
                        </div>
                        <div class="selector-options">
                          <div
                            v-for="option in fontSizeOptions"
                            :key="option.value"
                            :class="['selector-option', currentPage.textStyle.fontSize === option.value ? 'active' : '']"
                            @click="updateFontSize(option.value)"
                          >
                            {{ option.icon }}
                          </div>
                        </div>
                      </div>

                      <!-- 水平对齐设置 -->
                      <div class="element-selector">
                        <div class="selector-label">
                          <span class="selector-icon">↔️</span>
                          <span>水平</span>
                        </div>
                        <div class="selector-options">
                          <div
                            v-for="option in horizontalAlignmentOptions"
                            :key="option.value"
                            :class="['selector-option', currentPage.textStyle.alignment === option.value ? 'active' : '']"
                            @click="updateTextAlignment(option.value)"
                          >
                            {{ option.icon }}
                          </div>
                        </div>
                      </div>

                      <!-- 垂直对齐设置 -->
                      <div class="element-selector">
                        <div class="selector-label">
                          <span class="selector-icon">↕️</span>
                          <span>垂直</span>
                        </div>
                        <div class="selector-options">
                          <div
                            v-for="option in verticalAlignmentOptions"
                            :key="option.value"
                            :class="['selector-option', currentPage.textStyle.verticalAlignment === option.value ? 'active' : '']"
                            @click="updateVerticalAlignment(option.value)"
                          >
                            {{ option.icon }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </NTabPane>
            </NTabs>
          </div>
        </div>
      </div>
    </div>

    <div class="tip-box">
      <span class="tip-icon">✨</span>
      <div class="tip-content">你的绘本会自动保存到本地和草稿箱，随时可以继续创作！</div>
    </div>

    <div class="actions-container">
      <button class="primary-btn" @click="saveStoryContent">
        <span>💾</span> 保存我的绘本
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { NCard, NTabs, NTabPane, NCollapse, NCollapseItem, NInput, NButton, NSpace, NDivider, NSelect, NColorPicker, NSlider, NRadio, NRadioGroup, NRadioButton, NModal, NGrid, NGridItem } from 'naive-ui';

const props = defineProps({
  projectData: {
    type: Object,
    required: true
  }
});

// 故事内容数据
const storyContent = reactive({
  pages: []
});

// 当前编辑的页面索引
const currentPageIndex = ref(0);

// 更多选项对话框
const showMoreOptionsDialog = ref(false);
const currentOptionType = ref('');
const moreOptionsTitle = ref('');

// 计算当前页面
const currentPage = computed(() => {
  if (storyContent.pages.length === 0) {
    return null;
  }
  return storyContent.pages[currentPageIndex.value];
});

// 场景设置选项
const sceneSettings = [
  { label: '🌳 森林', value: 'forest' },
  { label: '🏫 学校', value: 'school' },
  { label: '🏠 家里', value: 'home' },
  { label: '🏞️ 公园', value: 'park' },
  { label: '🏖️ 海边', value: 'beach' },
  { label: '⛰️ 山上', value: 'mountain' },
  { label: '🏙️ 城市', value: 'city' },
  { label: '🚜 农场', value: 'farm' },
  { label: '🦁 动物园', value: 'zoo' },
  { label: '🚀 太空', value: 'space' },
  { label: '🐠 海底', value: 'underwater' },
  { label: '🏰 童话城堡', value: 'castle' }
];

// 默认使用图上字下布局

// 文字位置选项
const textPositionOptions = [
  { label: '上方', value: 'top', icon: '⬆️' },
  { label: '下方', value: 'bottom', icon: '⬇️' },
  { label: '左侧', value: 'left', icon: '⬅️' },
  { label: '右侧', value: 'right', icon: '➡️' },
  { label: '覆盖在图片上', value: 'overlay', icon: '📝' }
];

// 水平对齐选项
const horizontalAlignmentOptions = [
  { label: '左对齐', value: 'left', icon: '⬅️' },
  { label: '居中对齐', value: 'center', icon: '↔️' },
  { label: '右对齐', value: 'right', icon: '➡️' }
];

// 垂直对齐选项
const verticalAlignmentOptions = [
  { label: '顶部对齐', value: 'flex-start', icon: '⬆️' },
  { label: '居中对齐', value: 'center', icon: '↕️' },
  { label: '底部对齐', value: 'flex-end', icon: '⬇️' }
];

// 字体大小选项
const fontSizeOptions = [
  { label: '小', value: 'small', icon: '小' },
  { label: '中', value: 'medium', icon: '中' },
  { label: '大', value: 'large', icon: '大' }
];

// 儿童友好的文字颜色预设
const textColorPresets = [
  { name: '黑色', value: '#000000' },
  { name: '白色', value: '#FFFFFF' },
  { name: '红色', value: '#FF0000' },
  { name: '橙色', value: '#FF9800' },
  { name: '黄色', value: '#FFEB3B' },
  { name: '绿色', value: '#4CAF50' },
  { name: '蓝色', value: '#2196F3' },
  { name: '紫色', value: '#9C27B0' },
  { name: '粉色', value: '#E91E63' },
  { name: '棕色', value: '#795548' }
];

// 纸张尺寸选项已移除，默认使用横版比例 (16:9)

// 场景类型选项
const sceneTypeOptions = [
  { label: '室内', value: 'indoor', icon: '🏠' },
  { label: '室外', value: 'outdoor', icon: '🌳' },
  { label: '城市', value: 'city', icon: '🏙️' },
  { label: '乡村', value: 'countryside', icon: '🏞️' },
  { label: '海洋', value: 'ocean', icon: '🌊' },
  { label: '山区', value: 'mountain', icon: '⛰️' },
  { label: '森林', value: 'forest', icon: '🌲' },
  { label: '沙漠', value: 'desert', icon: '🏜️' },
  { label: '太空', value: 'space', icon: '🚀' },
  { label: '幻想世界', value: 'fantasy', icon: '🧚' },
  { label: '学校', value: 'school', icon: '🏫' },
  { label: '公园', value: 'park', icon: '🎡' }
];

// 时间选项
const timeOptions = [
  { label: '早晨', value: 'morning', icon: '🌅' },
  { label: '中午', value: 'noon', icon: '☀️' },
  { label: '下午', value: 'afternoon', icon: '🌤️' },
  { label: '黄昏', value: 'sunset', icon: '🌇' },
  { label: '夜晚', value: 'night', icon: '🌃' },
  { label: '深夜', value: 'midnight', icon: '🌙' }
];

// 天气选项
const weatherOptions = [
  { label: '晴天', value: 'sunny', icon: '☀️' },
  { label: '多云', value: 'cloudy', icon: '⛅' },
  { label: '阴天', value: 'overcast', icon: '☁️' },
  { label: '雨天', value: 'rainy', icon: '🌧️' },
  { label: '雷雨', value: 'thunderstorm', icon: '⛈️' },
  { label: '雪天', value: 'snowy', icon: '❄️' },
  { label: '雾天', value: 'foggy', icon: '🌫️' },
  { label: '彩虹', value: 'rainbow', icon: '🌈' }
];

// 氛围选项
const moodOptions = [
  { label: '欢乐', value: 'happy', icon: '😄' },
  { label: '平静', value: 'peaceful', icon: '😌' },
  { label: '神秘', value: 'mysterious', icon: '🔮' },
  { label: '紧张', value: 'tense', icon: '😨' },
  { label: '温馨', value: 'warm', icon: '❤️' },
  { label: '悲伤', value: 'sad', icon: '😢' },
  { label: '兴奋', value: 'excited', icon: '🎉' },
  { label: '梦幻', value: 'dreamy', icon: '✨' },
  { label: '冒险', value: 'adventure', icon: '🧭' },
  { label: '魔法', value: 'magical', icon: '🧙' }
];

// 获取角色选项
const characterOptions = computed(() => {
  if (!props.projectData.outline || !props.projectData.outline.characters) {
    return [];
  }

  return Object.values(props.projectData.outline.characters)
    .filter(char => char.name) // 只返回有名字的角色
    .map(char => ({
      label: `${char.name}`,
      value: char.id
    }));
});

// 组件挂载时初始化数据
onMounted(() => {
  initializeStoryContent();
});

// 初始化故事内容
const initializeStoryContent = () => {
  // 如果项目中已有页面数据，使用它
  if (props.projectData.pages && props.projectData.pages.length > 0) {
    storyContent.pages = props.projectData.pages;
  } else {
    // 否则，创建初始页面
    createInitialPages();
  }
};

// 创建初始页面 - 使用标准绘本比例
const createInitialPages = () => {
  // 清空现有页面
  storyContent.pages = [];

  // 创建第一个页面
  storyContent.pages.push({
    id: Date.now(),
    pageNumber: 1,
    image: '',
    text: '',
    setting: '',
    characters: [],
    description: '',
    // 新增布局相关属性
    layout: 'vertical', // 默认图上字下布局
    paperSize: 'landscape', // 默认使用横版比例
    textStyle: {
      position: 'bottom', // 文字位置：上、下、左、右、覆盖
      alignment: 'left',  // 水平对齐：左、中、右
      verticalAlignment: 'center', // 垂直对齐：顶部、中间、底部
      fontSize: 'medium', // 字体大小：小、中、大
      color: '#000000',   // 文字颜色
      backgroundColor: 'transparent', // 文字背景色
      opacity: 1          // 背景透明度
    },
    // 新增画面描述相关属性
    imageDescription: '', // 额外的画面描述文本
    sceneElements: {      // 场景元素
      type: '',           // 场景类型
      time: '',           // 时间
      weather: '',        // 天气
      mood: ''            // 氛围
    }
  });

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 添加新页面 - 使用标准绘本比例
const addNewPage = () => {
  const newPage = {
    id: Date.now(),
    pageNumber: storyContent.pages.length + 1,
    image: '',
    text: '',
    setting: '',
    characters: [],
    description: '',
    // 新增布局相关属性
    layout: 'vertical', // 默认图上字下布局
    paperSize: 'landscape', // 默认使用横版比例
    textStyle: {
      position: 'bottom', // 文字位置：上、下、左、右、覆盖
      alignment: 'left',  // 水平对齐：左、中、右
      verticalAlignment: 'center', // 垂直对齐：顶部、中间、底部
      fontSize: 'medium', // 字体大小：小、中、大
      color: '#000000',   // 文字颜色
      backgroundColor: 'transparent', // 文字背景色
      opacity: 1          // 背景透明度
    },
    // 新增画面描述相关属性
    imageDescription: '', // 额外的画面描述文本
    sceneElements: {      // 场景元素
      type: '',           // 场景类型
      time: '',           // 时间
      weather: '',        // 天气
      mood: ''            // 氛围
    }
  };

  storyContent.pages.push(newPage);
  currentPageIndex.value = storyContent.pages.length - 1;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 删除当前页面
const deleteCurrentPage = () => {
  if (storyContent.pages.length <= 1) {
    window.$message?.warning('至少需要保留一个页面');
    return;
  }

  storyContent.pages.splice(currentPageIndex.value, 1);

  // 更新页码
  storyContent.pages.forEach((page, index) => {
    page.pageNumber = index + 1;
  });

  // 调整当前页面索引
  if (currentPageIndex.value >= storyContent.pages.length) {
    currentPageIndex.value = storyContent.pages.length - 1;
  }

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新页面内容
const updatePageContent = (text) => {
  if (currentPage.value) {
    currentPage.value.text = text;

    // 更新项目数据
    props.projectData.pages = storyContent.pages;
  }
};

// 更新页面场景设置
const updatePageSetting = (setting) => {
  if (currentPage.value) {
    currentPage.value.setting = setting;

    // 更新项目数据
    props.projectData.pages = storyContent.pages;
  }
};

// 更新页面角色
const updatePageCharacters = (characters) => {
  if (currentPage.value) {
    currentPage.value.characters = characters;

    // 更新项目数据
    props.projectData.pages = storyContent.pages;
  }
};

// 切换角色选择
const toggleCharacter = (characterId) => {
  if (!currentPage.value) return;

  if (!currentPage.value.characters) {
    currentPage.value.characters = [];
  }

  const index = currentPage.value.characters.indexOf(characterId);
  if (index === -1) {
    // 添加角色
    currentPage.value.characters.push(characterId);
  } else {
    // 移除角色
    currentPage.value.characters.splice(index, 1);
  }

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

import { fetchChatAPIProcess } from '@/api';
import { useAuthStore, useChatStore } from '@/store';
import ImageGenerationProgress from './ImageGenerationProgress.vue';
import { inject } from 'vue';

// 注入onConversation函数，用于与聊天界面一致的图像生成
const onConversation = inject('onConversation');

const authStore = useAuthStore();
const chatStore = useChatStore();
const isGeneratingImage = ref(false);
const generationStatus = ref('queuing'); // queuing, generating, progress, completed, failed
const generationProgress = ref(0);

// 生成页面图像
const generatePageImage = async () => {
  if (!currentPage.value) {
    console.error('[AI绘本创作-页面生成] 当前页面为空，无法生成图像');
    window.$message?.error('当前页面为空，无法生成图像');
    return;
  }

  // 防止重复点击
  if (isGeneratingImage.value) {
    window.$message?.warning('正在生成图像，请稍候...');
    return;
  }

  // 使用完整的画面描述作为提示词
  const prompt = getFullImageDescription();
  console.log('[AI绘本创作-页面生成] 开始生成图像，提示词:', prompt);

  // 声明进度更新定时器变量和超时定时器变量
  let progressInterval = null;
  let timeoutTimer = null;

  try {
    // 设置生成状态
    isGeneratingImage.value = true;
    generationStatus.value = 'queuing';
    generationProgress.value = 0;
    window.$message?.info('正在使用AI生成故事图像，请稍候...');
    console.log('[AI绘本创作-页面生成] 状态已设置为queuing，开始生成图像');

    // 创建一个响应处理函数
    const handleImageResponse = (response) => {
      console.log('[AI绘本创作-页面生成] 收到图像生成响应:', {
        status: response?.status,
        hasFileInfo: !!response?.fileInfo,
        error: response?.error
      });

      // 尝试从不同格式的响应中提取图像URL
      let imageUrl = null;

      if (response && response.fileInfo) {
        // 旧格式
        imageUrl = response.fileInfo;
      } else if (response && response.data && response.data.length > 0) {
        // OpenAI API格式
        if (response.data[0].b64_json) {
          // 如果是base64格式，转换为数据URL
          imageUrl = 'data:image/png;base64,' + response.data[0].b64_json;
        } else if (response.data[0].url) {
          imageUrl = response.data[0].url;
        }
      } else if (response && response.url) {
        // 简化格式
        imageUrl = response.url;
      } else if (typeof response === 'string' && (response.startsWith('http') || response.startsWith('data:'))) {
        // 直接返回URL字符串
        imageUrl = response;
      }

      if (imageUrl) {
        // 图像生成成功
        console.log('[AI绘本创作-页面生成] 图像生成成功，文件URL:', imageUrl.substring(0, 50) + '...');
        generationStatus.value = 'completed';
        generationProgress.value = 100;
        currentPage.value.image = imageUrl;
        window.$message?.success('故事图像生成成功！');

        // 更新项目数据
        props.projectData.pages = storyContent.pages;

        // 清除可能存在的进度更新定时器和超时定时器
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }
        if (timeoutTimer) {
          clearTimeout(timeoutTimer);
          timeoutTimer = null;
        }

        // 延迟重置生成状态
        setTimeout(() => {
          isGeneratingImage.value = false;
        }, 1000);

        return true;
      } else if (response && response.status === 5) {
        // 图像生成失败
        console.error('[AI绘本创作-页面生成] 图像生成失败，状态码:', response.status, '错误信息:', response.error || '未知错误');
        handleGenerationFailure(response.error || '未知错误');
        return false;
      } else if (response && response.status === 2) {
        // 处理中状态，更新进度
        console.log('[AI绘本创作-页面生成] 图像生成处理中，状态码:', response.status);
        generationStatus.value = 'generating';

        // 如果有进度百分比，更新进度条
        if (response.progress) {
          const progressValue = parseInt(response.progress);
          if (!isNaN(progressValue)) {
            generationProgress.value = Math.min(90, progressValue);
            console.log('[AI绘本创作-页面生成] 更新进度:', generationProgress.value);
          }
        }

        // 如果没有进度更新定时器，创建一个
        if (!progressInterval) {
          progressInterval = setInterval(() => {
            if (generationStatus.value === 'completed' || generationStatus.value === 'failed') {
              console.log('[AI绘本创作-页面生成] 进度更新结束，最终状态:', generationStatus.value);
              clearInterval(progressInterval);
              progressInterval = null;
            } else {
              generationProgress.value = Math.min(90, generationProgress.value + 2);
              console.log('[AI绘本创作-页面生成] 更新进度:', generationProgress.value);
            }
          }, 3000);
        }
        return null; // 继续等待
      } else {
        console.error('[AI绘本创作-页面生成] 收到未预期的响应格式:', response);
        handleGenerationFailure('收到未预期的响应格式');
        return false;
      }
    };

    // 设置超时处理
    const timeoutDuration = 120000; // 2分钟超时
    timeoutTimer = setTimeout(() => {
      if (isGeneratingImage.value && generationStatus.value !== 'completed') {
        console.warn('[AI绘本创作-页面生成] 图像生成超时');
        handleGenerationFailure('生成超时，请稍后重试');

        // 尝试使用备用方案
        tryFallbackImageGeneration(prompt);
      }
    }, timeoutDuration);

    // 使用onConversation函数发送请求（与聊天界面一致）
    console.log('[AI绘本创作-页面生成] 发送图像生成请求，参数:', {
      model: 'gpt-image-1',
      modelName: 'GPT Image',
      modelType: 2,
      promptLength: prompt.length,
      extraParam: { size: '1792x1024' }
    });

    // 设置初始进度状态
    generationStatus.value = 'generating';
    generationProgress.value = 30;

    const response = await onConversation({
      msg: prompt,
      model: 'gpt-image-1',
      modelName: 'GPT Image',
      modelType: 2,
      extraParam: {
        size: '1792x1024' // 图像尺寸，适合16:9比例
      },
      onSuccess: handleImageResponse
    });

    // 如果onConversation直接返回了结果（而不是通过回调）
    if (response) {
      const result = handleImageResponse(response);
      if (result === false) {
        // 如果处理失败，尝试使用备用方案
        await tryFallbackImageGeneration(prompt);
      }
    }

  } catch (error) {
    console.error('[AI绘本创作-页面生成] 生成故事图像失败:', error);
    handleGenerationFailure(error.message || '未知错误');

    // 尝试使用备用方案
    await tryFallbackImageGeneration(prompt);
  } finally {
    // 清除超时定时器
    if (timeoutTimer) {
      clearTimeout(timeoutTimer);
      timeoutTimer = null;
    }
  }
};

// 处理生成失败的统一函数
const handleGenerationFailure = (errorMessage) => {
  generationStatus.value = 'failed';
  generationProgress.value = 0;

  // 显示错误消息
  let displayError = '图像生成失败';
  if (errorMessage) {
    displayError += ': ' + errorMessage;
  }
  window.$message?.error(displayError);

  // 清除进度更新定时器
  if (progressInterval) {
    clearInterval(progressInterval);
    progressInterval = null;
  }

  // 重置生成状态
  isGeneratingImage.value = false;
};

// 尝试使用备用方案生成图像
const tryFallbackImageGeneration = async (prompt) => {
  try {
    console.log('[AI绘本创作-页面生成] 尝试使用备用方案生成图像');
    window.$message?.info('正在使用备用方案生成故事图像...');

    // 设置生成状态
    isGeneratingImage.value = true;
    generationStatus.value = 'generating';
    generationProgress.value = 30;

    // 使用固定的横版比例 (16:9)
    const aspectRatio = "16:9";
    const fallbackUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=${aspectRatio.split(':')[0]}&height=${aspectRatio.split(':')[1]}`;
    console.log('[AI绘本创作-页面生成] 使用备用方案生成图像:', fallbackUrl.substring(0, 50) + '...');

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 更新页面图像
    currentPage.value.image = fallbackUrl;
    generationStatus.value = 'completed';
    generationProgress.value = 100;

    window.$message?.success('已使用备用方案生成故事图像');

    // 更新项目数据
    props.projectData.pages = storyContent.pages;

    // 重置生成状态
    setTimeout(() => {
      isGeneratingImage.value = false;
    }, 1000);

    return true;
  } catch (fallbackError) {
    console.error('[AI绘本创作-页面生成] 备用方案也失败:', fallbackError);
    window.$message?.error('备用图像生成方案也失败，请稍后重试');
    isGeneratingImage.value = false;
    generationStatus.value = 'failed';
    return false;
  }
};

// 更新画面描述
const updateImageDescription = (description) => {
  if (!currentPage.value) return;

  currentPage.value.imageDescription = description;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新场景元素
const updateSceneElement = (elementType, value) => {
  if (!currentPage.value) return;

  // 确保sceneElements对象存在
  if (!currentPage.value.sceneElements) {
    currentPage.value.sceneElements = {
      type: '',
      time: '',
      weather: '',
      mood: ''
    };
  }

  // 更新指定的场景元素
  currentPage.value.sceneElements[elementType] = value;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 获取完整的画面描述
const getFullImageDescription = () => {
  if (!currentPage.value) return '';

  let description = '';

  // 添加场景类型
  if (currentPage.value.sceneElements?.type) {
    const typeOption = sceneTypeOptions.find(option => option.value === currentPage.value.sceneElements.type);
    if (typeOption) {
      description += `${typeOption.label}场景，`;
    }
  }

  // 添加时间
  if (currentPage.value.sceneElements?.time) {
    const timeOption = timeOptions.find(option => option.value === currentPage.value.sceneElements.time);
    if (timeOption) {
      description += `${timeOption.label}时分，`;
    }
  }

  // 添加天气
  if (currentPage.value.sceneElements?.weather) {
    const weatherOption = weatherOptions.find(option => option.value === currentPage.value.sceneElements.weather);
    if (weatherOption) {
      description += `${weatherOption.label}，`;
    }
  }

  // 添加氛围
  if (currentPage.value.sceneElements?.mood) {
    const moodOption = moodOptions.find(option => option.value === currentPage.value.sceneElements.mood);
    if (moodOption) {
      description += `${moodOption.label}的氛围，`;
    }
  }

  // 添加场景设置
  if (currentPage.value.setting) {
    const settingOption = sceneSettings.find(option => option.value === currentPage.value.setting);
    if (settingOption) {
      description += `在${settingOption.label.replace(/^🌳 |^🏫 |^🏠 |^🏞️ |^🏖️ |^⛰️ |^🏙️ |^🚜 |^🦁 |^🚀 |^🐠 |^🏰 /g, '')}，`;
    }
  }

  // 添加角色
  if (currentPage.value.characters && currentPage.value.characters.length > 0) {
    const characterNames = currentPage.value.characters.map(charId => {
      const character = props.projectData.outline?.characters?.find(c => c.id === charId);
      return character ? character.name : '';
    }).filter(Boolean);

    if (characterNames.length > 0) {
      description += `有${characterNames.join('、')}，`;
    }
  }

  // 添加额外描述
  if (currentPage.value.imageDescription) {
    description += currentPage.value.imageDescription;
  }

  // 添加默认的儿童绘本风格描述
  description += '，儿童绘本风格, 简单可爱, 明亮色彩, 高质量插图, 白色背景, 细节丰富';

  return description;
};

// 显示更多选项对话框
const showMoreOptions = (optionType) => {
  currentOptionType.value = optionType;

  // 设置对话框标题
  switch (optionType) {
    case 'type':
      moreOptionsTitle.value = '选择场景类型';
      break;
    case 'weather':
      moreOptionsTitle.value = '选择天气';
      break;
    case 'mood':
      moreOptionsTitle.value = '选择氛围';
      break;
    case 'characters':
      moreOptionsTitle.value = '选择角色';
      break;
    default:
      moreOptionsTitle.value = '更多选项';
  }

  // 显示对话框
  showMoreOptionsDialog.value = true;
};

// 获取角色表情符号
const getCharacterEmoji = (character) => {
  // 根据角色名称或类型返回合适的表情符号
  // 这里使用简单的映射，可以根据实际需求扩展
  const name = character.label.toLowerCase();

  if (name.includes('男') || name.includes('爸') || name.includes('父')) return '👨';
  if (name.includes('女') || name.includes('妈') || name.includes('母')) return '👩';
  if (name.includes('孩') || name.includes('子') || name.includes('童')) return '👧';
  if (name.includes('猫')) return '🐱';
  if (name.includes('狗')) return '🐶';
  if (name.includes('鸟')) return '🐦';
  if (name.includes('兔')) return '🐰';
  if (name.includes('熊')) return '🐻';
  if (name.includes('鱼')) return '🐠';

  // 默认返回一个通用的角色图标
  return '👤';
};

// 获取对话框图标
const getModalIcon = () => {
  switch (currentOptionType.value) {
    case 'type':
      return '🏞️';
    case 'weather':
      return '☁️';
    case 'mood':
      return '✨';
    case 'characters':
      return '👪';
    default:
      return '🔍';
  }
};

// 获取更多选项列表
const getMoreOptions = () => {
  switch (currentOptionType.value) {
    case 'type':
      return sceneTypeOptions;
    case 'weather':
      return weatherOptions;
    case 'mood':
      return moodOptions;
    case 'characters':
      return characterOptions;
    default:
      return [];
  }
};

// 检查选项是否被选中
const isOptionSelected = (value) => {
  if (!currentPage.value) return false;

  switch (currentOptionType.value) {
    case 'type':
      return currentPage.value.sceneElements?.type === value;
    case 'weather':
      return currentPage.value.sceneElements?.weather === value;
    case 'mood':
      return currentPage.value.sceneElements?.mood === value;
    case 'characters':
      return currentPage.value.characters && currentPage.value.characters.includes(value);
    default:
      return false;
  }
};

// 选择更多选项中的一项
const selectMoreOption = (value) => {
  if (currentOptionType.value === 'characters') {
    toggleCharacter(value);
  } else {
    updateSceneElement(currentOptionType.value, value);
  }
  showMoreOptionsDialog.value = false;
};

// 更新页面布局
const updatePageLayout = (layout) => {
  if (!currentPage.value) return;

  currentPage.value.layout = layout;

  // 根据布局类型自动调整文字位置
  switch (layout) {
    case 'vertical':
      currentPage.value.textStyle.position = 'bottom';
      break;
    case 'horizontal':
      currentPage.value.textStyle.position = 'right';
      break;

    case 'full-image':
      currentPage.value.textStyle.position = 'overlay';
      break;
    case 'collage':
      // 拼贴布局保持当前文字位置
      break;
  }

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新文字位置
const updateTextPosition = (position) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.position = position;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新水平对齐方式
const updateTextAlignment = (alignment) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.alignment = alignment;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新垂直对齐方式
const updateVerticalAlignment = (alignment) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.verticalAlignment = alignment;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新字体大小
const updateFontSize = (size) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.fontSize = size;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新文字颜色
const updateTextColor = (color) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.color = color;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新文字背景色
const updateTextBackground = (color) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.backgroundColor = color;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 更新背景透明度
const updateBackgroundOpacity = (opacity) => {
  if (!currentPage.value) return;

  currentPage.value.textStyle.opacity = opacity;

  // 更新项目数据
  props.projectData.pages = storyContent.pages;
};

// 纸张尺寸更新函数已移除，默认使用横版比例

// 布局模板相关函数已移除，默认使用图上字下布局

// 获取字体大小值
const getFontSizeValue = (size) => {
  switch (size) {
    case 'small':
      return '0.9rem';
    case 'medium':
      return '1.1rem';
    case 'large':
      return '1.4rem';
    default:
      return '1.1rem';
  }
};

// 获取图片容器样式
const getImageContainerStyle = () => {
  if (!currentPage.value) return {};

  // 默认使用图上字下布局
  return {
    width: '100%',
    height: '75%' // 标准比例，图片占3/4
  };
};

// 获取文本容器样式
const getTextContainerStyle = () => {
  if (!currentPage.value) return {};

  const backgroundColor = currentPage.value.textStyle.backgroundColor;
  const opacity = currentPage.value.textStyle.opacity;

  let style = {
    backgroundColor: backgroundColor === 'transparent' ? 'transparent' : `${backgroundColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`
  };

  // 默认使用图上字下布局
  return {
    ...style,
    width: '100%',
    height: '25%' // 标准比例，文字占1/4
  };
};

// 获取文本内容样式
const getTextContentStyle = () => {
  if (!currentPage.value) return {};

  const alignment = currentPage.value.textStyle.alignment;
  const verticalAlignment = currentPage.value.textStyle.verticalAlignment;
  const fontSize = currentPage.value.textStyle.fontSize;
  const color = currentPage.value.textStyle.color;

  let fontSizeValue;
  switch (fontSize) {
    case 'small':
      fontSizeValue = '0.9rem';
      break;
    case 'medium':
      fontSizeValue = '1.1rem';
      break;
    case 'large':
      fontSizeValue = '1.4rem';
      break;
    default:
      fontSizeValue = '1.1rem';
  }

  return {
    textAlign: alignment,
    fontSize: fontSizeValue,
    color: color,
    padding: '0.75rem',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: verticalAlignment,
    height: '100%'
  };
};

// 纸张尺寸标签函数已移除，默认使用横版比例

// 保存故事内容
const saveStoryContent = () => {
  // 更新项目数据
  props.projectData.pages = storyContent.pages;

  window.$message?.success('故事内容已保存');
};
</script>

<style scoped>
.story-page-creator {
  padding: 0.5rem 0;
  position: relative;
}

.story-page-creator::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 5px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  border-radius: 5px;
  opacity: 0.3;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 1.5rem;
}



.header-content {
  flex: 1;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.25rem;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .step-title {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.dark .step-description {
  color: #94a3b8;
}

/* 书本导航样式 */
.book-guide {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: #f0f9ff;
  padding: 0.75rem;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .book-guide {
  background-color: #0f172a;
}

.book-icon {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

.book-text {
  font-weight: 600;
  color: #3b82f6;
}

.dark .book-text {
  color: #60a5fa;
}

.page-tips {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #fffbeb;
  padding: 0.75rem;
  border-radius: 0.75rem;
  margin-top: 1rem;
  font-size: 0.85rem;
  color: #92400e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .page-tips {
  background-color: #422006;
  color: #fef3c7;
}

.tip-icon {
  font-size: 1.25rem;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.content-layout {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.page-navigation {
  width: 200px;
  flex-shrink: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.page-item {
  cursor: pointer;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.2s ease;
}

.page-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.page-item.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.page-preview {
  height: 100px;
  background-color: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.page-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f1f5f9;
}

.placeholder-icon {
  font-size: 2rem;
  color: #94a3b8;
}

.page-number {
  padding: 0.5rem;
  text-align: center;
  background-color: #f1f5f9;
  font-weight: 600;
  color: #64748b;
}

.page-item.active .page-number {
  background-color: #3b82f6;
  color: white;
}

.add-page {
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border: 2px dashed #cbd5e1;
}

.add-icon {
  font-size: 2rem;
  color: #64748b;
  line-height: 1;
}

.add-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-top: 0.5rem;
}

.page-editor {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dark .page-editor {
  background-color: #1e293b;
  border-color: #334155;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px dashed #e2e8f0;
}

.dark .editor-header {
  border-bottom-color: #334155;
}

.editor-title-wrapper {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.page-emoji {
  font-size: 1.5rem;
  background-color: #f0f9ff;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .page-emoji {
  background-color: #0f172a;
}

.editor-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .editor-title {
  color: #e2e8f0;
}

.delete-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  border-color: #ef4444;
  transition: all 0.3s ease;
}

.delete-button:hover {
  background-color: #fee2e2;
  color: #b91c1c;
}

.dark .delete-button:hover {
  background-color: #7f1d1d;
  color: #fecaca;
}

.delete-icon {
  font-size: 1.1rem;
}

.editor-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page-preview-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  position: relative;
}

.page-preview-large {
  height: 400px;
  background-color: #f8fafc;
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 2px dashed #e2e8f0;
  transition: all 0.3s ease;
}

/* 纸张尺寸样式（固定横版比例） */
.page-preview-large {
  width: auto;
  height: 400px;
  aspect-ratio: 16 / 9; /* 横版：16:9 */
  margin: 0 auto;
  max-width: 711px; /* 保持16:9的比例 */
}

.dark .page-preview-large {
  background-color: #0f172a;
  border-color: #334155;
}

/* 纸张尺寸指示器已移除，默认使用横版比例 */

/* 页面布局容器 */
.page-layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

/* 默认使用图上字下布局 */
.layout-vertical {
  flex-direction: column;
}

/* 图片容器样式 */
.page-image-container {
  overflow: hidden;
  position: relative;
}

/* 文本容器样式 */
.page-text-container {
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 文本内容样式 */
.page-text-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  line-height: 1.5;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.02);
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f1f5f9;
  padding: 1rem;
  text-align: center;
}

.dark .preview-placeholder {
  background-color: #1e293b;
}

.placeholder-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

.placeholder-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #60a5fa;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.5s;
}

.dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.5); opacity: 1; }
}

.placeholder-text {
  font-size: 1rem;
  color: #64748b;
  margin-top: 0.5rem;
  max-width: 250px;
  line-height: 1.4;
}

.dark .placeholder-text {
  color: #94a3b8;
}

.generate-image-btn {
  margin-top: 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 10;
}

.generate-image-btn:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.dark .generate-image-btn {
  background-color: #60a5fa;
  color: #1e293b;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
}

.dark .generate-image-btn:hover {
  background-color: #93c5fd;
  box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
}

.btn-icon {
  font-size: 1.1rem;
}

/* 主标签页容器 */
.main-tabs-container {
  margin-top: 1rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .main-tabs-container {
  background-color: #1e293b;
}

.tab-content {
  padding: 1rem 0;
}

/* 旧样式保留兼容 */
.page-settings {
  margin-top: 1rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .page-settings {
  background-color: #1e293b;
}

.settings-tabs-content {
  padding: 1rem 0;
}

.settings-section {
  margin-bottom: 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .settings-section {
  background-color: #0f172a;
}

/* 布局选项样式 */
.layout-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.layout-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: #f1f5f9;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.dark .layout-option {
  background-color: #1e293b;
}

.layout-option:hover {
  transform: translateY(-2px);
  background-color: #e2e8f0;
}

.dark .layout-option:hover {
  background-color: #334155;
}

.layout-option.active {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.dark .layout-option.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.layout-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.layout-label {
  font-size: 0.8rem;
  text-align: center;
  color: #1e293b;
}

.dark .layout-label {
  color: #e2e8f0;
}

/* 布局预览样式 */
.layout-preview {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.preview-container {
  width: 200px;
  height: 150px;
  background-color: #f1f5f9;
  border-radius: 0.5rem;
  display: flex;
  position: relative;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark .preview-container {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-image-placeholder {
  background-color: #cbd5e1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-weight: bold;
}

.dark .preview-image-placeholder {
  background-color: #475569;
  color: #94a3b8;
}

.preview-text-placeholder {
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-weight: bold;
}

.dark .preview-text-placeholder {
  background-color: #334155;
  color: #94a3b8;
}

/* 不同布局的预览样式 */
.preview-vertical {
  flex-direction: column;
}

.preview-vertical .preview-image-placeholder {
  width: 100%;
  height: 75%; /* 标准绘本比例，图片占3/4 */
}

.preview-vertical .preview-text-placeholder {
  width: 100%;
  height: 25%; /* 标准绘本比例，文字占1/4 */
}

.preview-horizontal {
  flex-direction: row;
}

.preview-horizontal .preview-image-placeholder {
  width: 75%; /* 标准绘本比例，图片占3/4 */
  height: 100%;
}

.preview-horizontal .preview-text-placeholder {
  width: 25%; /* 标准绘本比例，文字占1/4 */
  height: 100%;
}



.preview-full-image .preview-image-placeholder {
  width: 100%;
  height: 100%;
}

.preview-full-image .preview-text-placeholder {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background-color: rgba(226, 232, 240, 0.7);
}

.dark .preview-full-image .preview-text-placeholder {
  background-color: rgba(51, 65, 85, 0.7);
}

.preview-collage {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4px;
}

.preview-collage .preview-image-placeholder {
  grid-column: span 1;
  grid-row: span 1;
}

.preview-collage .preview-text-placeholder {
  grid-column: span 1;
  grid-row: span 1;
}

/* 儿童友好的模板卡片 */
.kid-friendly-templates {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.template-card {
  background-color: #f8fafc;
  border-radius: 1rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dark .template-card {
  background-color: #1e293b;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .template-card.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.template-preview {
  width: 100%;
  height: 100px;
  background-color: #f1f5f9;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.dark .template-preview {
  background-color: #0f172a;
}

.template-preview-inner {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.template-card:hover .template-preview-inner {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.template-image {
  background-color: #cbd5e1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  overflow: hidden;
}

.preview-sample-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.template-card:hover .preview-sample-image {
  transform: scale(1.05);
}

.template-text {
  background-color: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  padding: 0.5rem;
  font-weight: 500;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}

.template-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.badge-icon {
  font-weight: bold;
}

.badge-text {
  font-weight: 600;
}

.template-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.dark .template-name {
  color: #e2e8f0;
}

.template-tip {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  background-color: #f0f9ff;
  padding: 0.75rem;
  border-radius: 0.75rem;
  margin-top: 1.5rem;
}

.dark .template-tip {
  background-color: #0f172a;
}

/* 纸张尺寸选择器样式 */
.paper-size-selector {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
}

.settings-subtitle-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.settings-icon-small {
  font-size: 1.1rem;
}

.settings-subtitle {
  font-size: 1rem;
  font-weight: 600;
  color: #475569;
  margin: 0;
}

.dark .settings-subtitle {
  color: #94a3b8;
}

.paper-size-options {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.paper-size-option {
  flex: 1;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
}

.dark .paper-size-option {
  background-color: #1e293b;
}

.paper-size-option:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.paper-size-option.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .paper-size-option.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.size-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.size-label {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.dark .size-label {
  color: #e2e8f0;
}

.size-dimensions {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
}

.dark .size-dimensions {
  color: #94a3b8;
}

.size-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.dark .size-badge {
  background-color: #60a5fa;
  color: #0f172a;
}

.badge-icon-small {
  font-size: 0.7rem;
  font-weight: bold;
}

.mt-4 {
  margin-top: 1rem;
}

/* 儿童友好的颜色选择器 */
.kid-friendly-colors {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.75rem;
  margin-top: 1rem;
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.color-option {
  position: relative;
  border-radius: 50% !important;
  width: 28px !important;
  height: 28px !important;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option.active {
  border-color: #3b82f6;
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dark .color-option.active {
  border-color: #60a5fa;
}

.color-tooltip {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 10;
}

.color-option:hover .color-tooltip {
  opacity: 1;
  visibility: visible;
}

.check-mark {
  color: #3b82f6;
  font-weight: bold;
  font-size: 0.8rem;
}

@keyframes bounceIn {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 儿童友好的大小按钮 */
.size-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
}

.size-buttons.compact {
  margin-top: 0.5rem;
}

.size-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f1f5f9;
  border: none;
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 30%;
  position: relative;
  overflow: hidden;
}

.size-button.compact {
  padding: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .size-button {
  background-color: #1e293b;
}

.size-button:hover {
  transform: translateY(-5px);
  background-color: #e2e8f0;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.size-button.compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.dark .size-button:hover {
  background-color: #334155;
}

.size-button.active {
  background-color: #dbeafe;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  border: 2px solid #3b82f6;
}

.size-button.compact.active {
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.dark .size-button.active {
  background-color: #1e3a8a;
  border-color: #60a5fa;
}

.settings-section.compact {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

/* 元素选择器样式 */
.element-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #64748b;
  min-width: 60px;
}

.dark .selector-label {
  color: #94a3b8;
}

.selector-icon {
  font-size: 1.2rem;
}

.selector-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.selector-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  background-color: #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.dark .selector-option {
  background-color: #1e293b;
}

.selector-option:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

.dark .selector-option:hover {
  background-color: #334155;
}

.selector-option.active {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.dark .selector-option.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.size-icon-wrapper {
  width: 3rem;
  height: 3rem;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .size-icon-wrapper {
  background-color: #0f172a;
}

.size-button:hover .size-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.size-icon {
  font-size: 2rem;
  transition: all 0.3s ease;
}

.size-label {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.dark .size-label {
  color: #e2e8f0;
}

.size-example {
  font-size: 0.8rem;
  color: #64748b;
  transition: all 0.3s ease;
}

.size-example.medium {
  font-size: 1rem;
}

.size-example.large {
  font-size: 1.2rem;
}

.dark .size-example {
  color: #94a3b8;
}

.size-check {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  animation: bounceIn 0.3s ease;
}

.dark .size-check {
  background-color: #60a5fa;
  color: #1e293b;
}

.size-button.small .size-icon {
  font-size: 1.5rem;
}

.size-button.medium .size-icon {
  font-size: 2rem;
}

.size-button.large .size-icon {
  font-size: 2.5rem;
}

.size-preview-box {
  margin-top: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .size-preview-box {
  background-color: #1e293b;
}

/* 儿童友好的对齐按钮 */
.alignment-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
}

.alignment-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f1f5f9;
  border: none;
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 30%;
  position: relative;
  overflow: hidden;
}

.dark .alignment-button {
  background-color: #1e293b;
}

.alignment-button:hover {
  transform: translateY(-5px);
  background-color: #e2e8f0;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.dark .alignment-button:hover {
  background-color: #334155;
}

.alignment-button.active {
  background-color: #dbeafe;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  border: 2px solid #3b82f6;
}

.dark .alignment-button.active {
  background-color: #1e3a8a;
  border-color: #60a5fa;
}

.alignment-icon-wrapper {
  width: 3rem;
  height: 3rem;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .alignment-icon-wrapper {
  background-color: #0f172a;
}

.alignment-button:hover .alignment-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.alignment-icon {
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.alignment-label {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.dark .alignment-label {
  color: #e2e8f0;
}

.alignment-example {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 80%;
  margin-top: 0.5rem;
}

.example-line {
  height: 0.25rem;
  background-color: #cbd5e1;
  border-radius: 0.125rem;
  transition: all 0.3s ease;
}

.dark .example-line {
  background-color: #475569;
}

.example-line.short {
  width: 60%;
}

.example-line.medium {
  width: 80%;
}

.alignment-example.left {
  align-items: flex-start;
}

.alignment-example.center {
  align-items: center;
}

.alignment-example.right {
  align-items: flex-end;
}

.alignment-check {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  animation: bounceIn 0.3s ease;
}

.dark .alignment-check {
  background-color: #60a5fa;
  color: #1e293b;
}

.alignment-preview-box {
  margin-top: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .alignment-preview-box {
  background-color: #1e293b;
}

.settings-title-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.settings-icon {
  font-size: 1.25rem;
}

.settings-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.dark .settings-title {
  color: #e2e8f0;
}

.scene-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.scene-button {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .scene-button {
  background-color: #0f172a;
  border-color: #334155;
  color: #e2e8f0;
}

.scene-button:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.dark .scene-button:hover {
  background-color: #334155;
}

.scene-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.dark .scene-button.active {
  background-color: #60a5fa;
  color: #1e293b;
  border-color: #60a5fa;
}

.character-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.character-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .character-option {
  background-color: #0f172a;
}

.character-option:hover {
  background-color: #f1f5f9;
  transform: translateX(3px);
}

.dark .character-option:hover {
  background-color: #334155;
}

.character-option.selected {
  background-color: #dbeafe;
  border-left: 3px solid #3b82f6;
}

.dark .character-option.selected {
  background-color: #1e3a8a;
  border-left: 3px solid #60a5fa;
}

.character-checkbox {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 2px solid #cbd5e1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.dark .character-checkbox {
  border-color: #475569;
  background-color: #1e293b;
}

.character-option.selected .character-checkbox {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.dark .character-option.selected .character-checkbox {
  background-color: #60a5fa;
  border-color: #60a5fa;
}

.check-icon {
  color: white;
  font-size: 0.8rem;
}

.dark .check-icon {
  color: #1e293b;
}

.character-name {
  font-size: 0.9rem;
  color: #1e293b;
}

.dark .character-name {
  color: #e2e8f0;
}

.page-text-editor {
  margin-top: 1rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .page-text-editor {
  background-color: #1e293b;
}

.text-editor-tips {
  margin-top: 0.75rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  background-color: #f0f9ff;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.dark .text-editor-tips {
  background-color: #0f172a;
}

.tip-text {
  font-size: 0.85rem;
  color: #3b82f6;
  line-height: 1.4;
}

.dark .tip-text {
  color: #60a5fa;
}

.child-input {
  font-size: 1rem;
  border-radius: 0.75rem;
}

.form-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .form-actions {
  background-color: #1e293b;
}

.save-hint {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.hint-icon {
  font-size: 1.25rem;
  color: #3b82f6;
}

.dark .hint-icon {
  color: #60a5fa;
}

.hint-text {
  font-size: 0.9rem;
  color: #64748b;
}

.dark .hint-text {
  color: #94a3b8;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.save-button:hover {
  background-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.dark .save-button {
  background-color: #60a5fa;
  color: #1e293b;
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
}

.dark .save-button:hover {
  background-color: #93c5fd;
  box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
}

.save-icon {
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
  }

  .page-navigation {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  .page-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  .page-settings {
    flex-direction: column;
    gap: 1rem;
  }
}

/* 紧凑型布局 */
.compact-editor-layout {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.page-text-editor.compact,
.page-style-editor.compact,
.text-style-editor.compact {
  flex: 1;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .page-text-editor.compact,
.dark .page-style-editor.compact,
.dark .text-style-editor.compact {
  background-color: #1e293b;
}

/* 文字样式编辑器 */
.text-style-editor.full-width {
  width: 100%;
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .text-style-editor.full-width {
  background-color: #1e293b;
}

.text-style-selectors {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 场景描述编辑器样式 */
.scene-description-editor {
  margin-top: 1.5rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.scene-description-editor.compact {
  flex: 1.5;
  margin-top: 0;
  padding: 0.75rem;
}

.dark .scene-description-editor {
  background-color: #1e293b;
}

/* 描述预览栏 */
.description-preview-bar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f0f9ff;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  flex-wrap: wrap;
}

.dark .description-preview-bar {
  background-color: #0c4a6e;
}

.preview-label {
  font-weight: 600;
  color: #0369a1;
  white-space: nowrap;
}

.dark .preview-label {
  color: #bae6fd;
}

.preview-content {
  color: #0369a1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.preview-hint {
  font-size: 0.75rem;
  color: #0369a1;
  font-style: italic;
  opacity: 0.8;
  width: 100%;
  margin-top: 0.25rem;
}

.dark .preview-content {
  color: #bae6fd;
}

.dark .preview-hint {
  color: #bae6fd;
}

/* 生成预览按钮 */
.generate-preview-btn {
  margin-left: auto;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
}

.btn-icon {
  margin-right: 0.25rem;
}

/* 场景元素样式 */
.scene-elements-container {
  padding: 0.5rem 0;
}

.scene-category {
  margin-bottom: 1.5rem;
}

/* 场景元素选择器 */
.scene-elements-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.element-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.selector-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  width: 60px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .selector-label {
  color: #e2e8f0;
}

.selector-icon {
  font-size: 1.1rem;
}

.selector-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex: 1;
}

.selector-option {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  font-size: 1.25rem;
}

.dark .selector-option {
  background-color: #1e293b;
}

.selector-option:hover {
  transform: translateY(-2px);
  background-color: #e2e8f0;
}

.dark .selector-option:hover {
  background-color: #334155;
}

.selector-option.active {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.dark .selector-option.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.selector-more {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.7rem;
  color: #64748b;
}

.dark .selector-more {
  background-color: #1e293b;
  color: #94a3b8;
}

.selector-more:hover {
  transform: translateY(-2px);
  background-color: #e2e8f0;
}

.dark .selector-more:hover {
  background-color: #334155;
}

/* 额外描述输入框 */
.extra-description {
  margin-top: 0.5rem;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.category-icon {
  font-size: 1.25rem;
}

.category-title h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .category-title h5 {
  color: #e2e8f0;
}

.scene-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
}

.scene-option-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 0.75rem 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.dark .scene-option-button {
  background-color: #1e293b;
}

.scene-option-button:hover {
  transform: translateY(-2px);
  background-color: #e2e8f0;
}

.dark .scene-option-button:hover {
  background-color: #334155;
}

.scene-option-button.active {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.dark .scene-option-button.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.option-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.option-label {
  font-size: 0.8rem;
  text-align: center;
  color: #1e293b;
}

.dark .option-label {
  color: #e2e8f0;
}

/* 场景细节样式 */
.scene-details-container {
  padding: 0.5rem 0;
}

.scene-description-wrapper {
  margin-bottom: 1rem;
}

.scene-elements-preview {
  margin-top: 1rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px dashed #cbd5e1;
}

.dark .scene-elements-preview {
  background-color: #0f172a;
  border-color: #334155;
}

.preview-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-content {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
  white-space: pre-wrap;
}

.dark .preview-content {
  color: #94a3b8;
}

/* 更多选项对话框 */
.modal-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-icon {
  font-size: 1.5rem;
}

.modal-title {
  font-size: 1.1rem;
  font-weight: 600;
}

.more-options-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.75rem;
  padding: 0.5rem;
}

.more-option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 0.75rem 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.dark .more-option-item {
  background-color: #1e293b;
}

.more-option-item:hover {
  transform: translateY(-2px);
  background-color: #e2e8f0;
}

.dark .more-option-item:hover {
  background-color: #334155;
}

.more-option-item.active {
  border-color: #3b82f6;
  background-color: #dbeafe;
}

.dark .more-option-item.active {
  border-color: #60a5fa;
  background-color: #1e3a8a;
}

.more-option-item .option-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.more-option-item .option-label {
  font-size: 0.9rem;
  text-align: center;
  color: #1e293b;
}

.dark .more-option-item .option-label {
  color: #e2e8f0;
}

/* 生成中遮罩样式 */
.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 0.75rem;
}

.generating-spinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #60a5fa;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.generating-text {
  color: white;
  font-size: 1rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
</style>
