<script setup lang="ts">
import { SvgIcon } from '@/components/common';
import { useChatStore } from '@/store';
import { Switch } from '@headlessui/vue';
import { computed, onMounted, ref, watch } from 'vue';

const chatStore = useChatStore();
const customKeyId = ref(100);
const dataSources = computed(() => chatStore.groupList);
const groupKeyWord = computed(() => chatStore.groupKeyWord);

// 默认插件列表
const defaultPlugins: any[] = [
  // {
  //   pluginId: '1',
  //   pluginName: '思维导图',
  //   description: '支持思维导图的创建、编辑、分享、查看等功能',
  //   pluginImg: mindmap,
  //   parameters: 'usingMindMap',
  // },
  // {
  //   pluginName: 'Midjourney',
  //   description: '使用 Midjourney 绘图, 为你的创意添加更多可能性',
  //   pluginImg: midjourney,
  //   parameters: 'midjourney',
  // },
];

// 计算属性插件列表
const pluginList = computed(() =>
  chatStore.pluginList?.length ? chatStore.pluginList : defaultPlugins
);

watch(dataSources, () => (customKeyId.value = customKeyId.value + 1));
watch(groupKeyWord, () => (customKeyId.value = customKeyId.value + 1));
const usingPlugin = computed(() => chatStore.currentPlugin);

dataSources.value.filter((item) =>
  groupKeyWord.value
    ? item.title.includes(groupKeyWord.value) && item.isSticky
    : item.isSticky
);

function toggleSelection(plugin: any) {
  if (usingPlugin.value?.parameters === plugin.parameters) {
    // Clear the current plugin because it is already selected
    chatStore.setUsingPlugin(null); // Clear the selection
  } else {
    // Set the current plugin to the selected plugin
    chatStore.setUsingPlugin(plugin);
  }
}

onMounted(() => {
  chatStore.queryPlugins();
  chatStore.queryMyGroup();
});
</script>

<template>
  <div
    class="flex flex-col gap-2 px-3 py-2 overflow-y-auto max-h-full scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
  >
    <!-- 插件标题 -->
    <p class="mt-2 mb-2 text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-3">
      可用插件
    </p>

    <!-- 插件列表 -->
    <div
      v-for="plugin in pluginList"
      :key="plugin.parameters"
      class="select-none relative bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 flex flex-col items-start px-3 py-2 break-all rounded-md cursor-pointer group font-medium text-sm transition-all duration-200 border-l-2 border-l-transparent"
    >
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-2">
          <!-- 插件图标 -->
          <div
            class="w-7 h-7 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center overflow-hidden shadow-sm border border-gray-200 dark:border-gray-500"
          >
            <span
              v-if="plugin.pluginImg && plugin.pluginImg.startsWith('emoji:')"
              class="text-xl"
            >
              {{ plugin.pluginImg.replace('emoji:', '') }}
            </span>
            <img
              v-else-if="plugin.pluginImg"
              :src="plugin.pluginImg"
              alt="Plugin icon"
              class="w-full h-full object-cover"
            />
            <span v-else class="text-sm font-medium">
              {{ plugin.pluginName.charAt(0) }}
            </span>
          </div>

          <!-- 插件名称 -->
          <span
            class="line-clamp-1 overflow-hidden text-ellipsis block whitespace-nowrap font-medium text-gray-700 dark:text-gray-200"
          >
            {{ plugin.pluginName }}
          </span>
        </div>

        <!-- 开关控件 -->
        <Switch
          :class="[
            'group relative inline-flex h-5 w-10 flex-shrink-0 cursor-pointer items-center justify-center rounded-full focus:outline-none',
          ]"
          @click.prevent="toggleSelection(plugin)"
        >
          <span
            aria-hidden="true"
            class="pointer-events-none absolute h-full w-full rounded-full bg-white dark:bg-transparent"
          ></span>
          <span
            aria-hidden="true"
            :class="[
              usingPlugin?.parameters === plugin.parameters
                ? 'bg-indigo-600'
                : 'bg-gray-200 dark:bg-gray-700',
              'pointer-events-none absolute mx-auto h-4 w-9 rounded-full transition-colors duration-200 ease-in-out',
            ]"
          ></span>
          <span
            aria-hidden="true"
            :class="[
              usingPlugin?.parameters === plugin.parameters
                ? 'translate-x-5'
                : 'translate-x-0',
              'pointer-events-none absolute left-0 inline-block h-5 w-5 transform rounded-full border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-500 shadow ring-0 transition-transform duration-200 ease-in-out',
            ]"
          ></span>
        </Switch>
      </div>

      <!-- 插件描述 -->
      <div class="mt-1 pl-9">
        <span
          class="text-xs line-clamp-2 text-gray-500 dark:text-gray-400"
        >
          {{ plugin.description }}
        </span>
      </div>
    </div>

    <!-- 无插件时的提示 -->
    <div v-if="!pluginList.length" class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
      <SvgIcon icon="ri:plug-line" class="text-3xl mb-2" />
      <span class="text-sm">暂无可用插件</span>
    </div>
  </div>
</template>
