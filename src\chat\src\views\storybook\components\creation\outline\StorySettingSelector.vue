<template>
  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">🏞️</span>
      故事发生在哪里？什么时候？
    </div>
    <div class="setting-section">
      <div class="setting-cards">
        <div
          v-for="place in placeOptions"
          :key="place.value"
          class="setting-card"
          :class="{ 'active': setting.place === place.value }"
          @click="updatePlace(place.value)"
        >
          <div class="setting-card-icon">{{ place.icon }}</div>
          <div class="setting-card-label">{{ place.label }}</div>
        </div>
        <div class="setting-card custom-setting">
          <div class="setting-card-icon">✏️</div>
          <NInput
            v-model:value="customPlace"
            placeholder="自定义地点..."
            class="custom-setting-input"
            @update:value="updatePlace"
          />
        </div>
      </div>

      <div class="time-selector">
        <div class="form-label secondary-label">
          <span class="form-icon small">🗓️</span>
          什么时候发生的？
        </div>
        <div class="time-buttons">
          <button
            v-for="time in timeOptions"
            :key="time.value"
            type="button"
            :class="['time-button', setting.time === time.value ? 'active' : '']"
            @click="updateTime(time.value)"
          >
            {{ time.label }}
          </button>
          <div class="custom-time">
            <NInput
              v-model:value="customTime"
              placeholder="自定义时间..."
              class="custom-time-input"
              @update:value="updateTime"
            />
          </div>
        </div>
      </div>

      <div class="background-description">
        <div class="form-label secondary-label">
          <span class="form-icon small">✨</span>
          这个地方有什么特别的？
        </div>
        <NInput
          v-model:value="setting.background"
          type="textarea"
          placeholder="例如：这是一个充满魔法的世界，动物们可以说话..."
          :autosize="{ minRows: 2, maxRows: 4 }"
          class="child-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NInput } from 'naive-ui';

// 地点选项
const placeOptions = [
  { label: '魔法森林', value: '魔法森林', icon: '🌳' },
  { label: '王国城堡', value: '王国城堡', icon: '🏰' },
  { label: '海底世界', value: '海底世界', icon: '🌊' },
  { label: '太空冒险', value: '太空冒险', icon: '🚀' },
  { label: '小镇', value: '小镇', icon: '🏘️' },
  { label: '学校', value: '学校', icon: '🏫' },
  { label: '山洞', value: '山洞', icon: '⛰️' },
  { label: '热带雨林', value: '热带雨林', icon: '🌴' },
  { label: '沙漠', value: '沙漠', icon: '🏜️' }
];

// 时间选项
const timeOptions = [
  { label: '很久很久以前', value: '很久很久以前' },
  { label: '现在', value: '现在' },
  { label: '未来世界', value: '未来世界' },
  { label: '四季变换', value: '四季变换' },
  { label: '恐龙时代', value: '恐龙时代' },
  { label: '古代中国', value: '古代中国' },
  { label: '中世纪', value: '中世纪' },
  { label: '一年四季', value: '一年四季' }
];

// 使用defineModel实现双向绑定
const setting = defineModel('setting');

// 自定义地点输入
const customPlace = ref('');

// 自定义时间输入
const customTime = ref('');

// 监听自定义地点变化
watch(() => setting.value.place, (newPlace) => {
  // 如果当前地点不在预设选项中，则更新自定义地点输入框
  if (!placeOptions.some(option => option.value === newPlace)) {
    customPlace.value = newPlace;
  }
});

// 监听自定义时间变化
watch(() => setting.value.time, (newTime) => {
  // 如果当前时间不在预设选项中，则更新自定义时间输入框
  if (!timeOptions.some(option => option.value === newTime)) {
    customTime.value = newTime;
  }
});

// 更新地点
const updatePlace = (place: string) => {
  if (place.trim()) {
    setting.value.place = place;
  }
};

// 更新时间
const updateTime = (time: string) => {
  if (time.trim()) {
    setting.value.time = time;
  }
};
</script>

<style scoped>
.form-group {
  margin-bottom: 2.5rem;
  width: 100%;
  position: relative;
}

.form-group.animated-form-group {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-label {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.form-label.secondary-label {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: #4b5563;
}

.dark .form-label {
  color: #60a5fa;
}

.dark .form-label.secondary-label {
  color: #9ca3af;
}

.form-icon {
  font-size: 1.5rem;
}

.form-icon.small {
  font-size: 1.2rem;
}

/* 故事设定样式 */
.setting-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background-color: #f8fafc;
  padding: 1.5rem;
  border-radius: 1rem;
  border: 2px dashed #93c5fd;
  transition: all 0.3s ease;
}

.setting-section:hover {
  background-color: #f1f5f9;
  border-color: #60a5fa;
}

.dark .setting-section {
  background-color: #1e293b;
  border-color: #60a5fa;
}

.dark .setting-section:hover {
  background-color: #0f172a;
  border-color: #3b82f6;
}

.setting-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.setting-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1rem 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100px;
}

.setting-card:hover {
  transform: translateY(-5px);
  border-color: #93c5fd;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.setting-card.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .setting-card {
  background-color: #334155;
  border-color: #475569;
}

.dark .setting-card:hover {
  border-color: #60a5fa;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.dark .setting-card.active {
  border-color: #3b82f6;
  background-color: #1e3a8a;
}

.setting-card-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.setting-card-label {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  color: #334155;
}

.dark .setting-card-label {
  color: #e2e8f0;
}

.custom-setting {
  cursor: default;
}

.custom-setting:hover {
  transform: none;
  border-color: #e2e8f0;
  box-shadow: none;
}

.dark .custom-setting:hover {
  border-color: #475569;
  box-shadow: none;
}

.custom-setting-input {
  width: 100%;
  margin-top: 0.5rem;
}

.time-selector {
  margin-bottom: 1.5rem;
}

.time-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.time-button {
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  color: #334155;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-button:hover {
  background-color: #f8fafc;
  border-color: #93c5fd;
  transform: translateY(-2px);
}

.time-button.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1e40af;
}

.dark .time-button {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.dark .time-button:hover {
  background-color: #1e293b;
  border-color: #60a5fa;
}

.dark .time-button.active {
  border-color: #3b82f6;
  background-color: #1e3a8a;
  color: #e2e8f0;
}

.custom-time {
  margin-top: 0.5rem;
  width: 100%;
  max-width: 300px;
}

.custom-time-input {
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.custom-time-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .custom-time-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .custom-time-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.child-input {
  font-size: 1.1rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.child-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .child-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .child-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}
</style>
