<script setup lang="ts">
import { ref, computed } from 'vue';
import { N<PERSON><PERSON>er, NDrawerContent, NTabs, NTabPane, NInput, NButton, NSpace, NCard, NDivider } from 'naive-ui';
import InspirationTab from './InspirationTab.vue';
import FoxAssistantChat from './FoxAssistantChat.vue';

const props = defineProps<{
  show: boolean;
  activeTab: string;
  projectData: any;
}>();

const emit = defineEmits(['update:show', 'update:activeTab', 'close']);

// 标签页配置
const tabs = [
  { id: 'magichelper', label: '创作帮助', emoji: '🦊' },
  { id: 'inspirations', label: '灵感收集', emoji: '💡' }
];

// 本地状态，用于绑定到NTabs组件
const localActiveTab = computed({
  get: () => props.activeTab,
  set: (value) => emit('update:activeTab', value)
});

// 更新显示状态
const updateShow = (value) => {
  emit('update:show', value);
};

// 关闭抽屉
const closeDrawer = () => {
  // 触发关闭事件
  emit('close');
};
</script>

<template>
  <NDrawer :show="show" :width="500" placement="right" @update:show="updateShow" class="magic-pen-drawer">
    <NDrawerContent closable @close="closeDrawer">
      <template #header>
        <div class="drawer-header">
          <div class="drawer-title-wrapper">
            <span class="drawer-icon">🦊</span>
            <div class="drawer-title-content">
              <h3 class="drawer-title">小狐狸助手</h3>
              <p class="drawer-subtitle">我来帮你创作精彩故事！</p>
            </div>
          </div>
        </div>
      </template>

      <NDivider class="header-divider" />

      <NTabs v-model:value="localActiveTab" type="segment" animated class="custom-tabs">
        <NTabPane
          v-for="tab in tabs"
          :key="tab.id"
          :name="tab.id"
        >
          <template #tab>
            <div class="tab-label">
              <span class="tab-emoji">{{ tab.emoji }}</span>
              <span>{{ tab.label }}</span>
            </div>
          </template>
        </NTabPane>
      </NTabs>

      <div class="drawer-content">
        <!-- 魔法助手标签页 -->
        <FoxAssistantChat
          v-if="localActiveTab === 'magichelper'"
          :projectData="projectData"
        />

        <!-- 灵感收集标签页 -->
        <InspirationTab
          v-else-if="localActiveTab === 'inspirations'"
          :project-data="projectData"
        />


      </div>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 抽屉标题样式 */
.drawer-header {
  padding: 0.5rem 0;
}

.drawer-title-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.drawer-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #f97316, #fb923c);
  color: white;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(249, 115, 22, 0.3);
  transform: rotate(-5deg);
  transition: all 0.3s ease;
  animation: wiggle 3s ease-in-out infinite;
}

@keyframes wiggle {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

.drawer-title-content {
  display: flex;
  flex-direction: column;
}

.drawer-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #f97316, #fb923c);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.drawer-subtitle {
  font-size: 0.85rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

.dark .drawer-subtitle {
  color: #94a3b8;
}

.header-divider {
  margin: 0.5rem 0 1rem 0;
}

/* 标签页样式 */
.custom-tabs {
  margin-bottom: 1rem;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.tab-emoji {
  font-size: 1.2rem;
}

/* 内容区域样式 */
.drawer-content {
  margin-top: 1rem;
  height: calc(100vh - 200px);
  overflow-y: auto;
  padding: 0 0.5rem;
  border-radius: 0.75rem;
  background-color: rgba(248, 250, 252, 0.5);
}

.dark .drawer-content {
  background-color: rgba(15, 23, 42, 0.5);
}



/* 确保抽屉在绘本阅读器上方显示 */
:deep(.magic-pen-drawer) {
  z-index: 10001 !important; /* 确保高于绘本阅读器的z-index(9990) */
}

:deep(.n-drawer-content) {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.95), rgba(241, 245, 249, 0.95));
}

.dark :deep(.n-drawer-content) {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
}
</style>
