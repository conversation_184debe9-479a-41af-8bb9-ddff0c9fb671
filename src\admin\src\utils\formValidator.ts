/**
 * 表单验证工具
 * 提供表单数据验证功能
 */
import { ElMessage } from 'element-plus';

/**
 * 验证表单字段JSON格式
 * @param fieldsJson 表单字段JSON字符串
 * @returns 是否有效
 */
export function validateFormFieldsJson(fieldsJson: string): boolean {
  try {
    const fields = JSON.parse(fieldsJson);
    if (!Array.isArray(fields)) {
      return false;
    }
    
    // 验证每个字段是否有必要的属性
    for (const field of fields) {
      if (!field.type || !field.name || !field.label) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 验证应用表单数据
 * @param formData 表单数据
 * @returns 是否有效
 */
export function validateAppFormData(formData: any): boolean {
  // 验证表单名称
  if (!formData.formName) {
    ElMessage.warning('表单名称不能为空');
    return false;
  }
  
  // 验证表单字段
  if (!formData.formFields) {
    ElMessage.warning('表单字段不能为空');
    return false;
  }
  
  // 验证表单字段JSON格式
  if (!validateFormFieldsJson(formData.formFields)) {
    ElMessage.warning('表单字段格式不正确');
    return false;
  }
  
  return true;
}

/**
 * 验证应用数据
 * @param appData 应用数据
 * @returns 是否有效
 */
export function validateAppData(appData: any): boolean {
  // 验证应用分类
  if (!appData.catId) {
    ElMessage.warning('应用分类不能为空');
    return false;
  }
  
  // 验证应用名称
  if (!appData.name) {
    ElMessage.warning('应用名称不能为空');
    return false;
  }
  
  // 验证应用描述
  if (!appData.des) {
    ElMessage.warning('应用描述不能为空');
    return false;
  }
  
  return true;
}

export default {
  validateFormFieldsJson,
  validateAppFormData,
  validateAppData
};
