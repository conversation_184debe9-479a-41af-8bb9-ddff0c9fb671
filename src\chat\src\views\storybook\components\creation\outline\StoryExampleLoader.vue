<template>
  <div class="example-loader">
    <button
      ref="buttonRef"
      type="button"
      class="load-example-btn"
      @click.stop="showExampleStories = !showExampleStories"
    >
      <span class="btn-icon">💡</span> 加载示例故事
    </button>
    <div
      ref="dropdownRef"
      class="example-stories-dropdown"
      v-if="showExampleStories"
      @click.stop
    >
      <div class="dropdown-header">
        <span class="dropdown-title">选择一个示例故事</span>
      </div>
      <div
        v-for="(story, index) in exampleStories"
        :key="index"
        class="example-story-item"
        @click="loadExampleStory(story)"
      >
        <span class="story-icon">{{ story.icon }}</span>
        <div class="story-info">
          <span class="story-title">{{ story.title }}</span>
          <span class="story-theme">{{ story.theme }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  outlineData: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['load-example']);

// 示例故事下拉菜单状态
const showExampleStories = ref(false);
const dropdownRef = ref(null);
const buttonRef = ref(null);

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  if (
    showExampleStories.value && 
    dropdownRef.value && 
    !dropdownRef.value.contains(event.target) &&
    buttonRef.value && 
    !buttonRef.value.contains(event.target)
  ) {
    showExampleStories.value = false;
  }
};

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除点击事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 示例故事列表
const exampleStories = [
  {
    icon: '🐰',
    title: '小兔子的勇气',
    theme: '勇气与成长',
    data: {
      title: '小兔子的勇气',
      mainIdea: '勇敢面对挑战，克服恐惧，发现自己的潜力',
      theme: 'courage',
      setting: {
        place: '魔法森林',
        time: '四季变换',
        background: '这是一片神奇的森林，四季分明，动物们和谐相处，但也有各种挑战需要面对。'
      },
      beginning: '在一片郁郁葱葱的森林里，住着一只名叫跳跳的小白兔。跳跳非常胆小，总是害怕尝试新事物，每天只敢在自己的小窝附近活动。',
      middle: '一天，森林里举办了一场寻宝比赛。所有动物都很兴奋，但跳跳却不敢参加。他的好朋友小松鼠鼓励他："跳跳，这是个锻炼勇气的好机会！"在朋友的鼓励下，跳跳决定尝试一下。',
      climax: '比赛开始后，跳跳需要穿过一条湍急的小溪。他站在溪边，腿发抖，几乎要放弃。这时，他想起了朋友的鼓励，深吸一口气，鼓起勇气一跃而过！虽然他的脚被水打湿了，但他成功了！',
      ending: '最终，跳跳没有找到宝藏，但他发现了更重要的东西——勇气。从那以后，跳跳变得更加勇敢，愿意尝试新事物。他明白了，有时候迈出第一步是最难的，但只要勇敢尝试，就能发现自己比想象中更强大。',
      notes: '这个故事适合3-6岁的孩子，可以帮助他们学会面对恐惧，培养勇气。'
    }
  },
  {
    icon: '🌈',
    title: '彩虹桥的秘密',
    theme: '探索与分享',
    data: {
      title: '彩虹桥的秘密',
      mainIdea: '分享美好的事物会带给更多人快乐',
      theme: 'sharing',
      setting: {
        place: '小镇',
        time: '现在',
        background: '一个宁静美丽的小镇，周围有山有水，居民们友好和睦。'
      },
      beginning: '小镇上有一个传说，每当下雨后出现彩虹时，如果能找到彩虹的尽头，就会发现一个神奇的秘密。小女孩莉莉一直梦想着能找到彩虹的尽头。',
      middle: '一天下午，一场大雨过后，天空中出现了一道美丽的彩虹。莉莉立刻骑上自行车，追寻彩虹的方向。她穿过小镇，经过田野，来到了一片从未去过的森林。',
      climax: '在森林深处，莉莉发现彩虹似乎落在一个小湖泊上。当她走近湖边，彩虹的倒影在水中形成了一个完整的圆。就在这时，湖面上出现了七彩的光芒，照亮了整个湖泊！',
      ending: '莉莉惊讶地发现，彩虹的秘密不是什么宝藏，而是这个美丽的景象本身。她拍下照片，带回小镇与大家分享。从此，每当出现彩虹，小镇的人们都会一起去湖边欣赏这奇妙的景象。莉莉明白了，有时候最美的秘密就是大自然的奇迹，而分享这些奇迹能带给更多人快乐。',
      notes: '这个故事可以教导孩子们欣赏自然之美，以及分享的重要性。'
    }
  },
  {
    icon: '🐉',
    title: '友善的小龙',
    theme: '友谊与包容',
    data: {
      title: '友善的小龙',
      mainIdea: '不要以貌取人，真正的友谊需要相互理解和信任',
      theme: 'friendship',
      setting: {
        place: '王国城堡',
        time: '很久很久以前',
        background: '一个被高山环绕的王国，有城堡、村庄和神秘的山洞。'
      },
      beginning: '在一座高高的山上，住着一条名叫火花的小龙。虽然火花能喷火，但他其实非常友善。可惜山下的村民们都害怕龙，从来没有人敢接近他，这让火花感到很孤独。',
      middle: '一天，一个名叫小明的男孩在森林里迷路了。天色已晚，小明又冷又怕。火花发现了小明，想要帮助他。但当火花靠近时，小明吓得直哭。火花不知道该怎么办，最后决定用自己的火焰点燃了一堆木柴，让小明能够取暖。',
      climax: '小明惊讶地发现这条龙并没有伤害他，反而在帮助他。渐渐地，小明不再害怕火花。他们开始交谈，成为了朋友。第二天，火花带着小明飞回了村庄。村民们一开始都很害怕，但小明告诉大家火花是如何救了他。',
      ending: '村民们终于明白了不应该仅仅因为外表或传说就判断他人。他们邀请火花参加村里的庆典，火花用他的火焰表演了精彩的烟火秀。从此以后，火花不再孤独，他成为了村庄的守护者，而村民们也学会了不要以貌取人，真正的友谊需要相互了解和信任。',
      notes: '这个故事可以帮助孩子们理解接纳与包容的重要性，不要因为外表或传言而拒绝他人。'
    }
  },
  {
    icon: '🌟',
    title: '星星的礼物',
    theme: '梦想与友谊',
    data: {
      title: '星星的礼物',
      mainIdea: '梦想和友谊可以带给我们力量和勇气',
      theme: 'dreams',
      setting: {
        place: '小镇',
        time: '现在',
        background: '一个安静的小镇，夜空特别清澈，能看到满天的星星。'
      },
      beginning: '小女孩小雨非常喜欢观察夜空中的星星。每天晚上，她都会坐在窗前，数着星星，对它们说悄悄话。她总是想知道星星是什么样子的，它们是否能听到她说话。',
      middle: '一天晚上，小雨看到一颗流星划过天空。她闭上眼睛许愿："我希望能和星星做朋友。"奇妙的事情发生了，一个发着光的小点落在了她的窗台上。那是一颗会说话的小星星！小星星告诉小雨，它听到了她的心愿，特地来和她做朋友。',
      climax: '小星星告诉小雨，它可以带她去看宇宙中最美丽的景象，但只有一晚上的时间。小雨非常兴奋，她和小星星一起踏上了奇妙的旅程。他们飞过银河，看到了彩色的星云，还遇到了其他友好的星星。这是小雨做过的最美丽的梦。',
      ending: '天亮时，小雨发现自己回到了床上。窗台上有一颗闪闪发光的小石头，那是小星星留给她的礼物。虽然小雨不能每天都去太空旅行，但她知道只要抬头看天空，她的朋友们就在那里。这颗星星石头成为了她最珍贵的宝物，提醒她梦想和友谊的力量。',
      notes: '这个故事可以激发孩子们的想象力和对宇宙的好奇心，同时传递友谊和梦想的美好。'
    }
  }
];

// 加载示例故事
const loadExampleStory = (story) => {
  emit('load-example', story.data);
  showExampleStories.value = false;
  
  // 显示成功提示
  if (window.$message) {
    window.$message.success(`已加载"${story.title}"的故事内容`);
  }
};
</script>

<style scoped>
/* 示例故事按钮样式 */
.example-loader {
  position: relative;
  margin-left: auto;
}

.load-example-btn {
  background-color: #f0f9ff;
  border: 2px solid #bae6fd;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  color: #0369a1;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.load-example-btn:hover {
  background-color: #e0f2fe;
  border-color: #38bdf8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .load-example-btn {
  background-color: #0c4a6e;
  border-color: #0284c7;
  color: #7dd3fc;
}

.dark .load-example-btn:hover {
  background-color: #075985;
  border-color: #0ea5e9;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  font-size: 1.2rem;
}

/* 示例故事下拉菜单 */
.example-stories-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 0.75rem;
  margin-top: 0.5rem;
  z-index: 100;
  border: 2px solid #bae6fd;
  animation: fadeIn 0.3s ease-out;
}

.dark .example-stories-dropdown {
  background-color: #1e293b;
  border-color: #0284c7;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 下拉菜单标题 */
.dropdown-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .dropdown-header {
  border-bottom-color: #334155;
}

.dropdown-title {
  font-weight: 600;
  color: #3b82f6;
  font-size: 0.95rem;
}

.dark .dropdown-title {
  color: #60a5fa;
}

.example-story-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-story-item:hover {
  background-color: #f0f9ff;
  transform: translateX(5px);
}

.dark .example-story-item:hover {
  background-color: #0c4a6e;
}

.story-icon {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: #f0f9ff;
  border-radius: 50%;
  border: 2px solid #bae6fd;
}

.dark .story-icon {
  background-color: #0c4a6e;
  border-color: #0284c7;
}

.story-info {
  display: flex;
  flex-direction: column;
}

.story-title {
  font-weight: 600;
  color: #0369a1;
  font-size: 1rem;
}

.story-theme {
  font-size: 0.8rem;
  color: #64748b;
}

.dark .story-title {
  color: #7dd3fc;
}

.dark .story-theme {
  color: #94a3b8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .example-stories-dropdown {
    width: 250px;
    right: auto;
    left: 0;
  }
}
</style>
