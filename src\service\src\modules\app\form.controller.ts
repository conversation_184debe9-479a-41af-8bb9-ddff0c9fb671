import { AdminAuthGuard } from '@/common/auth/adminAuth.guard';
import { SuperAuthGuard } from '@/common/auth/superAuth.guard';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FormService } from './form.service';
import { CreateFormDto, DeleteFormDto, QueryFormDto, UpdateFormDto } from './dto/form.dto';

@ApiTags('Form')
@Controller('form')
export class FormController {
  constructor(private readonly formService: FormService) {}

  /**
   * 创建表单
   * @param body 创建表单DTO
   * @returns 创建的表单
   */
  @Post('create')
  @ApiOperation({ summary: '创建表单' })
  @UseGuards(SuperAuthGuard)
  @ApiBearerAuth()
  createForm(@Body() body: CreateFormDto) {
    return this.formService.createForm(body);
  }

  /**
   * 更新表单
   * @param body 更新表单DTO
   * @returns 更新结果
   */
  @Post('update')
  @ApiOperation({ summary: '更新表单' })
  @UseGuards(SuperAuthGuard)
  @ApiBearerAuth()
  updateForm(@Body() body: UpdateFormDto) {
    return this.formService.updateForm(body);
  }

  /**
   * 删除表单
   * @param body 删除表单DTO
   * @returns 删除结果
   */
  @Post('delete')
  @ApiOperation({ summary: '删除表单' })
  @UseGuards(SuperAuthGuard)
  @ApiBearerAuth()
  deleteForm(@Body() body: DeleteFormDto) {
    return this.formService.deleteForm(body);
  }

  /**
   * 查询表单列表
   * @param query 查询表单DTO
   * @returns 表单列表和总数
   */
  @Get('query')
  @ApiOperation({ summary: '查询表单列表' })
  @UseGuards(AdminAuthGuard)
  @ApiBearerAuth()
  queryForms(@Query() query: QueryFormDto) {
    return this.formService.queryForms(query);
  }

  /**
   * 根据ID获取表单详情
   * @param id 表单ID
   * @returns 表单详情
   */
  @Get('getById/:id')
  @ApiOperation({ summary: '根据ID获取表单详情' })
  getFormById(@Param('id') id: number) {
    return this.formService.getFormById(id);
  }

  /**
   * 获取应用关联的所有表单
   * @param appId 应用ID
   * @returns 表单列表
   */
  @Get('getByAppId/:appId')
  @ApiOperation({ summary: '获取应用关联的所有表单' })
  getFormsByAppId(@Param('appId') appId: number) {
    return this.formService.getFormsByAppId(appId);
  }
}
