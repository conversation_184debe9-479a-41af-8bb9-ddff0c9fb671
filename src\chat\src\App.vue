<script setup lang="ts">
import favicon from '@/assets/favicon.ico';
import { NaiveProvider } from '@/components/common';
import GlobalUserCenterModal from '@/components/common/GlobalUserCenterModal.vue';
import { useLanguage } from '@/hooks/useLanguage';
import { useTheme } from '@/hooks/useTheme';
import { useAuthStore, useChatStore, useGlobalStoreWithOut } from '@/store';
import { ClientJS } from 'clientjs';
import { dateZhCN, NConfigProvider, NGlobalStyle } from 'naive-ui';
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ss } from './utils/storage';
const client = new ClientJS();
const chatStore = useChatStore();

// Get the client's fingerprint id
const fingerprint = client.getFingerprint();
const authStore = useAuthStore();
const useGlobalStore = useGlobalStoreWithOut();
const router = useRouter();
useGlobalStore.updateFingerprint(fingerprint);
const { theme, lightThemeOverrides, darkThemeOverrides } = useTheme();
const { naiveUILocale } = useLanguage();

const homePath = computed(() => authStore.globalConfig?.clientHomePath);
const faviconPath = computed(
  () => authStore.globalConfig?.clientFavoIconPath || favicon
);
const isAutoOpenNotice = computed(
  () => Number(authStore.globalConfig?.isAutoOpenNotice) === 1
);

async function loadBaiduCode() {
  const baiduCode: any = authStore.globalConfig?.baiduCode || '';
  if (!baiduCode) return;
  const scriptElem = document.createElement('script');
  const escapedCode = baiduCode.replace(
    /<script[\s\S]*?>([\s\S]*?)<\/script>/gi,
    '$1'
  );
  scriptElem.innerHTML = escapedCode;
  document.head.appendChild(scriptElem);
}

function setDocumentTitle() {
  document.title = authStore.globalConfig?.siteName || 'AI';
}

const themeOverrides = computed(() => {
  const config = !theme.value ? lightThemeOverrides : darkThemeOverrides;
  return config;
});

function goHome() {
  // 如果当前路径是分享页面，则不进行重定向
  const currentPath = window.location.hash;
  if (currentPath.includes('/share/') || currentPath.includes('/s/')) {
    console.log('当前是分享页面，不进行首页重定向');
    return;
  }

  // 如果设置了homePath且不为空，则使用它，否则使用storybook作为默认首页
  if (homePath.value && homePath.value !== '/storybook') {
    router.replace(homePath.value);
  } else {
    router.replace('/storybook');
  }
}

function noticeInit() {
  const showNotice = ss.get('showNotice');
  if (!showNotice && isAutoOpenNotice.value) {
    useGlobalStore.updateNoticeDialog(true);
  } else {
    if (Date.now() > Number(showNotice) && isAutoOpenNotice.value)
      useGlobalStore.updateNoticeDialog(true);
  }
}

/* 动态设置网站ico svg格式 */
const link = document.createElement('link');
link.rel = 'shortcut icon';
link.href = faviconPath.value;
// link.type = 'image/svg+xml';
document.getElementsByTagName('head')[0].appendChild(link);

onMounted(async () => {
  goHome();
  await chatStore.getBaseModelConfig();
  loadBaiduCode();
  setDocumentTitle();
  noticeInit();
});
</script>

<template>
  <NConfigProvider
    class="h-full"
    :theme="theme"
    :theme-overrides="themeOverrides"
    :locale="naiveUILocale"
    :date-locale="dateZhCN"
    preflight-style-disabled
  >
    <NaiveProvider>
      <RouterView />
      <GlobalUserCenterModal />
    </NaiveProvider>
    <NGlobalStyle />
  </NConfigProvider>
</template>
