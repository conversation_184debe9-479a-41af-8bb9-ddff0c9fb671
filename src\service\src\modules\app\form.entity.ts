import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'app_form' })
export class FormEntity extends BaseEntity {
  @Column({ comment: '表单名称' })
  name: string;

  @Column({ comment: '表单描述', nullable: true })
  description: string;

  @Column({ comment: '关联的应用ID' })
  appId: number;

  @Column({ comment: '表单字段定义', type: 'text' })
  fields: string;

  @Column({ comment: '表单排序、数字越大越靠前', default: 100 })
  order: number;

  @Column({ comment: '表单状态 0：禁用 1：启用', default: 1 })
  status: number;
}
