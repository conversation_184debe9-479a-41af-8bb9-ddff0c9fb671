# API使用示例

本文档提供了使用各种编程语言和框架调用AI聊天系统API的示例代码。

## JavaScript/TypeScript (Axios)

### 用户登录

```javascript
import axios from 'axios';

async function login(username, password) {
  try {
    const response = await axios.post('http://your-api-base-url/auth/login', {
      username,
      password
    });
    
    const token = response.data.data;
    // 保存token
    localStorage.setItem('token', token);
    
    return token;
  } catch (error) {
    console.error('登录失败:', error.response?.data?.message || error.message);
    throw error;
  }
}
```

### 聊天对话（同步）

```javascript
import axios from 'axios';

async function chatSync(prompt, options = {}) {
  try {
    const response = await axios.post('http://your-api-base-url/chatgpt/chat-sync', {
      prompt,
      options: {
        parentMessageId: options.parentMessageId || '',
        model: options.model || 'gpt-3.5-turbo',
        temperature: options.temperature || 0.7,
        groupId: options.groupId
      },
      systemMessage: options.systemMessage
    }, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.data.data;
  } catch (error) {
    console.error('聊天失败:', error.response?.data?.message || error.message);
    throw error;
  }
}
```

### 聊天对话（流式）

```javascript
async function chatStream(prompt, options = {}, onProgress) {
  try {
    const response = await fetch('http://your-api-base-url/chatgpt/chat-process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        prompt,
        options: {
          parentMessageId: options.parentMessageId || '',
          model: options.model || 'gpt-3.5-turbo',
          temperature: options.temperature || 0.7,
          groupId: options.groupId
        },
        systemMessage: options.systemMessage
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      
      const text = decoder.decode(value);
      buffer += text;
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            onProgress && onProgress(data);
          } catch (e) {
            console.error('解析JSON失败:', e, line);
          }
        }
      }
    }
  } catch (error) {
    console.error('流式聊天失败:', error);
    throw error;
  }
}

// 使用示例
chatStream('你好，请介绍一下自己', {}, (data) => {
  console.log('收到数据:', data.text);
  // 更新UI显示
  document.getElementById('response').textContent = data.text;
});
```

## Vue.js 3 示例

### 聊天组件

```vue
<template>
  <div class="chat-container">
    <div class="messages" ref="messagesContainer">
      <div v-for="(message, index) in messages" :key="index" :class="['message', message.role]">
        <div class="avatar">
          <img :src="message.role === 'user' ? userAvatar : aiAvatar" alt="avatar" />
        </div>
        <div class="content" v-html="formatMessage(message.content)"></div>
      </div>
      <div v-if="loading" class="message assistant">
        <div class="avatar">
          <img :src="aiAvatar" alt="avatar" />
        </div>
        <div class="content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="input-area">
      <textarea 
        v-model="userInput" 
        @keydown.enter.prevent="sendMessage"
        placeholder="输入消息..."
        :disabled="loading"
      ></textarea>
      <button @click="sendMessage" :disabled="loading || !userInput.trim()">
        发送
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';

// 配置marked使用highlight.js
marked.setOptions({
  highlight: function(code, lang) {
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  },
  langPrefix: 'hljs language-'
});

const props = defineProps({
  groupId: {
    type: Number,
    default: null
  },
  userAvatar: {
    type: String,
    default: '/user-avatar.png'
  },
  aiAvatar: {
    type: String,
    default: '/ai-avatar.png'
  }
});

const messages = ref([]);
const userInput = ref('');
const loading = ref(false);
const messagesContainer = ref(null);
const parentMessageId = ref('');

// 格式化消息，将markdown转换为HTML
const formatMessage = (content) => {
  return marked(content);
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || loading.value) return;
  
  const userMessage = userInput.value;
  messages.value.push({ role: 'user', content: userMessage });
  userInput.value = '';
  scrollToBottom();
  
  loading.value = true;
  
  try {
    // 添加一个空的AI回复消息
    const aiMessageIndex = messages.value.length;
    messages.value.push({ role: 'assistant', content: '' });
    
    // 调用流式聊天API
    await chatStream(userMessage, {
      parentMessageId: parentMessageId.value,
      groupId: props.groupId
    }, (data) => {
      // 更新AI回复内容
      messages.value[aiMessageIndex].content = data.text;
      scrollToBottom();
      
      // 保存parentMessageId用于下一次对话
      if (data.parentMessageId) {
        parentMessageId.value = data.parentMessageId;
      }
    });
  } catch (error) {
    console.error('发送消息失败:', error);
    messages.value.push({ 
      role: 'system', 
      content: `发送消息失败: ${error.message || '未知错误'}` 
    });
  } finally {
    loading.value = false;
    scrollToBottom();
  }
};

// 监听groupId变化，加载历史消息
watch(() => props.groupId, async (newGroupId) => {
  if (newGroupId) {
    try {
      // 这里可以添加加载历史消息的逻辑
      // const history = await loadChatHistory(newGroupId);
      // messages.value = history;
    } catch (error) {
      console.error('加载历史消息失败:', error);
    }
  }
}, { immediate: true });

onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped>
/* 样式省略 */
</style>
```

## React 示例

```jsx
import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ChatComponent = ({ groupId, userAvatar, aiAvatar }) => {
  const [messages, setMessages] = useState([]);
  const [userInput, setUserInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [parentMessageId, setParentMessageId] = useState('');
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!userInput.trim() || loading) return;
    
    const userMessage = userInput;
    setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
    setUserInput('');
    
    setLoading(true);
    
    try {
      // 添加一个空的AI回复消息
      setMessages(prev => [...prev, { role: 'assistant', content: '' }]);
      
      // 调用流式聊天API
      await chatStream(userMessage, {
        parentMessageId,
        groupId
      }, (data) => {
        // 更新AI回复内容
        setMessages(prev => {
          const newMessages = [...prev];
          newMessages[newMessages.length - 1].content = data.text;
          return newMessages;
        });
        
        // 保存parentMessageId用于下一次对话
        if (data.parentMessageId) {
          setParentMessageId(data.parentMessageId);
        }
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      setMessages(prev => [...prev, { 
        role: 'system', 
        content: `发送消息失败: ${error.message || '未知错误'}` 
      }]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((message, index) => (
          <div key={index} className={`message ${message.role}`}>
            <div className="avatar">
              <img 
                src={message.role === 'user' ? userAvatar : aiAvatar} 
                alt="avatar" 
              />
            </div>
            <div className="content">
              <ReactMarkdown
                children={message.content}
                components={{
                  code({node, inline, className, children, ...props}) {
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline && match ? (
                      <SyntaxHighlighter
                        children={String(children).replace(/\n$/, '')}
                        style={tomorrow}
                        language={match[1]}
                        PreTag="div"
                        {...props}
                      />
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    )
                  }
                }}
              />
            </div>
          </div>
        ))}
        {loading && (
          <div className="message assistant">
            <div className="avatar">
              <img src={aiAvatar} alt="avatar" />
            </div>
            <div className="content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="input-area">
        <textarea 
          value={userInput} 
          onChange={(e) => setUserInput(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              sendMessage();
            }
          }}
          placeholder="输入消息..."
          disabled={loading}
        ></textarea>
        <button 
          onClick={sendMessage} 
          disabled={loading || !userInput.trim()}
        >
          发送
        </button>
      </div>
    </div>
  );
};

export default ChatComponent;
```
