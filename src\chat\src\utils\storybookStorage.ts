/**
 * AI绘本创作数据持久化服务
 * 用于管理AI绘本创作的数据保存和恢复
 */

// 存储键名
const STORYBOOK_PROJECT_KEY = 'storybook-project-data';
const STORYBOOK_CURRENT_STEP_KEY = 'storybook-current-step';
const STORYBOOK_ACTIVE_TAB_KEY = 'storybook-active-tab';

/**
 * 保存绘本项目数据
 * @param projectData 项目数据对象
 */
export function saveStorybookProject(projectData: any): void {
  try {
    // 更新时间戳
    projectData.updatedAt = new Date().toISOString();
    
    // 保存到localStorage
    localStorage.setItem(STORYBOOK_PROJECT_KEY, JSON.stringify(projectData));
    console.log('[AI绘本创作] 项目数据已保存到本地存储');
  } catch (error) {
    console.error('[AI绘本创作] 保存项目数据失败:', error);
  }
}

/**
 * 获取绘本项目数据
 * @returns 项目数据对象，如果不存在则返回null
 */
export function getStorybookProject(): any | null {
  try {
    const projectDataStr = localStorage.getItem(STORYBOOK_PROJECT_KEY);
    if (!projectDataStr) return null;
    
    return JSON.parse(projectDataStr);
  } catch (error) {
    console.error('[AI绘本创作] 获取项目数据失败:', error);
    return null;
  }
}

/**
 * 清除绘本项目数据
 */
export function clearStorybookProject(): void {
  try {
    localStorage.removeItem(STORYBOOK_PROJECT_KEY);
    console.log('[AI绘本创作] 项目数据已清除');
  } catch (error) {
    console.error('[AI绘本创作] 清除项目数据失败:', error);
  }
}

/**
 * 保存当前步骤
 * @param step 当前步骤
 */
export function saveCurrentStep(step: number): void {
  try {
    localStorage.setItem(STORYBOOK_CURRENT_STEP_KEY, step.toString());
  } catch (error) {
    console.error('[AI绘本创作] 保存当前步骤失败:', error);
  }
}

/**
 * 获取当前步骤
 * @returns 当前步骤，如果不存在则返回1
 */
export function getCurrentStep(): number {
  try {
    const step = localStorage.getItem(STORYBOOK_CURRENT_STEP_KEY);
    return step ? parseInt(step, 10) : 1;
  } catch (error) {
    console.error('[AI绘本创作] 获取当前步骤失败:', error);
    return 1;
  }
}

/**
 * 保存当前活动标签
 * @param tab 当前活动标签
 */
export function saveActiveTab(tab: string): void {
  try {
    localStorage.setItem(STORYBOOK_ACTIVE_TAB_KEY, tab);
  } catch (error) {
    console.error('[AI绘本创作] 保存当前活动标签失败:', error);
  }
}

/**
 * 获取当前活动标签
 * @returns 当前活动标签，如果不存在则返回'outline'
 */
export function getActiveTab(): string {
  try {
    const tab = localStorage.getItem(STORYBOOK_ACTIVE_TAB_KEY);
    return tab || 'outline';
  } catch (error) {
    console.error('[AI绘本创作] 获取当前活动标签失败:', error);
    return 'outline';
  }
}

/**
 * 创建新的绘本项目
 * @returns 新的项目数据对象
 */
export function createNewStorybookProject(): any {
  // 清除旧数据
  clearStorybookProject();
  saveCurrentStep(1);
  saveActiveTab('outline');
  
  // 创建新项目
  const newProject = {
    id: Date.now(),
    title: '我的新绘本',
    description: '',
    notes: '',
    // AI魔笔相关数据
    aiMagicPen: {
      inspirations: [], // 保存的灵感
      outlines: [],     // 构思的大纲
      questions: []     // 提问历史
    },
    // 创作相关数据
    outline: {
      mainIdea: '',
      characters: [],
      scenes: []
    },
    pages: [],
    references: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  // 保存到localStorage
  saveStorybookProject(newProject);
  
  return newProject;
}
