/**
 * 表单验证工具类
 */
export class FormValidator {
  /**
   * 验证表单字段JSON格式
   * @param fieldsJson 表单字段JSON字符串
   * @returns 是否有效
   */
  static validateFormFieldsJson(fieldsJson: string): boolean {
    try {
      const fields = JSON.parse(fieldsJson);
      if (!Array.isArray(fields)) {
        return false;
      }
      
      // 验证每个字段是否有必要的属性
      for (const field of fields) {
        if (!field.type || !field.name || !field.label) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 验证表单字段名称是否符合规范
   * @param name 字段名称
   * @returns 是否有效
   */
  static validateFieldName(name: string): boolean {
    // 字段名称只能包含字母、数字和下划线，且必须以字母开头
    const nameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
    return nameRegex.test(name);
  }

  /**
   * 验证表单字段类型是否支持
   * @param type 字段类型
   * @returns 是否支持
   */
  static validateFieldType(type: string): boolean {
    const supportedTypes = [
      'input',
      'textarea',
      'select',
      'radio',
      'checkbox',
      'switch',
      'slider',
      'date',
      'time',
      'datetime',
      'rate',
      'color',
      'number'
    ];
    return supportedTypes.includes(type);
  }

  /**
   * 验证表单字段选项是否有效
   * @param options 字段选项
   * @returns 是否有效
   */
  static validateFieldOptions(options: any[]): boolean {
    if (!Array.isArray(options) || options.length === 0) {
      return false;
    }

    // 验证每个选项是否有必要的属性
    for (const option of options) {
      if (typeof option === 'object' && (!option.label || !option.value)) {
        return false;
      } else if (typeof option !== 'object' && typeof option !== 'string') {
        return false;
      }
    }

    return true;
  }

  /**
   * 验证表单字段是否有效
   * @param field 字段对象
   * @returns 是否有效
   */
  static validateField(field: any): boolean {
    // 验证必要属性
    if (!field.type || !field.name || !field.label) {
      return false;
    }

    // 验证字段名称
    if (!this.validateFieldName(field.name)) {
      return false;
    }

    // 验证字段类型
    if (!this.validateFieldType(field.type)) {
      return false;
    }

    // 验证选项类型字段的选项
    if (['select', 'radio', 'checkbox'].includes(field.type) && field.options) {
      if (!this.validateFieldOptions(field.options)) {
        return false;
      }
    }

    // 验证数值类型字段的范围
    if (['number', 'slider'].includes(field.type)) {
      if (field.min !== undefined && field.max !== undefined && field.min > field.max) {
        return false;
      }
    }

    return true;
  }

  /**
   * 验证表单字段列表是否有效
   * @param fields 字段列表
   * @returns 是否有效
   */
  static validateFields(fields: any[]): boolean {
    if (!Array.isArray(fields)) {
      return false;
    }

    // 验证每个字段
    for (const field of fields) {
      if (!this.validateField(field)) {
        return false;
      }
    }

    // 验证字段名称是否重复
    const fieldNames = fields.map(field => field.name);
    const uniqueFieldNames = new Set(fieldNames);
    if (fieldNames.length !== uniqueFieldNames.size) {
      return false;
    }

    return true;
  }
}
