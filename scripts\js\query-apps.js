const mysql = require('mysql2/promise');

async function queryDatabase() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    user: 'root',
    password: '123456',
    database: 'chatgpt'
  });

  try {
    // 查询应用分类
    console.log('=== 应用分类 ===');
    const [appCats] = await connection.execute('SELECT * FROM app_cats');
    console.log(JSON.stringify(appCats, null, 2));

    // 查询应用
    console.log('\n=== 应用列表 ===');
    const [apps] = await connection.execute('SELECT id, name, catId, des, status FROM app');
    console.log(JSON.stringify(apps, null, 2));
  } catch (error) {
    console.error('查询出错:', error);
  } finally {
    // 关闭连接
    await connection.end();
  }
}

queryDatabase();
