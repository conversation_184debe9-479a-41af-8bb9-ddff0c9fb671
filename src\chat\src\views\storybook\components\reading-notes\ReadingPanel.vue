<script setup lang="ts">
import { ref, watch } from 'vue';
import { NButton } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import BookReader from '@/components/BookReader/index.vue';

const props = defineProps<{
  bookData: any;
  landscape?: boolean;
}>();

const emit = defineEmits<{
  (e: 'page-flip', pageNumber: number): void;
  (e: 'toggle-orientation'): void;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 状态
const showReader = ref(true);
const currentPage = ref(0);

// 处理页面翻转
const handlePageFlip = (pageNumber) => {
  currentPage.value = pageNumber;
  emit('page-flip', pageNumber);
};

// 切换横竖版模式
const toggleOrientation = () => {
  emit('toggle-orientation');
};
</script>

<template>
  <div class="reading-panel" :class="{ 'landscape-mode': props.landscape }">
    <div class="panel-header">
      <h2 class="panel-title">绘本阅读</h2>
      <div class="panel-controls">
        <NButton size="small" @click="toggleOrientation" class="control-button">
          <SvgIcon :name="props.landscape ? 'ri:smartphone-line' : 'ri:tablet-line'" size="16" />
          {{ props.landscape ? '切换竖版' : '切换横版' }}
        </NButton>
      </div>
    </div>

    <div class="reader-container">
      <BookReader
        v-if="showReader"
        :pages="props.bookData.pages"
        :width="isMobile ? 320 : (props.landscape ? 700 : 450)"
        :height="isMobile ? 420 : (props.landscape ? 525 : 600)"
        :show-cover="true"
        :landscape="props.landscape"
        @page-flip="handlePageFlip"
        @close="showReader = false"
      />
    </div>
  </div>
</template>

<style scoped>
.reading-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.dark .reading-panel {
  background-color: #1e293b;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dark .panel-header {
  border-bottom-color: #334155;
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .panel-title {
  color: #e2e8f0;
}

.panel-controls {
  display: flex;
  gap: 0.5rem;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.reader-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  overflow: hidden;
}

/* 横板模式样式 */
.landscape-mode .reader-container {
  padding: 1rem;
}
</style>
