import axios, { type AxiosResponse } from 'axios'
import { useAuthStore, useGlobalStore } from '@/store'

// 打印环境变量，帮助调试
console.log('[AXIOS CONFIG] VITE_GLOB_API_URL:', import.meta.env.VITE_GLOB_API_URL);
console.log('[AXIOS CONFIG] VITE_APP_API_BASE_URL:', import.meta.env.VITE_APP_API_BASE_URL);
console.log('[AXIOS CONFIG] 当前域名:', window.location.origin);
console.log('[AXIOS CONFIG] 当前路径:', window.location.pathname);

// 确保baseURL不为空，如果为空则使用默认值
const apiBaseUrl = import.meta.env.VITE_GLOB_API_URL || '/api';
console.log('[AXIOS CONFIG] 最终使用的baseURL:', apiBaseUrl);

const service = axios.create({
  baseURL: apiBaseUrl,
  timeout: 2400 * 1000,
  // 禁用自动重定向，避免重定向循环
  maxRedirects: 0,
  // 自定义状态码验证，接受所有非500错误的状态码
  validateStatus: function (status) {
    return status >= 200 && status < 500;
  },
  paramsSerializer: {
    // 确保数字参数不被转换为字符串
    serialize: (params) => {
      console.log('序列化前的参数:', params);
      const parts = [];

      for (const key in params) {
        const value = params[key];
        if (value === null || typeof value === 'undefined') {
          continue;
        }

        if (Array.isArray(value)) {
          value.forEach(item => {
            parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(item)}`);
          });
        } else {
          parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
      }

      const result = parts.join('&');
      console.log('序列化后的URL参数:', result);
      return result;
    }
  }
})

service.interceptors.request.use(
  (config) => {
    console.log('[AXIOS] 发送请求:', config.method?.toUpperCase(), config.url);

    // 确保URL路径正确
    if (config.url && !config.url.startsWith('/') && config.baseURL && !config.baseURL.endsWith('/')) {
      config.url = '/' + config.url;
      console.log('[AXIOS] URL路径已修正:', config.url);
    }

    console.log('[AXIOS] 完整请求URL:', config.baseURL + (config.url || ''));
    console.log('[AXIOS] 请求配置:', {
      baseURL: config.baseURL,
      url: config.url,
      method: config.method,
      params: config.params,
      timeout: config.timeout,
      withCredentials: config.withCredentials
    });

    // 记录请求体
    if (config.data) {
      console.log('[AXIOS] 请求体类型:', typeof config.data);
      if (config.data.htmlContent) {
        console.log('[AXIOS] 包含 htmlContent, 长度:', config.data.htmlContent.length);
        console.log('[AXIOS] htmlContent 类型:', typeof config.data.htmlContent);
        // 仅显示前100个字符作为预览
        console.log('[AXIOS] htmlContent 预览:', config.data.htmlContent.substring(0, 100) + '...');
      } else {
        console.log('[AXIOS] 请求体:', config.data);
      }
    }

    const token = useAuthStore().token
    const fingerprint = useGlobalStore()?.fingerprint
		const currentDomain = window.location.origin;
		config.headers['X-Website-Domain'] = currentDomain;
    fingerprint && (config.headers.Fingerprint = fingerprint)
    if (token)
      config.headers.Authorization = `Bearer ${token}`

    console.log('[AXIOS] 请求头:', config.headers);
    console.log('[AXIOS] 最终请求配置:', config);
    return config
  },
  (error) => {
    console.error('[AXIOS] 请求拦截器错误:', error);
    return Promise.reject(error.response)
  },
)

service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    console.log('[AXIOS] 响应状态:', response.status);
    console.log('[AXIOS] 响应头:', response.headers);

    // 对于分享相关的响应，记录更多信息
    if (response.config.url?.includes('/share/')) {
      console.log('[AXIOS] 分享相关响应数据:', response.data);

      // 检查响应数据结构
      if (response.data) {
        console.log('[AXIOS] 响应数据结构详情:', {
          hasData: !!response.data.data,
          dataType: typeof response.data.data,
          dataContent: response.data.data,
          nestedData: response.data.data?.data,
          nestedDataType: typeof response.data.data?.data,
          possibleShareCode1: response.data.data?.data?.shareCode,
          possibleShareCode2: response.data.data?.shareCode,
          possibleShareCode3: response.data.shareCode
        });
      }
    } else {
      // 对于其他响应，简单记录
      console.log('[AXIOS] 响应数据:', response.data);
    }

    if ([200, 201].includes(response.status))
      return response

    console.error('[AXIOS] 响应状态错误:', response.status);
    throw new Error(response.status.toString())
  },
  (error) => {
    console.error('[AXIOS] 响应错误:', error);
    if (error.response) {
      console.error('[AXIOS] 错误状态:', error.response.status);
      console.error('[AXIOS] 错误数据:', error.response.data);
    } else if (error.request) {
      console.error('[AXIOS] 请求已发送但无响应');
    } else {
      console.error('[AXIOS] 请求配置错误:', error.message);
    }
    return Promise.reject(error)
  },
)

export default service
