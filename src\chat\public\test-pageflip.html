<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PageFlip.js Test</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    h1 {
      margin-bottom: 20px;
    }
    
    .container {
      width: 100%;
      max-width: 1200px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    #book {
      margin-bottom: 20px;
    }
    
    .page {
      background-color: white;
      color: #333;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .page-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 15px;
      box-sizing: border-box;
      position: relative;
    }
    
    .page-image {
      flex: 1;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      margin-bottom: 15px;
      border-radius: 4px;
    }
    
    .page-text {
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 20px;
    }
    
    .page-number {
      position: absolute;
      bottom: 10px;
      right: 10px;
      font-size: 12px;
      color: #999;
    }
    
    .controls {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    button {
      padding: 8px 16px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    .status {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>PageFlip.js Test</h1>
    
    <div id="book"></div>
    
    <div class="controls">
      <button id="prev">Previous Page</button>
      <button id="next">Next Page</button>
    </div>
    
    <div class="status">
      Page: <span id="page-number">0</span> / <span id="total-pages">0</span>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/page-flip@2.0.7/dist/js/page-flip.browser.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Create PageFlip instance
      const pageFlip = new PageFlip(document.getElementById('book'), {
        width: 550,
        height: 733,
        size: "fixed",
        maxShadowOpacity: 0.5,
        showCover: true,
        mobileScrollSupport: true
      });
      
      // Sample pages data
      const pagesData = [
        { 
          type: 'hard',
          image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20cover%20illustration',
          text: '小王子'
        },
        { 
          image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20page%201%20illustration',
          text: '当我六岁的时候，在一本描述原始森林的名叫《真实的故事》的书中，看到了一副精彩的插画，画的是一条蟒蛇正在吞食一只野兽。'
        },
        { 
          image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20page%202%20illustration',
          text: '于是我度过了我生命中孤独的岁月，没有一个能真正谈得来的人，直到六年前在沙漠上发生了那次故障。'
        },
        { 
          image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20page%203%20illustration',
          text: '这时，一个奇怪的小家伙突然出现在我面前，他要求我："请你给我画一只羊！"'
        },
        { 
          type: 'hard',
          image: 'https://image.pollinations.ai/prompt/The%20Little%20Prince%20back%20cover%20illustration',
          text: '小王子的故事告诉我们，真正的爱需要责任和理解。'
        }
      ];
      
      // Create HTML pages
      const htmlPages = pagesData.map((page, index) => {
        const pageElement = document.createElement('div');
        pageElement.className = 'page';
        
        if (page.type === 'hard' || (index === 0 || index === pagesData.length - 1)) {
          pageElement.setAttribute('data-density', 'hard');
        }
        
        const pageContent = document.createElement('div');
        pageContent.className = 'page-content';
        
        if (page.image) {
          const imageElement = document.createElement('div');
          imageElement.className = 'page-image';
          imageElement.style.backgroundImage = `url(${page.image})`;
          pageContent.appendChild(imageElement);
        }
        
        if (page.text) {
          const textElement = document.createElement('div');
          textElement.className = 'page-text';
          textElement.textContent = page.text;
          pageContent.appendChild(textElement);
        }
        
        // Add page number
        const pageNumber = document.createElement('div');
        pageNumber.className = 'page-number';
        pageNumber.textContent = (index + 1).toString();
        pageContent.appendChild(pageNumber);
        
        pageElement.appendChild(pageContent);
        return pageElement;
      });
      
      // Load pages
      pageFlip.loadFromHTML(htmlPages);
      
      // Update page numbers
      const updatePageNumbers = () => {
        document.getElementById('page-number').textContent = pageFlip.getCurrentPageIndex() + 1;
        document.getElementById('total-pages').textContent = pageFlip.getPageCount();
      };
      
      // Add event listeners
      pageFlip.on('flip', (e) => {
        updatePageNumbers();
      });
      
      document.getElementById('prev').addEventListener('click', () => {
        pageFlip.flipPrev();
      });
      
      document.getElementById('next').addEventListener('click', () => {
        pageFlip.flipNext();
      });
      
      // Initialize page numbers
      updatePageNumbers();
    });
  </script>
</body>
</html>
