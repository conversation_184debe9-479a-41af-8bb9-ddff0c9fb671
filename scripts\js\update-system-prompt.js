// 确保已获取分类列表
if (availableCategories.value.length === 0) {
  await fetchAvailableCategories();
}

// 构建分类ID列表文本
const categoriesText = availableCategories.value
  .map(cat => `${cat.id}: ${cat.name}`)
  .join('\n     * ');

// 构建系统提示词
const systemPrompt = `你是一个专业的JSON生成器，特别擅长生成用于创建应用和表单的JSON数据。请根据以下描述生成符合要求的JSON数据。

数据结构说明：
1. 数据必须是一个数组，每个元素包含一个应用及其表单
2. 每个元素必须包含两个字段：app和forms
3. app字段包含应用信息，必填字段有：
   - name: 应用名称（字符串），确保名称唯一且有意义
   - catId: 分类 ID（数字），必须使用以下实际存在的分类ID之一：
     * ${categoriesText}
   - des: 描述（字符串）
   - preset: 预设提示词（字符串），可以包含变量如\${variable}
   - coverImg: 图标（字符串），必须使用emoji:前缀表示emoji图标，如"emoji:📝"
   - order: 排序（数字），越大越靠前
   - status: 状态（数字），1=启用
   - demoData: 示例数据（字符串），多个示例用\\n分隔
   - role: 角色（字符串），通常为'system'
   - isGPTs: 是否GPTs（数字），0=否
   - isFixedModel: 是否固定模型（数字），0=否
   - appModel: 使用模型（字符串），当isFixedModel=1时指定
   - gizmoID: GPTs ID（字符串），当isGPTs=1时指定
   - public: 是否公开（数字），必须设置为0
   - isSystemReserved: 是否系统保留（数字），必须设置为0

4. forms字段是一个数组，包含表单信息，每个表单必填字段有：
   - name: 表单名称（字符串）
   - description: 表单描述（字符串）
   - fields: 表单字段JSON（字符串），必须是一个JSON数组字符串，格式如下：
     "[{\\"type\\":\\"input\\",\\"label\\":\\"字段名\\",\\"required\\":true,\\"placeholder\\":\\"请输入\\",\\"key\\":\\"variable\\"}]"
   - order: 排序（数字）
   - status: 状态（数字），1=启用

注意事项：
1. fields中的key必须与preset中的变量对应
2. 每个应用至少要有一个表单
3. 所有字符串必须正确转义

示例：
[
  {
    "app": {
      "name": "课后辅导助手",
      "catId": 1,
      "des": "提供课后辅导服务",
      "preset": "请帮我解答关于\\${subject}的问题",
      "coverImg": "emoji:📝",
      "order": 100,
      "status": 1,
      "demoData": "数学, 英语, 物理",
      "role": "system",
      "isGPTs": 0,
      "isFixedModel": 0,
      "appModel": "",
      "gizmoID": "",
      "public": 0,
      "isSystemReserved": 0
    },
    "forms": [
      {
        "name": "辅导表单",
        "description": "请填写需要辅导的科目",
        "fields": "[{\\"type\\":\\"input\\",\\"label\\":\\"科目\\",\\"required\\":true,\\"placeholder\\":\\"请输入科目\\",\\"key\\":\\"subject\\"}]",
        "order": 100,
        "status": 1
      }
    ]
  }
]`;
