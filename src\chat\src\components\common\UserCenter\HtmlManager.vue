<template>
  <div class="html-manager">
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <NInput
          v-model:value="keyword"
          placeholder="搜索HTML内容"
          class="w-64"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <NButton text @click="handleSearch">
              <SvgIcon icon="ri:search-line" class="text-lg" />
            </NButton>
          </template>
        </NInput>
      </div>
      <div class="flex space-x-2">
        <NButton type="primary" @click="refreshList">
          <template #icon>
            <SvgIcon icon="ri:refresh-line" class="mr-1" />
          </template>
          刷新
        </NButton>
        <NButton @click="createTestShare">
          <template #icon>
            <SvgIcon icon="ri:add-line" class="mr-1" />
          </template>
          创建测试分享
        </NButton>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center items-center py-10">
      <NSpin size="large" />
    </div>
    <div v-else-if="htmlList.length === 0" class="empty-state">
      <div class="flex flex-col items-center justify-center py-10 text-gray-500">
        <SvgIcon icon="ri:file-code-line" class="text-5xl mb-2" />
        <p>暂无HTML分享内容</p>
        <p class="text-sm mt-2">您可以在聊天中创建HTML代码并分享</p>
      </div>
    </div>
    <div v-else>
      <NCard v-for="item in htmlList" :key="item.id" class="mb-4">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <div class="flex items-center">
              <span class="text-lg font-medium">分享代码: {{ item.shareCode }}</span>
              <NTag
                class="ml-2"
                :type="item.isExpired ? 'error' : 'success'"
                size="small"
              >
                {{ item.isExpired ? '已过期' : '有效' }}
              </NTag>
            </div>
            <div class="text-gray-500 text-sm mt-1">
              创建时间: {{ formatDate(item.createdAt) }}
            </div>
            <div class="text-gray-500 text-sm">
              过期时间: {{ formatDate(item.expiresAt) }}
            </div>
          </div>
          <div class="flex space-x-2">
            <NButton size="small" @click="handlePreview(item)">
              <template #icon>
                <SvgIcon icon="ri:eye-line" class="mr-1" />
              </template>
              预览
            </NButton>
            <NButton size="small" @click="handleEdit(item)">
              <template #icon>
                <SvgIcon icon="ri:edit-line" class="mr-1" />
              </template>
              编辑
            </NButton>
            <NButton size="small" type="error" @click="handleDelete(item)">
              <template #icon>
                <SvgIcon icon="ri:delete-bin-line" class="mr-1" />
              </template>
              删除
            </NButton>
            <NButton size="small" type="primary" @click="handleCopyLink(item)">
              <template #icon>
                <SvgIcon icon="ri:share-line" class="mr-1" />
              </template>
              复制链接
            </NButton>
          </div>
        </div>
      </NCard>

      <div class="flex justify-center mt-4">
        <NPagination
          v-model:page="page"
          v-model:page-size="pageSize"
          :item-count="total"
          :page-sizes="[10, 20, 30, 40]"
          show-size-picker
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 预览对话框 -->
    <NModal
      v-model:show="previewModalVisible"
      preset="card"
      title="HTML预览"
      :style="{ width: '80%', maxWidth: '1000px' }"
      :bordered="false"
    >
      <div class="preview-container">
        <iframe
          v-if="previewContent"
          ref="previewFrame"
          class="preview-frame"
          :srcdoc="previewContent"
        ></iframe>
        <div v-else class="flex justify-center items-center h-64">
          <NSpin size="large" />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end">
          <NButton @click="previewModalVisible = false">关闭</NButton>
        </div>
      </template>
    </NModal>

    <!-- 编辑对话框 -->
    <NModal
      v-model:show="editModalVisible"
      preset="card"
      title="编辑HTML"
      :style="{ width: '90%', maxWidth: '1200px' }"
      :bordered="false"
    >
      <div class="edit-container">
        <div class="flex h-[500px]">
          <div class="w-1/2 pr-2">
            <div class="mb-2 font-medium">HTML代码</div>
            <CodeMirrorEditor
              v-if="editContent"
              v-model:value="editContent"
              :language="'html'"
              :height="450
              "
            />
            <div v-else class="flex justify-center items-center h-full">
              <NSpin size="large" />
            </div>
          </div>
          <div class="w-1/2 pl-2">
            <div class="mb-2 font-medium">预览</div>
            <iframe
              ref="editPreviewFrame"
              class="preview-frame"
              :srcdoc="editContent || ''"
              style="height: 450px"
            ></iframe>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" :loading="saving" @click="handleSave">保存</NButton>
        </div>
      </template>
    </NModal>

    <!-- 分享对话框 -->
    <ShareDialog
      v-if="showShareDialog"
      :share-url="shareUrl"
      :on-close="() => showShareDialog = false"
      @close="showShareDialog = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import {
  NButton,
  NCard,
  NInput,
  NModal,
  NPagination,
  NSpin,
  NTag,
  useMessage
} from 'naive-ui';
import { SvgIcon } from '@/components/common';
import {
  fetchGetUserShares,
  fetchDeleteShare,
  fetchGetShareContent,
  fetchCreateHtmlShare
} from '@/api';
import type { ResData } from '@/api/types';
import CodeMirrorEditor from '@/components/CodeMirrorEditor.vue';
import ShareDialog from '@/components/ShareDialog.vue';

const message = useMessage();
const loading = ref(false);
const htmlList = ref<any[]>([]);
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const keyword = ref('');

// 预览相关
const previewModalVisible = ref(false);
const previewContent = ref('');
const previewFrame = ref<HTMLIFrameElement | null>(null);

// 编辑相关
const editModalVisible = ref(false);
const editContent = ref('');
const editPreviewFrame = ref<HTMLIFrameElement | null>(null);
const currentEditItem = ref<any>(null);
const saving = ref(false);

// 分享相关
const showShareDialog = ref(false);
const shareUrl = ref('');

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 获取HTML分享列表
const fetchHtmlList = async () => {
  loading.value = true;
  try {
    console.log('正在获取HTML分享列表，参数:', {
      page: page.value,
      size: pageSize.value,
      keyword: keyword.value
    });

    const res: ResData = await fetchGetUserShares({
      page: page.value,
      size: pageSize.value,
      keyword: keyword.value
    });

    console.log('获取HTML分享列表响应:', res);

    // 处理嵌套的数据结构
    if (res.success) {
      // 尝试从不同的嵌套层级获取数据
      let rows = null;
      let count = 0;

      if (res.data && res.data.data && Array.isArray(res.data.data.rows)) {
        // 格式: res.data.data.rows
        console.log('使用格式: res.data.data.rows');
        rows = res.data.data.rows;
        count = res.data.data.count || 0;
      } else if (res.data && Array.isArray(res.data.rows)) {
        // 格式: res.data.rows
        console.log('使用格式: res.data.rows');
        rows = res.data.rows;
        count = res.data.count || 0;
      } else if (res.data && res.data.data && res.data.data.rows) {
        // 格式: res.data.data.rows (非数组)
        console.log('使用格式: res.data.data.rows (非数组)');
        rows = res.data.data.rows;
        count = res.data.data.count || 0;
      }

      if (rows) {
        console.log('获取到的HTML分享列表:', rows);
        htmlList.value = rows;
        total.value = count;
      } else {
        console.warn('响应成功但无法找到数据:', res);
        htmlList.value = [];
        total.value = 0;
        message.warning('未找到HTML分享记录');
      }
    } else {
      console.warn('响应失败:', res);
      htmlList.value = [];
      total.value = 0;
      message.error(res.message || '获取HTML分享列表失败');
    }
  } catch (error) {
    console.error('获取HTML分享列表失败:', error);
    message.error('获取HTML分享列表失败');
    htmlList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 刷新列表
const refreshList = () => {
  fetchHtmlList();
};

// 搜索
const handleSearch = () => {
  page.value = 1;
  fetchHtmlList();
};

// 页码变化
const handlePageChange = (newPage: number) => {
  page.value = newPage;
  fetchHtmlList();
};

// 每页数量变化
const handlePageSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  page.value = 1;
  fetchHtmlList();
};

// 预览
const handlePreview = async (item: any) => {
  try {
    console.log('获取分享内容, ID:', item.id);
    const res: ResData = await fetchGetShareContent(item.id);
    console.log('获取分享内容响应:', res);

    // 处理嵌套的数据结构
    let htmlContent = null;

    if (res.success) {
      if (res.data && res.data.htmlContent) {
        // 格式: res.data.htmlContent
        console.log('使用格式: res.data.htmlContent');
        htmlContent = res.data.htmlContent;
      } else if (res.data && res.data.data && res.data.data.htmlContent) {
        // 格式: res.data.data.htmlContent
        console.log('使用格式: res.data.data.htmlContent');
        htmlContent = res.data.data.htmlContent;
      }

      if (htmlContent) {
        console.log('获取到的HTML内容长度:', htmlContent.length);
        previewContent.value = htmlContent;
        previewModalVisible.value = true;
      } else {
        console.warn('响应成功但无法找到HTML内容:', res);
        message.error('无法找到HTML内容');
      }
    } else {
      message.error(res.message || '获取HTML内容失败');
    }
  } catch (error) {
    console.error('获取HTML内容失败:', error);
    message.error('获取HTML内容失败');
  }
};

// 编辑
const handleEdit = async (item: any) => {
  currentEditItem.value = item;
  try {
    console.log('编辑分享内容, ID:', item.id);
    const res: ResData = await fetchGetShareContent(item.id);
    console.log('编辑分享内容响应:', res);

    // 处理嵌套的数据结构
    let htmlContent = null;

    if (res.success) {
      if (res.data && res.data.htmlContent) {
        // 格式: res.data.htmlContent
        console.log('使用格式: res.data.htmlContent');
        htmlContent = res.data.htmlContent;
      } else if (res.data && res.data.data && res.data.data.htmlContent) {
        // 格式: res.data.data.htmlContent
        console.log('使用格式: res.data.data.htmlContent');
        htmlContent = res.data.data.htmlContent;
      }

      if (htmlContent) {
        console.log('获取到的HTML内容长度:', htmlContent.length);
        editContent.value = htmlContent;
        editModalVisible.value = true;
      } else {
        console.warn('响应成功但无法找到HTML内容:', res);
        message.error('无法找到HTML内容');
      }
    } else {
      message.error(res.message || '获取HTML内容失败');
    }
  } catch (error) {
    console.error('获取HTML内容失败:', error);
    message.error('获取HTML内容失败');
  }
};

// 保存编辑
const handleSave = async () => {
  if (!editContent.value) {
    message.warning('HTML内容不能为空');
    return;
  }

  saving.value = true;
  try {
    console.log('保存编辑内容');
    // 创建新的分享
    const res: ResData = await fetchCreateHtmlShare({
      htmlContent: editContent.value
    });

    console.log('保存编辑响应:', res);

    if (res.success) {
      // 处理嵌套的数据结构
      let shareCodeValue = null;

      if (res.data && res.data.shareCode) {
        // 格式: res.data.shareCode
        console.log('使用格式: res.data.shareCode');
        shareCodeValue = res.data.shareCode;
      } else if (res.data && res.data.data && res.data.data.shareCode) {
        // 格式: res.data.data.shareCode
        console.log('使用格式: res.data.data.shareCode');
        shareCodeValue = res.data.data.shareCode;
      } else if (res.data && typeof res.data === 'string') {
        // 格式: res.data (字符串)
        console.log('使用格式: res.data (字符串)');
        shareCodeValue = res.data;
      }

      if (shareCodeValue) {
        console.log('获取到的分享代码:', shareCodeValue);
        message.success('保存成功');
        editModalVisible.value = false;

        // 显示分享对话框
        const isHashMode = window.location.href.includes('#/');
        shareUrl.value = isHashMode
          ? `${window.location.origin}/#/share/${shareCodeValue}`
          : `${window.location.origin}/share/${shareCodeValue}`;

        showShareDialog.value = true;

        // 刷新列表
        fetchHtmlList();
      } else {
        console.warn('响应成功但无法找到分享代码:', res);
        message.error('保存成功但无法获取分享链接');
        editModalVisible.value = false;
        fetchHtmlList();
      }
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 删除
const handleDelete = async (item: any) => {
  try {
    const res: ResData = await fetchDeleteShare({ shareId: item.id });

    if (res.success) {
      message.success('删除成功');
      fetchHtmlList();
    } else {
      message.error(res.message || '删除失败');
    }
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 复制链接
const handleCopyLink = async (item: any) => {
  try {
    await navigator.clipboard.writeText(item.shareUrl);
    message.success('链接已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动复制');
  }
};

// 创建测试分享
const createTestShare = async () => {
  try {
    message.info('正在创建测试HTML分享...');
    const testHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试HTML分享</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      color: #333;
    }
    p {
      color: #666;
      margin-bottom: 1.5rem;
    }
    .timestamp {
      font-size: 0.8rem;
      color: #999;
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>测试HTML分享</h1>
    <p>这是一个测试HTML分享，用于验证用户ID是否正确保存。</p>
    <p>如果您看到这个页面，说明分享功能正常工作。</p>
    <div class="timestamp">创建时间: ${new Date().toLocaleString()}</div>
  </div>
</body>
</html>
    `;

    console.log('发送测试HTML分享请求');
    const res: ResData = await fetchCreateHtmlShare({
      htmlContent: testHtml
    });

    console.log('测试HTML分享响应:', res);

    if (res.success) {
      // 处理嵌套的数据结构
      let shareCodeValue = null;

      if (res.data && res.data.shareCode) {
        // 格式: res.data.shareCode
        console.log('使用格式: res.data.shareCode');
        shareCodeValue = res.data.shareCode;
      } else if (res.data && res.data.data && res.data.data.shareCode) {
        // 格式: res.data.data.shareCode
        console.log('使用格式: res.data.data.shareCode');
        shareCodeValue = res.data.data.shareCode;
      } else if (res.data && typeof res.data === 'string') {
        // 格式: res.data (字符串)
        console.log('使用格式: res.data (字符串)');
        shareCodeValue = res.data;
      }

      if (shareCodeValue) {
        console.log('获取到的分享代码:', shareCodeValue);
        message.success('测试分享创建成功');

        // 显示分享对话框
        const isHashMode = window.location.href.includes('#/');
        shareUrl.value = isHashMode
          ? `${window.location.origin}/#/share/${shareCodeValue}`
          : `${window.location.origin}/share/${shareCodeValue}`;

        showShareDialog.value = true;

        // 刷新列表
        fetchHtmlList();
      } else {
        console.warn('响应成功但无法找到分享代码:', res);
        message.error('创建测试分享失败');
      }
    } else {
      message.error(res.message || '创建测试分享失败');
    }
  } catch (error) {
    console.error('创建测试分享失败:', error);
    message.error('创建测试分享失败');
  }
};

// 监听编辑内容变化，更新预览
watch(editContent, () => {
  if (editPreviewFrame.value) {
    const doc = editPreviewFrame.value.contentDocument;
    if (doc) {
      doc.open();
      doc.write(editContent.value);
      doc.close();
    }
  }
});

onMounted(() => {
  fetchHtmlList();
});
</script>

<style scoped>
.html-manager {
  padding: 16px;
}

.preview-frame {
  width: 100%;
  height: 500px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.empty-state {
  border: 1px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}
</style>
