<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { FormField } from '@/services/FormService';
import formFieldUtils from '@/utils/FormFieldUtils';
import draggable from 'vuedraggable';
import FormTemplateDialog from '@/components/FormTemplateDialog/index.vue';
import { FormTemplate } from '@/utils/formTemplates';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  showJsonEditor: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'update:showJsonEditor']);

// 表单字段
const formFields = ref<FormField[]>([]);

// 当前编辑的字段索引
const currentEditIndex = ref(-1);

// 当前编辑的字段
const currentEditField = ref<FormField | null>(null);

// 字段编辑对话框可见性
const fieldDialogVisible = ref(false);

// 视图模式
const viewMode = ref('table');

// 字段类型列表
const fieldTypes = formFieldUtils.getFieldTypes();

// 模板对话框可见性
const templateDialogVisible = ref(false);

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  try {
    if (typeof newVal === 'string') {
      formFields.value = JSON.parse(newVal);
    } else {
      formFields.value = [...newVal];
    }
  } catch (error) {
    console.error('解析表单字段失败', error);
    formFields.value = [];
  }
}, { immediate: true, deep: true });

// 监听formFields变化，更新modelValue
watch(formFields, (newVal) => {
  emit('update:modelValue', JSON.stringify(newVal));
}, { deep: true });

// 添加字段
function addField() {
  currentEditIndex.value = -1;
  currentEditField.value = {
    type: 'input',
    name: '',
    label: '',
    placeholder: '',
    required: false
  };
  fieldDialogVisible.value = true;
}

// 编辑字段
function editField(index: number) {
  currentEditIndex.value = index;
  currentEditField.value = JSON.parse(JSON.stringify(formFields.value[index]));
  fieldDialogVisible.value = true;
}

// 删除字段
function deleteField(index: number) {
  ElMessageBox.confirm('确认删除此字段吗？', '删除确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formFields.value.splice(index, 1);
    ElMessage.success('删除字段成功');
  }).catch(() => {
    // 用户取消删除
  });
}

// 保存字段
function saveField() {
  if (!currentEditField.value) return;

  // 验证字段名称
  if (!currentEditField.value.name) {
    ElMessage.error('字段名称不能为空');
    return;
  }

  // 验证字段名称格式
  const nameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
  if (!nameRegex.test(currentEditField.value.name)) {
    ElMessage.error('字段名称只能包含字母、数字和下划线，且必须以字母开头');
    return;
  }

  // 验证字段标签
  if (!currentEditField.value.label) {
    ElMessage.error('字段标签不能为空');
    return;
  }

  // 验证选项类型字段的选项
  if (['select', 'radio', 'checkbox'].includes(currentEditField.value.type) &&
      (!currentEditField.value.options || !Array.isArray(currentEditField.value.options) || currentEditField.value.options.length === 0)) {
    ElMessage.error('选项不能为空');
    return;
  }

  // 验证字段名称是否重复
  const nameExists = formFields.value.some((field, index) => {
    return field.name === currentEditField.value?.name && index !== currentEditIndex.value;
  });
  if (nameExists) {
    ElMessage.error('字段名称已存在');
    return;
  }

  if (currentEditIndex.value === -1) {
    // 添加新字段
    formFields.value.push(currentEditField.value);
    ElMessage.success('添加字段成功');
  } else {
    // 更新现有字段
    formFields.value[currentEditIndex.value] = currentEditField.value;
    ElMessage.success('更新字段成功');
  }

  fieldDialogVisible.value = false;
}

// 取消编辑
function cancelEdit() {
  fieldDialogVisible.value = false;
}

// 字段类型变化处理
function handleFieldTypeChange() {
  if (!currentEditField.value) return;

  // 根据字段类型设置默认值
  const type = currentEditField.value.type;
  const name = currentEditField.value.name;
  const label = currentEditField.value.label;

  // 创建新字段
  const newField = formFieldUtils.createFieldByType(type, name, label);

  // 保留原字段的name和label
  newField.name = name;
  newField.label = label;

  // 更新当前编辑的字段
  currentEditField.value = newField;
}

// 添加选项
function addOption() {
  if (!currentEditField.value || !currentEditField.value.options) {
    if (currentEditField.value) {
      currentEditField.value = {
        ...currentEditField.value,
        type: currentEditField.value.type || 'input',
        name: currentEditField.value.name || '',
        label: currentEditField.value.label || '',
        options: []
      };
    }
  }

  if (currentEditField.value && currentEditField.value.options) {
    currentEditField.value.options.push({
      label: `选项${currentEditField.value.options.length + 1}`,
      value: `option${currentEditField.value.options.length + 1}`
    });
  }
}

// 删除选项
function deleteOption(index: number) {
  if (!currentEditField.value || !currentEditField.value.options) return;
  currentEditField.value.options.splice(index, 1);
}

// 上移字段
function moveUp(index: number) {
  if (index === 0) return;
  const temp = formFields.value[index];
  formFields.value[index] = formFields.value[index - 1];
  formFields.value[index - 1] = temp;
}

// 下移字段
function moveDown(index: number) {
  if (index === formFields.value.length - 1) return;
  const temp = formFields.value[index];
  formFields.value[index] = formFields.value[index + 1];
  formFields.value[index + 1] = temp;
}

// 复制字段
function copyField(index: number) {
  const field = JSON.parse(JSON.stringify(formFields.value[index]));
  field.name = `${field.name}_copy`;
  field.label = `${field.label} (复制)`;
  formFields.value.splice(index + 1, 0, field);
  ElMessage.success('复制字段成功');
}

// 切换到JSON编辑器
function switchToJsonEditor() {
  emit('update:showJsonEditor', true);
}

// 获取字段类型标签
function getFieldTypeLabel(type: string): string {
  return formFieldUtils.getFieldTypeLabel(type);
}

// 获取字段类型标签类型
function getFieldTypeTagType(type: string): string {
  return formFieldUtils.getFieldTypeTagType(type);
}

// 拖拽结束处理
function handleDragEnd() {
  ElMessage.success('字段排序已更新');
}

// 打开模板对话框
function openTemplateDialog() {
  templateDialogVisible.value = true;
}

// 处理模板选择
function handleTemplateSelect(template: FormTemplate) {
  // 确认是否覆盖现有字段
  if (formFields.value.length > 0) {
    ElMessageBox.confirm('使用模板将覆盖当前所有字段，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 应用模板
      applyTemplate(template);
    }).catch(() => {
      // 用户取消
    });
  } else {
    // 直接应用模板
    applyTemplate(template);
  }
}

// 应用模板
function applyTemplate(template: FormTemplate) {
  formFields.value = JSON.parse(JSON.stringify(template.fields));
  ElMessage.success(`已应用模板: ${template.name}`);
}
</script>

<template>
  <div class="form-field-editor">
    <div class="form-field-editor-header">
      <h3>表单字段编辑器</h3>
      <div class="form-field-editor-actions">
        <el-button type="primary" size="small" @click="addField">
          <el-icon><Plus /></el-icon> 添加字段
        </el-button>
        <el-button type="success" size="small" @click="openTemplateDialog">
          <el-icon><Files /></el-icon> 使用模板
        </el-button>
        <el-button type="info" size="small" @click="switchToJsonEditor">
          <el-icon><Document /></el-icon> 切换到JSON编辑器
        </el-button>
      </div>
    </div>

    <div v-if="formFields.length === 0" class="form-field-editor-empty">
      <el-empty description="暂无表单字段，请点击'添加字段'按钮添加" />
    </div>

    <div v-else class="form-field-editor-list">
      <!-- 拖拽模式切换 -->
      <div class="form-field-editor-view-toggle">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button value="table">表格视图</el-radio-button>
          <el-radio-button value="card">卡片视图</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 表格视图 -->
      <el-table v-if="viewMode === 'table'" :data="formFields" border style="width: 100%">
        <el-table-column label="序号" width="60" type="index" />
        <el-table-column prop="name" label="字段名称" width="120" />
        <el-table-column prop="label" label="字段标签" width="120" />
        <el-table-column prop="type" label="字段类型" width="120">
          <template #default="scope">
            <el-tag :type="getFieldTypeTagType(scope.row.type)">
              {{ getFieldTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="required" label="是否必填" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.required ? 'danger' : 'info'">
              {{ scope.row.required ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="placeholder" label="占位文本" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="editField(scope.$index)">
              编辑
            </el-button>
            <el-button link type="success" size="small" @click="copyField(scope.$index)">
              复制
            </el-button>
            <el-button link type="danger" size="small" @click="deleteField(scope.$index)">
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="排序" width="80">
          <template #default="scope">
            <div class="form-field-editor-sort">
              <el-button link :disabled="scope.$index === 0" @click="moveUp(scope.$index)">
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <el-button link :disabled="scope.$index === formFields.length - 1" @click="moveDown(scope.$index)">
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 卡片视图（可拖拽） -->
      <div v-else class="form-field-editor-cards">
        <draggable
          v-model="formFields"
          item-key="name"
          handle=".drag-handle"
          ghost-class="ghost"
          animation="300"
          @end="handleDragEnd"
        >
          <template #item="{ element, index }">
            <div class="form-field-card">
              <div class="form-field-card-header">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <div class="form-field-card-title">
                  <span class="field-label">{{ element.label }}</span>
                  <span v-if="element.required" class="required-mark">*</span>
                </div>
                <div class="form-field-card-type">
                  <el-tag size="small" :type="getFieldTypeTagType(element.type)">
                    {{ getFieldTypeLabel(element.type) }}
                  </el-tag>
                </div>
              </div>

              <div class="form-field-card-content">
                <div class="form-field-card-info">
                  <div class="info-item">
                    <span class="info-label">字段名:</span>
                    <span class="info-value">{{ element.name }}</span>
                  </div>

                  <div v-if="element.placeholder" class="info-item">
                    <span class="info-label">占位文本:</span>
                    <span class="info-value">{{ element.placeholder }}</span>
                  </div>

                  <!-- 特定字段类型的额外属性 -->
                  <div v-if="['select', 'radio', 'checkbox'].includes(element.type) && element.options" class="info-item">
                    <span class="info-label">选项:</span>
                    <div class="options-list">
                      <el-tag
                        v-for="(option, optIndex) in element.options"
                        :key="optIndex"
                        size="small"
                        class="option-tag"
                      >
                        {{ option.label || option }}
                      </el-tag>
                    </div>
                  </div>

                  <div v-if="['number', 'slider'].includes(element.type)" class="info-item">
                    <span class="info-label">范围:</span>
                    <span class="info-value">{{ element.min || 0 }} - {{ element.max || 100 }}, 步长: {{ element.step || 1 }}</span>
                  </div>
                </div>

                <div class="form-field-card-actions">
                  <el-button type="primary" size="small" @click="editField(index)">
                    <el-icon><Edit /></el-icon> 编辑
                  </el-button>
                  <el-button type="success" size="small" @click="copyField(index)">
                    <el-icon><CopyDocument /></el-icon> 复制
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteField(index)">
                    <el-icon><Delete /></el-icon> 删除
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>

    <!-- 字段编辑对话框 -->
    <el-dialog
      v-model="fieldDialogVisible"
      :title="currentEditIndex === -1 ? '添加字段' : '编辑字段'"
      width="600px"
      destroy-on-close
    >
      <el-form v-if="currentEditField" label-width="100px">
        <el-form-item label="字段类型">
          <el-select v-model="currentEditField.type" @change="handleFieldTypeChange">
            <el-option
              v-for="item in fieldTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="字段名称">
          <el-input v-model="currentEditField.name" placeholder="请输入字段名称，只能包含字母、数字和下划线，且必须以字母开头" />
        </el-form-item>

        <el-form-item label="字段标签">
          <el-input v-model="currentEditField.label" placeholder="请输入字段标签，将显示在表单中" />
        </el-form-item>

        <el-form-item label="是否必填">
          <el-switch v-model="currentEditField.required" />
        </el-form-item>

        <el-form-item v-if="['input', 'textarea', 'select', 'date', 'time', 'datetime', 'number'].includes(currentEditField.type)" label="占位文本">
          <el-input v-model="currentEditField.placeholder" placeholder="请输入占位文本" />
        </el-form-item>

        <el-form-item v-if="['input', 'textarea'].includes(currentEditField.type)" label="默认值">
          <el-input v-model="currentEditField.defaultValue" placeholder="请输入默认值" />
        </el-form-item>

        <el-form-item v-if="currentEditField.type === 'textarea'" label="行数">
          <el-input-number v-model="currentEditField.rows" :min="2" :max="10" />
        </el-form-item>

        <el-form-item v-if="['select', 'radio', 'checkbox'].includes(currentEditField.type)" label="选项">
          <div v-for="(option, index) in currentEditField.options" :key="index" class="option-item">
            <el-input v-model="option.label" placeholder="选项标签" />
            <el-input v-model="option.value" placeholder="选项值" />
            <el-button type="danger" circle @click="deleteOption(index)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <el-button type="primary" @click="addOption">添加选项</el-button>
        </el-form-item>

        <el-form-item v-if="currentEditField.type === 'switch'" label="开启文本">
          <el-input v-model="currentEditField.activeText" placeholder="请输入开启时显示的文本" />
        </el-form-item>

        <el-form-item v-if="currentEditField.type === 'switch'" label="关闭文本">
          <el-input v-model="currentEditField.inactiveText" placeholder="请输入关闭时显示的文本" />
        </el-form-item>

        <el-form-item v-if="['slider', 'number'].includes(currentEditField.type)" label="最小值">
          <el-input-number v-model="currentEditField.min" />
        </el-form-item>

        <el-form-item v-if="['slider', 'number'].includes(currentEditField.type)" label="最大值">
          <el-input-number v-model="currentEditField.max" />
        </el-form-item>

        <el-form-item v-if="['slider', 'number'].includes(currentEditField.type)" label="步长">
          <el-input-number v-model="currentEditField.step" :min="0.01" :step="0.01" />
        </el-form-item>

        <el-form-item v-if="['date', 'time', 'datetime'].includes(currentEditField.type)" label="格式">
          <el-input v-model="currentEditField.format" placeholder="请输入日期/时间格式" />
        </el-form-item>

        <el-form-item v-if="currentEditField.type === 'rate'" label="最大值">
          <el-input-number v-model="currentEditField.max" :min="1" :max="10" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveField">保存</el-button>
      </template>
    </el-dialog>

    <!-- 表单模板对话框 -->
    <FormTemplateDialog
      v-model:visible="templateDialogVisible"
      @select="handleTemplateSelect"
    />
  </div>
</template>

<style scoped>
.form-field-editor {
  width: 100%;
}

.form-field-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.form-field-editor-header h3 {
  margin: 0;
}

.form-field-editor-actions {
  display: flex;
  gap: 8px;
}

.form-field-editor-empty {
  padding: 40px 0;
}

.form-field-editor-list {
  margin-bottom: 16px;
}

.form-field-editor-view-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.form-field-editor-sort {
  display: flex;
  flex-direction: column;
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

/* 卡片视图样式 */
.form-field-editor-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.form-field-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-field-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.form-field-card-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.drag-handle {
  cursor: move;
  padding: 4px;
  margin-right: 8px;
  color: #909399;
}

.drag-handle:hover {
  color: #409eff;
}

.form-field-card-title {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

.form-field-card-content {
  padding: 16px;
}

.form-field-card-info {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.info-label {
  font-weight: 600;
  color: #606266;
  margin-right: 4px;
}

.info-value {
  color: #303133;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.option-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.form-field-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

/* 拖拽相关样式 */
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
