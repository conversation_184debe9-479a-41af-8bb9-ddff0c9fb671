<template>
  <div class="form-group animated-form-group">
    <div class="form-label">
      <span class="form-icon">💡</span>
      有什么其他想法吗？
    </div>
    <NInput
      v-model:value="notes"
      type="textarea"
      placeholder="在这里写下你的其他想法和创意..."
      :autosize="{ minRows: 3, maxRows: 6 }"
      class="child-input"
    />
  </div>
</template>

<script setup lang="ts">
import { NInput } from 'naive-ui';

// 使用defineModel实现双向绑定
const notes = defineModel('notes');
</script>

<style scoped>
.form-group {
  margin-bottom: 2.5rem;
  width: 100%;
  position: relative;
}

.form-group.animated-form-group {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-label {
  font-weight: 700;
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.dark .form-label {
  color: #60a5fa;
}

.form-icon {
  font-size: 1.5rem;
}

.child-input {
  font-size: 1.1rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.child-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
}

.dark .child-input {
  border-color: #334155;
  background-color: #1e293b;
  color: #e2e8f0;
}

.dark .child-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}
</style>
