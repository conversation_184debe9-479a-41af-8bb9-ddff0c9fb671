<script setup lang="ts">
import { ref, computed } from 'vue';
import { NLayout, NLayoutSider, useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAuthStore } from '@/store';
import UserCenterSidebar from './components/UserCenterSidebar.vue';
import PersonalInfo from './components/PersonalInfo.vue';
import PointsManagement from './components/PointsManagement.vue';
import Notifications from './components/Notifications.vue';

// 响应式布局
const { isMobile } = useBasicLayout();
const router = useRouter();
const authStore = useAuthStore();
const ms = useMessage();

// 侧边栏折叠状态
const sidebarCollapsed = ref(false);

// 当前激活的标签页
const activeTab = ref('personal-info');

// 检查登录状态
const isLogin = computed(() => authStore.isLogin);

// 更新侧边栏折叠状态
function updateSidebarCollapsed(collapsed: boolean) {
  sidebarCollapsed.value = collapsed;
}

// 切换标签页
function switchTab(tab: string) {
  activeTab.value = tab;
}

// 检查路由权限
function checkRoute() {
  if (!isLogin.value) {
    ms.error('请先登录');
    router.replace('/');
    authStore.setLoginDialog(true);
  }
}

// 组件挂载时检查登录状态
checkRoute();
</script>

<template>
  <div class="user-center-page">
    <NLayout has-sider class="h-full">
      <!-- 侧边栏 -->
      <NLayoutSider
        :collapsed="sidebarCollapsed"
        :collapsed-width="0"
        :width="280"
        collapse-mode="transform"
        position="static"
        :bordered="false"
        :style="{ display: sidebarCollapsed ? 'none' : 'block' }"
        @update-collapsed="updateSidebarCollapsed"
      >
        <UserCenterSidebar
          :collapsed="sidebarCollapsed"
          :active-tab="activeTab"
          @update-collapsed="updateSidebarCollapsed"
          @switch-tab="switchTab"
        />
      </NLayoutSider>

      <!-- 主内容区域 -->
      <div class="user-center-content">
        <!-- 根据当前激活的标签页显示对应的组件 -->
        <PersonalInfo v-if="activeTab === 'personal-info'" />
        <PointsManagement v-else-if="activeTab === 'points'" />
        <Notifications v-else-if="activeTab === 'notifications'" />
      </div>
    </NLayout>

    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && !sidebarCollapsed"
      class="fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300"
      @click="updateSidebarCollapsed(true)"
    />
  </div>
</template>

<style scoped>
.user-center-page {
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
}

.dark .user-center-page {
  background-color: #1a1a1a;
}

.user-center-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  height: 100%;
}

@media (max-width: 768px) {
  .user-center-content {
    padding: 12px;
  }
}
</style>
