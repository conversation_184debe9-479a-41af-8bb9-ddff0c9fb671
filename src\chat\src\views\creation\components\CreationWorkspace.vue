<template>
  <div class="creation-workspace">
    <!-- 提示词区域 -->
    <div v-if="showPrompt" class="prompt-area">
      <div class="prompt-header">
        <h3 class="prompt-title">提示词</h3>
        <n-button text @click="togglePrompt">
          <template #icon>
            <SvgIcon name="ri:close-line" size="16" />
          </template>
        </n-button>
      </div>
      
      <div class="prompt-content">
        <n-input
          v-model:value="promptText"
          type="textarea"
          placeholder="输入提示词..."
          :autosize="{ minRows: 3, maxRows: 10 }"
        />
        
        <div class="prompt-examples">
          <h4 class="examples-title">示例提示词</h4>
          <div class="examples-list">
            <div 
              v-for="(example, index) in promptExamples" 
              :key="index"
              class="example-item"
              @click="useExample(example)"
            >
              {{ example }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-area" :class="{ 'with-prompt': showPrompt }">
      <component 
        :is="contentComponent" 
        :content="content"
        :loading="loading"
        :streaming="streaming"
        @update:content="updateContent"
      />
    </div>
    
    <!-- 输入区域 -->
    <div class="input-area">
      <n-input
        v-model:value="inputText"
        type="textarea"
        placeholder="输入内容或指令..."
        :autosize="{ minRows: 1, maxRows: 5 }"
        @keydown.enter.ctrl.prevent="handleSend"
      />
      
      <div class="input-actions">
        <n-tooltip placement="top">
          <template #trigger>
            <n-button circle @click="togglePrompt">
              <template #icon>
                <SvgIcon name="ri:lightbulb-line" size="18" />
              </template>
            </n-button>
          </template>
          显示/隐藏提示词
        </n-tooltip>
        
        <n-tooltip placement="top">
          <template #trigger>
            <n-button circle @click="clearInput">
              <template #icon>
                <SvgIcon name="ri:delete-bin-line" size="18" />
              </template>
            </n-button>
          </template>
          清空输入
        </n-tooltip>
        
        <n-button 
          type="primary" 
          :loading="loading" 
          :disabled="!inputText.trim()" 
          @click="handleSend"
        >
          <template #icon>
            <SvgIcon v-if="!loading" name="ri:send-plane-fill" size="18" />
          </template>
          {{ loading ? '生成中...' : '发送' }}
        </n-button>
        
        <n-button 
          v-if="streaming" 
          type="error" 
          @click="handleStop"
        >
          <template #icon>
            <SvgIcon name="ri:stop-fill" size="18" />
          </template>
          停止
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, markRaw } from 'vue';
import { NInput, NButton, NTooltip } from 'naive-ui';
import { SvgIcon } from '@/components/common';

// 导入不同类型的内容组件
import ProgrammingContent from './types/Programming.vue';
import WritingContent from './types/Writing.vue';
import PictureBookContent from './types/PictureBook.vue';
import PresentationContent from './types/Presentation.vue';
import MusicContent from './types/Music.vue';
import FreestyleContent from './types/Freestyle.vue';
import DefaultContent from './types/Default.vue';

const props = defineProps({
  creationType: {
    type: String,
    default: 'chat'
  },
  content: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  streaming: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:content', 'send', 'stop']);

// 状态
const inputText = ref('');
const promptText = ref('');
const showPrompt = ref(false);

// 根据创作类型获取示例提示词
const promptExamples = computed(() => {
  switch (props.creationType) {
    case 'programming':
      return [
        '创建一个简单的待办事项应用，使用HTML、CSS和JavaScript',
        '编写一个React组件，用于显示用户资料卡片',
        '使用Vue.js实现一个图片轮播组件',
        '创建一个响应式导航栏，在移动设备上变为汉堡菜单'
      ];
    case 'writing':
      return [
        '写一篇关于人工智能在医疗领域应用的文章',
        '创作一个科幻短篇故事，主题是时间旅行',
        '撰写一份产品介绍，描述一款智能家居设备',
        '写一篇关于可持续发展的博客文章'
      ];
    case 'picturebook':
      return [
        '创作一个关于勇敢小兔子的儿童绘本故事',
        '设计一本教育性绘本，介绍太阳系的行星',
        '创作一个关于友谊的图文故事',
        '设计一本关于环保的儿童绘本'
      ];
    case 'ppt':
      return [
        '创建一个公司业务介绍的演示文稿',
        '设计一个教育课程的PPT，主题是编程基础',
        '制作一个项目提案的演示文稿',
        '创建一个产品发布会的演示文稿'
      ];
    case 'music':
      return [
        '创作一首轻松愉快的背景音乐',
        '为一个悬疑短片创作配乐',
        '创作一首钢琴曲，表达平静与希望',
        '为一个广告创作30秒的配乐'
      ];
    case 'freestyle':
      return [
        '创作一个融合文字、代码和设计的多媒体作品',
        '设计一个创意概念，结合文字和视觉元素',
        '创作一个交互式故事，包含选择分支',
        '设计一个创新的用户界面概念'
      ];
    default:
      return [
        '请帮我解释量子计算的基本原理',
        '分析当前全球经济趋势',
        '提供一些提高工作效率的方法',
        '讨论人工智能对就业市场的影响'
      ];
  }
});

// 根据创作类型选择内容组件
const contentComponent = computed(() => {
  switch (props.creationType) {
    case 'programming':
      return markRaw(ProgrammingContent);
    case 'writing':
      return markRaw(WritingContent);
    case 'picturebook':
      return markRaw(PictureBookContent);
    case 'ppt':
      return markRaw(PresentationContent);
    case 'music':
      return markRaw(MusicContent);
    case 'freestyle':
      return markRaw(FreestyleContent);
    default:
      return markRaw(DefaultContent);
  }
});

// 方法
function togglePrompt() {
  showPrompt.value = !showPrompt.value;
}

function useExample(example: string) {
  promptText.value = example;
}

function clearInput() {
  inputText.value = '';
}

function updateContent(newContent: string) {
  emit('update:content', newContent);
}

function handleSend() {
  if (props.loading || !inputText.value.trim()) return;
  
  // 构建完整提示
  let fullPrompt = inputText.value;
  if (promptText.value.trim()) {
    fullPrompt = `${promptText.value}\n\n${inputText.value}`;
  }
  
  emit('send', fullPrompt);
  inputText.value = '';
}

function handleStop() {
  emit('stop');
}

// 监听创作类型变化，更新提示词
watch(() => props.creationType, () => {
  // 根据创作类型设置默认提示词
  switch (props.creationType) {
    case 'programming':
      promptText.value = '请创建以下代码：';
      break;
    case 'writing':
      promptText.value = '请创作以下内容：';
      break;
    case 'picturebook':
      promptText.value = '请创作一个绘本故事：';
      break;
    case 'ppt':
      promptText.value = '请创建一个演示文稿：';
      break;
    case 'music':
      promptText.value = '请创作一段音乐：';
      break;
    case 'freestyle':
      promptText.value = '请创作以下内容：';
      break;
    default:
      promptText.value = '';
  }
});
</script>

<style scoped>
.creation-workspace {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dark .creation-workspace {
  background-color: var(--color-gray-800);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.prompt-area {
  background-color: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
  padding: 1rem;
}

.dark .prompt-area {
  background-color: var(--color-gray-850);
  border-bottom: 1px solid var(--color-gray-700);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.prompt-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
}

.prompt-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.prompt-examples {
  background-color: white;
  border-radius: 0.375rem;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-200);
}

.dark .prompt-examples {
  background-color: var(--color-gray-800);
  border: 1px solid var(--color-gray-700);
}

.examples-title {
  font-size: 0.75rem;
  font-weight: 600;
  margin: 0 0 0.5rem;
  color: var(--color-gray-600);
}

.dark .examples-title {
  color: var(--color-gray-400);
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.example-item {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border-radius: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.dark .example-item {
  background-color: var(--color-gray-700);
  color: var(--color-gray-300);
}

.example-item:hover {
  background-color: var(--color-gray-200);
}

.dark .example-item:hover {
  background-color: var(--color-gray-600);
}

.content-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-area.with-prompt {
  height: calc(100% - 200px);
}

.input-area {
  padding: 1rem;
  border-top: 1px solid var(--color-gray-200);
  background-color: white;
}

.dark .input-area {
  border-top: 1px solid var(--color-gray-700);
  background-color: var(--color-gray-800);
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

@media (max-width: 768px) {
  .prompt-area {
    padding: 0.75rem;
  }
  
  .content-area.with-prompt {
    height: calc(100% - 180px);
  }
  
  .input-area {
    padding: 0.75rem;
  }
}
</style>
