<template>
  <div>
    <n-card :bordered="false" class="mt-4 mb-4 proCard">
      <div class="flex items-center justify-between mb-4">
        <div>
          <n-button type="primary" @click="handleRefresh">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button class="ml-2" @click="showConfigModal = true">
            <template #icon>
              <n-icon>
                <SettingsOutline />
              </n-icon>
            </template>
            图像生成配置
          </n-button>
        </div>
        <div class="flex items-center">
          <n-input
            v-model:value="searchParams.keyword"
            placeholder="请输入图像描述"
            clearable
            class="w-200px mr-2"
            @keydown.enter="handleSearch"
          />
          <n-select
            v-model:value="searchParams.imageType"
            placeholder="图像类型"
            clearable
            :options="imageTypeOptions"
            class="w-150px mr-2"
            @update:value="handleSearch"
          />
          <n-select
            v-model:value="searchParams.auditStatus"
            placeholder="审核状态"
            clearable
            :options="auditStatusOptions"
            class="w-150px mr-2"
            @update:value="handleSearch"
          />
          <n-button type="primary" @click="handleSearch">
            <template #icon>
              <n-icon>
                <SearchOutline />
              </n-icon>
            </template>
            搜索
          </n-button>
        </div>
      </div>

      <!-- 图像网格 -->
      <n-spin :show="loading">
        <div v-if="images.length > 0" class="grid grid-cols-4 gap-4">
          <n-card
            v-for="image in images"
            :key="image.id"
            :bordered="false"
            class="image-card"
            :class="{ 'border-green-500': image.auditStatus === 1, 'border-red-500': image.auditStatus === 2 }"
          >
            <div class="relative">
              <img
                :src="image.imageUrl"
                class="w-full h-200px object-cover rounded-t-md"
                @click="handlePreview(image)"
              />
              <div class="absolute top-2 right-2">
                <n-tag :type="getAuditStatusType(image.auditStatus)">
                  {{ getAuditStatusText(image.auditStatus) }}
                </n-tag>
              </div>
              <div class="absolute bottom-2 right-2">
                <n-tag :type="getImageTypeType(image.imageType)">
                  {{ getImageTypeText(image.imageType) }}
                </n-tag>
              </div>
            </div>
            <div class="p-3">
              <div class="text-sm text-gray-500 mb-2 truncate" :title="image.description">
                {{ image.description || '无描述' }}
              </div>
              <div class="flex justify-between items-center">
                <div class="text-xs text-gray-400">
                  {{ image.createdAt }}
                </div>
                <div class="flex space-x-1">
                  <n-button size="tiny" @click="handlePreview(image)">预览</n-button>
                  <n-button
                    size="tiny"
                    type="primary"
                    :disabled="image.auditStatus === 1"
                    @click="handleAudit(image, 1)"
                  >
                    通过
                  </n-button>
                  <n-button
                    size="tiny"
                    type="error"
                    :disabled="image.auditStatus === 2"
                    @click="handleAudit(image, 2)"
                  >
                    拒绝
                  </n-button>
                </div>
              </div>
            </div>
          </n-card>
        </div>
        <div v-else-if="!loading" class="py-10 text-center text-gray-500">
          暂无图像资源
        </div>
      </n-spin>

      <!-- 分页 -->
      <div class="flex justify-center mt-4">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :item-count="pagination.itemCount"
          :page-sizes="pagination.pageSizes"
          show-size-picker
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </n-card>

    <!-- 图像预览 -->
    <n-modal v-model:show="showPreviewModal" preset="card" style="width: 70%">
      <template #header>
        <div class="text-lg font-bold">图像预览</div>
      </template>
      <div v-if="currentImage">
        <div class="flex justify-center mb-4">
          <img :src="currentImage.imageUrl" class="max-w-full max-h-500px" />
        </div>
        <n-descriptions bordered :column="2">
          <n-descriptions-item label="ID">{{ currentImage.id }}</n-descriptions-item>
          <n-descriptions-item label="用户ID">{{ currentImage.userId }}</n-descriptions-item>
          <n-descriptions-item label="图像类型">
            {{ getImageTypeText(currentImage.imageType) }}
          </n-descriptions-item>
          <n-descriptions-item label="审核状态">
            <n-tag :type="getAuditStatusType(currentImage.auditStatus)">
              {{ getAuditStatusText(currentImage.auditStatus) }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="质量评级">
            <n-rate v-model:value="currentImage.qualityRating" :count="5" readonly />
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">{{ currentImage.createdAt }}</n-descriptions-item>
          <n-descriptions-item label="描述" :span="2">
            {{ currentImage.description || '无描述' }}
          </n-descriptions-item>
          <n-descriptions-item label="审核备注" :span="2" v-if="currentImage.auditRemark">
            {{ currentImage.auditRemark }}
          </n-descriptions-item>
        </n-descriptions>
      </div>
    </n-modal>

    <!-- 审核对话框 -->
    <n-modal v-model:show="showAuditModal" preset="dialog" title="审核图像">
      <template #header>
        <div class="text-lg font-bold">审核图像</div>
      </template>
      <div class="py-4">
        <p class="mb-4">确定要将图像审核状态更改为 {{ getAuditStatusText(auditStatus) }} 吗？</p>
        <n-input
          v-if="auditStatus === 2"
          v-model:value="auditRemark"
          type="textarea"
          placeholder="请输入拒绝原因"
          :rows="3"
        />
      </div>
      <template #action>
        <n-space>
          <n-button @click="showAuditModal = false">取消</n-button>
          <n-button type="primary" :loading="submitting" @click="confirmAudit">确认</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 配置对话框 -->
    <n-modal v-model:show="showConfigModal" preset="card" style="width: 600px">
      <template #header>
        <div class="text-lg font-bold">图像生成配置</div>
      </template>
      <n-spin :show="configLoading">
        <n-form
          v-if="imageConfig"
          ref="formRef"
          :model="imageConfig"
          label-placement="left"
          label-width="120px"
          require-mark-placement="right-hanging"
        >
          <n-form-item label="AI模型" path="model">
            <n-select v-model:value="imageConfig.model" :options="modelOptions" />
          </n-form-item>
          <n-form-item label="图像尺寸" path="size">
            <n-select v-model:value="imageConfig.size" :options="sizeOptions" />
          </n-form-item>
          <n-form-item label="图像质量" path="quality">
            <n-select v-model:value="imageConfig.quality" :options="qualityOptions" />
          </n-form-item>
        </n-form>
      </n-spin>
      <template #footer>
        <div class="flex justify-end">
          <n-button @click="showConfigModal = false">取消</n-button>
          <n-button
            type="primary"
            class="ml-2"
            :loading="configSubmitting"
            @click="saveImageConfig"
          >
            保存
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { RefreshOutline, SearchOutline, SettingsOutline } from '@vicons/ionicons5';
import {
  getImageList,
  getImageConfig,
  updateImageConfig,
  auditImage,
  updateImageQuality,
} from '@/api/modules/storybook';

const message = useMessage();

// 图像类型选项
const imageTypeOptions = [
  { label: '绘本页面', value: 1 },
  { label: '角色形象', value: 2 },
  { label: '场景背景', value: 3 },
  { label: '其他', value: 0 },
];

// 审核状态选项
const auditStatusOptions = [
  { label: '待审核', value: 0 },
  { label: '已通过', value: 1 },
  { label: '已拒绝', value: 2 },
];

// 模型选项
const modelOptions = [
  { label: 'GPT-Image-1', value: 'gpt-image-1' },
  { label: 'DALL-E-3', value: 'dall-e-3' },
];

// 尺寸选项
const sizeOptions = [
  { label: '1024x1024', value: '1024x1024' },
  { label: '1792x1024', value: '1792x1024' },
  { label: '1024x1792', value: '1024x1792' },
];

// 质量选项
const qualityOptions = [
  { label: '标准', value: 'standard' },
  { label: '高清', value: 'hd' },
];

// 搜索参数
const searchParams = reactive({
  keyword: '',
  imageType: null,
  auditStatus: null,
  page: 1,
  limit: 12,
});

// 图像数据
const images = ref<any[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 12,
  itemCount: 0,
  pageSizes: [12, 24, 36, 48],
});

// 预览相关
const showPreviewModal = ref(false);
const currentImage = ref<any>(null);

// 审核相关
const showAuditModal = ref(false);
const auditStatus = ref(1);
const auditRemark = ref('');
const submitting = ref(false);

// 配置相关
const showConfigModal = ref(false);
const imageConfig = ref<any>(null);
const configLoading = ref(false);
const configSubmitting = ref(false);

// 获取审核状态文本
const getAuditStatusText = (status: number) => {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝',
  };
  return statusMap[status] || '未知状态';
};

// 获取审核状态类型
const getAuditStatusType = (status: number) => {
  const statusMap = {
    0: 'default',
    1: 'success',
    2: 'error',
  };
  return statusMap[status] || 'default';
};

// 获取图像类型文本
const getImageTypeText = (type: number) => {
  const typeMap = {
    0: '其他',
    1: '绘本页面',
    2: '角色形象',
    3: '场景背景',
  };
  return typeMap[type] || '未知类型';
};

// 获取图像类型标签类型
const getImageTypeType = (type: number) => {
  const typeMap = {
    0: 'default',
    1: 'info',
    2: 'success',
    3: 'warning',
  };
  return typeMap[type] || 'default';
};

// 加载图像列表
const loadImages = async () => {
  loading.value = true;
  try {
    const res = await getImageList(searchParams);
    images.value = res.data.items;
    pagination.itemCount = res.data.total;
  } catch (error) {
    console.error('加载图像列表失败', error);
    message.error('加载图像列表失败');
  } finally {
    loading.value = false;
  }
};

// 加载图像配置
const loadImageConfig = async () => {
  configLoading.value = true;
  try {
    const res = await getImageConfig();
    imageConfig.value = res.data;
  } catch (error) {
    console.error('加载图像配置失败', error);
    message.error('加载图像配置失败');
  } finally {
    configLoading.value = false;
  }
};

// 保存图像配置
const saveImageConfig = async () => {
  configSubmitting.value = true;
  try {
    await updateImageConfig(imageConfig.value);
    message.success('保存配置成功');
    showConfigModal.value = false;
  } catch (error) {
    console.error('保存配置失败', error);
    message.error('保存配置失败');
  } finally {
    configSubmitting.value = false;
  }
};

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  searchParams.page = page;
  loadImages();
};

// 处理每页数量变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  searchParams.page = 1;
  searchParams.limit = pageSize;
  loadImages();
};

// 处理搜索
const handleSearch = () => {
  searchParams.page = 1;
  pagination.page = 1;
  loadImages();
};

// 处理刷新
const handleRefresh = () => {
  loadImages();
};

// 处理预览
const handlePreview = (image: any) => {
  currentImage.value = image;
  showPreviewModal.value = true;
};

// 处理审核
const handleAudit = (image: any, status: number) => {
  currentImage.value = image;
  auditStatus.value = status;
  auditRemark.value = '';
  showAuditModal.value = true;
};

// 确认审核
const confirmAudit = async () => {
  if (auditStatus.value === 2 && !auditRemark.value) {
    message.warning('请输入拒绝原因');
    return;
  }

  submitting.value = true;
  try {
    await auditImage(currentImage.value.id, auditStatus.value, auditRemark.value);
    message.success(`审核${auditStatus.value === 1 ? '通过' : '拒绝'}成功`);
    showAuditModal.value = false;
    loadImages();
  } catch (error) {
    console.error('审核失败', error);
    message.error('审核失败');
  } finally {
    submitting.value = false;
  }
};

onMounted(() => {
  loadImages();
  loadImageConfig();
});
</script>

<style scoped>
.proCard {
  border-radius: 4px;
}

.image-card {
  transition: all 0.3s;
  border: 2px solid transparent;
}

.image-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
