import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import FeedbackToast from '@/components/FeedbackToast.vue';

describe('FeedbackToast.vue', () => {
  beforeEach(() => {
    // 创建一个挂载点
    const el = document.createElement('div');
    el.id = 'app';
    document.body.appendChild(el);
    
    // 模拟计时器
    vi.useFakeTimers();
  });

  afterEach(() => {
    // 清理DOM
    document.body.innerHTML = '';
    
    // 恢复计时器
    vi.restoreAllMocks();
  });

  it('renders correctly when visible is true', async () => {
    const wrapper = mount(FeedbackToast, {
      props: {
        message: '测试消息',
        type: 'success',
        duration: 3000,
        visible: true
      },
      attachTo: '#app'
    });

    // 检查组件是否渲染
    expect(wrapper.text()).toContain('测试消息');
    
    // 检查样式类是否正确应用
    const toast = wrapper.find('[role="alert"]');
    expect(toast.classes()).toContain('bg-green-500');
  });

  it('does not render when visible is false', () => {
    const wrapper = mount(FeedbackToast, {
      props: {
        message: '测试消息',
        type: 'success',
        duration: 3000,
        visible: false
      },
      attachTo: '#app'
    });

    // 检查组件是否不可见
    const toast = wrapper.find('[role="alert"]');
    expect(toast.exists()).toBe(false);
  });

  it('emits update:visible event after duration', async () => {
    const wrapper = mount(FeedbackToast, {
      props: {
        message: '测试消息',
        type: 'success',
        duration: 3000,
        visible: true
      },
      attachTo: '#app'
    });

    // 前进3000毫秒
    vi.advanceTimersByTime(3000);
    
    // 检查是否发出了更新事件
    expect(wrapper.emitted('update:visible')).toBeTruthy();
    expect(wrapper.emitted('update:visible')![0]).toEqual([false]);
  });

  it('renders error type correctly', () => {
    const wrapper = mount(FeedbackToast, {
      props: {
        message: '错误消息',
        type: 'error',
        duration: 3000,
        visible: true
      },
      attachTo: '#app'
    });

    // 检查错误样式是否正确应用
    const toast = wrapper.find('[role="alert"]');
    expect(toast.classes()).toContain('bg-red-500');
  });
});
