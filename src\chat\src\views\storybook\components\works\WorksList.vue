<template>
  <div class="works-list-container">
    <!-- 顶部筛选和搜索 - 更加儿童友好的设计 -->
    <div class="works-header">
      <!-- 搜索框 - 更加直观友好 -->
      <div class="search-box">
        <div class="search-input-wrapper">
          <NInput
            v-model:value="searchText"
            placeholder="输入故事名字找一找..."
            clearable
            @update:value="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <div class="search-icon">
                <span class="search-emoji">🔍</span>
              </div>
            </template>
          </NInput>
        </div>
      </div>

      <!-- 排序选项 - 简化为图形按钮 -->
      <div class="sort-buttons">
        <div class="sort-label">排序方式：</div>
        <NButtonGroup>
          <NButton
            v-for="(option, key) in sortOptions"
            :key="key"
            :type="sortOption === key ? 'primary' : 'default'"
            @click="setSortOption(key)"
            class="sort-button"
            :title="option.label"
          >
            <span class="sort-emoji">{{ option.emoji }}</span>
            <span class="sort-text">{{ key.includes('title') ? '名字' : key.includes('updatedAt') ? '最近编辑' : '最近创建' }}</span>
          </NButton>
        </NButtonGroup>
      </div>

      <!-- 视图切换 - 更加直观 -->
      <div class="view-actions">
        <NButtonGroup>
          <NButton
            :type="viewMode === 'grid' ? 'primary' : 'default'"
            @click="viewMode = 'grid'"
            class="view-button"
            title="格子视图"
          >
            <span class="view-emoji">🔲</span>
          </NButton>
          <NButton
            :type="viewMode === 'list' ? 'primary' : 'default'"
            @click="viewMode = 'list'"
            class="view-button"
            title="列表视图"
          >
            <span class="view-emoji">📋</span>
          </NButton>
        </NButtonGroup>

        <NButton @click="refreshWorks" class="refresh-button" title="刷新我的故事">
          <span class="refresh-emoji">🔄</span>
        </NButton>
      </div>
    </div>

    <!-- 作品列表 - 更加儿童友好的加载和空状态 -->
    <div v-if="loading" class="works-loading">
      <div class="loading-animation">
        <span class="loading-emoji">📚</span>
      </div>
      <p class="loading-text">正在找故事书...</p>
    </div>

    <div v-else-if="filteredWorks.length === 0" class="works-empty">
      <div class="empty-animation">
        <span class="empty-emoji">🔍</span>
      </div>
      <h3 class="empty-title">还没有故事书呢</h3>
      <p class="empty-description">点击下面的按钮开始创作你的第一本故事书吧！</p>
      <NButton type="primary" @click="createNewWork" class="create-button">
        <span class="button-emoji">✏️</span>
        <span>开始创作</span>
      </NButton>
    </div>

    <div v-else :class="['works-grid', { 'list-view': viewMode === 'list' }]">
      <!-- 添加新建故事按钮 -->
      <div class="new-work-button-container">
        <NButton type="primary" @click="createNewWork" class="new-work-button">
          <span class="button-emoji">✏️</span>
          <span>创作新故事</span>
        </NButton>
      </div>
      <!-- 添加装饰元素 -->
      <div class="decoration-element star-1">✨</div>
      <div class="decoration-element star-2">✨</div>
      <div class="decoration-element star-3">✨</div>

      <NGrid :cols="viewMode === 'grid' ? (isMobile ? 1 : 3) : 1" :x-gap="20" :y-gap="24">
        <NGridItem v-for="work in paginatedWorks" :key="work.id">
          <WorkCard
            :work="work"
            :view-mode="viewMode"
            @edit="editWork"
            @view="viewWork"
            @delete="confirmDelete"
            @export="showExportModal"
            @share="showShareModal"
            @move="showMoveModal"
          />
        </NGridItem>
      </NGrid>
    </div>

    <!-- 分页 - 更加儿童友好的设计 -->
    <div v-if="filteredWorks.length > 0" class="works-pagination">
      <div class="pagination-info">
        <span class="info-emoji">📖</span>
        <span>第 {{ currentPage }} 页，共 {{ Math.ceil(totalWorks / pageSize) }} 页</span>
      </div>

      <div class="pagination-controls">
        <NButton
          class="page-button prev-button"
          :disabled="currentPage === 1"
          @click="handlePageChange(currentPage - 1)"
        >
          <span class="page-emoji">👈</span>
          <span>上一页</span>
        </NButton>

        <NButton
          class="page-button next-button"
          :disabled="currentPage >= Math.ceil(totalWorks / pageSize)"
          @click="handlePageChange(currentPage + 1)"
        >
          <span>下一页</span>
          <span class="page-emoji">👉</span>
        </NButton>
      </div>

      <div class="page-size-selector">
        <span>每页显示：</span>
        <NButtonGroup>
          <NButton
            v-for="size in [12, 24, 36]"
            :key="size"
            :type="pageSize === size ? 'primary' : 'default'"
            class="size-button"
            @click="handlePageSizeChange(size)"
          >
            {{ size }}
          </NButton>
        </NButtonGroup>
      </div>
    </div>

    <!-- 删除确认对话框 - 更加儿童友好的设计 -->
    <NModal
      v-model:show="showDeleteModal"
      preset="dialog"
      title="放入回收站"
      positive-text="放入回收站"
      negative-text="不要放"
      class="custom-modal delete-modal"
      @positive-click="deleteWork"
    >
      <div class="modal-content">
        <div class="modal-animation">
          <span class="modal-emoji">🗑️</span>
        </div>
        <h3 class="modal-title">要把这个故事放进回收站吗？</h3>
        <p class="modal-story-name">《{{ workToDelete?.title || '未命名故事' }}》</p>
        <p class="modal-description">别担心！放进回收站的故事可以再拿出来哦~</p>
      </div>
    </NModal>

    <!-- 导出对话框 - 更加儿童友好的设计 -->
    <NModal
      v-model:show="showExportModalVisible"
      preset="dialog"
      title="保存我的故事"
      positive-text="开始保存"
      negative-text="不保存了"
      class="custom-modal export-modal"
      @positive-click="handleExport"
    >
      <div class="modal-content">
        <div class="export-header">
          <div class="modal-animation">
            <span class="modal-emoji">📥</span>
          </div>
          <h3 class="modal-title">把《{{ workToExport?.title || '未命名故事' }}》保存到电脑</h3>
        </div>

        <NForm ref="exportFormRef" :model="exportForm" class="export-form">
          <NFormItem label="保存成什么格式？" class="form-item">
            <NRadioGroup v-model:value="exportForm.format">
              <NRadio value="pdf">
                <div class="option-with-icon">
                  <span class="format-emoji">📄</span>
                  <span>PDF文档</span>
                  <span class="format-hint">(可以打印)</span>
                </div>
              </NRadio>
              <NRadio value="images">
                <div class="option-with-icon">
                  <span class="format-emoji">🖼️</span>
                  <span>图片集</span>
                  <span class="format-hint">(可以分享)</span>
                </div>
              </NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem label="图片质量怎么样？" v-if="exportForm.format === 'images'" class="form-item">
            <NRadioGroup v-model:value="exportForm.quality">
              <NRadio value="high">
                <div class="option-with-icon">
                  <span class="quality-emoji">🌟🌟🌟</span>
                  <span>超清晰</span>
                </div>
              </NRadio>
              <NRadio value="medium">
                <div class="option-with-icon">
                  <span class="quality-emoji">🌟🌟</span>
                  <span>清晰</span>
                </div>
              </NRadio>
              <NRadio value="low">
                <div class="option-with-icon">
                  <span class="quality-emoji">🌟</span>
                  <span>快速</span>
                </div>
              </NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem label="要保存哪些页面？" class="form-item">
            <NRadioGroup v-model:value="exportForm.range">
              <NRadio value="all">
                <div class="option-with-icon">
                  <span class="range-emoji">📚</span>
                  <span>全部页面</span>
                </div>
              </NRadio>
              <NRadio value="custom">
                <div class="option-with-icon">
                  <span class="range-emoji">📑</span>
                  <span>选择页面</span>
                </div>
              </NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem v-if="exportForm.range === 'custom'" class="form-item">
            <div class="range-input-group">
              <span class="range-label">从第</span>
              <NInputNumber
                v-model:value="exportForm.startPage"
                :min="1"
                :max="workToExport?.pageCount || 1"
                class="range-input"
              />
              <span class="range-label">页到第</span>
              <NInputNumber
                v-model:value="exportForm.endPage"
                :min="exportForm.startPage"
                :max="workToExport?.pageCount || 1"
                class="range-input"
              />
              <span class="range-label">页</span>
            </div>
          </NFormItem>
        </NForm>
      </div>
    </NModal>

    <!-- 分享对话框 - 更加儿童友好的设计 -->
    <NModal
      v-model:show="showShareModalVisible"
      preset="dialog"
      title="分享我的故事"
      positive-text="复制链接"
      negative-text="关闭"
      class="custom-modal share-modal"
      @positive-click="copyShareLink"
    >
      <div class="modal-content">
        <div class="share-header">
          <div class="modal-animation">
            <span class="modal-emoji">🔗</span>
          </div>
          <h3 class="modal-title">把《{{ workToShare?.title || '未命名故事' }}》分享给朋友</h3>
        </div>

        <div class="share-content">
          <div class="share-method">
            <div class="method-title">
              <span class="method-emoji">📱</span>
              <span>手机扫一扫</span>
            </div>
            <div class="qrcode-container">
              <div ref="qrcodeRef" class="qrcode"></div>
              <p class="qrcode-tip">用手机扫描这个二维码就能看到你的故事啦！</p>
            </div>
          </div>

          <div class="share-method">
            <div class="method-title">
              <span class="method-emoji">🔗</span>
              <span>复制链接发给朋友</span>
            </div>
            <div class="link-container">
              <NInput v-model:value="shareLink" readonly class="share-link" />
              <NButton @click="copyShareLink" class="copy-button">
                <span class="copy-emoji">📋</span>
                <span>复制</span>
              </NButton>
            </div>
          </div>

          <div class="share-options">
            <div class="options-title">分享设置：</div>
            <div class="options-list">
              <NCheckbox v-model:checked="shareOptions.allowDownload">
                <div class="option-with-icon">
                  <span class="option-emoji">📥</span>
                  <span>允许朋友下载我的故事</span>
                </div>
              </NCheckbox>
              <NCheckbox v-model:checked="shareOptions.expiresIn7Days">
                <div class="option-with-icon">
                  <span class="option-emoji">⏰</span>
                  <span>7天后链接不能用了</span>
                </div>
              </NCheckbox>
            </div>
          </div>
        </div>
      </div>
    </NModal>

    <!-- 移动到文件夹对话框 - 更加儿童友好的设计 -->
    <NModal
      v-model:show="showMoveModalVisible"
      preset="dialog"
      title="整理我的故事"
      positive-text="放进去"
      negative-text="不放了"
      class="custom-modal move-modal"
      @positive-click="handleMove"
    >
      <div class="modal-content">
        <div class="move-header">
          <div class="modal-animation">
            <span class="modal-emoji">📁</span>
          </div>
          <h3 class="modal-title">把《{{ workToMove?.title || '未命名故事' }}》放到哪个收藏夹？</h3>
        </div>

        <div class="folder-list">
          <NRadioGroup v-model:value="selectedFolderId">
            <NSpace vertical>
              <NRadio :value="0" class="folder-radio">
                <div class="folder-item">
                  <span class="folder-emoji">📥</span>
                  <span class="folder-name">所有故事</span>
                </div>
              </NRadio>
              <NRadio v-for="folder in folders" :key="folder.id" :value="folder.id" class="folder-radio">
                <div class="folder-item">
                  <span class="folder-emoji">{{ getFolderEmoji(folder) }}</span>
                  <span class="folder-name">{{ folder.name }}</span>
                </div>
              </NRadio>
            </NSpace>
          </NRadioGroup>

          <!-- 新建文件夹按钮 -->
          <div class="new-folder-button-container">
            <NButton @click="showCreateFolderModal" class="new-folder-button">
              <span class="button-emoji">📁</span>
              <span>新建收藏夹</span>
            </NButton>
          </div>
        </div>

        <div class="folder-hint">
          <span class="hint-emoji">💡</span>
          <span class="hint-text">把故事放进收藏夹，可以更容易找到它们哦！</span>
        </div>
      </div>
    </NModal>

    <!-- 创建文件夹对话框 -->
    <NModal
      v-model:show="showCreateFolderModalVisible"
      preset="dialog"
      title="创建新收藏夹"
      positive-text="创建"
      negative-text="取消"
      class="custom-modal folder-modal"
      @positive-click="createNewFolder"
    >
      <div class="modal-content">
        <div class="folder-header">
          <div class="modal-animation">
            <span class="modal-emoji">📁</span>
          </div>
          <h3 class="modal-title">创建一个新的收藏夹</h3>
        </div>

        <NForm ref="folderFormRef" :model="folderForm" class="folder-form">
          <NFormItem label="收藏夹名称" class="form-item">
            <NInput v-model:value="folderForm.name" placeholder="给收藏夹起个好听的名字吧" />
          </NFormItem>

          <div class="folder-emoji-selector">
            <div class="emoji-selector-title">选择一个图标：</div>
            <div class="emoji-grid">
              <div
                v-for="emoji in folderEmojis"
                :key="emoji"
                :class="['emoji-item', folderForm.emoji === emoji ? 'selected' : '']"
                @click="folderForm.emoji = emoji"
              >
                {{ emoji }}
              </div>
            </div>
          </div>
        </NForm>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
  NInput, NButtonGroup, NButton, NGrid, NGridItem, NModal, NForm, NFormItem,
  NRadioGroup, NRadio, NInputNumber, NCheckbox, NSpace
} from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import WorkCard from './WorkCard.vue';
import { useAuthStore } from '@/store';
import { useWorksStore, Work, WorkFolder } from '@/store/modules/works';
import { useGlobalStoreWithOut } from '@/store';
import QRCode from 'qrcode';

const props = defineProps({
  folderId: {
    type: Number,
    default: null
  },
  isTrash: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['create']);

// 布局响应式
const { isMobile } = useBasicLayout();
const router = useRouter();
const authStore = useAuthStore();
const worksStore = useWorksStore();

// 状态变量
const loading = ref(false);
const viewMode = ref('grid'); // 'grid' 或 'list'
const searchText = ref('');
const sortOption = ref('updatedAt-DESC');
const currentPage = ref(1);
const pageSize = ref(12);
const showDeleteModal = ref(false);
const workToDelete = ref<Work | null>(null);
const showExportModalVisible = ref(false);
const workToExport = ref<Work | null>(null);
const showShareModalVisible = ref(false);
const workToShare = ref<Work | null>(null);
const showMoveModalVisible = ref(false);
const workToMove = ref<Work | null>(null);
const selectedFolderId = ref<number | null>(null);
const shareLink = ref('');
const qrcodeRef = ref<HTMLElement | null>(null);

// 创建文件夹相关
const showCreateFolderModalVisible = ref(false);
const folderForm = ref({
  name: '',
  emoji: '📁'
});
const folderEmojis = ['📁', '📚', '❤️', '🏫', '🐾', '🗺️', '🔬', '🧚', '👨‍👩‍👧‍👦', '🎨', '🎮', '🎵', '🌈', '🌟', '🌍', '🏆', '🎭', '🎪'];

// 导出表单
const exportForm = ref({
  format: 'pdf',
  quality: 'high',
  range: 'all',
  startPage: 1,
  endPage: 1
});

// 分享选项
const shareOptions = ref({
  allowDownload: true,
  expiresIn7Days: true
});

// 排序选项 - 简化版本，只保留值和标签
const sortOptions = {
  'updatedAt-DESC': { label: '我最近编辑的', emoji: '⏰' },
  'createdAt-DESC': { label: '我最近创建的', emoji: '🆕' },
  'createdAt-ASC': { label: '我最早创建的', emoji: '📅' },
  'title-ASC': { label: '按故事名字排序（顺序）', emoji: '🔤' },
  'title-DESC': { label: '按故事名字排序（倒序）', emoji: '🔡' }
};

// 获取作品列表
const fetchWorks = async () => {
  if (!authStore.isLogin) {
    loading.value = false;
    return;
  }

  loading.value = true;
  try {
    const [sortField, sortOrder] = sortOption.value.split('-');

    await worksStore.fetchMyWorks({
      folderId: props.folderId,
      isDeleted: props.isTrash ? 1 : 0,
      sortField,
      sortOrder: sortOrder.toUpperCase() as 'ASC' | 'DESC',
      keyword: searchText.value
    });
  } catch (error) {
    console.error('获取作品列表失败:', error);
    window.$message?.error('获取作品列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取文件夹列表
const fetchFolders = async () => {
  if (!authStore.isLogin) return;

  try {
    await worksStore.fetchFolders();
  } catch (error) {
    console.error('获取文件夹列表失败:', error);
    window.$message?.error('获取文件夹列表失败');
  }
};

// 文件夹列表
const folders = computed(() => worksStore.folders);

// 筛选和排序后的作品
const filteredWorks = computed(() => {
  return props.isTrash ? worksStore.trashWorks : worksStore.myWorks;
});

// 总作品数
const totalWorks = computed(() => filteredWorks.value.length);

// 分页后的作品
const paginatedWorks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredWorks.value.slice(start, end);
});

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchWorks();
};

// 处理排序变化
const handleSortChange = () => {
  currentPage.value = 1;
  fetchWorks();
};

// 设置排序选项
const setSortOption = (option) => {
  sortOption.value = option;
  handleSortChange();
};

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 处理每页数量变化
const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 刷新作品列表
const refreshWorks = () => {
  fetchWorks();
};

// 创建新绘本
const createNewWork = () => {
  // 触发create事件，让父组件处理创建新绘本的逻辑
  emit('create');
  // 确保用户中心对话框关闭
  const globalStore = useGlobalStoreWithOut();
  globalStore.updateUserCenterDialog(false);
  // 不再使用路由跳转，而是由父组件处理标签页切换

  // 设置一个延时，等待创建完成后刷新列表
  setTimeout(() => {
    console.log('创建新故事后刷新列表');
    fetchWorks();
  }, 1000);
};

// 编辑作品
const editWork = (work) => {
  // 在新标签页中打开编辑页面，避免离开当前页面
  const editUrl = `/storybook/edit/${work.id}`;
  window.open(editUrl, '_blank');
};

// 查看作品
const viewWork = (work) => {
  // 在新标签页中打开查看页面，避免离开当前页面
  const viewUrl = `/storybook/view/${work.id}`;
  window.open(viewUrl, '_blank');
};

// 确认删除
const confirmDelete = (work) => {
  workToDelete.value = work;
  showDeleteModal.value = true;
};

// 删除作品
const deleteWork = async () => {
  if (!workToDelete.value) return;

  try {
    await worksStore.deleteWork(workToDelete.value.id);
    window.$message?.success('作品已移至回收站');
    fetchWorks();
  } catch (error) {
    console.error('删除作品失败:', error);
    window.$message?.error('删除作品失败');
  } finally {
    showDeleteModal.value = false;
    workToDelete.value = null;
  }
};

// 显示导出对话框
const showExportModal = ({ work, format }) => {
  workToExport.value = work;
  exportForm.value.format = format;
  exportForm.value.endPage = work.pageCount || 1;
  showExportModalVisible.value = true;
};

// 处理导出
const handleExport = async () => {
  if (!workToExport.value) return;

  try {
    window.$message?.info('正在准备导出...');

    const options = {
      range: exportForm.value.range,
      startPage: exportForm.value.range === 'custom' ? exportForm.value.startPage : undefined,
      endPage: exportForm.value.range === 'custom' ? exportForm.value.endPage : undefined,
      quality: exportForm.value.format === 'images' ? exportForm.value.quality : undefined
    };

    let result;
    if (exportForm.value.format === 'pdf') {
      result = await worksStore.exportWorkToPdf(workToExport.value.id, options);
    } else {
      result = await worksStore.exportWorkToImages(workToExport.value.id, options);
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.href = result.url;
    link.download = result.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.$message?.success('绘本导出成功');
    showExportModalVisible.value = false;
  } catch (error) {
    console.error('导出绘本失败:', error);
    window.$message?.error('导出绘本失败');
  }
};

// 显示分享对话框
const showShareModal = (work) => {
  workToShare.value = work;
  // 生成分享链接
  shareLink.value = `${window.location.origin}/storybook/share/${work.id}`;
  showShareModalVisible.value = true;

  // 生成二维码
  setTimeout(() => {
    if (qrcodeRef.value) {
      QRCode.toCanvas(qrcodeRef.value, shareLink.value, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff'
        }
      }).catch(err => {
        console.error('生成二维码失败:', err);
      });
    }
  }, 100);
};

// 复制分享链接
const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)
    .then(() => {
      window.$message?.success('链接已复制到剪贴板');
    })
    .catch(() => {
      window.$message?.error('复制链接失败');
    });
};

// 显示移动对话框
const showMoveModal = (work) => {
  workToMove.value = work;
  selectedFolderId.value = work.folderId;
  showMoveModalVisible.value = true;
};

// 处理移动
const handleMove = async () => {
  if (!workToMove.value) return;

  try {
    await worksStore.moveWorkToFolder(workToMove.value.id, selectedFolderId.value === 0 ? undefined : selectedFolderId.value);
    window.$message?.success('移动成功');
    fetchWorks();
    showMoveModalVisible.value = false;
  } catch (error) {
    console.error('移动作品失败:', error);
    window.$message?.error('移动作品失败');
  }
};

// 根据文件夹获取对应的emoji
const getFolderEmoji = (folder) => {
  // 如果文件夹有自定义emoji，则使用它
  if (folder.emoji) {
    return folder.emoji;
  }

  // 根据文件夹名称或ID返回不同的emoji
  const folderName = folder.name.toLowerCase();

  if (folderName.includes('喜欢') || folderName.includes('收藏')) {
    return '❤️';
  } else if (folderName.includes('学校') || folderName.includes('课堂')) {
    return '🏫';
  } else if (folderName.includes('动物')) {
    return '🐾';
  } else if (folderName.includes('冒险')) {
    return '🗺️';
  } else if (folderName.includes('科学')) {
    return '🔬';
  } else if (folderName.includes('童话')) {
    return '🧚';
  } else if (folderName.includes('家庭')) {
    return '👨‍👩‍👧‍👦';
  } else {
    // 默认文件夹emoji
    return '📁';
  }
};

// 显示创建文件夹对话框
const showCreateFolderModal = () => {
  // 重置表单
  folderForm.value = {
    name: '',
    emoji: '📁'
  };
  showCreateFolderModalVisible.value = true;
};

// 创建新文件夹
const createNewFolder = async () => {
  if (!folderForm.value.name.trim()) {
    window.$message?.warning('请输入收藏夹名称');
    return;
  }

  try {
    await worksStore.createFolder({
      name: folderForm.value.name.trim(),
      emoji: folderForm.value.emoji
    });
    window.$message?.success('创建收藏夹成功');
    fetchFolders();
    showCreateFolderModalVisible.value = false;
  } catch (error) {
    console.error('创建收藏夹失败:', error);
    window.$message?.error('创建收藏夹失败');
  }
};

// 组件挂载时获取作品列表
onMounted(() => {
  fetchWorks();
  fetchFolders();

  // 检查是否有新创建的故事标记
  const createdNewStory = localStorage.getItem('storybook-created-new');
  if (createdNewStory) {
    try {
      const storyInfo = JSON.parse(createdNewStory);
      console.log('检测到新创建的故事:', storyInfo.title, '(ID:', storyInfo.id, ')');

      // 清除标记
      localStorage.removeItem('storybook-created-new');

      // 刷新列表以显示新故事
      setTimeout(() => {
        console.log('刷新列表以显示新创建的故事');
        fetchWorks();
      }, 500);
    } catch (error) {
      console.error('解析新故事信息失败:', error);
    }
  }
});

// 监听登录状态变化
watch(() => authStore.isLogin, (isLogin) => {
  if (isLogin) {
    fetchWorks();
    fetchFolders();
  }
});

// 监听文件夹ID变化
watch(() => props.folderId, () => {
  currentPage.value = 1;
  fetchWorks();
});

// 监听是否为回收站变化
watch(() => props.isTrash, () => {
  currentPage.value = 1;
  fetchWorks();
});
</script>

<style scoped>
/* 基础样式 - 更加儿童友好的设计 */
.works-list-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  height: 100%;
  background-color: #f0f9ff;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(249, 115, 22, 0.05) 0%, transparent 20%);
  border-radius: 1.5rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
  border: 2px solid rgba(186, 230, 253, 0.8);
  overflow-y: auto;
  transition: all 0.4s ease;
  position: relative;
}

.dark .works-list-container {
  background-color: #1e293b;
  border-color: rgba(51, 65, 85, 0.8);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 顶部标题和搜索区域 */
.works-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  margin-bottom: 0.5rem;
  position: relative;
}

.dark .works-header {
  border-bottom: 1px solid rgba(51, 65, 85, 0.8);
}

.works-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
}

.dark .works-header::after {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
}

.search-filter {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  flex: 1;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.search-input {
  border-radius: 1.5rem !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9)) !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08) !important;
  transition: all 0.3s ease !important;
  padding: 0.5rem 1rem !important;
  font-size: 1rem !important;
}

.dark .search-input {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9)) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
}

.search-input:hover, .search-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.12) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

.search-icon {
  color: #3b82f6;
  margin-right: 0.5rem;
  transition: all 0.3s ease;
}

.dark .search-icon {
  color: #60a5fa;
}

.search-input:focus + .search-icon,
.search-input:hover + .search-icon {
  transform: scale(1.1);
}

.filter-wrapper, .sort-wrapper {
  position: relative;
  min-width: 150px;
}

.filter-select, .sort-select {
  border-radius: 1.2rem !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9)) !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08) !important;
  transition: all 0.3s ease !important;
  font-size: 0.95rem !important;
}

.dark .filter-select, .dark .sort-select {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9)) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
}

.filter-select:hover, .sort-select:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.12) !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
}

.select-arrow {
  color: #3b82f6;
  transition: all 0.3s ease;
}

.dark .select-arrow {
  color: #60a5fa;
}

.filter-select:hover .select-arrow,
.sort-select:hover .select-arrow {
  transform: translateY(2px);
}

.option-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.option-emoji {
  font-size: 1.2rem;
  display: inline-block;
  transition: all 0.2s ease;
}

.option-with-icon:hover .option-emoji {
  transform: scale(1.2) rotate(5deg);
}

.view-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(240, 249, 255, 0.8));
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  border: 2px solid rgba(186, 230, 253, 0.6);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.05);
}

.dark .view-actions {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.8));
  border: 2px solid rgba(51, 65, 85, 0.6);
}

.view-mode-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.view-mode-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #3b82f6;
}

.dark .view-mode-label {
  color: #60a5fa;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.button-text {
  font-size: 0.85rem;
}

.view-button {
  padding: 0.4rem 0.8rem !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s ease !important;
  background: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(186, 230, 253, 0.8) !important;
}

.dark .view-button {
  background: rgba(30, 41, 59, 0.7) !important;
  border: 1px solid rgba(51, 65, 85, 0.8) !important;
}

.view-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1) !important;
}

.view-button[type="primary"] {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
  color: white !important;
}

.refresh-button {
  border-radius: 0.75rem !important;
  padding: 0.4rem 0.8rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9)) !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08) !important;
  color: #3b82f6 !important;
  transition: all 0.3s ease !important;
}

.dark .refresh-button {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9)) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
  color: #60a5fa !important;
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.12) !important;
}

.refresh-button:active {
  transform: translateY(0) scale(0.98);
}

/* 作品网格 */
.works-grid {
  min-height: 200px;
  transition: all 0.3s ease;
  position: relative;
}

.works-grid.list-view {
  width: 100%;
}

/* 加载和空状态 - 更加儿童友好的设计 */
.works-loading, .works-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 1.5rem;
  padding: 2.5rem;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 1.5rem;
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.03),
              0 3px 5px rgba(0, 0, 0, 0.05);
  border: 2px dashed rgba(186, 230, 253, 0.8);
  transition: all 0.4s ease;
}

/* 加载动画 */
.loading-animation {
  font-size: 4rem;
  animation: bounce 2s infinite;
}

.loading-emoji {
  display: inline-block;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.loading-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: #3b82f6;
  animation: pulse 2s infinite;
}

/* 空状态动画 */
.empty-animation {
  font-size: 5rem;
  animation: float 3s ease-in-out infinite;
}

.empty-emoji {
  display: inline-block;
  filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.1));
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0;
}

.empty-description {
  font-size: 1.1rem;
  color: #64748b;
  text-align: center;
  max-width: 300px;
  margin: 0.5rem 0 1.5rem;
}

.create-button {
  padding: 0.75rem 1.5rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.2) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.create-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3) !important;
}

.button-emoji {
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.create-button:hover .button-emoji {
  transform: rotate(15deg) scale(1.2);
}

/* 装饰元素 */
.decoration-element {
  position: absolute;
  font-size: 1.5rem;
  opacity: 0.5;
  animation: twinkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.star-1 {
  top: 10%;
  left: 5%;
  animation-delay: 0s;
}

.star-2 {
  top: 15%;
  right: 10%;
  animation-delay: 0.5s;
}

.star-3 {
  bottom: 20%;
  right: 15%;
  animation-delay: 1s;
}

.dark .works-loading, .dark .works-empty {
  background-color: rgba(15, 23, 42, 0.5);
  border-color: rgba(51, 65, 85, 0.7);
}

/* 分页 - 更加儿童友好的设计 */
.works-pagination {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  border-top: 2px dashed rgba(186, 230, 253, 0.8);
  background: rgba(240, 249, 255, 0.5);
  border-radius: 1rem;
  gap: 1rem;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #3b82f6;
}

.info-emoji {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

.pagination-controls {
  display: flex;
  gap: 1rem;
}

.page-button {
  padding: 0.5rem 1rem !important;
  border-radius: 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  transition: all 0.3s ease !important;
  background: white !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  color: #3b82f6 !important;
}

.page-button:not(:disabled):hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.15) !important;
}

.page-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.page-emoji {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.page-button:not(:disabled):hover .page-emoji {
  transform: scale(1.2);
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
}

.size-button {
  min-width: 3rem !important;
  height: 2.5rem !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s ease !important;
}

.size-button[type="primary"] {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
}

.dark .works-pagination {
  border-top: 2px dashed rgba(51, 65, 85, 0.8);
  background: rgba(15, 23, 42, 0.3);
}

.dark .pagination-info {
  color: #60a5fa;
}

.dark .page-button {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
  color: #60a5fa !important;
}

/* 对话框样式 - 更加儿童友好的设计 */
.custom-modal {
  border-radius: 1.5rem !important;
  overflow: hidden !important;
}

.modal-animation {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
  text-align: center;
}

.modal-emoji {
  display: inline-block;
  filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.1));
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #3b82f6;
  margin: 0 0 1rem;
  text-align: center;
}

.modal-story-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0.5rem 0;
  text-align: center;
}

.modal-description {
  font-size: 1rem;
  color: #64748b;
  margin: 0.5rem 0 0;
  text-align: center;
}

/* 导出对话框 */
.export-header, .share-header, .move-header {
  margin-bottom: 1.5rem;
}

.format-emoji, .quality-emoji, .range-emoji, .method-emoji, .option-emoji, .folder-emoji, .hint-emoji {
  font-size: 1.3rem;
  margin-right: 0.5rem;
  display: inline-block;
  vertical-align: middle;
}

.format-hint {
  font-size: 0.8rem;
  color: #64748b;
  margin-left: 0.5rem;
}

.range-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.range-label {
  font-size: 1rem;
  color: #64748b;
}

.range-input {
  width: 5rem !important;
}

/* 分享对话框 */
.share-method {
  margin-bottom: 1.5rem;
  background: rgba(240, 249, 255, 0.5);
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid rgba(186, 230, 253, 0.5);
}

.method-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.link-container {
  display: flex;
  gap: 0.5rem;
}

.copy-button {
  display: flex !important;
  align-items: center !important;
  gap: 0.3rem !important;
  padding: 0 1rem !important;
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
}

.options-title {
  font-size: 1rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* 文件夹对话框 */
.folder-radio {
  margin-bottom: 0.5rem;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.7);
}

.folder-emoji {
  font-size: 1.5rem;
  margin-right: 0.75rem;
  transition: all 0.3s ease;
}

.folder-name {
  font-size: 1rem;
  font-weight: 500;
}

.folder-item:hover {
  background: rgba(240, 249, 255, 0.8);
  transform: translateX(3px);
}

.folder-item:hover .folder-emoji {
  transform: scale(1.2) rotate(5deg);
}

.folder-hint {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(240, 249, 255, 0.5);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: 1px dashed rgba(186, 230, 253, 0.7);
}

.hint-text {
  font-size: 0.9rem;
  color: #3b82f6;
}

/* 动画 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0); }
  50% { transform: translateY(-15px) rotate(5deg); }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

/* 搜索框样式已在上方定义，这里不再重复 */

/* 排序按钮 */
.sort-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(240, 249, 255, 0.5);
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(186, 230, 253, 0.5);
}

.sort-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #3b82f6;
}

.sort-button {
  padding: 0.4rem 0.8rem !important;
  border-radius: 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.4rem !important;
  transition: all 0.3s ease !important;
}

.sort-emoji {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.sort-text {
  font-size: 0.9rem;
}

.sort-button:hover .sort-emoji {
  transform: scale(1.2) rotate(10deg);
}

.sort-button[type="primary"] {
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
}

/* 视图切换按钮样式已在上方定义，这里不再重复 */

/* 分享内容 */
.share-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.qrcode {
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.qrcode:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.share-options {
  display: flex;
  gap: 1rem;
  padding: 0.75rem;
  background-color: rgba(241, 245, 249, 0.7);
  border-radius: 0.75rem;
  border: 1px solid rgba(226, 232, 240, 0.7);
}

.dark .share-options {
  background-color: rgba(30, 41, 59, 0.7);
  border-color: rgba(51, 65, 85, 0.7);
}

/* 文件夹列表 */
.folder-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: rgba(241, 245, 249, 0.7);
  border-radius: 0.75rem;
  border: 1px solid rgba(226, 232, 240, 0.7);
  scrollbar-width: thin;
}

.dark .folder-list {
  background-color: rgba(30, 41, 59, 0.7);
  border-color: rgba(51, 65, 85, 0.7);
}

.folder-list::-webkit-scrollbar {
  width: 6px;
}

.folder-list::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 3px;
}

.folder-list::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.dark .folder-list::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.5);
}

.dark .folder-list::-webkit-scrollbar-thumb {
  background-color: rgba(71, 85, 105, 0.5);
}

.folder-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 0.5rem;
  color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.dark .folder-icon {
  background: linear-gradient(135deg, #334155, #1e293b);
  color: #60a5fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.folder-name {
  font-weight: 500;
  color: #1e293b;
  transition: all 0.3s ease;
}

.dark .folder-name {
  color: #e2e8f0;
}

.folder-item:hover {
  background-color: rgba(226, 232, 240, 0.7);
  transform: translateX(2px);
}

.folder-item:hover .folder-icon {
  transform: scale(1.05) rotate(-5deg);
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  color: #2563eb;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.1);
}

.folder-item:hover .folder-name {
  color: #3b82f6;
}

.dark .folder-item:hover {
  background-color: rgba(51, 65, 85, 0.7);
}

.dark .folder-item:hover .folder-icon {
  background: linear-gradient(135deg, #475569, #334155);
  color: #93c5fd;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.15);
}

.dark .folder-item:hover .folder-name {
  color: #60a5fa;
}

/* 对话框样式 */
.custom-modal {
  --primary-color: #3b82f6;
  --primary-hover-color: #2563eb;
  --warning-color: #ef4444;
  --success-color: #10b981;
  --info-color: #6366f1;
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
}

.modal-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.modal-icon.warning {
  background: linear-gradient(135deg, #ef4444, #f87171);
  box-shadow: 0 4px 10px rgba(239, 68, 68, 0.2);
}

.modal-icon.export {
  background: linear-gradient(135deg, #6366f1, #818cf8);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
}

.modal-icon.share {
  background: linear-gradient(135deg, #10b981, #34d399);
  box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2);
}

.modal-icon.move {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
}

.modal-message {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
  margin: 0;
}

.dark .modal-message {
  color: #e2e8f0;
}

.modal-description {
  font-size: 0.9rem;
  color: #64748b;
  text-align: center;
  margin: 0;
}

.dark .modal-description {
  color: #94a3b8;
}

.export-form {
  width: 100%;
  margin-top: 0.5rem;
}

.form-item {
  margin-bottom: 1rem;
}

.option-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.share-description {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0 0 0.5rem 0;
}

.dark .share-description {
  color: #94a3b8;
}

.share-link {
  margin-bottom: 1rem;
}

.qrcode-tip {
  font-size: 0.8rem;
  color: #64748b;
  text-align: center;
  margin: 0.5rem 0 0 0;
}

.dark .qrcode-tip {
  color: #94a3b8;
}

/* 新建故事按钮容器 */
.new-work-button-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  width: 100%;
}

.new-work-button {
  padding: 0.75rem 1.5rem !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  border-radius: 1rem !important;
  background: linear-gradient(135deg, #3b82f6, #60a5fa) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.2) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  animation: pulse 2s infinite;
}

.new-work-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.3) !important;
}

.new-work-button .button-emoji {
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.new-work-button:hover .button-emoji {
  transform: rotate(15deg) scale(1.2);
}

/* 新建文件夹按钮 */
.new-folder-button-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(186, 230, 253, 0.7);
}

.new-folder-button {
  width: 100%;
  padding: 0.5rem 1rem !important;
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  border-radius: 0.75rem !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9)) !important;
  border: 2px solid rgba(186, 230, 253, 0.8) !important;
  color: #3b82f6 !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
}

.new-folder-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.15) !important;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
}

.dark .new-folder-button {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9)) !important;
  border: 2px solid rgba(51, 65, 85, 0.8) !important;
  color: #60a5fa !important;
}

.dark .new-folder-button:hover {
  background: linear-gradient(135deg, rgba(51, 65, 85, 0.9), rgba(30, 41, 59, 0.9)) !important;
}

/* 文件夹创建对话框 */
.folder-form {
  width: 100%;
  margin-top: 1rem;
}

.folder-emoji-selector {
  margin-top: 1rem;
}

.emoji-selector-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 0.75rem;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0.5rem;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.5rem;
  background: rgba(240, 249, 255, 0.7);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.emoji-item:hover {
  transform: scale(1.1);
  background: rgba(224, 242, 254, 0.9);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.15);
}

.emoji-item.selected {
  border-color: #3b82f6;
  background: rgba(219, 234, 254, 0.9);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

.dark .emoji-selector-title {
  color: #60a5fa;
}

.dark .emoji-item {
  background: rgba(30, 41, 59, 0.7);
}

.dark .emoji-item:hover {
  background: rgba(51, 65, 85, 0.9);
}

.dark .emoji-item.selected {
  border-color: #60a5fa;
  background: rgba(30, 58, 138, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .works-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .search-filter {
    flex-direction: column;
    gap: 0.75rem;
  }

  .search-input-wrapper {
    width: 100%;
  }

  .sort-wrapper {
    width: 100%;
  }

  .view-actions {
    width: 100%;
    justify-content: space-between;
    margin-top: 0.5rem;
    padding: 0.75rem;
  }

  .view-mode-buttons {
    flex: 1;
  }

  .button-text {
    font-size: 0.8rem;
  }

  .works-list-container {
    padding: 0.75rem;
    gap: 1rem;
  }

  .option-with-icon {
    font-size: 0.9rem;
  }
}
</style>
