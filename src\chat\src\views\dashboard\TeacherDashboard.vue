<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore, useChatStore } from '@/store';
import { 
  NGrid, 
  NGridItem, 
  NCard, 
  NButton, 
  NIcon, 
  NStatistic, 
  NSpace,
  NTag,
  NList,
  NListItem,
  NThing,
  NTime,
  NProgress,
  NBadge,
  NEmpty
} from 'naive-ui';
import {
  ChatbubbleEllipsesOutline,
  PeopleOutline,
  BarChartOutline,
  BookOutline,
  TrendingUpOutline,
  TimeOutline,
  SchoolOutline,
  CreateOutline
} from '@vicons/ionicons5';

const router = useRouter();
const authStore = useAuthStore();
const chatStore = useChatStore();

// 用户信息
const userInfo = computed(() => authStore.userInfo);

// 模拟数据 - 实际应用中应该从API获取
const dashboardData = ref({
  stats: {
    totalChats: 156,
    activeStudents: 28,
    worksReviewed: 45,
    helpRequests: 8
  },
  recentChats: [
    {
      id: 1,
      title: 'AI作文写作指导',
      time: new Date(Date.now() - 1000 * 60 * 30),
      status: 'active',
      messages: 15
    },
    {
      id: 2, 
      title: '数学概念解释',
      time: new Date(Date.now() - 1000 * 60 * 60 * 2),
      status: 'completed',
      messages: 8
    },
    {
      id: 3,
      title: '英语语法练习',
      time: new Date(Date.now() - 1000 * 60 * 60 * 5),
      status: 'completed', 
      messages: 12
    }
  ],
  studentWorks: [
    {
      id: 1,
      studentName: '小明',
      workTitle: '我的家乡',
      type: 'storybook',
      status: 'pending',
      submitTime: new Date(Date.now() - 1000 * 60 * 60 * 3),
      score: null
    },
    {
      id: 2,
      studentName: '小红',
      workTitle: '小动物的故事',
      type: 'storybook', 
      status: 'reviewed',
      submitTime: new Date(Date.now() - 1000 * 60 * 60 * 24),
      score: 95
    },
    {
      id: 3,
      studentName: '小刚',
      workTitle: '太空探险',
      type: 'storybook',
      status: 'pending',
      submitTime: new Date(Date.now() - 1000 * 60 * 30),
      score: null
    }
  ]
});

// 统计卡片配置
const statCards = computed(() => [
  {
    title: 'AI对话总数',
    value: dashboardData.value.stats.totalChats,
    icon: ChatbubbleEllipsesOutline,
    color: '#2080f0',
    suffix: '次'
  },
  {
    title: '活跃学生',
    value: dashboardData.value.stats.activeStudents,
    icon: PeopleOutline,
    color: '#18a058',
    suffix: '人'
  },
  {
    title: '已评阅作品',
    value: dashboardData.value.stats.worksReviewed,
    icon: BookOutline,
    color: '#f0a020',
    suffix: '份'
  },
  {
    title: '待处理请求',
    value: dashboardData.value.stats.helpRequests,
    icon: TrendingUpOutline,
    color: '#d03050',
    suffix: '个'
  }
]);

// 快速操作
const quickActions = [
  {
    title: '新建AI对话',
    description: '开始新的教学助手对话',
    icon: ChatbubbleEllipsesOutline,
    color: 'info',
    action: () => router.push('/chat')
  },
  {
    title: '查看学生作品',
    description: '查看和评阅学生绘本作品',
    icon: BookOutline,
    color: 'success',
    action: () => router.push('/storybook/works')
  },
  {
    title: '课堂工具',
    description: '使用AI编程等教学工具',
    icon: SchoolOutline,
    color: 'warning',
    action: () => router.push('/aiProgramming')
  },
  {
    title: '教学工具',
    description: '访问智能教学工具集',
    icon: SchoolOutline,
    color: 'warning',
    action: () => router.push('/teacher/tools')
  }
];

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap = {
    'active': 'info',
    'completed': 'success',
    'pending': 'warning',
    'reviewed': 'success'
  };
  return statusMap[status as keyof typeof statusMap] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'active': '进行中',
    'completed': '已完成',
    'pending': '待评阅',
    'reviewed': '已评阅'
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

// 处理快速操作点击
const handleQuickAction = (action: () => void) => {
  action();
};

// 查看对话详情
const viewChatDetail = (chatId: number) => {
  router.push(`/chat?id=${chatId}`);
};

// 查看作品详情
const viewWorkDetail = (workId: number) => {
  router.push(`/storybook/view/${workId}`);
};

onMounted(() => {
  // 这里可以加载真实数据
  console.log('教师仪表板已加载');
});
</script>

<template>
  <div class="teacher-dashboard p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 欢迎信息 -->
    <div class="welcome-section mb-6">
      <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold mb-2">
              欢迎回来，{{ userInfo?.username || '老师' }}！
            </h1>
            <p class="text-blue-100">
              今天是美好的一天，让我们用AI的力量来创造更精彩的教学体验吧！
            </p>
          </div>
          <div class="text-right">
            <NIcon size="48" class="text-blue-200">
              <SchoolOutline />
            </NIcon>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据 -->
    <div class="stats-section mb-6">
      <NGrid :cols="4" :x-gap="16" responsive="screen" class="stats-grid">
        <NGridItem v-for="(stat, index) in statCards" :key="index" :span="1">
          <NCard class="stat-card h-full">
            <div class="flex items-center justify-between">
              <div>
                <NStatistic
                  :label="stat.title"
                  :value="stat.value"
                  :suffix="stat.suffix"
                  class="stat-number"
                />
              </div>
              <div 
                class="w-12 h-12 rounded-lg flex items-center justify-center"
                :style="{ backgroundColor: stat.color + '20' }"
              >
                <NIcon 
                  :component="stat.icon" 
                  size="24" 
                  :style="{ color: stat.color }"
                />
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 主要内容区域 -->
    <NGrid :cols="12" :x-gap="24" :y-gap="24" responsive="screen">
      <!-- 快速操作 -->
      <NGridItem :span="12" :lg-span="6">
        <NCard title="快速操作" class="quick-actions-card">
          <template #header-extra>
            <NIcon :component="CreateOutline" />
          </template>
          
          <div class="grid grid-cols-2 gap-4">
            <div 
              v-for="(action, index) in quickActions" 
              :key="index"
              class="quick-action-item p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-all cursor-pointer"
              @click="handleQuickAction(action.action)"
            >
              <div class="flex items-center mb-2">
                <div 
                  class="w-8 h-8 rounded-lg flex items-center justify-center mr-3"
                  :class="`bg-${action.color}-100 dark:bg-${action.color}-900/30`"
                >
                  <NIcon 
                    :component="action.icon" 
                    size="16" 
                    :class="`text-${action.color}-600 dark:text-${action.color}-400`"
                  />
                </div>
                <span class="font-medium text-gray-900 dark:text-gray-100">
                  {{ action.title }}
                </span>
              </div>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ action.description }}
              </p>
            </div>
          </div>
        </NCard>
      </NGridItem>

      <!-- 最近对话 -->
      <NGridItem :span="12" :lg-span="6">
        <NCard title="最近AI对话" class="recent-chats-card">
          <template #header-extra>
            <NBadge :value="dashboardData.recentChats.length" type="info">
              <NIcon :component="ChatbubbleEllipsesOutline" />
            </NBadge>
          </template>
          
          <NList v-if="dashboardData.recentChats.length > 0">
            <NListItem 
              v-for="chat in dashboardData.recentChats" 
              :key="chat.id"
              class="chat-item cursor-pointer"
              @click="viewChatDetail(chat.id)"
            >
              <NThing>
                <template #header>
                  <div class="flex items-center justify-between">
                    <span class="font-medium">{{ chat.title }}</span>
                    <NTag :type="getStatusType(chat.status)" size="small">
                      {{ getStatusText(chat.status) }}
                    </NTag>
                  </div>
                </template>
                <template #description>
                  <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <NIcon :component="TimeOutline" size="14" class="mr-1" />
                    <NTime :time="chat.time" format="MM-dd HH:mm" />
                    <span class="ml-4">{{ chat.messages }} 条消息</span>
                  </div>
                </template>
              </NThing>
            </NListItem>
          </NList>
          
          <NEmpty v-else description="暂无对话记录" size="small" />
        </NCard>
      </NGridItem>

      <!-- 学生作品状态 -->
      <NGridItem :span="12">
        <NCard title="学生作品状态" class="student-works-card">
          <template #header-extra>
            <div class="flex items-center space-x-2">
              <NBadge 
                :value="dashboardData.studentWorks.filter(w => w.status === 'pending').length" 
                type="warning"
              >
                <span class="text-sm">待评阅</span>
              </NBadge>
              <NIcon :component="BookOutline" />
            </div>
          </template>
          
          <NList v-if="dashboardData.studentWorks.length > 0">
            <NListItem 
              v-for="work in dashboardData.studentWorks" 
              :key="work.id"
              class="work-item cursor-pointer"
              @click="viewWorkDetail(work.id)"
            >
              <NThing>
                <template #header>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <span class="font-medium">{{ work.workTitle }}</span>
                      <span class="text-sm text-gray-500">by {{ work.studentName }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <NTag :type="getStatusType(work.status)" size="small">
                        {{ getStatusText(work.status) }}
                      </NTag>
                      <span v-if="work.score" class="text-sm font-medium text-green-600">
                        {{ work.score }}分
                      </span>
                    </div>
                  </div>
                </template>
                <template #description>
                  <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div class="flex items-center">
                      <NIcon :component="TimeOutline" size="14" class="mr-1" />
                      <span>提交时间：</span>
                      <NTime :time="work.submitTime" format="MM-dd HH:mm" />
                    </div>
                    <div class="flex items-center">
                      <span class="mr-2">类型：</span>
                      <NTag size="small" type="info">绘本作品</NTag>
                    </div>
                  </div>
                </template>
              </NThing>
            </NListItem>
          </NList>
          
          <NEmpty v-else description="暂无学生作品" size="small" />
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>

<style scoped lang="scss">
.teacher-dashboard {
  .welcome-section {
    .bg-gradient-to-r {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    }
  }

  .stats-grid {
    @media (max-width: 768px) {
      :deep(.n-grid-item) {
        span: 2;
      }
    }
    
    @media (max-width: 640px) {
      :deep(.n-grid-item) {
        span: 4;
      }
    }
  }

  .stat-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .stat-number {
      :deep(.n-statistic-value) {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1f2937;
        @apply dark:text-gray-100;
      }
      
      :deep(.n-statistic-label) {
        font-size: 0.875rem;
        color: #6b7280;
        @apply dark:text-gray-400;
      }
    }
  }

  .quick-actions-card {
    .quick-action-item {
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
      }
    }
  }

  .chat-item, .work-item {
    transition: all 0.2s ease;
    border-radius: 8px;
    padding: 8px;
    margin: -8px;
    
    &:hover {
      background-color: #f9fafb;
      @apply dark:bg-gray-800;
    }
  }

  :deep(.n-card) {
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    .n-card-header {
      .n-card-header__main {
        font-weight: 600;
        color: #374151;
        @apply dark:text-gray-200;
      }
    }
  }

  :deep(.n-list-item) {
    border-radius: 8px;
  }

  :deep(.n-empty) {
    padding: 2rem 0;
  }
}

// 响应式优化
@media (max-width: 1024px) {
  .teacher-dashboard {
    .stats-grid {
      :deep(.n-grid-item) {
        span: 2;
      }
    }
  }
}

@media (max-width: 640px) {
  .teacher-dashboard {
    padding: 1rem;
    
    .welcome-section {
      .bg-gradient-to-r {
        padding: 1.5rem;
        
        h1 {
          font-size: 1.5rem;
        }
      }
    }
    
    .stats-grid {
      :deep(.n-grid-item) {
        span: 4;
      }
    }
    
    .quick-actions-card {
      .grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style> 