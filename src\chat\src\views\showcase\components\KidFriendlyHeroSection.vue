<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NButton } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';

const { isMobile } = useBasicLayout();
const router = useRouter();
const showFox = ref(false);
const foxMessage = ref('嗨！想创作一本属于你的绘本吗？点击"开始创作"按钮吧！');

const emit = defineEmits(['create']);

const handleCreateClick = () => {
  emit('create');
};

const scrollToTools = () => {
  const toolsSection = document.querySelector('.kid-creation-tools-section');
  if (toolsSection) {
    toolsSection.scrollIntoView({ behavior: 'smooth' });
  }
};

onMounted(() => {
  // 显示小狐狸助手
  setTimeout(() => {
    showFox.value = true;
  }, 1000);
});

// 小狐狸消息变化
const changeFoxMessage = () => {
  foxMessage.value = '点击"开始创作"，我们一起来创作一本精彩的绘本吧！';
};
</script>

<template>
  <section class="kid-hero-section">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="text-gradient">创作你的绘本故事</span>
          <br />让想象力飞翔！
        </h1>
        <p class="hero-description">
          和AI小狐狸一起，画出你想象中的故事世界！
        </p>
        <div class="hero-buttons">
          <NButton type="primary" size="large" class="create-btn" @click="handleCreateClick">
            <span class="btn-icon">✏️</span>
            开始创作
          </NButton>
          <NButton size="large" class="explore-btn" @click="scrollToTools">
            <span class="btn-icon">🧰</span>
            查看创作工具
          </NButton>
        </div>
      </div>
      <div class="hero-image">
        <img src="@/assets/images/hero-illustration.svg" alt="孩子们在创作" class="main-illustration" />
        <div class="floating-element book-element">
          <SvgIcon name="ri:book-open-line" size="48" color="#FF8A65" />
        </div>
        <div class="floating-element star-element">
          <span class="star-icon">⭐</span>
        </div>
        <div class="floating-element pencil-element">
          <span class="pencil-icon">✏️</span>
        </div>
      </div>
    </div>

    <!-- 小狐狸助手 -->
    <div class="fox-assistant" :class="{ 'show': showFox }" @click="changeFoxMessage">
      <div class="fox-message">
        {{ foxMessage }}
      </div>
      <div class="fox-character">
        <span class="fox-emoji">🦊</span>
      </div>
    </div>
  </section>
</template>

<style scoped>
.kid-hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FFF9C4 0%, #FFECB3 100%);
  overflow: hidden;
}

.kid-hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  z-index: 1;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.hero-text {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.text-gradient {
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.hero-description {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #5D4037;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.create-btn {
  background: linear-gradient(90deg, #FF9A9E, #FFAFBD);
  border: none;
  font-size: 1.25rem;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  box-shadow: 0 8px 15px rgba(255, 154, 158, 0.3);
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.create-btn:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 12px 20px rgba(255, 154, 158, 0.4);
}

.explore-btn {
  background: white;
  color: #FF9A9E;
  border: 2px solid #FF9A9E;
  font-size: 1.25rem;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-family: 'Comic Sans MS', cursive, sans-serif;
}

.explore-btn:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  background: #FFF9C4;
}

.btn-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
  vertical-align: middle;
}

.hero-image {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.main-illustration {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
  animation: float 6s ease-in-out infinite;
  z-index: 1;
}

.floating-element {
  position: absolute;
  animation: float 4s ease-in-out infinite;
}

.book-element {
  top: 10%;
  right: 10%;
  animation-delay: 0.5s;
}

.star-element {
  bottom: 20%;
  left: 15%;
  animation-delay: 1s;
}

.star-icon {
  font-size: 3rem;
  color: #FFD54F;
  filter: drop-shadow(0 5px 10px rgba(255, 213, 79, 0.5));
}

.pencil-element {
  top: 30%;
  left: 5%;
  animation-delay: 1.5s;
}

.pencil-icon {
  font-size: 3rem;
  filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
}

/* 小狐狸助手 */
.fox-assistant {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  align-items: flex-end;
  z-index: 100;
  transform: translateY(150px);
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  cursor: pointer;
}

.fox-assistant.show {
  transform: translateY(0);
  animation: pop-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes pop-in {
  0% {
    transform: translateY(150px) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

.fox-message {
  background: white;
  padding: 15px 20px;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-right: 15px;
  max-width: 250px;
  position: relative;
  font-family: 'Comic Sans MS', cursive, sans-serif;
  color: #5D4037;
  font-size: 1rem;
  line-height: 1.4;
  animation: message-pulse 2s infinite;
  border: 2px solid #FFD54F;
}

@keyframes message-pulse {
  0%, 100% {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 5px 20px rgba(255, 213, 79, 0.4);
  }
}

.fox-message::after {
  content: '';
  position: absolute;
  right: -12px;
  bottom: 15px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 12px solid white;
}

.fox-message::before {
  content: '';
  position: absolute;
  right: -15px;
  bottom: 15px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 12px solid #FFD54F;
  z-index: -1;
}

.fox-character {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #FF9A9E, #FFAFBD);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 20px rgba(255, 154, 158, 0.6);
  animation: bounce 2s ease-in-out infinite;
  border: 3px solid white;
  position: relative;
  overflow: hidden;
}

.fox-character::after {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 60%);
  opacity: 0.6;
}

.fox-emoji {
  font-size: 3rem;
  position: relative;
  z-index: 2;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-text {
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.25rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .fox-assistant {
    bottom: 20px;
    right: 20px;
  }

  .fox-message {
    max-width: 200px;
    font-size: 0.9rem;
  }

  .fox-character {
    width: 60px;
    height: 60px;
  }

  .fox-emoji {
    font-size: 2rem;
  }
}
</style>
