@echo off
echo 正在运行AI绘本创作后台管理系统测试...

echo 1. 运行服务层单元测试
cd src/service
npm test -- src/modules/storybook/storybook.service.spec.ts

echo 2. 运行控制器单元测试
npm test -- src/modules/storybook/storybook.controller.spec.ts

echo 3. 运行管理员控制器单元测试
npm test -- src/modules/storybook/storybook-admin.controller.spec.ts

echo 4. 运行内容安全测试
npm test -- src/modules/storybook/content-safety.spec.ts

echo 5. 运行图像管理测试
npm test -- src/modules/storybook/image-management.spec.ts

echo 6. 运行端到端测试
npm run test:e2e -- test/storybook.e2e-spec.ts

echo 所有测试已完成！
