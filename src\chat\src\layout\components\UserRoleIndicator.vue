<script setup lang="ts">
import { computed, ref, h } from 'vue';
import { useAuthStore } from '@/store';
import { useRouter, useRoute } from 'vue-router';
import { 
  NAvatar, 
  NButton, 
  NDropdown, 
  NIcon, 
  NTag, 
  NTooltip,
  useMessage 
} from 'naive-ui';
import {
  PersonOutline,
  SchoolOutline,
  SwapHorizontalOutline,
  LogOutOutline,
  SettingsOutline
} from '@vicons/ionicons5';

const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const message = useMessage();

// 用户角色检测逻辑（与RoleBasedNav保持一致）
const userRole = computed(() => {
  const userInfo = authStore.userInfo;
  // 优先使用store中的角色信息
  if (userInfo?.role && (userInfo.role === 'teacher' || userInfo.role === 'student')) {
    return userInfo.role;
  }
  // 根据当前路径推断角色
  if (route.path.includes('/chat') || route.path.includes('/teacher')) {
    return 'teacher';
  } else if (route.path.includes('/storybook') || route.path.includes('/student')) {
    return 'student';  
  }
  return 'student';
});

// 用户信息
const userInfo = computed(() => authStore.userInfo);
const isLogin = computed(() => authStore.isLogin);

// 角色信息配置
const roleConfig = computed(() => {
  const config = {
    teacher: {
      name: '老师',
      description: 'AI教学助手',
      color: '#2080f0',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      textColor: 'text-blue-700 dark:text-blue-300',
      icon: SchoolOutline,
      defaultPath: '/teacher/chat'
    },
    student: {
      name: '学生',
      description: 'AI创作伙伴',
      color: '#18a058',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      textColor: 'text-green-700 dark:text-green-300',
      icon: PersonOutline,
      defaultPath: '/student/storybook'
    }
  };
  const role = userRole.value as keyof typeof config;
  return config[role] || config.student; // 默认返回学生配置，防止undefined
});

// 下拉菜单选项
const dropdownOptions = computed(() => [
  {
    key: 'switch-role',
    label: '切换角色',
    icon: () => h(NIcon, { size: 16 }, { default: () => h(SwapHorizontalOutline) })
  },
  {
    key: 'divider-1',
    type: 'divider'
  },
  {
    key: 'settings',
    label: '个人设置',
    icon: () => h(NIcon, { size: 16 }, { default: () => h(SettingsOutline) })
  },
  {
    key: 'logout',
    label: '退出登录',
    icon: () => h(NIcon, { size: 16 }, { default: () => h(LogOutOutline) })
  }
]);

// 切换角色
const switchRole = () => {
  const newRole = userRole.value === 'teacher' ? 'student' : 'teacher';
  const targetPath = newRole === 'teacher' ? '/teacher' : '/student';
  
  // 更新store中的用户角色信息
  authStore.setUserRole(newRole);
  
  message.success(`已切换为${newRole === 'teacher' ? '老师' : '学生'}模式`);
  router.push(targetPath);
};

// 处理下拉菜单点击
const handleDropdownSelect = (key: string) => {
  switch (key) {
    case 'switch-role':
      switchRole();
      break;
    case 'settings':
      router.push('/user-center');
      break;
    case 'logout':
      authStore.logout();
      message.success('已退出登录');
      break;
  }
};

// 获取用户头像
const userAvatar = computed(() => {
  return userInfo.value?.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${userInfo.value?.username || '用户'}`;
});

// 获取用户显示名称
const displayName = computed(() => {
  return userInfo.value?.username || userInfo.value?.nickname || '用户';
});
</script>

<template>
  <div class="user-role-indicator">
    <div v-if="!isLogin" class="flex items-center space-x-2 px-3 py-2">
      <NButton 
        type="primary" 
        size="small"
        @click="authStore.setLoginDialog(true)"
      >
        登录
      </NButton>
    </div>

    <div v-else class="flex items-center space-x-3 px-3 py-2">
      <!-- 角色标识 -->
      <NTooltip trigger="hover">
        <template #trigger>
          <NTag 
            :type="userRole === 'teacher' ? 'info' : 'success'" 
            size="small"
            :class="roleConfig.bgColor"
          >
            <template #icon>
              <NIcon :component="roleConfig.icon" />
            </template>
            {{ roleConfig.name }}
          </NTag>
        </template>
        <span>{{ roleConfig.description }}</span>
      </NTooltip>

      <!-- 用户信息下拉菜单 -->
      <NDropdown
        :options="dropdownOptions"
        @select="handleDropdownSelect"
        trigger="click"
        placement="bottom-end"
      >
        <div class="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 px-2 py-1 rounded-lg transition-colors">
          <NAvatar
            :src="userAvatar"
            :size="32"
            class="border-2"
            :style="{ borderColor: roleConfig.color }"
          />
          <div class="hidden sm:block">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ displayName }}
            </div>
            <div class="text-xs" :class="roleConfig.textColor">
              {{ roleConfig.description }}
            </div>
          </div>
          <NIcon 
            size="12" 
            class="text-gray-400 dark:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </NIcon>
        </div>
      </NDropdown>

      <!-- 快速角色切换按钮（移动端） -->
      <div class="sm:hidden">
        <NTooltip trigger="hover">
          <template #trigger>
            <NButton
              circle
              quaternary
              @click="switchRole"
              size="small"
            >
              <template #icon>
                <NIcon :component="SwapHorizontalOutline" />
              </template>
            </NButton>
          </template>
          <span>切换到{{ userRole === 'teacher' ? '学生' : '老师' }}模式</span>
        </NTooltip>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.user-role-indicator {
  @apply flex items-center justify-end;
  
  .n-avatar {
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
  
  .n-tag {
    @apply font-medium;
    
    &.bg-blue-50 {
      @apply border-blue-200 dark:border-blue-700;
    }
    
    &.bg-green-50 {
      @apply border-green-200 dark:border-green-700;
    }
  }
  
  .n-dropdown {
    @apply rounded-lg;
  }
}

// 响应式优化 - TailwindCSS通过类名自动处理响应式布局
</style>