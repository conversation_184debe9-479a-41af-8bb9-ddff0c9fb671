import { useAuthStoreWithout } from '@/store/modules/auth';
import type { Router } from 'vue-router';

export function setupPageGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    window.$loadingBar?.start();

    // 如果是分享页面，直接放行，使用游客身份访问
    if (to.name === 'ShareView' || to.path.includes('/share')) {
      console.log('分享页面路由，直接放行, 路径:', to.path, '参数:', to.query);
      // 仍然直接放行，但在前端请求中添加指纹信息
      next();
      return; // 添加return确保不会继续执行后面的代码
    }

    const authStore = useAuthStoreWithout();
    if (!authStore.userInfo.username) {
      try {
        authStore.token && (await authStore.getUserInfo());
        if (authStore.globalConfigLoading) {
          let domain = `${window.location.protocol}//${window.location.hostname}`;
          if (window.location.port) domain += `:${window.location.port}`;
          await authStore.getglobalConfig(domain);
          if (authStore.globalConfig.clientHomePath) {
            next({ path: authStore.globalConfig.clientHomePath });
            return;
          }
        }

        if (to.path === '/500') {
          next({ path: '/' });
        } else {
          next();
        }
      } catch (error) {
        if (to.path === '/500') {
          next({ path: '/' });
        } else {
          next();
        }
      }
    } else {
      const clientMenuList = authStore.globalConfig?.clientMenuList;
      const openMenuList = clientMenuList ? JSON.parse(clientMenuList) : [];
      if (openMenuList.length && !openMenuList.includes(to.name)) {
        if (
          authStore.globalConfig.clientHomePath &&
          authStore.globalConfig.clientHomePath !== ''
        ) {
          next({ path: authStore.globalConfig.clientHomePath });
          return;
        }
      }

      next();
    }
  });

  router.afterEach((to: any) => {
    window.$loadingBar?.finish();
  });
}
