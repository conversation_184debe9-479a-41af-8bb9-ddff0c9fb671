<script setup lang="ts">

import { SvgIcon } from '@/components/common';

import { t } from '@/locales';

import {
  SunOne,
} from '@icon-park/vue-next';

import { NLayoutSider } from 'naive-ui';
import type { CSSProperties } from 'vue';
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import List from './List.vue';
import Plugin from './Plugin.vue';

import { useBasicLayout } from '@/hooks/useBasicLayout';
import {
  useAppStore,
  useAuthStore,
  useChatStore,
  useGlobalStoreWithOut,
} from '@/store';
const useGlobalStore = useGlobalStoreWithOut();
const router = useRouter();
const appStore = useAppStore();
const chatStore = useChatStore();
const authStore = useAuthStore();


const darkMode = computed(() => appStore.theme === 'dark');

const { isMobile } = useBasicLayout();
const siteName = authStore.globalConfig?.siteName || 'AIWeb';
const pluginFirst = computed(
  () => Number(authStore.globalConfig.pluginFirst) === 1
);
const isHidePlugin = computed(
  () => Number(authStore.globalConfig.isHidePlugin) === 1
);

// const addLoading = ref(false);

const collapsed = computed(() => appStore.siderCollapsed);

const activeSideOption = ref(
  !pluginFirst.value || isHidePlugin.value ? 'chat' : 'plugin'
);

const globaelConfig = computed(() => authStore.globalConfig);

const isSetBeian = computed(
  () => globaelConfig.value?.companyName && globaelConfig.value?.filingNumber
);







function checkMode() {
  const mode = darkMode.value ? 'light' : 'dark';
  appStore.setTheme(mode);
}

function handleUpdateCollapsed() {
  appStore.setSiderCollapsed(!collapsed.value);
}





function goToUserCenter() {
  // 使用弹窗而不是页面跳转
  useGlobalStore.updateUserCenterDialog(true);
  if (isMobile.value) {
    appStore.setSiderCollapsed(true);
  }
}

function goToSettings() {
  // 根据当前路径判断跳转到对应的设置页面
  const currentPath = router.currentRoute.value.path;
  let settingsPath = '/teacher/settings'; // 默认教师设置

  if (currentPath.includes('/student')) {
    settingsPath = '/student/settings';
  } else if (currentPath.includes('/teacher')) {
    settingsPath = '/teacher/settings';
  }

  router.push(settingsPath);

  if (isMobile.value) {
    appStore.setSiderCollapsed(true);
  }
}

const getMobileClass = computed<CSSProperties>(() => {
  if (isMobile.value) {
    return {
      position: collapsed.value ? 'static' : 'fixed',
      zIndex: 50,
      left: 0,
      top: 0,
      height: '100%',
    };
  }
  return {};
});

const mobileSafeArea = computed(() => {
  if (isMobile.value) {
    return {
      paddingBottom: 'env(safe-area-inset-bottom)',
    };
  }
  return {};
});

watch(
  isMobile,
  (val) => {
    appStore.setSiderCollapsed(val);
  },
  {
    immediate: true,
    flush: 'post',
  }
);


</script>

<template>
  <div>
    <NLayoutSider
      :collapsed="collapsed"
      :collapsed-width="0"
      :width="260"
      collapse-mode="transform"
      position="static"
      :bordered="false"
      :style="[getMobileClass, { 'display': collapsed ? 'none' : 'block' }]"
      @update-collapsed="handleUpdateCollapsed"
    >
      <div
        class="flex flex-col h-full w-full bg-white dark:bg-gray-800 select-none transition-all duration-300 ds-shadow-md rounded-lg overflow-hidden border border-gray-100 dark:border-gray-700 backdrop-blur-sm mr-0"
        :style="mobileSafeArea"
      >
        <main class="flex flex-col h-full flex-1 overflow-hidden flex-shrink-0">
          <!-- 豆包风格顶部区域 -->
          <div
            class="flex bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 w-full justify-between items-center px-4 py-3 border-b border-gray-100 dark:border-gray-700 rounded-t-lg shadow-sm"
          >
            <div class="flex items-center gap-3">
              <!-- 标题 -->
              <div class="font-medium text-gray-800 dark:text-gray-200 text-shadow-sm">{{ siteName }}</div>
            </div>

            <!-- 右侧按钮组 -->
            <div class="flex items-center gap-2">
              <!-- 主题切换按钮 -->
              <button
                @click="checkMode"
                class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
                title="切换主题"
              >
                <SunOne v-if="!darkMode" theme="outline" size="18" class="text-gray-600 dark:text-gray-300" />
                <SunOne v-else theme="filled" size="18" class="text-gray-600 dark:text-gray-300" />
              </button>

              <!-- 用户中心按钮 -->
              <button
                @click="goToUserCenter"
                class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
                title="个人中心"
              >
                <SvgIcon icon="ri:user-3-line" class="text-gray-600 dark:text-gray-300" />
              </button>

              <!-- 设置按钮 -->
              <button
                @click="goToSettings"
                class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 hover-float"
                title="设置中心"
              >
                <SvgIcon icon="ri:settings-3-line" class="text-gray-600 dark:text-gray-300" />
              </button>
            </div>
          </div>

          <div class="flex-1 min-h-0 overflow-hidden w-full flex flex-col">
            <!-- 豆包风格的侧边栏内容 -->
            <List v-if="activeSideOption === 'chat'" />
            <Plugin v-if="activeSideOption === 'plugin'" />
          </div>



          <!-- 备案信息 -->
          <div
            v-if="isSetBeian && isMobile"
            class="w-full flex justify-center items-center pb-3 text-xs text-gray-500"
          >
            版权所有 © {{ globaelConfig?.companyName }}
            <a
              class="ml-2 transition-all text-gray-500 hover:text-gray-600 dark:hover:text-gray-400"
              href="https://beian.miit.gov.cn"
              target="_blank"
              >{{ globaelConfig?.filingNumber }}</a
            >
          </div>
        </main>
      </div>
    </NLayoutSider>
    <template v-if="isMobile">
      <div
        v-show="!collapsed"
        class="fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300"
        @click="handleUpdateCollapsed"
      />
    </template>

  </div>
</template>
