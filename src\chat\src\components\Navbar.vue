<template>
  <header class="navbar">
    <div class="navbar-container">
      <div class="logo-container">
        <img :src="logoPath" alt="Logo" class="logo" />
        <h1 class="site-name">{{ siteName }}</h1>
      </div>
      <nav class="nav-links">
        <a href="#features" class="nav-link">创作类型</a>
        <a href="#community" class="nav-link">社区作品</a>
        <a href="#about" class="nav-link">关于我们</a>
      </nav>
      <div class="nav-actions">
        <n-button v-if="!isLogin" @click="handleLogin" class="login-btn">
          登录 / 注册
        </n-button>
        <div v-else class="user-menu">
          <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
            <div class="user-avatar">
              <img v-if="avatar" :src="avatar" alt="用户头像" />
              <div v-else class="avatar-placeholder">{{ userInitial }}</div>
            </div>
          </n-dropdown>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { NButton, NDropdown } from 'naive-ui';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import logo from '@/assets/logo.png';

const useGlobalStore = useGlobalStoreWithOut();

const props = defineProps({
  scrolled: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const authStore = useAuthStore();
const isLogin = computed(() => authStore.isLogin);
const avatar = computed(() => authStore.userInfo.avatar);
const siteName = computed(() => authStore.globalConfig?.siteName || 'DeepCreate');
const logoPath = computed(() => authStore.globalConfig.clientLogoPath || logo);

const userInitial = computed(() => {
  const username = authStore.userInfo.username || '';
  return username.charAt(0).toUpperCase();
});

// 用户菜单选项
const userMenuOptions = computed(() => [
  {
    label: '个人中心',
    key: 'user-center'
  },
  {
    label: '我的作品',
    key: 'my-works'
  },
  {
    label: '退出登录',
    key: 'logout'
  }
]);

// 处理登录按钮点击
function handleLogin() {
  authStore.setLoginDialog(true);
}

// 处理用户菜单选择
function handleUserMenuSelect(key) {
  switch (key) {
    case 'user-center':
      // 使用弹窗而不是页面跳转
      useGlobalStore.updateUserCenterDialog(true);
      break;
    case 'my-works':
      router.push('/works');
      break;
    case 'logout':
      authStore.logOut();
      break;
  }
}
</script>

<style scoped>
.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  transition: all 0.3s ease;
}

.dark .navbar {
  background-color: rgba(31, 41, 55, 0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  height: 2rem;
  width: 2rem;
  object-fit: contain;
}

.site-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900);
}

.dark .site-name {
  color: var(--color-gray-100);
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600);
  text-decoration: none;
  transition: color 0.2s ease;
  padding: 0.5rem 0;
  position: relative;
}

.dark .nav-link {
  color: var(--color-gray-400);
}

.nav-link:hover {
  color: var(--color-gray-900);
}

.dark .nav-link:hover {
  color: var(--color-gray-100);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary-500);
  transition: width 0.2s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
}

.login-btn {
  font-weight: 500;
}

.user-avatar {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid var(--color-primary-500);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary-500);
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
}
</style>
