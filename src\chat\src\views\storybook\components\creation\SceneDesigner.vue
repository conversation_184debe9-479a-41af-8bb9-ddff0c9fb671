<template>
  <div class="scene-designer">
    <h3 class="step-title">设计故事场景</h3>
    <p class="step-description">创建故事中的重要场景</p>

    <SceneBuilder
      v-if="scenes.length > 0"
      :scene="scenes[activeSceneIndex]"
      :characters="getCharacters()"
      :scenes="scenes"
      :currentIndex="activeSceneIndex"
      @update:scene="updateScene"
      @select-scene="selectScene"
      @add-scene="addNewScene"
    />

    <div v-else class="empty-scenes">
      <div class="empty-icon">🏞️</div>
      <p class="empty-text">还没有场景，点击"添加场景"开始创建</p>
      <button class="add-scene-btn" @click="addNewScene">添加场景</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import SceneBuilder from './SceneBuilder.vue';

const props = defineProps({
  projectData: Object
});

// 确保场景数据结构存在
onMounted(() => {
  if (!props.projectData.outline) {
    props.projectData.outline = {};
  }
  if (!props.projectData.outline.scenes) {
    props.projectData.outline.scenes = [];
  }

  // 确保所有场景都有characters数组
  ensureSceneCharacters();

  // 如果没有场景，自动添加一个
  if (props.projectData.outline.scenes.length === 0) {
    addNewScene();
  }
});

const scenes = computed(() => props.projectData.outline.scenes);
const activeSceneIndex = ref(0);

const addNewScene = () => {
  const newScene = {
    id: Date.now(),
    title: `场景 ${props.projectData.outline.scenes.length + 1}`,
    setting: '',
    description: '',
    action: '',
    characters: [],
    image: ''
  };

  props.projectData.outline.scenes.push(newScene);
  activeSceneIndex.value = props.projectData.outline.scenes.length - 1;
};

// 确保所有场景都有characters数组
const ensureSceneCharacters = () => {
  if (props.projectData.outline && props.projectData.outline.scenes) {
    props.projectData.outline.scenes.forEach(scene => {
      if (!scene.characters) {
        scene.characters = [];
      }
    });
  }
};

const updateScene = (updatedScene) => {
  props.projectData.outline.scenes[activeSceneIndex.value] = updatedScene;
};

const selectScene = (index) => {
  activeSceneIndex.value = index;
};

const getCharacters = () => {
  if (!props.projectData.outline.characters) {
    return [];
  }

  // 将角色对象转换为数组
  return Object.values(props.projectData.outline.characters)
    .filter(char => char.name) // 只返回有名字的角色
    .map(char => ({
      id: char.id,
      name: char.name,
      type: char.characterType,
      image: char.image
    }));
};
</script>

<style scoped>
.scene-designer {
  padding: 0.5rem 0;
  position: relative;
}

.scene-designer::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 5px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  border-radius: 5px;
  opacity: 0.3;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.25rem;
  text-align: center;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
  left: 50%;
  transform: translateX(-50%);
}

.dark .step-title {
  background: linear-gradient(90deg, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.step-description {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 1rem;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dark .step-description {
  color: #94a3b8;
}



.empty-scenes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 2px dashed #e2e8f0;
  position: relative;
  overflow: hidden;
}

.empty-scenes::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #FF9A9E, #FAD0C4, #FFC3A0, #FFAFBD);
  opacity: 0.3;
}

.dark .empty-scenes {
  background-color: #1e293b;
  border-color: #334155;
}

.empty-icon {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  color: #94a3b8;
  animation: float-scene 3s ease-in-out infinite;
}

@keyframes float-scene {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.empty-text {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 1.5rem;
  text-align: center;
  max-width: 400px;
}

.dark .empty-text {
  color: #94a3b8;
}

.add-scene-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  background-color: #3b82f6;
  color: white;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  border: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
  position: relative;
  overflow: hidden;
}

.add-scene-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.dark .add-scene-btn {
  background-color: #60a5fa;
  color: #1e293b;
  box-shadow: 0 4px 10px rgba(96, 165, 250, 0.3);
}

.add-scene-btn:hover {
  background-color: #2563eb;
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.add-scene-btn:hover::before {
  left: 100%;
}

.dark .add-scene-btn:hover {
  background-color: #93c5fd;
  box-shadow: 0 8px 20px rgba(96, 165, 250, 0.4);
}

@media (max-width: 768px) {
  .step-title {
    font-size: 1.75rem;
  }

  .empty-icon {
    font-size: 4rem;
  }

  .add-scene-btn {
    padding: 0.85rem 1.75rem;
  }
}
</style>
