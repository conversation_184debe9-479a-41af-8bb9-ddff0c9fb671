<script setup lang="ts">
import { fetchUpdateInfoAPI } from '@/api';
import { fetchSyncVisitorDataAPI, fetchVisitorCountAPI } from '@/api/balance';
import type { ResData } from '@/api/types';
import defaultAvatar from '@/assets/avatar.png';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import {
  NAvatar,
  NButton,
  NCard,
  NGi,
  NGrid,
  NInput,
  NSkeleton,
  NSpace,
  useMessage,
} from 'naive-ui';
import { computed, onMounted, ref } from 'vue';

const useGlobalStore = useGlobalStoreWithOut();
const authStore = useAuthStore();
const ms = useMessage();
const visitorCount = ref(0);

const isUseWxLogin = computed(() => authStore.globalConfig?.isUseWxLogin);
const wechatRegisterStatus = computed(
  () => Number(authStore.globalConfig.wechatRegisterStatus) === 1
);
const isBindWx = computed(() => authStore.userInfo.isBindWx);
// 始终使用默认头像
const avatar = ref(defaultAvatar);
const username = ref(authStore.userInfo.username ?? t('usercenter.notLoggedIn'));
const email = computed(() => authStore.userInfo.email || '');

const loading = ref(true);
const btnDisabled = ref(false);
const { isMobile } = useBasicLayout();

async function getVisitorCount() {
  const res: ResData = await fetchVisitorCountAPI();
  visitorCount.value = res.data || 0;
}

async function syncVisitorData() {
  const res: ResData = await fetchSyncVisitorDataAPI();
  if (res.success) {
    ms.success(t('usercenter.syncComplete'));
  }
  getVisitorCount();
}

async function updateUserInfo(options: {
  username?: string;
}) {
  try {
    btnDisabled.value = true;
    const res: ResData = await fetchUpdateInfoAPI(options);
    btnDisabled.value = false;
    if (!res.success) return ms.error(res.message);
    ms.success(t('common.updateUserSuccess'));
    authStore.getUserInfo();
  } catch (error) {
    btnDisabled.value = false;
  }
}

onMounted(() => {
  getVisitorCount();
  setTimeout(() => {
    loading.value = false;
  }, 500);
});
</script>

<template>
  <div class="profile-container">
    <!-- 用户基本信息卡片 -->
    <div class="user-info-card">
      <div class="flex items-center justify-center flex-col md:flex-row md:justify-start gap-6 p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-750 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
        <NAvatar
          :size="80"
          :src="avatar"
          :fallback-src="defaultAvatar"
          class="border-2 border-white dark:border-gray-700 shadow-md hover-float transition-all duration-300"
        />
        <div class="flex flex-col items-center md:items-start">
          <div class="text-lg font-medium text-gray-800 dark:text-gray-200">{{ username }}</div>
          <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ email || '邮箱未设置' }}</div>
        </div>
      </div>
    </div>

    <!-- 基本设置卡片 -->
    <NCard class="mt-4">
      <template #header>
        <div class="text-base font-medium">用户基本设置</div>
      </template>
      <NSpace v-if="loading" vertical>
        <NSkeleton height="40px" size="medium" />
        <NSkeleton height="40px" size="medium" />
      </NSpace>
      <template v-else>
        <NGrid :x-gap="12" :cols="1">
          <NGi>
            <!-- 移除头像上传功能 -->
            <div class="flex items-center space-x-4 mt-5">
              <span class="flex-shrink-0 w-[80px] text-gray-600 dark:text-gray-400">用户名</span>
              <div class="flex-1">
                <NInput
                  v-model:value="username"
                  placeholder="请输入用户名"
                  maxlength="12"
                  show-count
                  clearable
                  class="hover-input"
                />
              </div>
              <NButton
                size="small"
                type="primary"
                :disabled="btnDisabled"
                class="hover-float transition-all duration-300"
                @click="updateUserInfo({ username })"
              >
                更新
              </NButton>
            </div>
          </NGi>
        </NGrid>
      </template>
    </NCard>

    <!-- 账号设置卡片 -->
    <NCard v-if="isUseWxLogin && wechatRegisterStatus || visitorCount > 0" class="mt-4">
      <template #header>
        <div class="text-base font-medium">账号设置</div>
      </template>
      <div class="space-y-4">
        <!-- 微信绑定 -->
        <div v-if="isUseWxLogin && wechatRegisterStatus" class="flex items-center justify-between">
          <span class="text-gray-600 dark:text-gray-400">绑定微信</span>
          <div>
            <NButton
              v-if="!isBindWx"
              size="small"
              type="primary"
              @click="useGlobalStore.updateBindwxDialog(true)"
              class="hover-float transition-all duration-300"
            >
              绑定
            </NButton>
            <span v-else class="text-green-500">已绑定微信</span>
          </div>
        </div>

        <!-- 游客数据同步 -->
        <div v-if="visitorCount > 0" class="flex items-center justify-between">
          <span class="text-gray-600 dark:text-gray-400">游客数据</span>
          <div class="flex items-center">
            <span class="mr-2 text-sm text-gray-500">{{ visitorCount }} 条记录</span>
            <NButton
              size="small"
              type="primary"
              @click="syncVisitorData"
              class="hover-float transition-all duration-300"
            >
              同步游客数据
            </NButton>
          </div>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 8px 0;
}

.hover-input:hover {
  border-color: var(--primary-color);
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
