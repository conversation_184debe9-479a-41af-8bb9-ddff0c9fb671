/* 笔触大小控制 */
.pen-size-control {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: white;
  padding: 0.75rem;
  border-radius: 0.75rem;
  border: 2px solid #e2e8f0;
}

.dark .pen-size-control {
  background-color: #334155;
  border-color: #475569;
}

.pen-size-label {
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
}

.dark .pen-size-label {
  color: #e2e8f0;
}

.pen-size-options {
  display: flex;
  gap: 0.5rem;
}

.size-option {
  width: 3rem;
  height: 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background-color: #f8fafc;
  color: #334155;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .size-option {
  background-color: #1e293b;
  border-color: #475569;
  color: #e2e8f0;
}

.size-option:hover {
  background-color: #eff6ff;
  border-color: #93c5fd;
}

.dark .size-option:hover {
  background-color: #1e40af;
  border-color: #60a5fa;
}

.size-option.active {
  background-color: #3b82f6;
  border-color: #2563eb;
  color: white;
}

.dark .size-option.active {
  background-color: #2563eb;
  border-color: #1d4ed8;
}

.pen-size-preview {
  background-color: #3b82f6;
  border-radius: 50%;
  margin-left: 0.5rem;
}

/* 右侧面板 */
.editor-right-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow-y: auto;
}

/* 常用修改选项 */
.common-edits {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 2px solid #e2e8f0;
}

.dark .common-edits {
  background-color: #1e293b;
  border-color: #475569;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title::before {
  content: '✨';
}

.dark .section-title {
  color: #e2e8f0;
}

.edit-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.75rem;
}

.edit-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 0.5rem;
}

.dark .edit-option {
  background-color: #334155;
  border-color: #475569;
}

.edit-option:hover {
  background-color: #eff6ff;
  border-color: #93c5fd;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

.dark .edit-option:hover {
  background-color: #1e40af;
  border-color: #60a5fa;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.option-icon {
  font-size: 1.75rem;
}

.option-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #334155;
  text-align: center;
}

.dark .option-text {
  color: #e2e8f0;
}

/* 提示词输入 */
.prompt-container {
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 2px solid #e2e8f0;
}

.dark .prompt-container {
  background-color: #1e293b;
  border-color: #475569;
}

.prompt-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.prompt-icon {
  font-size: 1.25rem;
}

.dark .prompt-label {
  color: #e2e8f0;
}

.prompt-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  font-size: 1rem;
  color: #1e293b;
  background-color: white;
  resize: vertical;
  min-height: 5rem;
  font-family: inherit;
  margin-bottom: 0.75rem;
}

.dark .prompt-input {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.prompt-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.dark .prompt-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
}

.prompt-examples {
  margin-top: 0.75rem;
}

.example-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.dark .example-title {
  color: #94a3b8;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.example-tag {
  display: inline-block;
  padding: 0.5rem 0.75rem;
  background-color: #f1f5f9;
  border-radius: 1rem;
  font-size: 0.875rem;
  color: #475569;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.example-tag:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

.dark .example-tag {
  background-color: #334155;
  color: #cbd5e1;
  border-color: #475569;
}

.dark .example-tag:hover {
  background-color: #475569;
}
