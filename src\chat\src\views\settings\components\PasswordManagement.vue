<template>
  <div class="password-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">密码管理</h1>
      <p class="page-description">修改您的登录密码和安全设置</p>
    </div>

    <!-- 复用现有的密码组件 -->
    <div class="password-wrapper">
      <Password />
    </div>
  </div>
</template>

<script setup lang="ts">
import Password from '@/components/common/UserCenter/Password.vue';
</script>

<style scoped>
.password-management {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.password-wrapper {
  @apply w-full;
}

/* 覆盖内部组件样式以适应新的布局 */
.password-wrapper :deep(.password-container) {
  @apply space-y-6;
}
</style>
