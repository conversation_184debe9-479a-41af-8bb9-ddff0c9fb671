import { get, post } from '@/utils/request';

/**
 * 角色相关API模块
 */
export default {
  /**
   * 获取我的角色列表
   * @returns 角色列表
   */
  getMyCharacters: () => get({
    url: 'storybook/character/mine',
    params: {}
  }),

  /**
   * 获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  getCharacterDetail: (id) => get({
    url: `storybook/character/${id}`,
    params: {}
  }),

  /**
   * 创建角色
   * @param data 角色数据
   * @returns 创建的角色
   */
  createCharacter: (data) => post({
    url: 'storybook/character',
    data
  }),

  /**
   * 更新角色
   * @param id 角色ID
   * @param data 角色数据
   * @returns 更新后的角色
   */
  updateCharacter: (id, data) => post({
    url: `storybook/character/${id}`,
    method: 'PUT',
    data
  }),

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 删除结果
   */
  deleteCharacter: (id) => {
    // 使用原生fetch API发送DELETE请求，确保使用正确的HTTP方法
    const apiUrl = import.meta.env.VITE_GLOB_API_URL || '';
    const url = `${apiUrl}/storybook/character/${id}`;

    return fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'X-Website-Domain': window.location.origin,
        'Fingerprint': window.navigator.userAgent.length.toString()
      }
    }).then(response => {
      if (!response.ok) {
        throw new Error(`删除失败: ${response.status}`);
      }
      return response.json();
    });
  },

  /**
   * 获取角色模板
   * @returns 角色模板列表
   */
  getCharacterTemplates: () => get({
    url: 'storybook/character/templates',
    params: {}
  }),
};
