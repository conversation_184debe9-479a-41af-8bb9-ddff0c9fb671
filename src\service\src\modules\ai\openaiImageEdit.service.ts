import { Injectable, Logger } from '@nestjs/common';
import { GlobalConfigService } from '../../modules/globalConfig/globalConfig.service';
import { ChatLogService } from '../chatLog/chatLog.service';
import { UploadService } from '../upload/upload.service';
import { OpenAIChatService } from './openaiChat.service';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import * as FormData from 'form-data';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as crypto from 'crypto';

@Injectable()
export class OpenAIImageEditService {
  constructor(
    private readonly globalConfigService: GlobalConfigService,
    private readonly chatLogService: ChatLogService,
    private readonly uploadService: UploadService,
    private readonly openAIChatService: OpenAIChatService,
  ) {}

  /**
   * 编辑图像
   * @param inputs 输入参数
   */
  async editImage(inputs) {
    Logger.log(`开始提交 OpenAI 图像编辑任务，模型: ${inputs.model}`, 'OpenAIImageEditService');
    const {
      apiKey,
      model,
      proxyUrl,
      prompt,
      extraParam,
      timeout,
      onSuccess,
      onFailure,
      groupId,
      assistantLogId,
      modelName,
    } = inputs;

    // 获取配置
    const configs = await this.globalConfigService.getConfigs([
      'isConvertToBase64',
    ]);
    const isConvertToBase64 = configs?.isConvertToBase64 || '0';

    // 处理参数
    const size = extraParam?.size || '1024x1024';
    const quality = extraParam?.quality || 'standard';
    const responseFormat = isConvertToBase64 === '1' ? 'b64_json' : 'url';
    const imageUrl = extraParam?.image;
    const maskBase64 = extraParam?.mask;

    let result = {
      answer: '',
      fileInfo: '',
      status: 2,
      drawId: '',
      customId: '',
      progress: '0%'
    };

    // 更新任务状态为处理中
    if (assistantLogId) {
      await this.chatLogService.updateChatLog(assistantLogId, {
        answer: '图像编辑中...',
        progress: '10%',
        status: 2,
      });
    }

    try {
      // 验证参数
      if (!imageUrl) {
        throw new Error('缺少图像参数');
      }

      if (!maskBase64) {
        throw new Error('缺少蒙版参数');
      }

      // 创建临时文件目录
      const tempDir = path.join(os.tmpdir(), 'openai-image-edit');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 验证和处理原始图像
      Logger.log('处理原始图像', 'OpenAIImageEditService');
      let imageBuffer: Buffer;
      let imageFormat = 'png';
      let imagePath: string;
      let maskPath: string;

      try {
        if (imageUrl.startsWith('data:')) {
          // 处理Base64图像
          const matches = imageUrl.match(/^data:image\/([a-zA-Z0-9]+);base64,(.*)$/);
          if (!matches || matches.length !== 3) {
            throw new Error('无效的Base64图像格式');
          }

          imageFormat = matches[1];
          const base64Data = matches[2];
          imageBuffer = Buffer.from(base64Data, 'base64');

          // 验证图像大小
          const sizeInMB = imageBuffer.length / (1024 * 1024);
          if (sizeInMB > 25) {
            throw new Error(`图像大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
          }
        } else {
          // 下载URL图像
          Logger.log(`从URL下载图像: ${imageUrl.substring(0, 50)}...`, 'OpenAIImageEditService');
          const imageResponse = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 30000, // 30秒超时
            maxContentLength: 25 * 1024 * 1024 // 25MB
          });

          imageBuffer = Buffer.from(imageResponse.data);

          // 验证图像大小
          const sizeInMB = imageBuffer.length / (1024 * 1024);
          if (sizeInMB > 25) {
            throw new Error(`图像大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
          }
        }

        // 保存原始图像到临时文件
        const imageFileName = `image-${crypto.randomBytes(8).toString('hex')}.${imageFormat}`;
        imagePath = path.join(tempDir, imageFileName);
        fs.writeFileSync(imagePath, imageBuffer);
        Logger.log(`图像已保存到临时文件: ${imagePath}`, 'OpenAIImageEditService');
      } catch (error) {
        Logger.error(`处理原始图像失败: ${error.message}`, error, 'OpenAIImageEditService');
        throw new Error(`处理原始图像失败: ${error.message}`);
      }

      // 验证和处理蒙版图像
      Logger.log('处理蒙版图像', 'OpenAIImageEditService');
      let maskBuffer: Buffer;

      try {
        if (!maskBase64 || !maskBase64.startsWith('data:image/png;base64,')) {
          throw new Error('蒙版必须是PNG格式的Base64数据');
        }

        const maskData = maskBase64.split(',')[1];
        maskBuffer = Buffer.from(maskData, 'base64');

        // 验证蒙版大小
        const sizeInMB = maskBuffer.length / (1024 * 1024);
        if (sizeInMB > 25) {
          throw new Error(`蒙版大小超过25MB限制 (${sizeInMB.toFixed(2)}MB)`);
        }

        // 保存蒙版到临时文件
        const maskFileName = `mask-${crypto.randomBytes(8).toString('hex')}.png`;
        maskPath = path.join(tempDir, maskFileName);
        fs.writeFileSync(maskPath, maskBuffer);
        Logger.log(`蒙版已保存到临时文件: ${maskPath}`, 'OpenAIImageEditService');
      } catch (error) {
        Logger.error(`处理蒙版图像失败: ${error.message}`, error, 'OpenAIImageEditService');
        throw new Error(`处理蒙版图像失败: ${error.message}`);
      }

      // 创建FormData
      const formData = new FormData();
      formData.append('image', fs.createReadStream(imagePath));
      formData.append('mask', fs.createReadStream(maskPath));
      formData.append('prompt', prompt);
      formData.append('model', model);
      formData.append('size', size);
      formData.append('response_format', responseFormat);

      if (quality) {
        formData.append('quality', quality);
      }

      // 构建API请求
      const options: AxiosRequestConfig = {
        method: 'POST',
        url: `${proxyUrl}/v1/images/edits`,
        timeout: timeout,
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${apiKey}`,
        },
        data: formData,
      };

      // 记录请求日志
      Logger.log(
        `正在准备发送请求到 ${options.url}，prompt: ${prompt}, model: ${model}, size: ${size}`,
        'OpenAIImageEditService'
      );

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: '正在编辑图像...',
          progress: '30%',
          status: 2,
        });
      }

      // 发送请求
      const response: AxiosResponse = await axios(options);

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: '图像编辑完成，正在处理结果...',
          progress: '70%',
          status: 2,
        });
      }

      // 处理返回的图像数据
      const images = response.data.data;
      const imageUrls = [];

      // 处理每张生成的图像
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        let imageUrl;

        if (responseFormat === 'b64_json') {
          // 如果是Base64格式，上传到服务器
          try {
            // 使用 Date 对象获取当前日期并格式化为 YYYYMM/DD
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const currentDate = `${year}${month}/${day}`;

            // 上传图像
            const buffer = Buffer.from(image.b64_json, 'base64');
            imageUrl = await this.uploadService.uploadBuffer({
              buffer,
              filename: `openai_edit_${Date.now()}_${i}.png`,
              dir: `images/openai/${currentDate}`,
            });
          } catch (error) {
            Logger.error(`上传图像失败: ${error.message}`, error, 'OpenAIImageEditService');
            throw new Error(`上传图像失败: ${error.message}`);
          }
        } else {
          // 如果是URL格式，上传到服务器以确保永久保存
          try {
            // 使用 Date 对象获取当前日期并格式化为 YYYYMM/DD
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const currentDate = `${year}${month}/${day}`;

            // 上传图像
            imageUrl = await this.uploadService.uploadFileFromUrl({
              url: image.url,
              dir: `images/openai/${currentDate}`,
            });
          } catch (error) {
            Logger.error(`上传图像失败: ${error.message}`, error, 'OpenAIImageEditService');
            // 如果上传失败，使用原始URL
            imageUrl = image.url;
          }
        }

        imageUrls.push(imageUrl);
      }

      // 清理临时文件
      try {
        fs.unlinkSync(imagePath);
        fs.unlinkSync(maskPath);
      } catch (error) {
        Logger.warn(`清理临时文件失败: ${error.message}`, 'OpenAIImageEditService');
      }

      // 生成回复文本
      let replyText;
      try {
        replyText = await this.openAIChatService.chatFree(
          `我已经根据提示词"${prompt}"编辑了图像。请用中文回复，告诉用户图像已经编辑完成。`
        );
      } catch (error) {
        replyText = `已根据您的提示"${prompt}"编辑图像`;
        Logger.error('生成回复文本失败: ', error);
      }

      // 设置结果
      result.answer = replyText;
      result.fileInfo = imageUrls[0]; // 主图像URL
      result.status = 3; // 成功状态
      result.customId = JSON.stringify(imageUrls); // 存储所有图像URL
      result.progress = '100%';

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          fileInfo: result.fileInfo,
          answer: result.answer,
          progress: result.progress,
          status: result.status,
          customId: result.customId,
        });
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      // 处理错误
      result.status = 5; // 失败状态
      result.progress = '0%';

      // 获取错误信息
      const status = error?.response?.status || 500;
      const message = error?.response?.data?.error?.message || error.message || '未知错误';

      Logger.error(`图像编辑失败: ${status} - ${message}`, error, 'OpenAIImageEditService');

      // 根据错误类型设置不同的错误消息
      if (status === 429) {
        result.answer = '当前请求已过载，请稍后再试！';
      } else if (status === 400) {
        if (message.includes('Billing hard limit has been reached')) {
          result.answer = '当前模型key已被封禁、已冻结当前调用Key、尝试重新对话试试吧！';
        } else if (message.includes('image and mask')) {
          result.answer = '图像和蒙版尺寸不匹配，请确保它们具有相同的尺寸。';
        } else {
          result.answer = `请求参数错误: ${message}`;
        }
      } else if (status === 401) {
        result.answer = 'API密钥无效或已过期';
      } else if (status === 500) {
        result.answer = '编辑图像失败，请检查你的提示词是否有非法描述！';
      } else {
        result.answer = `图像编辑失败: ${message}`;
      }

      // 更新任务状态
      if (assistantLogId) {
        await this.chatLogService.updateChatLog(assistantLogId, {
          answer: result.answer,
          status: result.status,
        });
      }

      // 调用失败回调
      if (onFailure) {
        onFailure(result);
      }

      return result;
    }
  }
}
