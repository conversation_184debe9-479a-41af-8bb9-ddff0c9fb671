<template>
  <div class="ai-assistant">
    <NButton @click="showAssistant = true" class="assistant-button">
      <span class="assistant-icon">🧙‍♂️</span>
      <span class="assistant-text">AI故事助手</span>
    </NButton>

    <NModal
      v-model:show="showAssistant"
      preset="card"
      style="width: 90%; max-width: 700px;"
      title="AI故事助手"
      :bordered="false"
      size="huge"
    >
      <div class="assistant-modal">
        <div class="assistant-intro">
          <div class="intro-icon">✨</div>
          <div class="intro-content">
            <h3 class="intro-title">让AI帮你创作故事</h3>
            <p class="intro-text">不知道写什么？选择一个提示，或者输入你自己的想法，AI会帮你创作故事！</p>
          </div>
        </div>

        <div class="quick-prompts">
          <div 
            v-for="(prompt, index) in quickPrompts" 
            :key="index"
            class="prompt-card"
            @click="selectPrompt(prompt)"
          >
            <div class="prompt-icon">{{ prompt.icon }}</div>
            <div class="prompt-text">{{ prompt.text }}</div>
          </div>
        </div>

        <div class="custom-prompt">
          <h4 class="prompt-title">或者，告诉AI你想要什么样的故事</h4>
          <NInput
            v-model:value="customPrompt"
            type="textarea"
            placeholder="例如：一个关于友谊的故事，发生在海底世界..."
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
          <div class="prompt-actions">
            <NButton type="primary" @click="generateStory" :loading="isGenerating" :disabled="!customPrompt.trim()">
              <span class="generate-icon">🪄</span>
              <span>生成故事</span>
            </NButton>
          </div>
        </div>

        <div v-if="generatedStory" class="generated-story">
          <div class="story-header">
            <div class="story-icon">📖</div>
            <h3 class="story-title">AI创作的故事</h3>
          </div>
          <div class="story-content">
            <p v-for="(paragraph, index) in storyParagraphs" :key="index" class="story-paragraph">
              {{ paragraph }}
            </p>
          </div>
          <div class="story-actions">
            <NButton @click="clearStory">清除</NButton>
            <NButton type="primary" @click="applyStory">应用到我的故事</NButton>
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { NButton, NModal, NInput } from 'naive-ui';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  outlineData: any;
}>();

const emit = defineEmits(['update']);

// 状态
const showAssistant = ref(false);
const customPrompt = ref('');
const generatedStory = ref('');
const isGenerating = ref(false);
const authStore = useAuthStore();

// 快速提示选项
const quickPrompts = [
  { 
    icon: '🧚‍♀️', 
    text: '创作一个童话故事', 
    prompt: '请创作一个适合儿童的童话故事，包含魔法元素和一个简单的道德寓意。' 
  },
  { 
    icon: '🐾', 
    text: '动物冒险故事', 
    prompt: '请创作一个关于动物朋友们一起冒险的故事，故事中包含友谊和合作的主题。' 
  },
  { 
    icon: '🚀', 
    text: '太空探险故事', 
    prompt: '请创作一个发生在太空中的冒险故事，主角是一个勇敢的小宇航员。' 
  },
  { 
    icon: '🌊', 
    text: '海底世界故事', 
    prompt: '请创作一个发生在海底世界的故事，主角是一条小鱼，学会了勇气的重要性。' 
  }
];

// 将生成的故事分段显示
const storyParagraphs = computed(() => {
  if (!generatedStory.value) return [];
  return generatedStory.value.split('\n').filter(p => p.trim());
});

// 选择预设提示
const selectPrompt = (prompt) => {
  customPrompt.value = prompt.prompt;
  generateStory();
};

// 生成故事
const generateStory = async () => {
  if (!customPrompt.value.trim() || isGenerating.value) return;
  
  isGenerating.value = true;
  generatedStory.value = '';
  
  try {
    // 构建提示词
    const prompt = `
作为儿童绘本创作助手，请根据以下要求创建一个适合儿童的故事大纲：

${customPrompt.value}

请提供完整的故事结构，包括：
1. 故事开始：介绍主角和背景
2. 中间部分：主角面临的挑战或问题
3. 高潮部分：最紧张或最重要的时刻
4. 结尾：问题如何解决，主角学到了什么

故事应该简单易懂，适合儿童阅读，包含积极的价值观。
`;

    // 创建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: prompt,
      options: {
        groupId: 0
      }
    };

    // 处理流式响应
    params.onDownloadProgress = (progressEvent) => {
      const text = progressEvent.target.responseText;
      if (text) {
        generatedStory.value = text;
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);
    
  } catch (error) {
    console.error('生成故事失败:', error);
    window.$message?.error('生成故事失败，请稍后重试');
  } finally {
    isGenerating.value = false;
  }
};

// 清除生成的故事
const clearStory = () => {
  generatedStory.value = '';
};

// 应用生成的故事到表单
const applyStory = () => {
  if (!generatedStory.value) return;
  
  // 解析生成的故事
  const paragraphs = storyParagraphs.value;
  
  // 简单的解析逻辑，可以根据实际生成的内容格式调整
  let beginning = '';
  let middle = '';
  let climax = '';
  let ending = '';
  
  // 尝试从生成的内容中提取各部分
  for (const paragraph of paragraphs) {
    if (paragraph.includes('开始') || paragraph.includes('背景') || paragraph.toLowerCase().includes('beginning')) {
      beginning += paragraph + '\n';
    } else if (paragraph.includes('中间') || paragraph.includes('挑战') || paragraph.toLowerCase().includes('middle')) {
      middle += paragraph + '\n';
    } else if (paragraph.includes('高潮') || paragraph.includes('紧张') || paragraph.toLowerCase().includes('climax')) {
      climax += paragraph + '\n';
    } else if (paragraph.includes('结尾') || paragraph.includes('解决') || paragraph.toLowerCase().includes('ending')) {
      ending += paragraph + '\n';
    }
  }
  
  // 如果没有成功提取，则使用简单的分段逻辑
  if (!beginning && !middle && !climax && !ending) {
    const totalParagraphs = paragraphs.length;
    if (totalParagraphs >= 4) {
      beginning = paragraphs.slice(0, Math.floor(totalParagraphs * 0.25)).join('\n');
      middle = paragraphs.slice(Math.floor(totalParagraphs * 0.25), Math.floor(totalParagraphs * 0.5)).join('\n');
      climax = paragraphs.slice(Math.floor(totalParagraphs * 0.5), Math.floor(totalParagraphs * 0.75)).join('\n');
      ending = paragraphs.slice(Math.floor(totalParagraphs * 0.75)).join('\n');
    } else if (totalParagraphs === 3) {
      beginning = paragraphs[0];
      middle = paragraphs[1];
      ending = paragraphs[2];
    } else if (totalParagraphs === 2) {
      beginning = paragraphs[0];
      ending = paragraphs[1];
    } else if (totalParagraphs === 1) {
      beginning = paragraphs[0];
    }
  }
  
  // 更新表单数据
  emit('update', {
    beginning: beginning.trim(),
    middle: middle.trim(),
    climax: climax.trim(),
    ending: ending.trim()
  });
  
  // 关闭弹窗
  showAssistant.value = false;
  
  // 显示成功消息
  window.$message?.success('故事已应用到表单');
};
</script>

<style scoped>
.ai-assistant {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.assistant-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.assistant-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(99, 102, 241, 0.4);
}

.assistant-icon {
  font-size: 1.25rem;
}

.assistant-modal {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.assistant-intro {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: #f0f9ff;
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 0.5rem;
}

.dark .assistant-intro {
  background-color: #0c4a6e;
}

.intro-icon {
  font-size: 2rem;
  color: #3b82f6;
}

.dark .intro-icon {
  color: #60a5fa;
}

.intro-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1e40af;
}

.dark .intro-title {
  color: #93c5fd;
}

.intro-text {
  margin: 0;
  color: #334155;
  font-size: 0.95rem;
}

.dark .intro-text {
  color: #e2e8f0;
}

.quick-prompts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.prompt-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: white;
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .prompt-card {
  background-color: #1e293b;
  border-color: #334155;
}

.prompt-card:hover {
  background-color: #f8fafc;
  border-color: #93c5fd;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.dark .prompt-card:hover {
  background-color: #0f172a;
  border-color: #60a5fa;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.prompt-icon {
  font-size: 1.5rem;
}

.prompt-text {
  font-size: 0.95rem;
  font-weight: 500;
  color: #334155;
}

.dark .prompt-text {
  color: #e2e8f0;
}

.custom-prompt {
  margin-top: 1rem;
}

.prompt-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #334155;
}

.dark .prompt-title {
  color: #e2e8f0;
}

.prompt-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.generate-icon {
  margin-right: 0.5rem;
}

.generated-story {
  margin-top: 1.5rem;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.dark .generated-story {
  background-color: #1e293b;
  border-color: #334155;
}

.story-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.story-icon {
  font-size: 1.5rem;
  color: #3b82f6;
}

.dark .story-icon {
  color: #60a5fa;
}

.story-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #334155;
}

.dark .story-title {
  color: #e2e8f0;
}

.story-content {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.dark .story-content {
  background-color: #0f172a;
  border-color: #334155;
}

.story-paragraph {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  color: #334155;
}

.dark .story-paragraph {
  color: #e2e8f0;
}

.story-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

@media (max-width: 640px) {
  .quick-prompts {
    grid-template-columns: 1fr;
  }
}
</style>
