<template>
  <div class="notice-content">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">网站公告</h1>
      <p class="page-description">查看最新的网站公告和重要通知</p>
    </div>

    <!-- 复用现有的公告组件 -->
    <div class="notice-wrapper">
      <NoticeDialog />
    </div>
  </div>
</template>

<script setup lang="ts">
import NoticeDialog from '@/layout/components/Settings/NoticeDialog.vue';
</script>

<style scoped>
.notice-content {
  @apply space-y-6;
}

.page-header {
  @apply mb-8;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400;
}

.notice-wrapper {
  @apply w-full;
}

/* 覆盖内部组件样式以适应新的布局 */
.notice-wrapper :deep(.notice-container) {
  @apply bg-transparent shadow-none border-0 p-0;
}
</style>
