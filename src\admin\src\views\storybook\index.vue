<template>
  <div>
    <page-header title="绘本列表" />
    <page-main>
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="mb-4">
      <el-form-item label="关键词">
        <el-input v-model="searchForm.keyword" placeholder="标题/描述" clearable />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="全部" clearable>
          <el-option label="草稿" :value="0" />
          <el-option label="已发布" :value="1" />
          <el-option label="审核中" :value="2" />
          <el-option label="已拒绝" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input v-model="searchForm.userId" placeholder="用户ID" clearable />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          :shortcuts="dateShortcuts"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="封面" width="120">
        <template #default="{ row }">
          <el-image
            v-if="row.coverImg"
            :src="row.coverImg"
            fit="cover"
            style="width: 80px; height: 80px; border-radius: 4px"
          />
          <el-empty v-else description="无封面" :image-size="40" />
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
      <el-table-column prop="userId" label="创建用户" width="100" />
      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="公开" width="80">
        <template #default="{ row }">
          <el-tag :type="row.isPublic === 1 ? 'success' : 'info'">
            {{ row.isPublic === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐" width="80">
        <template #default="{ row }">
          <el-tag :type="row.isRecommended === 1 ? 'warning' : 'info'">
            {{ row.isRecommended === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="viewCount" label="浏览量" width="90" />
      <el-table-column prop="likeCount" label="点赞量" width="90" />
      <el-table-column prop="createdAt" label="创建时间" width="180" />
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="viewDetail(row.id)">查看</el-button>
          <el-button
            v-if="row.status === 2"
            size="small"
            type="success"
            @click="handleAudit(row, 1)"
          >通过</el-button>
          <el-button
            v-if="row.status === 2"
            size="small"
            type="danger"
            @click="handleAudit(row, 3)"
          >拒绝</el-button>
          <el-button
            v-if="row.isRecommended === 0 && row.status === 1"
            size="small"
            type="warning"
            @click="handleRecommend(row, 1)"
          >推荐</el-button>
          <el-button
            v-if="row.isRecommended === 1"
            size="small"
            @click="handleRecommend(row, 0)"
          >取消推荐</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 审核拒绝原因对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="拒绝原因" width="500px">
      <el-form :model="rejectForm">
        <el-form-item label="拒绝原因" :label-width="'80px'">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReject">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </page-main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import ApiStorybook from '@/api/modules/storybook';

const router = useRouter();
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);
const dateRange = ref([]);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  userId: '',
  startDate: '',
  endDate: '',
});

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 拒绝对话框
const rejectDialogVisible = ref(false);
const rejectForm = reactive({
  id: 0,
  reason: '',
});
const rejectingStorybook = ref(null);

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '草稿',
    1: '已发布',
    2: '审核中',
    3: '已拒绝',
  };
  return statusMap[status] || '未知';
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger',
  };
  return typeMap[status] || 'info';
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      searchForm.startDate = dateRange.value[0];
      searchForm.endDate = dateRange.value[1];
    } else {
      searchForm.startDate = '';
      searchForm.endDate = '';
    }

    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...searchForm,
    };

    const res = await ApiStorybook.getAllStorybooks(params);
    tableData.value = res.data.items;
    total.value = res.data.total;
  } catch (error) {
    console.error('加载绘本列表失败', error);
    ElMessage.error('加载绘本列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  dateRange.value = [];
  currentPage.value = 1;
  loadData();
};

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  loadData();
};

// 页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 查看详情
const viewDetail = (id) => {
  router.push(`/storybook/detail/${id}`);
};

// 处理审核
const handleAudit = async (row, status) => {
  if (status === 3) {
    // 拒绝需要填写原因
    rejectingStorybook.value = row;
    rejectForm.id = row.id;
    rejectForm.reason = '';
    rejectDialogVisible.value = true;
    return;
  }

  try {
    await ApiStorybook.updateStorybookStatus(row.id, { status });
    ElMessage.success('审核操作成功');
    loadData();
  } catch (error) {
    console.error('审核操作失败', error);
    ElMessage.error('审核操作失败');
  }
};

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.reason) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }

  try {
    await ApiStorybook.updateStorybookStatus(rejectForm.id, {
      status: 3,
      rejectReason: rejectForm.reason
    });
    ElMessage.success('拒绝操作成功');
    rejectDialogVisible.value = false;
    loadData();
  } catch (error) {
    console.error('拒绝操作失败', error);
    ElMessage.error('拒绝操作失败');
  }
};

// 处理推荐
const handleRecommend = async (row, isRecommended) => {
  try {
    await ApiStorybook.setStorybookRecommend(row.id, { isRecommended });
    ElMessage.success(isRecommended === 1 ? '设置推荐成功' : '取消推荐成功');
    loadData();
  } catch (error) {
    console.error('推荐操作失败', error);
    ElMessage.error('推荐操作失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除绘本"${row.title}"吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await ApiStorybook.deleteStorybook(row.id);
        ElMessage.success('删除成功');
        loadData();
      } catch (error) {
        console.error('删除失败', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}
</style>
