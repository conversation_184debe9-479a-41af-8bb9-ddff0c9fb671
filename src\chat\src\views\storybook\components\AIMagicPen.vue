<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AIMagicPenButton from './ai-magic-pen/AIMagicPenButton.vue';
import AIMagicPenDrawer from './ai-magic-pen/AIMagicPenDrawer.vue';

// 定义属性
const props = defineProps<{
  projectData: any;
  currentStep?: number;
  currentPage?: string;
}>();

// 抽屉状态
const showDrawer = ref(false);
const activeTab = ref('magichelper');

// 按钮位置状态
const buttonPosition = ref({ x: 0, y: 0 });

// 初始化按钮位置
onMounted(() => {
  // 从本地存储中获取保存的位置，如果没有则使用默认位置
  const savedPosition = localStorage.getItem('aiMagicPenButtonPosition');
  if (savedPosition) {
    buttonPosition.value = JSON.parse(savedPosition);
  } else {
    // 默认位置：右下角
    buttonPosition.value = { x: window.innerWidth - 150, y: window.innerHeight - 100 };
  }
});

// 打开抽屉
const openDrawer = () => {
  showDrawer.value = true;
};

// 关闭抽屉
const closeDrawer = () => {
  console.log('Closing AI Magic Pen drawer');
  showDrawer.value = false;
};

// 保存按钮位置
const saveButtonPosition = (position) => {
  buttonPosition.value = position;
  localStorage.setItem('aiMagicPenButtonPosition', JSON.stringify(position));
};

// 切换标签页
const switchTab = (tabName) => {
  activeTab.value = tabName;
};
</script>

<template>
  <div class="ai-magic-pen">
    <!-- 可拖动的悬浮按钮 -->
    <AIMagicPenButton
      :position="buttonPosition"
      @update:position="saveButtonPosition"
      @click="openDrawer"
    />

    <!-- 抽屉 -->
    <AIMagicPenDrawer
      v-model:show="showDrawer"
      v-model:active-tab="activeTab"
      :project-data="projectData"
      @close="closeDrawer"
    />
  </div>
</template>

<style scoped>
.ai-magic-pen {
  position: relative;
  z-index: 10001; /* 确保高于绘本阅读器的z-index(9990) */
}
</style>
