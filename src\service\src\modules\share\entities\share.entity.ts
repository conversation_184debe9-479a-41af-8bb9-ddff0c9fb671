import { Column, CreateDateColumn, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class ShareEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 20, unique: true })
  shareCode: string;

  @Column({ type: 'text' })
  htmlContent: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ nullable: true })
  creatorId: number;


}
