<template>
  <div class="scene-builder">
    <div class="builder-layout">
      <!-- 左侧：场景预览区 -->
      <div class="scene-preview-panel">
        <div class="preview-header">
          <h4 class="preview-title">场景预览</h4>
        </div>
        <div class="preview-content">
          <!-- 场景选择器 -->
          <div class="scene-selector">
            <div class="scene-selector-title">选择场景</div>
            <div class="scene-selector-options">
              <div
                v-for="(sceneTab, index) in scenesTabs"
                :key="index"
                class="scene-option"
                :class="{ 'active': currentSceneIndex === index }"
                @click="selectScene(index)"
              >
                <div class="scene-icon">🏞️</div>
                <div class="scene-label">场景 {{ index + 1 }}</div>
              </div>
              <div
                v-if="scenesTabs.length < 6"
                class="scene-option add-scene"
                @click="addNewScene"
              >
                <div class="scene-icon">➕</div>
                <div class="scene-label">添加场景</div>
              </div>
            </div>
          </div>

          <div class="preview-image">
            <img v-if="localScene.image" :src="localScene.image" :alt="localScene.title" />
            <div v-else class="image-placeholder">
              <span class="emoji-icon">🏞️</span>
              <p>点击"魔法生成场景"创建图像</p>
            </div>
          </div>
          <div class="preview-info" v-if="localScene.setting">
            <h3 class="preview-title">{{ getSettingLabel(localScene.setting) }}</h3>
            <div class="preview-characters" v-if="localScene.characters.length > 0">
              <div class="preview-subtitle">场景中的角色：</div>
              <div class="character-chips">
                <span v-for="charId in localScene.characters" :key="charId" class="character-chip">
                  {{ getCharacterName(charId) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：场景信息编辑区 -->
      <div class="scene-options-panel">
        <div class="options-header">
          <h4 class="options-title">场景信息</h4>
        </div>
        <div class="options-content">
          <!-- 场景设置 -->
          <div class="option-group">
            <h4 class="option-title">这个场景发生在哪里？</h4>
            <div class="option-buttons">
              <button
                v-for="setting in sceneSettings"
                :key="setting.value"
                class="option-btn"
                :class="{ active: localScene.setting === setting.value }"
                @click="localScene.setting = setting.value"
              >
                {{ setting.icon }} {{ setting.label }}
              </button>
            </div>
          </div>

          <!-- 场景描述 -->
          <div class="option-group">
            <h4 class="option-title">在这个场景中发生了什么？</h4>
            <textarea
              v-model="localScene.description"
              placeholder="描述场景中发生的事情..."
              class="scene-description"
              rows="3"
            ></textarea>
          </div>

          <!-- 场景角色 -->
          <div class="option-group">
            <h4 class="option-title">谁在这个场景中？</h4>
            <div class="character-selector">
              <div
                v-for="character in characters"
                :key="character.id"
                class="character-chip-select"
                :class="{ selected: isCharacterInScene(character.id) }"
                @click="toggleCharacterInScene(character.id)"
              >
                <span class="character-emoji">{{ getCharacterEmoji(character) }}</span>
                {{ character.name }}
              </div>
            </div>
          </div>

          <!-- 生成场景按钮 -->
          <button class="generate-btn" @click="generateScene">
            ✨ 魔法生成场景
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';

const props = defineProps({
  scene: Object,
  characters: Array,
  scenes: Array,
  currentIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:scene', 'add-scene', 'select-scene']);

// 当前场景索引
const currentSceneIndex = ref(props.currentIndex);

// 场景选项卡数据
const scenesTabs = computed(() => {
  return props.scenes || [];
});

// 创建本地副本以便编辑，确保characters是数组
const localScene = reactive({
  ...props.scene,
  characters: props.scene?.characters || []
});

// 当本地数据变化时，通知父组件
watch(localScene, () => {
  emit('update:scene', {...localScene});
}, { deep: true });

// 选择场景
const selectScene = (index) => {
  currentSceneIndex.value = index;
  emit('select-scene', index);
};

// 添加新场景
const addNewScene = () => {
  emit('add-scene');
};

const sceneSettings = [
  { label: '森林', value: 'forest', icon: '🌳' },
  { label: '学校', value: 'school', icon: '🏫' },
  { label: '家里', value: 'home', icon: '🏠' },
  { label: '公园', value: 'park', icon: '🏞️' },
  { label: '海边', value: 'beach', icon: '🏖️' },
  { label: '山上', value: 'mountain', icon: '⛰️' },
  { label: '城市', value: 'city', icon: '🏙️' },
  { label: '农场', value: 'farm', icon: '🚜' },
  { label: '动物园', value: 'zoo', icon: '🦁' },
  { label: '太空', value: 'space', icon: '🚀' },
  { label: '海底', value: 'underwater', icon: '🐠' },
  { label: '童话城堡', value: 'castle', icon: '🏰' }
];

const isCharacterInScene = (characterId) => {
  return localScene.characters.includes(characterId);
};

const toggleCharacterInScene = (characterId) => {
  if (isCharacterInScene(characterId)) {
    localScene.characters = localScene.characters.filter(id => id !== characterId);
  } else {
    localScene.characters.push(characterId);
  }
};

const getCharacterName = (characterId) => {
  const character = props.characters.find(char => char.id === characterId);
  return character ? character.name : '未知角色';
};

const getCharacterEmoji = (character) => {
  const typeEmojis = {
    boy: '👦',
    girl: '👧',
    animal: '🐶',
    magical: '🧚'
  };
  return typeEmojis[character.type] || '👤';
};

const getSettingLabel = (settingValue) => {
  const setting = sceneSettings.find(s => s.value === settingValue);
  return setting ? `${setting.icon} ${setting.label}` : '';
};

const generateScene = () => {
  if (!localScene.setting) {
    window.$message?.warning('请先选择场景发生的地点');
    return;
  }

  // 这里将来可以接入AI生成功能
  // 现在先用简单的随机生成代替

  // 如果没有描述，生成一个简单的描述
  if (!localScene.description) {
    const settingDescriptions = {
      forest: ['在茂密的森林里，阳光透过树叶洒下斑驳的光影。', '森林里的小路蜿蜒曲折，两旁是高大的树木。', '森林中央有一片小空地，周围开满了五颜六色的野花。'],
      school: ['教室里充满了欢声笑语，窗外是明媚的阳光。', '学校操场上，孩子们正在玩耍和奔跑。', '图书馆里安静祥和，书架上摆满了各种有趣的书籍。'],
      home: ['温馨的客厅里，壁炉燃烧着温暖的火焰。', '厨房里飘着香甜的饼干味道，窗外是美丽的花园。', '卧室里，柔软的床上堆满了毛绒玩具。'],
      park: ['公园里的草地上，孩子们正在放风筝。', '公园的长椅上，可以看到美丽的湖泊和游船。', '公园的游乐场上，有滑梯、秋千和旋转木马。'],
      beach: ['金色的沙滩上，海浪轻轻拍打着岸边。', '沙滩上搭建着五颜六色的遮阳伞，孩子们在堆沙堡。', '海边的礁石上，可以看到远处的灯塔和帆船。'],
      mountain: ['高山之巅，云雾缭绕，远处是连绵的山脉。', '山间的小路上，开满了野花，空气清新。', '山腰上有一个小木屋，周围是茂密的松树林。'],
      city: ['繁华的城市街道上，高楼大厦闪烁着灯光。', '城市广场上，喷泉喷涌而出，周围是购物中心和咖啡店。', '城市公园里，人们在散步、聊天和休息。'],
      farm: ['农场的田野里，金色的麦浪随风摇曳。', '农场的谷仓旁，各种动物悠闲地吃着草。', '农场的果园里，树上挂满了红彤彤的苹果。'],
      zoo: ['动物园里，长颈鹿伸长脖子吃着树叶。', '猴山上，猴子们在树枝间灵活地跳跃。', '企鹅馆里，企鹅们在水中欢快地游泳。'],
      space: ['宇宙飞船上，透过窗户可以看到无数闪烁的星星。', '月球表面，宇航员留下了一串脚印。', '太空站里，宇航员们在失重环境中漂浮。'],
      underwater: ['海底世界里，五颜六色的鱼儿在珊瑚丛中穿梭。', '深海中，神秘的海底城堡闪烁着蓝色的光芒。', '海底洞穴里，藏着闪闪发光的珍珠和宝藏。'],
      castle: ['童话城堡高耸入云，塔尖上飘扬着彩色的旗帜。', '城堡的大厅里，水晶吊灯闪烁着璀璨的光芒。', '城堡的花园里，有迷宫、喷泉和雕像。']
    };

    const descriptions = settingDescriptions[localScene.setting] || settingDescriptions.forest;
    localScene.description = descriptions[Math.floor(Math.random() * descriptions.length)];
  }

  // 生成场景图像URL (使用Pollinations API)
  const setting = sceneSettings.find(s => s.value === localScene.setting);
  const settingLabel = setting ? setting.label : '';
  const characterNames = localScene.characters.map(id => getCharacterName(id)).join(',');

  const prompt = `${settingLabel}场景, ${localScene.description}, ${characterNames ? characterNames + '在场景中,' : ''} 儿童绘本风格, 简单可爱, 明亮色彩`;

  localScene.image = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}`;

  window.$message?.success('场景生成成功！');
};
</script>

<style scoped>
.scene-builder {
  padding: 0;
  background-color: transparent;
  border-radius: 0.75rem;
}

.dark .scene-builder {
  background-color: transparent;
}

.builder-layout {
  display: flex;
  gap: 1.5rem;
  height: 100%;
}

/* 左侧预览面板 */
.scene-preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark .scene-preview-panel {
  background-color: #1e293b;
  border-color: #334155;
}

.preview-header {
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.dark .preview-header {
  background-color: #0f172a;
  border-color: #334155;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  overflow-y: auto;
  gap: 1rem;
}

/* 场景选择器 */
.scene-selector {
  width: 100%;
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
  border: 1px solid #e2e8f0;
}

.dark .scene-selector {
  background-color: #252525;
  border-color: #334155;
}

.scene-selector-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1e293b;
  text-align: center;
  margin-bottom: 1rem;
}

.dark .scene-selector-title {
  color: #e2e8f0;
}

.scene-selector-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.scene-option {
  flex: 1 0 calc(33.333% - 0.5rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border-radius: 0.5rem;
  background-color: #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  min-width: 80px;
}

.dark .scene-option {
  background-color: #334155;
}

.scene-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.scene-option.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .scene-option.active {
  border-color: #60a5fa;
  background-color: #1e40af;
}

.scene-option.add-scene {
  background-color: #ecfdf5;
  color: #10b981;
  border: 2px dashed #10b981;
}

.scene-option.add-scene:hover {
  background-color: #d1fae5;
}

.dark .scene-option.add-scene {
  background-color: #064e3b;
  color: #6ee7b7;
  border-color: #6ee7b7;
}

.dark .scene-option.add-scene:hover {
  background-color: #065f46;
}

.scene-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.scene-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.dark .scene-label {
  color: #e2e8f0;
}

.preview-image {
  width: 100%;
  height: 300px;
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: #f1f5f9;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dark .preview-image {
  background-color: #334155;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.emoji-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.preview-info {
  width: 100%;
  padding: 1rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .preview-info {
  background-color: #252525;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  text-align: center;
}

.dark .preview-title {
  color: #e2e8f0;
}

.preview-subtitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.dark .preview-subtitle {
  color: #94a3b8;
}

.character-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.character-chip {
  background-color: #eff6ff;
  color: #3b82f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.dark .character-chip {
  background-color: #1e40af;
  color: #93c5fd;
}

/* 右侧选项面板 */
.scene-options-panel {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.dark .scene-options-panel {
  background-color: #1e293b;
  border-color: #334155;
}

.options-header {
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.dark .options-header {
  background-color: #0f172a;
  border-color: #334155;
}

.options-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.dark .options-title {
  color: #e2e8f0;
}

.options-content {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
}

.option-group {
  margin-bottom: 1.5rem;
}

.option-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
}

.dark .option-title {
  color: #e2e8f0;
}

.scene-description {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  background-color: white;
  color: #1e293b;
  resize: vertical;
}

.dark .scene-description {
  background-color: #334155;
  border-color: #475569;
  color: #e2e8f0;
}

.option-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.option-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .option-btn {
  background-color: #334155;
  border-color: #475569;
  color: #94a3b8;
}

.option-btn:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.dark .option-btn:hover {
  background-color: #475569;
  color: #e2e8f0;
}

.option-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.dark .option-btn.active {
  background-color: #60a5fa;
  color: #1e293b;
  border-color: #60a5fa;
}

.character-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.character-chip-select {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  background-color: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .character-chip-select {
  background-color: #334155;
  border-color: #475569;
  color: #94a3b8;
}

.character-chip-select:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.dark .character-chip-select:hover {
  background-color: #475569;
  color: #e2e8f0;
}

.character-chip-select.selected {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.dark .character-chip-select.selected {
  background-color: #60a5fa;
  color: #1e293b;
  border-color: #60a5fa;
}

.character-emoji {
  font-size: 1.25rem;
}

.generate-btn {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #8b5cf6;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.dark .generate-btn {
  background-color: #a78bfa;
  color: #1e293b;
}

.generate-btn:hover {
  background-color: #7c3aed;
}

.dark .generate-btn:hover {
  background-color: #c4b5fd;
}

@media (max-width: 768px) {
  .builder-layout {
    flex-direction: column;
    gap: 1rem;
  }

  .preview-image {
    height: 250px;
  }

  .scene-option {
    flex: 1 0 calc(50% - 0.5rem);
  }

  .scene-selector-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .scene-icon {
    font-size: 1.25rem;
  }

  .scene-label {
    font-size: 0.8rem;
  }
}
</style>
