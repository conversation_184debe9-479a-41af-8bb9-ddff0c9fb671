<template>
  <div class="h-full flex flex-col ds-card rounded-lg" role="region" aria-label="代码预览区域">
    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-t-lg">
      <div class="text-lg font-medium flex items-center" id="preview-title">
        <span class="mr-2 text-primary-500 dark:text-primary-400" aria-hidden="true">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </span>
        <span>代码预览</span>
        <span v-if="code" class="ml-2 text-xs text-gray-500 dark:text-gray-400">
          (代码长度: {{ code.length }} 字符)
        </span>
      </div>
      <div class="flex space-x-2" role="toolbar" aria-label="预览工具栏">
        <button
          v-if="code"
          @click="copyCode"
          class="px-4 py-1.5 rounded-md bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-300 hover-float ds-shadow-sm"
          title="复制代码"
          aria-label="复制代码到剪贴板"
        >
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
            </svg>
            <span>复制代码</span>
          </div>
        </button>
      </div>
    </div>

    <div class="flex-1 overflow-hidden">
      <!-- 设备预览区域 -->
      <div class="h-full" aria-labelledby="preview-title">
        <DevicePreview
          v-if="code"
          :code="code"
          ref="devicePreviewRef"
          @update:code="handleCodeUpdate"
          @element-edited="handleElementEdited"
        />
        <div v-else class="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
          <div class="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
            <p class="text-lg font-medium">等待代码生成</p>
            <p class="text-sm mt-2">AI生成代码后将在此处显示预览</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 使用通用反馈提示组件 -->
    <FeedbackToast
      v-model:visible="feedbackVisible"
      :message="feedbackMessage"
      type="success"
      :duration="3000"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { useMessage } from 'naive-ui';
import DevicePreview from './DevicePreview.vue';
import FeedbackToast from '@/components/FeedbackToast.vue';

const props = defineProps<{
  code?: string;
}>();

const emit = defineEmits(['update:code', 'element-edited']);

const message = useMessage();
const devicePreviewRef = ref<InstanceType<typeof DevicePreview> | null>(null);

// 移除应用中心相关逻辑，只保留代码预览功能

// 移除应用数量相关逻辑

// 反馈提示
const feedbackVisible = ref(false);
const feedbackMessage = ref('');

// 显示反馈提示
function showFeedback(message: string) {
  feedbackMessage.value = message;
  feedbackVisible.value = true;
}

// 复制代码到剪贴板
function copyCode() {
  if (!props.code) return;

  navigator.clipboard.writeText(props.code)
    .then(() => {
      showFeedback('代码已复制到剪贴板');
      message.success('代码已复制到剪贴板');
    })
    .catch(err => {
      showFeedback('复制失败，请手动复制');
      message.error('复制失败，请手动复制');
      console.error('复制失败:', err);
    });
}

// 移除切换视图相关逻辑

// 处理代码更新
function handleCodeUpdate(newCode: string) {
  console.log('收到代码更新，长度:', newCode.length);
  emit('update:code', newCode);
  showFeedback('HTML已更新');
}

// 处理元素编辑
function handleElementEdited(data: any) {
  console.log('元素已编辑:', data);
  emit('element-edited', data);
  showFeedback(`已编辑元素: ${data.element}`);
}

// 当代码变化时刷新预览
watch(
  () => props.code,
  (newCode) => {
    console.log('CodePreview检测到代码变化，长度:', newCode?.length || 0);
    if (!newCode || !devicePreviewRef.value) return;

    try {
      // 使用Vue的nextTick确保状态已更新
      nextTick(() => {
        // 刷新预览
        devicePreviewRef.value?.refreshPreview();

        // 对于大型代码块，可能需要更多时间加载
        const codeSize = newCode.length;

        // 根据代码大小动态调整延迟
        const delay = codeSize > 10000 ? 800 : 500;

        // 添加一个延迟刷新，确保内容完全加载
        setTimeout(() => {
          if (devicePreviewRef.value) {
            console.log('延迟刷新预览');
            devicePreviewRef.value.refreshPreview();
          }
        }, delay);
      });
    } catch (error) {
      console.error('CodePreview刷新预览时出错:', error);
    }
  },
  { immediate: true } // 立即执行，处理初始化时的代码
);
</script>

<style scoped>
/* 添加一些基本样式 */
.device-preview-container {
  height: 100%;
  width: 100%;
}
</style>
