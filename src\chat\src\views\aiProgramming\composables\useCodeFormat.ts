// 代码格式化相关功能

/**
 * 提供HTML代码格式化功能
 * @returns 格式化相关的功能
 */
export function useCodeFormat() {
    /**
     * 美化HTML代码
     * 一个简单的HTML格式化实现
     * @param html 原始HTML字符串
     * @returns 格式化后的HTML字符串
     */
    const prettifyHTML = (html: string): string => {
        let formatted = '';
        let indent = '';
        const tab = '  '; // 两个空格作为缩进

        html.split(/>\s*</).forEach(element => {
            if (element.match(/^\/\w/)) {
                // 如果是闭合标签，减少缩进
                indent = indent.substring(tab.length);
            }

            formatted += indent + '<' + element + '>\n';

            if (element.match(/^<?\w[^>]*[^\/]$/) &&
                !element.startsWith('input') &&
                !element.startsWith('img') &&
                !element.startsWith('br')) {
                // 如果是开放标签且不是自闭合标签，增加缩进
                indent += tab;
            }
        });

        return formatted.substring(1, formatted.length - 2);
    };

    return {
        prettifyHTML
    };
} 