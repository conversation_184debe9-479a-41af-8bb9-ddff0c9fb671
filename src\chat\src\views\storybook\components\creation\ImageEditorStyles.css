/* 儿童友好的图像编辑器样式 */

/* 基础样式 */
.image-editor {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.editor-container {
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .editor-container {
  background-color: #1e293b;
  border: 1px solid #334155;
}

/* 编辑器头部 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.dark .editor-header {
  border-color: #334155;
  background-color: #0f172a;
}

.editor-title-container {
  display: flex;
  flex-direction: column;
}

.editor-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.magic-wand {
  font-size: 1.5rem;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.editor-subtitle {
  margin-top: 0.5rem;
  font-size: 1rem;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 600;
}

.dark .editor-title {
  color: #e2e8f0;
}

.dark .editor-subtitle {
  color: #94a3b8;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.75rem;
  color: #64748b;
  cursor: pointer;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #f1f5f9;
  color: #334155;
  transform: rotate(90deg);
}

.dark .close-button {
  color: #94a3b8;
}

.dark .close-button:hover {
  background-color: #334155;
  color: #e2e8f0;
}

/* 编辑器主体 */
.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧面板 */
.editor-left-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  border-right: 1px solid #e2e8f0;
}

.dark .editor-left-panel {
  border-color: #334155;
}

/* 画布容器 */
.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  min-height: 400px;
  background-color: #f8fafc;
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  border: 2px dashed #cbd5e1;
}

.dark .canvas-container {
  background-color: #0f172a;
  border-color: #475569;
}

/* 画布提示 */
.canvas-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
  pointer-events: none;
}

.dark .canvas-hint {
  background-color: rgba(15, 23, 42, 0.7);
}

.hint-content {
  text-align: center;
  padding: 1.5rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: bounce 2s infinite;
}

.dark .hint-content {
  background-color: #1e293b;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.hint-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.hint-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

.dark .hint-text {
  color: #e2e8f0;
}

.hint-tip {
  font-size: 0.875rem;
  color: #64748b;
  font-style: italic;
  max-width: 250px;
  margin: 0 auto;
}

.dark .hint-tip {
  color: #94a3b8;
}

/* 工具栏 */
.editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  align-items: center;
  justify-content: space-between;
}

.dark .editor-toolbar {
  background-color: #1e293b;
}

.tool-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.tool-button {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-button-large {
  width: auto;
  min-width: 5rem;
  height: 4rem;
  padding: 0.5rem 1rem;
  flex-direction: column;
  gap: 0.25rem;
}

.tool-label {
  font-size: 0.875rem;
  font-weight: 600;
}

.dark .tool-button {
  background-color: #334155;
  border-color: #475569;
}

.tool-button:hover {
  background-color: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .tool-button:hover {
  background-color: #475569;
  border-color: #64748b;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.tool-button.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.dark .tool-button.active {
  background-color: #1e40af;
  border-color: #60a5fa;
  color: #60a5fa;
}

.tool-icon {
  font-size: 1.5rem;
}
