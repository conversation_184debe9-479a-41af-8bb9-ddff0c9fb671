<script setup lang="ts">
import { fetchCollectAppAPI, fetchCleanInvalidCollectionsAPI } from '@/api/appStore';
import type { ResData } from '@/api/types';
import { SvgIcon } from '@/components/common';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import {
  useAppCatStore,
  useAppStore,
  useAuthStore,
  useChatStore,
  useGlobalStoreWithOut,
} from '@/store';
import { ApplicationTwo, Down, Up, Plus } from '@icon-park/vue-next';
import { NScrollbar, NButton, NIcon, useMessage } from 'naive-ui';
import { computed, inject, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ListItem from './sider/ListItem.vue';

const { isMobile } = useBasicLayout();
const router = useRouter();
const route = useRoute();
const appStore = useAppStore();
const chatStore = useChatStore();
const authStore = useAuthStore();
const ms = useMessage();

const customKeyId = ref(100);
const appCatStore = useAppCatStore();
const showMoreSticky = ref(false);
const showMoreMineApps = ref(false);
const showMorePlugins = ref(false);

// 数据源计算
const dataSources = computed(() => chatStore.groupList);
const groupKeyWord = computed(() => chatStore.groupKeyWord);
watch(dataSources, () => (customKeyId.value = customKeyId.value + 1));
watch(groupKeyWord, () => (customKeyId.value = customKeyId.value + 1));

const isStreamIn = computed(() => {
  return chatStore.isStreamIn !== undefined ? chatStore.isStreamIn : false;
});
const isLogin = computed(() => authStore.isLogin);

const createNewChatGroup = inject(
  'createNewChatGroup',
  async (appId?: number) => {
    // 默认逻辑或简单的提示信息
  }
) as (appId?: number) => Promise<void>;

const mineApps = computed(() => {
  return appCatStore.mineApps;
});

// utc格式转换
function formatUtcTime(utcTime: Date | string | number) {
  if (typeof utcTime === 'number') {
    return utcTime;
  }

  try {
    const date = new Date(utcTime);
    if (isNaN(date.getTime())) {
      console.error('无效的日期格式:', utcTime);
      return 0;
    }
    const shanghaiTime = date.getTime() + 8 * 60 * 60 * 1000;
    return shanghaiTime;
  } catch (error) {
    console.error('日期转换错误:', error);
    return 0;
  }
}

const today = new Date().setHours(0, 0, 0, 0);

const stickyList = computed(() => {
  const filteredList = dataSources.value.filter((item) =>
    groupKeyWord.value
      ? item.title.includes(groupKeyWord.value) && item.isSticky && !item.params
      : item.isSticky && !item.params
  );

  return filteredList.sort((a, b) => {
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA;
    }

    return b.uuid - a.uuid;
  });
});

const todayList = computed(() => {
  const filteredList = dataSources.value.filter((item: any) => {
    if (groupKeyWord.value)
      return (
        item.title.includes(groupKeyWord.value) &&
        !item.isSticky &&
        formatUtcTime(item.updatedAt) >= today &&
        !item.params
      );
    else
      return (
        !item.isSticky && formatUtcTime(item.updatedAt) >= today && !item.params
      );
  });

  return filteredList.sort((a, b) => {
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA;
    }

    return b.uuid - a.uuid;
  });
});

const otherList = computed(() => {
  const filteredList = dataSources.value.filter((item: any) => {
    if (groupKeyWord.value)
      return (
        item.title.includes(groupKeyWord.value) &&
        !item.isSticky &&
        formatUtcTime(item.updatedAt) < today &&
        !item.params
      );
    else
      return (
        !item.isSticky && formatUtcTime(item.updatedAt) < today && !item.params
      );
  });

  return filteredList.sort((a, b) => {
    const timeA = formatUtcTime(a.updatedAt);
    const timeB = formatUtcTime(b.updatedAt);

    if (timeA !== timeB) {
      return timeB - timeA;
    }

    return b.uuid - a.uuid;
  });
});

// 新建对话处理
async function handleNewChat() {
  if (isStreamIn.value) {
    ms.info('AI回复中，请稍后再试');
    return;
  }
  await createNewChatGroup();
}

onMounted(() => {
  appCatStore.queryMineApps();
  chatStore.queryPlugins();
});
</script>

<template>
  <div class="chat-history-tabs h-full flex flex-col">
    <!-- 标签页头部 -->
    <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">聊天历史</h3>
      <NButton
        secondary
        circle
        size="small"
        @click="handleNewChat"
        :disabled="isStreamIn"
        class="flex-shrink-0"
      >
        <template #icon>
          <NIcon :component="Plus" />
        </template>
      </NButton>
    </div>

    <!-- 聊天历史内容 -->
    <div class="flex-1 overflow-hidden">
      <NScrollbar class="h-full">
        <div class="p-2 space-y-1">
          <!-- 置顶对话 -->
          <div v-if="stickyList.length > 0">
            <div class="flex items-center justify-between px-2 py-1">
              <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                置顶对话
              </span>
              <button
                @click="showMoreSticky = !showMoreSticky"
                class="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <Down v-if="!showMoreSticky" class="w-3 h-3" />
                <Up v-else class="w-3 h-3" />
              </button>
            </div>
            
            <div v-show="showMoreSticky || stickyList.length <= 3">
              <ListItem
                v-for="(item, index) of stickyList"
                :key="`${item.uuid}-${customKeyId}`"
                :date-time="item.updatedAt"
                :text="item.title"
                :active="item.uuid === chatStore.active"
                :uuid="item.uuid"
                :is-sticky="item.isSticky"
                :app-id="item.appId"
              />
            </div>
            
            <div v-show="!showMoreSticky && stickyList.length > 3">
              <ListItem
                v-for="(item, index) of stickyList.slice(0, 3)"
                :key="`${item.uuid}-${customKeyId}`"
                :date-time="item.updatedAt"
                :text="item.title"
                :active="item.uuid === chatStore.active"
                :uuid="item.uuid"
                :is-sticky="item.isSticky"
                :app-id="item.appId"
              />
            </div>
          </div>

          <!-- 今日对话 -->
          <div v-if="todayList.length > 0">
            <div class="px-2 py-1">
              <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                今日对话
              </span>
            </div>
            <ListItem
              v-for="(item, index) of todayList"
              :key="`${item.uuid}-${customKeyId}`"
              :date-time="item.updatedAt"
              :text="item.title"
              :active="item.uuid === chatStore.active"
              :uuid="item.uuid"
              :is-sticky="item.isSticky"
              :app-id="item.appId"
            />
          </div>

          <!-- 历史对话 -->
          <div v-if="otherList.length > 0">
            <div class="px-2 py-1">
              <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                历史对话
              </span>
            </div>
            <ListItem
              v-for="(item, index) of otherList"
              :key="`${item.uuid}-${customKeyId}`"
              :date-time="item.updatedAt"
              :text="item.title"
              :active="item.uuid === chatStore.active"
              :uuid="item.uuid"
              :is-sticky="item.isSticky"
              :app-id="item.appId"
            />
          </div>

          <!-- 空状态 -->
          <div v-if="dataSources.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-400 dark:text-gray-500">
            <SvgIcon icon="ri:chat-3-line" class="text-3xl mb-2" />
            <span class="text-sm">暂无聊天记录</span>
            <NButton
              type="primary"
              size="small"
              @click="handleNewChat"
              class="mt-3"
            >
              开始新对话
            </NButton>
          </div>
        </div>
      </NScrollbar>
    </div>
  </div>
</template>

<style scoped lang="scss">
.chat-history-tabs {
  background: white;
  
  @media (prefers-color-scheme: dark) {
    background: #1f2937;
  }
}
</style> 