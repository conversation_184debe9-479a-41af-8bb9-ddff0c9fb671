<template>
  <div class="creation-header">
    <div class="header-left">
      <div class="back-button" @click="goBack">
        <SvgIcon name="ri:arrow-left-line" size="20" />
      </div>

      <div class="title-container">
        <div
          class="work-title"
          :class="{ 'editing': isEditingTitle }"
          @click="startEditTitle"
        >
          <input
            v-if="isEditingTitle"
            ref="titleInput"
            v-model="localTitle"
            @blur="finishEditTitle"
            @keydown.enter="finishEditTitle"
            class="title-input"
          />
          <h1 v-else class="title-text">{{ title }}</h1>
        </div>

        <div class="creation-type">
          <div class="type-badge" :class="getBadgeClass">
            <SvgIcon :name="getTypeIcon" size="16" />
            <span>{{ getTypeName }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="header-right">
      <n-button-group>
        <n-button @click="$emit('new')" secondary>
          <template #icon>
            <SvgIcon name="ri:add-line" size="18" />
          </template>
          新建
        </n-button>
        <!-- 作品管理功能已移除 -->
      </n-button-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { NButton, NButtonGroup } from 'naive-ui';
import { SvgIcon } from '@/components/common';

const props = defineProps({
  creationType: {
    type: String,
    default: 'chat'
  },
  title: {
    type: String,
    default: '未命名作品'
  }
});

const emit = defineEmits(['update:title', 'new']); // 作品管理功能已移除

const router = useRouter();
const isEditingTitle = ref(false);
const localTitle = ref(props.title);
const titleInput = ref<HTMLInputElement | null>(null);

// 计算属性
const getTypeName = computed(() => {
  const typeMap = {
    'chat': '通用对话',
    'programming': 'AI编程',
    'writing': 'AI写作',
    'picturebook': 'AI绘本',
    'ppt': 'AI PPT',
    'music': 'AI音乐',
    'freestyle': '自由创作'
  };

  return typeMap[props.creationType] || '通用对话';
});

const getTypeIcon = computed(() => {
  const iconMap = {
    'chat': 'ri:chat-3-line',
    'programming': 'ri:code-box-line',
    'writing': 'ri:file-text-line',
    'picturebook': 'ri:book-open-line',
    'ppt': 'ri:slideshow-line',
    'music': 'ri:music-line',
    'freestyle': 'ri:magic-line'
  };

  return iconMap[props.creationType] || 'ri:chat-3-line';
});

const getBadgeClass = computed(() => {
  const classMap = {
    'chat': 'badge-blue',
    'programming': 'badge-green',
    'writing': 'badge-purple',
    'picturebook': 'badge-orange',
    'ppt': 'badge-indigo',
    'music': 'badge-pink',
    'freestyle': 'badge-rainbow'
  };

  return classMap[props.creationType] || 'badge-blue';
});

// 方法
function goBack() {
  router.push('/');
}

function startEditTitle() {
  localTitle.value = props.title;
  isEditingTitle.value = true;

  nextTick(() => {
    titleInput.value?.focus();
  });
}

function finishEditTitle() {
  if (localTitle.value.trim()) {
    emit('update:title', localTitle.value);
  } else {
    localTitle.value = props.title;
  }

  isEditingTitle.value = false;
}
</script>

<style scoped>
.creation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.dark .creation-header {
  background-color: var(--color-gray-800);
  border-bottom: 1px solid var(--color-gray-700);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: var(--color-gray-100);
}

.dark .back-button:hover {
  background-color: var(--color-gray-700);
}

.title-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.work-title {
  display: flex;
  align-items: center;
}

.title-text {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  cursor: pointer;
}

.title-input {
  font-size: 1.25rem;
  font-weight: 600;
  border: none;
  border-bottom: 2px solid var(--color-primary-500);
  background-color: transparent;
  padding: 0.25rem 0;
  width: 100%;
  outline: none;
}

.dark .title-input {
  color: white;
}

.creation-type {
  display: flex;
  align-items: center;
}

.type-badge {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.badge-blue {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

.badge-green {
  background: linear-gradient(135deg, #10b981, #3b82f6);
}

.badge-purple {
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
}

.badge-orange {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

.badge-indigo {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.badge-pink {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
}

.badge-rainbow {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  .creation-header {
    padding: 0.75rem 1rem;
  }

  .title-text {
    font-size: 1rem;
  }

  .title-input {
    font-size: 1rem;
  }

  .header-right :deep(.n-button-group) {
    display: flex;
  }

  .header-right :deep(.n-button) {
    padding: 0 0.5rem;
  }

  .header-right :deep(.n-button-content) {
    display: flex;
    align-items: center;
  }

  .header-right :deep(.n-button__icon) {
    margin-right: 0;
  }

  .header-right :deep(.n-button__content) {
    display: none;
  }
}
</style>
