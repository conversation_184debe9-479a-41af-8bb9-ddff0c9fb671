import { BaseEntity } from 'src/common/entity/baseEntity';
import { Column, Entity } from 'typeorm';

@Entity({ name: 'storybook_template' })
export class StorybookTemplateEntity extends BaseEntity {
  @Column({ comment: '模板标题' })
  title: string;

  @Column({ comment: '模板描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '封面图片URL', type: 'text', nullable: true })
  coverImg: string;

  @Column({ comment: '分类', nullable: true })
  category: string;

  @Column({ comment: '模板内容', type: 'json' })
  content: object;

  @Column({ comment: '状态(0:禁用,1:启用)', default: 1 })
  status: number;

  @Column({ comment: '排序', default: 100 })
  order: number;
}
