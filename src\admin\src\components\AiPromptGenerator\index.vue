<template>
  <div class="ai-prompt-generator">
    <el-dialog
      v-model="dialogVisible"
      title="AI生成提示词和表单"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <div class="generator-container">
        <!-- 用户输入区域 -->
        <div class="input-section">
          <h3>描述您的应用</h3>
          <p class="text-gray-500 text-sm mb-2">
            请详细描述您想要创建的应用，包括应用的功能、目的、用户需要填写的信息等。AI将根据您的描述生成结构化的提示词和表单字段。
          </p>
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="6"
            placeholder="例如：我需要一个AI编程助手应用，用户需要提供项目名称、编程语言、项目描述和具体需求等信息。生成的内容应该是高质量的代码和详细的注释。"
            :disabled="loading"
          />

          <div class="mt-4">
            <div class="flex justify-between mb-3">
              <div>
                <el-checkbox v-model="generateForm" :disabled="loading">自动生成表单</el-checkbox>
                <el-tooltip content="启用后，AI会根据生成的提示词自动创建表单字段">
                  <el-icon class="ml-1"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div>
                <el-select
                  v-model="selectedModel"
                  placeholder="选择模型"
                  size="default"
                  :loading="loadingModels"
                  :disabled="loading"
                  style="width: 200px;"
                >
                  <el-option
                    v-for="option in modelOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  >
                    <div class="flex items-center space-x-2">
                      <img
                        v-if="option.modelAvatar"
                        :src="option.modelAvatar"
                        class="w-5 h-5 rounded-full object-cover"
                      />
                      <div
                        v-else
                        class="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center"
                      >
                        <span class="text-xs">AI</span>
                      </div>
                      <span>{{ option.label }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="flex justify-end">
              <el-button type="primary" @click="generatePrompt" :loading="loading">
                {{ loading ? '生成中...' : '生成提示词和表单' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 生成结果区域 -->
        <div v-if="generatedResult" class="result-section mt-4">
          <h3>生成结果</h3>

          <div class="prompt-result">
            <div class="flex justify-between items-center">
              <h4>提示词</h4>
              <el-button type="primary" link @click="copyText(generatedResult.prompt)">
                <el-icon><CopyDocument /></el-icon> 复制
              </el-button>
            </div>
            <el-input
              v-model="generatedResult.prompt"
              type="textarea"
              :rows="6"
              readonly
            />
          </div>

          <div v-if="generatedResult.formFields && generatedResult.formFields.length > 0" class="form-result mt-4">
            <div class="flex justify-between items-center">
              <h4>表单字段</h4>
              <el-button type="primary" link @click="copyText(JSON.stringify(generatedResult.formFields, null, 2))">
                <el-icon><CopyDocument /></el-icon> 复制
              </el-button>
            </div>
            <el-input
              v-model="formFieldsJson"
              type="textarea"
              :rows="6"
              readonly
            />

            <div class="form-preview mt-4">
              <h4>表单预览</h4>
              <form-preview :fields="formFieldsJson" />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="applyResult"
            :disabled="!generatedResult"
          >
            应用到表单
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { QuestionFilled, CopyDocument } from '@element-plus/icons-vue';
import FormPreview from '@/components/FormPreview/index.vue';
import { extractVariablesFromPreset, createFieldsFromPreset } from '@/utils/formUtils';
import ApiAi from '@/api/modules/ai';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'generate-success']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 用户输入
const userInput = ref('');

// 是否生成表单
const generateForm = ref(true);

// 加载状态
const loading = ref(false);

// 模型相关
const loadingModels = ref(false);
const selectedModel = ref('');
const modelOptions = ref<{ label: string; value: string; modelAvatar?: string; }[]>([]);

// 生成结果
const generatedResult = ref<{
  prompt: string;
  formFields: any[];
} | null>(null);

// 表单字段JSON
const formFieldsJson = computed(() => {
  if (generatedResult.value && generatedResult.value.formFields) {
    return JSON.stringify(generatedResult.value.formFields, null, 2);
  }
  return '';
});

// 生成提示词和表单
async function generatePrompt() {
  if (!userInput.value.trim()) {
    ElMessage.warning('请输入应用描述');
    return;
  }

  try {
    loading.value = true;

    // 构建系统提示词
    const basePrompt = '你是一个专业的AI应用开发助手，擅长创建提示词和表单。你的任务是根据用户的需求描述，生成结构化的提示词和表单定义。请仔细阅读并理解用户的需求，然后生成高质量的提示词和表单。\n\n' +
      '用户将提供一个应用描述，例如"我需要一个AI编程助手"或"我需要一个教案生成器"。\n\n' +
      '你需要生成：\n' +
      '1. 一个结构化的提示词，使用${variable}格式标记变量\n' +
      '2. ' + (generateForm.value ? '一个JSON格式的表单字段定义，包含用户需要填写的所有字段' : '') + '\n\n' +
      '提示词要求：\n' +
      '- 应该清晰、专业，并包含所有必要的指导\n' +
      '- 应该足够详细，以便生成高质量的内容\n' +
      '- 应该包含多个变量，使用${variableName}格式\n' +
      '- 变量名称应该简洁易懂，使用驼峰命名法，例如${subjectName}、${gradeLevel}等\n' +
      '- 提示词应该包含足够的上下文信息，以便生成的内容更加相关和有用\n\n';

    const formFieldsRequirements = generateForm.value ?
      '表单字段要求：\n' +
      '- 每个字段应包含name、label、type、required等属性\n' +
      '- name属性应与提示词中的变量名称保持一致，例如变量${subjectName}对应的字段name应为"subjectName"\n' +
      '- label属性应为中文，清晰说明字段的用途\n' +
      '- 支持的字段类型有：input、textarea、select、radio、checkbox、switch、slider、date、time、datetime、rate、color、number\n' +
      '- 对于select、radio、checkbox类型，应提供选项数组，每个选项包含label和value属性\n' +
      '- 字段类型应与字段的实际用途相符，例如文本内容使用textarea，日期使用date等\n' +
      '- 对于必填字段，设置required为true\n\n' : '';

    const returnFormat =
      '你必须仅返回JSON格式的结果，不要有任何其他文本。返回格式必须如下：\n' +
      '{\n' +
      '  "prompt": "提示词内容",\n' +
      '  ' + (generateForm.value ? '"formFields": [表单字段数组]' : '') + '\n' +
      '}\n\n' +
      '请注意：\n' +
      '1. 不要在JSON前后添加任何解释或其他文本，只返回JSON对象本身\n' +
      '2. 确保生成的JSON格式正确，可以被直接解析\n' +
      '3. 确保提示词中的变量名称与表单字段的name属性一致\n' +
      '4. 不要使用Markdown格式或代码块，直接返回JSON对象';

    const systemPrompt = basePrompt + formFieldsRequirements + returnFormat;

    // 调用AI服务
    const response = await ApiAi.generateContent({
      prompt: userInput.value,
      systemMessage: systemPrompt,
      model: selectedModel.value // 传递选定的模型
    });

    // 解析返回结果
    try {
      // 尝试从返回结果中提取JSON
      let result;
      let content = '';

      // 处理不同的响应格式
      console.log('收到的响应：', response);

      if (response && typeof response === 'object' && 'text' in response) {
        // 如果返回的是聊天API的结果格式
        content = response.text as string;
      } else if (response && typeof response === 'object' && 'answer' in response) {
        content = response.answer as string;
      } else if (response && response.data) {
        // 如果数据在data字段中
        if (typeof response.data === 'string') {
          content = response.data;
        } else if (response.data && response.data.text) {
          content = response.data.text;
        } else if (response.data && response.data.answer) {
          content = response.data.answer;
        } else if (response.data && response.data.choices && response.data.choices.length > 0) {
          // 处理直接调用OpenAI API的响应格式
          const message = response.data.choices[0].message;
          if (message && message.content) {
            content = message.content;
          } else {
            content = JSON.stringify(response.data);
          }
        } else {
          content = JSON.stringify(response.data);
        }
      } else if (typeof response === 'string') {
        content = response;
      } else {
        // 如果没有找到有效的内容，尝试将整个响应转换为字符串
        content = JSON.stringify(response);
      }

      // 尝试从内容中提取JSON
      try {
        // 先尝试直接解析全文
        try {
          result = JSON.parse(content);
          console.log('直接解析成功', result);
        } catch (directParseError) {
          // 如果直接解析失败，尝试查找JSON块
          console.log('直接解析失败，尝试查找JSON块');
          const jsonRegex = /\{[\s\S]*?\}(?=\s*$)/;
          const jsonMatch = content.match(jsonRegex);

          if (jsonMatch) {
            const jsonStr = jsonMatch[0].trim();
            console.log('找到JSON块', jsonStr);
            result = JSON.parse(jsonStr);
          } else {
            // 如果没有找到JSON块，尝试其他正则表达式
            console.log('没有找到JSON块，尝试其他正则表达式');
            const alternativeRegex = /\{[\s\S]*\}/;
            const alternativeMatch = content.match(alternativeRegex);

            if (alternativeMatch) {
              console.log('找到替代JSON块', alternativeMatch[0]);
              result = JSON.parse(alternativeMatch[0]);
            } else {
              // 如果所有尝试都失败，创建一个基本的结果对象
              console.log('所有JSON提取尝试失败，创建基本结果');
              result = {
                prompt: content,
                formFields: []
              };
            }
          }
        }
      } catch (jsonError) {
        console.error('解析JSON失败', jsonError);
        // 如果解析JSON失败，创建一个基本的结果对象
        result = {
          prompt: content,
          formFields: []
        };
      }

      // 验证结果格式
      if (!result.prompt) {
        throw new Error('返回结果格式错误，缺少prompt字段');
      }

      // 如果需要生成表单但返回结果中没有formFields，则自动从提示词中提取变量生成表单
      if (generateForm.value && (!result.formFields || !Array.isArray(result.formFields))) {
        const variables = extractVariablesFromPreset(result.prompt);
        if (variables.size > 0) {
          const { fields } = createFieldsFromPreset(result.prompt, []);
          result.formFields = fields;
        } else {
          result.formFields = [];
        }
      }

      generatedResult.value = result;
      ElMessage.success('生成成功');
    } catch (error) {
      console.error('解析AI返回结果失败', error);
      ElMessage.error('解析AI返回结果失败，请重试');
    }
  } catch (error) {
    console.error('生成提示词失败', error);
    ElMessage.error('生成提示词失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 复制文本
function copyText(text: string) {
  navigator.clipboard.writeText(text)
    .then(() => {
      ElMessage.success('复制成功');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
}

// 应用生成结果
function applyResult() {
  if (!generatedResult.value) {
    ElMessage.warning('请先生成提示词和表单');
    return;
  }

  emit('generate-success', {
    prompt: generatedResult.value.prompt,
    formFields: generatedResult.value.formFields
  });

  dialogVisible.value = false;
}

// 获取模型列表
async function fetchModelsList() {
  loadingModels.value = true;
  try {
    const res = await ApiAi.getModelsList();
    if (res.data) {
      const { modelMaps, modelTypeList } = res.data;

      // 处理模型列表，与聊天组件保持一致
      const flatModelArray = Object.values(modelMaps).flat() as any[];
      // 这里可以根据需要筛选特定类型的模型，例如只显示keyType为1的模型
      const filteredModelArray = flatModelArray.filter(
        (model) => model.keyType === 1
      );

      modelOptions.value = filteredModelArray.map((model) => ({
        label: model.modelName,
        value: model.model,
        modelAvatar: model.modelAvatar
      }));

      // 默认选择第一个模型
      if (modelOptions.value.length > 0 && !selectedModel.value) {
        selectedModel.value = modelOptions.value[0].value;
      }
    }
  } catch (error) {
    console.error('获取模型列表失败', error);
    ElMessage.error('获取模型列表失败，将使用默认模型');

    // 如果获取模型列表失败，使用默认模型
    modelOptions.value = [
      { label: 'GPT-3.5', value: 'gpt-3.5-turbo' },
      { label: 'GPT-4', value: 'gpt-4' },
      { label: 'Claude-3', value: 'claude-3-sonnet-20240229' }
    ];
    selectedModel.value = 'gpt-3.5-turbo';
  } finally {
    loadingModels.value = false;
  }
}

// 关闭对话框
function handleClose() {
  userInput.value = '';
  generatedResult.value = null;
}

// 组件挂载时获取模型列表
onMounted(() => {
  fetchModelsList();
});
</script>

<style scoped>
.ai-prompt-generator {
  .generator-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .input-section, .result-section {
    padding: 15px;
    border-radius: 4px;
    background-color: #f9f9f9;
  }

  .form-preview {
    padding: 15px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #eee;
  }
}
</style>
