{"id": "freestyle", "name": "自由创作", "description": "不受限制的创意探索，发挥您的无限想象力", "systemMessage": "你是一位多才多艺的创意助手，擅长各种形式的创作，包括但不限于文字、代码、设计、音乐、视觉艺术等。你的目标是帮助用户探索和实现他们的创意想法，无论多么独特或跨领域。请根据用户的需求提供灵感、建议和具体内容，鼓励创新思维和自由表达。", "temperature": 0.8, "welcomeMessage": "欢迎来到自由创作空间！这里没有固定的规则和限制，你可以探索任何创意想法。无论是头脑风暴、概念设计、跨媒体创作，还是其他任何创意项目，我都可以提供帮助。请告诉我你想要探索的方向或需要什么样的创意支持。", "promptExamples": ["帮我进行一次创意头脑风暴，主题是'未来城市中的社交互动'", "我想创作一个融合科幻和神话元素的故事世界，请给我一些建议", "设计一个既有艺术性又有实用性的家居产品概念", "帮我构思一个跨媒体艺术项目，结合音乐、视觉和互动元素", "我需要一个创新的教育游戏概念，目标是教授环境保护知识"], "showTokenCount": false, "showModelName": false, "showResponseTime": false, "enableMarkdownEditor": true, "enableHtmlEditor": true, "enablePreview": true, "enablePrompt": true, "enableContinueGeneration": true, "enableStreamResponse": true, "enableAutoSave": true, "enableAutoTitle": true, "enableAutoSummary": true, "enableAutoTags": true, "enableVoiceInput": true, "enableTextToSpeech": false, "enableImageGeneration": false, "enableCodeHighlight": true, "enableMath": true, "enableChart": true, "enableDiagram": true, "enableMindMap": true, "enableTable": true, "enableTaskList": true, "enableEmoji": true, "enableToolbar": true, "enableStatusBar": true, "enableOutline": true, "enableWordCount": true, "enableTypewriter": true, "enableZenMode": true, "enableAutoCompletion": true, "enableSpellCheck": true, "enableAutoPair": true, "enableAutoIndent": true, "enableLineWrapping": true, "enableLineNumbers": true, "enableCodeFolding": true, "enableIndentGuide": true, "enableHighlightActiveLine": true, "enableHighlightActiveLineGutter": true, "enableHighlightSpecialChars": true, "enableHighlightTrailingWhitespace": true, "enableMatchBrackets": true, "enableMatchTags": true, "enableAutoCloseTags": true, "enableAutoCloseBrackets": true, "enableMultipleCursors": true, "enableSearchReplace": true, "enableHistory": true, "enableCommenting": true, "enableLinting": true, "enableFormatting": true, "enableFolding": true, "enableIndentation": true, "enableSorting": true, "enableCaseConversion": true, "enableTextWrapping": true, "enableTextAlignment": true, "enableTextDirection": true, "enableTextTransformation": true, "enableTextEncoding": true, "enableTextDecoding": true, "enableTextCompression": true, "enableTextDecompression": true, "enableTextEncryption": true, "enableTextDecryption": true, "enableTextHashing": true, "enableTextVerification": true}