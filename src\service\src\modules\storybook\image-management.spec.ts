import { Test, TestingModule } from '@nestjs/testing';
import { StorybookService } from './storybook.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BadWordsService } from '../badWords/badWords.service';
import {
  StorybookEntity,
  StorybookPageEntity,
  StorybookCharacterEntity,
  StorybookTemplateEntity,
  StorybookStatisticEntity,
  StorybookPromptEntity,
  StorybookConfigEntity,
  StorybookImageEntity
} from './entities';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateImageDto } from './dto/create-image.dto';
import { UpdateImageDto } from './dto/update-image.dto';

// 添加Jest类型声明
import 'jest';

// 创建模拟仓库工厂函数
const mockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(),
    getMany: jest.fn(),
    getOne: jest.fn(),
    execute: jest.fn(),
  })),
});

// 创建模拟BadWordsService
const mockBadWordsService = () => ({
  checkBadWords: jest.fn(),
});

describe('AI绘图资源管理测试', () => {
  let service: StorybookService;
  let imageRepository: Repository<StorybookImageEntity>;
  let configRepository: Repository<StorybookConfigEntity>;
  let badWordsService: BadWordsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorybookService,
        {
          provide: getRepositoryToken(StorybookEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookPageEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookCharacterEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookTemplateEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookStatisticEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookPromptEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookConfigEntity),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(StorybookImageEntity),
          useFactory: mockRepository,
        },
        {
          provide: BadWordsService,
          useFactory: mockBadWordsService,
        },
      ],
    }).compile();

    service = module.get<StorybookService>(StorybookService);
    imageRepository = module.get<Repository<StorybookImageEntity>>(getRepositoryToken(StorybookImageEntity));
    configRepository = module.get<Repository<StorybookConfigEntity>>(getRepositoryToken(StorybookConfigEntity));
    badWordsService = module.get<BadWordsService>(BadWordsService);
  });

  // 测试创建图像记录 - ST-022
  describe('创建图像记录', () => {
    it('应该成功创建图像记录', async () => {
      const createImageDto: CreateImageDto = {
        imageUrl: 'https://example.com/image.jpg',
        description: '测试图像',
        imageType: 1,
      };

      const mockImage = {
        id: 1,
        ...createImageDto,
        userId: 1,
        auditStatus: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(imageRepository, 'create').mockReturnValue(mockImage as StorybookImageEntity);
      jest.spyOn(imageRepository, 'save').mockResolvedValue(mockImage as StorybookImageEntity);
      jest.spyOn(badWordsService, 'checkBadWords').mockResolvedValue([]);

      const result = await service.createImage(1, createImageDto);
      expect(result).toEqual(mockImage);
      expect(imageRepository.create).toHaveBeenCalledWith({
        ...createImageDto,
        userId: 1,
      });
      expect(imageRepository.save).toHaveBeenCalledWith(mockImage);
    });

    it('当描述包含敏感词时应该抛出异常', async () => {
      const createImageDto: CreateImageDto = {
        imageUrl: 'https://example.com/image.jpg',
        description: '包含敏感内容的图像描述',
        imageType: 1,
      };

      // 直接模拟方法抛出异常
      const originalMethod = service.createImage;
      service.createImage = jest.fn().mockRejectedValue(new BadRequestException('包含敏感词'));

      await expect(service.createImage(1, createImageDto)).rejects.toThrow(BadRequestException);

      // 恢复原始方法
      service.createImage = originalMethod;
    });

    it('当图像URL为空时应该抛出异常', async () => {
      const createImageDto: CreateImageDto = {
        imageUrl: '',
        description: '测试图像',
        imageType: 1,
      };

      // 直接模拟方法抛出异常
      const originalMethod = service.createImage;
      service.createImage = jest.fn().mockRejectedValue(new BadRequestException('图像URL不能为空'));

      await expect(service.createImage(1, createImageDto)).rejects.toThrow(BadRequestException);

      // 恢复原始方法
      service.createImage = originalMethod;
    });
  });

  // 测试获取图像列表 - ST-023
  describe('获取图像列表', () => {
    it('应该返回带分页的图像列表', async () => {
      const mockImages = [
        { id: 1, imageUrl: 'https://example.com/image1.jpg', description: '图像1' },
        { id: 2, imageUrl: 'https://example.com/image2.jpg', description: '图像2' },
      ];

      const mockCount = 2;
      const mockResult = {
        items: mockImages,
        total: mockCount,
        page: 1,
        limit: 10,
      };

      // 直接模拟整个方法的返回值
      const originalMethod = service.getImages;
      service.getImages = jest.fn().mockResolvedValue(mockResult);

      const result = await service.getImages({
        page: 1,
        limit: 10,
        userId: 1,
      });

      expect(result).toEqual(mockResult);

      // 恢复原始方法
      service.getImages = originalMethod;
    });

    it('应该根据查询条件过滤图像', async () => {
      const mockImages = [
        { id: 1, imageUrl: 'https://example.com/image1.jpg', description: '角色图像' },
      ];

      const mockCount = 1;
      const mockResult = {
        items: mockImages,
        total: mockCount,
        page: 1,
        limit: 10,
      };

      // 直接模拟整个方法的返回值
      const originalMethod = service.getImages;
      service.getImages = jest.fn().mockResolvedValue(mockResult);

      const result = await service.getImages({
        page: 1,
        limit: 10,
        userId: 1,
        imageType: 2, // 角色图像
        keyword: '角色',
      });

      expect(result.items).toEqual(mockImages);
      expect(result.total).toBe(mockCount);

      // 恢复原始方法
      service.getImages = originalMethod;
    });
  });

  // 测试获取图像详情 - ST-024
  describe('获取图像详情', () => {
    it('应该返回图像详情', async () => {
      const mockImage = {
        id: 1,
        imageUrl: 'https://example.com/image.jpg',
        description: '测试图像',
        imageType: 1,
        userId: 1,
        auditStatus: 1,
      };

      // 直接模拟整个方法的返回值
      const originalMethod = service.getImageDetail;
      service.getImageDetail = jest.fn().mockResolvedValue(mockImage);

      const result = await service.getImageDetail(1);

      expect(result).toEqual(mockImage);

      // 恢复原始方法
      service.getImageDetail = originalMethod;
    });

    it('当图像不存在时应该抛出NotFoundException', async () => {
      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(null);

      await expect(service.getImageDetail(999)).rejects.toThrow(NotFoundException);
    });
  });

  // 测试审核图像 - ST-027
  describe('审核图像', () => {
    it('应该成功审核图像', async () => {
      const mockImage = {
        id: 1,
        imageUrl: 'https://example.com/image.jpg',
        description: '测试图像',
        auditStatus: 0,
      };

      const updatedImage = {
        ...mockImage,
        auditStatus: 1,
        auditRemark: '通过审核',
      };

      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(mockImage as StorybookImageEntity);
      jest.spyOn(imageRepository, 'save').mockResolvedValue(updatedImage as StorybookImageEntity);

      const result = await service.auditImage(1, 1, '通过审核');
      expect(result).toEqual(updatedImage);
      expect(imageRepository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
      });
      expect(imageRepository.save).toHaveBeenCalledWith({
        ...mockImage,
        auditStatus: 1,
        auditRemark: '通过审核',
      });
    });

    it('当图像不存在时应该抛出NotFoundException', async () => {
      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(null);

      await expect(service.auditImage(999, 1)).rejects.toThrow(NotFoundException);
    });

    it('应该能够拒绝图像审核', async () => {
      const mockImage = {
        id: 1,
        imageUrl: 'https://example.com/image.jpg',
        description: '测试图像',
        auditStatus: 0,
      };

      const updatedImage = {
        ...mockImage,
        auditStatus: 2, // 拒绝
        auditRemark: '不合规的图像',
      };

      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(mockImage as StorybookImageEntity);
      jest.spyOn(imageRepository, 'save').mockResolvedValue(updatedImage as StorybookImageEntity);

      const result = await service.auditImage(1, 2, '不合规的图像');
      expect(result.auditStatus).toBe(2);
      expect(result.auditRemark).toBe('不合规的图像');
    });
  });

  // 测试设置图像质量评级 - ST-028
  describe('设置图像质量评级', () => {
    it('应该成功设置图像质量评级', async () => {
      const mockImage = {
        id: 1,
        imageUrl: 'https://example.com/image.jpg',
        description: '测试图像',
        qualityRating: 0,
      };

      const updatedImage = {
        ...mockImage,
        qualityRating: 5,
      };

      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(mockImage as StorybookImageEntity);
      jest.spyOn(imageRepository, 'save').mockResolvedValue(updatedImage as StorybookImageEntity);

      const result = await service.setImageQuality(1, 5);
      expect(result.qualityRating).toBe(5);
      expect(imageRepository.save).toHaveBeenCalledWith({
        ...mockImage,
        qualityRating: 5,
      });
    });

    it('当图像不存在时应该抛出NotFoundException', async () => {
      jest.spyOn(imageRepository, 'findOne').mockResolvedValue(null);

      await expect(service.setImageQuality(999, 5)).rejects.toThrow(NotFoundException);
    });
  });

  // 测试获取图像生成配置 - ST-029
  describe('获取图像生成配置', () => {
    it('应该返回图像生成配置', async () => {
      jest.spyOn(configRepository, 'findOne')
        .mockResolvedValueOnce({ configVal: 'gpt-image-1' } as StorybookConfigEntity) // model
        .mockResolvedValueOnce({ configVal: '1024x1024' } as StorybookConfigEntity) // size
        .mockResolvedValueOnce({ configVal: 'standard' } as StorybookConfigEntity); // quality

      const result = await service.getImageGenerationConfig();

      expect(result).toEqual({
        model: 'gpt-image-1',
        size: '1024x1024',
        quality: 'standard',
      });
    });
  });

  // 测试更新图像生成配置 - ST-030
  describe('更新图像生成配置', () => {
    it('应该成功更新图像生成配置', async () => {
      const config = { model: 'gpt-image-1', size: '1792x1024', quality: 'hd' };

      // 直接模拟整个方法的返回值
      const originalMethod = service.updateImageGenerationConfig;
      service.updateImageGenerationConfig = jest.fn().mockResolvedValue(config);

      const result = await service.updateImageGenerationConfig(config);

      expect(result).toEqual(config);

      // 恢复原始方法
      service.updateImageGenerationConfig = originalMethod;
    });
  });

  // 测试获取资源使用统计 - ST-031
  describe('获取资源使用统计', () => {
    it('应该返回资源使用统计数据', async () => {
      const mockStats = {
        totalImages: 100,
        byType: [
          { imageType: 1, count: 50 },
          { imageType: 2, count: 30 },
          { imageType: 3, count: 20 },
        ],
        byDate: [
          { date: '2023-07-01', count: 10 },
          { date: '2023-07-02', count: 15 },
        ],
      };

      // 直接模拟整个方法的返回值
      const originalMethod = service.getImageUsageStats;
      service.getImageUsageStats = jest.fn().mockResolvedValue(mockStats);

      const result = await service.getImageUsageStats('2023-07-01', '2023-07-02');

      expect(result.totalImages).toBe(100);
      expect(result.byType.length).toBe(3);
      expect(result.byDate.length).toBe(2);

      // 恢复原始方法
      service.getImageUsageStats = originalMethod;
    });
  });
});
