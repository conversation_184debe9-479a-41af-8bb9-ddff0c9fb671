<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { FormTemplate, getAllTemplates, getAllCategories } from '@/utils/formTemplates';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 所有模板
const templates = ref<FormTemplate[]>([]);

// 所有分类
const categories = ref<string[]>([]);

// 当前选中的分类
const currentCategory = ref<string>('');

// 搜索关键词
const searchKeyword = ref('');

// 加载模板
function loadTemplates() {
  templates.value = getAllTemplates();
  categories.value = getAllCategories();

  if (categories.value.length > 0) {
    currentCategory.value = categories.value[0];
  }
}

// 过滤后的模板
const filteredTemplates = computed(() => {
  let result = templates.value;

  // 按分类过滤
  if (currentCategory.value) {
    result = result.filter(template => template.category === currentCategory.value);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(template =>
      template.name.toLowerCase().includes(keyword) ||
      template.description.toLowerCase().includes(keyword)
    );
  }

  return result;
});

// 选择模板
function selectTemplate(template: FormTemplate) {
  emit('select', template);
  dialogVisible.value = false;
  ElMessage.success(`已选择模板: ${template.name}`);
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false;
}

// 组件挂载时加载模板
onMounted(() => {
  loadTemplates();
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择表单模板"
    width="800px"
    destroy-on-close
  >
    <div class="form-template-dialog">
      <!-- 搜索和分类过滤 -->
      <div class="form-template-filter">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索模板"
          clearable
          prefix-icon="Search"
        />

        <div class="form-template-categories">
          <el-radio-group v-model="currentCategory" size="small">
            <el-radio-button v-for="category in categories" :key="category" :value="category">
              {{ category }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 模板列表 -->
      <div class="form-template-list">
        <el-empty v-if="filteredTemplates.length === 0" description="没有找到匹配的模板" />

        <div v-else class="template-cards">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-card"
            @click="selectTemplate(template)"
          >
            <div class="template-card-header">
              <h3 class="template-card-title">{{ template.name }}</h3>
              <el-tag size="small">{{ template.category }}</el-tag>
            </div>

            <div class="template-card-content">
              <p class="template-card-description">{{ template.description }}</p>
              <div class="template-card-fields">
                <span class="fields-count">{{ template.fields.length }} 个字段</span>
                <div class="fields-preview">
                  <el-tag
                    v-for="(field, index) in template.fields.slice(0, 3)"
                    :key="index"
                    size="small"
                    class="field-tag"
                  >
                    {{ field.label }}
                  </el-tag>
                  <el-tag v-if="template.fields.length > 3" size="small" type="info">
                    +{{ template.fields.length - 3 }} 个字段
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="template-card-footer">
              <el-button type="primary" size="small">使用此模板</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.form-template-dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-template-filter {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-template-categories {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.template-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  background-color: #fff;
  cursor: pointer;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: #409eff;
}

.template-card-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-card-content {
  padding: 16px;
}

.template-card-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.template-card-fields {
  margin-top: 12px;
}

.fields-count {
  display: block;
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.fields-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.field-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.template-card-footer {
  padding: 12px 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
}
</style>
