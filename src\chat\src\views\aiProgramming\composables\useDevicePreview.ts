import { Computer, Ipad, Phone } from '@icon-park/vue-next';
import { computed, ref, type Ref } from 'vue';

// 设备预览相关状态和功能
export function useDevicePreview() {
    // 当前设备类型
    const deviceType = ref<'mobile' | 'tablet' | 'desktop'>('desktop');

    // 是否横屏
    const isLandscape = ref(false);

    // 设备自适应缩放比例
    const deviceScale = ref(1);

    // 设备预设配置
    const devicePresets = {
        mobile: {
            name: '手机',
            icon: Phone,
            width: '375px',
            height: '667px',
            portraitClass: 'w-[375px] h-[667px]',
            landscapeClass: 'w-[667px] h-[375px]',
            // 实际物理尺寸 (用于计算缩放比例)
            physicalWidth: 375,
            physicalHeight: 667,
            // 设备内边距和边框 (用于计算实际内容区域)
            borderWidth: 8,
        },
        tablet: {
            name: '平板',
            icon: Ipad,
            width: '768px',
            height: '1024px',
            portraitClass: 'w-[768px] h-[1024px]',
            landscapeClass: 'w-[1024px] h-[768px]',
            // 实际物理尺寸
            physicalWidth: 768,
            physicalHeight: 1024,
            // 设备内边距和边框
            borderWidth: 12,
        },
        desktop: {
            name: '电脑',
            icon: Computer,
            width: '100%',
            height: '100%',
            portraitClass: 'w-full h-full desktop-frame',
            landscapeClass: 'w-full h-full desktop-frame',
            // 桌面模式不需要考虑物理尺寸
            physicalWidth: 0,
            physicalHeight: 0,
            borderWidth: 1,
        }
    };

    // 计算当前设备的物理尺寸 (考虑横竖屏)
    const physicalDimensions = computed(() => {
        const preset = devicePresets[deviceType.value];
        if (deviceType.value === 'desktop') {
            return { width: 0, height: 0 };
        }

        if (isLandscape.value) {
            return {
                width: preset.physicalHeight,
                height: preset.physicalWidth
            };
        } else {
            return {
                width: preset.physicalWidth,
                height: preset.physicalHeight
            };
        }
    });

    // 当前设备样式
    const currentDeviceStyle = computed(() => {
        const preset = devicePresets[deviceType.value];

        // 桌面模式直接返回预设的尺寸
        if (deviceType.value === 'desktop') {
            return {
                width: preset.width,
                height: preset.height,
            };
        }

        // 横屏模式交换宽高
        if (isLandscape.value) {
            return {
                width: preset.height,
                height: preset.width,
                transform: `scale(${deviceScale.value})`,
                transformOrigin: 'center center',
            };
        }

        // 竖屏模式使用原始尺寸
        return {
            width: preset.width,
            height: preset.height,
            transform: `scale(${deviceScale.value})`,
            transformOrigin: 'center center',
        };
    });

    // 预览容器类
    const previewContainerClass = computed(() => {
        if (deviceType.value === 'desktop') {
            return '';
        }

        const scaleClass = deviceScale.value !== 1 ? 'auto-scaled-device' : '';

        const orientationClass = isLandscape.value
            ? devicePresets[deviceType.value].landscapeClass
            : devicePresets[deviceType.value].portraitClass;

        return `${orientationClass} ${scaleClass}`;
    });

    // 切换设备类型
    const changeDeviceType = (type: 'mobile' | 'tablet' | 'desktop') => {
        deviceType.value = type;
    };

    // 切换横竖屏
    const toggleOrientation = () => {
        // 只允许手机和平板设备切换横竖屏
        if (deviceType.value !== 'desktop') {
            isLandscape.value = !isLandscape.value;
        }
    };

    // 刷新预览 - 需要传入previewIframeRef
    const refreshPreview = (iframe: HTMLIFrameElement) => {
        if (iframe) {
            const content = iframe.srcdoc;
            iframe.srcdoc = "";
            setTimeout(() => {
                iframe.srcdoc = content;
            }, 10);
        }
    };

    // 计算适合容器的设备缩放比例
    const calculateDeviceScale = (containerWidth: number, containerHeight: number) => {
        // 桌面模式不需要缩放
        if (deviceType.value === 'desktop') {
            deviceScale.value = 1;
            return;
        }

        const { width, height } = physicalDimensions.value;
        if (width === 0 || height === 0) return;

        // 计算设备在容器中的最大可能缩放比例
        // 减去一些边距，确保有足够的空间
        const containerPadding = 40;  // 容器内边距总和
        let maxWidthScale = (containerWidth - containerPadding) / width;
        let maxHeightScale = (containerHeight - containerPadding) / height;

        // 平板竖屏模式特殊处理：高度按照容器高度的90%进行缩放
        if (deviceType.value === 'tablet' && !isLandscape.value) {
            // 计算目标高度为容器高度的90%
            const targetHeight = containerHeight * 0.9;
            // 根据目标高度计算缩放比例
            maxHeightScale = targetHeight / height;
        }

        // 选择较小的缩放比例以确保设备完全可见
        const newScale = Math.min(maxWidthScale, maxHeightScale, 1);

        // 设置最小缩放比例，防止设备太小
        deviceScale.value = Math.max(newScale, 0.3);
    };

    return {
        deviceType,
        isLandscape,
        devicePresets,
        currentDeviceStyle,
        previewContainerClass,
        changeDeviceType,
        toggleOrientation,
        refreshPreview,
        calculateDeviceScale,
        deviceScale,
        physicalDimensions
    };
}

// 为了在需要结合全屏模式的地方使用
export function useDeviceWithFullscreen(
    deviceType: Ref<'mobile' | 'tablet' | 'desktop'>,
    isLandscape: Ref<boolean>,
    isFullscreen: Ref<boolean>
) {
    // 设备预设配置
    const devicePresets = {
        mobile: {
            width: '375px',
            height: '667px',
            physicalWidth: 375,
            physicalHeight: 667,
        },
        tablet: {
            width: '768px',
            height: '1024px',
            physicalWidth: 768,
            physicalHeight: 1024,
        },
        desktop: {
            width: '100%',
            height: '100%',
            physicalWidth: 0,
            physicalHeight: 0,
        }
    };

    // 结合全屏模式的当前设备样式
    const currentDeviceStyle = computed(() => {
        const preset = devicePresets[deviceType.value];

        // 桌面模式直接返回预设的尺寸
        if (deviceType.value === 'desktop') {
            return {
                width: preset.width,
                height: preset.height
            };
        }

        // 全屏模式下的特殊处理
        if (isFullscreen.value) {
            // 自动计算最适合全屏的缩放系数
            return {
                width: isLandscape.value ? preset.height : preset.width,
                height: isLandscape.value ? preset.width : preset.height,
                transform: 'scale(var(--device-scale, 1))',
                transformOrigin: 'center center',
            };
        }

        // 横屏模式交换宽高
        if (isLandscape.value) {
            return {
                width: preset.height,
                height: preset.width,
                transform: 'scale(var(--device-scale, 1))',
                transformOrigin: 'center center',
            };
        }

        // 竖屏模式使用原始尺寸
        return {
            width: preset.width,
            height: preset.height,
            transform: 'scale(var(--device-scale, 1))',
            transformOrigin: 'center center',
        };
    });

    // 计算设备在全屏容器中的最佳缩放比例
    const calculateFullscreenScale = (containerWidth: number, containerHeight: number) => {
        if (deviceType.value === 'desktop') return 1;

        const preset = devicePresets[deviceType.value];
        const deviceWidth = isLandscape.value ? preset.physicalHeight : preset.physicalWidth;
        const deviceHeight = isLandscape.value ? preset.physicalWidth : preset.physicalHeight;

        // 全屏状态下，预留更多空间用于控制UI
        const containerPadding = 80;
        const maxWidthScale = (containerWidth - containerPadding) / deviceWidth;
        const maxHeightScale = (containerHeight - containerPadding) / deviceHeight;

        // 选择较小的缩放比例
        const scale = Math.min(maxWidthScale, maxHeightScale, 1);

        // 设置最小值，确保不会过小
        return Math.max(scale, 0.3);
    };

    // 添加全屏模式下的屏幕方向切换动画
    const handleOrientationChangeInFullscreen = (previewFrame: Element | null) => {
        if (isFullscreen.value && previewFrame) {
            // 添加过渡类
            previewFrame.classList.add('rotating');

            // 延迟移除类以允许过渡完成
            setTimeout(() => {
                previewFrame.classList.remove('rotating');
            }, 500);
        }
    };

    return {
        currentDeviceStyle,
        handleOrientationChangeInFullscreen,
        calculateFullscreenScale
    };
} 