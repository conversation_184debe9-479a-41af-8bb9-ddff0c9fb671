<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { utcToShanghaiTime } from '@/utils/utcformatTime';
import { QUESTION_STATUS_MAP } from '@/constants/index';

const props = defineProps({
  app: {
    type: Object,
    required: true
  },
  isSystemRole: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['edit', 'delete']);

// 应用状态标签类型
const statusTagType = computed(() => {
  return props.app.status === 1 ? 'success' : 'danger';
});

// 应用状态标签文本
const statusTagText = computed(() => {
  return QUESTION_STATUS_MAP[props.app.status] || '未知';
});

// 应用创建时间
const createdTime = computed(() => {
  return utcToShanghaiTime(props.app.createdAt, 'YYYY-MM-DD');
});

// 应用描述（截断）
const shortDescription = computed(() => {
  if (!props.app.des) return '';
  return props.app.des.length > 50 ? props.app.des.substring(0, 50) + '...' : props.app.des;
});

// 应用预设（截断）
const shortPreset = computed(() => {
  if (!props.app.preset) return '';
  return props.app.preset.length > 50 ? props.app.preset.substring(0, 50) + '...' : props.app.preset;
});

// 编辑应用
function handleEdit() {
  emit('edit', props.app);
}

// 删除应用
function handleDelete() {
  ElMessageBox.confirm('确认删除此应用吗？此操作不可恢复！', '删除确认', {
    confirmButtonText: '确认删除',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    emit('delete', props.app);
  }).catch(() => {
    // 用户取消删除
  });
}

// 显示完整预设
function showFullPreset() {
  ElMessageBox.alert(props.app.preset, '应用预设', {
    confirmButtonText: '关闭',
    dangerouslyUseHTMLString: false
  });
}

// 显示完整描述
function showFullDescription() {
  ElMessageBox.alert(props.app.des, '应用描述', {
    confirmButtonText: '关闭',
    dangerouslyUseHTMLString: false
  });
}
</script>

<template>
  <div class="app-card">
    <div class="app-card-header">
      <div class="app-card-avatar">
        <div v-if="app.coverImg && app.coverImg.startsWith('emoji:')" class="emoji-avatar">
          {{ app.coverImg.replace('emoji:', '') }}
        </div>
        <el-image v-else :src="app.coverImg" fit="cover" />
      </div>
      <div class="app-card-title">
        <h3>{{ app.name }}</h3>
        <div class="app-card-meta">
          <el-tag size="small" :type="statusTagType">{{ statusTagText }}</el-tag>
          <span class="app-card-category">{{ app.catName }}</span>
          <span class="app-card-date">{{ createdTime }}</span>
        </div>
      </div>
    </div>

    <div class="app-card-content">
      <div class="app-card-description" @click="showFullDescription">
        <span class="label">描述：</span>
        <span class="text">{{ shortDescription }}</span>
      </div>

      <div class="app-card-preset" @click="showFullPreset">
        <span class="label">预设：</span>
        <span class="text">{{ shortPreset }}</span>
      </div>

      <div class="app-card-info">
        <div class="app-card-info-item">
          <span class="label">排序：</span>
          <span class="text">{{ app.order }}</span>
        </div>

        <div class="app-card-info-item" v-if="app.isGPTs === 1">
          <span class="label">GPTs ID：</span>
          <span class="text">{{ app.gizmoID || '无' }}</span>
        </div>

        <div class="app-card-info-item" v-if="app.isFixedModel === 1">
          <span class="label">模型：</span>
          <span class="text">{{ app.appModel || '无' }}</span>
        </div>
      </div>
    </div>

    <div class="app-card-actions">
      <el-button
        v-if="app.role === 'system' || app.public"
        type="primary"
        size="small"
        @click="handleEdit"
      >
        编辑
      </el-button>

      <el-button
        v-if="isSystemRole"
        type="danger"
        size="small"
        @click="handleDelete"
      >
        删除
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.app-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s;
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.app-card-header {
  display: flex;
  margin-bottom: 16px;
}

.app-card-avatar {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.app-card-avatar .el-image {
  width: 100%;
  height: 100%;
}

.emoji-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.app-card-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.app-card-title h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.app-card-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.app-card-category {
  font-size: 12px;
  color: #606266;
}

.app-card-date {
  font-size: 12px;
  color: #909399;
}

.app-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.app-card-description,
.app-card-preset {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.app-card-description:hover,
.app-card-preset:hover {
  background-color: #f5f7fa;
}

.app-card-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.app-card-info-item {
  font-size: 12px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.label {
  font-weight: 600;
  color: #606266;
}

.text {
  color: #303133;
}

.app-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
