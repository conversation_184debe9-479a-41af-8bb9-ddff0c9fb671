<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { <PERSON><PERSON>rawer, NDrawerContent, NButton, NInput, NTabs, NTabPane, NTag, NSpace, NSpin, NList, NListItem, NCard, NEmpty } from 'naive-ui';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import { fetchChatAPIProcess } from '@/api';
import { useAuthStore } from '@/store';

const props = defineProps<{
  projectData: any;
}>();

// 抽屉状态
const showDrawer = ref(false);
const activeTab = ref('notes');
const isExpanded = ref(false);
const botMessage = ref('有灵感了吗？点击记录下来！');

// 笔记状态
const notes = ref(props.projectData.notes || '');
const inspirations = ref<string[]>(props.projectData.inspirations || []);
const newInspiration = ref('');
const autoSaveTimeout = ref(null);

// 笔记类型
const noteTypes = [
  { id: 'notes', label: '笔记本', emoji: '📝' },
  { id: 'inspirations', label: '灵感收集', emoji: '💡' },
  { id: 'ai-search', label: 'AI资料查询', emoji: '🔍' }
];

// AI资料查询状态
const authStore = useAuthStore();
const searchQuery = ref('');
const isSearching = ref(false);
const searchResults = ref([]);
const selectedResult = ref(null);
const searchError = ref('');
const controller = ref(null);

// 自动保存笔记
const autoSaveNotes = () => {
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }

  autoSaveTimeout.value = setTimeout(() => {
    props.projectData.notes = notes.value;
    props.projectData.inspirations = inspirations.value;
  }, 2000);
};

// 添加灵感
const addInspiration = () => {
  if (newInspiration.value.trim()) {
    inspirations.value.push(newInspiration.value.trim());
    newInspiration.value = '';
    autoSaveNotes();
  }
};

// 删除灵感
const removeInspiration = (index: number) => {
  inspirations.value.splice(index, 1);
  autoSaveNotes();
};

// 监听笔记变化
watch(notes, () => {
  autoSaveNotes();
});

// 监听抽屉状态
watch(showDrawer, (newVal) => {
  if (newVal) {
    // 打开抽屉时，更新笔记内容
    notes.value = props.projectData.notes || '';
    inspirations.value = props.projectData.inspirations || [];
  } else {
    // 关闭抽屉时，保存笔记内容
    props.projectData.notes = notes.value;
    props.projectData.inspirations = inspirations.value;
  }
});

// 切换机器人展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;

  if (isExpanded.value) {
    const messages = [
      '有什么创意想法吗？',
      '需要记录灵感吗？',
      '有趣的故事点子？',
      '记下你的创作思路！',
      '别让灵感溜走！'
    ];
    botMessage.value = messages[Math.floor(Math.random() * messages.length)];
  }
};

// 打开笔记抽屉
const openNotes = () => {
  showDrawer.value = true;
  isExpanded.value = false;
};

// 灵感提示列表
const inspirationPrompts = [
  '如果主角是一只会飞的猫...',
  '故事发生在一个永远不会下雨的世界...',
  '主角发现了一个神奇的门...',
  '一个失去颜色的王国...',
  '一个能听懂动物语言的孩子...',
  '一棵会走路的树...',
  '一个永远向前走的小女孩...',
  '一个装满星星的口袋...'
];

// 使用提示作为灵感
const usePromptAsInspiration = (prompt: string) => {
  newInspiration.value = prompt;
};

// 执行AI资料查询
const searchWithAI = async () => {
  if (!searchQuery.value.trim()) {
    window.$message?.warning('请输入搜索内容');
    return;
  }

  try {
    isSearching.value = true;
    searchError.value = '';
    searchResults.value = [];

    // 创建AbortController用于取消请求
    if (controller.value) controller.value.abort();
    controller.value = new AbortController();

    // 构建请求参数
    const params = {
      model: authStore.currentChat?.model || 'gpt-3.5-turbo',
      modelName: authStore.currentChat?.modelName || 'GPT-3.5',
      modelType: 1,
      modelAvatar: '',
      prompt: searchQuery.value,
      signal: controller.value.signal,
      options: {
        groupId: 0,
        usingNetwork: true,
        pluginParam: 'net-search'
      },
      onDownloadProgress: (progressEvent: ProgressEvent) => {
        // 处理流式响应
        const text = progressEvent.target.responseText;
        try {
          // 尝试解析JSON响应
          const data = JSON.parse(text);
          if (data.searchResults && Array.isArray(data.searchResults)) {
            searchResults.value = data.searchResults;
          }
        } catch (e) {
          // 如果不是有效的JSON，可能是流式响应的一部分
          console.log('处理中...');
        }
      }
    };

    // 发送请求
    await fetchChatAPIProcess(params);

  } catch (error) {
    console.error('搜索失败:', error);
    searchError.value = '搜索失败，请稍后重试';
  } finally {
    isSearching.value = false;
    controller.value = null;
  }
};

// 将搜索结果添加到笔记
const addResultToNotes = (result) => {
  if (!result) return;

  const resultText = `
📚 资料来源: ${result.title}
🔗 链接: ${result.link}
📝 内容摘要:
${result.content}
`;

  // 添加到笔记中
  notes.value += notes.value ? `\n\n${resultText}` : resultText;
  window.$message?.success('已添加到笔记');

  // 切换到笔记标签页
  activeTab.value = 'notes';

  // 自动保存
  autoSaveNotes();
};

// 清空搜索结果
const clearSearchResults = () => {
  searchResults.value = [];
  searchQuery.value = '';
};
</script>

<template>
  <div class="floating-note-bot">
    <!-- 悬浮机器人按钮 -->
    <div
      class="bot-button"
      :class="{ 'expanded': isExpanded }"
      @click="toggleExpand"
    >
      <div class="bot-icon">
        <span class="emoji-icon">🤖</span>
      </div>
      <div v-if="isExpanded" class="bot-message">
        <p>{{ botMessage }}</p>
        <NButton size="small" type="primary" @click.stop="openNotes">
          打开笔记
        </NButton>
      </div>
    </div>

    <!-- 笔记抽屉 -->
    <NDrawer v-model:show="showDrawer" placement="right" :width="320">
      <NDrawerContent title="创作笔记" closable>
        <NTabs v-model:value="activeTab" type="line" animated>
          <NTabPane
            v-for="type in noteTypes"
            :key="type.id"
            :name="type.id"
          >
            <template #tab>
              <div class="tab-label">
                <span class="emoji-icon">{{ type.emoji }}</span>
                <span>{{ type.label }}</span>
              </div>
            </template>
          </NTabPane>
        </NTabs>

        <div class="drawer-content">
          <!-- 笔记本 -->
          <div v-if="activeTab === 'notes'" class="notes-container">
            <div class="notes-paper">
              <textarea
                v-model="notes"
                class="paper-content"
                placeholder="在这里记录你的创作笔记、想法和灵感..."
              ></textarea>
            </div>
          </div>

          <!-- 灵感收集 -->
          <div v-else-if="activeTab === 'inspirations'" class="inspirations-container">
            <div class="add-inspiration">
              <NInput
                v-model:value="newInspiration"
                type="text"
                placeholder="添加一个新的灵感..."
                @keyup.enter="addInspiration"
              >
                <template #suffix>
                  <NButton type="primary" ghost @click="addInspiration">
                    <span class="emoji-icon">➕</span>
                  </NButton>
                </template>
              </NInput>
            </div>

            <div class="inspirations-board">
              <div v-if="inspirations.length === 0" class="empty-inspirations">
                <span class="emoji-icon large-emoji">💡</span>
                <p>还没有添加灵感，开始记录你的创意吧！</p>
              </div>

              <div v-else class="inspiration-notes">
                <div
                  v-for="(inspiration, index) in inspirations"
                  :key="index"
                  class="inspiration-note"
                  :style="{ '--index': index }"
                >
                  <div class="note-content">{{ inspiration }}</div>
                  <NButton
                    size="tiny"
                    quaternary
                    class="remove-button"
                    @click="removeInspiration(index)"
                  >
                    <span class="emoji-icon small-emoji">❌</span>
                  </NButton>
                </div>
              </div>
            </div>

            <div class="inspiration-prompts">
              <h3 class="prompts-title">灵感提示</h3>
              <div class="prompts-list">
                <NTag
                  v-for="(prompt, index) in inspirationPrompts"
                  :key="index"
                  class="prompt-tag"
                  type="info"
                  size="medium"
                  @click="usePromptAsInspiration(prompt)"
                >
                  {{ prompt }}
                </NTag>
              </div>
            </div>
          </div>

          <!-- AI资料查询 -->
          <div v-else-if="activeTab === 'ai-search'" class="ai-search-container">
            <div class="search-input-container">
              <NInput
                v-model:value="searchQuery"
                type="text"
                placeholder="输入要查询的内容..."
                @keyup.enter="searchWithAI"
              >
                <template #suffix>
                  <NButton
                    type="primary"
                    ghost
                    @click="searchWithAI"
                    :loading="isSearching"
                    :disabled="isSearching || !searchQuery.trim()"
                  >
                    <span class="emoji-icon">🔍</span>
                  </NButton>
                </template>
              </NInput>
              <p class="search-tips">提示：输入关于绘本创作、儿童教育等相关问题</p>
            </div>

            <div class="search-results-container">
              <!-- 加载中 -->
              <div v-if="isSearching" class="search-loading">
                <NSpin size="large" />
                <p>正在搜索相关资料...</p>
              </div>

              <!-- 错误信息 -->
              <div v-else-if="searchError" class="search-error">
                <span class="emoji-icon large-emoji">⚠️</span>
                <p>{{ searchError }}</p>
                <NButton size="small" @click="searchWithAI">重试</NButton>
              </div>

              <!-- 空结果 -->
              <div v-else-if="searchResults.length === 0 && searchQuery" class="search-empty">
                <NEmpty description="暂无搜索结果">
                  <template #extra>
                    <p>尝试使用不同的关键词搜索</p>
                  </template>
                </NEmpty>
              </div>

              <!-- 搜索结果 -->
              <div v-else-if="searchResults.length > 0" class="search-results">
                <div class="results-header">
                  <h3 class="results-title">搜索结果</h3>
                  <NButton size="tiny" text @click="clearSearchResults">
                    清空结果
                  </NButton>
                </div>

                <NList hoverable clickable>
                  <NListItem
                    v-for="result in searchResults"
                    :key="result.resultIndex"
                    @click="selectedResult = result"
                  >
                    <div class="result-item">
                      <h4 class="result-title">{{ result.title }}</h4>
                      <p class="result-content">{{ result.content.substring(0, 100) }}...</p>
                      <div class="result-actions">
                        <NButton size="tiny" @click.stop="addResultToNotes(result)">
                          添加到笔记
                        </NButton>
                        <NButton size="tiny" tag="a" :href="result.link" target="_blank" @click.stop>
                          查看原文
                        </NButton>
                      </div>
                    </div>
                  </NListItem>
                </NList>
              </div>

              <!-- 初始状态 -->
              <div v-else class="search-initial">
                <span class="emoji-icon large-emoji">🔍</span>
                <p>输入关键词，使用AI查找相关资料</p>
                <div class="search-examples">
                  <p>示例搜索：</p>
                  <NTag
                    v-for="(example, index) in [
                      '儿童绘本创作技巧',
                      '如何设计绘本角色',
                      '绘本故事结构',
                      '儿童认知发展阶段'
                    ]"
                    :key="index"
                    class="example-tag"
                    @click="searchQuery = example; searchWithAI()"
                  >
                    {{ example }}
                  </NTag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.floating-note-bot {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
}

.bot-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: #3b82f6;
  color: white;
  overflow: hidden;
  max-width: 50px;
}

.bot-button.expanded {
  max-width: 300px;
  border-radius: 12px;
  padding-right: 16px;
}

.bot-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #3b82f6;
  position: relative;
  z-index: 2;
}

.bot-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(59, 130, 246, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.bot-message {
  padding: 0 8px;
  white-space: nowrap;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bot-message p {
  margin: 0;
  font-size: 14px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.emoji-icon {
  font-size: 18px;
  line-height: 1;
}

.large-emoji {
  font-size: 48px;
  margin-bottom: 12px;
}

.small-emoji {
  font-size: 12px;
}

.drawer-content {
  height: calc(100vh - 150px);
  overflow-y: auto;
  padding: 16px 0;
}

/* 笔记本样式 */
.notes-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.notes-paper {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff9c4;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  min-height: 400px;
}

.dark .notes-paper {
  background-color: #2d3748;
}

.paper-content {
  flex: 1;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  background-color: transparent;
  border: none;
  resize: none;
  min-height: 350px;
  color: #5d4037;
  background-image: linear-gradient(#ffefd5 1px, transparent 1px);
  background-size: 100% 1.6rem;
  line-height: 1.6rem;
}

.dark .paper-content {
  color: #e2e8f0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

.paper-content:focus {
  outline: none;
}

/* 灵感收集样式 */
.inspirations-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.add-inspiration {
  margin-bottom: 1rem;
}

.inspirations-board {
  flex: 1;
  min-height: 200px;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.dark .inspirations-board {
  background-color: #1e293b;
}

.empty-inspirations {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.inspiration-notes {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.inspiration-note {
  position: relative;
  width: 100%;
  min-height: 60px;
  padding: 0.75rem;
  background-color: #fbec8f;
  border-radius: 0.25rem;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  transform: rotate(var(--rotation));
  --rotation: calc(var(--index) * 1deg - 1.5deg);
}

.dark .inspiration-note {
  background-color: #4a5568;
}

.note-content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #5d4037;
}

.dark .note-content {
  color: #e2e8f0;
}

.remove-button {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.inspiration-note:hover .remove-button {
  opacity: 1;
}

.inspiration-prompts {
  margin-top: 0.5rem;
}

.prompts-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.dark .prompts-title {
  color: #e2e8f0;
}

.prompts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.prompt-tag {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.prompt-tag:hover {
  transform: translateY(-2px);
}

/* AI资料查询样式 */
.ai-search-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.search-input-container {
  margin-bottom: 0.5rem;
}

.search-tips {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.dark .search-tips {
  color: #94a3b8;
}

.search-results-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  min-height: 300px;
}

.dark .search-results-container {
  background-color: #1e293b;
}

.search-loading,
.search-error,
.search-empty,
.search-initial {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  text-align: center;
  color: #64748b;
}

.dark .search-loading,
.dark .search-error,
.dark .search-empty,
.dark .search-initial {
  color: #94a3b8;
}

.search-examples {
  margin-top: 1rem;
  width: 100%;
}

.example-tag {
  margin: 0.25rem;
  cursor: pointer;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.results-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.dark .results-title {
  color: #e2e8f0;
}

.result-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.result-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.dark .result-title {
  color: #e2e8f0;
}

.result-content {
  font-size: 0.75rem;
  margin: 0;
  color: #64748b;
  line-height: 1.4;
}

.dark .result-content {
  color: #94a3b8;
}

.result-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
}
</style>
