{"name": "deepcreate-chat", "version": "4.1.0", "private": true, "description": "DeepCreate chat", "author": "vastxie", "keywords": ["aiweb", "chatgpt", "chatbot", "chatweb", "vue", "<PERSON><PERSON><PERSON>"], "main": "electron/main.js", "scripts": {"start:h": "pnpm run -C service dev", "start:f": "vite", "all": "npm-run-all --parallel start:h start:f", "dev": "vite", "build-check": "run-p type-check build-only", "preview": "vite preview", "build": "vite build --mode=production", "type-check": "vue-tsc --noEmit", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.3", "@codemirror/state": "^6.5.2", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.1", "@icon-park/vue-next": "^1.4.2", "@tailwindcss/typography": "^0.5.10", "@traptitech/markdown-it-katex": "^3.6.0", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^9.13.0", "@vueuse/integrations": "^10.2.0", "@vueuse/motion": "^2.1.0", "browserslist": "^4.24.4", "clientjs": "^0.2.1", "codemirror": "^6.0.1", "file-saver": "^2.0.5", "highlight.js": "^11.7.0", "html-to-image": "^1.11.11", "jszip": "^3.10.1", "katex": "^0.16.4", "markdown-it": "^13.0.1", "markmap-common": "0.14.2", "markmap-lib": "0.14.4", "markmap-view": "0.14.4", "md-editor-v3": "^4.17.4", "naive-ui": "^2.34.3", "npx": "^10.2.2", "pinia": "^2.0.33", "pinyin-match": "^1.2.6", "qrcode": "^1.5.3", "tailwind-scrollbar": "^3.1.0", "v-viewer": "3.0.11", "vue": "^3.4.30", "vue-i18n": "^11.1.1", "vue-router": "^4.1.6", "vue3-pdfjs": "^0.1.6", "xml2js": "^0.6.2"}, "devDependencies": {"@iconify/vue": "^4.1.0", "@types/crypto-js": "^4.1.1", "@types/katex": "^0.16.0", "@types/node": "^18.14.6", "@vitejs/plugin-vue": "^5.0.5", "axios": "^1.7.2", "crypto-js": "^4.1.1", "less": "^4.1.3", "lint-staged": "^13.1.2", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "rimraf": "^4.2.0", "tailwindcss": "^3.4.1", "terser": "^5.31.1", "typescript": "~4.9.5", "update-browserslist-db": "^1.1.2", "vite": "^4.2.0", "vue-tsc": "^1.2.0"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}, "license": "MIT"}