<template>
  <div class="my-works-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">我的作品集</h1>
          <p class="page-description">这里是你所有创作故事的展示空间</p>
        </div>
        
        <div class="header-actions">
          <NButton 
            type="primary" 
            @click="createNewWork"
            class="create-btn"
          >
            <template #icon>
              <NIcon><CreateOutline /></NIcon>
            </template>
            创作新故事
          </NButton>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <NGrid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard class="stat-card">
            <NStatistic label="总作品数" :value="workStats.total">
              <template #prefix>
                <NIcon class="stat-icon total">
                  <BookOutline />
                </NIcon>
              </template>
            </NStatistic>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <NStatistic label="已完成" :value="workStats.completed">
              <template #prefix>
                <NIcon class="stat-icon completed">
                  <CheckmarkCircleOutline />
                </NIcon>
              </template>
            </NStatistic>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <NStatistic label="获得点赞" :value="workStats.likes">
              <template #prefix>
                <NIcon class="stat-icon likes">
                  <HeartOutline />
                </NIcon>
              </template>
            </NStatistic>
          </NCard>
        </NGridItem>
        
        <NGridItem>
          <NCard class="stat-card">
            <NStatistic label="总浏览量" :value="workStats.views">
              <template #prefix>
                <NIcon class="stat-icon views">
                  <EyeOutline />
                </NIcon>
              </template>
            </NStatistic>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 作品筛选和视图切换 -->
    <div class="filter-section">
      <div class="filter-left">
        <NSpace>
          <NSelect
            v-model:value="filterStatus"
            :options="statusOptions"
            placeholder="筛选状态"
            style="width: 120px;"
            @update:value="handleFilterChange"
          />
          <NSelect
            v-model:value="sortBy"
            :options="sortOptions"
            placeholder="排序方式"
            style="width: 140px;"
            @update:value="handleSortChange"
          />
        </NSpace>
      </div>
      
      <div class="filter-right">
        <NButtonGroup>
          <NButton
            :type="viewMode === 'grid' ? 'primary' : 'default'"
            @click="viewMode = 'grid'"
          >
            <template #icon>
              <NIcon><GridOutline /></NIcon>
            </template>
          </NButton>
          <NButton
            :type="viewMode === 'list' ? 'primary' : 'default'"
            @click="viewMode = 'list'"
          >
            <template #icon>
              <NIcon><ListOutline /></NIcon>
            </template>
          </NButton>
        </NButtonGroup>
      </div>
    </div>

    <!-- 作品列表区域 -->
    <div class="works-section">
      <div v-if="loading" class="loading-container">
        <NSpin size="large" />
        <p class="loading-text">正在加载作品...</p>
      </div>

      <div v-else-if="filteredWorks.length === 0" class="empty-container">
        <NEmpty description="还没有创作任何作品">
          <template #extra>
            <NButton type="primary" @click="createNewWork">
              开始第一个创作
            </NButton>
          </template>
        </NEmpty>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="grid-view">
        <NGrid :cols="isMobile ? 1 : isTablet ? 2 : 3" :x-gap="20" :y-gap="20">
          <NGridItem v-for="work in filteredWorks" :key="work.id">
            <NCard class="work-card" hoverable @click="viewWork(work)">
              <div class="work-cover">
                <img 
                  v-if="work.coverImage" 
                  :src="work.coverImage" 
                  :alt="work.title"
                  class="cover-image"
                />
                <div v-else class="cover-placeholder">
                  <NIcon size="48"><BookOutline /></NIcon>
                </div>
                
                <div class="work-status">
                  <NTag :type="getStatusType(work.status)" size="small">
                    {{ getStatusText(work.status) }}
                  </NTag>
                </div>
              </div>
              
              <div class="work-info">
                <h3 class="work-title">{{ work.title || '未命名作品' }}</h3>
                <p class="work-description">{{ work.description || '暂无描述' }}</p>
                
                <div class="work-meta">
                  <span class="meta-item">
                    <NIcon><TimeOutline /></NIcon>
                    {{ formatDate(work.updatedAt) }}
                  </span>
                  <span class="meta-item">
                    <NIcon><EyeOutline /></NIcon>
                    {{ work.views || 0 }}
                  </span>
                  <span class="meta-item">
                    <NIcon><HeartOutline /></NIcon>
                    {{ work.likes || 0 }}
                  </span>
                </div>
              </div>
              
              <div class="work-actions">
                <NButton size="small" @click.stop="editWork(work)">
                  编辑
                </NButton>
                <NButton size="small" @click.stop="shareWork(work)">
                  分享
                </NButton>
                <NDropdown
                  :options="getWorkActions(work)"
                  @select="handleWorkAction($event, work)"
                  @click.stop
                >
                  <NButton size="small" quaternary>
                    <template #icon>
                      <NIcon><EllipsisHorizontalOutline /></NIcon>
                    </template>
                  </NButton>
                </NDropdown>
              </div>
            </NCard>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <NList bordered>
          <NListItem v-for="work in filteredWorks" :key="work.id">
            <template #prefix>
              <div class="list-cover">
                <img 
                  v-if="work.coverImage" 
                  :src="work.coverImage" 
                  :alt="work.title"
                />
                <NIcon v-else size="24"><BookOutline /></NIcon>
              </div>
            </template>
            
            <div class="list-content">
              <div class="list-header">
                <h3 class="list-title">{{ work.title || '未命名作品' }}</h3>
                <NTag :type="getStatusType(work.status)" size="small">
                  {{ getStatusText(work.status) }}
                </NTag>
              </div>
              
              <p class="list-description">{{ work.description || '暂无描述' }}</p>
              
              <div class="list-meta">
                <span>更新时间：{{ formatDate(work.updatedAt) }}</span>
                <span>浏览：{{ work.views || 0 }}</span>
                <span>点赞：{{ work.likes || 0 }}</span>
              </div>
            </div>
            
            <template #suffix>
              <NSpace>
                <NButton size="small" @click="viewWork(work)">查看</NButton>
                <NButton size="small" @click="editWork(work)">编辑</NButton>
                <NButton size="small" @click="shareWork(work)">分享</NButton>
              </NSpace>
            </template>
          </NListItem>
        </NList>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredWorks.length > 0" class="pagination-section">
      <NPagination
        v-model:page="currentPage"
        :page-count="totalPages"
        :page-size="pageSize"
        show-size-picker
        :page-sizes="[6, 12, 18, 24]"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import {
  NCard,
  NGrid,
  NGridItem,
  NButton,
  NIcon,
  NStatistic,
  NSpace,
  NSelect,
  NButtonGroup,
  NSpin,
  NEmpty,
  NTag,
  NDropdown,
  NList,
  NListItem,
  NPagination
} from 'naive-ui';
import {
  BookOutline,
  CreateOutline,
  CheckmarkCircleOutline,
  HeartOutline,
  EyeOutline,
  TimeOutline,
  GridOutline,
  ListOutline,
  EllipsisHorizontalOutline
} from '@vicons/ionicons5';

const router = useRouter();
const message = useMessage();
const { isMobile } = useBasicLayout();

// 响应式断点
const isTablet = computed(() => window.innerWidth <= 1024);

// 数据状态
const loading = ref(true);
const workStats = ref({
  total: 0,
  completed: 0,
  likes: 0,
  views: 0
});

// 筛选和排序
const filterStatus = ref('all');
const sortBy = ref('updated');
const viewMode = ref('grid');

// 分页
const currentPage = ref(1);
const pageSize = ref(12);
const totalPages = computed(() => Math.ceil(filteredWorks.value.length / pageSize.value));

// 选项配置
const statusOptions = [
  { label: '全部状态', value: 'all' },
  { label: '草稿', value: 'draft' },
  { label: '已完成', value: 'completed' },
  { label: '已发布', value: 'published' }
];

const sortOptions = [
  { label: '最近更新', value: 'updated' },
  { label: '创建时间', value: 'created' },
  { label: '浏览量', value: 'views' },
  { label: '点赞数', value: 'likes' }
];

// 作品数据（模拟数据，实际应从API获取）
const works = ref([
  {
    id: 1,
    title: '小兔子的冒险',
    description: '一只勇敢的小兔子在森林里遇到了很多朋友的故事',
    coverImage: 'https://api.dicebear.com/7.x/shapes/svg?seed=rabbit',
    status: 'completed',
    views: 156,
    likes: 23,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 2,
    title: '魔法王国',
    description: '在一个充满魔法的王国里，公主学会了使用魔法的故事',
    coverImage: 'https://api.dicebear.com/7.x/shapes/svg?seed=magic',
    status: 'draft',
    views: 89,
    likes: 12,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z'
  },
  {
    id: 3,
    title: '海底世界探险',
    description: '小鱼儿带我们探索神秘的海底世界',
    coverImage: 'https://api.dicebear.com/7.x/shapes/svg?seed=ocean',
    status: 'published',
    views: 234,
    likes: 45,
    createdAt: '2024-01-05T11:30:00Z',
    updatedAt: '2024-01-15T13:20:00Z'
  }
]);

// 筛选后的作品
const filteredWorks = computed(() => {
  let filtered = works.value;
  
  // 状态筛选
  if (filterStatus.value !== 'all') {
    filtered = filtered.filter(work => work.status === filterStatus.value);
  }
  
  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'created':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'likes':
        return (b.likes || 0) - (a.likes || 0);
      default: // 'updated'
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    }
  });
  
  return filtered;
});

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getStatusType = (status: string) => {
  const types = {
    draft: 'default',
    completed: 'success',
    published: 'info'
  };
  return types[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    draft: '草稿',
    completed: '已完成',
    published: '已发布'
  };
  return texts[status] || '未知';
};

const getWorkActions = (work: any) => [
  {
    key: 'duplicate',
    label: '复制作品'
  },
  {
    key: 'export',
    label: '导出作品'
  },
  {
    key: 'delete',
    label: '删除作品'
  }
];

// 事件处理
const createNewWork = () => {
  router.push('/student/storybook');
};

const viewWork = (work: any) => {
  router.push(`/student/storybook/view/${work.id}`);
};

const editWork = (work: any) => {
  router.push(`/student/storybook/edit/${work.id}`);
};

const shareWork = (work: any) => {
  // 分享功能
  navigator.clipboard.writeText(`${window.location.origin}/storybook/share/${work.id}`);
  message.success('分享链接已复制到剪贴板');
};

const handleWorkAction = (key: string, work: any) => {
  switch (key) {
    case 'duplicate':
      message.info('复制功能开发中...');
      break;
    case 'export':
      message.info('导出功能开发中...');
      break;
    case 'delete':
      message.warning('删除功能开发中...');
      break;
  }
};

const handleFilterChange = () => {
  currentPage.value = 1;
};

const handleSortChange = () => {
  currentPage.value = 1;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 计算统计数据
const calculateStats = () => {
  workStats.value = {
    total: works.value.length,
    completed: works.value.filter(work => work.status === 'completed' || work.status === 'published').length,
    likes: works.value.reduce((sum, work) => sum + (work.likes || 0), 0),
    views: works.value.reduce((sum, work) => sum + (work.views || 0), 0)
  };
};

onMounted(() => {
  // 模拟加载
  setTimeout(() => {
    calculateStats();
    loading.value = false;
  }, 1000);
});
</script>

<style scoped lang="scss">
.my-works-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
  }
  
  .title-section {
    flex: 1;
  }
  
  .page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: #22c55e;
  }
  
  .page-description {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
  }
  
  .create-btn {
    @apply bg-green-500 hover:bg-green-600;
  }
}

.stats-section {
  margin-bottom: 24px;
  
  .stat-card {
    text-align: center;
    
    .stat-icon {
      &.total { color: #3b82f6; }
      &.completed { color: #22c55e; }
      &.likes { color: #ef4444; }
      &.views { color: #f59e0b; }
    }
  }
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .filter-right {
    @media (max-width: 768px) {
      display: flex;
      justify-content: center;
    }
  }
}

.works-section {
  margin-bottom: 24px;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
  
  .loading-text {
    margin-top: 16px;
    color: #6b7280;
  }
}

.empty-container {
  text-align: center;
  padding: 60px 20px;
}

.grid-view {
  .work-card {
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: translateY(-4px);
    }
  }
  
  .work-cover {
    position: relative;
    aspect-ratio: 16/9;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 12px;
    
    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .cover-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f3f4f6;
      color: #9ca3af;
    }
    
    .work-status {
      position: absolute;
      top: 8px;
      right: 8px;
    }
  }
  
  .work-info {
    margin-bottom: 12px;
    
    .work-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 1.4;
      @apply text-gray-900;
    }
    
    .work-description {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #6b7280;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .work-meta {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #9ca3af;
      
      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .work-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.list-view {
  .list-cover {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f3f4f6;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .list-content {
    flex: 1;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .list-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .list-description {
      margin: 0 0 8px 0;
      color: #6b7280;
      font-size: 14px;
    }
    
    .list-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #9ca3af;
    }
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

@media (max-width: 768px) {
  .my-works-page {
    padding: 16px;
  }
}
</style> 