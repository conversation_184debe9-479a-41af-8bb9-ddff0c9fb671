<template>
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">我的绘本</h2>
      <p class="page-description">让我们一起创造一个精彩的故事吧！</p>
      <div class="sync-hint">
        <span class="sync-icon">✨</span>
        <span class="sync-text">你的绘本会自动保存，可以随时继续创作</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 无需props或其他逻辑，这是一个纯展示组件
</script>

<style scoped>
.page-header {
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1.5rem;
  border-bottom: 2px dashed #e2e8f0;
  max-width: 100%;
}

.dark .page-header {
  border-bottom-color: #334155;
}

.header-content {
  flex-grow: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #3b82f6;
  letter-spacing: -0.025em;
  position: relative;
  display: inline-block;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 3rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  border-radius: 0.25rem;
}

.dark .page-title {
  color: #60a5fa;
  text-shadow: 2px 2px 0 rgba(0, 0, 0, 0.3);
}

.dark .page-title::after {
  background: linear-gradient(90deg, #8b5cf6, #93c5fd);
}

.page-description {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 1rem;
  max-width: 100%;
  line-height: 1.5;
}

.dark .page-description {
  color: #94a3b8;
}

.sync-hint {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
  padding: 0.75rem 1rem;
  background-color: #f0f9ff;
  border-radius: 0.75rem;
  font-size: 1rem;
  color: #0369a1;
  border-left: 4px solid #0ea5e9;
  max-width: fit-content;
  margin-bottom: 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
  transition: all 0.2s ease;
}

.sync-hint:hover {
  background-color: #e0f2fe;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .sync-hint {
  background-color: #0c4a6e;
  color: #7dd3fc;
  border-left-color: #0ea5e9;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark .sync-hint:hover {
  background-color: #075985;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.sync-icon {
  font-size: 1.25rem;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
