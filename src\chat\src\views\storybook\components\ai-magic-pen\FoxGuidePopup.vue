<template>
  <div class="fox-guide-popup" :style="popupStyle">
    <div class="guide-bubble">
      <div class="guide-header">
        <h3 class="guide-title">{{ title }}</h3>
        <button class="guide-close" @click="$emit('close')">×</button>
      </div>
      <div class="guide-content">
        <p>{{ content }}</p>
      </div>
      <div class="guide-actions">
        <button class="guide-button" @click="$emit('close')">
          知道了
        </button>
        <button class="guide-button primary" @click="openHelp">
          查看更多帮助
        </button>
      </div>
    </div>
    <div class="guide-arrow"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  title: string;
  content: string;
  position: { x: number, y: number };
}>();

const emit = defineEmits(['close', 'help']);

// 计算弹窗位置
const popupStyle = computed(() => {
  // 获取窗口宽度，用于确保弹窗不会超出屏幕
  const windowWidth = window.innerWidth;

  // 计算弹窗左侧位置，确保居中于按钮上方
  let leftPosition = props.position.x - 150 + 60; // 弹窗宽度的一半 + 按钮宽度的一半偏移

  // 确保弹窗不会超出屏幕左侧
  leftPosition = Math.max(20, leftPosition);

  // 确保弹窗不会超出屏幕右侧
  leftPosition = Math.min(windowWidth - 320, leftPosition);

  // 弹窗显示在按钮上方，距离足够远以避免重叠
  return {
    left: `${leftPosition}px`,
    top: `${props.position.y - 250}px` // 增加距离，确保不会与按钮重叠
  };
});

// 打开帮助
const openHelp = () => {
  emit('close');
  // 延迟一下再打开抽屉，避免动画冲突
  setTimeout(() => {
    emit('help');
  }, 100);
};
</script>

<style scoped>
.fox-guide-popup {
  position: fixed;
  z-index: 10002; /* 确保在按钮上方 */
  width: 320px;
  animation: pop-in 0.3s ease-out;
  pointer-events: auto; /* 确保弹窗可以接收点击事件 */
}

@keyframes pop-in {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

.guide-bubble {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
  overflow: hidden;
  border: 2px solid #f97316;
}

.dark .guide-bubble {
  background-color: #1e293b;
  border-color: #ea580c;
  box-shadow: 0 4px 12px rgba(234, 88, 12, 0.3);
}

.guide-header {
  background-color: #f97316;
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guide-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.guide-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.guide-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.guide-content {
  padding: 1rem;
}

.guide-content p {
  margin: 0;
  font-size: 1rem;
  color: #334155;
  line-height: 1.5;
}

.dark .guide-content p {
  color: #e2e8f0;
}

.guide-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 0 1rem 1rem;
}

.guide-button {
  background-color: #f1f5f9;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #334155;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .guide-button {
  background-color: #334155;
  color: #e2e8f0;
}

.guide-button:hover {
  background-color: #e2e8f0;
}

.dark .guide-button:hover {
  background-color: #475569;
}

.guide-button.primary {
  background-color: #f97316;
  color: white;
}

.dark .guide-button.primary {
  background-color: #ea580c;
}

.guide-button.primary:hover {
  background-color: #ea580c;
}

.dark .guide-button.primary:hover {
  background-color: #c2410c;
}

.guide-arrow {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #f97316;
  position: absolute;
  bottom: -10px;
  left: 150px; /* 固定位置，对应按钮中心 */
  transform: translateX(-50%);
}

.dark .guide-arrow {
  border-top-color: #ea580c;
}
</style>
