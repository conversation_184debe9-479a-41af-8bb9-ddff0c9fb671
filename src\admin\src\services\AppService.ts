/**
 * 应用服务类
 * 封装应用创建、更新、删除和查询的逻辑
 */
import ApiApp from '@/api/modules/app';
import FormService from '@/services/FormService';
import { ElMessage } from 'element-plus';

/**
 * 应用数据接口
 */
export interface AppData {
  id?: number;
  catId: string | number;
  name: string;
  preset: string;
  des: string;
  coverImg: string;
  demoData: string;
  order: number;
  status: number;
  isGPTs: number;
  gizmoID: string;
  isFixedModel: number;
  appModel: string;
}

/**
 * 表单数据接口
 */
export interface FormData {
  id?: number;
  name: string;
  description: string;
  appId: number;
  fields: string;
  status: number;
  order: number;
}

/**
 * 应用服务类
 */
export class AppService {
  /**
   * 创建应用
   * @param appData 应用数据
   * @param formData 表单数据
   * @returns 创建的应用ID
   */
  static async createApp(appData: AppData, formData?: FormData): Promise<number> {
    try {
      // 1. 创建应用
      const res = await ApiApp.createApp(appData);
      const appId = res.data.id;

      // 2. 如果有表单数据，创建表单
      if (formData && formData.name && formData.fields) {
        await this.createOrUpdateForm({
          ...formData,
          appId,
          status: 1,
          order: 100
        });
      }

      ElMessage.success('创建新的应用成功！');
      return appId;
    } catch (error) {
      console.error('创建应用失败', error);
      ElMessage.error('创建应用失败！');
      throw error;
    }
  }

  /**
   * 更新应用
   * @param appData 应用数据
   * @param formData 表单数据
   * @param isUserApp 是否是用户应用
   * @param userAppStatus 用户应用状态
   * @returns 更新的应用ID
   */
  static async updateApp(
    appData: AppData,
    formData?: FormData,
    isUserApp: boolean = false,
    userAppStatus: number = 0
  ): Promise<number> {
    try {
      if (!appData.id) {
        throw new Error('应用ID不能为空');
      }

      // 1. 更新应用
      const params = { ...appData };

      // 如果是用户的app 不能修改状态 保持原样返回
      if (isUserApp) {
        params.status = userAppStatus;
      }

      await ApiApp.updateApp(params);
      const appId = appData.id;

      // 2. 处理表单信息
      if (formData) {
        if (formData.name && formData.fields) {
          // 有表单数据，创建或更新表单
          await this.createOrUpdateForm({
            ...formData,
            appId,
            status: 1,
            order: 100
          });
        } else {
          // 没有表单数据，删除表单
          await this.deleteFormByAppId(appId);
        }
      }

      ElMessage.success('更新应用成功！');
      return appId;
    } catch (error) {
      console.error('更新应用失败', error);
      ElMessage.error('更新应用失败！');
      throw error;
    }
  }

  /**
   * 删除应用
   * @param id 应用ID
   */
  static async deleteApp(id: number): Promise<void> {
    try {
      await ApiApp.deleteApp({ id });
      ElMessage.success('删除应用成功！');
    } catch (error) {
      console.error('删除应用失败', error);
      ElMessage.error('删除应用失败！');
      throw error;
    }
  }

  /**
   * 获取应用关联的表单
   * @param appId 应用ID
   * @returns 表单数据
   */
  static async getFormsByAppId(appId: number): Promise<any[]> {
    return FormService.getFormsByAppId(appId);
  }

  /**
   * 创建或更新表单
   * @param formData 表单数据
   */
  static async createOrUpdateForm(formData: any): Promise<void> {
    await FormService.createOrUpdateForm(formData);
  }

  /**
   * 删除应用关联的表单
   * @param appId 应用ID
   */
  static async deleteFormByAppId(appId: number): Promise<void> {
    try {
      // 查询应用关联的表单
      const forms = await FormService.getFormsByAppId(appId);

      if (forms && forms.length > 0) {
        // 删除表单
        const formId = forms[0].id;
        if (formId !== undefined) {
          await FormService.deleteForm(formId);
        }
      }
    } catch (error) {
      console.error('删除应用关联的表单失败', error);
    }
  }
}

export default AppService;
