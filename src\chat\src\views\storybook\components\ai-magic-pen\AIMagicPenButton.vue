<script setup lang="ts">
import { ref, computed } from 'vue';

const props = defineProps<{
  position: { x: number, y: number }
}>();

const emit = defineEmits(['update:position', 'click']);

// 拖拽相关状态
const isDragging = ref(false);
const startPosition = ref({ x: 0, y: 0 });

// 计算样式
const buttonStyle = computed(() => {
  return {
    left: `${props.position.x}px`,
    top: `${props.position.y}px`,
    cursor: isDragging.value ? 'grabbing' : 'grab'
  };
});

// 拖拽相关方法
const startDrag = (event) => {
  // 只处理鼠标左键
  if (event.button !== 0) return;

  // 记录开始拖拽时的位置
  isDragging.value = true;
  startPosition.value = {
    x: event.clientX - props.position.x,
    y: event.clientY - props.position.y
  };

  // 添加鼠标移动和松开事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);

  // 阻止默认行为和冒泡
  event.preventDefault();
  event.stopPropagation();
};

const onDrag = (event) => {
  if (!isDragging.value) return;

  // 计算新位置
  const newPosition = {
    x: event.clientX - startPosition.value.x,
    y: event.clientY - startPosition.value.y
  };

  // 限制按钮不超出屏幕边界
  const buttonWidth = 150; // 估计按钮宽度
  const buttonHeight = 50; // 估计按钮高度

  if (newPosition.x < 0) newPosition.x = 0;
  if (newPosition.y < 0) newPosition.y = 0;
  if (newPosition.x > window.innerWidth - buttonWidth)
    newPosition.x = window.innerWidth - buttonWidth;
  if (newPosition.y > window.innerHeight - buttonHeight)
    newPosition.y = window.innerHeight - buttonHeight;

  // 更新位置
  emit('update:position', newPosition);

  event.preventDefault();
};

const stopDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;

  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 处理按钮点击，只有在非拖拽状态下才触发点击事件
const handleButtonClick = (event) => {
  if (!isDragging.value) {
    emit('click');
  }
  event.stopPropagation();
};
</script>

<template>
  <div
    class="magic-pen-button"
    :style="buttonStyle"
    @mousedown="startDrag"
    @click="handleButtonClick"
  >
    <div class="fox-avatar">
      <span class="fox-icon">🦊</span>
    </div>
    <div class="button-content">
      <span class="button-text">小狐狸助手</span>
      <span class="button-subtitle">点击获取帮助</span>
    </div>
    <span class="drag-hint">拖动</span>
  </div>
</template>

<style scoped>
.magic-pen-button {
  position: fixed;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f97316;
  color: white;
  border-radius: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 10001; /* 确保高于绘本阅读器的z-index(9990) */
  user-select: none;
  transition: transform 0.2s, box-shadow 0.2s;
  gap: 0.75rem;
}

.magic-pen-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
}

.fox-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  animation: bounce 3s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.fox-icon {
  font-size: 1.5rem;
}

.button-content {
  display: flex;
  flex-direction: column;
}

.button-text {
  font-weight: 600;
  font-size: 0.95rem;
  line-height: 1.2;
}

.button-subtitle {
  font-size: 0.7rem;
  opacity: 0.9;
}

.drag-hint {
  position: absolute;
  top: -1.2rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.7rem;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.1rem 0.4rem;
  border-radius: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
}

.magic-pen-button:hover .drag-hint {
  opacity: 1;
}

.dark .magic-pen-button {
  background-color: #ea580c;
  color: white;
}

.dark .fox-avatar {
  background-color: #fef3c7;
}
</style>
