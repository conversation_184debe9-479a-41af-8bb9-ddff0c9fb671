<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  NCard,
  NGrid,
  NGridItem,
  NButton,
  NInput,
  NDataTable,
  NSpace,
  NStatistic,
  NNumberAnimation,
  NProgress,
  NDrawer,
  NDrawerContent,
  useMessage
} from 'naive-ui';
import { SvgIcon } from '@/components/common';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { useAuthStore, useGlobalStoreWithOut } from '@/store';
import { fetchGetRechargeLogAPI } from '@/api/balance';
import { fetchUseCramiAPI, fetchGetPackageAPI } from '@/api/crami';
import type { ResData } from '@/api/types';

const authStore = useAuthStore();
const useGlobalStore = useGlobalStoreWithOut();
const ms = useMessage();
const { isMobile, isSmallMd } = useBasicLayout();

// 用户余额信息
const userBalance = computed(() => authStore.userBalance);

// 模型名称
const model3Name = computed(() => authStore.globalConfig.model3Name || '基础模型点数');
const model4Name = computed(() => authStore.globalConfig.model4Name || '高级模型点数');
const drawMjName = computed(() => authStore.globalConfig.drawMjName || '绘图点数');

// 是否隐藏某些点数显示
const isHideModel3Point = computed(() => Number(authStore.globalConfig.isHideModel3Point) === 1);
const isHideModel4Point = computed(() => Number(authStore.globalConfig.isHideModel4Point) === 1);
const isHideDrawMjPoint = computed(() => Number(authStore.globalConfig.isHideDrawMjPoint) === 1);

// 卡密兑换
const code = ref('');
const loading = ref(false);

// 充值记录
const rechargeLoading = ref(false);
const rechargeData = ref([]);

// 套餐购买
const showPackageDrawer = ref(false);
const packageList = ref([]);

// 分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  pageSizes: [10, 20, 30, 40],
  showSizePicker: true,
  prefix({ itemCount }) {
    return `共 ${itemCount} 条`;
  },
});

// 表格列定义
const columns = computed(() => {
  const cols = [
    {
      title: '充值时间',
      key: 'createTime',
    },
    {
      title: '充值类型',
      key: 'type',
      render(row) {
        return h('span', null, [
          row.type === 1 ? '卡密充值' : '管理员充值',
        ]);
      },
    },
  ];

  if (!isHideModel3Point.value) {
    cols.splice(2, 0, {
      title: model3Name.value,
      key: 'model3Count',
    });
  }

  if (!isHideModel4Point.value) {
    cols.splice(3, 0, {
      title: model4Name.value,
      key: 'model4Count',
    });
  }

  if (!isHideDrawMjPoint.value) {
    cols.splice(4, 0, {
      title: drawMjName.value,
      key: 'drawMjCount',
    });
  }

  return cols;
});

// 获取充值记录
async function getRechargeLog() {
  rechargeLoading.value = true;
  try {
    const res = await fetchGetRechargeLogAPI({
      page: pagination.value.page,
      size: pagination.value.pageSize,
    });

    if (res.success) {
      const { rows, count } = res.data;
      rechargeData.value = rows;
      pagination.value.itemCount = count;
    }
  } catch (error) {
    console.error('获取充值记录失败:', error);
  } finally {
    rechargeLoading.value = false;
  }
}

// 使用卡密
async function useCrami() {
  if (!code.value) {
    ms.warning('请输入卡密');
    return;
  }

  try {
    loading.value = true;
    const res = await fetchUseCramiAPI({ code: code.value });

    if (res.success) {
      ms.success('卡密兑换成功');
      code.value = '';
      getRechargeLog();
      authStore.getUserInfo();
    } else {
      ms.error(res.message || '卡密兑换失败');
    }
  } catch (error) {
    ms.error('卡密兑换失败，请稍后重试');
    console.error('卡密兑换失败:', error);
  } finally {
    loading.value = false;
  }
}

// 打开套餐抽屉
function openPackageDrawer() {
  showPackageDrawer.value = true;
}

// 抽屉打开后获取套餐列表
async function getPackageList() {
  try {
    const res = await fetchGetPackageAPI({ status: 1, size: 30 });

    if (res.success) {
      packageList.value = res.data.rows;
    }
  } catch (error) {
    console.error('获取套餐列表失败:', error);
  }
}

// 购买套餐
function buyPackage() {
  const buyCramiAddress = authStore.globalConfig?.buyCramiAddress;
  if (buyCramiAddress) {
    window.open(buyCramiAddress);
  } else {
    ms.warning('未配置购买地址');
  }
}

// 组件挂载时获取充值记录
onMounted(() => {
  getRechargeLog();
});

// 从 vue 导入 h 函数用于渲染
import { h } from 'vue';
</script>

<template>
  <div class="points-management-container animate__animated animate__fadeIn">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-6">积分/订阅管理</h2>

    <!-- 积分概览卡片 -->
    <NCard class="mb-6">
      <template #header>
        <div class="text-lg font-medium">积分概览</div>
      </template>

      <NGrid :cols="isMobile ? 1 : 3" :x-gap="16" :y-gap="16">
        <!-- 基础模型积分 -->
        <NGridItem v-if="!isHideModel3Point">
          <div class="point-card">
            <div class="point-icon model3-icon">
              <SvgIcon icon="ri:chat-3-line" />
            </div>
            <div class="point-info">
              <div class="point-name">{{ model3Name }}</div>
              <NNumberAnimation
                :from="0"
                :to="userBalance.sumModel3Count > 99999 ? 99999 : userBalance.sumModel3Count || 0"
                :duration="1000"
                class="point-value"
              />
              <div class="point-label">可用积分</div>
            </div>
          </div>
        </NGridItem>

        <!-- 高级模型积分 -->
        <NGridItem v-if="!isHideModel4Point">
          <div class="point-card">
            <div class="point-icon model4-icon">
              <SvgIcon icon="ri:robot-line" />
            </div>
            <div class="point-info">
              <div class="point-name">{{ model4Name }}</div>
              <NNumberAnimation
                :from="0"
                :to="userBalance.sumModel4Count > 99999 ? 99999 : userBalance.sumModel4Count || 0"
                :duration="1000"
                class="point-value"
              />
              <div class="point-label">可用积分</div>
            </div>
          </div>
        </NGridItem>

        <!-- 绘图积分 -->
        <NGridItem v-if="!isHideDrawMjPoint">
          <div class="point-card">
            <div class="point-icon draw-icon">
              <SvgIcon icon="ri:image-line" />
            </div>
            <div class="point-info">
              <div class="point-name">{{ drawMjName }}</div>
              <NNumberAnimation
                :from="0"
                :to="userBalance.sumDrawMjCount > 99999 ? 99999 : userBalance.sumDrawMjCount || 0"
                :duration="1000"
                class="point-value"
              />
              <div class="point-label">可用积分</div>
            </div>
          </div>
        </NGridItem>
      </NGrid>

      <!-- 会员信息 -->
      <div v-if="userBalance.expirationTime" class="membership-info mt-6">
        <div class="membership-header">
          <div class="membership-title">
            <SvgIcon icon="ri:vip-crown-2-line" class="membership-icon" />
            <span>会员有效期</span>
          </div>
          <div class="membership-expiry">{{ userBalance.expirationTime }}</div>
        </div>

        <NProgress
          type="line"
          :percentage="userBalance.membershipPercentage || 50"
          :indicator-placement="'inside'"
          :height="16"
          :border-radius="8"
          :color="userBalance.membershipPercentage < 30 ? '#f56c6c' : '#18a058'"
          class="membership-progress"
        />
      </div>
    </NCard>

    <!-- 积分充值卡片 -->
    <NCard class="mb-6">
      <template #header>
        <div class="text-lg font-medium">积分充值</div>
      </template>

      <div class="recharge-container">
        <div class="recharge-methods">
          <!-- 卡密兑换 -->
          <div class="recharge-method">
            <div class="method-header">
              <div class="method-title">
                <SvgIcon icon="ri:coupon-3-line" class="method-icon" />
                <span>卡密兑换</span>
              </div>
            </div>

            <div class="method-content">
              <NInput
                v-model:value="code"
                placeholder="请输入卡密"
                clearable
                class="code-input"
              />

              <NButton
                type="primary"
                :loading="loading"
                @click="useCrami"
                class="exchange-btn hover-float transition-all duration-300"
              >
                兑换
              </NButton>
            </div>
          </div>

          <!-- 套餐购买 -->
          <div class="recharge-method">
            <div class="method-header">
              <div class="method-title">
                <SvgIcon icon="ri:shopping-cart-2-line" class="method-icon" />
                <span>套餐购买</span>
              </div>
            </div>

            <div class="method-content">
              <NButton
                type="primary"
                @click="openPackageDrawer"
                class="package-btn hover-float transition-all duration-300"
              >
                <template #icon>
                  <SvgIcon icon="ri:store-2-line" />
                </template>
                浏览套餐
              </NButton>
            </div>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 充值记录卡片 -->
    <NCard>
      <template #header>
        <div class="text-lg font-medium">充值记录</div>
      </template>

      <NDataTable
        :columns="columns"
        :data="rechargeData"
        :loading="rechargeLoading"
        :pagination="pagination"
        :scroll-x="800"
        @update:page="getRechargeLog"
        @update:page-size="getRechargeLog"
      />
    </NCard>

    <!-- 套餐抽屉 -->
    <NDrawer
      v-model:show="showPackageDrawer"
      :width="isSmallMd ? '100%' : 600"
      :placement="isSmallMd ? 'bottom' : 'right'"
      :on-after-enter="getPackageList"
    >
      <NDrawerContent title="套餐购买" closable>
        <div v-if="packageList.length > 0" class="package-list">
          <NGrid :cols="isSmallMd ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem v-for="(item, index) in packageList" :key="index">
              <div class="package-card hover-float transition-all duration-300">
                <div class="package-header">
                  <div class="package-name">{{ item.name }}</div>
                </div>

                <div class="package-content">
                  <div class="package-description">{{ item.des }}</div>

                  <div class="package-points">
                    <div v-if="!isHideModel3Point" class="point-item">
                      <span class="point-label">{{ model3Name }}</span>
                      <span class="point-value">{{ item.model3Count }}</span>
                    </div>

                    <div v-if="!isHideModel4Point" class="point-item">
                      <span class="point-label">{{ model4Name }}</span>
                      <span class="point-value">{{ item.model4Count }}</span>
                    </div>

                    <div v-if="!isHideDrawMjPoint" class="point-item">
                      <span class="point-label">{{ drawMjName }}</span>
                      <span class="point-value">{{ item.drawMjCount }}</span>
                    </div>
                  </div>

                  <div class="package-price">
                    <span class="price-label">价格</span>
                    <span class="price-value">¥{{ item.price }}</span>
                  </div>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <div v-else class="package-empty">
          <SvgIcon icon="ri:inbox-line" size="48" class="empty-icon" />
          <div class="empty-text">暂无可用套餐</div>
        </div>

        <template #footer>
          <NButton
            type="primary"
            block
            @click="buyPackage"
            class="hover-float transition-all duration-300"
          >
            前往购买
          </NButton>
        </template>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
.points-management-container {
  max-width: 900px;
  margin: 0 auto;
}

.hover-float {
  transition: all 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 积分卡片样式 */
.point-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .point-card {
  background-color: #2a2a2a;
  border-color: rgba(255, 255, 255, 0.1);
}

.point-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.point-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 24px;
}

.model3-icon {
  background-color: #2080f0;
}

.model4-icon {
  background-color: #18a058;
}

.draw-icon {
  background-color: #f0a020;
}

.point-info {
  flex: 1;
}

.point-name {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.dark .point-name {
  color: #aaa;
}

.point-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.dark .point-value {
  color: #e0e0e0;
}

.point-label {
  font-size: 12px;
  color: #999;
}

.dark .point-label {
  color: #777;
}

/* 会员信息样式 */
.membership-info {
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .membership-info {
  background-color: #2a2a2a;
  border-color: rgba(255, 255, 255, 0.1);
}

.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.membership-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.dark .membership-title {
  color: #e0e0e0;
}

.membership-icon {
  color: #f0a020;
  margin-right: 8px;
}

.membership-expiry {
  font-size: 14px;
  color: #666;
}

.dark .membership-expiry {
  color: #aaa;
}

.membership-progress {
  margin-top: 8px;
}

/* 充值方式样式 */
.recharge-container {
  padding: 8px;
}

.recharge-methods {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recharge-method {
  padding: 16px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .recharge-method {
  background-color: #2a2a2a;
  border-color: rgba(255, 255, 255, 0.1);
}

.method-header {
  margin-bottom: 16px;
}

.method-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.dark .method-title {
  color: #e0e0e0;
}

.method-icon {
  margin-right: 8px;
  color: #2080f0;
}

.method-content {
  display: flex;
  gap: 12px;
}

.code-input {
  flex: 1;
}

/* 套餐样式 */
.package-list {
  margin-top: 16px;
}

.package-card {
  padding: 16px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 100%;
}

.dark .package-card {
  background-color: #1e1e1e;
  border-color: rgba(255, 255, 255, 0.1);
}

.package-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .package-header {
  border-color: rgba(255, 255, 255, 0.1);
}

.package-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.dark .package-name {
  color: #e0e0e0;
}

.package-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.dark .package-description {
  color: #aaa;
}

.package-points {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.point-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.point-label {
  color: #666;
}

.dark .point-label {
  color: #aaa;
}

.point-value {
  font-weight: 500;
  color: #333;
}

.dark .point-value {
  color: #e0e0e0;
}

.package-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

.dark .package-price {
  border-color: rgba(255, 255, 255, 0.1);
}

.price-label {
  font-size: 14px;
  color: #666;
}

.dark .price-label {
  color: #aaa;
}

.price-value {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

.package-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #999;
}

.dark .package-empty {
  color: #777;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

@media (max-width: 768px) {
  .method-content {
    flex-direction: column;
  }

  .exchange-btn, .package-btn {
    width: 100%;
  }
}
</style>
