<template>
  <UserCenterModal v-model:visible="show" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useGlobalStoreWithOut } from '@/store';
import UserCenterModal from './UserCenterModal/index.vue';

const useGlobalStore = useGlobalStoreWithOut();

// 使用计算属性来处理双向绑定
const show = computed({
  get() {
    return useGlobalStore.userCenterDialog;
  },
  set(visible: boolean) {
    useGlobalStore.updateUserCenterDialog(visible);
  },
});
</script>
