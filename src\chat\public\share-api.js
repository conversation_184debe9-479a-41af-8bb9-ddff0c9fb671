// 简单的API代理，用于处理分享请求
async function handleRequest(request) {
  // 获取请求URL
  const url = new URL(request.url);
  const path = url.pathname;
  
  // 只处理分享API请求
  if (path === '/api/share/getSharedHtml') {
    // 获取分享代码
    const shareCode = url.searchParams.get('shareCode');
    console.log('处理分享请求，分享代码:', shareCode);
    
    if (!shareCode) {
      return new Response(JSON.stringify({
        code: 400,
        message: '分享代码无效',
        success: false
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    try {
      // 构建API请求
      const apiUrl = `${url.origin}/api/share/getSharedHtml?shareCode=${encodeURIComponent(shareCode)}`;
      console.log('转发请求到:', apiUrl);
      
      // 创建请求头
      const headers = new Headers();
      headers.set('X-Website-Domain', url.origin);
      
      // 发送请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers
      });
      
      // 返回响应
      return response;
    } catch (error) {
      console.error('处理分享请求失败:', error);
      return new Response(JSON.stringify({
        code: 500,
        message: '处理分享请求失败',
        success: false
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }
  
  // 对于其他请求，返回404
  return new Response('Not Found', { status: 404 });
}

// 注册Service Worker
self.addEventListener('fetch', event => {
  if (event.request.url.includes('/api/share/getSharedHtml')) {
    event.respondWith(handleRequest(event.request));
  }
});
