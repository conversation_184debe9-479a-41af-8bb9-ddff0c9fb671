{"name": "deepcreate", "version": "4.1.0", "description": "", "author": "vastxie", "private": true, "license": "Apache-2.0", "bin": "./dist/main.js", "scripts": {"start": "pm2 start pm2.conf.json", "build": "nest build", "build:test": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "encrypt": "node ./encrypt.js", "start:daemon": "pm2 start pm2.conf.json --no-daemon", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "pkg:win": "pkg . -t node16-win-x64 -o app-win --debug", "pkg:mac": "pkg . -t node16-mac-x64 -o app-mac --debug", "pkg:linux": "pkg . -t node16-linux-x64 -o app-linux --debug"}, "dependencies": {"@alicloud/pop-core": "^1.7.13", "@nestjs/bull": "^0.6.3", "@nestjs/common": "^9.4.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^9.4.3", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^2.2.3", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^10.3.9", "abort-controller": "^3.0.0", "ali-oss": "^6.20.0", "archiver": "^7.0.1", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bull": "^4.14.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "cos-nodejs-sdk-v5": "^2.14.2", "dayjs": "^1.11.11", "decimal.js": "^10.4.3", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "express-xml-bodyparser": "^0.3.0", "form-data": "^4.0.0", "gpt-tokenizer": "^2.2.1", "guid-typescript": "^1.0.9", "image-size": "^1.1.1", "ioredis": "^5.4.1", "isomorphic-fetch": "^3.0.0", "javascript-obfuscator": "^4.1.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "mysql2": "^3.10.1", "nodemailer": "^6.9.14", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "puppeteer": "^24.8.2", "qs": "^6.12.3", "redis": "^4.6.14", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "stream-to-buffer": "^0.1.0", "typeorm": "^0.3.20", "uuid": "^9.0.1", "wechatpay-node-v3": "^2.2.0"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "18.11.18", "jest": "29.3.1", "prettier": "^2.8.8", "ts-jest": "29.0.3", "ts-node": "^10.9.2", "typescript": "^4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}