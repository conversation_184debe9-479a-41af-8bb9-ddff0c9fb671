<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { NCard, NButton, NSpin, NModal, NInput, NForm, NFormItem, NGrid, NGridItem, NEmpty, NTabs, NTabPane } from 'naive-ui';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import SvgIcon from '@/components/common/SvgIcon/index.vue';
import BookReader from '@/components/BookReader/index.vue';

const router = useRouter();

const props = defineProps<{
  projectData: any;
}>();

// 响应式布局
const { isMobile } = useBasicLayout();

// 绘本状态
const activeTab = ref('pages');
const isGenerating = ref(false);
const showPageEditor = ref(false);
const currentPageIndex = ref(-1);
const showReader = ref(false);
// 使用横版模式

// 处理页面编辑器显示状态
const updatePageEditorVisible = (value: boolean) => {
  showPageEditor.value = value;
};

// 绘本页面
const bookPages = ref(props.projectData.pages || []);

// 添加新页面
const addNewPage = () => {
  currentPageIndex.value = -1;
  showPageEditor.value = true;
};

// 编辑页面
const editPage = (index) => {
  currentPageIndex.value = index;
  showPageEditor.value = true;
};

// 删除页面
const deletePage = (index) => {
  if (confirm('确定要删除这个页面吗？')) {
    bookPages.value.splice(index, 1);
    savePages();
  }
};

// 保存页面
const savePage = (pageData) => {
  if (currentPageIndex.value === -1) {
    // 添加新页面
    bookPages.value.push({
      ...pageData,
      id: Date.now()
    });
  } else {
    // 更新现有页面
    bookPages.value[currentPageIndex.value] = {
      ...bookPages.value[currentPageIndex.value],
      ...pageData
    };
  }

  savePages();
  showPageEditor.value = false;
};

// 保存所有页面
const savePages = () => {
  props.projectData.pages = bookPages.value;
  window.$message?.success('绘本页面已保存');
};

// 生成整本绘本
const generateFullBook = () => {
  if (bookPages.value.length === 0) {
    window.$message?.warning('请先添加页面');
    return;
  }

  isGenerating.value = true;

  // 模拟生成过程
  setTimeout(() => {
    isGenerating.value = false;
    window.$message?.success('绘本生成完成');
  }, 3000);
};

// 导出绘本
const exportBook = () => {
  if (bookPages.value.length === 0) {
    window.$message?.warning('请先添加页面');
    return;
  }

  window.$message?.success('绘本导出功能开发中...');
};

onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="book-preview">
    <!-- 预览类型切换 -->
    <NTabs v-model:value="activeTab" type="line" animated>
      <NTabPane name="pages" tab="页面编辑">
        <template #tab>
          <div class="tab-label">
            <SvgIcon name="ri:file-list-line" size="16" class="mr-1" />
            <span>页面编辑</span>
          </div>
        </template>
      </NTabPane>
      <NTabPane name="preview" tab="绘本预览">
        <template #tab>
          <div class="tab-label">
            <SvgIcon name="ri:book-open-line" size="16" class="mr-1" />
            <span>绘本预览</span>
          </div>
        </template>
      </NTabPane>
      <NTabPane name="settings" tab="绘本设置">
        <template #tab>
          <div class="tab-label">
            <SvgIcon name="ri:settings-line" size="16" class="mr-1" />
            <span>绘本设置</span>
          </div>
        </template>
      </NTabPane>
    </NTabs>

    <div class="preview-content">
      <!-- 页面编辑 -->
      <div v-if="activeTab === 'pages'" class="pages-container">
        <div class="pages-header">
          <h3 class="pages-title">绘本页面</h3>
          <NButton @click="addNewPage" class="add-page-btn">
            <SvgIcon name="ri:add-line" size="16" class="mr-1" />
            添加页面
          </NButton>
        </div>

        <div v-if="bookPages.length === 0" class="empty-pages">
          <SvgIcon name="ri:book-open-line" size="48" class="empty-icon" />
          <p>还没有添加页面，点击"添加页面"开始创建</p>
        </div>

        <div v-else class="pages-grid">
          <NGrid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
            <NGridItem v-for="(page, index) in bookPages" :key="page.id">
              <NCard class="page-card" hoverable @click="editPage(index)">
                <div class="page-image">
                  <img v-if="page.image" :src="page.image" :alt="`Page ${index + 1}`" />
                  <div v-else class="image-placeholder">
                    <SvgIcon name="ri:image-line" size="48" />
                  </div>
                  <div class="page-number">{{ index + 1 }}</div>
                </div>
                <div class="page-info">
                  <div class="page-text">{{ page.text || '无文本内容' }}</div>
                </div>
                <div class="page-actions">
                  <NButton
                    size="small"
                    @click.stop="deletePage(index)"
                    class="delete-btn"
                  >
                    <SvgIcon name="ri:delete-bin-line" size="14" />
                  </NButton>
                </div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <!-- 绘本预览 -->
      <div v-else-if="activeTab === 'preview'" class="preview-container">
        <div v-if="bookPages.length === 0" class="empty-pages">
          <SvgIcon name="ri:book-open-line" size="48" class="empty-icon" />
          <p>还没有添加页面，无法预览绘本</p>
          <NButton @click="activeTab = 'pages'" class="mt-4">
            去添加页面
          </NButton>
        </div>
        <div v-else class="preview-actions-top">
          <NButton type="primary" @click="() => {
            // 如果有页面编辑器弹窗打开，先关闭它
            if (showPageEditor) {
              showPageEditor = false;
            }
            // 将书籍数据存储在 localStorage 中
            const bookData = {
              id: props.projectData.id || Date.now(),
              title: props.projectData.title || '预览绘本',
              pages: bookPages.value
            };
            localStorage.setItem('current-book-data', JSON.stringify(bookData));

            // 跳转到独立的绘本阅读器页面
            router.push({
              path: `/storybook/reader/${bookData.id}`,
              query: { source: 'preview' }
            });
          }">
            <SvgIcon name="ri:book-open-line" size="16" class="mr-1" />
            阅读绘本
          </NButton>
        </div>
      </div>

      <!-- 绘本设置 -->
      <div v-else-if="activeTab === 'settings'" class="settings-placeholder">
        <NEmpty description="绘本设置功能已移除">
          <template #extra>
            <NButton @click="activeTab = 'pages'">
              返回页面编辑
            </NButton>
          </template>
        </NEmpty>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="preview-actions">
      <NButton @click="savePages">
        <SvgIcon name="ri:save-line" size="16" class="mr-1" />
        保存
      </NButton>
      <NButton
        type="primary"
        @click="generateFullBook"
        :loading="isGenerating"
        :disabled="bookPages.length === 0"
      >
        <SvgIcon name="ri:book-line" size="16" class="mr-1" />
        生成绘本
      </NButton>
      <NButton
        @click="exportBook"
        :disabled="bookPages.length === 0"
      >
        <SvgIcon name="ri:download-line" size="16" class="mr-1" />
        导出
      </NButton>
    </div>

    <!-- 页面编辑器弹窗 -->
    <NModal
      v-model:show="showPageEditor"
      preset="card"
      style="width: 90%; max-width: 800px;"
      :title="`编辑页面 ${currentPageIndex >= 0 ? currentPageIndex + 1 : bookPages.length + 1}`"
      :bordered="false"
      size="huge"
    >
      <NEmpty description="页面编辑器功能已移除">
        <template #extra>
          <NButton @click="showPageEditor = false">
            关闭
          </NButton>
        </template>
      </NEmpty>
    </NModal>

    <!-- 绘本阅读器已移至独立页面 -->
  </div>
</template>

<style scoped>
.book-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow-y: auto;
}



.tab-label {
  display: flex;
  align-items: center;
}

.preview-content {
  flex: 1;
  overflow-y: auto;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.preview-actions-top {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.mt-4 {
  margin-top: 1rem;
}

/* 页面编辑样式 */
.pages-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.pages-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.dark .pages-title {
  color: #e2e8f0;
}

.add-page-btn {
  background-color: #eff6ff;
  color: #3b82f6;
}

.dark .add-page-btn {
  background-color: #1e3a8a;
  color: #93c5fd;
}

.empty-pages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  color: #94a3b8;
}

.dark .empty-pages {
  background-color: #1e293b;
  color: #94a3b8;
}

.empty-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.pages-grid {
  margin-bottom: 1.5rem;
}

.page-card {
  height: 100%;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.page-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.page-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  background-color: #f1f5f9;
}

.dark .page-image {
  background-color: #334155;
}

.page-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

.page-number {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.page-info {
  padding: 0 0.5rem;
}

.page-text {
  font-size: 0.875rem;
  color: #475569;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.dark .page-text {
  color: #cbd5e1;
}

.page-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.page-card:hover .page-actions {
  opacity: 1;
}

.delete-btn {
  background-color: rgba(255, 255, 255, 0.8);
  color: #ef4444;
}

.dark .delete-btn {
  background-color: rgba(30, 41, 59, 0.8);
  color: #f87171;
}

.preview-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.dark .preview-actions {
  border-top: 1px solid #334155;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .book-preview {
    padding: 1rem;
  }

  .pages-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .add-page-btn {
    width: 100%;
  }

  .preview-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .preview-actions button {
    width: 100%;
  }
}
</style>
