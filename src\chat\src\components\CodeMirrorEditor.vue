<template>
    <div ref="editorRef" class="w-full h-full"></div>
</template>

<script lang="ts" setup>
import { autocompletion, completionKeymap } from '@codemirror/autocomplete';
import { defaultKeymap, history, historyKeymap, indentWithTab } from '@codemirror/commands';
import { html } from '@codemirror/lang-html';
import { HighlightStyle, bracketMatching, defaultHighlightStyle, foldGutter, indentOnInput, syntaxHighlighting } from '@codemirror/language';
import { EditorState, Extension } from '@codemirror/state';
import { EditorView, ViewUpdate, highlightActiveLineGutter, keymap, lineNumbers } from '@codemirror/view';
import { tags } from '@lezer/highlight';
import { defineEmits, defineProps, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';

interface Props {
    modelValue: string;
    dark?: boolean;
    placeholder?: string;
    readonly?: boolean;
    htmlMode?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue', 'change']);

const editorRef = ref<HTMLElement | null>(null);
let editorView: EditorView | null = null;
let isInternalChange = false;

// 创建暗色主题的语法高亮样式
const createDarkTheme = () => {
    return HighlightStyle.define([
        { tag: tags.keyword, color: '#569CD6' },
        { tag: tags.comment, color: '#6A9955' },
        { tag: tags.definition, color: '#4EC9B0' },
        { tag: tags.variableName, color: '#9CDCFE' },
        { tag: tags.string, color: '#CE9178' },
        { tag: tags.number, color: '#B5CEA8' },
        { tag: tags.operator, color: '#D4D4D4' },
        { tag: tags.tagName, color: '#569CD6' },
        { tag: tags.attributeName, color: '#9CDCFE' },
        { tag: tags.attributeValue, color: '#CE9178' },
        { tag: tags.heading, color: '#E06C75', fontWeight: 'bold' },
        { tag: tags.link, color: '#98C379', textDecoration: 'underline' },
    ]);
};

// 创建编辑器状态
const createEditorState = (doc: string, extensions: Extension[]) => {
    return EditorState.create({
        doc,
        extensions,
    });
};

// 更新编辑器内容（但不触发change事件）
const updateEditorContent = (content: string) => {
    if (!editorView) return;

    const currentContent = editorView.state.doc.toString();
    if (content === currentContent) return;

    isInternalChange = true;

    try {
        const transaction = editorView.state.update({
            changes: { from: 0, to: currentContent.length, insert: content },
        });

        editorView.dispatch(transaction);
    } finally {
        // 使用nextTick确保在DOM更新后重置标志
        nextTick(() => {
            isInternalChange = false;
        });
    }
};

// 初始化编辑器
const initEditor = () => {
    if (!editorRef.value) return;

    // 基础扩展配置
    const extensions: Extension[] = [
        // 基础功能
        lineNumbers(),
        highlightActiveLineGutter(),
        history(),
        bracketMatching(),
        autocompletion(),
        foldGutter(),
        indentOnInput(),

        // 键盘快捷键
        keymap.of([
            ...defaultKeymap,
            ...historyKeymap,
            ...completionKeymap,
            indentWithTab,
        ]),

        // 变更监听
        EditorView.updateListener.of((update: ViewUpdate) => {
            if (update.docChanged && !isInternalChange) {
                const doc = update.state.doc.toString();
                emit('update:modelValue', doc);
                emit('change', doc);
            }
        }),

        // 自动高度
        EditorView.lineWrapping,
    ];

    // HTML语言支持
    if (props.htmlMode !== false) {
        extensions.push(html());
    }

    // 语法高亮
    if (props.dark) {
        // 暗色主题
        const darkTheme = createDarkTheme();
        extensions.push(
            syntaxHighlighting(darkTheme),
            EditorView.theme({
                '&': {
                    backgroundColor: '#1e1e1e',
                    color: '#d4d4d4',
                    height: '100%',
                },
                '.cm-content': {
                    caretColor: '#d4d4d4',
                    fontFamily: 'monospace',
                },
                '.cm-cursor': {
                    borderLeftColor: '#d4d4d4',
                },
                '&.cm-focused .cm-cursor': {
                    borderLeftColor: '#d4d4d4',
                },
                '&.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection': {
                    backgroundColor: 'rgba(113, 113, 113, 0.3)',
                },
                '.cm-gutters': {
                    backgroundColor: '#1e1e1e',
                    color: '#858585',
                    border: 'none',
                },
                '.cm-activeLineGutter': {
                    backgroundColor: '#242424',
                },
                '.cm-foldGutter': {
                    color: '#858585',
                },
                '.cm-activeLine': {
                    backgroundColor: 'rgba(50, 50, 50, 0.3)',
                },
            })
        );
    } else {
        // 亮色主题
        extensions.push(
            syntaxHighlighting(defaultHighlightStyle),
            EditorView.theme({
                '&': {
                    height: '100%',
                },
                '.cm-content': {
                    fontFamily: 'monospace',
                },
                '.cm-gutters': {
                    backgroundColor: '#f5f5f5',
                    color: '#6e6e6e',
                    border: 'none',
                },
                '.cm-activeLine': {
                    backgroundColor: 'rgba(224, 224, 224, 0.5)',
                },
            })
        );
    }

    // 只读模式
    if (props.readonly) {
        extensions.push(EditorView.editable.of(false));
    }

    // 占位符
    if (props.placeholder) {
        extensions.push(EditorView.placeholder(props.placeholder));
    }

    try {
        // 创建编辑器
        const state = createEditorState(props.modelValue || '', extensions);
        editorView = new EditorView({
            state,
            parent: editorRef.value,
        });

        // 初始时聚焦编辑器
        nextTick(() => {
            editorView?.focus();
        });
    } catch (error) {
        console.error('CodeMirror初始化失败:', error);
    }
};

// 监听props变化
watch(() => props.modelValue, (newValue, oldValue) => {
    if (editorView && newValue !== editorView.state.doc.toString()) {
        // 更新编辑器内容
        updateEditorContent(newValue || '');

        // 判断是否是流式输出（新值是旧值的扩展）
        const isStreaming = oldValue && newValue && newValue.startsWith(oldValue);

        // 如果是流式输出，自动滚动到底部
        if (isStreaming) {
            // 延迟滚动，确保内容已更新
            setTimeout(() => {
                scrollToBottom();
            }, 10);
        }
    }
});

watch(() => props.dark, () => {
    // 当主题变化时，需要重新初始化编辑器
    try {
        const content = editorView?.state.doc.toString() || '';

        if (editorView) {
            editorView.destroy();
            editorView = null;
        }

        nextTick(() => {
            initEditor();
            if (content && editorView) {
                updateEditorContent(content);
            }
        });
    } catch (error) {
        console.error('主题切换时编辑器重建失败:', error);
    }
}, { deep: true });

// 挂载和销毁
onMounted(() => {
    initEditor();
});

onBeforeUnmount(() => {
    if (editorView) {
        editorView.destroy();
        editorView = null;
    }
});

// 格式化当前代码
const formatCurrentCode = (beautifyFn?: (code: string) => string) => {
    if (!editorView) return;

    try {
        const content = editorView.state.doc.toString();

        if (beautifyFn && typeof beautifyFn === 'function') {
            const formatted = beautifyFn(content);
            updateEditorContent(formatted);
        }
    } catch (error) {
        console.error('代码格式化失败:', error);
    }
};

/**
 * 格式化代码
 * @param formatter 格式化函数
 */
const formatCode = (formatter: (code: string) => string) => {
    if (!editorView) return;

    try {
        const currentCode = editorView.state.doc.toString();
        const formattedCode = formatter(currentCode);

        if (formattedCode !== currentCode) {
            updateEditorContent(formattedCode);
        }
    } catch (error) {
        console.error('格式化代码时出错:', error);
    }
};

/**
 * 滚动到编辑器底部
 * 使用更可靠的滚动机制
 */
const scrollToBottom = () => {
    if (!editorView) return;

    try {
        // 获取最后一行的位置
        const lastLine = editorView.state.doc.lines;
        const lastLinePos = editorView.state.doc.line(lastLine).from;

        // 首先使用CodeMirror的API滚动到最后一行
        editorView.dispatch({
            effects: EditorView.scrollIntoView(lastLinePos, {
                y: 'end',
                yMargin: 50 // 添加底部边距，确保最后一行不会太靠近底部
            })
        });

        // 然后使用原生滚动方法确保滚动到底部
        const scrollDOM = editorView.scrollDOM;
        if (scrollDOM) {
            // 使用平滑滚动
            scrollDOM.scrollTo({
                top: scrollDOM.scrollHeight,
                behavior: 'smooth'
            });

            // 为了确保滚动生效，再次尝试直接设置滚动位置
            setTimeout(() => {
                if (scrollDOM) {
                    scrollDOM.scrollTop = scrollDOM.scrollHeight;
                }
            }, 50);
        }
    } catch (error) {
        console.error('滚动到底部时出错:', error);

        // 如果上面的方法失败，尝试使用更直接的方法
        try {
            const scrollDOM = editorView.scrollDOM;
            if (scrollDOM) {
                scrollDOM.scrollTop = scrollDOM.scrollHeight;
            }
        } catch (fallbackError) {
            console.error('备用滚动方法也失败:', fallbackError);
        }
    }
};

/**
 * 设置编辑器内容
 * @param content 要设置的内容
 */
const setValue = (content: string) => {
    if (!editorView) return;

    try {
        // 更新编辑器内容
        updateEditorContent(content);

        // 延迟滚动到底部，确保内容已更新
        setTimeout(() => {
            scrollToBottom();
        }, 10);
    } catch (error) {
        console.error('设置编辑器内容时出错:', error);
    }
};

// 暴露方法给父组件
defineExpose({
    focus: () => {
        editorView?.focus();
    },
    getContent: () => {
        return editorView?.state.doc.toString() || '';
    },
    formatCode,
    scrollToBottom,
    setValue
});
</script>

<style scoped>
.cm-editor {
    height: 100%;
    width: 100%;
    font-size: 14px;
}
</style>