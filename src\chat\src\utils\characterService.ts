// 角色服务 - 处理角色数据的存储、同步和转换
import { ss } from './storage';
import { Character } from './characterLibrary';
import { get, post } from '@/utils/request';
import { useAuthStore } from '@/store';

const CHARACTER_LIBRARY_KEY = 'storybook-character-library';
const CHARACTER_SYNC_TIME_KEY = 'storybook-character-sync-time';

/**
 * 角色数据转换 - 前端到后端
 * 将前端Character对象转换为后端接口需要的格式
 */
export function convertToBackendCharacter(character: Character) {
  // 处理personalityTraits字段，确保格式正确
  let personalityTraits = character.personalityTraits;
  if (Array.isArray(personalityTraits)) {
    personalityTraits = { traits: personalityTraits };
  } else if (!personalityTraits || typeof personalityTraits !== 'object') {
    personalityTraits = { traits: [] };
  }

  // 处理appearanceTraits字段，确保格式正确
  let appearanceTraits = character.appearanceTraits;
  if (Array.isArray(appearanceTraits)) {
    appearanceTraits = { traits: appearanceTraits };
  } else if (!appearanceTraits || typeof appearanceTraits !== 'object') {
    appearanceTraits = { traits: [] };
  }

  // 处理tags字段，确保是数组
  const tags = Array.isArray(character.tags) ? character.tags : [];

  // 构建后端需要的对象
  return {
    // 如果是本地生成的ID（大于1000000），则不传ID
    id: character.id > 1000000 ? undefined : character.id,
    name: character.name || '未命名角色',
    characterType: character.characterType || '',
    appearance: character.appearance || '',
    personalityTraits,
    appearanceTraits,
    // 优先使用imageUrl字段，兼容旧版本的image字段
    imageUrl: character.imageUrl || character._image || '',
    isTemplate: typeof character.isTemplate === 'number' ? character.isTemplate : 0,
    isFavorite: typeof character.isFavorite === 'number' ? character.isFavorite :
                (character.isFavorite === true ? 1 : 0),
    tags,
    role: character.role || '',
    // 如果有storybookId和userId，也传递给后端
    ...(character.storybookId ? { storybookId: character.storybookId } : {}),
    ...(character.userId ? { userId: character.userId } : {})
  };
}

/**
 * 角色数据转换 - 后端到前端
 * 将后端返回的角色数据转换为前端Character对象
 */
export function convertToFrontendCharacter(backendCharacter: any): Character {
  if (!backendCharacter || typeof backendCharacter !== 'object') {
    console.error('[角色服务] 无效的后端角色数据:', backendCharacter);
    return createEmptyCharacter();
  }

  // 处理personalityTraits字段
  let personalityTraits: string[] = [];
  if (backendCharacter.personalityTraits) {
    if (Array.isArray(backendCharacter.personalityTraits)) {
      personalityTraits = backendCharacter.personalityTraits;
    } else if (typeof backendCharacter.personalityTraits === 'object') {
      if (Array.isArray(backendCharacter.personalityTraits.traits)) {
        personalityTraits = backendCharacter.personalityTraits.traits;
      } else {
        // 尝试从对象中提取值
        personalityTraits = Object.values(backendCharacter.personalityTraits)
          .filter(val => typeof val === 'string') as string[];
      }
    }
  }

  // 处理appearanceTraits字段
  let appearanceTraits: string[] = [];
  if (backendCharacter.appearanceTraits) {
    if (Array.isArray(backendCharacter.appearanceTraits)) {
      appearanceTraits = backendCharacter.appearanceTraits;
    } else if (typeof backendCharacter.appearanceTraits === 'object') {
      if (Array.isArray(backendCharacter.appearanceTraits.traits)) {
        appearanceTraits = backendCharacter.appearanceTraits.traits;
      } else {
        // 尝试从对象中提取值
        appearanceTraits = Object.values(backendCharacter.appearanceTraits)
          .filter(val => typeof val === 'string') as string[];
      }
    }
  }

  // 处理tags字段
  const tags = Array.isArray(backendCharacter.tags) ? backendCharacter.tags : [];

  // 构建前端Character对象
  return {
    id: backendCharacter.id,
    name: backendCharacter.name || '未命名角色',
    characterType: backendCharacter.characterType || '',
    personalityTraits,
    appearance: backendCharacter.appearance || '',
    appearanceTraits,
    imageUrl: backendCharacter.imageUrl || '',
    _image: backendCharacter.imageUrl || '', // 兼容旧版本
    createdAt: backendCharacter.createdAt || new Date().toISOString(),
    updatedAt: backendCharacter.updatedAt || new Date().toISOString(),
    role: backendCharacter.role || '',
    tags,
    isFavorite: typeof backendCharacter.isFavorite === 'number' ? backendCharacter.isFavorite : 0,
    storybookId: backendCharacter.storybookId,
    userId: backendCharacter.userId,
    isTemplate: typeof backendCharacter.isTemplate === 'number' ? backendCharacter.isTemplate : 0,
    // 兼容旧版本
    traits: personalityTraits
  };
}

/**
 * 创建空的角色对象
 * 用于处理错误情况
 */
function createEmptyCharacter(): Character {
  return {
    id: Date.now() + Math.floor(Math.random() * 1000),
    name: '未命名角色',
    characterType: '',
    personalityTraits: [],
    appearance: '',
    appearanceTraits: [],
    imageUrl: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: [],
    isFavorite: 0,
    isTemplate: 0
  };
}

// 获取本地角色库
export function getLocalCharacters(): Character[] {
  const library = ss.get(CHARACTER_LIBRARY_KEY);
  return Array.isArray(library) ? library : [];
}

// 保存本地角色库
export function saveLocalCharacters(characters: Character[]): void {
  ss.set(CHARACTER_LIBRARY_KEY, characters);
}

// 获取最后同步时间
export function getLastSyncTime(): string | null {
  return ss.get(CHARACTER_SYNC_TIME_KEY);
}

// 设置最后同步时间
export function setLastSyncTime(time: string): void {
  ss.set(CHARACTER_SYNC_TIME_KEY, time);
}

// 从服务器获取角色列表
export async function fetchCharactersFromServer(): Promise<Character[]> {
  try {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.log('[角色服务] 用户未登录，无法从服务器获取角色');
      return [];
    }

    const response = await get({
      url: '/storybook/character/mine',
      params: {}
    });

    // 处理不同的响应格式
    let characterData: any[] = [];

    if (response && response.data) {
      // 标准格式：{ code: 200, data: [...], success: true }
      if (Array.isArray(response.data)) {
        characterData = response.data;
      }
      // 嵌套格式：{ code: 200, data: { data: [...] }, success: true }
      else if (response.data.data && Array.isArray(response.data.data)) {
        characterData = response.data.data;
      }
      // 直接返回数组格式
      else if (Array.isArray(response)) {
        characterData = response;
      }
    }

    if (characterData.length > 0) {
      const characters = characterData.map(convertToFrontendCharacter);
      console.log(`[角色服务] 从服务器获取到${characters.length}个角色`);
      return characters;
    } else {
      console.log('[角色服务] 服务器返回的角色数据为空或格式不正确');
      return [];
    }
  } catch (error) {
    console.error('[角色服务] 从服务器获取角色失败:', error);
    if (error instanceof Error) {
      console.error('[角色服务] 错误详情:', error.message);
    }
    return [];
  }
}

// 保存角色到服务器
export async function saveCharacterToServer(character: Character): Promise<Character | null> {
  try {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.log('[角色服务] 用户未登录，无法保存角色到服务器');
      return null;
    }

    const backendCharacter = convertToBackendCharacter(character);
    const isUpdate = character.id && character.id < 1000000;

    let response;
    if (isUpdate) {
      response = await post({
        url: `/storybook/character/${character.id}`,
        method: 'PUT',
        data: backendCharacter
      });
    } else {
      response = await post({
        url: '/storybook/character',
        data: backendCharacter
      });
    }

    if (response.data) {
      const savedCharacter = convertToFrontendCharacter(response.data);
      console.log(`[角色服务] 角色"${savedCharacter.name}"已${isUpdate ? '更新' : '保存'}到服务器`);
      return savedCharacter;
    }
    return null;
  } catch (error) {
    console.error('[角色服务] 保存角色到服务器失败:', error);
    return null;
  }
}

// 从服务器删除角色
export async function deleteCharacterFromServer(characterId: number): Promise<boolean> {
  try {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.log('[角色服务] 用户未登录，无法从服务器删除角色');
      return false;
    }

    // 只有服务器ID（小于1000000）才需要从服务器删除
    if (characterId > 1000000) {
      return true;
    }

    // 使用原生fetch API发送DELETE请求
    const apiUrl = import.meta.env.VITE_GLOB_API_URL || '';
    const response = await fetch(`${apiUrl}/storybook/character/${characterId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'X-Website-Domain': window.location.origin,
        'Content-Type': 'application/json',
        'Fingerprint': window.navigator.userAgent.length.toString()
      }
    });

    if (response.ok) {
      console.log(`[角色服务] 角色ID:${characterId}已从服务器删除`);
      return true;
    }

    // 检查是否是404错误（角色不存在）
    if (response.status === 404) {
      console.warn(`[角色服务] 角色ID:${characterId}在服务器上不存在，视为删除成功`);
      return true; // 如果角色不存在，视为删除成功
    }

    console.error(`[角色服务] 从服务器删除角色失败: HTTP ${response.status}`);
    return false;
  } catch (error) {
    console.error('[角色服务] 从服务器删除角色失败:', error);
    return false;
  }
}

/**
 * 同步角色库（合并本地和服务器数据）
 * 增强版本，包含更好的错误处理和冲突解决
 */
export async function syncCharacterLibrary(): Promise<Character[]> {
  try {
    const localCharacters = getLocalCharacters();
    const lastSyncTime = getLastSyncTime();

    // 检查用户是否登录
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.log('[角色服务] 用户未登录，仅使用本地角色库');
      return localCharacters;
    }

    console.log(`[角色服务] 开始同步角色库，上次同步时间: ${lastSyncTime || '从未同步'}`);

    // 获取服务器角色
    let serverCharacters: Character[] = [];
    try {
      serverCharacters = await fetchCharactersFromServer();
      console.log(`[角色服务] 从服务器获取到${serverCharacters.length}个角色`);
    } catch (error) {
      console.error('[角色服务] 从服务器获取角色失败:', error);
      // 如果获取失败但有本地数据，返回本地数据
      if (localCharacters.length > 0) {
        console.log('[角色服务] 使用本地缓存的角色库');
        return localCharacters;
      }
      throw new Error('无法从服务器获取角色，且本地没有缓存数据');
    }

    // 如果服务器没有角色数据，尝试上传本地角色
    if (serverCharacters.length === 0 && localCharacters.length > 0) {
      console.log('[角色服务] 服务器没有角色数据，尝试上传本地角色');

      const uploadResults = await Promise.allSettled(
        localCharacters.map(character => saveCharacterToServer(character))
      );

      const successCount = uploadResults.filter(result => result.status === 'fulfilled' && result.value).length;
      console.log(`[角色服务] 成功上传${successCount}/${localCharacters.length}个本地角色到服务器`);

      // 如果有成功上传的角色，重新获取服务器数据
      if (successCount > 0) {
        try {
          serverCharacters = await fetchCharactersFromServer();
        } catch (error) {
          console.error('[角色服务] 上传后重新获取服务器角色失败:', error);
        }
      }
    }

    // 准备合并角色
    const mergedCharacters: Character[] = [];
    const serverIdsMap = new Map(serverCharacters.map(c => [c.id, c]));
    const localIdsMap = new Map(localCharacters.map(c => [c.id, c]));

    // 处理服务器角色
    for (const serverChar of serverCharacters) {
      // 检查是否存在本地版本
      const localChar = localIdsMap.get(serverChar.id);

      if (!localChar) {
        // 服务器独有的角色，直接添加
        mergedCharacters.push(serverChar);
      } else {
        // 本地和服务器都有的角色，比较更新时间
        const serverTime = new Date(serverChar.updatedAt).getTime();
        const localTime = new Date(localChar.updatedAt).getTime();

        // 如果本地版本更新，且不是服务器ID（小于1000000），尝试更新服务器版本
        if (localTime > serverTime && serverChar.id < 1000000) {
          try {
            const updatedChar = await saveCharacterToServer(localChar);
            if (updatedChar) {
              mergedCharacters.push(updatedChar);
              console.log(`[角色服务] 已将本地更新的角色"${localChar.name}"同步到服务器`);
            } else {
              // 更新失败，使用服务器版本
              mergedCharacters.push(serverChar);
              console.warn(`[角色服务] 无法更新服务器角色"${serverChar.name}"，使用服务器版本`);
            }
          } catch (error) {
            // 更新失败，使用服务器版本
            mergedCharacters.push(serverChar);
            console.error(`[角色服务] 更新服务器角色"${serverChar.name}"失败:`, error);
          }
        } else {
          // 服务器版本更新或相同，使用服务器版本
          mergedCharacters.push(serverChar);
        }
      }
    }

    // 处理本地独有角色
    for (const localChar of localCharacters) {
      // 如果是本地ID（大于1000000）或服务器没有此角色
      if (localChar.id > 1000000 || !serverIdsMap.has(localChar.id)) {
        // 尝试上传到服务器（如果是本地创建的角色）
        if (localChar.id > 1000000) {
          try {
            const savedChar = await saveCharacterToServer(localChar);
            if (savedChar) {
              // 上传成功，使用服务器返回的数据（包含新ID）
              mergedCharacters.push(savedChar);
              console.log(`[角色服务] 已将本地角色"${localChar.name}"上传到服务器`);
              continue;
            }
          } catch (error) {
            console.warn(`[角色服务] 上传本地角色"${localChar.name}"失败:`, error);
          }
        }

        // 如果上传失败或不需要上传，保留本地版本
        mergedCharacters.push(localChar);
      }
      // 如果服务器已有此角色，在处理服务器角色时已经处理过，这里跳过
    }

    // 保存合并后的角色库到本地
    saveLocalCharacters(mergedCharacters);
    setLastSyncTime(new Date().toISOString());

    console.log(`[角色服务] 角色库同步完成，共${mergedCharacters.length}个角色`);
    return mergedCharacters;
  } catch (error) {
    console.error('[角色服务] 同步角色库失败:', error);
    // 显示更友好的错误信息
    if (window.$message) {
      window.$message.error('同步角色库失败，请检查网络连接后重试');
    }
    return getLocalCharacters();
  }
}
