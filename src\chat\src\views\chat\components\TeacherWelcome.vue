<template>
  <div class="teacher-welcome h-full flex flex-col">
    <!-- 欢迎头部 -->
    <div class="welcome-header text-center py-8 px-6">
      <div class="avatar-section mb-6">
        <div class="w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-4xl text-white shadow-lg animate-bounce">
          🤖
        </div>
      </div>

      <h1 class="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-3">
        AI教学助手
      </h1>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
        您的智能教学伙伴，随时为您提供专业的教学支持
      </p>
    </div>

    <!-- 功能介绍 -->
    <div class="features-section flex-1 px-6">
      <!-- 内容区域已简化 -->
    </div>

    <!-- 底部提示 -->
    <div class="welcome-footer text-center py-4 px-6 border-t border-gray-200 dark:border-gray-700">
      <p class="text-sm text-gray-500 dark:text-gray-400">
        💡 在下方输入框中描述您的需求，我将为您提供专业的教学支持
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件现在主要用于显示欢迎信息和功能介绍
// 快速开始功能已移动到右侧边栏
</script>

<style scoped>
.teacher-welcome {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.dark .teacher-welcome {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-8px,0);
  }
  70% {
    transform: translate3d(0,-4px,0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}
</style>
