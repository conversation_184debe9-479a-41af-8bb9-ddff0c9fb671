<template>
  <div class="fox-assistant-container">
    <div class="fox-assistant-header">
      <div class="fox-assistant-title-container">
        <span class="fox-emoji">🦊</span>
        <h3 class="fox-assistant-title">小狐狸创作助手</h3>
      </div>
      <p class="fox-assistant-subtitle">嗨，小朋友！有任何绘本创作问题都可以问我哦！我会用简单易懂的方式帮助你创作出精彩的故事！</p>
    </div>

    <div class="chat-container">
      <!-- 聊天消息区域 -->
      <div ref="scrollRef" class="chat-messages" @scroll="onScroll">
        <div v-if="dataSources.length === 0" class="empty-chat">
          <div class="helper-prompts">
            <div
              v-for="(prompt, index) in prompts"
              :key="index"
              class="prompt-card"
              @click="handlePromptClick(prompt)"
            >
              <div class="prompt-content">
                <div class="prompt-icon">{{ prompt.icon }}</div>
                <div class="prompt-text">{{ prompt.text }}</div>
              </div>
              <div class="prompt-arrow">
                <span>→</span>
              </div>
            </div>
          </div>
        </div>

        <template v-else>
          <Message
            v-for="(item, index) of dataSources"
            :key="`${index}-${componentKey}`"
            :index="index"
            :text="item.text"
            :inversion="item.inversion"
            :model="item.model"
            :modelType="item.modelType"
            :modelName="item.modelName"
            :modelAvatar="item.modelAvatar"
            :status="item.status"
            :loading="item.loading"
            :error="item.error"
            :dateTime="item.timestamp"
            @delete="handleDelete(item)"
            @copy="handleCopy(item)"
            @regenerate="handleRegenerate(index, dataSources.length)"
          />
        </template>

        <!-- 底部占位元素，用于滚动到底部 -->
        <div ref="bottomRef" style="height: 1px;"></div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-container">
        <NInput
          v-model:value="prompt"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          placeholder="在这里输入你的创作问题，小狐狸助手将为你解答..."
          @keydown.enter.ctrl="handleSend"
        />
        <div class="input-actions">
          <NButton
            size="small"
            @click="clearChat"
            v-if="dataSources.length > 0"
            class="clear-button"
          >
            <span class="button-icon">🗑️</span>
            <span>清空历史</span>
          </NButton>
          <NButton
            type="primary"
            @click="handleSend"
            :loading="isLoading"
            :disabled="isLoading || !prompt.trim()"
            class="ask-button"
          >
            <span class="button-icon">🦊</span>
            <span>向小狐狸提问</span>
          </NButton>
        </div>
        <div class="input-tips">
          提示：按Ctrl+Enter快速发送
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, provide, watch } from 'vue';
import { NInput, NButton, useMessage, useDialog } from 'naive-ui';
import { fetchChatAPIProcess } from '@/api';
import { post } from '@/utils/request';
import { useAuthStore, useChatStore, useGlobalStoreWithOut } from '@/store';
import StorybookApi from '@/api/modules/storybook';
import Message from '@/views/chat/components/Message/index.vue';
import { copyText } from '@/utils/format';
import { useScroll } from '@/views/chat/hooks/useScroll';
import { useChat } from '@/views/chat/hooks/useChat';

const props = defineProps<{
  projectData: any;
}>();

const ms = useMessage();
const dialog = useDialog();
const prompt = ref('');
const isLoading = ref(false);
const controller = ref(new AbortController());
const authStore = useAuthStore();
const chatStore = useChatStore();
const useGlobalStore = useGlobalStoreWithOut();
const dataSources = ref([]);
const bottomRef = ref(null);
const isAtBottom = ref(true);
const componentKey = ref(0);
const firstScroll = ref<boolean>(true);
const { scrollRef, scrollToBottom: scrollToBottomHook, scrollToBottomIfAtBottom } = useScroll();
const { addGroupChat, updateGroupChat, updateGroupChatSome } = useChat();

// 小狐狸助手AI配置
const foxAssistantConfig = ref({
  model: '',
  temperature: 0.8, // 提高温度以增加创意性
  maxTokens: 2000,
  systemPrompt: `你是一个专业的儿童绘本创作助手"小狐狸"，专为5-12岁的小学生提供绘本创作帮助。
你的回答应该：
1. 使用简单易懂的语言，适合小学生理解
2. 充满童趣和想象力，激发孩子的创造力
3. 提供具体的例子和可操作的建议
4. 鼓励积极、健康的价值观和情感表达
5. 回答要简洁明了，避免过长的段落
6. 适当使用表情符号增加亲和力
7. 回答问题时要有耐心和鼓励性

你的专长是：
- 儿童绘本故事创作和构思
- 角色设计和发展
- 情节构建和故事结构
- 场景描写和氛围营造
- 对话和叙述技巧
- 绘本创作中的教育价值融入

请以友好、活泼的"小狐狸助手"形象与小朋友互动，称呼自己为"我"或"小狐狸"，称呼用户为"小朋友"。`
});

// 精心设计的提示列表，更符合儿童绘本创作需求
const prompts = computed(() => {
  return [
    {
      icon: '✨',
      text: '我想创作一个绘本故事，有什么好主题？',
      prompt: '我是小学生，想创作一个绘本故事，请给我推荐5-6个有趣又有教育意义的故事主题，每个主题都要有简短的描述和适合的年龄段。主题要多样化，包括冒险、友谊、环保、成长等不同类型。'
    },
    {
      icon: '🦸',
      text: '如何设计有趣的故事角色？',
      prompt: '我想为我的儿童绘本设计有趣的角色，请告诉我设计角色的步骤和方法，包括如何设计角色的外观、性格特点和背景故事。请给我一些具体的例子和技巧，让我的角色更加生动有趣。'
    },
    {
      icon: '📚',
      text: '绘本故事应该有什么结构？',
      prompt: '请用简单的语言告诉我一个好的儿童绘本故事应该有什么样的结构？开头、中间和结尾分别应该包含哪些内容？如何设计情节让故事更吸引人？请给我一些适合小学生理解的建议和例子。'
    },
    {
      icon: '🎨',
      text: '如何描述绘本中的场景？',
      prompt: '我在创作儿童绘本时不知道如何描述场景，请告诉我如何用生动的语言描述不同类型的场景（如森林、城市、海洋等），如何通过场景描述营造故事氛围，以及如何让场景描述与故事情节相结合。请给我一些简单的例子。'
    },
    {
      icon: '💭',
      text: '如何写好角色对话？',
      prompt: '请教我如何在儿童绘本中写出自然、有趣的角色对话。对话应该怎样反映角色性格？如何通过对话推动故事发展？有哪些常见的对话写作错误需要避免？请给我一些适合小学生的对话写作技巧和例子。'
    },
    {
      icon: '🌈',
      text: '如何在故事中融入教育意义？',
      prompt: '我想创作一个既有趣又有教育意义的绘本故事，请告诉我如何在不说教的情况下，自然地在故事中融入积极的价值观和教育意义。有哪些适合小学生的主题和价值观？如何通过故事情节和角色成长来传递这些价值观？'
    }
  ];
});

// 加载小狐狸助手配置
const loadFoxAssistantConfig = async () => {
  try {
    // 尝试从后端获取配置
    try {
      const res = await StorybookApi.getFoxAssistantConfig();
      if (res && res.data) {
        const config = res.data;
        // 优先使用专用配置，如果没有则使用通用配置
        foxAssistantConfig.value = {
          model: config.foxAssistantModel || config.aiModel || 'deepseek-v3',
          temperature: config.foxAssistantTemperature || config.temperature || 0.8,
          maxTokens: config.foxAssistantMaxTokens || config.maxTokens || 2000,
          systemPrompt: config.foxAssistantSystemPrompt || config.systemPrompt ||
            foxAssistantConfig.value.systemPrompt
        };
        console.log('已加载小狐狸助手配置:', foxAssistantConfig.value);
      }
    } catch (apiError) {
      console.error('从API加载小狐狸助手配置失败:', apiError);

      // 尝试从通用配置获取
      try {
        const generalConfig = await StorybookApi.getStorybookConfig();
        if (generalConfig && generalConfig.data) {
          const config = generalConfig.data;
          foxAssistantConfig.value.model = config.aiModel || 'deepseek-v3';
          foxAssistantConfig.value.temperature = parseFloat(config.temperature) || 0.8;
          foxAssistantConfig.value.maxTokens = parseInt(config.maxTokens) || 2000;
          console.log('已从通用配置加载小狐狸助手配置:', foxAssistantConfig.value);
        }
      } catch (generalError) {
        console.error('从通用配置加载小狐狸助手配置失败:', generalError);
        // 保持默认配置
      }
    }
  } catch (error) {
    console.error('加载小狐狸助手配置失败:', error);
    // 使用默认配置
  }
};

// 组件挂载时加载配置和历史记录
onMounted(() => {
  loadFoxAssistantConfig();
  // 尝试从本地存储加载聊天历史
  try {
    const savedHistory = localStorage.getItem('fox-assistant-chat-history');
    if (savedHistory) {
      dataSources.value = JSON.parse(savedHistory);
    }
  } catch (e) {
    console.warn('Failed to load chat history from localStorage', e);
  }
});

// 监听dataSources变化
watch(
  dataSources,
  (val) => {
    if (val.length === 0) return;
    if (firstScroll.value) {
      firstScroll.value = false;
      scrollToBottomHook();
    }
  },
  { immediate: true }
);

// 提供onConversation给后代组件
provide('onConversation', onConversation);
provide('handleRegenerate', handleRegenerate);
provide('pauseRequest', pauseRequest);

// 保存聊天历史到本地存储
const saveChatHistory = () => {
  try {
    localStorage.setItem('fox-assistant-chat-history', JSON.stringify(dataSources.value));
  } catch (e) {
    console.warn('Failed to save chat history to localStorage', e);
  }
};

// 保存问答记录到项目数据
const saveQuestionToProject = (question, answer) => {
  try {
    // 检查是否有props.projectData
    if (props && props.projectData) {
      // 确保项目数据中有aiMagicPen.questions数组
      if (!props.projectData.aiMagicPen) {
        props.projectData.aiMagicPen = { inspirations: [], outlines: [], questions: [] };
      }
      if (!props.projectData.aiMagicPen.questions) {
        props.projectData.aiMagicPen.questions = [];
      }

      // 添加新问答记录
      props.projectData.aiMagicPen.questions.push({
        id: Date.now(),
        question,
        answer,
        timestamp: new Date().toISOString(),
        source: 'fox-assistant' // 标记来源为小狐狸助手
      });

      // 更新本地存储
      try {
        localStorage.setItem('project-data', JSON.stringify(props.projectData));
      } catch (e) {
        console.warn('Failed to save project data to localStorage', e);
      }
    } else {
      // 如果没有项目数据，只记录到控制台
      console.log('保存问答记录:', { question, answer });
    }
  } catch (error) {
    console.error('保存问答记录失败:', error);
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (bottomRef.value) {
    bottomRef.value.scrollIntoView({ behavior: 'smooth' });
  }
};

// 监听滚动事件
const onScroll = () => {
  if (!scrollRef.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollRef.value;
  isAtBottom.value = Math.abs(scrollHeight - scrollTop - clientHeight) < 10;
};

// 处理发送消息
const handleSend = async () => {
  if (!prompt.value.trim() || isLoading.value) return;

  const userPrompt = prompt.value;
  prompt.value = ''; // 清空输入框
  isLoading.value = true;

  // 构建系统提示词和用户提示词
  const systemPrompt = foxAssistantConfig.value.systemPrompt;

  // 根据用户提问内容，增强提示词以获得更好的回答
  let enhancedUserPrompt = userPrompt;

  // 检测是否包含关键词，添加特定的指导
  if (userPrompt.includes('角色') || userPrompt.includes('人物')) {
    enhancedUserPrompt += '\n请在回答中提供具体的角色设计例子和步骤。';
  } else if (userPrompt.includes('场景') || userPrompt.includes('环境')) {
    enhancedUserPrompt += '\n请在回答中提供生动的场景描述例子。';
  } else if (userPrompt.includes('情节') || userPrompt.includes('故事')) {
    enhancedUserPrompt += '\n请在回答中提供简单的故事结构和情节发展建议。';
  } else if (userPrompt.includes('对话')) {
    enhancedUserPrompt += '\n请在回答中提供生动的对话例子。';
  }

  // 构建完整提示词
  const fullPrompt = `${systemPrompt}\n\n请回答小朋友的问题：${enhancedUserPrompt}\n\n请记住，你是在和小学生交流，使用友好、简单的语言，适当使用表情符号增加亲切感。`;

  await onConversation({
    msg: fullPrompt,
    model: foxAssistantConfig.value.model || 'deepseek-v3',
    modelName: '小狐狸助手',
    modelType: 1,
    modelAvatar: '🦊'
  });
};

// 会话处理函数
const onConversation = async ({
  msg,
  action,
  drawId,
  customId,
  model,
  modelName,
  modelType,
  modelAvatar,
  appId,
  extraParam,
  fileUrl,
  chatId,
}: Chat.ConversationParams) => {
  chatStore.setStreamIn(true);
  useGlobalStore.updateIsChatIn(true);

  let message = msg || '提问';
  let useModel = model || foxAssistantConfig.value.model || 'deepseek-v3';
  controller.value = new AbortController();

  /* 增加一条用户记录 */
  addGroupChat({
    text: message,
    model: useModel,
    modelName: modelName || '用户',
    modelType: modelType,
    inversion: true,
    fileInfo: fileUrl,
    timestamp: new Date().toISOString(),
  });

  let options: any = {
    groupId: 0, // 小狐狸助手不使用群组ID
  };

  /* 虚拟增加一条ai记录 */
  addGroupChat({
    text: '',
    model: useModel,
    modelName: modelName || '小狐狸助手',
    modelType: modelType || 1,
    loading: true,
    inversion: false,
    error: false,
    modelAvatar: modelAvatar || '🦊',
    status: 2,
    timestamp: new Date().toISOString(),
  });

  await scrollToBottomHook();
  let fullText = ''; // 用于累加整个会话的内容
  let lastProcessedIndex = 0;

  try {
    // 记录请求参数
    console.log('【小狐狸助手】请求参数:', {
      model: useModel,
      modelName: modelName || '小狐狸助手',
      modelType: modelType || 1,
      prompt: msg.substring(0, 100) + '...' // 只记录前100个字符
    });

    await fetchChatAPIProcess({
      model: useModel,
      modelName: modelName || '小狐狸助手',
      modelType: modelType || 1,
      prompt: msg,
      modelAvatar: modelAvatar || '🦊',
      options,
      signal: controller.value.signal,
      onDownloadProgress: (progressEvent) => {
        // 兼容不同版本的axios事件结构
        const xhr = progressEvent.event?.target || progressEvent.target;
        const responseText = xhr.responseText;
        const newResponsePart = responseText.substring(lastProcessedIndex);
        lastProcessedIndex = responseText.length; // 更新处理位置

        const responseParts = newResponsePart.trim().split('\n');

        responseParts.forEach((part: string) => {
          try {
            // 尝试将字符串解析为 JSON 对象
            const jsonObj = JSON.parse(part);
            if (jsonObj.userBalance)
              authStore.updateUserBalance(jsonObj.userBalance);
            // 检查 jsonObj 是否具有有效的 text 字段
            if (jsonObj.text) {
              // 累加 text 字段的内容到 fullText
              fullText += jsonObj.text;
            }
          } catch (error) {}
        });

        // 更新UI
        updateGroupChat(dataSources.value.length - 1, {
          text: fullText, // 使用累加后的全文本更新
          modelType: 1,
          modelName: modelName || '小狐狸助手',
          error: false,
          loading: true,
        });

        scrollToBottomIfAtBottom();
      },
    });

    // 保存问答记录到项目数据
    if (fullText) {
      saveQuestionToProject(message, fullText);
    }

    // 保存聊天历史
    saveChatHistory();
  } catch (error) {
    console.error('【小狐狸助手】查询失败:', error);
    useGlobalStore.updateIsChatIn(false);

    // 显示友好的错误提示
    let errorMessage = '抱歉，我遇到了一些问题，请稍后再试。';

    // 根据错误类型提供更具体的错误信息
    if (error.message) {
      if (error.message.includes('500')) {
        errorMessage = '抱歉，服务器出现了问题，请稍后再试。';
      } else if (error.message.includes('timeout')) {
        errorMessage = '抱歉，请求超时，请检查网络连接后再试。';
      } else if (error.message.includes('Network Error')) {
        errorMessage = '抱歉，网络连接出现问题，请检查网络后再试。';
      } else if (error.message.includes('canceled')) {
        errorMessage = '请求已取消。';
      } else if (error.message.includes('余额不足')) {
        errorMessage = '抱歉，账户余额不足，请充值后再试。';
      } else if (error.message.includes('违规')) {
        errorMessage = '抱歉，您的提问包含敏感内容，请调整后重试。';
      }
    }

    ms.error('小狐狸助手查询失败：' + errorMessage);

    const currentChat = dataSources.value[dataSources.value.length - 1];
    updateGroupChat(dataSources.value.length - 1, {
      text: currentChat.text === '' ? errorMessage : currentChat.text,
      inversion: false,
      loading: false,
      error: true,
    });

    scrollToBottomIfAtBottom();
  } finally {
    useGlobalStore.updateIsChatIn(false);
    chatStore.setStreamIn(false);
    updateGroupChatSome(dataSources.value.length - 1, { loading: false });
    isLoading.value = false;
  }
};

// 处理预设提示点击
const handlePromptClick = (promptItem) => {
  prompt.value = promptItem.prompt;
  handleSend();
};

// 清空聊天历史
const clearChat = () => {
  if (confirm('确定要清空聊天历史吗？')) {
    dataSources.value = [];
    localStorage.removeItem('fox-assistant-chat-history');
  }
};

// 处理删除消息
const handleDelete = (item) => {
  const index = dataSources.value.findIndex(msg =>
    msg.timestamp === item.timestamp && msg.text === item.text
  );
  if (index !== -1) {
    dataSources.value.splice(index, 1);
    saveChatHistory();
  }
};

// 处理复制消息
const handleCopy = (item) => {
  copyText({ text: item.text });
  ms.success('复制成功！');
};

// 处理重新生成
const handleRegenerate = async (index: number, chatId: number) => {
  if (dataSources.value.length === 0 || index === 0) return;
  let message = '';

  /* 如果有index就是重新生成 */
  if (index && typeof index === 'number') {
    const { text, inversion } = dataSources.value[index - 1];
    if (text) {
      message = text;
    }
    if (!inversion) {
      return;
    }
  }

  await onConversation({
    msg: message,
    chatId: chatId - 1,
    model: foxAssistantConfig.value.model || 'deepseek-v3',
    modelName: '小狐狸助手',
    modelType: 1,
    modelAvatar: '🦊'
  });

  scrollToBottomHook();
};

// 停止回复
const pauseRequest = () => {
  controller.value.abort();
  chatStore.setStreamIn(false);
  setTimeout(scrollToBottomHook, 1000);
};
</script>

<style scoped>
.fox-assistant-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
  font-family: 'Comic Sans MS', 'Yuanti SC', 'YouYuan', 'Microsoft YaHei', sans-serif;
}

.fox-assistant-header {
  margin-bottom: 1.2rem;
  background-color: #fff8e6;
  padding: 1rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #ffd166;
}

.fox-assistant-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.fox-emoji {
  font-size: 2rem;
  margin-right: 0.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.fox-assistant-title {
  font-size: 1.6rem;
  font-weight: 600;
  margin: 0;
  color: #ff7e3e;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.fox-assistant-subtitle {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  border-radius: 1rem;
  background-color: #f9fafb;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.helper-prompts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  width: 100%;
}

.prompt-card {
  background-color: white;
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 2px solid #e5e7eb;
  overflow: hidden;
  position: relative;
}

.prompt-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  border-color: #ffd166;
  background-color: #fffaf0;
}

.prompt-card:hover .prompt-icon {
  transform: scale(1.2) rotate(10deg);
}

.prompt-card:hover .prompt-arrow {
  color: #ff7e3e;
  transform: translateX(3px);
}

.prompt-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  z-index: 1;
}

.prompt-icon {
  font-size: 1.8rem;
  transition: transform 0.3s ease;
}

.prompt-text {
  font-size: 1rem;
  font-weight: 500;
  color: #4b5563;
}

.prompt-arrow {
  color: #9ca3af;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.chat-input-container {
  padding: 1.2rem;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  border-radius: 0 0 1rem 1rem;
  position: relative;
}

.chat-input-container::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.03));
  pointer-events: none;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 0.75rem;
  align-items: center;
}

.input-tips {
  font-size: 0.85rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  text-align: right;
  font-style: italic;
}

.button-icon {
  margin-right: 4px;
  font-size: 1.1em;
}

.ask-button {
  background-color: #ff7e3e !important;
  border-color: #ff7e3e !important;
  border-radius: 1rem !important;
  padding: 0.5rem 1rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(255, 126, 62, 0.3) !important;
  transition: all 0.3s ease !important;
}

.ask-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(255, 126, 62, 0.4) !important;
  background-color: #ff6a20 !important;
}

.clear-button {
  border-radius: 1rem !important;
  padding: 0.4rem 0.8rem !important;
  font-size: 0.9rem !important;
  border: 1px solid #e5e7eb !important;
  background-color: white !important;
  color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

.clear-button:hover {
  background-color: #f9fafb !important;
  color: #4b5563 !important;
  border-color: #d1d5db !important;
}

/* 暗色模式样式 */
.dark .chat-container {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .fox-assistant-header {
  background-color: #2d3748;
  border-color: #4b5563;
}

.dark .fox-assistant-title {
  color: #ff9e66;
}

.dark .fox-assistant-subtitle {
  color: #d1d5db;
}

.dark .prompt-card {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .prompt-card:hover {
  background-color: #4b5563;
  border-color: #ff9e66;
}

.dark .prompt-text {
  color: #e5e7eb;
}

.dark .prompt-arrow {
  color: #9ca3af;
}

.dark .chat-input-container {
  background-color: #1f2937;
  border-top-color: #374151;
}

.dark .clear-button {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
  color: #d1d5db !important;
}

.dark .clear-button:hover {
  background-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.dark .input-tips {
  color: #9ca3af;
}
</style>
